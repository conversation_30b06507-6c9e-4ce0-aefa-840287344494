# -*- coding: utf-8 -*-

from sqlalchemy import (
    Column,
    String,
    BigInteger,
    Text,
    Boolean,
    Index
)
from sqlalchemy.ext.declarative import declarative_base

# openwifi的pg库数据表，alembic中不要做管理
NewBase = declarative_base()


class Variables(NewBase):
    __tablename__ = 'variables2'
    id = Column(String(64), primary_key=True)
    name = Column(Text)
    description = Column(Text)
    notes = Column(Text, default='[]')
    created = Column(BigInteger)
    modified = Column(BigInteger)
    variables = Column(Text)
    entity = Column(Text, default='')
    venue = Column(Text)
    subscriber = Column(Text, default='')
    inventory = Column(Text, default='')
    configurations = Column(Text, default='[]')
    managementpolicy = Column(Text, default='')


class Configurations(NewBase):
    __tablename__ = 'configurations'
    id = Column(String(64), primary_key=True)
    name = Column(Text)
    description = Column(Text, default='')
    notes = Column(Text, default='[]')
    created = Column(BigInteger)
    modified = Column(BigInteger)
    managementpolicy = Column(Text, default='')
    devicetypes = Column(Text, default='["*"]')
    configuration = Column(Text, default='[]')
    inuse = Column(Text, default='[]')
    variables = Column(Text, default='[]')
    devicerules = Column(Text, default='{"firmwareUpgrade":"inherit","rcOnly":"inherit","rrm":"inherit"}')
    tags = Column(Text, default='[]')
    subscriberonly = Column(Boolean, default=False)
    entity = Column(Text, default='')
    venue = Column(Text, default='')
    subscriber = Column(Text, default='')

class Inventory(NewBase):
    __tablename__ = 'inventory'

    id = Column(String(64), primary_key=True)
    name = Column(Text)
    description = Column(Text, default='')
    notes = Column(Text, default='')
    created = Column(BigInteger)
    modified = Column(BigInteger)
    serialnumber = Column(Text)
    venue = Column(Text)  
    entity = Column(Text)
    subscriber = Column(Text)
    devicetype = Column(Text)
    qrcode = Column(Text)
    geocode = Column(Text)
    location = Column(Text)
    contact = Column(Text)
    deviceconfiguration = Column(Text)
    devicerules = Column(Text)
    tags = Column(Text)
    managementpolicy = Column(Text)
    state = Column(Text)
    devclass = Column(Text)
    locale = Column(Text)
    realmacaddress = Column(Text)
    donotallowoverrides = Column(Boolean)
    imported = Column(BigInteger)
    connected = Column(BigInteger)
    platform = Column(Text)
    labelsname = Column(Text)  

class Devices(NewBase):
    __tablename__ = 'devices'

    serialnumber = Column(String(30), primary_key=True)
    devicetype = Column(String(32))
    macaddress = Column(String(30))
    manufacturer = Column(String(64))
    configuration = Column(Text)
    notes = Column(Text)
    owner = Column(String(64), index=True)
    location = Column(Text, index=True)
    venue = Column(String(64))
    devicepassword = Column(String(64))
    firmware = Column(String(128))
    compatible = Column(String(128))
    fwupdatepolicy = Column(String(128))
    uuid = Column(BigInteger)
    creationtimestamp = Column(BigInteger)
    lastconfigurationchange = Column(BigInteger)
    lastconfigurationdownload = Column(BigInteger)
    lastfwupdate = Column(BigInteger)
    subscriber = Column(String(64))
    entity = Column(String(64))  # 有索引
    modified = Column(BigInteger)
    locale = Column(String(32))
    restricteddevice = Column(Boolean)
    pendingconfiguration = Column(Text)
    pendingconfigurationcmd = Column(String(64))
    restrictiondetails = Column(Text)
    pendinguuid = Column(BigInteger)
    simulated = Column(Boolean)
    lastrecordedcontact = Column(BigInteger)
    certificateexpirydate = Column(BigInteger)
    connectreason = Column(Text)

    # 定义索引
    __table_args__ = (
        Index('devicelocation', 'location'),
        Index('deviceowner', 'owner'),
    )