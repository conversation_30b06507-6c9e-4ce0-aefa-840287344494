import time
import copy
import json

from sqlalchemy import (
    <PERSON>umn,
    Integer,
    String,
    Text,
    <PERSON><PERSON><PERSON>,
    <PERSON>olean,
    DateTime,
    Enum,
    JSON,
    Table
)
from sqlalchemy.orm import relationship, exc

from server.db.db_common import DBCommon
from server.db.models.base import Base
from server.util.encrypt_util import aes_cipher
from server.db.models import inventory
from server.db.models.inventory import Switch
from server.util.prometheus_util import query_lldp_state

class CampusTopologyConfig(Base):
    __tablename__ = "campus_topology_config"
    id = Column(Integer, primary_key=True, autoincrement=True)
    type = Column(String(128), default='mlag', nullable=False)
    site_id = Column(Integer, ForeignKey('site.id', ondelete='CASCADE', onupdate='CASCADE'))
    site_name = Column(String(128))
    topology_name = Column(String(128))
    configuration = Column(JSON)


class CampusSiteNodes(Base):
    __tablename__ = "campus_site_nodes"
    id = Column(Integer, primary_key=True, autoincrement=True)
    topology_config_id = Column(Integer, ForeignKey('campus_topology_config.id', ondelete='CASCADE', onupdate='CASCADE'))    
    pod_id = Column(Integer, ForeignKey('campus_site_pods.id', ondelete='CASCADE', onupdate='CASCADE'), nullable=True)
    switch_sn = Column(String(128))
    type = Column(String(32), default='core', nullable=False)
    node_info = Column(JSON)
    node_config_data = Column(Text)


class CampusSitePods(Base):
    __tablename__ = "campus_site_pods"
    id = Column(Integer, primary_key=True, autoincrement=True)
    pod_name = Column(String(128))
    pod_index = Column(Integer)
    topology_config_id = Column(Integer, ForeignKey('campus_topology_config.id', ondelete='CASCADE', onupdate='CASCADE'))


class CampusTemplateVersion(Base):
    __tablename__ = 'campus_template_version'
    id = Column(Integer, primary_key=True, autoincrement=True)
    topology_id = Column(Integer, ForeignKey('campus_topology_config.id', ondelete='CASCADE'), nullable=False)
    name = Column(String(255), nullable=False, unique=True)
    config_info = Column(JSON)
    switch_num = Column(Integer, default=0)
    description = Column(Text)
    created_by = Column(String(50))


class CampusBlueprintDB(DBCommon):

    def add_campus_site_config(self, site_id, site_name, topology_config_type, topology_name, configuration):
        session = self.get_session()
        with session.begin(subtransactions=True):
            config = CampusTopologyConfig(
                site_id=site_id,
                site_name=site_name,
                type=topology_config_type, 
                topology_name=topology_name, 
                configuration=configuration
            )
            session.add(config)
            return config
    
    def add_campus_site_node_and_return_prev_node_config(self, config_id, switch_sn, node_type, node_info, pod_id=None):
        """
        Add or update a campus site node
        Args:
            config_id: ID of the topology config
            switch_sn: Switch serial number
            node_type: Type of the node
            node_info: Node information
            pod_id: ID of the pod (optional)
        Returns:
            Previous node config if updated, empty dict if new node
        """
        session = self.get_session()
        with session.begin(subtransactions=True):
            # Check if node with same config_id and switch_sn exists
            existing_node = session.query(CampusSiteNodes).filter(
                CampusSiteNodes.topology_config_id == config_id,
                CampusSiteNodes.switch_sn == switch_sn
            ).first()
            
            if existing_node:
                # Update if exists
                existing_node.type = node_type
                existing_node.node_info = node_info
                if pod_id:
                    existing_node.pod_id = pod_id  # 更新 pod_id
                
                if existing_node.node_config_data:
                    return json.loads(existing_node.node_config_data)
                else:
                    return {}
            else:
                # Create new if not exists
                node = CampusSiteNodes(
                    topology_config_id=config_id, 
                    switch_sn=switch_sn, 
                    type=node_type, 
                    node_info=node_info,
                    pod_id=pod_id  # 设置 pod_id
                )
                session.add(node)
                return {}
            
    def update_campus_site_config(self, config_id, site_id, site_name, topology_config_type, topology_name, configuration):
        session = self.get_session()
        with session.begin(subtransactions=True):
            session.query(CampusTopologyConfig).filter(CampusTopologyConfig.id == config_id).update(
                {
                    CampusTopologyConfig.site_id: site_id,
                    CampusTopologyConfig.site_name: site_name,
                    CampusTopologyConfig.type: topology_config_type,
                    CampusTopologyConfig.topology_name: topology_name,
                    CampusTopologyConfig.configuration: configuration,
                })
    
    def get_campus_site_config(self, config_id):
        session = self.get_session()
        config = session.query(CampusTopologyConfig).filter(CampusTopologyConfig.id == config_id).first()
        return config
    
    def get_campus_site_nodes(self, config_id):
        session = self.get_session()
        nodes = session.query(CampusSiteNodes).filter(CampusSiteNodes.topology_config_id == config_id).all()
        return nodes
    
    def delete_campus_site_config(self, config_id):
        session = self.get_session()
        with session.begin(subtransactions=True):
            session.query(CampusTopologyConfig).filter(CampusTopologyConfig.id == config_id).delete()

    def get_node_switch_sns_by_config(self, config_id):
        """
        Get all switch_sns for nodes under specified config_id
        """
        session = self.get_session()
        nodes = session.query(CampusSiteNodes.switch_sn).filter(
            CampusSiteNodes.topology_config_id == config_id
        ).all()
        return {node.switch_sn for node in nodes}

    def delete_nodes_by_sns(self, config_id, switch_sns):
        """
        Delete nodes with specific switch_sns under specified config_id
        """
        session = self.get_session()
        with session.begin(subtransactions=True):
            session.query(CampusSiteNodes).filter(
                CampusSiteNodes.topology_config_id == config_id,
                CampusSiteNodes.switch_sn.in_(switch_sns)
            ).delete(synchronize_session=False)

    def add_or_update_campus_site_pod(self, topology_config_id, pod_name, pod_index, pod_id=None):
        """
        Add or update a campus site pod
        Args:
            topology_config_id: ID of the topology config
            pod_name: Name of the pod
            pod_index: Index of the pod
            pod_id: ID of existing pod (None for new pod)
        Returns:
            The created or updated pod object
        """
        session = self.get_session()
        with session.begin(subtransactions=True):
            if pod_id:
                # Update existing pod
                pod = session.query(CampusSitePods).filter(
                    CampusSitePods.id == pod_id,
                    CampusSitePods.topology_config_id == topology_config_id
                ).first()
                if pod:
                    pod.pod_name = pod_name
                    return pod
                else:
                    return None
            else:
                # Create new pod
                pod = CampusSitePods(
                    topology_config_id=topology_config_id,
                    pod_name=pod_name,
                    pod_index=pod_index
                )
                session.add(pod)
                return pod

    def remove_no_exist_site_pod(self, topology_config_id, pod_id_list):
        session = self.get_session()
        with session.begin(subtransactions=True):
            session.query(CampusSitePods).filter(CampusSitePods.topology_config_id == topology_config_id).filter(
                CampusSitePods.id.notin_(pod_id_list)
            ).delete()

    def delete_campus_site_pod(self, pod_id, topology_config_id):
        """
        Delete a campus site pod
        Args:
            pod_id: ID of the pod to delete
            topology_config_id: ID of the topology config
        Returns:
            True if deletion successful, False if pod not found
        """
        session = self.get_session()
        with session.begin(subtransactions=True):
            result = session.query(CampusSitePods).filter(
                CampusSitePods.id == pod_id,
                CampusSitePods.topology_config_id == topology_config_id
            ).delete()
            return result > 0

    def get_site_by_id(self, site_id):
        """Get site by id"""
        session = self.get_session()
        return session.query(inventory.Site).filter(
            inventory.Site.id == site_id
        ).first()

    def get_site_by_name(self, site_name):
        """Get site by name"""
        session = self.get_session()
        return session.query(inventory.Site).filter(
            inventory.Site.site_name == site_name
        ).first()

    def get_switch_by_sn(self, switch_sn):
        """Get switch by serial number"""
        session = self.get_session()
        return session.query(Switch).filter(
            Switch.sn == switch_sn
        ).first()

    def get_pods_by_config(self, topology_config_id):
        """Get all pods for a specific topology config"""
        session = self.get_session()
        return session.query(CampusSitePods).filter(
            CampusSitePods.topology_config_id == topology_config_id
        ).all()

    def get_topology_view_data(self, nodes):
        """Build topology view data for a list of nodes"""
        topology_view_nodes = []
        topology_view_edges = []

        for node in nodes:
            node_info = node.node_info['node_info']
            switch_obj = self.get_switch_by_sn(node.switch_sn)
            if not switch_obj:
                continue

            node_data = self.build_node_data(node, node_info, switch_obj)
            
            # Add node view data
            lldp_state = query_lldp_state(node.switch_sn, node_info.get('mac_addr', ''))
            view_node = node_data.copy()
            view_node.update(lldp_state)
            topology_view_nodes.append(view_node)

            # Add edge view data
            self._build_edge_data(node, node_info, topology_view_edges)

        return topology_view_nodes, topology_view_edges

    def _build_edge_data(self, node, node_info, topology_view_edges):
        """Helper method to build edge data"""
        links = node_info.get('links', {})
        for link_type, link_ports in links.items():
            for port_id, port_info in link_ports.items():
                if isinstance(port_info, dict):
                    target_sn = port_info.get('core_sn') or port_info.get('dis_sn')
                else:
                    target_sn = port_info
                
                if target_sn:
                    edge = {
                        'source_sn': node.switch_sn,
                        'source_label': node_info.get('mac_addr', ''),
                        'target_sn': target_sn,
                        'target_label': '',
                        'port_info': [{
                            'source_port': port_id,
                            'target_port': port_id
                        }]
                    }
                    topology_view_edges.append(edge)

    def build_node_data(self, node, node_info, switch_obj):
        """Build basic node data structure"""
        return {
            'id': node_info.get('id'),
            'type': node.type,
            'router_id': node_info.get('router_id'),
            'other_ip_config': node_info.get('other_ip_config', []),
            'links': node_info.get('links', {}),
            'mac_addr': node_info.get('mac_addr'),
            'label': node_info.get('label'),
            'mgt_ip': node_info.get('mgt_ip'),
            'model': node_info.get('model'),
            'switch_sn': node.switch_sn,
            'status': "online" if switch_obj.reachable_status == 0 else "offline"
        }


campus_blueprint_db = CampusBlueprintDB()
