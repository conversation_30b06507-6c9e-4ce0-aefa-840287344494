# revision identifiers, used by Alembic.
revision = 'v2.2.0'
down_revision = 'v9'
branch_labels = None
depends_on = None

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql
from sqlalchemy import inspect


connection = op.get_bind()

inspector = inspect(connection)

def upgrade():
    #### campus
    op.create_table('campus_topology_config',
                    sa.Column('id', sa.Integer(), primary_key=True),
                    sa.Column('site_id', sa.Integer(), nullable=False),
                    sa.Column('site_name', sa.String(length=128), nullable=False),
                    sa.Column('topology_name', sa.String(length=128), nullable=False),
                    sa.Column('type', sa.String(length=128), nullable=False, default='mlag'),
                    sa.Column('configuration', sa.JSON(), nullable=False),
                    sa.Column('create_time', sa.DateTime(), nullable=True),
                    sa.Column('modified_time', sa.DateTime(), nullable=True),
                    )

    op.create_table('campus_site_pods',
                    sa.Column('id', sa.Integer(), primary_key=True),
                    sa.Column('pod_name', sa.String(length=128), nullable=False),
                    sa.Column('pod_index', sa.Integer(), nullable=False),
                    sa.Column('topology_config_id', sa.Integer(), nullable=False),
                    sa.Column('create_time', sa.DateTime(), nullable=True),
                    sa.Column('modified_time', sa.DateTime(), nullable=True),
                    )

    op.create_table('campus_site_nodes',
                    sa.Column('id', sa.Integer(), primary_key=True),
                    sa.Column('topology_config_id', sa.Integer(), nullable=False),
                    sa.Column('pod_id', sa.Integer(), nullable=True),
                    sa.Column('switch_sn', sa.String(length=128), nullable=False),
                    sa.Column('type', sa.String(length=32), nullable=False),
                    sa.Column('node_info', sa.JSON(), nullable=False),
                    sa.Column('node_config_data', sa.Text(65535), nullable=True),
                    sa.Column('create_time', sa.DateTime(), nullable=True),
                    sa.Column('modified_time', sa.DateTime(), nullable=True),
                    )

    op.create_foreign_key('fk_campus_topology_config_site_id', 'campus_topology_config', 'site', ['site_id'], ['id'],
                          ondelete='CASCADE', onupdate='CASCADE')
    op.create_foreign_key('fk_campus_site_pods_topology_config_id', 'campus_site_pods', 'campus_topology_config',
                          ['topology_config_id'], ['id'],
                          ondelete='CASCADE', onupdate='CASCADE')
    op.create_foreign_key('fk_campus_site_nodes_topology_config_id', 'campus_site_nodes', 'campus_topology_config',
                          ['topology_config_id'], ['id'],
                          ondelete='CASCADE', onupdate='CASCADE')
    op.create_foreign_key('fk_campus_site_nodes_pod_id', 'campus_site_nodes', 'campus_site_pods',
                          ['pod_id'], ['id'],
                          ondelete='CASCADE', onupdate='CASCADE')

    op.create_index('idx_campus_site_nodes_pod_id', 'campus_site_nodes', ['pod_id'])
    op.create_index('idx_campus_site_nodes_topology_config_id', 'campus_site_nodes', ['topology_config_id'])

    ##### dc
    op.create_table('dc_fabric_unit',
                    sa.Column('id', sa.Integer(), primary_key=True),
                    sa.Column('name', sa.String(length=128), nullable=False),
                    sa.Column('description', sa.String(length=256), nullable=True),
                    sa.Column('unit_info', sa.JSON(), nullable=False),
                    sa.Column('create_time', sa.DateTime(), nullable=True),
                    sa.Column('modified_time', sa.DateTime(), nullable=True),
                    )

    op.create_table('dc_fabric_template',
                    sa.Column('id', sa.Integer(), primary_key=True),
                    sa.Column('name', sa.String(length=128), nullable=False),
                    sa.Column('description', sa.String(length=256), nullable=True),
                    sa.Column('type', sa.String(length=32), nullable=False),
                    sa.Column('underlay_routing_protocol', sa.String(length=32), nullable=False),
                    sa.Column('overlay_control_protocol', sa.String(length=32), nullable=False),
                    sa.Column('template_info', sa.JSON(), nullable=False),
                    sa.Column('create_time', sa.DateTime(), nullable=True),
                    sa.Column('modified_time', sa.DateTime(), nullable=True),
                    )

    op.create_table('dc_fabric_topology',
                    sa.Column('id', sa.Integer(), primary_key=True),
                    sa.Column('fabric_id', sa.Integer(), nullable=False),
                    sa.Column('template_name', sa.String(length=128), nullable=False),
                    sa.Column('fabric_config', sa.JSON(), nullable=False),
                    sa.Column('status', sa.String(length=32), nullable=False),
                    sa.Column('create_time', sa.DateTime(), nullable=True),
                    sa.Column('modified_time', sa.DateTime(), nullable=True),
                    )

    op.create_table('dc_fabric_topology_node',
                    sa.Column('id', sa.Integer(), primary_key=True),
                    sa.Column('logic_name', sa.String(length=128), nullable=False),
                    sa.Column('switch_sn', sa.String(length=128), nullable=True),
                    sa.Column('fabric_topo_id', sa.Integer(), nullable=False),
                    sa.Column('type', sa.String(length=32), nullable=False),
                    sa.Column('status', sa.String(length=32), nullable=False),
                    sa.Column('node_info', sa.JSON(), nullable=False),
                    sa.Column('node_config', sa.JSON(), nullable=True),
                    sa.Column('vlan_domain_pool_id', sa.BIGINT(), nullable=True),
                    sa.Column('create_time', sa.DateTime(), nullable=True),
                    sa.Column('modified_time', sa.DateTime(), nullable=True),
                    )

    op.create_foreign_key('fk_dc_fabric_topology_fabric_id', 'dc_fabric_topology', 'fabric', ['fabric_id'], ['id'],
                          ondelete='CASCADE', onupdate='CASCADE')
    op.create_foreign_key('fk_dc_fabric_topology_node_fabric_topo_id', 'dc_fabric_topology_node', 'dc_fabric_topology',
                          ['fabric_topo_id'], ['id'],
                          ondelete='CASCADE', onupdate='CASCADE')

    # FMT
    if 'dcp_device_basic' in inspector.get_table_names():
        op.add_column('dcp_device_basic', sa.Column('series', sa.Integer(), nullable=True))
        op.execute(
            '''update automation.dcp_device_basic set series=1 where series is null;''')
        op.rename_table('dcp_device_basic', 'otn_device_basic')

    if 'dcp_temp_data' in inspector.get_table_names():
        op.rename_table('dcp_temp_data', 'otn_temp_data')

    op.create_table('snmp_alarm_trap_metadata',
                    sa.Column('trap_id', sa.Integer(), nullable=False, primary_key=True),
                    sa.Column('create_time', sa.DateTime(), nullable=False),
                    sa.Column('modified_time', sa.DateTime(), nullable=False),
                    sa.Column('name', sa.String(128), nullable=False),
                    sa.Column('alarm_type', sa.Integer(), nullable=False),
                    sa.Column('alarm_level', sa.Integer(), nullable=False),
                    sa.Column('oid', sa.String(128), nullable=False),
                    sa.Column('match_regular', sa.String(128), nullable=False),
                    sa.Column('sub_oid_mapping', sa.Text(4096), nullable=True),
                    sa.Column('value_mapping', sa.Text(4096), nullable=True),
                    sa.Column('description', sa.String(64), nullable=True)
                    )

    op.create_table('snmp_alarm_trap_original_data',
                    sa.Column('alarm_id', sa.Integer(), nullable=False, primary_key=True),
                    sa.Column('create_time', sa.DateTime(), nullable=False),
                    sa.Column('modified_time', sa.DateTime(), nullable=False),
                    sa.Column('source_ip', sa.String(64), nullable=False),
                    sa.Column('occurrence_time', sa.DateTime(), nullable=False),
                    sa.Column('name', sa.String(128), nullable=False),
                    sa.Column('value', sa.Text(4096), nullable=False),
                    sa.Column('description', sa.String(128), nullable=True)
                    )

    op.create_table('fmt_device_basic',
                    sa.Column('device_id', sa.Integer(), nullable=False, primary_key=True),
                    sa.Column('create_time', sa.DateTime(), nullable=False),
                    sa.Column('modified_time', sa.DateTime(), nullable=False),
                    sa.Column('serial_number', sa.String(48), nullable=True),
                    sa.Column('slot_number', sa.Integer(), nullable=False, default=0),
                    sa.Column('mask', sa.String(48), nullable=True),
                    sa.Column('gateway', sa.String(48), nullable=True),
                    sa.Column('mac', sa.String(48), nullable=True),
                    sa.Column('key_lock_status', sa.String(10), nullable=False, default="0"),
                    sa.Column('bzc_status', sa.String(10), nullable=False, default="0"),
                    sa.Column('bzs_status', sa.String(10), nullable=False, default="0"),
                    sa.Column('fnc_status', sa.String(10), nullable=False, default="0"),
                    sa.Column('fns_status', sa.String(10), nullable=False, default="0"),
                    sa.Column('pwr_status', sa.String(10), nullable=False, default="0"),
                    sa.Column('production_date', sa.String(48), nullable=True),
                    sa.Column('hardware_version', sa.String(48), nullable=True),
                    sa.Column('software_version', sa.String(48), nullable=True),
                    sa.Column('firmware_version', sa.String(48), nullable=True),
                    sa.Column('temperature', sa.String(10), nullable=True)
                    )

    op.create_foreign_key('fmt_device_basic_fk', 'fmt_device_basic', 'otn_device_basic', ['device_id'], ['id'],
                          ondelete='CASCADE')

    op.create_table('fmt_device_cards',
                    sa.Column('card_id', sa.String(32), nullable=False, primary_key=True),
                    sa.Column('device_id', sa.Integer(), nullable=False),
                    sa.Column('slot_index', sa.Integer(), nullable=False),
                    sa.Column('type', sa.String(32), nullable=False),
                    sa.Column('model', sa.String(32), nullable=True),
                    sa.Column('create_time', sa.DateTime(), nullable=False),
                    sa.Column('modified_time', sa.DateTime(), nullable=False),
                    sa.Column('serial_number', sa.String(48), nullable=True),
                    sa.Column('production_date', sa.String(48), nullable=True),
                    sa.Column('hardware_version', sa.String(48), nullable=True),
                    sa.Column('software_version', sa.String(48), nullable=True),
                    sa.Column('firmware_version', sa.String(48), nullable=True),
                    sa.Column('temperature', sa.String(10), nullable=True),
                    sa.Column('ports_data', sa.Text(65535), nullable=True)
                    )

    op.create_foreign_key('fmt_device_cards_fk', 'fmt_device_cards', 'fmt_device_basic', ['device_id'], ['device_id'],
                          ondelete='CASCADE')

    # DC Fabric
    op.execute('''
        CREATE TABLE resource_pool_asn
            (
                `create_time`   datetime     NULL DEFAULT NULL,
                `modified_time` datetime     NULL DEFAULT NULL,
                `id`            BIGINT PRIMARY KEY AUTO_INCREMENT,
                `name`          VARCHAR(128) NOT NULL UNIQUE 
            );
    ''')
    op.execute('''
        CREATE TABLE resource_pool_asn_ranges
            (
                `create_time`      datetime NULL DEFAULT NULL,
                `modified_time`    datetime NULL DEFAULT NULL,
                `id`               BIGINT PRIMARY KEY AUTO_INCREMENT,
                `start_value`      BIGINT   NOT NULL,
                `end_value`        BIGINT   NOT NULL,
                `resource_pool_asn_id` BIGINT   NOT NULL,
                `is_in_use`        BOOLEAN  NOT NULL,
                CONSTRAINT fk_resource_pool FOREIGN KEY (resource_pool_asn_id)
                    REFERENCES resource_pool_asn (id)
                    ON DELETE CASCADE
            );
    ''')

    op.execute('''
        CREATE TABLE resource_pool_asn_use_detail
            (
                `create_time`        datetime NULL DEFAULT NULL,
                `modified_time`      datetime NULL DEFAULT NULL,
                `id`                 BIGINT PRIMARY KEY AUTO_INCREMENT,
                `value`              BIGINT   NOT NULL,
                `resource_pool_asn_ranges_id` BIGINT   NOT NULL,
                CONSTRAINT value_resource_pool_asn_ranges_uc UNIQUE (value, resource_pool_asn_ranges_id),
                CONSTRAINT fk_resource_ranges_asn FOREIGN KEY (resource_pool_asn_ranges_id)
                    REFERENCES resource_pool_asn_ranges (id)
                    ON DELETE CASCADE
            );
    ''')

    op.execute('''
        CREATE TABLE resource_pool_area
            (
                `create_time`   datetime     NULL DEFAULT NULL,
                `modified_time` datetime     NULL DEFAULT NULL,
                `id`            BIGINT PRIMARY KEY AUTO_INCREMENT,
                `name`          VARCHAR(128) NOT NULL UNIQUE 
            );
    ''')
    op.execute('''
        CREATE TABLE resource_pool_area_ranges
            (
                `create_time`      datetime NULL DEFAULT NULL,
                `modified_time`    datetime NULL DEFAULT NULL,
                `id`               BIGINT PRIMARY KEY AUTO_INCREMENT,
                `start_value`      BIGINT   NOT NULL,
                `end_value`        BIGINT   NOT NULL,
                `resource_pool_area_id` BIGINT   NOT NULL,
                `is_in_use`        BOOLEAN  NOT NULL,
                CONSTRAINT fk_resource_pool_area FOREIGN KEY (resource_pool_area_id)
                    REFERENCES resource_pool_area (id)
                    ON DELETE CASCADE
            );
    ''')

    op.execute('''
        CREATE TABLE resource_pool_area_use_detail
            (
                `create_time`        datetime NULL DEFAULT NULL,
                `modified_time`      datetime NULL DEFAULT NULL,
                `id`                 BIGINT PRIMARY KEY AUTO_INCREMENT,
                `value`              BIGINT   NOT NULL,
                `resource_pool_area_ranges_id` BIGINT   NOT NULL,
                CONSTRAINT value_resource_pool_area_ranges_uc UNIQUE (value, resource_pool_area_ranges_id),
                CONSTRAINT fk_resource_ranges_area FOREIGN KEY (resource_pool_area_ranges_id)
                    REFERENCES resource_pool_area_ranges (id)
                    ON DELETE CASCADE
            );
    ''')

    op.execute('''
            CREATE TABLE resource_pool_ip
                (
                    `create_time`   datetime     NULL DEFAULT NULL,
                    `modified_time` datetime     NULL DEFAULT NULL,
                    `id`            BIGINT PRIMARY KEY AUTO_INCREMENT,
                    `name`          VARCHAR(128) NOT NULL UNIQUE 
                );
        ''')
    op.execute('''
            CREATE TABLE resource_pool_ip_ranges
                (
                    `create_time`      datetime NULL DEFAULT NULL,
                    `modified_time`    datetime NULL DEFAULT NULL,
                    `id`               BIGINT PRIMARY KEY AUTO_INCREMENT,
                    `start_value`      BIGINT   NOT NULL,
                    `end_value`        BIGINT   NOT NULL,
                    `resource_pool_ip_id` BIGINT   NOT NULL,
                    `is_in_use`        BOOLEAN  NOT NULL,
                    CONSTRAINT fk_resource_pool_ip_ranges FOREIGN KEY (resource_pool_ip_id)
                        REFERENCES resource_pool_ip (id)
                        ON DELETE CASCADE
                );
        ''')

    op.execute('''
            CREATE TABLE resource_pool_ip_use_detail
                (
                    `create_time`        datetime NULL DEFAULT NULL,
                    `modified_time`      datetime NULL DEFAULT NULL,
                    `id`                 BIGINT PRIMARY KEY AUTO_INCREMENT,
                    `value`              BIGINT   NOT NULL,
                    `resource_pool_ip_ranges_id` BIGINT   NOT NULL,
                    CONSTRAINT value_resource_pool_ip_ranges_uc UNIQUE (value, resource_pool_ip_ranges_id),
                    CONSTRAINT fk_resource_ranges_ip FOREIGN KEY (resource_pool_ip_ranges_id)
                        REFERENCES resource_pool_ip_ranges (id)
                        ON DELETE CASCADE
                );
        ''')

    op.execute('''
        CREATE TABLE resource_pool_vni
            (
                `create_time`   datetime     NULL DEFAULT NULL,
                `modified_time` datetime     NULL DEFAULT NULL,
                `id`            BIGINT PRIMARY KEY AUTO_INCREMENT,
                `name`          VARCHAR(128) NOT NULL UNIQUE 
            );
    ''')
    op.execute('''
        CREATE TABLE resource_pool_vni_ranges
            (
                `create_time`      datetime NULL DEFAULT NULL,
                `modified_time`    datetime NULL DEFAULT NULL,
                `id`               BIGINT PRIMARY KEY AUTO_INCREMENT,
                `start_value`      BIGINT   NOT NULL,
                `end_value`        BIGINT   NOT NULL,
                `resource_pool_vni_id` BIGINT   NOT NULL,
                `is_in_use`        BOOLEAN  NOT NULL,
                CONSTRAINT fk_resource_pool_vni FOREIGN KEY (resource_pool_vni_id)
                    REFERENCES resource_pool_vni (id)
                    ON DELETE CASCADE
            );
    ''')

    op.execute('''
        CREATE TABLE resource_pool_vni_use_detail
            (
                `create_time`        datetime NULL DEFAULT NULL,
                `modified_time`      datetime NULL DEFAULT NULL,
                `id`                 BIGINT PRIMARY KEY AUTO_INCREMENT,
                `value`              BIGINT   NOT NULL,
                `resource_pool_vni_ranges_id` BIGINT   NOT NULL,
                CONSTRAINT value_resource_pool_vni_ranges_uc UNIQUE (value, resource_pool_vni_ranges_id),
                CONSTRAINT fk_resource_ranges_vni FOREIGN KEY (resource_pool_vni_ranges_id)
                    REFERENCES resource_pool_vni_ranges (id)
                    ON DELETE CASCADE
            );
    ''')

    op.execute('''
        CREATE TABLE resource_pool_vlan_domain
            (
                `create_time`   datetime     NULL DEFAULT NULL,
                `modified_time` datetime     NULL DEFAULT NULL,
                `id`            BIGINT PRIMARY KEY AUTO_INCREMENT,
                `name`          VARCHAR(128) NOT NULL,
                `fabric_id`     INT          NOT NULL, 
                `device_type`             VARCHAR(32) NOT NULL,
                UNIQUE KEY `unique_name_fabric_id` (`name`,`fabric_id`),
                CONSTRAINT fk_vlan_domain_fabric_id FOREIGN KEY (fabric_id)
                    REFERENCES fabric (id)
                    ON DELETE CASCADE
            );
    ''')

    op.create_foreign_key('fk_dc_fabric_topology_node_vd_pool_id', 'dc_fabric_topology_node', 'resource_pool_vlan_domain',
                        ['vlan_domain_pool_id'], ['id'], onupdate='CASCADE')

    op.execute('''
        CREATE TABLE resource_pool_bridge_domain
            (
                `create_time`     datetime     NULL DEFAULT NULL,
                `modified_time`   datetime     NULL DEFAULT NULL,
                `id`              BIGINT PRIMARY KEY AUTO_INCREMENT,
                `name`            VARCHAR(128) NOT NULL,
                `vlan_domain_id`  BIGINT          NOT NULL, 
                CONSTRAINT fk_bridge_domain_vlan_domain_id FOREIGN KEY (vlan_domain_id)
                    REFERENCES resource_pool_vlan_domain (id)
                    ON DELETE CASCADE
            );
    ''')

    op.execute('''
        CREATE TABLE resource_pool_bridge_domain_ranges
            (
                `create_time`      datetime NULL DEFAULT NULL,
                `modified_time`    datetime NULL DEFAULT NULL,
                `id`               BIGINT PRIMARY KEY AUTO_INCREMENT,
                `start_value`      BIGINT   NOT NULL,
                `end_value`        BIGINT   NOT NULL,
                `resource_pool_bridge_domain_id` BIGINT   NOT NULL,
                `is_in_use`        BOOLEAN  NOT NULL,
                CONSTRAINT fk_resource_pool_bridge_domain FOREIGN KEY (resource_pool_bridge_domain_id)
                    REFERENCES resource_pool_bridge_domain (id)
                    ON DELETE CASCADE
            );
    ''')

    op.execute('''
        CREATE TABLE resource_pool_bridge_domain_use_detail
            (
                `create_time`        datetime NULL DEFAULT NULL,
                `modified_time`      datetime NULL DEFAULT NULL,
                `id`                 BIGINT PRIMARY KEY AUTO_INCREMENT,
                `value`              BIGINT   NOT NULL,
                `resource_pool_bridge_domain_ranges_id` BIGINT   NOT NULL,
                CONSTRAINT value_resource_pool_bridge_domain_ranges_uc UNIQUE (value, resource_pool_bridge_domain_ranges_id),
                CONSTRAINT fk_resource_ranges_bridge_domain FOREIGN KEY (resource_pool_bridge_domain_ranges_id)
                    REFERENCES resource_pool_bridge_domain_ranges (id)
                    ON DELETE CASCADE
            );
    ''')

    op.execute('''
        CREATE TABLE resource_pool_vrf_vlan
            (
                `create_time`     datetime     NULL DEFAULT NULL,
                `modified_time`   datetime     NULL DEFAULT NULL,
                `id`              BIGINT PRIMARY KEY AUTO_INCREMENT,
                `name`            VARCHAR(128) NOT NULL,
                `vlan_domain_id`  BIGINT          NOT NULL, 
                CONSTRAINT fk_vrf_vlan_vlan_domain_id FOREIGN KEY (vlan_domain_id)
                    REFERENCES resource_pool_vlan_domain (id)
                    ON DELETE CASCADE
            );
    ''')

    op.execute('''
        CREATE TABLE resource_pool_vrf_vlan_ranges
            (
                `create_time`      datetime NULL DEFAULT NULL,
                `modified_time`    datetime NULL DEFAULT NULL,
                `id`               BIGINT PRIMARY KEY AUTO_INCREMENT,
                `start_value`      BIGINT   NOT NULL,
                `end_value`        BIGINT   NOT NULL,
                `resource_pool_vrf_vlan_id` BIGINT   NOT NULL,
                `is_in_use`        BOOLEAN  NOT NULL,
                CONSTRAINT fk_resource_pool_vrf_vlan FOREIGN KEY (resource_pool_vrf_vlan_id)
                    REFERENCES resource_pool_vrf_vlan (id)
                    ON DELETE CASCADE
            );
    ''')

    op.execute('''
        CREATE TABLE resource_pool_vrf_vlan_use_detail
            (
                `create_time`        datetime NULL DEFAULT NULL,
                `modified_time`      datetime NULL DEFAULT NULL,
                `id`                 BIGINT PRIMARY KEY AUTO_INCREMENT,
                `value`              BIGINT   NOT NULL,
                `resource_pool_vrf_vlan_ranges_id` BIGINT   NOT NULL,
                CONSTRAINT value_resource_pool_vrf_vlan_ranges_uc UNIQUE (value, resource_pool_vrf_vlan_ranges_id),
                CONSTRAINT fk_resource_ranges_vrf_vlan FOREIGN KEY (resource_pool_vrf_vlan_ranges_id)
                    REFERENCES resource_pool_vrf_vlan_ranges (id)
                    ON DELETE CASCADE
            );
    ''')

    # virtual / cloud resources
    op.create_table(
        'virtual_resource_pool_az',
        sa.Column('id', sa.Integer(), primary_key=True, autoincrement=True),
        sa.Column('az_name', sa.String(length=256), nullable=False),
        sa.Column('fabric_id', sa.Integer(), sa.ForeignKey('fabric.id', ondelete='CASCADE', onupdate='CASCADE'), nullable=True),
        sa.Column('resource_type', sa.String(length=64), nullable=True),
        sa.Column('global_vlan', sa.Text(length=4096), nullable=True),
        sa.Column('global_vni_vlan', sa.Text(length=4096), nullable=True),
        sa.Column('auth_info', sa.Text(length=65535), nullable=True),
        sa.Column('connect_status', sa.Boolean, nullable=True),
        sa.Column('connect_active_time', sa.DateTime(), nullable=True),
        sa.Column('create_time', sa.DateTime(), nullable=True),
        sa.Column('modified_time', sa.DateTime(), nullable=True)
    )

    op.create_table(
        'virtual_resource_vpc',
        sa.Column('id', sa.Integer(), primary_key=True, autoincrement=True),
        sa.Column('vpc_id', sa.String(length=512), nullable=False),
        sa.Column('vpc_name', sa.String(length=512), nullable=False),
        sa.Column('tenant', sa.String(length=512), nullable=True),
        sa.Column('fabric_id', sa.Integer(), sa.ForeignKey('fabric.id', ondelete='CASCADE', onupdate='CASCADE'), nullable=True),
        sa.Column('az_id', sa.Integer(), sa.ForeignKey('virtual_resource_pool_az.id', ondelete='CASCADE', onupdate='CASCADE'), nullable=True),
        sa.Column('resource_create_time', sa.DateTime(), nullable=False),
        sa.Column('create_time', sa.DateTime(), nullable=True),
        sa.Column('modified_time', sa.DateTime(), nullable=True)
    )


    op.create_table(
        'virtual_resource_network',
        sa.Column('id', sa.Integer(), primary_key=True, autoincrement=True),
        sa.Column('network_name', sa.String(length=512), nullable=False),
        sa.Column('fabric_id', sa.Integer(), sa.ForeignKey('fabric.id', ondelete='CASCADE', onupdate='CASCADE'), nullable=True),
        sa.Column('az_id', sa.Integer(), sa.ForeignKey('virtual_resource_pool_az.id', ondelete='CASCADE', onupdate='CASCADE'), nullable=True),
        sa.Column('vpc_id', sa.Integer(), sa.ForeignKey('virtual_resource_vpc.id', ondelete='CASCADE', onupdate='CASCADE'), nullable=True),
        sa.Column('vm_count', sa.Integer(), nullable=True),
        sa.Column('host_name', sa.String(length=512)),
        sa.Column('host_count', sa.Integer(), nullable=True),
        sa.Column('resource_create_time', sa.DateTime(), nullable=False),
        sa.Column('create_time', sa.DateTime(), nullable=True),
        sa.Column('modified_time', sa.DateTime(), nullable=True)
    )

    op.create_table(
        'virtual_resource_vm',
        sa.Column('id', sa.Integer(), primary_key=True, autoincrement=True),
        sa.Column('vm_name', sa.String(length=1024), nullable=True),
        sa.Column('vm_ip_address', sa.String(length=64), nullable=True),
        sa.Column('host_ip_address', sa.String(length=64), nullable=True),
        sa.Column('network_name', sa.String(length=512), nullable=True),
        sa.Column('fabric_id', sa.Integer(), sa.ForeignKey('fabric.id', ondelete='CASCADE', onupdate='CASCADE'), nullable=True),
        sa.Column('az_id', sa.Integer(), sa.ForeignKey('virtual_resource_pool_az.id', ondelete='CASCADE', onupdate='CASCADE'), nullable=True),
        sa.Column('vpc_id', sa.Integer(), sa.ForeignKey('virtual_resource_vpc.id', ondelete='CASCADE', onupdate='CASCADE'), nullable=True),
        sa.Column('power_status', sa.String(length=64), nullable=True),
        sa.Column('info', sa.Text(length=1024), nullable=True),
        sa.Column('create_time', sa.DateTime(), nullable=True),
        sa.Column('modified_time', sa.DateTime(), nullable=True)
    )

    op.execute('''
        CREATE TABLE virtual_resource_host 
            (
                `id`              INT AUTO_INCREMENT PRIMARY KEY,
                `host_name`       VARCHAR(512) NOT NULL,
                `description`     VARCHAR(128),
                `management_ip`   VARCHAR(64),
                `username`        VARCHAR(128),
                `password`        VARCHAR(128),
                `az_id`           INT NOT NULL,
                `create_time`     datetime NULL DEFAULT NULL,
                `modified_time`   datetime NULL DEFAULT NULL,
                UNIQUE KEY uq_host_name_az_id (host_name, az_id),
                CONSTRAINT fk_virtual_resource_pool_az FOREIGN KEY (az_id) 
                    REFERENCES virtual_resource_pool_az(id) ON DELETE CASCADE ON UPDATE CASCADE
            );
    ''')

    op.execute('''
        CREATE TABLE virtual_resource_host_link 
            (
                `id`              INT AUTO_INCREMENT PRIMARY KEY,
                `host_id`         INT NOT NULL,
                `vlan_domain_id`  BIGINT NOT NULL,
                `port_group_name` VARCHAR(128),
                `connect_mode`    VARCHAR(32),
                `link_type`       VARCHAR(32),
                `link_count`      VARCHAR(32),
                `create_time`     datetime NULL DEFAULT NULL,
                `modified_time`   datetime NULL DEFAULT NULL,
                CONSTRAINT fk_virtual_resource_host FOREIGN KEY (host_id) 
                    REFERENCES virtual_resource_host(id) ON DELETE CASCADE ON UPDATE CASCADE,
                CONSTRAINT fk_resource_pool_vlan_domain FOREIGN KEY (vlan_domain_id) 
                    REFERENCES resource_pool_vlan_domain(id) ON DELETE CASCADE ON UPDATE CASCADE
            );
    ''')

    op.execute('''
        CREATE TABLE virtual_resource_host_linkport 
            (
                `id`              INT AUTO_INCREMENT PRIMARY KEY,
                `link_id`         INT NOT NULL,
                `switch_sn`       VARCHAR(128) NOT NULL,
                `logic_device_id` INT NOT NULL,
                `port_name`       VARCHAR(256),
                `create_time`     datetime NULL DEFAULT NULL,
                `modified_time`   datetime NULL DEFAULT NULL,
                CONSTRAINT fk_virtual_resource_host_link FOREIGN KEY (link_id) 
                    REFERENCES virtual_resource_host_link(id) ON DELETE CASCADE ON UPDATE CASCADE,
                CONSTRAINT fk_dc_fabric_topology_node FOREIGN KEY (logic_device_id) 
                    REFERENCES dc_fabric_topology_node(id) ON DELETE CASCADE ON UPDATE CASCADE,
                CONSTRAINT fk_switch FOREIGN KEY (switch_sn) 
                    REFERENCES switch(sn) ON DELETE CASCADE ON UPDATE CASCADE
            );
    ''')


    # roce
    op.create_table('roce_task',
                sa.Column('id', sa.Integer(), primary_key=True),
                sa.Column('task_name', sa.String(length=256), nullable=False),
                sa.Column('device_id', sa.Integer(), nullable=False),
                sa.Column('type', sa.String(length=32), nullable=False),
                sa.Column('status', sa.String(length=32), nullable=False),
                sa.Column('result', sa.Text(65535), nullable=False),
                sa.Column('create_time', sa.DateTime(), nullable=True),
                sa.Column('modified_time', sa.DateTime(), nullable=True),
                )
    op.create_foreign_key('fk_roce_task_device_id', 'roce_task', 'ansible_device', ['device_id'], ['id'],
                          ondelete='CASCADE', onupdate='CASCADE')

    op.create_table('host_info',
            sa.Column('id', sa.Integer(), primary_key=True),
            sa.Column('device_id', sa.Integer(), nullable=False),
            sa.Column('hostname', sa.String(length=256), nullable=True),
            sa.Column('os_version', sa.String(length=256), nullable=True),
            sa.Column('cpu', sa.String(length=256), nullable=True),
            sa.Column('memory', sa.String(length=32), nullable=True),
            sa.Column('storage', sa.String(length=32), nullable=False),
            sa.Column('last_seen', sa.DateTime(), nullable=True),
            sa.Column('create_time', sa.DateTime(), nullable=True),
            sa.Column('modified_time', sa.DateTime(), nullable=True),
            )
    op.create_foreign_key('fk_host_info_device_id', 'host_info', 'ansible_device', ['device_id'], ['id'],
                          ondelete='CASCADE', onupdate='CASCADE')

    op.execute('''
            CREATE table email_server_setting
                (
                    `id`            BIGINT PRIMARY KEY AUTO_INCREMENT,
                    `host`            VARCHAR(128)     NOT NULL,
                    `port`            BIGINT      NOT NULL,
                    `username`        VARCHAR(128) NOT NULL,
                    `password`        VARCHAR(256) NOT NULL,
                    `ssl`             BOOLEAN  NOT NULL DEFAULT FALSE,
                    `tls`             BOOLEAN  NOT NULL DEFAULT FALSE,
                    `sender_email`    VARCHAR(128) NOT NULL,
                    `is_authentication` BOOLEAN  NOT NULL DEFAULT TRUE,
                    `create_time`   datetime NULL DEFAULT NULL,
                    `modified_time` datetime NULL DEFAULT NULL
                );
    ''')

    op.execute('''
            CREATE TABLE email_rule_settings
                (
                    `id`            BIGINT PRIMARY KEY AUTO_INCREMENT,
                    `rule_name`     VARCHAR(128) NOT NULL,
                    `fabric_name_list` TEXT NULL DEFAULT NULL,
                    `site_name_list`   TEXT NULL DEFAULT NULL,
                    `silent_time`   BIGINT  NULL DEFAULT NULL,
                    `create_user`  VARCHAR(128) NULL DEFAULT NULL,
                    `email`          TEXT NULL DEFAULT NULL,
                    `alarm_level_settings`        TEXT NULL DEFAULT NULL,
                    `alarm_type_settings`         TEXT NULL DEFAULT NULL,
                    `enable`        BOOLEAN NULL DEFAULT TRUE,
                    `create_time`   datetime NULL DEFAULT NULL,
                    `modified_time` datetime NULL DEFAULT NULL
                );
    ''')
    op.execute('''CREATE INDEX idx_rule_name ON email_rule_settings (`rule_name`);''')

    op.execute('''
            CREATE TABLE email_rule_logs
                (
                    `id`            BIGINT PRIMARY KEY AUTO_INCREMENT,
                    `email_rule_name` VARCHAR(128) NOT NULL,
                    `email_search_key` VARCHAR(1000) NOT NULL,
                    `target_sn`     TEXT NOT NULL,
                    `receivers`     TEXT NOT NULL,
                    `subject`       TEXT NOT NULL,
                    `content`       LONGTEXT NOT NULL,
                    `status`        VARCHAR(128) NOT NULL,
                    `error_message` TEXT NULL DEFAULT NULL,
                    `create_time`   datetime NULL DEFAULT NULL,
                    `modified_time` datetime NULL DEFAULT NULL
                );
    ''')
    op.execute('''CREATE INDEX idx_email_search_key ON email_rule_logs (`email_search_key`);''')

    op.execute('''
            create table client_device_info
                (
                    `id`            BIGINT PRIMARY KEY AUTO_INCREMENT,
                    `client_name`   VARCHAR(256) NULL DEFAULT NULL,
                    `switch_sn`     VARCHAR(128) NOT NULL,
                    `mac_address`   VARCHAR(128) NOT NULL,
                    `ip_address`    VARCHAR(128) NULL DEFAULT NULL,
                    `manufacturer`  TEXT         NULL DEFAULT NULL,
                    `state`         VARCHAR(32)  NOT NULL,
                    `update_time`       datetime     NULL DEFAULT NULL,
                    `create_time`   datetime     NULL DEFAULT NULL,
                    `modified_time` datetime     NULL DEFAULT NULL,
                    INDEX idx_mac_address_switch_sn (`mac_address`, `switch_sn`)
                );
    ''')
    op.execute('''CREATE INDEX idx_client_name ON client_device_info (`client_name`);''')
    op.execute('''CREATE INDEX idx_switch_sn ON client_device_info (`switch_sn`);''')
    op.execute('''CREATE INDEX idx_mac_address ON client_device_info (`mac_address`);''')
    op.execute('''CREATE INDEX idx_ip_address ON client_device_info (`ip_address`);''')

    op.create_table('config_distribution_task_for_dc',
                    sa.Column('id', sa.Integer(), nullable=False, primary_key=True),
                    sa.Column('task_name', sa.String(64), nullable=False),
                    sa.Column('task_id', sa.String(64), nullable=True),
                    sa.Column('sn', sa.String(32), nullable=True),
                    sa.Column('task_role', sa.String(32), nullable=True),
                    sa.Column('task_type', sa.String(255), nullable=True),
                    sa.Column('task_status', sa.Integer(), nullable=True),
                    sa.Column('fabric_id', sa.Integer(), nullable=False),
                    sa.Column('logic_name', sa.String(128), nullable=True),
                    sa.Column('assigned_to', sa.String(32), nullable=True),
                    sa.Column('config_data', sa.Text(65535), nullable=True),
                    sa.Column('task_traceback_info', sa.Text(65535), nullable=True),
                    sa.Column('start_time', sa.DateTime(), nullable=True),
                    sa.Column('end_time', sa.DateTime(), nullable=True),
                    sa.Column('create_time', sa.DateTime(), nullable=True),
                    sa.Column('modified_time', sa.DateTime(), nullable=True),
                    )

    op.create_index('idx_config_distribution_task_task_name', 'config_distribution_task_for_dc', ['task_name'])
    op.create_index('idx_config_distribution_task_task_role', 'config_distribution_task_for_dc', ['task_role'])
    op.create_index('idx_config_distribution_task_task_type', 'config_distribution_task_for_dc', ['task_type'])
    op.create_index('idx_config_distribution_task_fabric_id', 'config_distribution_task_for_dc', ['fabric_id'])

    op.create_table('config_distribution_task_for_campus',
                    sa.Column('id', sa.Integer(), nullable=False, primary_key=True),
                    sa.Column('task_name', sa.String(64), nullable=False),
                    sa.Column('task_id', sa.String(64), nullable=True),
                    sa.Column('task_role', sa.String(32), nullable=True),
                    sa.Column('task_type', sa.String(255), nullable=True),
                    sa.Column('task_status', sa.Integer(), nullable=True),
                    sa.Column('site_id', sa.Integer(), nullable=False),
                    sa.Column('sn', sa.String(32), nullable=True),
                    sa.Column('assigned_to', sa.String(32), nullable=True),
                    sa.Column('config_data', sa.Text(65535), nullable=True),
                    sa.Column('task_traceback_info', sa.Text(65535), nullable=True),
                    sa.Column('start_time', sa.DateTime(), nullable=True),
                    sa.Column('end_time', sa.DateTime(), nullable=True),
                    sa.Column('create_time', sa.DateTime(), nullable=True),
                    sa.Column('modified_time', sa.DateTime(), nullable=True),
                    )

    op.create_index('idx_config_distribution_task_task_name', 'config_distribution_task_for_campus', ['task_name'])
    op.create_index('idx_config_distribution_task_task_role', 'config_distribution_task_for_campus', ['task_role'])
    op.create_index('idx_config_distribution_task_task_type', 'config_distribution_task_for_campus', ['task_type'])
    op.create_index('idx_config_distribution_task_site_id', 'config_distribution_task_for_campus', ['site_id'])
    op.create_index('idx_config_distribution_task_sn', 'config_distribution_task_for_campus', ['sn'])

    op.execute('''alter table switch modify version varchar(128) null;''')

    op.execute('''delete from model_physic_port where platform_name = 'as7326_56x' and (port_name = 'te-1/1/49' or port_name = 'te-1/1/50');''')


def downgrade():
    op.drop_constraint('fk_campus_site_nodes_topology_config_id', 'campus_site_nodes', type_='foreignkey')
    op.drop_constraint('fk_campus_site_pods_topology_config_id', 'campus_site_pods', type_='foreignkey')
    op.drop_constraint('fk_campus_topology_config_site_id', 'campus_topology_config', type_='foreignkey')
    op.drop_table('campus_topology_config')
    op.drop_table('campus_site_nodes')
    op.drop_table('campus_site_pods')

    op.drop_table('dc_fabric_unit')
    op.drop_table('dc_fabric_template')

    op.drop_table('fmt_device_cards')
    op.drop_table('fmt_device_basic')
    if 'otn_device_basic' in inspector.get_table_names():
        op.drop_column('otn_device_basic', 'series')
        op.rename_table('otn_device_basic', 'dcp_device_basic')
    if 'otn_temp_data' in inspector.get_table_names():
        op.rename_table('otn_temp_data', 'dcp_temp_data')

    op.drop_table('snmp_alarm_trap_metadata')
    op.drop_table('snmp_alarm_trap_original_data')

    op.execute('drop table if exists resource_pool_asn_use_detail;')
    op.execute('drop table if exists resource_pool_asn_ranges;')
    op.execute('drop table if exists resource_pool_asn;')
    op.execute('drop table if exists resource_pool_area_use_detail;')
    op.execute('drop table if exists resource_pool_area_ranges;')
    op.execute('drop table if exists resource_pool_area;')
    op.execute('drop table if exists resource_pool_ip_use_detail;')
    op.execute('drop table if exists resource_pool_ip_ranges;')
    op.execute('drop table if exists resource_pool_ip;')
    op.execute('drop table if exists resource_pool_bridge_domain_use_detail;')
    op.execute('drop table if exists resource_pool_bridge_domain_ranges;')
    op.execute('drop table if exists resource_pool_bridge_domain;')
    op.execute('drop table if exists resource_pool_vrf_vlan_use_detail;')
    op.execute('drop table if exists resource_pool_vrf_vlan_ranges;')
    op.execute('drop table if exists resource_pool_vrf_vlan;')
    op.execute('drop table if exists resource_pool_vlan_domain;')
    op.execute('drop table if exists roce_task;')
    op.execute('drop table if exists host_info;')
    op.execute('drop table if exists email_rule_logs;')
    op.execute('drop table if exists email_rule_settings;')
    op.execute('drop table if exists email_server_setting;')
    op.execute('drop table if exists client_device_info;')

    op.drop_table('virtual_resource_vm')
    op.drop_table('virtual_resource_network')
    op.drop_table('virtual_resource_vpc')
    op.execute('drop table if exists virtual_resource_host_linkport;')
    op.execute('drop table if exists virtual_resource_host_link;')
    op.execute('drop table if exists virtual_resource_host;')
    op.drop_table('virtual_resource_pool_az')
    op.drop_table('dc_fabric_topology_node')
    op.drop_table('dc_fabric_topology')


    op.drop_table('config_distribution_task_for_dc')
    op.drop_table('config_distribution_task_for_campus')
