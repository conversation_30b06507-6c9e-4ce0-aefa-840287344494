import asyncio
import re
import csv

from pysnmp.hlapi.v3arch.asyncio import *
import logging
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
import numpy as np
from datetime import datetime, timedelta

logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S' 
)
LOG = logging.getLogger(__name__)


class SnmpConnector:
    OID_CONFIG = {
        "system_description": "*******.*******.0",
        "system_name": "*******.*******.0",
        "version": "*******.********.*******.1",
        "serial_number": "*******.********.********.1",
        "model": "*******.********.********.1",
        "if_name": "*******.*******.1.2",
        "if_oper_status": "*******.*******.1.8",
        "if_speed": "*******.*******.1.5",
        "if_phys_address": "*******.*******.1.6",
        "if_in_errors": "*******.*******.1.14",
        "if_in_discards": "*******.*******.1.13",
        "if_out_errors": "*******.*******.1.20",
        "if_out_discards": "*******.*******.1.19",
        "if_hc_in_octets": "*******.********.1.1.6",   # 输入字节数
        "if_hc_out_octets": "*******.********.1.1.10" # 输出字节数
    }

    def __init__(self, snmp_version, community=None, user_name=None, security_level='noAuthNoPriv', context_name=None,
                 auth_protocol='NONE', auth_key=None, priv_protocol='NONE', priv_key=None):
        """
        Initialize SNMP connection, supporting v1/v2c/v3 versions

        Parameters:
            snmp_version: SNMP version ('v1', 'v2c', 'v3')
            community: Community string (used for v1/v2c)
            user_name: Username (used for v3)
            security_level: SNMPv3 security level ('noAuthNoPriv', 'authNoPriv', 'authPriv')
            auth_protocol: Authentication protocol (used for v3, default 'NONE')
            auth_key: Authentication key (used for v3)
            priv_protocol: Privacy protocol (used for v3, default 'NONE')
            priv_key: Privacy key (used for v3)
            engine_id: Engine ID (used for v3, optional)
        """
        self.context_name = context_name or ''
        self.snmp_engine = SnmpEngine()
        self.target = None
        self.auth = None
        


        if snmp_version in ['v1', 'v2c']:
            if not community:
                raise ValueError("SnmpConnector v1/v2c requires community string")
            self.auth = CommunityData(community, mpModel=0 if snmp_version == 'v1' else 1)
        elif snmp_version == 'v3':
            auth_proto = {
                'NONE': usmNoAuthProtocol,
                'MD5': usmHMACMD5AuthProtocol,
                'SHA': usmHMACSHAAuthProtocol,
                'SHA-224': usmHMAC128SHA224AuthProtocol,
                'SHA-256': usmHMAC192SHA256AuthProtocol,
                'SHA-384': usmHMAC256SHA384AuthProtocol,
                'SHA-512': usmHMAC384SHA512AuthProtocol
            }.get(auth_protocol, usmNoAuthProtocol)

            priv_proto = {
                'NONE': usmNoPrivProtocol,
                'DES': usmDESPrivProtocol,
                '3DES': usm3DESEDEPrivProtocol,
                'AES-128': usmAesCfb128Protocol,
                'AES-192': usmAesCfb192Protocol,
                'AES-256': usmAesCfb256Protocol,
                'AES-192-BLUMENTHAL': usmAesBlumenthalCfb192Protocol,
                'AES-256-BLUMENTHAL': usmAesBlumenthalCfb256Protocol
            }.get(priv_protocol, usmNoPrivProtocol)

            if security_level == 'noAuthNoPriv':
                self.auth = UsmUserData(user_name)
            elif security_level == 'authNoPriv':
                self.auth = UsmUserData(
                    user_name,
                    authProtocol=auth_proto,
                    authKey=auth_key
                )
            elif security_level == 'authPriv':
                self.auth = UsmUserData(
                    user_name,
                    authProtocol=auth_proto,
                    authKey=auth_key,
                    privProtocol=priv_proto,
                    privKey=priv_key
                )
            else:
                raise ValueError("Unsupported security level, supported levels: noAuthNoPriv, authNoPriv, authPriv")
        else:
            raise ValueError("Unsupported SNMP version, supported versions: v1, v2c, v3")


    async def connect(self, host_ip, port=161, timeout=1, retries=3):
        """Connect to device"""
        self.target = await UdpTransportTarget.create(
            (host_ip, port),
            timeout=timeout,
            retries=retries
        )
        LOG.info(f"Successfully connected to device {host_ip}")
        return self

    async def get(self, oid):
        """Execute SNMP GET request"""
        if not self.target:
            raise Exception("Please connect to the device via connect first")
        
        try:
            iterator = get_cmd(
                self.snmp_engine,
                self.auth,
                self.target,
                ContextData(contextName=self.context_name),
                ObjectType(ObjectIdentity(oid))
            )

            error_indication, error_status, error_index, var_binds = await iterator

            if error_indication:
                raise Exception(f"SNMP error: {error_indication}")
            elif error_status:
                raise Exception(f"SNMP status error: {error_status.prettyPrint()} at {error_index}")

            for var_bind in var_binds:
                LOG.info(f"GET request successful: {str(var_bind[0])} = {str(var_bind[1])}")
                if not str(var_bind[1]):
                    LOG.error(f"GET request returned empty value: {str(var_bind[0])} = {str(var_bind[1])}")
                return str(var_bind[1])
            
        except Exception as e:
            LOG.error(f"SNMP GET request execution failed: {e}")
            raise e

    async def walk(self, oid, max_repetitions=25):
        """Execute SNMP WALK request"""
        if not self.target:
            raise Exception("Please connect to the device via connect first")

        result = {}
        last_oid = None
        var_binds = [ObjectType(ObjectIdentity(oid))]
        should_continue = True

        try:
            while should_continue:
                error_indication, error_status, error_index, var_binds = await bulk_cmd(
                    SnmpEngine(),
                    self.auth,
                    self.target,
                    ContextData(contextName=self.context_name),
                    0,
                    max_repetitions,  # 每次请求的最大OID数量
                    *var_binds,
                    lookupMib=False  # 禁用MIB解析，提高性能
                )

                if error_indication:
                    raise Exception(f"SNMP error: {error_indication}")
                elif error_status:
                    raise Exception(f"SNMP status error: {error_status.prettyPrint()} at {error_index}")

                if not var_binds:
                    break

                # 处理批量返回的所有OID
                for var_bind in var_binds:
                    current_oid, value = var_bind
                    current_oid_str = str(current_oid)
                    value_str = str(value)

                    # 检查是否超出初始OID范围
                    if current_oid_str[0:len(oid)] != oid:
                        should_continue = False
                        LOG.error(f"Exceeded OID range: {current_oid_str}, terminating WALK")
                        break

                    # 防止重复OID导致死循环
                    if current_oid_str == last_oid:
                        should_continue = False
                        LOG.error(f"Duplicate OID detected: {current_oid_str}, terminating WALK")
                        break

                    print({current_oid_str: value_str})

                    result[current_oid_str] = value_str

                    # 更新上一次的OID
                    last_oid = current_oid_str

                # 更新下一个要查询的OID
                var_binds = [var_binds[-1]] 

            return result
        
        except Exception as e:
            LOG.error(f"SNMP WALK request execution exception: {e}")
            raise e

    async def get_system_description(self):
        LOG.info("Obtaining device system description")
        return await self.get(self.OID_CONFIG["system_description"])

    async def get_version(self):
        LOG.info("Obtaining device version information")
        return await self.get(self.OID_CONFIG["version"])

    async def get_model(self):
        description = await self.get_system_description()
        if not description:
            LOG.error("System description is empty, unable to extract model")
            return None
        
        bracket_pattern = r'\(([^()]+)\)'
        bracket_match = re.search(bracket_pattern, description)
        
        if bracket_match:
            model = bracket_match.group(1).strip().replace(' ', '-')
            return model
        
        model_pattern = r'\b([A-Za-z]+\d+(?:-[A-Za-z\d]+)*)\b'
        model_match = re.search(model_pattern, description)
        
        if model_match:
            return model_match.group(1).strip()
        LOG.error("Unable to extract model from system description")
        return None

    async def get_system_name(self):
        LOG.info("Obtaining system name")
        return await self.get(self.OID_CONFIG["system_name"])

    async def get_serial_number(self):
        LOG.info("Obtaining device serial number")
        return await self.get(self.OID_CONFIG["serial_number"])

    async def get_interfaces(self):
        """Obtain all interface information (using WALK)"""
        if not self.target:
            raise Exception("Please connect to the device via connect first")

        try:
            (
                if_names,       # 接口名称
                if_status,      # 接口状态
                if_speed,       # 接口速度
                if_phys_address, # 物理地址
                if_in_errors,   # 输入错误数
                if_in_discards, # 输入丢弃数
                if_out_errors,  # 输出错误数
                if_out_discards, # 输出丢弃数
                if_hc_in_octets, # 新增：输入字节数
                if_hc_out_octets # 新增：输出字节数
            ) = await asyncio.gather(
                self.walk(self.OID_CONFIG["if_name"]),
                self.walk(self.OID_CONFIG["if_oper_status"]),
                self.walk(self.OID_CONFIG["if_speed"]),
                self.walk(self.OID_CONFIG["if_phys_address"]),
                self.walk(self.OID_CONFIG["if_in_errors"]),
                self.walk(self.OID_CONFIG["if_in_discards"]),
                self.walk(self.OID_CONFIG["if_out_errors"]),
                self.walk(self.OID_CONFIG["if_out_discards"]),
                self.walk(self.OID_CONFIG["if_hc_in_octets"]), 
                self.walk(self.OID_CONFIG["if_hc_out_octets"])
            )

            # 2. 解析接口信息（通过OID末尾的索引关联指标）
            interfaces = {}
            # 以接口名称为基准遍历
            for oid, name in if_names.items():
                # 提取接口索引（OID格式：*******.*******.1.2.xxx → 索引为xxx）
                index = oid.split('.')[-1]

                # 匹配同索引的其他指标（处理可能的缺失数据）
                status_oid = f"{self.OID_CONFIG['if_oper_status']}.{index}"
                speed_oid = f"{self.OID_CONFIG['if_speed']}.{index}"
                in_errors_oid = f"{self.OID_CONFIG['if_in_errors']}.{index}"
                phys_address_oid = f"{self.OID_CONFIG['if_phys_address']}.{index}"
                in_discards_oid = f"{self.OID_CONFIG['if_in_discards']}.{index}"
                out_errors_oid = f"{self.OID_CONFIG['if_out_errors']}.{index}"
                out_discards_oid = f"{self.OID_CONFIG['if_out_discards']}.{index}"
                hc_in_oid = f"{self.OID_CONFIG['if_hc_in_octets']}.{index}"
                hc_out_oid = f"{self.OID_CONFIG['if_hc_out_octets']}.{index}"

                # 状态值映射
                status_map = {
                    '1': 'up', '2': 'down', '3': 'testing',
                    '4': 'unknown', '5': 'dormant', '6': 'notPresent', '7': 'lowerLayerDown'
                }

                # 构建接口完整信息
                interfaces[name] = {
                    "index": index,
                    "status": status_map.get(if_status.get(status_oid), 'unknown'),
                    "speed": if_speed.get(speed_oid, 'unknown'),
                    "phys_address": if_phys_address.get(phys_address_oid, 'unknown'),
                    "in_errors": if_in_errors.get(in_errors_oid, '0'),
                    "in_discards": if_in_discards.get(in_discards_oid, '0'),
                    "out_errors": if_out_errors.get(out_errors_oid, '0'),
                    "out_discards": if_out_discards.get(out_discards_oid, '0'),
                    "in_bytes": if_hc_in_octets.get(hc_in_oid, '0'),
                    "out_bytes": if_hc_out_octets.get(hc_out_oid, '0')
                }

            LOG.info(f"Successfully obtained {len(interfaces)} interfaces information")
            return interfaces

        except Exception as e:
            LOG.error(f"Failed to obtain interface information: {e}")
            raise e
        
    def close(self):
        self.snmp_engine.close_dispatcher()
        LOG.info("SNMP connection closed")


class RJSnmpConnector(SnmpConnector):

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.vendor = "RJ"

    async def get_version(self):
        LOG.info(f"{self.vendor} device: Obtaining version information")
        info = await super().get_version()
        if info:
            version = info.split(",")[0].split(" ")[-1]
            LOG.info(f"Extracted version information: {version}")
            return version
        LOG.error(f"Standard OID failed to obtain version information")
        return None


class SKSnmpConnector(SnmpConnector):
    OID_CONFIG = {
        **SnmpConnector.OID_CONFIG,  
        "version": "*******.********.1.1.1.10.1",
    }

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.vendor = "SK"


class HHSnmpConnector(SnmpConnector):
    OID_CONFIG = {
        **SnmpConnector.OID_CONFIG,  
        "version": "*******.********.1.1.1.10.1",
    }

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.vendor = "HH"


class BDSnmpConnector(SnmpConnector):
    OID_CONFIG = {
        **SnmpConnector.OID_CONFIG,  
        "version": "*******.4.1.17409.2.3.1.3.1.1.9.1.0",
        "serial_number": "*******.4.1.17409.2.3.1.3.1.1.12.1.0",
    }

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.vendor = "BD"
    

class SnmpVendor:
    # 设备型号到厂商的映射表
    MODEL_VENDOR_MAPPING = {
        "S3100-8TMS-P": "RJ",
        "S3100-16TMS-P": "RJ",
        "S3100-16TF": "RJ",
        "S3100-16TF-P": "RJ",
        "S3270-10TM": "RJ",
        "S3270-10TM-P": "RJ",
        "S3270-24TM": "RJ",
        "S3270-24TM-P": "RJ",
        "S3270-48TM": "RJ",
        "S3410-24TS": "RJ",
        "S3410-24TS-P": "RJ",
        "S3410-48TS": "RJ",
        "S3410-48TS-P": "RJ",
        "S3410-24TF-P": "RJ",
        "S3910-24TS": "RJ",
        "S3910-24TF": "RJ",
        "S3910-48TF": "RJ",
        "S3910-48TS": "RJ",
        "S5810-48FS": "RJ",
        "S5810-28FS": "RJ",
        "S5810-28TS": "RJ",
        "S5810-48TS": "RJ",
        "S5810-48TS-P": "RJ",
        "S5860-20SQ": "RJ",
        "S5860-48SC": "RJ",
        "S5860-24XMG": "RJ",
        "S5860-24MG-U": "RJ",
        "S5860-48XMG": "RJ",
        "S5860-48XMG-U": "RJ",
        "S5860-48MG-U": "RJ",
        "S5860-24XB-U": "RJ",
        "SC9405": "RJ",
        
        "S3950-4T12S-R": "SK",
        "S5800-24T8S": "SK",
        "S5800-48T4S": "SK",
        "S5800-48F4SR": "SK",
        "S5850-24S2Q": "SK",
        "S5850-48B8C": "SK",
        "S5850-16T16BS2Q": "SK",
        "S5850-48S6Q-R": "SK",
        "S5850-24B4C": "SK",
        "S5850-24S2C": "SK",
        "S5850-48S8C": "SK",
        "S8550-6Q2C": "SK",
        "S8550-32C": "SK",
        "S8550-16Q8C": "SK",
        
        "S3200-8MG4S-U": "HH",
        "S3400C-24MG4S": "HH",
        "S3400C-24MG4S-P": "HH",
        "S5800-48MBQ": "HH",
        "S5850-48T4Q": "HH",
        "S5850-24XMG": "HH",
        "S5850-24XMG-U": "HH",
        "S5850-48XMG8C": "HH",
        "S5850C-12XMS2C": "HH",
        "S5850C-24S2C": "HH",
        "S5850C-24XMG2C": "HH",
        
        "S3150-8T2F": "BD",
        "S3150-8T2FP": "BD",
        "S3260-8T2FP": "BD",
        "S3260-16T4FP": "BD",
        "S3400-24T4FP": "BD",
        "S3400-24T4SP": "BD",
        "S3400-48T6SP": "BD",
        "S3700-24T4F": "BD",
        "S3900-48T6S-R": "BD",
        "S3900-24F4S-R": "BD",
        "S3900-24T4S-R": "BD",
        "S5300-12S": "BD",
        "S5500-48T6SP-R": "BD",
        "S5500-24TF6S": "BD",
        "S5500-48T6S": "BD",
        "S5500-48F6S": "BD",
        "IES3100-8TF": "BD",
        "IES3100-8TF-P": "BD",
        "IES3100-16TM": "BD",
        "IES5100-24TF": "BD",
        "IES5100-16TS": "BD",
        "IES5100-24FS": "BD",
        "IES5100-24TS-P": "BD",
        
        "IES3220-8T4F": "HS",
        "IES3220-8T4F-U": "HS",
        "IES3220-4T2F": "HS",
        "IES2100-8FE": "HS",
        "IES2100-5FE": "HS",
        "IES5120-28TS": "HS",
        "IES5120-28TS-P": "HS",
        "IES5120-48T4S": "HS",
        "IES5120-28TF": "HS",
        "IES5220-6S-3ES": "HS",
        "IES5220-30TS-3ES": "HS",
        "TSN3220-10S-U": "HS",
        "TSN3220-10S": "HS",
        
        "IES5110-20FMS": "PL"
    }

    def __init__(self, ip, snmp_version, community=None, **kwargs):
        self.ip = ip
        self.snmp_version = snmp_version
        self.community = community
        self.connector_kwargs = kwargs 
        self.model = None
        self.vendor = None
    
    async def _identify_vendor(self):
        """
        Connect to the device and obtain the model to identify the vendor
        """
        try:
            # 临时连接器用于识别厂商
            temp_connector = SnmpConnector(
                snmp_version=self.snmp_version,
                community=self.community,
                **self.connector_kwargs
            )
            await temp_connector.connect(self.ip)
            
            self.model = await temp_connector.get_model()
            if not self.model:
                LOG.error("Initial model retrieval failed")
            
            LOG.info(f"Detected device model: {self.model}")
            
            # 根据型号映射表确定厂商
            self.vendor = self.MODEL_VENDOR_MAPPING.get(self.model, "Unknown")
            LOG.info(f"Identified device vendor: {self.vendor}")
            
        except Exception as e:
            LOG.error(f"Device vendor identification failed: {str(e)}")
            raise e
        finally:
            if 'temp_connector' in locals():
                temp_connector.close()

    async def create_connector(self):
        await self._identify_vendor()
        
        # 根据识别的厂商创建特定的连接器
        if self.vendor == "RJ":
            return RJSnmpConnector(
                snmp_version=self.snmp_version,
                community=self.community,
                **self.connector_kwargs
            )
        elif self.vendor == "SK":
            return SKSnmpConnector(
                snmp_version=self.snmp_version,
                community=self.community,
                **self.connector_kwargs
            )
        elif self.vendor == "HH":
            return HHSnmpConnector(
                snmp_version=self.snmp_version,
                community=self.community,
                **self.connector_kwargs
            )
        elif self.vendor == "BD":
            return BDSnmpConnector(
                snmp_version=self.snmp_version,
                community=self.community,
                **self.connector_kwargs
            )
        else:
            # 未知厂商返回通用连接器
            return SnmpConnector(
                snmp_version=self.snmp_version,
                community=self.community,
                **self.connector_kwargs
            )

def build_snmp_config(data):
    """
    Build basic SNMP configuration by extracting parameters from input data
    
    Args:
        data (dict): Input data containing SNMP configuration parameters
    
    Returns:
        dict: Basic SNMP configuration with required parameters
    """
    ip = data.get('ip')
    snmp_version = data.get('snmpVersion')
    
    config = {
        "ip": ip,
        "mgt_ip": ip,
        "snmp_version": snmp_version,
    }
    
    if snmp_version in ['v1', 'v2c']:
        config["community"] = data.get('community')
    

    elif snmp_version == 'v3':
        config.update({
            "security_level": data.get('securityLevel'),
            "security_user": data.get('securityUser'),
            "context_name": data.get('contextName', '')
        })
        
        if config.get('security_level') in ['authNoPriv', 'authPriv']:
            config.update({
                "auth_protocol": data.get('authProtocol'),
                "auth_key": data.get('authPassword')
            })
        
        if config.get('security_level') == 'authPriv':
            config.update({
                "priv_protocol": data.get('privProtocol'),
                "priv_key": data.get('privPassword')
            })
    
    return config

async def fetch_snmp_device_info(config):
    """
    Retrieve device information (model, serial number, etc.) via SNMP and enrich the configuration
    
    Args:
        config (dict): Basic SNMP configuration containing connection parameters
    
    Returns:
        dict: Enriched configuration with device information retrieved via SNMP
    """
    try:
        if config['snmp_version'] in ['v1', 'v2c']:
            vender = SnmpVendor(
                ip=config['ip'],
                snmp_version=config['snmp_version'],
                community=config['community']
            )
        else:
            vender = SnmpVendor(
                ip=config['ip'],
                snmp_version=config['snmp_version'],
                user_name=config['security_user'],
                security_level=config['security_level'],
                context_name=config.get('context_name', ''),
                auth_protocol=config.get('auth_protocol', 'none'),
                auth_key=config.get('auth_key'),
                priv_protocol=config.get('priv_protocol', 'none'),
                priv_key=config.get('priv_key')
            )
        
        snmp = await vender.create_connector()
        await snmp.connect(config['ip'])
        
        tasks = [
            asyncio.create_task(snmp.get_model()),
            asyncio.create_task(snmp.get_serial_number()),  
            asyncio.create_task(snmp.get_version()),
            asyncio.create_task(snmp.get_system_name()),
        ]
        model, sn, version, sysname = await asyncio.gather(*tasks)
        
        if not model or not sn:
            raise ValueError("Failed to retrieve device model or serial number!")
        
        config.update({
            'model': model,
            'sn': sn,
            'version': version,
            'sysname': sysname
        })
        
        snmp.close()
        return config
    
    except Exception as e:
        raise e

async def get_device_info(snmp):
    # 同时创建所有SNMP请求任务
    tasks = [
        asyncio.create_task(snmp.get_system_description()),
        asyncio.create_task(snmp.get_serial_number()),
        asyncio.create_task(snmp.get_model()),
        asyncio.create_task(snmp.get_version()),
        asyncio.create_task(snmp.get_system_name())
    ]
    
    results = await asyncio.gather(*tasks)
    print(f"device info: {results}")
    return {
        "system_info": results[0],
        "serial_number": results[1],
        "model": results[2],
        "version": results[3],
        "name": results[4]
    }

async def collect_interface_stats(snmp, duration=600, interval=1):
    """
    Continuously collect interface byte count data (default 10 minutes, once per second)
    :param snmp: SNMP connector instance
    :param duration: Collection duration in seconds (10 minutes = 600 seconds)
    :param interval: Collection interval in seconds
    :return: Collected statistical data
    """
    stats = {}
    start_time = datetime.now()
    end_time = start_time + timedelta(seconds=duration)
    
    print(f"Starting interface data collection, duration {duration} seconds (10 minutes), collecting every {interval} seconds...")
    print(f"Collection start time: {start_time.strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"Estimated end time: {end_time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    while datetime.now() < end_time:
        current_time = datetime.now()
        try:
            interfaces = await snmp.get_interfaces()  # 获取接口信息
            
            for if_name, if_details in interfaces.items():
                # 初始化接口数据结构
                if if_name not in stats:
                    stats[if_name] = {
                        "timestamps": [],  # 时间戳列表
                        "if_in_errors": [],  # 输入错误数列表
                        "if_in_discards": [],  # 输入丢弃数列表
                        "if_out_errors": [],  # 输出错误数列表
                        "if_out_discards": [],  # 输出丢弃数列表
                        "in_bytes": [],    # 输入字节数列表
                        "out_bytes": []    # 输出字节数列表
                    }
                
                # 存储当前数据（转换为整数，避免字符串）
                stats[if_name]["timestamps"].append(current_time)
                stats[if_name]["if_in_errors"].append(int(if_details["in_errors"]))
                stats[if_name]["if_in_discards"].append(int(if_details["in_discards"]))
                stats[if_name]["if_out_errors"].append(int(if_details["out_errors"]))
                stats[if_name]["if_out_discards"].append(int(if_details["out_discards"]))
                stats[if_name]["in_bytes"].append(int(if_details["in_bytes"]))
                stats[if_name]["out_bytes"].append(int(if_details["out_bytes"]))
            
            # 打印进度（每60秒打印一次，避免刷屏）
            if (current_time - start_time).total_seconds() % 60 == 0:
                elapsed = int((current_time - start_time).total_seconds())
                print(f"Collected {elapsed} seconds, remaining {int((end_time - current_time).total_seconds())} seconds...")
        
        except Exception as e:
            print(f"Error occurred during collection: {str(e)}, will continue to next collection")
        
        # 等待下一个采集周期
        await asyncio.sleep(interval)
    
    print(f"Interface byte count data collection completed, collected {len(stats)} interfaces, total duration {int((datetime.now() - start_time).total_seconds())} seconds")
    return stats


def save_stats_to_csv(stats, device_name, output_file=None):
    """
    Save the collected interface byte count data to a CSV file
    :param stats: Collected statistical data
    :param device_name: Device name (used to generate the file name)
    :param output_file: Output file path, automatically generated by default
    """
    # 生成默认文件名（包含设备名和当前时间）
    if not output_file:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_file = f"{device_name}_bytes_stats_{timestamp}.csv"
    
    with open(output_file, mode='w', newline='', encoding='utf-8') as file:
        writer = csv.writer(file)
        # 写入表头
        writer.writerow(["Interface Name", "Timestamp", "if_in_errors", "if_in_discards", "if_out_errors", "if_out_discards", "In Bytes", "Out Bytes"])
        
        # 遍历接口数据
        for if_name, if_stats in stats.items():
            # 确保时间戳、in_bytes、out_bytes长度一致
            num_records = len(if_stats["timestamps"])
            for i in range(num_records):
                # 时间戳格式化为ISO格式
                timestamp_str = if_stats["timestamps"][i].isoformat()
                in_bytes = if_stats["in_bytes"][i]
                out_bytes = if_stats["out_bytes"][i]
                writer.writerow([if_name, timestamp_str, in_bytes, out_bytes])
    
    print(f"Data successfully saved to CSV file: {output_file}")
    print(f"File contains {sum(len(if_stats['timestamps']) for if_stats in stats.values())} records")

async def main():
    # 创建SNMP连接实例
    vender = SnmpVendor(
        ip='************',
        snmp_version='v2c',
        community='Public',
        # 以下v3参数在使用v2c时可省略
        # user_name='usr2',
        # security_level='authPriv',
        # auth_protocol='md5',
        # auth_key='Fscom123',
        # priv_protocol='aes128',
        # priv_key='Fscom123'
    )
    
    # 建立连接
    snmp = await vender.create_connector()
    await snmp.connect('************')
    
    try:
        # 1. Get device basic information
        # device_info = await get_device_info(snmp)
        # print("===== Device Basic Information =====")
        # print(f"Device Name: {device_info['name']}")
        # print(f"Device Model: {device_info['model']}")
        # print(f"Serial Number: {device_info['serial_number']}")
        # print(f"Firmware Version: {device_info['version']}")
        # print(f"System Description: {device_info['system_info']}\n")
        
        # 2. Get interface details
        interfaces = await snmp.get_interfaces()
        mac_addr = await snmp.get('*******.4.1.52642.********.********.5')
        print(f"Device Physical Address: {mac_addr}")
        print("===== Interface Details =====")
        print(f"Total detected {len(interfaces)} interfaces:")
        for if_name, if_details in interfaces.items():
            phys_addr = if_details['phys_address']
            phys_addr_bytes = phys_addr.encode('latin-1')
            mac_add = ':'.join(f'{b:02x}' for b in phys_addr_bytes)
            print(f"\nInterface Name: {if_name}")  # 接口名称
            print(f"  Index: {if_details['index']}")  # 接口索引
            print(f"  Status: {if_details['status']}")  # 接口状态
            print(f"  Speed: {if_details['speed']}")  # 接口速度
            print(f"  Physical Address: {mac_add}")  # 物理地址
            print(f"  Input Error Packets: {if_details['in_errors']}")  # 输入错误包数
            print(f"  Input Discarded Packets: {if_details['in_discards']}")  # 输入丢弃包数
            print(f"  Output Error Packets: {if_details['out_errors']}")  # 输出错误包数
            print(f"  Output Discarded Packets: {if_details['out_discards']}")  # 输出丢弃包数
            print(f"  Input Bytes: {if_details['in_bytes']}")  # 输入字节数
            print(f"  Output Bytes: {if_details['out_bytes']}")  # 输出字节数
        
        # # 3. 持续采集10分钟接口字节数数据（duration=600秒）
        # stats = await collect_interface_stats(snmp, duration=600, interval=1)
        
        # # 3. 保存数据到CSV
        # save_stats_to_csv(stats, device_name=device_info['name'])
    except Exception as e:
        raise e
            
    finally:
        # 关闭连接
        snmp.close()
        print("\n===== connection closed =====")


# async def main():

#     device_ips = {
#         '************': {'snmp_version': 'v3', 'community': 'Public', 'user_name': 'usr2', 'security_level': 'authPriv', 'auth_protocol': 'MD5', 'auth_key': 12345678, 'priv_protocol': 'AES', 'priv_key': 12345678},
#         # '************': {'snmp_version': 'v2c', 'community': 'Public'},
#         # '************': {'snmp_version': 'v3', 'community': 'Public'},
#         # '************': {'snmp_version': 'v2c', 'community': 'Public'},
#         # '************': {'snmp_version': 'v2c', 'community': 'Public123'},
#         # '************': {'snmp_version': 'v2c', 'community': 'Public123'},
#         # '************': {'snmp_version': 'v2c', 'community': 'Public'},
#         # '************': {'snmp_version': 'v2c', 'community': 'Public'},
#     }
    
#     for ip, config in device_ips.items():
#         await test_device(ip, **config)


if __name__ == "__main__":
    asyncio.run(main())

