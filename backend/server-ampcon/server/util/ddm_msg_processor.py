import json
import time
import logging
from typing import List, Dict, Any
from contextlib import contextmanager

from server.db.redis_common import RedisSessionFactory, RedisQueue
from server.util.utils import email_handler

LOG = logging.getLogger(__name__)


def process_ddm_msg(ddm_msg: dict):
    """
    处理 DDM 消息，检查各项指标是否超过阈值并生成告警，同时处理告警恢复
    
    Args:
        ddm_msg: DDM 消息字典
    """
    # 获取告警阈值
    warn_threshold = ddm_msg.get('warnThreshold', {})
    alarm_threshold = ddm_msg.get('alarmThreshold', {})
    
    # 获取静默告警类型列表
    silent_alert_types = ddm_msg.get('silentAlertTypes', [])
    
    # 检查整体指标 (跳过静默的告警类型)
    for metric_type in ['supplyVoltage', 'laserTemperature']:
        if not silent_alert_types or metric_type not in silent_alert_types:
            if metric_type == 'supplyVoltage':
                check_metric(
                    ddm_msg,
                    ddm_msg.get('supplyVoltage'),
                    'SupplyVoltage',
                    warn_threshold.get('supplyVoltageLower'),
                    warn_threshold.get('supplyVoltageUpper'),
                    alarm_threshold.get('supplyVoltageLower'),
                    alarm_threshold.get('supplyVoltageUpper')
                )
            elif metric_type == 'laserTemperature':
                check_metric(
                    ddm_msg,
                    ddm_msg.get('laserTemperature'),
                    'LaserTemperature',
                    warn_threshold.get('laserTemperatureLower'),
                    warn_threshold.get('laserTemperatureUpper'),
                    alarm_threshold.get('laserTemperatureLower'),
                    alarm_threshold.get('laserTemperatureUpper')
                )
    
    # 检查每个通道的指标 (这些指标不在静默列表中)
    channels_state = ddm_msg.get('channelsState', {})
    for channel, metrics in channels_state.items():
        check_channel_metrics(ddm_msg, channel, metrics, warn_threshold, alarm_threshold)

# def check_overall_metrics(ddm_msg: dict, warn_threshold: dict, alarm_threshold: dict):
#     """检查整体指标（supplyVoltage, laserTemperature）"""
#     # 检查电源电压
#     check_metric(
#         ddm_msg,
#         ddm_msg.get('supplyVoltage'),
#         'SupplyVoltage',
#         warn_threshold.get('supplyVoltageLower'),
#         warn_threshold.get('supplyVoltageUpper'),
#         alarm_threshold.get('supplyVoltageLower'),
#         alarm_threshold.get('supplyVoltageUpper')
#     )
    
#     # 检查激光温度
#     check_metric(
#         ddm_msg,
#         ddm_msg.get('laserTemperature'),
#         'LaserTemperature',
#         warn_threshold.get('laserTemperatureLower'),
#         warn_threshold.get('laserTemperatureUpper'),
#         alarm_threshold.get('laserTemperatureLower'),
#         alarm_threshold.get('laserTemperatureUpper')
#     )

def check_channel_metrics(ddm_msg: dict, channel: str, metrics: dict, warn_threshold: dict, alarm_threshold: dict):
    """检查通道指标（outputPower, inputPower, laserBiasCurrent）"""
    # 检查输出功率
    check_metric(
        ddm_msg,
        metrics.get('outputPower'),
        'OutputPower',
        warn_threshold.get('outputPowerLower'),
        warn_threshold.get('outputPowerUpper'),
        alarm_threshold.get('outputPowerLower'),
        alarm_threshold.get('outputPowerUpper'),
        channel
    )
    
    # 检查输入功率
    check_metric(
        ddm_msg,
        metrics.get('inputPower'),
        'InputPower',
        warn_threshold.get('inputPowerLower'),
        warn_threshold.get('inputPowerUpper'),
        alarm_threshold.get('inputPowerLower'),
        alarm_threshold.get('inputPowerUpper'),
        channel
    )
    
    # 检查激光偏置电流
    check_metric(
        ddm_msg,
        metrics.get('laserBiasCurrent'),
        'LaserBiasCurrent',
        warn_threshold.get('laserBiasCurrentLower'),
        warn_threshold.get('laserBiasCurrentUpper'),
        alarm_threshold.get('laserBiasCurrentLower'),
        alarm_threshold.get('laserBiasCurrentUpper'),
        channel
    )

def check_metric(ddm_msg: dict, value: float, alert_type: str, 
                warn_lower: float, warn_upper: float,
                alarm_lower: float, alarm_upper: float,
                channel: str = None):
    """
    检查单个指标是否超过阈值，并处理告警恢复
    
    Args:
        ddm_msg: DDM 消息字典
        value: 当前值
        alert_type: 告警类型
        warn_lower: 警告下限
        warn_upper: 警告上限
        alarm_lower: 告警下限
        alarm_upper: 告警上限
        channel: 通道号（可选）
    """
    if value is None:
        return
    
    # 激光偏置电流的阈值为0时，不生成告警
    if alert_type == "LaserBiasCurrent" and warn_lower == 0 and warn_upper == 0 and alarm_lower == 0 and alarm_upper == 0:
        return
    
    # 生成告警key
    alert_key = generate_alert_key(ddm_msg['switch_sn'], ddm_msg['interface'], alert_type, channel or '-1')
    
    # 检查是否超过告警阈值
    if value < alarm_lower:
        alert_level = 'error'
        threshold_type = 'Low Alarm'
        threshold_value = alarm_lower
        compare_type = 'lower'
    elif value > alarm_upper:
        alert_level = 'error'
        threshold_type = 'High Alarm'
        threshold_value = alarm_upper
        compare_type = 'higher'
    elif value < warn_lower:
        alert_level = 'warning'
        threshold_type = 'Low Warning'
        threshold_value = warn_lower
        compare_type = 'lower'
    elif value > warn_upper:
        alert_level = 'warning'
        threshold_type = 'High Warning'
        threshold_value = warn_upper
        compare_type = 'higher'
    else:
        check_alert_recovery(ddm_msg, alert_type, channel or '-1', alert_key)
        return

    # 指标类型映射
    type_map = {
        'SupplyVoltage': 'Voltage',
        'LaserTemperature': 'Temperature',
        'LaserBiasCurrent': 'Bias',
        'OutputPower': 'Tx Power',
        'InputPower': 'Rx Power'
    }
    metric_name = type_map.get(alert_type, alert_type)

    # 组织告警消息
    if compare_type == 'lower':
        alert_msg = f"The current {metric_name} is {value}, lower than the {threshold_type} threshold of {threshold_value}."
    else:
        alert_msg = f"The current {metric_name} is {value}, higher than the {threshold_type} threshold of {threshold_value}."

    from server.db.models.monitor import monitor_db
    # 添加 DDM 事件
    monitor_db.add_ddm_event(
        switch_sn=ddm_msg.get('switch_sn', ''),
        interface=ddm_msg.get('interface', ''),
        channel=channel or '-1',  # 整体指标使用通道 0
        module_name=ddm_msg.get('transportType', ''),
        module_type=ddm_msg.get('moduleType', ''),
        alert_level=alert_level,
        alert_type=alert_type,
        alert_msg=alert_msg
    )
    # 记录到 Redis
    redis_client = RedisSessionFactory.get_client()
    redis_client.set(alert_key, alert_msg)

def check_alert_recovery(ddm_msg: dict, alert_type: str, channel: str, alert_key: str):
    """
    检查是否需要恢复告警
    
    Args:
        ddm_msg: DDM 消息字典
        alert_type: 告警类型
        channel: 通道号
        alert_key: Redis 中的告警 key
    """
    redis_client = RedisSessionFactory.get_client()
    if redis_client.exists(alert_key):
        # 指标恢复正常，处理告警恢复
        from server.db.models.monitor import monitor_db

        monitor_db.resolve_ddm_event(
            switch_sn=ddm_msg.get('switch_sn', ''),
            interface=ddm_msg.get('interface', ''),
            alert_type=alert_type,
            channel=channel
        )
        # 从 Redis 中删除告警记录
        redis_client.delete(alert_key)

def generate_alert_key(switch_sn: str, interface: str, alert_type: str, channel: str) -> str:
    """
    生成告警在 Redis 中的 key
    
    Args:
        switch_sn: 交换机序列号
        interface: 接口名称
        alert_type: 告警类型
        channel: 通道号
    
    Returns:
        str: Redis key
    """
    return f"ddm_alert:{switch_sn}:{interface}:{alert_type}:{channel}"

# class DDMMsgProcessor:
#     def __init__(self):
#         # Initialize configuration
#         self.running = True
#         self.redis_queue = RedisQueue('ddm_msg')
#         self.batch_size = 10  # Batch processing size
#         self.max_retries = 3  # Maximum retry attempts
#         self.retry_delay = 1  # Retry delay in seconds
#         self.poll_interval = 1  # 轮询间隔

#     def stop(self):
#         """Stop the processor gracefully"""
#         LOG.info("Stopping processor...")
#         self.running = False

#     @contextmanager
#     def _get_db_session(self):
#         """数据库会话上下文管理器"""
#         session = monitor_db.get_session()
#         try:
#             yield session
#         except Exception:
#             session.rollback()
#             raise
#         finally:
#             session.close()

#     def _process_single_alert(self, alert: Dict[str, Any], db_session) -> bool:
#         """Process single alert"""
#         LOG.info(alert)
#         pass
#         # try:
#         #     monitor_db.add_event(
#         #         alert["labels"]["target"],
#         #         alert["labels"]["severity"],
#         #         alert["annotations"]["description"],
#         #         db_session
#         #     )
#         #     email_handler(alert, db_session)
#         #     return True
#         # except Exception as e:
#         #     LOG.error(f"Failed to process alert: {e}")
#         #     return False

#     def _process_batch(self, alerts: List[Dict[str, Any]], db_session) -> None:
#         """Process batch of alerts"""
#         for alert in alerts:
#             for retry in range(self.max_retries):
#                 try:
#                     if self._process_single_alert(alert, db_session):
#                         break
#                     if retry < self.max_retries - 1:
#                         time.sleep(self.retry_delay * (retry + 1))  # 指数退避
#                 except Exception as e:
#                     LOG.error(f"Error processing alert (attempt {retry + 1}): {e}")
#                     db_session.rollback()
#                     if retry < self.max_retries - 1:
#                         time.sleep(self.retry_delay * (retry + 1))

#     def run(self):
#         """Main processing loop"""
#         LOG.info("Ddm msg processor started")
        
#         while self.running:
#             try:
#                 # 使用阻塞方式获取告警
#                 alerts = self.redis_queue.get_many(
#                     self.batch_size, 
#                     block=True, 
#                     timeout=self.poll_interval
#                 )
                
#                 if alerts:
#                     with self._get_db_session() as db_session:
#                         LOG.info(f"Processing {len(alerts)} alerts")
#                         self._process_batch(alerts, db_session)
                
#             except Exception as e:
#                 LOG.error(f"Error in main processing loop: {e}")
#                 time.sleep(self.poll_interval)

#         LOG.info("Alert processor stopped")

# def ddm_msg_processor():
#     """Start the alert processor"""
#     processor = DDMMsgProcessor()
#     processor.run()
