import copy
import os
import configparser
import logging
import re
import time
import urllib
from functools import wraps
from sqlalchemy import and_, or_, func, case, orm
import subprocess
import zipfile
from datetime import datetime, timedelta
from jinja2 import Environment, FileSystemLoader
import random
import traceback
import ipaddress

import jinja2
import psutil
import functools
import telnetlib
import yaml
from flask import json, request, Response, current_app, jsonify
import flask_login
import tarfile, os
import smtplib
from email.mime.text import MIMEText
from email.mime.image import MIMEImage
from email.header import Header
from email.mime.multipart import MIMEMultipart

from server.ansible_lib.pica_lic import pica8_license
import geocoder
from server.constants import PICOS_V_SN, DATABASE_PASSWD, DATABASE_USER, NAME_MATCH_REGEX
from server.db.models.inventory import AssociationGroup, SwitchConfigBackup, SwitchSystemInfo, VpnConfig, SwitchParking, MachineHistoryInfo
from server.db.models.monitor import Event, LicenseCount, LicenseStatisttic
from server.db.models.user import User
from server.db.models.inventory import inven_db, SwitchAutoConfig, License, SystemConfig, Switch, MonitorTarget, Fabric, AssociationFabric, Site, AssociationSite
from server.db.models.user import user_db
from server.db.models.monitor import monitor_db
from server.db.models.automation import AnsibleDevice
from server.db.models import general
from server.db.models import inventory, dc_blueprint, dc_virtual_resource
from server.db.redis_common import RedisSessionFactory
from server import cfg, constants
from server.util import str_helper
from server.util import http_client
from server.util.encrypt_util import aes_cipher
import shutil

from tacacs_plus.client import TACACSClient
import socket
import json
from server.util.pica8_query import Pica8Query
from sqlalchemy import or_, asc, desc
from sqlalchemy.orm.query import Query

LOG = logging.getLogger(__name__)


class StringLoader(jinja2.BaseLoader):
    def get_source(self, environment, template):

        db_temp = general.general_db.get_model(general.GeneralTemplate, filters={"name": [template]})

        if not db_temp or db_temp == '':
            raise jinja2.TemplateNotFound(template)

        def uptodate():
            try:
                return False
            except OSError:
                return False

        return db_temp.j2_template, None, uptodate


env = jinja2.Environment(loader=jinja2.FileSystemLoader(searchpath="."),
                         trim_blocks=True,
                         lstrip_blocks=True)


def str2bool(strs):
    return True if strs.lower() == 'true' else False


def check_auth(username, password):
    user = user_db.query_user(user=username)
    if not user or not user.check_password_hash(password):
        return False
    return True


def tacacs_auth(username, password):  # return (tacacs_enable, (authentication_status, authorization_role))
    tacacs_setting = user_db.get_tacacs_config()
    if tacacs_setting.enable:
        for host in [tacacs_setting.server_host, tacacs_setting.server_host_ii]:
            if not host:
                continue
            cli = TACACSClient(host, 49, tacacs_setting.server_secret,
                               timeout=tacacs_setting.session_timeout, family=socket.AF_INET)
            try:
                auth = cli.authenticate(username, password)
                if auth.valid:
                    # authentication success
                    author = cli.authorize(username, arguments=[b"service=AmpCon", b"cmd="])
                    if author.valid:
                        # authorize success
                        group_name = None
                        try:
                            user_mapping = {}
                            role = None
                            user_priv = None
                            for arg in author.arguments:
                                arg = arg.decode()
                                if 'priv-lvl=' in arg:
                                    user_priv = int(arg.replace('priv-lvl=', '').replace('privilege-level=', ''), 10)
                                    user_mapping = json.loads(
                                        tacacs_setting.user_mapping) if tacacs_setting.user_mapping else {}
                                if 'group-name' in arg:
                                    group_name = arg.replace('group-name=', '')
                            for user_map in user_mapping.keys():
                                user_level_range = range(user_mapping[user_map][0], user_mapping[user_map][0] + 1)
                                if user_priv and user_priv in user_level_range:
                                    role = user_map
                            if role:
                                return True, (True, role), (('group', group_name) if group_name is not None else ('global', ''))
                            else:
                                return True, (True, 'readonly'), (('group', group_name) if group_name is not None else ('global', ''))
                        except Exception as _:
                            return True, (True, 'readonly'), (('group', group_name) if group_name is not None else ('global', ''))
                    else:
                        # authorize failed
                        return True, (True, 'readonly'), ('global', '')
                else:
                    # authentication failed
                    return True, (False, ''), ('global', '')
            except Exception as  _:
                continue
        # Add LOG for fallback
        LOG.warn('TACACS+ fallback: user "{0}" trying to login with local credential '.format(username))
        monitor_db.add_operation_log(username, '/login', 'tacacs_auth', 'success',
                                     'username: {0} password:XXXXX'.format(username),
                                     'TACACS+ fallback: user "{0}" trying to login with local credential '.format(
                                         username))
    return False, (False, ''), ('global', '')


def user_req(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        username = flask_login.current_user.id
        if flask_login.current_user.role == 'tacacs':
            return f(flask_login.current_user, *args, **kwargs)
        user = user_db.query_user(username)
        return f(user, *args, **kwargs)

    return decorated_function


def retry(retries):
    """
    Decorator to retry a function if it raises an exception.

    :param retries: Number of retry attempts.
    """
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            attempt = 0
            while attempt < retries:
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    attempt += 1
                    print(f"Attempt {attempt}/{retries} failed: {e}")
                    if attempt == retries:
                        raise e
                    time.sleep(1)
        return wrapper
    return decorator


def operation_log(method=None, contents='', require_methods=None):
    def _wrap(f):
        @wraps(f)
        def wrap(*args, **kwargs):

            result = f(*args, **kwargs)
            try:
                # add operation log
                user = flask_login.current_user.id
                if user and (not require_methods or request.method in require_methods):
                    path = request.path
                    params = request.form.copy().to_dict(
                        flat=True) or request.args.copy().to_dict(flat=True) or {}
                    if not params:
                        try:
                            params = request.get_json()
                        except:
                            params = {}

                    params.update(kwargs)
                    params_str = json.dumps(params)
                    params_str = params_str.replace('"', '').replace('{', '').replace('}', '').replace(',', '')
                    params_str = params_str.replace(': ', ':')
                    log_method = method or f.__name__
                    content = contents.format(*args, **params)
                    if isinstance(result, str):
                        status = 'success' if 'error' not in result else 'error'
                    elif isinstance(result, Response):
                        if result.status_code == 200:
                            data = result.data
                            try:
                                data = json.loads(data)
                                if 'status' in data:
                                    status = 'success' if str(data['status']) == '200' else 'error'
                                elif 'error' in data and data['error']:
                                    status = 'error'
                                elif ('fail' in data and data['fail']) or ('failed' in data and data['failed']):
                                    status = 'error'
                                else:
                                    status = 'success'
                            except Exception as  _:
                                status = 'success'
                        else:
                            status = 'error'
                    else:
                        status = 'success'
                    if status == 'success':
                        LOG.info('%s %s status:[%s]', user, content, status)
                    else:
                        LOG.error('%s %s status:[%s]', user, content, status)
                    monitor_db.add_operation_log(user, path, log_method, status, params=params_str, content=content)
            except Exception as ex:
                LOG.exception('error in add operation log [%s]', ex)

            return result

        return wrap

    return _wrap


def base_info(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        event = monitor_db.get_collection(Event, filters={'status': ['unread']}, sorts=[('create_time', False)])
        # event = db_session.query(Event).filter(Event.status == 'unread').all()[0:5]
        return f(event, *args, **kwargs)

    return decorated_function


def find_template_path(hardware_model, flag, version=None):
    # path = os.path.join('config_gen', hardware_model)
    # file_name = 'switch_' + flag + '_config' if flag != '' else 'switch_config'
    # if version:
    #     version_prefixs = re.findall('.*([2-3].[0-9]+).', version)
    #     if len(version_prefixs) > 0:
    #         file_name += '_' + version_prefixs[0].replace('.', '_')
    # file_name += '.j2'
    # return os.path.join(path, file_name)
    if flag != '':
        path = 'config_gen/' + hardware_model + '/switch_' + flag + '_config'
    else:
        path = 'config_gen/' + hardware_model + '/switch_config'
    if version:
        version_prefixs = re.findall('.*([2-3]\.[0-9]+).', version)
        if len(version_prefixs) > 0:
            return path + '_' + version_prefixs[0].replace('.', '_') + '.j2'
    return path + '.j2'


def generate_config(tem_path, data):
    template = env.get_template(tem_path)
    return template.render(data)


def get_CPU_state(time_count=1):
    return {'cpu_count': str(psutil.cpu_count(logical=False)),
            'cpu_percent': str(psutil.cpu_percent(time_count, 0)) + "%"}


def get_memory_state():
    phymem = psutil.virtual_memory()
    memory = dict()
    memory['total'] = str(int(phymem.total / 1024 / 1024)) + "M"
    memory['used'] = str(int(phymem.used / 1024 / 1024)) + "M"
    memory['free'] = str(int(phymem.free / 1024 / 1024)) + "M"
    memory['sum_mem'] = str(float(phymem.used / 1024 / 1024) / float(phymem.total / 1024 / 1024) * 100)[0:5] + "%"
    return memory


# function of disk state
def get_disk_satate():
    diskinfo = psutil.disk_usage('/')
    disk = {
        'total': str(int(diskinfo.total / 1024 / 1024 / 1024)) + "G",
        'used': str(int(diskinfo.used / 1024 / 1024 / 1024)) + "G",
        'free': str(int(diskinfo.free / 1024 / 1024 / 1024)) + "G",
        'sum_disk': str(float(diskinfo.used / 1024 / 1024 / 1024) / float(diskinfo.total / 1024 / 1024 / 1024) * 100)[
                    0:5] + "%"
    }
    return disk


# function of proscess state
def check_process_state(process_name):
    for i in psutil.process_iter():
        if i.name() == process_name:
            str_tmp = str(i.name()) + "-" + str(i.pid) + "-" + str(i.status())
            return (str_tmp)


def time_now():
    return time.strftime("%Y-%m-%d %H:%M:%S")


def send_email_with_event(sn, e_type):
    sub = "deploy %s %s" % (sn, e_type)
    switch_logs = inven_db.get_switch_log(sn)
    contents = [switch_log.content for switch_log in switch_logs]
    msg = '\n'.join(contents)
    users = user_db.get_collection(User, {'sn': [sn]})
    receivers = []
    for user in users:
        if user.type in ['admin', 'superuser'] and user.email:
            receivers.append(user.email)
    send_email(sub, msg, receivers)


def send_email(sub, msg, receivers):
    """
     send email msg to receivers
    :param sub: title for email
    :param msg: body for email
    :param receivers: send to users
    :return:
    """
    assert cfg.CONF.mail.server and cfg.CONF.mail.port and cfg.CONF.mail.user and cfg.CONF.mail.password, \
        "mail server,port,user,password must be set"
    message = MIMEText(msg, 'plain', 'utf-8')
    message['From'] = Header(cfg.CONF.mail.user, 'utf-8')
    message['To'] = Header(",".join(receivers), 'utf-8')
    message['Subject'] = Header(sub, 'utf-8')
    try:
        mail_server = smtplib.SMTP(cfg.CONF.mail.server, cfg.CONF.mail.port)
        mail_server.ehlo()
        mail_server.starttls()
        mail_server.login(cfg.CONF.mail.user, cfg.CONF.mail.password)
        mail_server.sendmail(cfg.CONF.mail.user, receivers, message.as_string())
        mail_server.quit()
    except Exception:
        return False
    return True


def send_alert_email(sn, sub, msg, msg_for_log, alert_level, alert_type, alert_msg, switch_scope, session=None):
    if not session:
        session = general.general_db.get_session()
    sender = session.query(general.EmailServerSetting).first()
    email_rules = session.query(general.EmailRuleSettings).all()
    if not sender:
        return False
    sender_email_address = sender.sender_email
    sender_is_authentication = sender.is_authentication
    sender_server = sender.host
    sender_username = sender.username
    sender_port = sender.port
    sender_password = sender.password
    is_enable_ssl = sender.ssl
    is_enable_tls = sender.tls
    if not email_rules:
        return True
    email_logs_info = []
    mail_server = None
    try:
        for email_rule in email_rules:
            pro_type = get_pro_type()
            if pro_type == "ampcon-dc":
                if switch_scope not in email_rule.fabric_name_list.split(','):
                    continue
            elif pro_type == "ampcon-campus":
                if switch_scope not in email_rule.site_name_list.split(','):
                    continue
            email_rule_level = json.loads(email_rule.alarm_level_settings)
            email_rule_type = json.loads(email_rule.alarm_type_settings)
            receiver_list = []
            if email_rule.enable and email_rule_level.get('alarm_level_' + 'warning' if alert_level == 'warn' else 'alarm_level_' + alert_level, False) and email_rule_type.get('alarm_type_' + alert_type, False):
                receivers = email_rule.email
                for receiver in receivers.split(','):
                    if not is_alert_email_silent(email_rule.rule_name, receiver, sub, alert_msg, email_rule.silent_time):
                        receiver_list.append(receiver)
                if not receiver_list:
                    continue
                else:
                    if is_enable_ssl:
                        mail_server = smtplib.SMTP_SSL(sender_server, sender_port, timeout=15)
                        mail_server.ehlo()
                    else:
                        mail_server = smtplib.SMTP(sender_server, sender_port, timeout=15)
                        mail_server.ehlo()
                        if is_enable_tls:
                            mail_server.starttls()
                    if sender_is_authentication:
                        mail_server.login(sender_username, sender_password)
                    template_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), "monitor", "email_template")
                    logo_path = os.path.join(template_dir, "logo.jpg")
                    background_path = os.path.join(template_dir, "background.jpg")
                    with open(logo_path, "rb") as img_file:
                        img_logo = MIMEImage(img_file.read(), "jpg")
                        img_logo.add_header("Content-ID", "<logo>")
                        img_logo.add_header("Content-Disposition", 'inline')
                    with open(background_path, "rb") as img_file:
                        img_background = MIMEImage(img_file.read(), "jpg")
                        img_background.add_header("Content-ID", "<background>")
                        img_background.add_header("Content-Disposition", 'inline')
                message = MIMEMultipart()
                message.attach(MIMEText(msg, "html"))
                message.attach(img_logo)
                message.attach(img_background)
                message['From'] = Header(sender_email_address, 'utf-8')
                message['To'] = Header(receivers, 'utf-8')
                message['Subject'] = Header(sub, 'utf-8')
                mail_server.sendmail(sender_email_address, receiver_list, message.as_string())
            send_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            if receiver_list:
                email_logs_info.append({
                    'email_rule_name': email_rule.rule_name,
                    'email_search_key': email_rule.rule_name + email_rule.email + sn + send_time + sub,
                    'receivers': ','.join(receiver_list),
                    'subject': sub,
                    'content': msg_for_log,
                    'status': 'success',
                    'error_message': '',
                    'send_time': send_time
                })
        if mail_server:
            mail_server.quit()
    except Exception as e:
        LOG.error(f'send alert email failed, {str(e)}')
        send_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        try:
            email_logs_info.append({
                'email_rule_name': email_rule.rule_name,
                'email_search_key': email_rule.rule_name + email_rule.email + sn + send_time + sub,
                'receivers': email_rule.email,
                'subject': sub,
                'content': msg_for_log,
                'status': 'failed',
                'error_message': traceback.format_exc(),
                'send_time': send_time
            })
        except:
            pass
    finally:
        if email_logs_info:
            with session.begin(subtransactions=True):
                if email_logs_info:
                    thirty_days_ago = datetime.now() - timedelta(days=30)
                    if is_need_to_delete_email_log():
                        session.query(general.EmailRuleLogs).filter(
                            general.EmailRuleLogs.create_time < thirty_days_ago).delete()
                for log in email_logs_info:
                    session.add(general.EmailRuleLogs(email_rule_name=log['email_rule_name'], email_search_key=log['email_search_key'], target_sn=sn, receivers=log['receivers'], subject=log['subject'], content=log['content'], status=log['status'], error_message=log['error_message'], create_time=log['send_time']))
                session.commit()


def send_test_email(config, receivers):
    email_logs_info = []
    sender_server = config.get('host')
    sender_port = config.get('port')
    sender_email = config.get('sender_email')
    is_enable_ssl = config.get('ssl')
    is_enable_tls = config.get('tls')
    sender_username = config.get('username')
    sender_password = config.get('password')
    is_authentication = config.get('is_authentication')
    if not receivers:
        return True

    template_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), "monitor", "email_template")
    env = Environment(loader=FileSystemLoader(template_dir))

    template = env.get_template("test_email.j2")
    msg = template.render()

    template_for_log = env.get_template("test_email_for_log.j2")
    msg_for_log = template_for_log.render()

    message = MIMEMultipart('related')
    logo_path = os.path.join(template_dir,"logo.jpg")
    background_path = os.path.join(template_dir,"background.jpg")

    message.attach(MIMEText(msg, "html"))
    with open(logo_path, "rb") as img_file:
        img = MIMEImage(img_file.read(), "jpg")
        img.add_header("Content-ID", "<logo>")
        img.add_header("Content-Disposition", 'inline')
        message.attach(img)
    with open(background_path, "rb") as img_file:
        img = MIMEImage(img_file.read(), "jpg")
        img.add_header("Content-ID", "<background>")
        img.add_header("Content-Disposition", 'inline')
        message.attach(img)
    message['From'] = sender_email
    message['To'] = receivers
    message['Subject'] = Header('Test Email.', 'utf-8')
    try:
        if is_enable_ssl:
            mail_server = smtplib.SMTP_SSL(sender_server, sender_port, timeout=15)
            mail_server.ehlo()
        else:
            mail_server = smtplib.SMTP(sender_server, sender_port, timeout=15)
            mail_server.ehlo()
            if is_enable_tls:
                mail_server.starttls()
        if is_authentication:
            mail_server.login(sender_username, sender_password)
        mail_server.sendmail(sender_email, receivers.split(','), message.as_string())
        send_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        email_logs_info.append({
                'email_rule_name': "Test email sent",
                'email_search_key': 'Test Email.' + send_time,
                'receivers': receivers,
                'subject': 'Test Email.',
                'content': msg_for_log,
                'status': 'success',
                'error_message': '',
                'send_time': send_time
            })
        mail_server.quit()
        return True
    except Exception as e:
        LOG.error(f'send test email failed, {str(e)}')
        return False
    finally:
        session = general.general_db.get_session()
        with session.begin(subtransactions=True):
            if email_logs_info:
                thirty_days_ago = datetime.now() - timedelta(days=30)
                session.query(general.EmailRuleLogs).filter(
                    general.EmailRuleLogs.create_time < thirty_days_ago).delete()
            for log in email_logs_info:
                session.add(general.EmailRuleLogs(email_rule_name=log['email_rule_name'],
                                                  email_search_key=log['email_search_key'], target_sn="",
                                                  receivers=log['receivers'], subject=log['subject'],
                                                  content=log['content'], status=log['status'],
                                                  error_message=log['error_message'], create_time=log['send_time']))
            session.commit()


def is_email_server_available(host, port, username, password, is_enable_ssl, is_enable_tls):
    try:
        if is_enable_ssl:
            mail_server = smtplib.SMTP_SSL(host, port, timeout=10)
            mail_server.ehlo()
        else:
            mail_server = smtplib.SMTP(host, port, timeout=10)
            mail_server.ehlo()
            if is_enable_tls:
                mail_server.starttls()
        mail_server.login(username, password)
        mail_server.quit()
    except Exception as e:
        LOG.error(f'check email server failed, {str(e)}')
        return False
    return True


def is_alert_email_silent(rule_name, email_address, subject, alert_msg, silent_time=900):
    if silent_time == 0:
        return False
    redis_session = RedisSessionFactory.get_client()
    current_email_redis_key = f'alert:email:{email_address}:{rule_name}:{subject}:{alert_msg}'
    if redis_session.get(current_email_redis_key):
        LOG.info('email %s %s %s is in silent time', email_address, rule_name, subject)
        return True
    else:
        redis_session.setex(current_email_redis_key, silent_time, 1)
        return False


def is_need_to_delete_email_log():
    redis_session = RedisSessionFactory.get_client()
    date_str = datetime.now().strftime('%Y-%m-%d')
    current_email_redis_key = f'alert:email:current_data:{date_str}'
    if redis_session.get(current_email_redis_key):
        return False
    else:
        redis_session.setex(current_email_redis_key, 86400, 1)
        return True


def generate_config_from_yaml(template_path, yaml_path):
    yaml_data = yaml.load(open(yaml_path))
    template = env.get_template(template_path)
    return template.render(yaml_data)


def str_json(yaml_str):
    yaml_str_new = yaml_str.replace("u'", '"').replace("'", '"')
    return json.loads(yaml_str_new)


def update_db_license_count():
    LOG.info("update db license count & generate license portal token started.")
    try:
        system_config = inven_db.get_mgt_system_config()
        if system_config:
            LOG.debug("get license count by user %s", system_config.license_portal_user)
            inventory_list = pica8_license(system_config_name=constants.GLOBAL_CONFIG_TAG).get_inventory_all()
            for inventory in inventory_list:
                if not inventory:
                    LOG.error("error get license count from %s", system_config.license_portal_url)
                elif 'error' in inventory:
                    LOG.error('error in get inventory from %s, message %s', system_config.license_portal_url,
                              inventory['error'])
                elif 'CompanyName' in inventory:
                    update_num = monitor_db.update_model(LicenseStatisttic, {}, {'total': inventory['TotalCount'],
                                                                                 'remain': [inventory['AvaliableCount']]})
                    if update_num < 1:
                        monitor_db.insert(
                            LicenseStatisttic(total=inventory['TotalCount'], remain=inventory['AvaliableCount']))
                elif 'response' in inventory:
                    for license_count in inventory['response']:
                        update_num = monitor_db.update_model(LicenseCount, {'speed_type': [license_count['speed']],
                                                                            'feature_type': [license_count['type']]},
                                                             {'total': license_count['total'],
                                                              'remain': [license_count['remaining']]})
                        if update_num < 1:
                            monitor_db.insert(LicenseCount(speed_type=license_count['speed'],
                                                           feature_type=license_count['type'],
                                                           total=license_count['total'],
                                                           remain=license_count['remaining']))
        else:
            LOG.error("license portal token haven't set")
    except Exception as e:
        LOG.exception("error in update db license count %s", e)
    LOG.info("update db license count & generate license portal finished.")


def update_cpu_mem_record():
    cpu_info = round(float(get_CPU_state()['cpu_percent'].strip("%")), 2)
    mem_info = round(float(get_memory_state()['sum_mem'].strip("%")), 2)
    disk_info = round(float(get_disk_satate()['sum_disk'].strip("%")), 2)
    session = inven_db.get_session()

    with session.begin(subtransactions=True):
        mem_cpu_info = MachineHistoryInfo(mem=str(mem_info), cpu=str(cpu_info), disk=str(disk_info))
        session.add(mem_cpu_info)
        total_count = session.query(MachineHistoryInfo).count()
        if total_count > 6048:
            rows_to_delete = total_count - 6048
            oldest_rows = session.query(MachineHistoryInfo).order_by(MachineHistoryInfo.id).limit(rows_to_delete)
            for row in oldest_rows:
                session.delete(row)


def update_alarm_logs_read_tag():
    try:
        session = monitor_db.get_session()
        session.query(Event).filter(Event.status == 'unread', Event.create_time < datetime.now() - timedelta(days=7)).update({'status': 'read'})
    except Exception as e:
        LOG.exception("error in update alarm logs read tag %s", e)


def update_switch_status(interval):
    while True:
        # get status
        pass


def update_switch_status1(f):
    @wraps(f)
    def _update_warp(*args, **kwargs):
        res, status = f(*args, **kwargs)
        LOG.info('update switch %s status %s', args[0], status)
        inven_db.update_switch_status(args[0], status)
        return res, status

    return _update_warp


def un_tar(tar_path, model):
    tar = tarfile.open(tar_path)
    untar_file_final = os.path.join('patch_gen', model)
    if os.path.exists(untar_file_final):
        shutil.rmtree(untar_file_final)
    if not os.path.exists(untar_file_final):
        os.makedirs(untar_file_final)
    names = tar.getnames()
    for name in names:
        tar.extract(name, untar_file_final)
    return untar_file_final


def get_latlong(address):
    # url = 'None'
    # api_name = 'osm'
    # if url == None:
    #     g_result = getattr(geocoder, api_name)(address, proxies=cfg.CONF.license_portal_proxy)
    # else:
    #     g_result = getattr(geocoder, api_name)(address, proxies=cfg.CONF.license_portal_proxy)
    return Pica8Query(address, proxies=cfg.CONF.license_portal_proxy)


def get_db_sync_ip():
    result_proxy = None
    try:
        result_proxy = inven_db.get_session().execute('show slave status')
        replication_status = result_proxy.first()
        if not replication_status:
            return None, None, False
        return replication_status['Master_Host'], replication_status['Master_Server_Id'], \
               replication_status['Slave_IO_Running'] == 'Yes' and replication_status['Slave_SQL_Running'] == 'Yes'
    except Exception as e:
        LOG.exception('error in get db sync ip %s', e)
        return None, None, False
    finally:
        if result_proxy:
            result_proxy.close()


def get_switch_default_user(sn=None, system_config_name=None):
    if system_config_name:
        config = inven_db.get_system_config_by_config_name(system_config_name)
    elif sn:
        config = inven_db.get_system_config_by_sn(sn)
    else:
        config = inven_db.get_global_system_config()
    if not config:
        # to ensure the default user and password is not empty
        config = inven_db.get_global_system_config()
    return config.switch_op_user, config.switch_op_password


def multi_table_page_helper(args, models, joins, rule=None, extra_filter=None, json_data=True):
    """
    e.g.::
        multi_table_page_helper(request.args, (Switch, SwitchLog,), ((SwitchLog, Switch.sn == SwitchLog.sn,),),
                                rule=(Switch.sn=='test',), extra_filter=(Switch.status=='Staged'), josn_data=False)

    :param args: requests.args
    :param models: database model to query
    :param joins: join conditions
    :param rule: filter rules
    :param extra_filter: not Datatable filter
    :param json_data: if true return dict else return json str
    :return:
    """
    start = int(args.get('start'))
    length = int(args.get('length'))
    order_column_index = args.get('order[0][column]')
    order_column = args.get('columns[' + order_column_index + '][data]')
    order_dir = args.get('order[0][dir]')

    order = None
    if order_column:
        for model in models:
            column = getattr(model, order_column, None)
            if column:
                if order_dir == 'desc':
                    order = (column.desc(),)
                else:
                    order = (column.asc(),)
                break

    session = inven_db.get_session()
    with session.begin():
        query = session.query(*models)
        for join in joins:
            query = query.outerjoin(*join)

        if order is not None:
            query = query.order_by(*order)

        if extra_filter is not None:
            query = query.filter(extra_filter)

        records_total = query.count()

        if rule is not None:
            query = query.filter(rule)

        records_filtered = query.count()
        results = query.slice(start, start + length).all()

        data = []
        for result in results:
            first_slice = result[0].make_dict()
            for obj in result[1:]:
                if not obj:
                    continue
                prefix = obj.__class__.__name__.lower() + '_'
                first_slice.update(dict(((prefix + col.name, getattr(obj, col.name)) for col in obj.__table__.columns
                                         if getattr(obj, col.name))))
            data.append(first_slice)

        if json_data:
            return json.dumps({'data': data, 'recordsTotal': records_total, 'recordsFiltered': records_filtered},
                              default=str)
        else:
            return {'data': data, 'recordsTotal': records_total, 'recordsFiltered': records_filtered}


def page_helper(args, model, rule=None, extra_filter=None, json_data=True, session=None, order_by=None):
    start = int(args.get('start'))
    length = int(args.get('length'))
    draw = int(args.get('draw'))

    order_column_index = args.get('order[0][column]')
    order_column = args.get('columns[' + order_column_index + '][data]')
    order_dir = args.get('order[0][dir]')

    if order_column:
        if order_dir == 'desc':
            order = getattr(model, order_column).desc()
        else:
            order = getattr(model, order_column).asc()
    else:
        order = None

    session = session or inven_db.get_session()
    with session.begin():

        if model == Switch:
            query = query_switch()
        elif model == SwitchAutoConfig:
            query = query_switch_auto_config()
        else:
            query = session.query(model)

        if order_by is not None:
            query = query.order_by(order_by)
        if order is not None:
            query = query.order_by(order)

        if extra_filter is not None and type(extra_filter) == list:
            query = query.filter(*extra_filter)
        elif extra_filter is not None:
            query = query.filter(extra_filter)

        records_total = query.count()

        if rule is not None:
            query = query.filter(rule)

        records_filtered = query.count()
        results = query.slice(start, start + length).all()
        if json_data:
            if model == Switch:
                data = []
                for result in results:
                    switch_dict = result.make_dict(ignore_none=False)
                    if switch_dict.get('platform_model', '') and switch_dict.get('platform_model',
                                                                                 '') in constants.BLACK_BOX_MODEL_LIST:
                        switch_dict['is_black_box_model'] = True
                    else:
                        switch_dict['is_black_box_model'] = False
                    data.append(switch_dict)
            else:
                data = [result.make_dict(ignore_none=False) for result in results]

            return jsonify({'data': data, 'recordsTotal': records_total, 'recordsFiltered': records_filtered})
        else:
            return {'data': results, 'recordsTotal': records_total, 'recordsFiltered': records_filtered}


def new_page_helper(model, offset, order=None, rule=None, extra_filter=None):
    start = offset.get("start", 0)
    end = offset.get("end", 10)

    if order:
        order_column = order.get("column")
        order_dir = order.get("dir")
        if order_dir == 'desc':
            order = getattr(model, order_column).desc()
        else:
            order = getattr(model, order_column).asc()
    else:
        order = None

    session = inven_db.get_session()
    with session.begin():

        if model == Switch:
            query = query_switch()
        elif model == SwitchAutoConfig:
            query = query_switch_auto_config()
        else:
            query = session.query(model)

        if order is not None:
            query = query.order_by(order)

        if extra_filter is not None and type(extra_filter) == list:
            query = query.filter(*extra_filter)
        elif extra_filter is not None:
            query = query.filter(extra_filter)

        records_total = query.count()

        if rule is not None:
            query = query.filter(rule)

        records_filtered = query.count()
        results = query.slice(start, end).all()

        if model == Switch:
            data = []
            for result in results:
                switch_dict = result.make_dict(ignore_none=False)
                if switch_dict.get('platform_model', '') and switch_dict.get('platform_model',
                                                                                '') in constants.BLACK_BOX_MODEL_LIST:
                    switch_dict['is_black_box_model'] = True
                else:
                    switch_dict['is_black_box_model'] = False
                data.append(switch_dict)
        else:
            data = [result.make_dict(ignore_none=False) for result in results]

        return data, records_total, records_filtered


def page_helper_switch_with_system_config(args, selected_system_config_id=None, rule=None, json_data=True, order_by=None, session=None):
    start = int(args.get('start'))
    length = int(args.get('length'))
    draw = int(args.get('draw'))

    order_column_index = args.get('order[0][column]')
    order_column = args.get('columns[' + order_column_index + '][data]')
    order_dir = args.get('order[0][dir]')

    query = query_switch(system_config_id=selected_system_config_id)

    session = session or inven_db.get_session()
    with session.begin():

        records_total = query.count()

        if rule is not None:
            query = query.filter(rule)

        query = query.subquery()

        if order_column:
            if order_dir == 'desc':
                order = getattr(query.c, order_column).desc()
            else:
                order = getattr(query.c, order_column).asc()
        else:
            order = None

        order_list = [query.c.selected.desc()]
        if order_by is not None:
            order_list.append(order_by)
        if order is not None:
            order_list.append(order)

        query = session.query(query).order_by(*order_list)

        records_filtered = query.count()
        results = query.slice(start, start + length).all()
        data = [dict(result) for result in results]

        if json_data:
            return jsonify({'data': data, 'recordsTotal': records_total, 'recordsFiltered': records_filtered})
        else:
            return {'data': data, 'recordsTotal': records_total, 'recordsFiltered': records_filtered}


def page_helper_lifecycle(args, rule=None, status_filter=None, group_name=None, session=None, is_filter_group=True):
    start = int(args.get('start'))
    length = int(args.get('length'))
    draw = int(args.get('draw'))

    order_column_index = args.get('order[0][column]')
    order_column = args.get('columns[' + order_column_index + '][data]')
    order_dir = args.get('order[0][dir]')

    if order_column:
        column = getattr(Switch, order_column, None)
        if not column:
            if order_column == 'license_status':
                order_column = 'status'
            column = getattr(License, order_column, None)

        if order_column == 'flag':
            column = SwitchConfigBackup.back_up_type

        if order_column == 'backup_time':
            column = SwitchConfigBackup.modified_time

        if not column:
            column = getattr(SwitchSystemInfo, order_column, None)

        if order_dir == 'desc':
            if order_column == 'version':
                order = (column.desc(), Switch.revision.desc())
            else:
                order = (column.desc(),)
        else:
            if order_column == 'version':
                order = (column.asc(), Switch.revision.asc())
            else:
                order = (column.asc(),)
    else:
        order = None

    session = session or inven_db.get_session()
    with session.begin():

        query = session.query(Switch, License, SwitchSystemInfo, SwitchConfigBackup) \
            .outerjoin(License, Switch.sn == License.sn_num) \
            .outerjoin(SwitchSystemInfo, Switch.platform_model == SwitchSystemInfo.model) \
            .outerjoin(SwitchConfigBackup,
                       and_(Switch.sn == SwitchConfigBackup.sn, Switch.mgt_ip == SwitchConfigBackup.ip))

        if is_filter_group:
            query = query_switch(query, [group_name] if group_name else None)

        if order is not None:
            query = query.order_by(*order)

        if status_filter is not None and type(status_filter) == list:
            query = query.filter(*status_filter)
        elif status_filter is not None:
            query = query.filter(status_filter)

        records_total = query.count()

        if rule is not None:
            query = query.filter(rule)

        records_filtered = query.count()

        results = query.slice(start, start + length).all()
        data = []
        for result in results:
            switch_dict = result.Switch.make_dict()
            if switch_dict.get('platform_model', '') and switch_dict.get('platform_model', '') in constants.BLACK_BOX_MODEL_LIST:
                switch_dict['is_black_box_model'] = True
            else:
                switch_dict['is_black_box_model'] = False
            if 'version' not in switch_dict:
                data.append(switch_dict)
                continue
            if result.License:
                license_dict = result.License.make_dict()
                license_dict.pop('create_time')
                license_dict.pop('modified_time')
                license_dict.pop('sn_num')
                license_dict.pop('id')
                switch_dict['license_status'] = license_dict.pop('status')
                switch_dict.update(license_dict)
            if result.SwitchSystemInfo:
                switch_dict['up_to_date_version'] = result.SwitchSystemInfo.up_to_date_version
            if result.SwitchConfigBackup:
                if result.SwitchConfigBackup.back_up_type == 0:
                    switch_dict['flag'] = 'Unknown'
                elif result.SwitchConfigBackup.back_up_type == 1:
                    switch_dict['flag'] = 'R'
                else:
                    switch_dict['flag'] = 'U'
                switch_dict['backup_time'] = result.SwitchConfigBackup.modified_time

            if switch_dict.get('revision', ''):
                switch_dict['version'] = switch_dict.pop('version') + '/' + switch_dict.pop('revision')
            else:
                switch_dict.pop('revision')
                switch_dict['version'] = switch_dict.pop('version')
            data.append(switch_dict)

        return jsonify({'data': data, 'recordsTotal': records_total, 'recordsFiltered': records_filtered})

def new_page_helper_lifecycle(extra_filter, group=None, selected_platform=None, fabric=None, site=None, use_selected=False, selected_group=None):
    data = request.get_json()
    filter_fields = data.get("filterFields", [])
    sort_fields = data.get("sortFields", [])
    search_fields = data.get("searchFields", {})
    page_num = data.get('page', 1)
    page_size = data.get('pageSize', 10)

    start = (page_num - 1) * page_size
    end = start + page_size

    def filter_query(query, field, value, matchMode):
        if matchMode == "exact":
            query = query.filter(field == value)
        elif matchMode == "fuzzy":
            query = query.filter(field.like(f"%{value}%"))
        return query

    def sort_query(query, field, order):
        if order == "asc":
            query = query.order_by(asc(field))
        elif order == "desc":
            query = query.order_by(desc(field))
        return query

    session = inven_db.get_session()
    with session.begin():
        query = session.query(Switch, License, SwitchSystemInfo, SwitchConfigBackup) \
            .outerjoin(License, Switch.sn == License.sn_num) \
            .outerjoin(SwitchSystemInfo, Switch.platform_model == SwitchSystemInfo.model) \
            .outerjoin(SwitchConfigBackup,
                       and_(Switch.sn == SwitchConfigBackup.sn, Switch.mgt_ip == SwitchConfigBackup.ip))

        if extra_filter is not None and type(extra_filter) == list:
            query = query.filter(*extra_filter)
        elif extra_filter is not None:
            query = query.filter(extra_filter)

        if use_selected:
            query = query_switch(query)
        else:
            query = query_switch(query, group=[group] if group else None)
        if fabric:
            fabric_name_id_mapper = dict(map(lambda x: (x.fabric_name, x.id), session.query(inventory.Fabric).filter(inventory.Fabric.fabric_name.in_(fabric)).all()))
            query = query.join(inventory.AssociationFabric, inventory.Switch.id == inventory.AssociationFabric.switch_id).filter(inventory.AssociationFabric.fabric_id.in_(fabric_name_id_mapper.values()))
        elif site:
            site_name_id_mapper = dict(map(lambda x: (x.site_name, x.id), session.query(inventory.Site).filter(inventory.Site.site_name.in_(site)).all()))
            query = query.join(inventory.AssociationSite, inventory.Switch.id == inventory.AssociationSite.switch_id).filter(inventory.AssociationSite.site_id.in_(site_name_id_mapper.values()))
        elif selected_group:
            query = query.join(inventory.AssociationGroup, inventory.Switch.sn == inventory.AssociationGroup.switch_sn).filter(inventory.AssociationGroup.group_name.in_(selected_group))
        if selected_platform:
            query = query_switch(query).order_by((Switch.platform == selected_platform).desc(), Switch.switch_model)

        if use_selected:
            sn_list = list(map(lambda x: x.switch_sn, session.query(AssociationGroup).filter(AssociationGroup.group_name == group).all()))
            query = query.add_columns(case([(Switch.sn.in_(sn_list), True)], else_=False).label('selected')).order_by(desc('selected'))

        if filter_fields:
            for field in filter_fields:
                for filter in field["filters"]:
                    if field["field"] in ["sn", "host_name", "mgt_ip", "address", "hwid","platform_model"]:
                        query = filter_query(query, getattr(Switch, field["field"] if field[ "field"] != "platform" else "platform_model"),filter["value"], filter["matchMode"])
                    elif field["field"] in ["platform"]:
                        query = filter_query(query, SwitchSystemInfo.platform, filter["value"], filter["matchMode"])
                    elif field["field"] in ["version"]:
                        query = query.filter(func.concat(Switch.version, '/', Switch.revision).like(f"%{filter['value']}%"))
                    elif field["field"] in ["license_status"]:
                        query = filter_query(query, License.status, filter["value"], filter["matchMode"])
                    elif field["field"] == "backup_time":
                        query = filter_query(query, SwitchConfigBackup.modified_time, filter["value"], filter["matchMode"])
                    elif field["field"] == "license_expired":
                        query = filter_query(query, License.license_expired, filter["value"], filter["matchMode"])

        if sort_fields:
            for sort in sort_fields:
                if sort["field"] == "license_status":
                    query = sort_query(query, License.status, sort["order"])
                elif sort["field"] == "flag":
                    query = sort_query(query, SwitchConfigBackup.back_up_type, sort["order"])
                elif sort["field"] == "backup_time":
                    query = sort_query(query, SwitchConfigBackup.modified_time, sort["order"])
                elif getattr(Switch, sort["field"], None):
                    query = sort_query(query, getattr(Switch, sort["field"]), sort["order"])
                elif getattr(License, sort["field"], None):
                    query = sort_query(query, getattr(License, sort["field"]), sort["order"])
                elif getattr(SwitchSystemInfo, sort["field"], None):
                    query = sort_query(query, getattr(SwitchSystemInfo, sort["field"]), sort["order"])

        if search_fields:
            search_value = search_fields["value"]
            search_conditions = []
            for field in search_fields["fields"]:
                if field in ["sn", "host_name", "mgt_ip", "post_deployed_config"]:
                    search_conditions.append(getattr(Switch, field).like(f"%{search_value}%"))
                elif field in ["license_expired"]:
                    search_conditions.append(getattr(License, field).like(f"%{search_value}%"))
                elif field == "license_status":
                    search_conditions.append(License.status.like(f"%{search_value}%"))
                elif field == "backup_time":
                    search_conditions.append(SwitchConfigBackup.modified_time.like(f"%{search_value}%"))
                elif field == "version":
                    search_conditions.append(Switch.version.like(f"%{search_value}%"))
                    search_conditions.append(Switch.revision.like(f"%{search_value}%"))
            query = query.filter(or_(*search_conditions))

        total_count = query.count()
        query = query.slice(start, end)
        data = []
        for result in query:
            switch_dict = result.Switch.make_dict()
            switch_dict['platform'] = result.Switch.platform
            if switch_dict.get('platform_model', '') and switch_dict.get('platform_model', '') in constants.BLACK_BOX_MODEL_LIST:
                switch_dict['is_black_box_model'] = True
            else:
                switch_dict['is_black_box_model'] = False
            if 'version' not in switch_dict:
                if use_selected:
                    switch_dict['selected'] = getattr(result, 'selected', False)
                data.append(switch_dict)
                continue
            if result.License:
                license_dict = result.License.make_dict()
                license_dict.pop('create_time')
                license_dict.pop('modified_time')
                license_dict.pop('sn_num')
                license_dict.pop('id')
                switch_dict['license_status'] = license_dict.pop('status')
                switch_dict.update(license_dict)
            if result.SwitchSystemInfo:
                switch_dict['up_to_date_version'] = result.SwitchSystemInfo.up_to_date_version
            if result.SwitchConfigBackup:
                if result.SwitchConfigBackup.back_up_type == 0:
                    switch_dict['flag'] = 'Unknown'
                elif result.SwitchConfigBackup.back_up_type == 1:
                    switch_dict['flag'] = 'R'
                else:
                    switch_dict['flag'] = 'U'
                switch_dict['backup_time'] = result.SwitchConfigBackup.modified_time.strftime("%Y-%m-%d %H:%M:%S")

            if switch_dict.get('revision', ''):
                switch_dict['version'] = switch_dict.pop('version') + '/' + switch_dict.pop('revision')
            else:
                switch_dict.pop('revision')
                switch_dict['version'] = switch_dict.pop('version')
            if use_selected:
                switch_dict['selected'] = getattr(result, 'selected', False)
            data.append(switch_dict)

        return page_num, page_size, total_count, data


def pathch_path(path):
    if ',' in path:
        path = path.split(',')
        path_split = path[0].split('/')
    elif ';' in path:
        path = path.split(';')
        path_split = path[0].split('/')
    else:
        path_split = [path]
    if path_split[0]:
        path_res = path_split[0]
    else:
        path_res = path_split[1]
    return path_res


def conform_task_type():
    ip, server_id, running = get_db_sync_ip()
    LOG.info('get db sync ip:%s, server_id:%s, running:%s', ip, server_id, running)
    if not ip or not running:
        return 'all'

    random_user = user_db.get_model(User)
    if not http_client.conform_sync_server_ok(random_user.name, ip):
        return 'all'

    if server_id == 1:
        return 'import'

    return 'deploy'


def switch_config_backup(ip, date_dict):
    switch_config_backup = inven_db.get_model(SwitchConfigBackup, filters={'ip': [ip]})
    if switch_config_backup:
        if switch_config_backup.back_up_type == constants.SWITCH_BACK_MANUAL:
            date_dict['flag'] = 'U'
        elif switch_config_backup.back_up_type == constants.SWITCH_BACK_AUTO:
            date_dict['flag'] = 'R'
        date_dict['backup_time'] = switch_config_backup.modified_time
    else:
        date_dict['flag'] = 'unknown'
        date_dict['backup_time'] = '---'
    return date_dict


def get_system_user():
    # should be changed here in later
    user = user_db.get_model(User, filters={'type': ['admin', 'superuser', 'superadmin']})
    return user.name, user.passwd


def get_parking_security_config(sn):
    return inventory.inven_db.get_system_config_by_sn(sn).parking_security_config


def is_need_to_push_security_config_again(sn):
    switch_parked = inven_db.get_model(SwitchParking, filters={'sn': [sn]})
    return True if not switch_parked or (datetime.now() - inven_db.get_model(SwitchParking, filters={'sn': [sn]}).last_register).total_seconds() > 60 else False


def is_need_to_decrypt_file(path):
    security_config_path_list = inventory.inven_db.get_session().query(inventory.SystemConfig.security_config, inventory.SystemConfig.parking_security_config).all()
    all_security_config_path_list = []
    for security_config_path in security_config_path_list:
        for config_path in security_config_path:
            if config_path:
                all_security_config_path_list.append(config_path)
    all_security_config_real_path_list = list(map(lambda x: os.path.realpath(x), all_security_config_path_list))
    if path in all_security_config_real_path_list:
        return True
    else:
        return False


def switch_need_save_config(platform):
    platforms = {'N3048EP-ON', 'N3048ET-ON', 'N3024EP-ON', 'N3024ET-ON', 'N3132PX-ON'}
    if platform in platforms:
        return True
    return False


def get_vpn_status_file_content():
    if not os.path.exists(constants.OPENVPN_CONFIG_FILE):
        LOG.error("Openvpn configuration file: {0} is not existed".format(constants.OPENVPN_CONFIG_FILE))
        return ''
    with open(constants.OPENVPN_CONFIG_FILE) as fc:
        config_content = fc.read()
        if re.search('\nstatus (.*)\n', config_content):
            status_log_path = re.search('\nstatus (.*)\n', config_content).group(1)
        else:
            LOG.error('Can not find Openvpn status file path.')
            return ''

        if not os.path.exists(status_log_path):
            LOG.error("Openvpn status file: {0} is not existed".format(status_log_path))
            return ''
        with open(status_log_path) as fs:
            status_content = fs.read()
            return status_content


def update_vpn_client_status():
    str_client = get_vpn_status_file_content()
    client_list = re.findall('10.[0-9]+\.[0-9]+\.[0-9]+,([0-9a-zA-Z-_\.# ]+),[0-9]+\.+[0-9]+', str_client)
    vip_client_list = re.findall('(10.[0-9]+\.[0-9]+\.[0-9]+),([0-9a-zA-Z-_\.# ]+),[0-9]+\.+[0-9]+', str_client)
    for switch in inven_db.get_collection(Switch, filters={}):
        if switch.reachable_status in [1, 2] and (switch.sn in client_list or switch.remark in client_list):
            LOG.info('switch %s is online ', switch.sn)
            switch.reachable_status = 0
            monitor_db.add_event(switch.sn, 'info', ' The switch is online')
        elif switch.reachable_status in [0, 3] and switch.sn not in client_list and switch.remark not in client_list:
            LOG.info('switch %s is offline ', switch.sn)
            switch.reachable_status = 1
            monitor_db.add_event(switch.sn, 'warn', ' The switch is offline')
    if len(client_list) > 0:
        LOG.info('Active VPN %d clients', len(client_list))
        inven_db.update_model(VpnConfig, filters={}, updates={VpnConfig.vpn_online_status: False})
        for client in client_list:
            inven_db.update_model(VpnConfig, filters={'sn': [client]}, updates={VpnConfig.vpn_online_status: True})
        # we need also update the VPN ip address in database
        for vip_client in vip_client_list:
            inven_db.update_model(Switch, filters={'sn': [vip_client[1]]}, updates={Switch.mgt_ip: vip_client[0]})
            inven_db.update_model(Switch, filters={'remark': [vip_client[1]]}, updates={Switch.mgt_ip: vip_client[0]})


def update_vpn_link_ip_addr():
    str_client = get_vpn_status_file_content()
    # vip_client_list format: [('*********', 'EC1741001625', '************'), ('*********', 'EC1750001004', '************')]
    vip_client_list = re.findall('(10.[0-9]+\.[0-9]+\.[0-9]+),([0-9a-zA-Z-_\.# ]+),([0-9]+\.[0-9]+\.[0-9]+\.[0-9]+)',
                                 str_client)

    for vip_client in vip_client_list:
        inven_db.update_model(Switch, filters={'sn': [vip_client[1]]}, updates={Switch.link_ip_addr: vip_client[2]})
        inven_db.update_model(Switch, filters={'remark': [vip_client[1]]}, updates={Switch.link_ip_addr: vip_client[2]})


def wrap_file_stream(data, filename, as_attachment=True):
    import mimetypes
    from werkzeug.datastructures import Headers
    mimetype = mimetypes.guess_type(filename)[0] or 'application/octet-stream'
    encoded_filename = urllib.parse.quote(filename.encode('utf-8'))
    headers = Headers()
    if as_attachment:
        headers.add('Content-Disposition', 'attachment',
                    filename=encoded_filename)

    headers['Content-Length'] = len(data)

    rv = current_app.response_class(data, mimetype=mimetype, headers=headers,
                                    direct_passthrough=True)
    return rv


def is_zip_file_valid(zip_file):
    max_file_num = 100
    max_file_size = 1024 * 1024 * 100 * 20
    total_size = 0
    if len(zip_file.filelist) >= max_file_num:
        return False
    for file in zip_file.filelist:
        total_size += file.file_size
        if total_size >= max_file_size:
            return False
    return True


def zipFolder(zip_path, zip_file_path):
    zip_file = zipfile.ZipFile(zip_file_path, "w", zipfile.ZIP_DEFLATED)
    for path, _, filenames in os.walk(zip_path):
        fpath = path.replace(zip_path, '')
        for filename in filenames:
            zip_file.write(os.path.join(path, filename), os.path.join(fpath, filename))
    zip_file.close()


def unzipFile(zip_file_path, unzip_path):
    os.makedirs(unzip_path)
    zip_file = zipfile.ZipFile(zip_file_path)
    zip_list = zip_file.namelist()

    if not is_zip_file_valid(zip_file):
        raise IOError('zipfile is invalid!')

    for f in zip_list:
        zip_file.extract(f, unzip_path)

    zip_file.close()


def get_picos_v():
    return inven_db.get_session().query(Switch).filter(Switch.sn == "PICOS-V")


def modify_picos_v_switch_list(model):
    try:
        inven_db.get_session().query(Switch).filter(Switch.sn == "PICOS-V").update({"platform_model": model})
        inven_db.get_session().commit()
    except:
        return


def get_picos_v_support_model_list():
    support_mode = {'AS4630-54NPE', 'AS4630-54PE', 'N2224PX-ON', 'N2224X-ON', 'N2248PX-ON', 'N2248X-ON', 'N3208PX-ON',
                    'N3224F-ON', 'N3224P-ON', 'N3224PX-ON', 'N3224T-ON', 'N3248P-ON', 'N3248PXE-ON', 'N3248TE-ON',
                    'N3248X-ON', 'S4048-ON', 'S4128F-ON', 'S4128T-ON', 'S4148F-ON', 'S4148T-ON', 'S5212F-ON',
                    'S5224F-ON', 'S5232F-ON', 'S5248F-ON', 'S5296F-ON', 'Z9100-ON', 'Z9264F-ON', 'ag5648', 'ag7648',
                    'ag9032', 'as5712_54x', 'as5812_54t', 'as5812_54x', 'as5835_54t', 'as5835_54x', 'as7312_54x',
                    'as7326_56x', 'as7712_32x', 'as7726_32x', 'as7816_64x', 'S5860-20SQ', 'S5860-24XB-U',
                    'S5810-48TS-P',
                    'S5810-28TS', 'S5810-28FS', 'S5810-48TS', 'S5810-48FS', 'N8560-32C', 'S5860-24MG-U',
                    'S5860-48XMG-U', 'S5860-24XMG', 'S5860-48MG-U', 'S5860-48XMG', "S5870-48T6BC", "S5870-48T6BC-U",
                    "N5850-48X6C", "S5870-48T6S", "S5870-48T6S-U"}
    automation_support_model = set(get_support_models())
    return sorted(list(support_mode & automation_support_model), key=lambda x: x.upper())


def is_valid_switch_model_name(model_name):
    return True if model_name in get_support_models() else False


def remove_picos_v_in_switch_list(switch_list):
    for switch in switch_list:
        if switch.sn == PICOS_V_SN:
            switch_list.remove(switch)
            break
    return switch_list


def get_db_backup_config():
    config = []
    db_system_config = inven_db.get_global_system_config()
    if db_system_config:
        config.append(db_system_config.db_config_num)
    else:
        config.append(constants.DEFAULT_MAX_BACKUP_COUNT)
    db_backup_num = inven_db.get_session().query(inventory.DbBackup).count()
    config.append((constants.RANGE_MAX_BACKUP_COUNT[0] if db_backup_num == 0 else db_backup_num,
                   constants.RANGE_MAX_BACKUP_COUNT[1]))
    return tuple(config)


def encrypt_and_copy_config_file(security_config_source_file_path, security_config_target_file_path, security_config_enc_target_file_path, encrypt_secret_pin):
    if os.path.exists(security_config_source_file_path):
        copy_file(security_config_source_file_path, security_config_target_file_path)
        security_encrypt_cmd = ['openssl', 'enc', '-aes-256-cbc', '-salt', '-in', security_config_target_file_path,
                                '-out', security_config_enc_target_file_path, '-pass',
                                'pass:{}'.format(encrypt_secret_pin)]
        subprocess.Popen(security_encrypt_cmd, shell=False, stdout=subprocess.PIPE,
                         stderr=subprocess.PIPE).communicate()
        os.remove(security_config_target_file_path)


def backup_config_file(config_name, path, encrypt_secret_pin):
    if config_name == constants.GLOBAL_CONFIG_TAG:
        security_config_source_file_path = os.path.join(os.path.abspath('config_gen/security'), 'security.config')
        parking_security_config_source_file_path = os.path.join(os.path.abspath('config_gen/security'),
                                                                'parking_security.config')
        security_config_target_file_path = os.path.join(path, 'security.config')
        parking_security_config_target_file_path = os.path.join(path, 'parking_security.config')
        security_config_enc_target_file_path = os.path.join(path, 'security.config.enc')
        parking_security_config_enc_target_file_path = os.path.join(path, 'parking_security.config.enc')
    else:
        security_config_source_file_path = os.path.join(os.path.abspath('config_gen/security'), config_name, 'security.config')
        parking_security_config_source_file_path = os.path.join(os.path.abspath('config_gen/security'), config_name,
                                                                'parking_security.config')
        security_config_target_file_path = os.path.join(path, config_name, 'security.config')
        parking_security_config_target_file_path = os.path.join(path, config_name, 'parking_security.config')
        security_config_enc_target_file_path = os.path.join(path, config_name, 'security.config.enc')
        parking_security_config_enc_target_file_path = os.path.join(path, config_name, 'parking_security.config.enc')

        if not os.path.exists(os.path.join(path, config_name)):
            os.makedirs(os.path.join(path, config_name))

    encrypt_and_copy_config_file(security_config_source_file_path, security_config_target_file_path,
                                 security_config_enc_target_file_path, encrypt_secret_pin)
    encrypt_and_copy_config_file(parking_security_config_source_file_path, parking_security_config_target_file_path,
                                 parking_security_config_enc_target_file_path, encrypt_secret_pin)


def run_db_backup(path, encrypt_secret_pin):
    if os.path.exists(path):
        os.removedirs(path)
    os.makedirs(path)
    username, password = re.search('//(.*)@mysql-service', cfg.CONF.database.connection).group(1).split(':')
    export_cmd = ['mysqldump', '-h', 'mysql-service', '-u{}'.format(username), '-p{}'.format(password), '--databases', 'automation']
    for ignore_table in constants.DB_BACKUP_IGNORE_TABLE:
        export_cmd.append('--ignore-table=automation.{}'.format(ignore_table))
    with open(os.path.join(path, 'automation_backup.sql'), 'w+') as f:
        res = str(subprocess.Popen(export_cmd, shell=False, stdout=f, stderr=subprocess.PIPE).communicate()[1],
                  encoding='utf-8')
    if res == '':
        encrypt_cmd = ['openssl', 'enc', '-aes-256-cbc', '-salt', '-in', os.path.join(path, 'automation_backup.sql'),
                       '-out', os.path.join(path, 'automation_backup.sql.enc'), '-pass',
                       'pass:{}'.format(encrypt_secret_pin)]
        subprocess.Popen(encrypt_cmd, shell=False, stdout=subprocess.PIPE, stderr=subprocess.PIPE).communicate()
        os.remove(os.path.join(path, 'automation_backup.sql'))

    # backup security config and parking security config
    for config in inven_db.get_all_system_config():
        backup_config_file(config.config_name, path, encrypt_secret_pin)
    return res


def decrypt_and_copy_config_file(security_config_source_file_path, security_config_encrypted_file_path, security_config_file_path, encrypt_secret_pin):
    if os.path.exists(security_config_encrypted_file_path):
        security_decrypt_cmd = ['openssl', 'aes-256-cbc', '-d', '-in', security_config_encrypted_file_path, '-out',
                                security_config_file_path, '-k', encrypt_secret_pin]
        _, restore_result = subprocess.Popen(security_decrypt_cmd, shell=False, stdout=subprocess.PIPE,
                                             stderr=subprocess.PIPE).communicate()
        if re.match('.*be better.\nbad decrypt\n', restore_result.decode(), re.S):
            return False
        copy_file(security_config_file_path, security_config_source_file_path)
        os.remove(security_config_file_path)
    return True


def restore_config_file(config_name, path, encrypt_secret_pin):
    if config_name == constants.GLOBAL_CONFIG_TAG:
        security_config_source_file_path = os.path.join(os.path.abspath('config_gen/security'), 'security.config')
        parking_security_config_source_file_path = os.path.join(os.path.abspath('config_gen/security'),
                                                                'parking_security.config')
        security_config_encrypted_file_path = os.path.join(path, 'security.config.enc')
        parking_security_config_encrypted_file_path = os.path.join(path, 'parking_security.config.enc')
        security_config_file_path = os.path.join(path, 'security.config')
        parking_security_config_file_path = os.path.join(path, 'parking_security.config')
    else:
        security_config_source_file_path = os.path.join(os.path.abspath('config_gen/security'), config_name, 'security.config')
        parking_security_config_source_file_path = os.path.join(os.path.abspath('config_gen/security'), config_name,
                                                                'parking_security.config')
        security_config_encrypted_file_path = os.path.join(path, config_name, 'security.config.enc')
        parking_security_config_encrypted_file_path = os.path.join(path, config_name, 'parking_security.config.enc')
        security_config_file_path = os.path.join(path, config_name, 'security.config')
        parking_security_config_file_path = os.path.join(path, config_name, 'parking_security.config')
        if not os.path.exists(os.path.join(os.path.abspath('config_gen/security'), config_name)):
            os.makedirs(os.path.join(os.path.abspath('config_gen/security'), config_name))

    if not decrypt_and_copy_config_file(security_config_source_file_path, security_config_encrypted_file_path, security_config_file_path, encrypt_secret_pin):
        return False
    if not decrypt_and_copy_config_file(parking_security_config_source_file_path, parking_security_config_encrypted_file_path, parking_security_config_file_path, encrypt_secret_pin):
        return False
    return True


def restore_db_backup(path, encrypt_secret_pin):
    if not os.path.exists(path):
        return False
    username, password = re.search('//(.*)@mysql-service', cfg.CONF.database.connection).group(1).split(':')
    decrypt_cmd = ['openssl', 'aes-256-cbc', '-d', '-in', os.path.join(path, 'automation_backup.sql.enc'), '-out',
                   os.path.join(path, 'automation_backup.sql'), '-k', encrypt_secret_pin]
    _, restore_result = subprocess.Popen(decrypt_cmd, shell=False, stdout=subprocess.PIPE,
                                         stderr=subprocess.PIPE).communicate()
    if re.match('.*be better.\nbad decrypt\n', restore_result.decode(), re.S):
        return False
    disable_foreign_cmd = ['mysql', '-h', 'mysql-service', '-u{}'.format(username), '-p{}'.format(password), '-e',
                           '"SET GLOBAL FOREIGN_KEY_CHECKS=0"']
    subprocess.Popen(disable_foreign_cmd, shell=False, stdout=subprocess.PIPE, stderr=subprocess.PIPE).communicate()
    import_cmd = ['mysql', '-h', 'mysql-service', '-u{}'.format(username), '-p{}'.format(password)]
    with open(os.path.join(path, 'automation_backup.sql'), 'r') as f:
        subprocess.Popen(import_cmd, shell=False, stdin=f, stdout=subprocess.PIPE, stderr=subprocess.PIPE).communicate()
    enable_foreign_cmd = ['mysql', '-h', 'mysql-service', '-u{}'.format(username), '-p{}'.format(password), '-e',
                          '“SET GLOBAL FOREIGN_KEY_CHECKS=1”']
    subprocess.Popen(enable_foreign_cmd, shell=False, stdout=subprocess.PIPE, stderr=subprocess.PIPE).communicate()
    os.remove(os.path.join(path, 'automation_backup.sql'))
    # restore security config and parking security config
    for config in inven_db.get_all_system_config():
        if not restore_config_file(config.config_name, path, encrypt_secret_pin):
            return False
    return True


def backup_table(path, table_name):
    path = os.path.abspath(path)
    if not os.path.exists(path):
        os.makedirs(path)
    dst_path = os.path.join(path, table_name + '.sql')
    if os.path.exists(dst_path):
        os.remove(dst_path)
    os.system(
        'mysqldump -uroot -proot  --databases automation --tables {table} --no-create-info --no-create-db > {dst_path}'.format(
            dst_path=dst_path, table=table_name))
    return dst_path


def copy_file(src_path, dst_path):
    src_path = os.path.abspath(src_path)
    dst_path = os.path.abspath(dst_path)
    if not os.path.exists(src_path):
        return False
    if os.path.exists(dst_path):
        os.remove(dst_path)
    if not os.path.exists(os.path.dirname(dst_path)):
        os.makedirs(dst_path)
    shutil.copy(src_path, dst_path)
    return True


def rollback_table(path):
    if not os.path.exists(path):
        return False
    os.system('mysql -u{} -p{} automation -e "SET @@global.foreign_key_checks = 0; truncate automation.{}"'.format(
        DATABASE_USER, DATABASE_PASSWD, path.split('/')[-1].replace('.sql', '')))
    os.system('mysql -u{} -p{} automation < {}'.format(DATABASE_USER, DATABASE_PASSWD, path))
    os.system('mysql -u{} -p{} automation -e "SET @@global.foreign_key_checks = 1"'.format(DATABASE_USER, DATABASE_PASSWD))
    return True


def backup_encrypt_data():
    encrypt_temp_backup_path = 'db_backup/encrypt_temp_backup'
    if not os.path.exists(encrypt_temp_backup_path):
        os.makedirs(encrypt_temp_backup_path)
    general_config_backup_path = backup_table(encrypt_temp_backup_path, 'general_config')
    general_template_backup_path = backup_table(encrypt_temp_backup_path, 'general_template')
    switch_autoconfig_backup_path = backup_table(encrypt_temp_backup_path, 'switch_autoconfig')
    system_config_backup_path = backup_table(encrypt_temp_backup_path, 'system_config')
    ansible_device_backup_path = backup_table(encrypt_temp_backup_path, 'ansible_device')
    encrypt_key_backup_path = backup_table(encrypt_temp_backup_path, 'encrypt_key')
    copy_file('config_gen/security/parking_security.config',
              os.path.join(encrypt_temp_backup_path, 'parking_security.config'))
    copy_file('config_gen/security/security.config', os.path.join(encrypt_temp_backup_path, 'security.config'))
    return (general_config_backup_path, general_template_backup_path, switch_autoconfig_backup_path,
            system_config_backup_path,
            encrypt_key_backup_path), \
           {'config_gen/security/parking_security.config': os.path.join(encrypt_temp_backup_path,
                                                                        'parking_security.config'),
            'config_gen/security/security.config': os.path.join(encrypt_temp_backup_path, 'security.config')}


def rollback_encrypt_data(table_list, file_dict):
    for table in table_list:
        rollback_table(table)
    for src_path, backup_path in file_dict.items():
        copy_file(backup_path, src_path)


def delete_encrypt_temp_backup_data(table_list, file_dict):
    for table in table_list:
        if os.path.exists(table):
            os.remove(table)
    for _, backup_path in file_dict.items():
        if os.path.exists(backup_path):
            os.remove(backup_path)


def update_encrypt_key_and_column(key):
    # fill up key with space
    if len(key) > 32:
        key = key[:32]
    else:
        key = key.ljust(32, ' ')
    session = general.general_db.get_session()

    # table general.GeneralTemplate, SwitchAutoConfig, SystemConfig encrypt_key backup
    backup_table_list, backup_file_dict = backup_encrypt_data()

    # GeneralConfig
    general_config_data_list = []
    general_config_dict_list = []
    for row in session.query(general.GeneralConfig).all():
        general_config_data_list.append(row)
        general_config_dict_list.append(row.make_dict())

    # GeneralTemplate
    general_template_data_list = []
    general_template_dict_list = []
    for row in session.query(general.GeneralTemplate).all():
        general_template_data_list.append(row)
        general_template_dict_list.append(row.make_dict(html_escape=False))

    # SwitchAutoConfig
    switch_auto_config_data_list = []
    switch_auto_config_dict_list = []
    for row in session.query(SwitchAutoConfig).all():
        switch_auto_config_data_list.append(row)
        switch_auto_config_dict_list.append(row.make_dict(html_escape=False))

    # SystemConfig
    system_config = session.query(SystemConfig).all()
    system_config_dict = {}
    system_config_data = None
    if system_config:
        system_config_data = session.query(SystemConfig).all()[0]
        system_config_dict = system_config_data.make_dict(html_escape=False)
        system_config_dict['security_config_content'] = system_config_data.get_security_config_content()
        system_config_dict['parking_security_config_content'] = system_config_data.get_parking_security_config_content()

    # AnsibleDevice
    ansible_device_data_list = []
    ansible_device_dict_list = []
    for row in session.query(AnsibleDevice).all():
        ansible_device_data_list.append(row)
        ansible_device_dict_list.append(row.make_dict(html_escape=False))

    try:
        # update key
        aes_cipher.lock()
        aes_cipher.update_key(key)
        aes_cipher.unlock()

        # GeneralConfig
        for row, row_dict in zip(general_config_data_list, general_config_dict_list):
            row.content_encrypted = aes_cipher.encrypt(row_dict.get('content'))

        # GeneralTemplate
        for row, row_dict in zip(general_template_data_list, general_template_dict_list):
            row.j2_template_encrypted = aes_cipher.encrypt(row_dict.get('j2_template'))
            row.params_encrypted = aes_cipher.encrypt(row_dict.get('params'))

        # SwitchAutoConfig
        for row, row_dict in zip(switch_auto_config_data_list, switch_auto_config_dict_list):
            row.config_encrypted = aes_cipher.encrypt(row_dict.get('config'))

        # AnsibleDevice
        for row, row_dict in zip(ansible_device_data_list, ansible_device_dict_list):
            row.device_pwd_encrypted = aes_cipher.encrypt(row_dict.get('device_pwd'))

        # SystemConfig
        if system_config:
            system_config_data.switch_op_password_encrypted = aes_cipher.encrypt(
                system_config_dict.get('switch_op_password'))
            system_config_data.license_portal_password_encrypted = aes_cipher.encrypt(
                system_config_dict.get('license_portal_password'))
            if system_config_dict.get('security_config') and os.path.exists(system_config_dict.get('security_config')):
                os.remove(system_config_dict.get('security_config'))
                with open(system_config_dict.get('security_config'), 'w+') as f:
                    f.write(aes_cipher.encrypt(system_config_dict['security_config_content']))
                http_client.start_transfer_file('', [{'filename': os.path.basename(
                        system_config_dict.get('security_config')),
                        'path': os.path.realpath(system_config_dict.get('security_config')),
                        'dest': os.path.realpath(system_config_dict.get('security_config'))}])
            if system_config_dict.get('parking_security_config') and os.path.exists(
                    system_config_dict.get('parking_security_config')):
                os.remove(system_config_dict.get('parking_security_config'))
                with open(system_config_dict.get('parking_security_config'), 'w+') as f:
                    f.write(aes_cipher.encrypt(system_config_dict['parking_security_config_content']))
                http_client.start_transfer_file('', [{'filename': os.path.basename(
                        system_config_dict.get('parking_security_config')), 'path': os.path.realpath(
                        system_config_dict.get('parking_security_config')), 'dest': os.path.realpath(
                        system_config_dict.get('parking_security_config'))}])
        session.flush()
        return True
    except:
        rollback_encrypt_data(backup_table_list, backup_file_dict)
        return False
    finally:
        delete_encrypt_temp_backup_data(backup_table_list, backup_file_dict)


def batch_insert(data_list, key=None):
    # [[X_data_list], [Y_data_list]]
    if key is None:
        key = {}
    if not data_list:
        return True
    session = inven_db.get_session()
    key_dict = {}
    # for exception
    data_temp = None
    key_list_temp = None
    try:
        for table_list in data_list:
            key_list = []
            data_temp = table_list
            key_list.extend(table_list)
            # Make sure there is no duplicate data before inserting
            if table_list[0] and key.get(table_list[0].__class__.__name__):
                session.query(table_list[0].__class__).filter(getattr(table_list[0].__class__, key.get(table_list[0].__class__.__name__)).in_(
                    map(lambda x: getattr(x, key.get(table_list[0].__class__.__name__)), table_list))).delete()
            for data in table_list:
                key_list_temp = key_list
                session.add(data)
                session.flush()
            key_dict[table_list[0].__class__.__name__] = key_list
        return True
    except Exception as e:
        if data_temp and key_list_temp:
            key_dict[data_temp[0].__class__.__name__] = key_list_temp
        for table_name, key_list in key_dict.items():
            table_class = key_list[0].__class__
            session.query(table_class).filter(getattr(table_class, key.get(table_class.__name__, 'id')).in_(map(lambda x: getattr(x, key.get(table_class.__name__, 'id')), key_list))).delete()
        return False


def get_encrypt_key():
    return aes_cipher.get_key().strip()


def get_support_models():
    return list(map(lambda x: x[0], inven_db.get_session().query(SwitchSystemInfo.model)))


def is_name_valid(name):
    if NAME_MATCH_REGEX.match(name):
        return True
    else:
        return False


def is_group_user():
    if flask_login.current_user.role == 'tacacs':
        return flask_login.current_user.user_type == 'group'
    else:
        return user_db.query_user(flask_login.current_user.id).user_type == 'group'


def get_user_group():
    if flask_login.current_user.role == 'tacacs':
        if is_group_user():
            group_name_list = flask_login.current_user.group.split(',')
            return inven_db.get_session().query(inventory.Group).filter(inventory.Group.group_name.in_(group_name_list))
        else:
            return inven_db.get_session().query(inventory.Group)
    else:
        if is_group_user():
            user_id = user_db.query_user(flask_login.current_user.id).id
            return inven_db.get_session().query(inventory.Group).join(inventory.UserGroupMapping, inventory.Group.id == inventory.UserGroupMapping.group_id).filter(inventory.UserGroupMapping.user_id == user_id)
        else:
            return inven_db.get_session().query(inventory.Group)


def query_switch(query=None, group=None, system_config_id=None, sn_list=None, fabric_list=None, site_list=None, is_show_active_only=False, is_show_fabric=False, group_list=None):
    session = inven_db.get_session()
    if not query:
        if system_config_id:
            query = session.query(inventory.Switch, case([(inventory.Switch.system_config_id == system_config_id, True)], else_=False).label("selected")).order_by(desc("selected"))
        elif sn_list:
            query = session.query(inventory.Switch, case([(inventory.Switch.sn.in_(sn_list), True)], else_=False).label("selected")).order_by(desc("selected"))
        elif fabric_list:
            switch_id_list = list(map(lambda x: x[0], session.query(inventory.AssociationFabric.switch_id).join(inventory.Fabric,
                                                                               inventory.AssociationFabric.fabric_id == inventory.Fabric.id).filter(
                inventory.Fabric.fabric_name.in_(fabric_list)).all()))
            query = session.query(inventory.Switch, case([(inventory.Switch.id.in_(switch_id_list), True)], else_=False).label("selected")).order_by(desc("selected"))
        elif site_list:
            switch_id_list = list(map(lambda x: x[0], session.query(inventory.AssociationSite.switch_id).join(inventory.Site,
                                                                               inventory.AssociationSite.site_id == inventory.Site.id).filter(
                inventory.Site.site_name.in_(site_list)).all()))
            query = session.query(inventory.Switch, case([(inventory.Switch.id.in_(switch_id_list), True)], else_=False).label("selected")).order_by(desc("selected"))
        elif group_list:
            switch_sn_list = list(map(lambda x: x[0], session.query(inventory.AssociationGroup.switch_sn).join(inventory.Group,
                                                                               inventory.AssociationGroup.group_name==inventory.Group.group_name).filter(
                inventory.Group.group_name.in_(group_list)).all()))
            query = session.query(inventory.Switch, case([(inventory.Switch.sn.in_(switch_sn_list), True)], else_=False).label("selected")).order_by(desc("selected"))
        else:
            query = session.query(inventory.Switch)
    if flask_login.current_user.role == 'tacacs':
        if is_group_user():
            group_name_list = flask_login.current_user.group.split(',')
            if group:
                group_name_list = list(set(group_name_list) & set(group))
            query = query.join(inventory.AssociationGroup, inventory.Switch.sn == inventory.AssociationGroup.switch_sn).filter(inventory.AssociationGroup.group_name.in_(group_name_list))
        else:
            if group:
                group_name_list = [group]
                query = query.join(inventory.AssociationGroup, inventory.Switch.sn == inventory.AssociationGroup.switch_sn).filter(inventory.AssociationGroup.group_name.in_(group_name_list))
            else:
                query = query
    else:
        if is_group_user():
            user_id = user_db.query_user(flask_login.current_user.id).id
            group_id_list = map(lambda x: x.group_id, session.query(inventory.UserGroupMapping).filter(inventory.UserGroupMapping.user_id == user_id))
            group_name_list = map(lambda x: x.group_name, session.query(inventory.Group).filter(inventory.Group.id.in_(group_id_list)))
            if group:
                group_name_list = list(set(group_name_list) & set(group))
            query = query.join(inventory.AssociationGroup, inventory.Switch.sn == inventory.AssociationGroup.switch_sn).filter(inventory.AssociationGroup.group_name.in_(group_name_list))
        else:
            if group:
                group_name_list = [group]
                query = query.join(inventory.AssociationGroup, inventory.Switch.sn == inventory.AssociationGroup.switch_sn).filter(inventory.AssociationGroup.group_name.in_(group_name_list))
            else:
                query = query
    if is_show_active_only:
        query = query.filter(inventory.Switch.status.in_([constants.SwitchStatus.PROVISIONING_SUCCESS,
                                                          constants.SwitchStatus.IMPORTED]),
                             inventory.Switch.sn != PICOS_V_SN,
                             inventory.Switch.sn.in_(session.query(MonitorTarget.sn)))

    if is_show_fabric:
        query = query.outerjoin(inventory.AssociationFabric, inventory.Switch.id == inventory.AssociationFabric.switch_id) \
                    .outerjoin(inventory.Fabric, inventory.AssociationFabric.fabric_id == inventory.Fabric.id) \
                    .add_columns(inventory.Fabric.fabric_name)
        query = query.outerjoin(dc_blueprint.DCFabricTopologyNode, inventory.Switch.sn == dc_blueprint.DCFabricTopologyNode.switch_sn).group_by(inventory.Switch.sn) \
                         .add_columns(dc_blueprint.DCFabricTopologyNode.type)

    return query

def query_host(group_name=None, use_selected=False):
    session = inven_db.get_session()
    query = session.query(AnsibleDevice)

    if flask_login.current_user.role == 'tacacs':
        if is_group_user():
            group_name_list = flask_login.current_user.group.split(',')
            query = query.join(inventory.HostGroupMapping, AnsibleDevice.device_name == inventory.HostGroupMapping.device_name) \
                        .filter(inventory.HostGroupMapping.group_name.in_(group_name_list))
        else:
            user_id = user_db.query_user(flask_login.current_user.id).id
            group_id_list = map(lambda x: x.group_id, session.query(inventory.UserGroupMapping).filter(inventory.UserGroupMapping.user_id == user_id))
            group_name_list = map(lambda x: x.group_name, session.query(inventory.Group).filter(inventory.Group.id.in_(group_id_list)))
            query = query.join(inventory.HostGroupMapping, AnsibleDevice.device_name == inventory.HostGroupMapping.device_name) \
                        .filter(inventory.HostGroupMapping.group_name.in_(group_name_list))
    else:
        if is_group_user():
            user_id = user_db.query_user(flask_login.current_user.id).id
            group_id_list = map(lambda x: x.group_id, session.query(inventory.UserGroupMapping).filter(inventory.UserGroupMapping.user_id == user_id))
            group_name_list = map(lambda x: x.group_name, session.query(inventory.Group).filter(inventory.Group.id.in_(group_id_list)))
            query = query.join(inventory.HostGroupMapping, AnsibleDevice.device_name == inventory.HostGroupMapping.device_name) \
                        .filter(inventory.HostGroupMapping.group_name.in_(group_name_list)) \
                        .group_by(AnsibleDevice.device_name)
        else:
            query = query
    if use_selected and group_name:
        device_name_list = list(map(lambda x: x.device_name, session.query(inventory.HostGroupMapping).filter(inventory.HostGroupMapping.group_name == group_name).all()))
        query = query.add_columns(case([(AnsibleDevice.device_name.in_(device_name_list), True)], else_=False).label("selected")).order_by(desc("selected"))
    return query

def query_switch_auto_config():
    session = inven_db.get_session()
    switch_id_list = map(lambda x: x.id, query_switch().all())
    return session.query(inventory.SwitchAutoConfig)   \
                    .distinct(inventory.SwitchAutoConfig.id) \
                    .outerjoin(inventory.SwitchSwitchAutoConfig, inventory.SwitchAutoConfig.id==inventory.SwitchSwitchAutoConfig.switchAutoConfig_id)  \
                    .filter(or_(and_(inventory.SwitchSwitchAutoConfig.switch_id.in_(switch_id_list), inventory.SwitchAutoConfig.type == 'site'), inventory.SwitchAutoConfig.type != 'site'))


def get_group_id_list_by_name(group_name_list):
    return list(map(lambda x: x[0], inven_db.get_session().query(inventory.Group.id).filter(inventory.Group.group_name.in_(group_name_list)).all()))


def delete_group(group_name):
    session = inven_db.get_session()
    group = session.query(inventory.Group).filter(
        inventory.Group.group_name == group_name).first()
    if not group:
        return False
    session.query(inventory.AssociationGroup).filter(
        inventory.AssociationGroup.group_name == group_name).delete()
    session.query(inventory.UserGroupMapping).filter(
        inventory.UserGroupMapping.group_id == group.id).delete()
    session.query(inventory.Group).filter(
        inventory.Group.group_name == group_name).delete()
    session.flush()
    return True


def get_last_license_expired(start_date, end_date):
    session = inven_db.get_session()
    return session.query(func.date_format(License.license_expired, '%Y-%m').label('month'),
                         func.count(License.sn_num).label('count')) \
        .filter(License.license_expired >= start_date) \
        .filter(License.license_expired < end_date) \
        .filter(License.sn_num.in_(map(lambda x: x.sn, query_switch()))) \
        .group_by('month').all()


def is_active_standby_status():
    host, _, running = get_db_sync_ip()
    if not host:
        return False
    else:
        return True


def get_image_type(image_name):
    for image_type, regex in constants.IMAGE_NAME_REGEX_MAPPING.items():
        match = regex.match(image_name)
        if match:
            return image_type
    return None


def get_image_info(image_name):
    image_type = get_image_type(image_name)
    if not image_type:
        return None
    else:
        match = constants.IMAGE_NAME_REGEX_MAPPING[image_type].match(image_name)
        if match:
            temp = match.groupdict()
            if temp['platform'] == 'x86h':
                temp['platform'] = 'x86'
            return {
                'version': temp['version'],
                'revision': temp['revision'] if 'revision' in temp else '',
                'platform': temp['platform'].lower() if image_type == 'new_black_box_campus_research' or image_type == 'new_black_box_campus_release' or image_type == 'new_s3410_busy_box_release' or image_type == 'black_box_stable_release' or image_type == '4.6.0E_common' else temp['platform'],
            }
        else:
            return None


def is_image_name_comply_with_release_rules(image_name):
    image_type = get_image_type(image_name)
    if not image_type:
        return False
    else:
        match = constants.IMAGE_NAME_REGEX_MAPPING[image_type].match(image_name)
        if match:
            return True
        else:
            return False


def confuse_key_and_password(line):
    key_reg = re.compile('key\s+([^\s]+)')
    password_reg = re.compile('password\s+([^\s]+)')
    return password_reg.sub('***', key_reg.sub('***', line))


def query_helper(model, pre_query=None, skip_sort=False, data=None, disable_filter_and_sorter=False, default_order_by_func=None):
    if not data:
        data = request.get_json()
    if disable_filter_and_sorter:
        filter_fields = []
        sort_fields = []
        search_fields = {}
    else:
        filter_fields = data.get("filterFields", [])
        sort_fields = data.get("sortFields", [])
        search_fields = data.get("searchFields", {})
    page_num = data.get('page', 1)
    page_size = data.get('pageSize', 10)

    start = (page_num - 1) * page_size
    end = start + page_size

    if not pre_query:
        session = inven_db.get_session()
        if isinstance(model, Query):
            query = model
        else:
            if model == Switch:
                query = query_switch()
            elif model == SwitchAutoConfig:
                query = query_switch_auto_config()
            elif model == AnsibleDevice:
                query = query_host()
            else:
                query = session.query(model)
    else:
        query = pre_query

    total_count = query.count()

    # Apply filters
    if filter_fields:
        for field in filter_fields:
            for filter in field["filters"]:
                if filter["matchMode"] == "exact":
                    query = query.filter(getattr(model, field["field"]) == filter["value"])
                elif filter["matchMode"] == "fuzzy":
                    query = query.filter(getattr(model, field["field"]).like(f"%{filter['value']}%"))
        total_count = query.count()

    # Apply sorting
    if not skip_sort:
        if sort_fields:
            for field in sort_fields:
                if field["order"] == "asc":
                    query = query.order_by(asc(getattr(model, field["field"])))
                elif field["order"] == "desc":
                    query = query.order_by(desc(getattr(model, field["field"])))
        else:
            if default_order_by_func is not None:
                order_expr = default_order_by_func(model)
                if isinstance(order_expr, (tuple, list)):
                    query = query.order_by(*order_expr)
                else:
                    query = query.order_by(order_expr)
            else:
                query = query.order_by(asc(getattr(model, "id")))
        total_count = query.count()

    # Apply search
    if search_fields:
        search_value = search_fields["value"]
        search_conditions = [getattr(model, field).like(f"%{search_value}%") for field in search_fields["fields"]]
        query = query.filter(or_(*search_conditions))
        total_count = query.count()

    return page_num, page_size, total_count, query.slice(start, end)


def query_helper_without_page(model, pre_query=None, skip_sort=False, data=None, disable_filter_and_sorter=False, default_order_by_func=None):
    if not data:
        data = request.get_json()
    if disable_filter_and_sorter:
        filter_fields = []
        sort_fields = []
        search_fields = {}
    else:
        filter_fields = data.get("filterFields", [])
        sort_fields = data.get("sortFields", [])
        search_fields = data.get("searchFields", {})

    if not pre_query:
        session = inven_db.get_session()
        if isinstance(model, Query):
            query = model
        else:
            if model == Switch:
                query = query_switch()
            elif model == SwitchAutoConfig:
                query = query_switch_auto_config()
            else:
                query = session.query(model)
    else:
        query = pre_query

    # Apply filters
    if filter_fields:
        for field in filter_fields:
            for filter in field["filters"]:
                if filter["matchMode"] == "exact":
                    query = query.filter(getattr(model, field["field"]) == filter["value"])
                elif filter["matchMode"] == "fuzzy":
                    query = query.filter(getattr(model, field["field"]).like(f"%{filter['value']}%"))

    # Apply sorting
    if not skip_sort:
        if sort_fields:
            for field in sort_fields:
                if field["order"] == "asc":
                    query = query.order_by(asc(getattr(model, field["field"])))
                elif field["order"] == "desc":
                    query = query.order_by(desc(getattr(model, field["field"])))
        else:
            if default_order_by_func is not None:
                order_expr = default_order_by_func(model)
                if isinstance(order_expr, (tuple, list)):
                    query = query.order_by(*order_expr)
                else:
                    query = query.order_by(order_expr)
            else:
                query = query.order_by(asc(getattr(model, "id")))

    # Apply search
    if search_fields:
        search_value = search_fields["value"]
        search_conditions = [getattr(model, field).like(f"%{search_value}%") for field in search_fields["fields"]]
        query = query.filter(or_(*search_conditions))

    return query

def query_lifecycle_switch_data(switch_query_obj):
    SwitchSubQuery = orm.aliased((switch_query_obj.subquery()))
    session = inven_db.get_session()
    return session.query(SwitchSubQuery.c.sn, SwitchSubQuery.c.id, SwitchSubQuery.c.host_name,
                         SwitchSubQuery.c.create_time, SwitchSubQuery.c.version, SwitchSubQuery.c.revision,
                         SwitchSubQuery.c.status, SwitchSubQuery.c.mgt_ip, SwitchSubQuery.c.platform_model,
                         SwitchSubQuery.c.address,
                         SwitchSubQuery.c.selected, SwitchSubQuery.c.reachable_status, License,
                         SwitchSystemInfo, SwitchConfigBackup) \
        .outerjoin(License, SwitchSubQuery.c.sn == License.sn_num) \
        .outerjoin(SwitchSystemInfo, SwitchSubQuery.c.platform_model == SwitchSystemInfo.model) \
        .outerjoin(SwitchConfigBackup,
                   and_(SwitchSubQuery.c.sn == SwitchConfigBackup.sn, SwitchSubQuery.c.mgt_ip == SwitchConfigBackup.ip))


def parse_param_bool_to_str(input_dict):
    for key, value in input_dict.items():
        if isinstance(value, bool):
            input_dict[key] = 'true' if value else 'false'
        elif isinstance(value, dict):
            parse_param_bool_to_str(value)
    return input_dict


def save_switch_to_fabric(fabric_name, new_switches, del_switches):
    db_session = inven_db.get_session()

    fabric = db_session.query(inventory.Fabric).filter(inventory.Fabric.fabric_name == fabric_name).first()
    if not fabric:
        return False, 'fabric %s not found' % str(fabric_name)

    dc_fabric_topo = db_session.query(dc_blueprint.DCFabricTopology).filter(
        dc_blueprint.DCFabricTopology.fabric_id == fabric.id).first()
    if dc_fabric_topo:
        # 新增交换机是否被其他topo使用
        other_used_sn = list(set(map(lambda x: x[0], db_session.query(dc_blueprint.DCFabricTopologyNode.switch_sn) \
                                     .filter(dc_blueprint.DCFabricTopologyNode.fabric_topo_id != dc_fabric_topo.id) \
                                     .filter(dc_blueprint.DCFabricTopologyNode.switch_sn.in_(new_switches)).all())))
        if other_used_sn:
            return False, 'switch: %s is used by other fabric' % str(','.join(other_used_sn))
        # 删除交换机是否被本topo使用
        self_used_sn = list(set(map(lambda x: x[0], db_session.query(dc_blueprint.DCFabricTopologyNode.switch_sn) \
                                    .filter(dc_blueprint.DCFabricTopologyNode.fabric_topo_id == dc_fabric_topo.id) \
                                    .filter(dc_blueprint.DCFabricTopologyNode.switch_sn.in_(del_switches)).all())))
        if self_used_sn:
            return False, 'switch: %s is used by this fabric' % str(','.join(self_used_sn))
    else:
        # 新增交换机是否被其他topo使用
        other_used_sn = list(set(map(lambda x: x[0], db_session.query(dc_blueprint.DCFabricTopologyNode.switch_sn) \
                                     .filter(dc_blueprint.DCFabricTopologyNode.switch_sn.in_(new_switches)).all())))
        if other_used_sn:
            return False, 'switch: %s is used by other fabric' % str(','.join(other_used_sn))

    try:
        with db_session.begin():
            default_fabric = db_session.query(inventory.Fabric).filter(
                inventory.Fabric.fabric_name == "default").first()
            if not default_fabric:
                # 如果不存在default fabric，可以选择报错或新建，这里先报错返回
                return False, 'default fabric not found'

            switch_sn_id_dict = {switch.sn: switch.id for switch in
                                 db_session.query(inventory.Switch.sn, inventory.Switch.id).filter(
                                     inventory.Switch.sn.in_(del_switches + new_switches)).all()}
            monitor_sn_id_dict = {monitor_target.sn: monitor_target.id for monitor_target in
                                  db_session.query(inventory.MonitorTarget).filter(
                                      inventory.MonitorTarget.sn.in_(del_switches + new_switches)).all()}
            new_switch_list = db_session.query(inventory.Switch).filter(inventory.Switch.sn.in_(new_switches)).all()
            del_switch_list = db_session.query(inventory.Switch).filter(inventory.Switch.sn.in_(del_switches)).all()
            db_session.query(inventory.AssociationFabric).filter(
                inventory.AssociationFabric.switch_id.in_(switch_sn_id_dict.values())).delete()
            all_fabric_topology_id = list(map(lambda x: x[0], db_session.query(inventory.Topology.id).filter(
                inventory.Topology.topology_type == 'fabric').all()))
            for index, switch in enumerate(new_switch_list):
                association_fabric = inventory.AssociationFabric()
                association_fabric.fabric_id = fabric.id
                association_fabric.switch_id = switch_sn_id_dict[switch.sn]
                db_session.add(association_fabric)
                topology_id = fabric.topology_id
                # delete old nodes and edges if the switch is already in the fabric
                if switch.sn in monitor_sn_id_dict.keys():
                    db_session.query(inventory.TopologyNode).filter(inventory.TopologyNode.topology_id != topology_id,
                                                                    inventory.TopologyNode.monitor_target_id ==
                                                                    monitor_sn_id_dict[switch.sn],
                                                                    inventory.TopologyNode.topology_id.in_(
                                                                        all_fabric_topology_id)).delete()
                    # save new nodes and edges
                    if not db_session.query(inventory.TopologyNode).filter(
                            inventory.TopologyNode.topology_id == topology_id,
                            inventory.TopologyNode.monitor_target_id == monitor_sn_id_dict[switch.sn]).first():
                        new_node = inventory.TopologyNode(node_label=switch.host_name if switch.host_name else 'PICOS',
                                                          topology_id=topology_id,
                                                          monitor_target_id=monitor_sn_id_dict[switch.sn], layer=0,
                                                          position_x=50 * (index + 1),
                                                          position_y=int(50 * (random.uniform(1, 10))))
                        db_session.add(new_node)
            for index, switch in enumerate(del_switch_list):
                if switch.sn in monitor_sn_id_dict.keys():
                    association_node_query = db_session.query(inventory.TopologyNode).filter(
                        inventory.TopologyNode.topology_id == fabric.topology_id,
                        inventory.TopologyNode.monitor_target_id == monitor_sn_id_dict[switch.sn])
                    node_id_list = list(map(lambda x: x.id, association_node_query.all()))
                    if node_id_list:
                        db_session.query(inventory.TopologyEdge).filter(
                            inventory.TopologyEdge.topology_id == fabric.topology_id,
                            inventory.TopologyEdge.source_id.in_(node_id_list)).delete()
                        db_session.query(inventory.TopologyEdge).filter(
                            inventory.TopologyEdge.topology_id == fabric.topology_id,
                            inventory.TopologyEdge.target_id.in_(node_id_list)).delete()
                        association_node_query.delete()

                association_fabric = inventory.AssociationFabric()
                association_fabric.fabric_id = default_fabric.id  # 之前提前查询好的default fabric.id
                association_fabric.switch_id = switch_sn_id_dict[switch.sn]
                db_session.add(association_fabric)

            fabric.modified_time = func.now()
            db_session.commit()

        return True, "Fabric edit successfully"
    except Exception as e:
        LOG.error(traceback.format_exc())
        db_session.rollback()
        return False, "Fabric edit fail"


def save_switch_to_site(site_name, new_switches, del_switches):
    db_session = inven_db.get_session()

    site = db_session.query(inventory.Site).filter(inventory.Site.site_name == site_name).first()
    if not site:
        return False, 'site %s not found' % str(site_name)

    try:
        with db_session.begin():
            switch_sn_id_dict = {switch.sn: switch.id for switch in db_session.query(inventory.Switch.sn, inventory.Switch.id).filter(inventory.Switch.sn.in_(del_switches + new_switches)).all()}
            monitor_sn_id_dict = {monitor_target.sn: monitor_target.id for monitor_target in db_session.query(inventory.MonitorTarget).filter(inventory.MonitorTarget.sn.in_(del_switches + new_switches)).all()}
            new_switch_list = db_session.query(inventory.Switch).filter(inventory.Switch.sn.in_(new_switches)).all()
            del_switch_list = db_session.query(inventory.Switch).filter(inventory.Switch.sn.in_(del_switches)).all()
            db_session.query(inventory.AssociationSite).filter(
                inventory.AssociationSite.switch_id.in_(switch_sn_id_dict.values())).delete()
            all_site_topology_id = list(map(lambda x: x[0], db_session.query(inventory.Topology.id).filter(
                inventory.Topology.topology_type == 'site').all()))
            for index, switch in enumerate(new_switch_list):
                association_site = inventory.AssociationSite()
                association_site.site_id = site.id
                association_site.switch_id = switch_sn_id_dict[switch.sn]
                db_session.add(association_site)
                topology_id = site.topology_id
                # delete old nodes and edges if the switch is already in the site
                if switch.sn in monitor_sn_id_dict.keys():
                    db_session.query(inventory.TopologyNode).filter(inventory.TopologyNode.topology_id != topology_id,
                                                                    inventory.TopologyNode.monitor_target_id ==
                                                                    monitor_sn_id_dict[switch.sn],
                                                                    inventory.TopologyNode.topology_id.in_(
                                                                        all_site_topology_id)).delete()
                    # save new topology node and edge
                    if not db_session.query(inventory.TopologyNode).filter(
                            inventory.TopologyNode.topology_id == topology_id,
                            inventory.TopologyNode.monitor_target_id == monitor_sn_id_dict[switch.sn]).first():
                        new_node = inventory.TopologyNode(node_label=switch.host_name if switch.host_name else 'PICOS', topology_id=topology_id,
                                                          monitor_target_id=monitor_sn_id_dict[switch.sn], layer=0,
                                                          position_x=50 * (index + 1),
                                                          position_y=int(50 * (random.uniform(1, 10))))
                        db_session.add(new_node)
            for index, switch in enumerate(del_switch_list):
                if switch.sn in monitor_sn_id_dict.keys():
                    association_node_query = db_session.query(inventory.TopologyNode).filter(inventory.TopologyNode.topology_id == site.topology_id, inventory.TopologyNode.monitor_target_id == monitor_sn_id_dict[switch.sn])
                    node_id_list = list(map(lambda x: x.id, association_node_query.all()))
                    if node_id_list:
                        db_session.query(inventory.TopologyEdge).filter(inventory.TopologyEdge.topology_id == site.topology_id, inventory.TopologyEdge.source_id.in_(node_id_list)).delete()
                        db_session.query(inventory.TopologyEdge).filter(inventory.TopologyEdge.topology_id == site.topology_id, inventory.TopologyEdge.target_id.in_(node_id_list)).delete()
                        association_node_query.delete()
        return True, "Site edit successfully"
    except Exception as e:
        LOG.error(traceback.format_exc())
        db_session.rollback()
        return False, "Site edit fail"


def list_fabric_info(data=None):
    from server.db.models.resource_pool import ResourcePoolVlanDomain
    db_session = inven_db.get_session()
    if not data:
        data = request.get_json()
    filter_fields = data.get("filterFields", [])
    sort_fields = data.get("sortFields", [])
    search_fields = data.get("searchFields", {})
    page_num = data.get('page', 1)
    page_size = data.get('pageSize', 10)

    start = (page_num - 1) * page_size
    end = start + page_size

    query = db_session.query(
        inventory.Fabric,
        func.count(func.distinct(inventory.AssociationFabric.switch_id)).label('switch_count'),
        dc_blueprint.DCFabricTopology,
        func.count(func.distinct(dc_virtual_resource.DCVirtualResourcePoolAZ.id)).label('az_count'),
        func.count(func.distinct(ResourcePoolVlanDomain.id)).label('vd_count'),
        (
            func.coalesce(
                (
                    db_session.query(func.count(dc_blueprint.DCLogicalRouter.id))
                    .filter(dc_blueprint.DCLogicalRouter.fabric_id == inventory.Fabric.id)
                    .correlate(inventory.Fabric)
                    .as_scalar()
                ) +
                (
                    db_session.query(func.count(dc_blueprint.DCLogicalSwitch.id))
                    .filter(dc_blueprint.DCLogicalSwitch.fabric_id == inventory.Fabric.id)
                    .correlate(inventory.Fabric)
                    .as_scalar()
                ) +
                (
                    db_session.query(func.count(dc_blueprint.DCVirtualNetwork.id))
                    .filter(dc_blueprint.DCVirtualNetwork.fabric_id == inventory.Fabric.id)
                    .correlate(inventory.Fabric)
                    .as_scalar()
                ), 0
            ).label("logical_count")
        )
    ).outerjoin(
        inventory.AssociationFabric, inventory.Fabric.id == inventory.AssociationFabric.fabric_id
    ).outerjoin(
        dc_blueprint.DCFabricTopology, inventory.Fabric.id == dc_blueprint.DCFabricTopology.fabric_id
    ).outerjoin(
        dc_virtual_resource.DCVirtualResourcePoolAZ,
        inventory.Fabric.id == dc_virtual_resource.DCVirtualResourcePoolAZ.fabric_id
    ).outerjoin(
        ResourcePoolVlanDomain,
        inventory.Fabric.id == ResourcePoolVlanDomain.fabric_id
    ).group_by(
        inventory.Fabric.id
    )

    total_count = query.count()
    # Apply filters
    if filter_fields:
        for field in filter_fields:
            for filter in field["filters"]:
                column = None
                if hasattr(dc_blueprint.DCFabricTopology, field["field"]):
                    column = getattr(dc_blueprint.DCFabricTopology, field["field"])
                elif hasattr(inventory.Fabric, field["field"]):
                    column = getattr(inventory.Fabric, field["field"])

                if column:
                    if filter["matchMode"] == "exact":
                        query = query.filter(column == filter["value"])
                    elif filter["matchMode"] == "fuzzy":
                        query = query.filter(column.like(f"%{filter['value']}%"))
        total_count = query.count()

    # Apply sorting
    if sort_fields:
        for field in sort_fields:
            column = None
            if field["field"] == "switch_count" or field["field"] == "az_count":
                column = field["field"]
            elif hasattr(dc_blueprint.DCFabricTopology, field["field"]):
                column = getattr(dc_blueprint.DCFabricTopology, field["field"])
            elif hasattr(inventory.Fabric, field["field"]):
                column = getattr(inventory.Fabric, field["field"])

            if column:
                if field["order"] == "asc":
                    query = query.order_by(asc(column))
                elif field["order"] == "desc":
                    query = query.order_by(desc(column))
        total_count = query.count()
    else:
        query = query.order_by(asc(getattr(inventory.Fabric, "id")))


    # Apply search
    if search_fields:
        search_value = search_fields["value"]
        search_conditions = [getattr(inventory.Fabric, field).like(f"%{search_value}%") for field in search_fields["fields"] if hasattr(inventory.Fabric, field)]
        search_conditions.extend([getattr(dc_blueprint.DCFabricTopology, field).like(f"%{search_value}%") for field in search_fields["fields"] if hasattr(dc_blueprint.DCFabricTopology, field)])
        query = query.filter(or_(*search_conditions))
        total_count = query.count()

    return page_num, page_size, total_count, query.slice(start, end)


def update_host_info():
    session = inven_db.get_session()
    device_query = session.query(AnsibleDevice).all()

    from server.util import prometheus_util
    from server.db.models.automation import HostInfo

    for device in device_query:
        instance = device.ip + ":9100"
        host_info = prometheus_util.query_node_metric(instance, metric="node_host_info")
        if host_info:
            try:
                with session.begin(subtransactions=True):
                    existing_info = session.query(HostInfo).filter(HostInfo.device_id == device.id).first()
                    if existing_info:
                        existing_info.hostname = host_info["hostname"]
                        existing_info.os_version = host_info["os_version"]
                        existing_info.cpu = host_info["cpu"]
                        existing_info.memory = host_info["memory"]
                        existing_info.storage = host_info["storage"]
                        existing_info.last_seen = datetime.now()
                    else:
                        info = HostInfo(device_id=device.id, hostname=host_info["hostname"],
                                        os_version=host_info["os_version"],
                                        cpu=host_info["cpu"], memory=host_info["memory"],
                                        storage=host_info["storage"], last_seen=datetime.now())
                        session.add(info)
            except Exception as e:
                LOG.error(traceback.format_exc())
                continue


def get_pro_type(file_path="/usr/share/automation/server/.env"):
    pro_type = None
    try:
        with open(file_path, 'r') as file:
            for line in file:
                match = re.match(r'^\s*PRO_TYPE\s*=\s*(.*)\s*$', line)
                if match:
                    pro_type = match.group(1)
                    break
    except Exception as e:
        LOG.info(f": {e}")
    return pro_type


ampcon_pro_type = get_pro_type()


def get_assigned_ip(start_ip, offset, count, is_include_start_ip=False):
    ip_list = []
    ip = ipaddress.ip_address(start_ip)
    start_index = 0 if is_include_start_ip else 1
    for i in range(start_index, count + start_index):
        ip_list.append(str(ip + offset + i))
    return ip_list


def get_assigned_ip_reverse(subnet, offset, count, is_include_start_ip=False):
    ip_list = []
    network = ipaddress.ip_address(subnet.split('/')[0])
    prefix = int(subnet.split('/')[1])
    start_index = 0 if is_include_start_ip else 1
    for i in range(start_index, count + start_index):
        ip_list.append(str(network + pow(2, 32-prefix) - offset - 1 - i))
    return ip_list


def calculate_downstream_port_info(model, exclude_ports=()):
    model_port_mapping = inven_db.get_model_port_mapping(model)
    if model_port_mapping.get('ge_end', None):
        return {
            'downstream_port_upper_limit': model_port_mapping['ge_end'],
            'downstream_ports': [f'ge-1/1/{i}' for i in range(model_port_mapping['ge_start'], model_port_mapping['ge_end'] + 1) if f'ge-1/1/{i}' not in exclude_ports]
        }
    elif model_port_mapping.get('te_end', None):
        return {
            'downstream_port_upper_limit': model_port_mapping['te_end'],
            'downstream_ports': [f'te-1/1/{i}' for i in range(model_port_mapping['te_start'], model_port_mapping['te_end'] + 1) if f'te-1/1/{i}' not in exclude_ports]
        }
    elif model_port_mapping.get('qe_end', None):
        return {
            'downstream_port_upper_limit': model_port_mapping['qe_end'],
            'downstream_ports': [f'qe-1/1/{i}' for i in range(model_port_mapping['qe_start'], model_port_mapping['qe_end'] + 1) if f'qe-1/1/{i}' not in exclude_ports]
        }
    elif model_port_mapping.get('xe_end', None):
        return {
            'downstream_port_upper_limit': model_port_mapping['xe_end'],
            'downstream_ports': [f'xe-1/1/{i}' for i in range(model_port_mapping['xe_start'], model_port_mapping['xe_end'] + 1) if f'xe-1/1/{i}' not in exclude_ports]
        }
    else:
        return None


def get_campus_fabric_param_with_node_info(node, param):
    to_be_saved_core_param = copy.deepcopy(param)
    to_be_saved_core_param['node_info'] = {
        'router_id': node.get('router_id'),
        'other_ip_config': node.get('other_ip_config', []),
        'links': node.get('links', {}),
        'label': node.get('label'),
        'mac_addr': node.get('mac_addr'),
        'mgt_ip': node.get('mgt_ip'),
        'model': node.get('model'),
        'switch_sn': node.get('switch_sn'),
        'status': node.get('status'),
        'id': node.get('id')
    }
    return to_be_saved_core_param


def get_search_models(custom_models=None, without_black_box=False):
    session = inven_db.get_session()
    automation_ini_path = constants.AUTOMATION_CONFIG_FILE
    config = configparser.ConfigParser()
    config.read(automation_ini_path, encoding="utf-8")
    support_model = config.get('DEFAULT', 'supports_models').split(',')

    # filter out the models that are not supported by pro type
    if ampcon_pro_type == "ampcon-dc":
        support_model = list(filter(lambda x: x in constants.DC_SUPPORT_SWITCH_MODELS, support_model))
    elif ampcon_pro_type == "ampcon-campus":
        support_model = list(filter(lambda x: x in constants.CAMPUS_SUPPORT_SWITCH_MODELS, support_model))

    if custom_models:
        platform_models = session.query(inventory.Switch.platform_model).group_by(
            inventory.Switch.platform_model).all()
        models = [p_models.platform_model for p_models in platform_models]
    else:
        models = get_support_models()
    if without_black_box:
        models = [model for model in models if model not in constants.BLACK_BOX_MODEL_LIST]
    model_mapping_obj_list = session.query(inventory.SwitchSystemInfo.platform, inventory.SwitchSystemInfo.model).filter(
        inventory.SwitchSystemInfo.model.in_(models), inventory.SwitchSystemInfo.model.in_(support_model)).all()
    tmp_dict = dict()
    for platform, model in model_mapping_obj_list:
        if platform not in tmp_dict:
            tmp_dict[platform] = [model]
        else:
            tmp_dict[platform].append(model)
    return tmp_dict


def calculate_core_vrrp_ip(inband_subnet):
    """Calculate core VRRP IP from inband subnet by getting the last usable IP

    Args:
        inband_subnet: Subnet in CIDR format (e.g. '********/24')

    Returns:
        str: Last usable IP in the subnet (e.g. '**********')
    """
    network = ipaddress.ip_network(inband_subnet, strict=False)
    # Get last usable IP (broadcast address - 1)
    return str(network.broadcast_address - 1)


alarm_type_mappings = {
    "lag_in_errors_count": "packet_loss_alert",
    "lag_out_errors_count": "packet_loss_alert",
    "lag_in_discards_count": "packet_loss_alert",
    "lag_out_discards_count": "packet_loss_alert",
    "in-fcs-errors": "packet_loss_alert",
    "cpu_usage": "resource_usage_alert",
    "memory_usage": "resource_usage_alert",
    "in_bindwidth_usage": "resource_usage_alert",
    "out_bindwidth_usage": "resource_usage_alert",
    "fan_pwm": "resource_usage_alert",
    "rear_fan_pwm": "resource_usage_alert",
    "rpsu": "resource_usage_alert",
    "ecn_marked_packets": "ai_monitoring_alert",
    "send_pfc_pause_frames": "ai_monitoring_alert",
    "receive_pfc_pause_frames": "ai_monitoring_alert",
    "pfc_deadlock_monitor_count": "ai_monitoring_alert",
    "pfc_deadlock_recovery_count": "ai_monitoring_alert",
    "admin-status": "interface_monitoring_alert",
    "oper-status": "interface_monitoring_alert",
    "mtu": "interface_monitoring_alert",
    "loopback-mode": "interface_monitoring_alert",
    "duplex-mode": "interface_monitoring_alert",
    "port-speed": "interface_monitoring_alert",
    "laser-temperature": "optical_module_alert",
    "output-power": "optical_module_alert",
    "input-power": "optical_module_alert",
    "form-factor": "optical_module_alert",
    "output-power -input-power": "optical_module_alert",
}


def email_handler(alert, db_session):
    try:
        switch_sn = alert["labels"]["target"]
        switch = db_session.query(Switch).filter(Switch.sn == switch_sn).first()
        if not switch:
            return
        switch_sn = switch.sn
        switch_model = switch.platform_model
        pro_type = get_pro_type()
        alert_scope = 'default'
        if pro_type == "ampcon-dc":
            fabric = db_session.query(Fabric).join(AssociationFabric, Fabric.id == AssociationFabric.fabric_id).join(Switch, AssociationFabric.switch_id == Switch.id).filter(Switch.sn == switch_sn).first()
            if fabric:
                alert_scope = fabric.fabric_name
        elif pro_type == "ampcon-campus":
            site = db_session.query(Site).join(AssociationSite, Site.id == AssociationSite.site_id).join(Switch, AssociationSite.switch_id == Switch.id).filter(Switch.sn == switch_sn).first()
            if site:
                alert_scope = site.site_name
        alert_time = datetime.strptime(alert["startsAt"].replace(' ', ''), '%Y-%m-%dT%H:%M:%S.%fZ').strftime('%Y-%m-%d %H:%M:%S') if alert.get("startsAt", "") else datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        alert_name = alert["labels"]["alertname"]
        alert_type = alarm_type_mappings.get(alert_name)
        alert_title = alert["annotations"]["summary"]
        alert_msg = alert["annotations"]["description"]
        alert_level = alert["labels"]["severity"]
        alert_target = alert["labels"]["target"]
        if not alert_type:
            alert_type = "packet_loss_alert"
        template_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), "monitor", "email_template")
        env = Environment(loader=FileSystemLoader(template_dir))
        template = env.get_template("alert_email.j2")
        alert_type_label_mappings = {
            "packet_loss_alert": "Packet Loss Alert",
            "resource_usage_alert": "Resource Usage Alert",
            "ai_monitoring_alert": "AI Monitoring Alert",
            "interface_monitoring_alert": "Interface Monitoring Alert",
            "optical_module_alert": "Optical Module Alert"
        }
        msg = template.render(msg=alert_msg, alert_title=alert_title, alert_level=alert_level.capitalize(), alert_type=alert_type_label_mappings.get(alert_type, alert_type), switch_sn=switch_sn, switch_model=switch_model, alert_scope=alert_scope, alert_time=alert_time)
        template_log_for_log = env.get_template("alert_email_for_log.j2")
        msg_for_log = template_log_for_log.render(msg=alert_msg, alert_title=alert_title, alert_level=alert_level.capitalize(), alert_type=alert_type_label_mappings.get(alert_type, alert_type), switch_sn=switch_sn, switch_model=switch_model, alert_scope=alert_scope, alert_time=alert_time)
        sub = f'【Alert Notification】 {alert_title.capitalize()} -- {alert_level.capitalize()} : {alert_msg}'
        send_alert_email(sn=alert_target, sub=sub, msg=msg, msg_for_log=msg_for_log, alert_level=alert_level, alert_type=alert_type, alert_msg=alert_msg, switch_scope=alert_scope, session=db_session)
    except Exception as e:
        LOG.error(traceback.format_exc())
        pass


if __name__ == "__main__":
    update_encrypt_key_and_column('pica8')
