import logging
import requests
import traceback
import copy
from datetime import datetime, timedelta
from server import cfg
from sqlalchemy import func, and_
from pygnmi.client import gNMIclient
from concurrent.futures import ThreadPoolExecutor, as_completed

from server.util.terminal_identification_util import get_organization_name_by_mac
from server.db.models.monitor import monitor_db, ModulesLink
from server.db.models.inventory import SystemConfig, License, Switch, MonitorTarget, Topology, TopologyEdge, TopologyNode, ClientDeviceInfo
from server.db.models.inventory import inven_db, PushConfigTask, PushConfigTaskDetails
from server.util import utils
from server import constants

LOG = logging.getLogger(__name__)

def format_query_time(start_time=None, end_time=None):
    delta_time = "5m"
    timestamp = None 
    step = "15s"

    now_timestamp = datetime.now().timestamp()

    if end_time:
        if end_time > now_timestamp:
            end_time = now_timestamp
        timestamp = end_time
    else:
        end_time = now_timestamp
    
    if start_time:
        if start_time >= end_time:
            start_time = end_time - 300 # 5分钟 = 300秒
        else:
            time_delta = datetime.fromtimestamp(end_time) - datetime.fromtimestamp(start_time)
            step = f"{(time_delta.days +1) * 15}s"
            minutes_diff = int(time_delta.total_seconds() // 60)
            delta_time = f"{minutes_diff}m"
    else:
        start_time = end_time - 300 # 5分钟 = 300秒
        
    return timestamp, delta_time, start_time, end_time, step


def generate_time_points_dict(start_timestamp, end_timestamp, step):
    time_points_dict = {}
    interval = timedelta(seconds=int(step[:-1]))

    current_time = datetime.fromtimestamp(start_timestamp)
    end = datetime.fromtimestamp(end_timestamp)

    while current_time <= end:
        formatted_time = current_time.strftime('%Y-%m-%d %H:%M:%S')
        time_points_dict[formatted_time] = [formatted_time, None]
        current_time += interval

    return time_points_dict


def query_prometheus(query, time=None):
    try:
        response = requests.get(f'http://{cfg.CONF.prometheus_url}/api/v1/query', params={'query': query, 'time': time})
        data = response.json()
        if data['status'] != 'success':
            LOG.error(query)
            LOG.error(data)
            return []
        else:
            # print(data)
            return data['data']['result']
    except Exception as e:
        LOG.error(f"query_prometheus error: {traceback.format_exc()}")
        return []


def query_range_prometheus(query, start_time, end_time, step="15s"):
    try:
        response = requests.get(f'http://{cfg.CONF.prometheus_url}/api/v1/query_range', params={'query': query, 'start': start_time, 'end': end_time, 'step': step})
        data = response.json()
        if data['status'] != 'success':
            LOG.error(query)
            LOG.error(data)
            return []
        else:
            # print(data)
            return data['data']['result']
    except Exception as e:
        LOG.error(f"query_range_prometheus error: {traceback.format_exc()}")
        return []


def query_metric_filter_by_interface(metricName, targetName, interface_list, time=None):
    interfaces = "|".join(interface_list)
    query_template  = '{{__name__="{name}", target="{target_value}", interface_name=~"{interfaces}"}}'
    modified_query = query_template.format(name=metricName, target_value=targetName, interfaces=interfaces)
    # print(modified_query)
    result = query_prometheus(modified_query, time=time)
    # print(result)
    metric_data = {}
    for data in result:
        metric = data["metric"]
        if data["metric"].get("interface_name", None):
            name = data["metric"]["interface_name"]
            metric.pop("interface_name")
        else:
            continue

        if name not in metric_data:
            metric_data[name] = []

        metric.pop("__name__")
        metric.pop("instance")
        metric.pop("job")
        metric.pop("target")
        metric_data[name] = metric
        # print(name, metric) 
    return metric_data


def query_metric_value_filter_by_interface(metricName, targetName, interface_list, time=None):
    interfaces = "|".join(interface_list)
    query_template  = '{{__name__="{name}", target="{target_value}", interface_name=~"{interfaces}"}}'
    modified_query = query_template.format(name=metricName, target_value=targetName, interfaces=interfaces)
    # print(modified_query)
    result = query_prometheus(modified_query, time=time)
    # print(result)
    metric_data = {}
    for data in result:
        metric = data["metric"]
        if data["metric"].get("interface_name", None):
            name = data["metric"]["interface_name"]
            metric.pop("interface_name")
        else:
            continue

        if name not in metric_data:
            metric_data[name] = []

        metric.pop("__name__")
        metric.pop("instance")
        metric.pop("job")
        metric.pop("target")
        metric_data[name] = metric
        # print(name, metric) 
    return metric_data

def get_port_speed_usage(metricName, targetName, interface_list, keyName, time=None):

    interfaces = "|".join(interface_list)
    step1 = '( {{__name__="{name}", target="{targetName}", interface_name=~"{interfaces}"}}' 
    step2 = ' / on(interface_name, target)'
    step3 = '{{__name__="openconfig_interfaces:interfaces_interface_openconfig_if_ethernet:ethernet_state_port_speed", target="{targetName}", interface_name=~"{interfaces}"}}'
    step4 = ' * 100 )'

    query_template = step1 + step2 + step3 + step4
    modified_query = query_template.format(name=metricName, targetName=targetName, interfaces=interfaces)
    # print(modified_query)
    result = query_prometheus(modified_query, time=time)
    # print(result)
    metric_data = {}
    for data in result:
        metric = data["metric"]
        if data["metric"].get("interface_name", None):
            name = data["metric"]["interface_name"]
            metric.pop("interface_name")
        else:
            continue
        
        if data["value"][1] == "+Inf" or data["value"][1] == "NaN":
            rawValue = 0
            value = 0
        else:
            rawValue = data["value"][1]
            value = float(data["value"][1])
        if name not in metric_data:
            metric_data[name] = []

        # metric.pop("__name__")
        # metric.pop("instance")
        # metric.pop("job")
        metric.pop("target")
        if value == 0:
            metric[keyName] = "0.00%"
        elif 0 < value < 0.01:
            metric[keyName] = "0.01%"
        else:
            metric[keyName] = str(round(value, 2)) + "%"
        metric['raw_' + keyName] = rawValue
        metric_data[name] = metric
        # print(name, metric) 
    return metric_data


def query_metric(metricName, targetName, time=None):
    query_template  = '{{__name__="{name}", target="{target_value}"}}'
    modified_query = query_template.format(name=metricName, target_value=targetName)
    # print(modified_query)
    result = query_prometheus(modified_query, time=time)
    # print(result)
    metric_data = []
    for data in result:
        metric = data["metric"]
        metric.pop("__name__")
        metric.pop("instance")
        metric.pop("job")
        # metric.pop("target")
        metric_data.append(metric)
        metric["metric_value"] = float(data["value"][1])
        metric["metric_time"] = datetime.fromtimestamp(data["value"][0]).strftime('%Y-%m-%d %H:%M:%S')
        # print(name, metric) 
    return metric_data


def query_counters_with_prefix(metricPrefix ,targetName, interface_list, time=None):
    # 构造查询表达式
    interfaces = "|".join(interface_list)
    query_template  = '{{__name__=~"{prefix}.*", target="{target_value}", interface_name=~"{interfaces}"}}'
    query_template = query_template.format(prefix=metricPrefix, target_value=targetName, interfaces=interfaces)

    # print(query_template)
    result = query_prometheus(query_template, time)
    # print(result)
    counters_data = {}
    for data in result:
        name = data["metric"]["__name__"][len(metricPrefix):]
        interface_name = data["metric"]["interface_name"]
        if interface_name not in counters_data:
            counters_data[interface_name] = {}
        counters_data[interface_name][name] = int(float(data["value"][1]))

    return counters_data


def query_ai_with_prefix(metricPrefix ,targetName, interface_list, time=None):
    interfaces = "|".join(interface_list)
    query_template  = '{{__name__=~"{prefix}.*", target="{target_value}", interface_name=~"{interfaces}"}}'
    query_template = query_template.format(prefix=metricPrefix, target_value=targetName, interfaces=interfaces)
    result = query_prometheus(query_template, time)

    counters_data = {}
    for data in result:
        name = data["metric"]["__name__"][len(metricPrefix):]
        interface_name = data["metric"]["interface_name"]
        queue_name = data["metric"].get("queue_name", None)

        if interface_name not in counters_data:
            counters_data[interface_name] = {}

        if queue_name is None:
            counters_data[interface_name][name] = int(float(data["value"][1]))
        else:
            if queue_name not in counters_data[interface_name]:
                counters_data[interface_name][queue_name] = {}

            counters_data[interface_name][queue_name][name] = int(float(data["value"][1]))

    return counters_data


def query_counters(metricName, targetName, interface_list, time=None, multiChannel=False):
    interfaces = "|".join(interface_list)
    query_template  = '{{__name__="{name}", target="{target_value}", interface_name=~"{interfaces}"}}'
    query_template = query_template.format(name=metricName, target_value=targetName, interfaces=interfaces)

    # print(query_template)
    result = query_prometheus(query_template, time)
    # print(result)
    counters_data = {}

    for data in result:
        if data["metric"].get("interface_name", None):
            name = data["metric"]["interface_name"]
        else:
            continue
        value = float(data["value"][1])
        if not multiChannel:
            counters_data[name] = value
        else:
            channel = data["metric"].get("channel_index", None)
            # 多频道时，累加字符串，格式如 "12.34(C1), 56.78(C2)"
            prev = counters_data.get(name, "")
            entry = f"{value:.2f}(C{channel})"
            if prev:
                counters_data[name] = prev + ", " + entry
            else:
                counters_data[name] = entry

    return counters_data


def get_target_interfaces(target, time=None):
    query_template = '{{__name__="openconfig_interfaces:interfaces_interface", target="{target_value}"}}'
    result = query_prometheus(query_template.format(target_value=target), time=time)

    interface_list = [data["metric"]["interface_name"] for data in result if not data["metric"]["interface_name"].startswith(("eth", "vlan", "rif-"))]
    # print(interface_list)
    return interface_list


def query_counter_delta_topk(metric_name, metric_prefix="openconfig_interfaces:interfaces_interface_state_counters_", topk=5, target=None, start_time=None, end_time=None, filter=None):
    
    timestamp, delta_time, start_time, end_time, step = format_query_time(start_time, end_time)
    time_points_dict = generate_time_points_dict(start_time, end_time, step)

    if target:
        query_template = 'topK(' + str(topk) + ', max_over_time(' + metric_prefix + metric_name + '{target="' + target + '"}['+ delta_time +']))'
    elif filter:
        filter_list = []
        for target, value in filter.items():
            interfaces = "|".join(value)
            filter_template = f"max_over_time({metric_prefix}{metric_name}{{target=\"{target}\", interface_name=~\"{interfaces}\"}}[{delta_time}])"
            filter_list.append(filter_template)
            
        filter_query = " or ".join(filter_list)
        query_template = f'topk({str(topk)}, ({filter_query}))'
    else:
        query_template = 'topK(' + str(topk) + ', max_over_time(' + metric_prefix + metric_name + '['+ delta_time +']))'
    print(query_template)   
    result = query_prometheus(query_template, timestamp)

    query_templates = []
    for metric in result:
        if 'queue_name' in metric['metric']:
            query_template = f"{metric_prefix}{metric_name}{{target=\"{metric['metric']['target']}\", interface_name=\"{metric['metric']['interface_name']}\", queue_name=\"{metric['metric']['queue_name']}\"}}"
        else:
            query_template = f"{metric_prefix}{metric_name}{{target=\"{metric['metric']['target']}\", interface_name=\"{metric['metric']['interface_name']}\"}}"
        query_templates.append(query_template)

    final_query = " or ".join(query_templates)
    result = query_range_prometheus(final_query, start_time, end_time, step)

    res = []
    for metric in result:
        time_points_value = copy.deepcopy(time_points_dict)
        for timestamp, value in metric["values"]:
            date_time = datetime.fromtimestamp(timestamp)
            date_time_str = date_time.strftime("%Y-%m-%d %H:%M:%S")
            time_points_value[date_time_str] = [date_time_str, value]

        info ={
            'target': metric['metric']['target'],
            'interface_name': metric['metric']['interface_name'],
            "values": list(time_points_value.values())
        }

        if 'queue_name' in metric['metric']:
            info['queue_name'] = metric['metric']['queue_name']

        res.append(info)

    return res

def query_modules_topk(metric_name, topk=5, target=None, start_time=None, end_time=None, mode="topK"):
    
    timestamp, delta_time, start_time, end_time, step = format_query_time(start_time, end_time)
    time_points_dict = generate_time_points_dict(start_time, end_time, step)

    # 对于模块数据由于不是单调递增的 所以根据sum_over_time获取delta time内时间序列的总和然后排序
    if target:
        query_template = mode + '(' + str(topk) + ', sum_over_time(' + metric_name + '{target="' + target + '"}['+ delta_time +']))'
    else:
        query_template = mode + '(' + str(topk) + ', sum_over_time(' + metric_name + '['+ delta_time +']))'
    result = query_prometheus(query_template, timestamp)

    query_templates = []
    for metric in result:
        query_template = metric_name  + '{target="' + metric['metric']['target'] + '"' + ', interface_name="' + metric["metric"]["interface_name"] + '"'
        if metric['metric'].get("channel_index", None) is not None:
            query_template += ', channel_index="' + metric["metric"]["channel_index"] + '"}'
        else:
            query_template += '}'
        query_templates.append(query_template)

    final_query = " or ".join(query_templates)
    result = query_range_prometheus(final_query, start_time, end_time, step)

    res = []
    for metric in result:
        time_points_value = copy.deepcopy(time_points_dict)
        for timestamp, value in metric["values"]:
            date_time = datetime.fromtimestamp(timestamp)
            date_time_str = date_time.strftime("%Y-%m-%d %H:%M:%S")
            time_points_value[date_time_str] = [date_time_str, value]

        info ={
            'target': metric['metric']['target'],
            'interface_name': metric['metric']['interface_name'],
            "values": list(time_points_value.values())
        }
        if metric['metric'].get("channel_index", None) is not None:
            info["channel_index"] = metric["metric"]["channel_index"]
        res.append(info)
    
    return res

def query_cpu_usage_topk(topk=5, target=None, start_time=None, end_time=None):
    
    timestamp, delta_time, start_time, end_time, step = format_query_time(start_time, end_time)
    time_points_dict = generate_time_points_dict(start_time, end_time, step)
    
    # 获取每个核的total avg并计算平均值
    if target:
        query_template = 'topk(' + str(topk) + ', avg by (target) (avg_over_time(openconfig_system:system_cpus_cpu_state_total_avg{target="' + target + '"}[' + delta_time + '])))'
    else:
        query_template = 'topk(' + str(topk) + ', avg by (target) (avg_over_time(openconfig_system:system_cpus_cpu_state_total_avg['+ delta_time +'])))'

    result = query_prometheus(query_template, timestamp)

    query_templates = []
    for metric in result:
        query_template = 'avg by (target) (openconfig_system:system_cpus_cpu_state_total_avg{target="' + metric['metric']['target'] + '"})'
        query_templates.append(query_template)

    final_query = " or ".join(query_templates)
    result = query_range_prometheus(final_query, start_time, end_time, step)

    res = []
    for metric in result:
        time_points_value = copy.deepcopy(time_points_dict)
        for timestamp, value in metric["values"]:
            date_time = datetime.fromtimestamp(timestamp)
            date_time_str = date_time.strftime("%Y-%m-%d %H:%M:%S")
            time_points_value[date_time_str] = [date_time_str, round(float(value), 2)]

        info = {
            'target': metric['metric']['target'],
            "interface_name": "cpu",
            "values": list(time_points_value.values())
        }
        res.append(info)

    return res


def query_memory_usage_topk(topk=5, target=None, start_time=None, end_time=None):
    
    timestamp, delta_time, start_time, end_time, step = format_query_time(start_time, end_time)
    time_points_dict = generate_time_points_dict(start_time, end_time, step)
    
    # 获取每个核的total avg并计算平均值
    if target:
        query_template = 'topk(' + str(topk) + ', 100 * avg by (target) (avg_over_time(openconfig_system:system_memory_state_used{target="' + target + '"}[' + delta_time +']) / avg_over_time(openconfig_system:system_memory_state_physical{target="' + target + '"}[' + delta_time +'])))'
    else:
        query_template = 'topk(' + str(topk) + ', 100 * avg by (target) (avg_over_time(openconfig_system:system_memory_state_used[' + delta_time +']) / avg_over_time(openconfig_system:system_memory_state_physical[' + delta_time +'])))'

    result = query_prometheus(query_template, timestamp)

    query_templates = []
    for metric in result:
        query_template = '100 * avg by (target) (openconfig_system:system_memory_state_used{target="' + metric['metric']['target'] + '"} / openconfig_system:system_memory_state_physical{target="' + metric['metric']['target'] + '"})'
        query_templates.append(query_template)

    final_query = " or ".join(query_templates)
    result = query_range_prometheus(final_query, start_time, end_time, step)

    res = []
    for metric in result:
        time_points_value = copy.deepcopy(time_points_dict)
        for timestamp, value in metric["values"]:
            date_time = datetime.fromtimestamp(timestamp)
            date_time_str = date_time.strftime("%Y-%m-%d %H:%M:%S")
            time_points_value[date_time_str] = [date_time_str, round(float(value), 2)]

        info = {
            'target': metric['metric']['target'],
            "interface_name": "memory",
            "values": list(time_points_value.values())
        }
        res.append(info)

    return res

def query_rate_topk(metric_name, topk=5, target=None, filter=[], start_time=None, end_time=None, mode="topK"):
    
    timestamp, delta_time, start_time, end_time, step = format_query_time(start_time, end_time)
    time_points_dict = generate_time_points_dict(start_time, end_time, step)

    # 对于模块数据由于不是单调递增的 所以根据sum_over_time获取delta time内时间序列的总和然后排序
    if target:
        if filter:
            interface_name = "|".join(filter)
            query_template = f'{mode}({str(topk)}, sum_over_time({metric_name}{{target="{target}", interface_name=~"{interface_name}"}}[{delta_time}]))'
        else:
            query_template = f'{mode}({str(topk)}, sum_over_time({metric_name}{{target="{target}"}}[{delta_time}]))'
    else:
        query_template = f'{mode}({str(topk)}, sum_over_time({metric_name}[{delta_time}]))'
    # print(query_template)   
    result = query_prometheus(query_template, timestamp)

    query_templates = []
    for metric in result:
        query_template = metric_name  + '{target="' + metric['metric']['target'] + '"' + ', interface_name="' + metric["metric"]["interface_name"] + '"}'
        query_templates.append(query_template)

    final_query = " or ".join(query_templates)
    result = query_range_prometheus(final_query, start_time, end_time, step)

    res = []
    for metric in result:
        time_points_value = copy.deepcopy(time_points_dict)
        for timestamp, value in metric["values"]:
            date_time = datetime.fromtimestamp(timestamp)
            date_time_str = date_time.strftime("%Y-%m-%d %H:%M:%S")
            time_points_value[date_time_str] = [date_time_str, value]

        info ={
            'target': metric['metric']['target'],
            'interface_name': metric['metric']['interface_name'],
            "values": list(time_points_value.values())
        }
        res.append(info)
    
    return res
    

def query_attenuation_topk(topk=5, target=None, start_time=None, end_time=None):
    
    timestamp, delta_time, start_time, end_time, step = format_query_time(start_time, end_time)
    time_points_dict = generate_time_points_dict(start_time, end_time, step)

    if target:
        query_template = 'topk(' + str(topk) + ', sum_over_time(openconfig_platform:components_component_openconfig_platform_transceiver:transceiver_state_output_power{target="' + target + '"}['+ delta_time +']) -' +\
                            'sum_over_time(openconfig_platform:components_component_openconfig_platform_transceiver:transceiver_state_input_power{target="' + target + '"}['+ delta_time +']))'
    else:
        query_template = 'topk(' + str(topk) + ', sum_over_time(openconfig_platform:components_component_openconfig_platform_transceiver:transceiver_state_output_power['+ delta_time +']) -' +\
                            'sum_over_time(openconfig_platform:components_component_openconfig_platform_transceiver:transceiver_state_input_power['+ delta_time +']))'

    result = query_prometheus(query_template, timestamp)

    metriclabel = []
    for metric in result:
        info = {
            'name': "attenuation",
            'target': metric['metric']['target'],
            'interface_name': metric["metric"]["interface_name"],
        }
        metriclabel.append(info)

    query_templates = []
    for metric in result:
        query_template = "openconfig_platform:components_component_openconfig_platform_transceiver:transceiver_state_output_power - openconfig_platform:components_component_openconfig_platform_transceiver:transceiver_state_input_power" \
                        + '{target="' + metric['metric']['target'] + '"' + ', interface_name="' + metric["metric"]["interface_name"] + '"}'
        query_templates.append(query_template)

    final_query = " or ".join(query_templates)
    result = query_range_prometheus(final_query, start_time, end_time, step)


    res = []
    for metric in result:
        time_points_value = copy.deepcopy(time_points_dict)
        for timestamp, value in metric["values"]:
            date_time = datetime.fromtimestamp(timestamp)
            date_time_str = date_time.strftime("%Y-%m-%d %H:%M:%S")
            time_points_value[date_time_str] = [date_time_str, round(float(value), 2)]

        info ={
            'target': metric['metric']['target'],
            'interface_name': metric['metric']['interface_name'],
            "values": list(time_points_value.values())
        }
        res.append(info)

    return res


def query_lldp_neighbor_state(target_list, time=None):
    query_template = '{__name__="openconfig_lldp:lldp_interfaces_interface_neighbors_neighbor_state", target=~"' + '|'.join(target_list) + '"}'
    result = query_prometheus(query_template, time)
    lldp_neighbor_state = {}
    for res in result:
        state = {
            "source": res["metric"]["target"],
            "target_mac": res["metric"]["chassis_id"],
            "source_port": res["metric"]["interface_name"],
            "target_port": res["metric"]["port_id"],
        }
        source = state['source']
        source_port = state['source_port']
        key = f"{source}_{source_port}"
        # 暂定如果有重复的只取一个，有重复的说明当前时刻有变化
        if key not in lldp_neighbor_state:
            lldp_neighbor_state[key] = state

    return lldp_neighbor_state
 
 
def query_lldp_state(target, mac="", time=None):
    query_template = '{__name__="openconfig_lldp:lldp_state", target="' + target + '"}'
    result = query_prometheus(query_template, time)
    if result and result[0]["metric"].get("chassis_id"):
        state = {
            "mac_addr" : result[0]["metric"]["chassis_id"],
            "system_name" : result[0]["metric"]["system_name"],
            "system_description" : result[0]["metric"]["system_description"],
            "monitor_status": "online"
        }
    else:
        state = {
            "monitor_status": "offline",
            "mac_addr" : mac
        }
    return state
 
    
def query_node_metric(instance, metric, search_fields = {}):
    if not search_fields.get("value"):
        query_template  = '{{__name__="{name}", instance="{instance}"}}'
        modified_query = query_template.format(name=metric, instance=instance)
    else:
        query_template = '{{__name__="{name}", instance="{instance}", {field}=~"(?i).*{value}.*"}}'
    
        fields = search_fields.get("fields", [])
        value = search_fields.get("value", "")
        search_conditions = []
        for field in fields:
            search_conditions.append(query_template.format(name=metric, instance=instance, field=field, value=value))
        modified_query = " or ".join(search_conditions)

    result = query_prometheus(modified_query)
    
    metric_data = {}
    for data in result:
        if not data["metric"].get("device"):
            return data["metric"]
        else:
            device = data["metric"]["device"]
                
            if device not in metric_data:
                metric_data[device] = []
            
            metric = data["metric"]
            metric.pop("__name__")
            metric.pop("instance")
            metric.pop("job")
            metric.pop("device")
            metric_data[device] = metric 
    return metric_data


def query_node_range_metric(metric, start_time=None, end_time=None):
    query_template  = '{{__name__="{name}"}}'
    
    _, _, start_time, end_time, step = format_query_time(start_time, end_time)
    modified_query = query_template.format(name=metric)
    result = query_range_prometheus(modified_query, start_time, end_time, step)
    metric_data = {}
    for data in result:
        
        instance = data["metric"]["instance"]
            
        if instance not in metric_data:
            metric_data[instance] = []
        
        metric = data["metric"]
        metric.pop("__name__")
        metric.pop("instance")
        metric.pop("job")
        metric_data[instance].append(metric) 
    return metric_data


def query_node_topk(metric_name, topk=5, filter=None, start_time=None, end_time=None):
    
    timestamp, delta_time, start_time, end_time, step = format_query_time(start_time, end_time)
    time_points_dict = generate_time_points_dict(start_time, end_time, step)

    if filter:
        filter_list = []
        for key, value in filter.items():
            instance = key + ":9100"
            devices = "|".join(value)
            filter_template = f"max_over_time({metric_name}{{instance=\"{instance}\", device=~\"{devices}\"}}[{delta_time}])"
            filter_list.append(filter_template)
            
        filter_query = " or ".join(filter_list)
        query_template = f'topk({str(topk)}, ({filter_query}))'
    else:
        query_template = f'topk({str(topk)}, max_over_time({metric_name}[{delta_time}]) *on(device, instance) max by (instance, device) (max_over_time(node_nic_info[{delta_time}])))'

    result = query_prometheus(query_template, timestamp)
        
    query_templates = []
    for metric in result:
        query_template = f"{metric_name}{{instance=\"{metric['metric']['instance']}\", device=\"{metric['metric']['device']}\"}}"
        query_templates.append(query_template)

    final_query = " or ".join(query_templates)
    result = query_range_prometheus(final_query, start_time, end_time, step)
    
    res = []
    for metric in result:
        time_points_value = copy.deepcopy(time_points_dict)
        for timestamp, value in metric["values"]:
            date_time = datetime.fromtimestamp(timestamp)
            date_time_str = date_time.strftime("%Y-%m-%d %H:%M:%S")
            time_points_value[date_time_str] = [date_time_str, value]

        info ={
            'instance': metric['metric']['instance'].split(":")[0],
            'device': metric['metric']['device'],
            "values": list(time_points_value.values())
        }

        res.append(info)

    return res


def query_dlb_rate_topk(dividend_metric_name, divisor_metric_name, topk=5, target=None, filter=[], start_time=None, end_time=None, mode="topK"):
    
    timestamp, delta_time, start_time, end_time, step = format_query_time(start_time, end_time)
    time_points_dict = generate_time_points_dict(start_time, end_time, step)
    
    if target:
        if filter:
            interface_name = "|".join(filter)
            dividend_metric = f'{{__name__="{dividend_metric_name}", target="{target}", interface_name=~"{interface_name}"}}'
            divisor_metric = f'{{__name__="{divisor_metric_name}", target="{target}", interface_name=~"{interface_name}"}}'
        else:
            dividend_metric = f'{{__name__="{dividend_metric_name}", target="{target}"}}'
            divisor_metric = f'{{__name__="{divisor_metric_name}", target="{target}"}}'
        
        unless_metric_template = f' unless({{__name__="{divisor_metric_name}", target="{target}"}} == 0)' # 排除除数为0的指标进行排序
    else:
        dividend_metric = f'{{__name__="{dividend_metric_name}"}}'
        divisor_metric = f'{{__name__="{divisor_metric_name}"}}'
        unless_metric_template = f' unless({{__name__="{divisor_metric_name}"}} == 0)'
    metric_template = f'({dividend_metric} / on(interface_name, target) group_left {divisor_metric} * 100)'
    query_template = f'{mode}({str(topk)}, max_over_time({metric_template}[{delta_time}:{step}]){unless_metric_template})'
    
    LOG.info(query_template)   
    result = query_prometheus(query_template, timestamp)
    
    # 如果个数不够再考虑获取除数为0的指标, 排除上一次的查询结果
    if len(result) < topk:
        interface_name_dict = {}
        for metric in result:
            metric_target = metric['metric']['target']
            metric_interface_name = metric['metric']['interface_name']
            
            if metric_target not in interface_name_dict:
                interface_name_dict[metric_target] = []
            
            interface_name_dict[metric_target].append(metric_interface_name)
            
        unless_metric_template = ""
        for metric_target, interface_name_list in interface_name_dict.items():
            interface_name = "|".join(interface_name_list)
            unless_metric_template += f'unless({{__name__="{divisor_metric_name}", target="{metric_target}", interface_name=~"{interface_name}"}}) '
        
        query_template = f'{mode}({str(topk - len(result))}, max_over_time({metric_template}[{delta_time}:{step}]){unless_metric_template})'
        LOG.info(query_template)   
        result2 = query_prometheus(query_template, timestamp)
        result = result+ result2
       
    query_templates = []
    for metric in result:
        # print(metric['metric']['target'], metric['metric']['interface_name'])
        dividend_metric = f'{{__name__="{dividend_metric_name}", target="{metric["metric"]["target"]}", interface_name="{metric["metric"]["interface_name"]}"}}'
        divisor_metric = f'({{__name__="{divisor_metric_name}", target="{metric["metric"]["target"]}", interface_name="{metric["metric"]["interface_name"]}"}})'
        metric_template = f'({dividend_metric} / on(interface_name, target) {divisor_metric} * 100)'
        query_templates.append(metric_template)
        
    final_query = " or ".join(query_templates)
    # print(final_query)
    result = query_range_prometheus(final_query, start_time, end_time, step)
    # print(result)
    
    res = []
    for metric in result:
        time_points_value = copy.deepcopy(time_points_dict)
        for timestamp, value in metric["values"]:
            date_time = datetime.fromtimestamp(timestamp)
            date_time_str = date_time.strftime("%Y-%m-%d %H:%M:%S")
            if value == "+Inf" or value == "NaN":
                value = 0
            time_points_value[date_time_str] = [date_time_str, round(float(value), 8)]

        info ={
            'target': metric['metric']['target'],
            'interface_name': metric['metric']['interface_name'],
            "values": list(time_points_value.values())
        }
        res.append(info)

    return res 


def query_fan_data(target, start_time=None, end_time=None):
    
    timestamp, delta_time, start_time, end_time, step = format_query_time(start_time, end_time)
    time_points_dict = generate_time_points_dict(start_time, end_time, step)
    
    fan_state = query_metric("openconfig_platform:components_component_fan_state_fsconfig_platform_fan_extensions:fan", target, timestamp)
    fan_position = {fan['fan_index']: fan['position'] for fan in fan_state}
    rear_fan_state = query_metric("openconfig_platform:components_component_fan_state_fsconfig_platform_fan_extensions:rear_fan", target, timestamp)
    rear_fan_position = {fan['rear_fan_index']: fan['position'] for fan in rear_fan_state}

    query_template = f'{{__name__=~"openconfig_platform:components_component_fan_state_fsconfig_platform_fan_extensions:(fan_pwm|rear_fan_pwm)", target="{target}"}}'

    result = query_range_prometheus(query_template, start_time, end_time, step)
    res = []
    for metric in result:
        time_points_value = copy.deepcopy(time_points_dict)
        for timestamp, value in metric["values"]:
            date_time = datetime.fromtimestamp(timestamp)
            date_time_str = date_time.strftime("%Y-%m-%d %H:%M:%S")
            time_points_value[date_time_str] = [date_time_str, str(round(float(value) * 100, 4))]

        info ={
            'target': metric['metric']['target'],
            "values": list(time_points_value.values())
        }

        if 'fan_index' in metric['metric']:
            info['fan_index'] = metric['metric']['fan_index']
            info['interface_name'] = fan_position.get(metric['metric']['fan_index'], None)
        else:
            info['rear_fan_index'] = metric['metric']['rear_fan_index']
            info['interface_name'] = rear_fan_position.get(metric['metric']['rear_fan_index'], None)

        res.append(info)

    return res


def query_interface_nic(targetName, time=None):
    query_template  = 'label_replace(openconfig_network_instance:network_instances_network_instance_fdb_mac_table_entries_entry_interface_interface_ref_state{{target="{target_value}"}}, "address","$1","entry_mac_address", "(.*)")' + \
                      '* on(address) group_left(operstate, instance, device) (node_network_info unless (node_network_info{{device="lo"}}))' + \
                      '* on(device, instance) group_left(name) node_nic_info' + \
                      '* on(interface, target) group_left(oper_status, mtu) ' + \
                      'label_replace(openconfig_interfaces:interfaces_interface_state{{target="{target_value}"}}, "interface","$1","interface_name", "(.*)")'
    modified_query = query_template.format(target_value=targetName)
    print(modified_query)
    result = query_prometheus(modified_query, time=time)
    # print(result)
    res = []
    for data in result:
        metric_value = float(data["value"][1])
        if not metric_value:
            continue
        mac_info = data["metric"]
        mac_info.pop("job")
        mac_info["instance"] = mac_info["instance"].split(":")[0]
        res.append(mac_info)
    return res


def get_lldp_neighbor(target_ip, sn, username, passwd, port="9339"):
    res = {}
    try:
        tmp = []
        with gNMIclient(target=(target_ip, port), username=username, password=passwd, timeout=15) as gc:
            result = gc.get(path=['openconfig-lldp:lldp'])
            for notification in result["notification"]:
                for update in notification["update"]:
                    lldp_interface = update['val']['openconfig-lldp:interfaces']['interface']
                    for interface in lldp_interface:
                        if interface.get("neighbors", {}):
                            for neighbor in interface["neighbors"]["neighbor"]:
                                info = {
                                    "source_mac": update['val']['openconfig-lldp:state']['chassis-id'],
                                    "target_mac": neighbor["state"]['chassis-id'],
                                    "source_port": interface["name"],
                                    "target_port": neighbor["state"]['port-id']
                                }
                                tmp.append(info)
        res[sn] = tmp
    except Exception as e:
        LOG.error(traceback.format_exc())
    return res


def lldp_refresh(sn_list):  
    """
    return 
        {
            'source_sn_value': {
                'target_sn_value': [{
                        'source_mac': '',
                        'target_mac': '',
                        'source_port': '',
                        'target_port': ''
                    }, 
                ],
            },
        }
    """
    new_neighbor = {}
    try:
        session = monitor_db.get_session()
    
        query_node = session.query(MonitorTarget).filter(MonitorTarget.sn.in_(sn_list)).filter(MonitorTarget.device_type == 1).all()
        mac_sn_dict = {monitor_target.switch.mac_addr: monitor_target.sn for monitor_target in query_node }
        
        lldp_neighbor = query_lldp_neighbor_state(sn_list)

        for neighbor in lldp_neighbor.values():
            source_sn = neighbor.pop("source")  
            target_mac = neighbor["target_mac"].lower()
            if target_mac in mac_sn_dict:
                target_sn = mac_sn_dict[target_mac]
                
                if source_sn not in new_neighbor:
                    new_neighbor[source_sn] = {}  
                if target_sn not in new_neighbor[source_sn]:
                    new_neighbor[source_sn][target_sn] = []  
                
                new_neighbor[source_sn][target_sn].append(neighbor)
                    
        return new_neighbor    
    except Exception as e:
        LOG.error(traceback.format_exc())
    return new_neighbor


def query_module_link():
    query_template = 'openconfig_lldp:lldp_interfaces_interface_neighbors_neighbor_state * on(interface_name, target) group_left() openconfig_platform:components_component_openconfig_platform_transceiver:transceiver_state'
    result = query_prometheus(query_template)
    lldp_neighbor_state = {}
    for res in result:
        state = {
            "source": res["metric"]["target"],
            "target_mac": res["metric"]["chassis_id"],
            "source_port": res["metric"]["interface_name"],
            "target_port": res["metric"]["port_id"],
        }
        source = state['source']
        source_port = state['source_port']
        key = f"{source}_{source_port}"
        # 暂定如果有重复的只取一个，有重复的说明当前时刻有变化
        if key not in lldp_neighbor_state:
            lldp_neighbor_state[key] = state
    return lldp_neighbor_state


def update_module_link():
    print("update_module_link")
    try:
        session = monitor_db.get_session()

        query_node = session.query(MonitorTarget).filter(MonitorTarget.device_type == 1).all()
        mac_sn_dict = {monitor_target.switch.mac_addr: monitor_target.sn for monitor_target in query_node }

        lldp_neighbor = query_module_link()
        link_list=[]
        for neighbor in lldp_neighbor.values():
            source_sn = neighbor.pop("source")
            target_mac = neighbor["target_mac"].lower()
            if target_mac in mac_sn_dict:
                target_sn = mac_sn_dict[target_mac]
                link_list.append((source_sn, target_sn, neighbor["source_port"], neighbor["target_port"], neighbor["target_mac"]))

        print(link_list)
        session.query(ModulesLink).update({"link_status": False})
        for link in link_list:
            source_sn, target_sn, source_port, target_port, target_mac = link
            with session.begin(subtransactions=True):
                modules_link =  session.query(ModulesLink).filter(ModulesLink.source_sn == source_sn, ModulesLink.target_sn == target_sn,
                                                                ModulesLink.source_port == source_port, ModulesLink.target_port == target_port).first()

                if modules_link:
                    modules_link.link_status = True
                    modules_link.target_mac = target_mac
                else:
                    modules_link = ModulesLink(source_sn=source_sn, target_sn=target_sn, source_port=source_port, target_port=target_port,
                                            target_mac=target_mac, link_status=True, light_attenuation_threshold=5)
                    session.add(modules_link)
    except Exception as e:
        LOG.error(traceback.format_exc())


def query_nvidia_nic_port():
    query_template  = 'node_infiniband_physical_state_id * on(instance) group_left(nodename) node_uname_info'
    result = query_prometheus(query_template)
    res = {}
    for item in result:
        metric = item['metric']
        instance = metric['instance'].split(":")[0]

        if instance not in res:
            res[instance] = {
                "instance": instance,
                "nodename": metric['nodename'],
                "children": []
            }
        res[instance]["children"].append({
            "port_title": metric['device'] + " port " + metric['port'],
            "port_value": metric['device'],
        })
    return res

def query_broadcom_nic_port():
    query_template  = 'node_ethtool_info{driver="bnxt_en"} * on(instance) group_left(nodename) node_uname_info'
    result = query_prometheus(query_template)
    # print(result)
    res = {}
    for item in result:
        metric = item['metric']
        instance = metric['instance'].split(":")[0]

        if instance not in res:
            res[instance] = {
                "instance": instance,
                "nodename": metric['nodename'],
                "children": []
            }
        res[instance]["children"].append({
            "port_title": metric['device'],
            "port_value": metric['device']
        })
    return res


def query_rock_nic_port(start_time=None, end_time=None, type=""):
    _, _, start_time, end_time, step = format_query_time(start_time, end_time)
    
    if type == "nvidia":
        query_template  = 'node_ethtool_info{driver="mlx5_core"} * on(instance) group_left(nodename) node_uname_info'
    elif type == "broadcom":
        query_template  = 'node_ethtool_info{driver="bnxt_en"} * on(instance) group_left(nodename) node_uname_info'
    else:
        query_template  = 'node_ethtool_info{driver=~"bnxt_en|mlx5_core"} * on(instance) group_left(nodename) node_uname_info'
    result = query_range_prometheus(query_template, start_time, end_time, step)
    # print(result)
    res = {}
    for item in result:
        metric = item['metric']
        instance = metric['instance'].split(":")[0]

        if instance not in res:
            res[instance] = {
                "instance": instance,
                "nodename": metric['nodename'],
                "children": []
            }
        res[instance]["children"].append({
            "port_title": metric['device'],
            "port_value": metric['device']
        })
    return res


def query_roce_sfp_info():
    available_hosts = list(set(map(lambda x: x.ip, utils.query_host().all())))  # for host group filter
    instance_pattern = '|'.join([f"{host}:9100" for host in available_hosts])
    query_template = '{__name__=~"roce_sfp.*", instance=~"' + instance_pattern + '"}'
    result = query_prometheus(query_template)
    
    res = {}
    for item in result:
        metric = item['metric']
        value = item['value']
        instance = metric.pop("instance")
        device =  metric.pop("device")
        metric_name = metric.pop("__name__")
        metric.pop("job")
        
        if instance not in res:
            res[instance] = {}
        if device not in res[instance]:
            res[instance][device] = {} 
        
        if metric_name == "roce_sfp_info":
            res[instance][device].update(metric)
        elif metric_name in ["roce_sfp_tx_power_dbm", "roce_sfp_rx_power_dbm", "roce_sfp_tx_bias_current_ma"]:
            channel = int(metric["channel"])
            if metric_name not in res[instance][device]:
                res[instance][device][metric_name] = {}
            res[instance][device][metric_name][channel] = value[1]
        else:
            res[instance][device][metric_name] = value[1]
            
    roce_info = []
    for key, (instance, devices_info) in enumerate(res.items()):
        query_template  = '{__name__="node_uname_info", instance="' + instance +'"}'
        result = query_prometheus(query_template)
        hostname = result[0]["metric"]["nodename"]   
        
        nic_info = query_node_metric(instance=instance, metric="node_nic_info")
        
        info = {
            "name": hostname,
            "id": key + 1,
            "children": []
        } 
        
        for index, (device, values) in enumerate(devices_info.items()):
            for field in ["roce_sfp_tx_power_dbm", "roce_sfp_rx_power_dbm", "roce_sfp_tx_bias_current_ma"]:
                if field in values:
                    field_values = values[field]
                    field_str = ','.join(field_values[i] for i in sorted(field_values.keys()))
                    values[field] = field_str
            values["device"] = device
            values["id"] = int(str(key+1) + str(index+1))
            if device in nic_info:
                values["name"] = nic_info[device]["name"]
            info["children"].append(values)
                    
        roce_info.append(info)
    
    return roce_info


def query_interface_mac_address():
    """
    [
        {
            "sn": "testsn",
            "mac_address": "00:0c:29:d8:a5:a5",
            "port": "te-1/1/1"
        },
        {
            "sn": "testsn",
            "mac_address": "64:9d:99:d7:7d:de",
            "port": "te-1/1/1"
        }
    ]
    """
    query_template = 'openconfig_network_instance:network_instances_network_instance_fdb_mac_table_entries_entry_interface_interface_ref_state'
    result = query_prometheus(query_template)
    # print(result)
    res = []
    seen = set()
    for item in result:
        mac_address = item["metric"]["entry_mac_address"]
        sn = item["metric"]["target"]
        port = item["metric"]["interface"]

        identifier = (mac_address, sn)
        if identifier not in seen:
            seen.add(identifier)
            info = {
                "mac_address": mac_address.lower(),
                "sn": sn,
                "port": port
            }
            res.append(info)
    return res


def query_interface_mac_address_by_target(target, time=None):
    """
    [
        {
            "sn": "testsn",
            "mac_address": "00:0c:29:d8:a5:a5",
        },
        {
            "sn": "testsn",
            "mac_address": "64:9d:99:d7:7d:de",
        }
    ]
    """
    query_template  = f'{{__name__="openconfig_network_instance:network_instances_network_instance_fdb_mac_table_entries_entry_interface_interface_ref_state", target="{target}"}}'
    result = query_prometheus(query_template, time=time)
    # print(result)
    res = []
    seen = set()
    for item in result:
        mac_address = item["metric"]["entry_mac_address"]
        sn = item["metric"]["target"]
        port = item["metric"]["interface"]

        identifier = (mac_address, sn)
        if identifier not in seen:
            seen.add(identifier)
            info = {
                "mac_address": mac_address.lower(),
                "sn": sn,
                "port": port
            }
            res.append(info)
    return res


def update_client_data(mac_info_list):
    session = inven_db.get_session()
    switch_mac_list = list(map(lambda x: x[0], session.query(Switch.mac_addr).filter(Switch.mac_addr != None).all()))
    with session.begin(subtransactions=True):
        session.query(ClientDeviceInfo).filter(ClientDeviceInfo.mac_address.in_(switch_mac_list)).delete()

    mac_info_set = set((item['sn'], item['mac_address'], item['port']) for item in [i for i in mac_info_list if i['mac_address'] not in switch_mac_list])
    sn_mac_port_dict = {(item['sn']+item['mac_address']): item for item in mac_info_list if item['mac_address'] not in switch_mac_list}
    # key sn+mac+port value ip
    sn_mac_port_ip_address_dict = {(item['sn']+item['mac_address']+item['port']): item.get('ip_address', '') for item in mac_info_list if item['mac_address'] not in switch_mac_list}
    to_be_update_device_info_list = []

    session = inven_db.get_session()
    # update client device data
    try:
        with session.begin(subtransactions=True):
            existing_client_devices = session.query(ClientDeviceInfo).execution_options(populate_existing=True).all()
            for current_client_device in existing_client_devices:
                current_client_device.client_name = current_client_device.mac_address if not current_client_device.client_name else current_client_device.client_name
                # exact match switch_sn mac_address and port
                current_client_device.ip_source = 'static' if not current_client_device.ip_source or current_client_device.ip_source != 'dhcp' else current_client_device.ip_source
                if (current_client_device.switch_sn, current_client_device.mac_address, current_client_device.port) in mac_info_set:
                    if current_client_device.state == 'offline':
                        current_client_device.update_time = func.now()
                    current_client_device.state = 'online'
                    current_client_device.ip_address = sn_mac_port_ip_address_dict.get(
                        current_client_device.switch_sn + current_client_device.mac_address + current_client_device.port
                    ) or current_client_device.ip_address
                    current_client_device.ip_source = (
                        'dhcp' if sn_mac_port_ip_address_dict.get(
                            current_client_device.switch_sn + current_client_device.mac_address + current_client_device.port
                        ) else current_client_device.ip_source or 'static'
                    )
                    current_client_device.manufacturer = get_organization_name_by_mac(current_client_device.mac_address) if not current_client_device.manufacturer else current_client_device.manufacturer
                    mac_info_set.remove((current_client_device.switch_sn, current_client_device.mac_address, current_client_device.port))
                # can match switch_sn and mac_address but port cannot be matched, then try update record which port is empty
                elif not current_client_device.port and (current_client_device.switch_sn + current_client_device.mac_address) in sn_mac_port_dict.keys() and (current_client_device.switch_sn, current_client_device.mac_address) not in to_be_update_device_info_list:
                    if mac_info_set:
                        target_mac_info = sn_mac_port_dict[current_client_device.switch_sn+current_client_device.mac_address]
                        to_be_update_device_info_list.append((current_client_device.switch_sn, current_client_device.mac_address, target_mac_info['port']))
                        mac_info_set.remove((current_client_device.switch_sn, current_client_device.mac_address,target_mac_info['port']))
                # cannot match switch_sn and mac_address, then set state to offline
                # or can match switch_sn and mac_address but port cannot be matched and there is no record which port is empty
                # then set state to offline
                else:
                    if current_client_device.state == 'online':
                        current_client_device.update_time = func.now()
                    current_client_device.state = 'offline'
                    current_client_device.manufacturer = get_organization_name_by_mac(current_client_device.mac_address) if not current_client_device.manufacturer else current_client_device.manufacturer
            for to_be_update_device_info in to_be_update_device_info_list:
                to_be_update_device = session.query(ClientDeviceInfo).filter(and_(ClientDeviceInfo.port.is_(None), ClientDeviceInfo.switch_sn == to_be_update_device_info[0], ClientDeviceInfo.mac_address == to_be_update_device_info[1])).first()
                if to_be_update_device is None:
                    LOG.error("Update client not exist")
                    LOG.error(to_be_update_device_info)
                    continue
                to_be_update_device.state = 'online'
                to_be_update_device.update_time = func.now()
                to_be_update_device.manufacturer = get_organization_name_by_mac(to_be_update_device.mac_address) if not to_be_update_device.manufacturer else to_be_update_device.manufacturer
                to_be_update_device.port = to_be_update_device_info[2]
                to_be_update_device.ip_address = sn_mac_port_ip_address_dict.get(
                    to_be_update_device.switch_sn + to_be_update_device.mac_address + to_be_update_device.port,
                    '') or to_be_update_device.ip_address
                to_be_update_device.ip_source = 'dhcp' if to_be_update_device.ip_address else 'static'
            for to_be_insert_device_info in mac_info_set:
                to_be_insert_device_switch_sn, to_be_insert_device_mac_address, to_be_insert_device_port = to_be_insert_device_info
                if to_be_insert_device_mac_address not in switch_mac_list:
                    to_be_insert_device = ClientDeviceInfo(
                        switch_sn=to_be_insert_device_switch_sn,
                        mac_address=to_be_insert_device_mac_address,
                        ip_address=sn_mac_port_ip_address_dict.get(
                            to_be_insert_device_switch_sn + to_be_insert_device_mac_address + to_be_insert_device_port,
                            ''),
                        client_name=to_be_insert_device_mac_address,
                        manufacturer=get_organization_name_by_mac(to_be_insert_device_mac_address),
                        port=to_be_insert_device_port,
                        state='online',
                        update_time=func.now()
                    )
                    to_be_insert_device.ip_source = 'dhcp' if to_be_insert_device.ip_address else 'static'
                    session.add(to_be_insert_device)
    except Exception as e:
        session.rollback()
        LOG.error("Update client device data fail")
        LOG.error(traceback.format_exc())


def query_dhcp_snooping_info():
    query_template = 'fsconfig_dhcp_snooping:dhcp_snooping_state_entries_entry'
    result = query_prometheus(query_template)
    res = []
    seen = set()
    for item in result:
        vlan_id = item["metric"]["vlan_id"]
        mac_address = item["metric"]["mac_address"]
        port = item["metric"]["port"]
        ip_address = item["metric"]["ip_address"]
        lease = item["metric"]["lease"]
        sn = item["metric"]["target"]

        identifier = (sn, mac_address, port)
        if identifier not in seen:
            seen.add(identifier)
            info = {
                "vlan_id": vlan_id,
                "mac_address": mac_address.lower(),
                "port": port,
                "ip_address": ip_address,
                "lease": lease,
                "sn": sn
            }
            res.append(info)
    return res


def query_dhcp_snooping_info_by_target_and_mac(target, mac, port, time=None):
    query_template = f'{{__name__="fsconfig_dhcp_snooping:dhcp_snooping_state_entries_entry", target="{target}", mac_address="{mac}", port="{port}"}}'
    result = query_prometheus(query_template, time)
    res = []
    seen = set()
    for item in result:
        vlan_id = item["metric"]["vlan_id"]
        mac_address = item["metric"]["mac_address"]
        port = item["metric"]["port"]
        ip_address = item["metric"]["ip_address"]
        lease = item["metric"]["lease"]
        sn = item["metric"]["target"]

        identifier = (sn, mac_address, port)
        if identifier not in seen:
            seen.add(identifier)
            info = {
                "vlan_id": vlan_id,
                "mac_address": mac_address.lower(),
                "port": port,
                "ip_address": ip_address,
                "lease": lease,
                "sn": sn
            }
            res.append(info)
    return res[0] if res else None


def query_mlag_info_by_target(target, time=None):
    query_mlag_config_template  = f'{{__name__="fsconfig_mlag:mlag_config", target="{target}"}}'
    query_mlag_state_template  = f'{{__name__="fsconfig_mlag:mlag_state", target="{target}"}}'
    mlag_config = query_prometheus(query_mlag_config_template, time=time)
    mlag_state = query_prometheus(query_mlag_state_template, time=time)

    if not mlag_config and not mlag_state:
        return []

    return [{
        'domain_id': mlag_config[0]['metric'].get('domain_id', '') if mlag_config else None,
        'domain_mac': mlag_config[0]['metric'].get('domain_mac', '') if mlag_config else '',
        'node_id': mlag_config[0]['metric'].get('node_id', '') if mlag_config else None,
        'peer_link': mlag_config[0]['metric'].get('peer_link', '') if mlag_config else '',
        'peer_ip': mlag_config[0]['metric'].get('peer_ip', '') if mlag_config else '',
        'peer_vlan': mlag_config[0]['metric'].get('peer_vlan', '') if mlag_config else None,
        'neighbor_status': mlag_state[0]['metric'].get('neighbor_status', '') if mlag_state else '',
        'config_matched': mlag_state[0]['metric'].get('config_matched', '') if mlag_state else '',
        'mac_synced': mlag_state[0]['metric'].get('mac_synced', '') if mlag_state else '',
        'num_of_links': mlag_state[0]['metric'].get('num_of_links', 0) if mlag_state else 0,
    }]


def query_poe_info_by_target(target, time=None):
    query_poe_state_template = f'{{__name__="openconfig_interfaces:interfaces_interface_openconfig_if_ethernet:ethernet_openconfig_if_poe:poe_state", target="{target}"}}'
    poe_state = query_prometheus(query_poe_state_template, time=time)

    if not poe_state:
        return []

    data_dict = {}

    for poe_state_detail in reversed(poe_state):
        data_dict[poe_state_detail['metric'].get('interface_name', '') + target] = {
            'interface_name': poe_state_detail['metric'].get('interface_name', ''),
            'enabled': poe_state_detail['metric'].get('enabled', ''),
            'power_used': poe_state_detail['metric'].get('power_used', None),
            'power_class': poe_state_detail['metric'].get('power_class', '')
        }

    return list(reversed(data_dict.values()))


def get_modules_count(target=None):
    target_str = ""
    if target:
        target_str = f'target=~"{"|".join(target)}"'
    query_template = f"""
        count by (fsconfig_platform_transceiver_extensions_transmission_rate) (
            count by (fsconfig_platform_transceiver_extensions_transmission_rate, interface_group, target) (
                label_replace(
                    openconfig_platform:components_component_openconfig_platform_transceiver:transceiver_state{{interface_name=~"[a-z]+-1/1/[0-9]+.*",{target_str}}},
                    "interface_group",
                    "$1-$2",
                    "interface_name",
                    "([a-z]+)-(1/1/[0-9]+)[^,]*"
                )
            )
        )
    """
    result = query_prometheus(query_template)
    res = {}
    for item in result:
        metric = item.get('metric', {})
        rate = metric.get('fsconfig_platform_transceiver_extensions_transmission_rate', 'Unknow')
        if rate and item['value']:
            res[rate] = item['value'][1]
    return res

def get_modules_history_info(target=None, start_time=None, end_time=None):
    _, _, start_time, end_time, step = format_query_time(start_time, end_time)
    time_points_value = generate_time_points_dict(start_time, end_time, step)
    target_str = ""
    if target:
        target_str = f'target=~"{"|".join(target)}"'
    query_template = f"""
        (
            clamp_min((
                (sum(
                    label_replace(
                        openconfig_platform:components_component_openconfig_platform_transceiver:transceiver_state{{interface_name=~"[a-z]+-1/1/[0-9]+.*",{target_str}}},
                        "interface_group",
                        "$1-$2",
                        "interface_name",
                        "([a-z]+)-(1/1/[0-9]+)[^,]*"
                    )
                ) or vector(0)) - (sum(
                    label_replace(
                        openconfig_platform:components_interface_abnormal_num{{interface_name=~"[a-z]+-1/1/[0-9]+.*",{target_str}}},
                        "interface_group",
                        "$1-$2",
                        "interface_name",
                        "([a-z]+)-(1/1/[0-9]+)[^,]*"
                    )
                ) or vector(0))
            ),0)
            /
            (sum(
                label_replace(
                    openconfig_platform:components_component_openconfig_platform_transceiver:transceiver_state{{interface_name=~"[a-z]+-1/1/[0-9]+.*",{target_str}}},
                    "interface_group",
                    "$1-$2",
                    "interface_name",
                    "([a-z]+)-(1/1/[0-9]+)[^,]*"
                )
            ) or vector(0))
        ) * 100
    """
    print(query_template)
    result = query_range_prometheus(query_template, start_time, end_time, step)
    query_abnormal_template = f"""
        sum(
            label_replace(
                openconfig_platform:components_interface_abnormal_num{{interface_name=~"[a-z]+-1/1/[0-9]+.*",{target_str}}},
                "interface_group",
                "$1-$2",
                "interface_name",
                "([a-z]+)-(1/1/[0-9]+)[^,]*"
            )
        )
    """
    print(query_abnormal_template)
    abnormal_result = query_range_prometheus(query_abnormal_template, start_time, end_time, step)
    res = {
        "value": []
    }
    if result and abnormal_result:
        all_result = result[0]
        abnormal_result = abnormal_result[0]
        for index, item in enumerate(all_result["values"]):
            timestamp = item[0]
            date_time = datetime.fromtimestamp(timestamp)
            date_time_str = date_time.strftime("%Y-%m-%d %H:%M:%S")
            value = item[1] + '%'
            abnormal_count = abnormal_result["values"][index][1]
            time_points_value[date_time_str] = [date_time_str, value, abnormal_count]
        res.update({
            "value": list(time_points_value.values())
        })
    return res

def get_modules_port_status(target, port, metric, start_time=None, end_time=None, channel_index=None):
    _, _, start_time, end_time, step = format_query_time(start_time, end_time)
    time_points_value = generate_time_points_dict(start_time, end_time, step)
    thresholds = {}
    query_low = ''
    query_high = ''
    filter_str = f'channel_index="{channel_index}"' if channel_index else ''
    common_prefix = 'openconfig_platform:components_component_openconfig_platform_transceiver:'
    def get_thresholds(query, prefix, thresholds):
        result = query_prometheus(query, end_time)
        for item in result:
            severity = item["metric"].get("threshold_severity", "")
            if ":" in severity:
                key = f"{prefix}_{severity.split(':')[1].lower()}"
                value = item.get("value", [None, None])[1]
                thresholds[key] = value
        return thresholds
    if "temperature" in metric:
        thresholds = {
            "low_major": constants.temperature_status.LOW_MAJOR,
            "low_warning": constants.temperature_status.LOW_WARNING,
            "high_major": constants.temperature_status.HIGH_MAJOR,
            "high_warning": constants.temperature_status.HIGH_WARNING
        }
        query_low = "transceiver_thresholds_threshold_state_laser_temperature_lower"
        query_high = "transceiver_thresholds_threshold_state_laser_temperature_upper"
    elif "voltage" in metric:
        thresholds = {
            "low_major": constants.voltage_status.LOW_MAJOR,
            "low_warning": constants.voltage_status.LOW_WARNING,
            "high_major": constants.voltage_status.HIGH_MAJOR,
            "high_warning": constants.voltage_status.HIGH_WARNING
        }
        query_low = "transceiver_thresholds_threshold_state_supply_voltage_lower"
        query_high = "transceiver_thresholds_threshold_state_supply_voltage_upper"
    elif "bias" in metric:
        thresholds = {
            "low_major": None,
            "low_warning": None,
            "high_major": None,
            "high_warning": None
        }
        query_low = "transceiver_thresholds_threshold_state_laser_bias_current_lower"
        query_high = "transceiver_thresholds_threshold_state_laser_bias_current_upper"
    elif "output" in metric:
        thresholds = {
            "low_major": None,
            "low_warning": None,
            "high_major": None,
            "high_warning": None
        }
        query_template = f"""openconfig_platform:components_component_openconfig_platform_transceiver:transceiver_state{{interface_name="{port}", target="{target}"}}"""
        res = query_prometheus(query_template)
        if res:
            thresholds_dict = constants.power_thresholds.get(res[0]["metric"]["form_factor"], {})
            thresholds["low_warning"] = thresholds_dict["outputPowerMin"]
            thresholds["high_warning"] = thresholds_dict["outputPowerMax"]

        query_low = "transceiver_thresholds_threshold_state_output_power_lower"
        query_high = "transceiver_thresholds_threshold_state_output_power_upper"
    else:
        thresholds = {
            "low_major": None,
            "low_warning": None,
            "high_major": None,
            "high_warning": None
        }
        query_template = f"""openconfig_platform:components_component_openconfig_platform_transceiver:transceiver_state{{interface_name="{port}", target="{target}"}}"""
        res = query_prometheus(query_template)
        if res:
            thresholds_dict = constants.power_thresholds.get(res[0]["metric"]["form_factor"], {})
            thresholds["low_warning"] = thresholds_dict["inputPowerMin"]
            thresholds["high_warning"] = thresholds_dict["inputPowerMax"]

        query_low = "transceiver_thresholds_threshold_state_input_power_lower"
        query_high = "transceiver_thresholds_threshold_state_input_power_upper"

    query_low = f"""{common_prefix}{query_low}{{interface_name="{port}", target="{target}"}}"""
    thresholds = get_thresholds(query_low, "low", thresholds)
    query_high = f"""{common_prefix}{query_high}{{interface_name="{port}", target="{target}"}}"""
    thresholds = get_thresholds(query_high, "high", thresholds)
    query_template = f"""{common_prefix}{metric}{{interface_name="{port}", target="{target}", {filter_str}}}"""
    result = query_range_prometheus(query_template, start_time, end_time, step)
    res = {
        "threshold": thresholds,
        "value": [],
        "sn": target,
        "interface_name": port
    }
    if result:
        item = result[0]
        for timestamp, value in item["values"]:
            date_time = datetime.fromtimestamp(timestamp)
            date_time_str = date_time.strftime("%Y-%m-%d %H:%M:%S")
            time_points_value[date_time_str] = [date_time_str, value]
        res.update({
            "sn": item["metric"].get("target"),
            "interface_name": item["metric"].get("interface_name"),
            "value": list(time_points_value.values())
        })
        if item["metric"].get("channel_index", None) is not None:
            res["channel_index"] = item["metric"]["channel_index"]
    return res

def get_light_attenuation(port_info, start_time=None, end_time=None):
    _, _, start_time, end_time, step = format_query_time(start_time, end_time)
    time_points_dict = generate_time_points_dict(start_time, end_time, step)
    res = []
    for item in port_info:
        source_interface = item['source_port']
        source_sn = item['source_sn']
        target_interface = item['target_port']
        target_sn = item['target_sn']
        target_mac = item['target_mac']
        query_template = f"""
            openconfig_platform:components_component_openconfig_platform_transceiver:transceiver_state_output_power
                {{interface_name="{source_interface}",target="{source_sn}"}}
                    * on () group_left ()
                        (group by (interface_name, target, port_id, chassis_id) (
                            openconfig_lldp:lldp_interfaces_interface_neighbors_neighbor_state
                                {{chassis_id="{target_mac}",interface_name="{source_interface}",port_id="{target_interface}",target="{source_sn}"}}
                        ))
            -
            on(channel_index) group_left() 
                (openconfig_platform:components_component_openconfig_platform_transceiver:transceiver_state_input_power
                    {{interface_name="{target_interface}", target="{target_sn}"}}  * on() group_left() 
                        (group by (interface_name, target, port_id, chassis_id) (
                            openconfig_lldp:lldp_interfaces_interface_neighbors_neighbor_state
                                {{chassis_id="{target_mac}",interface_name="{source_interface}",port_id="{target_interface}",target="{source_sn}"}}
        )))
        """
        result = query_range_prometheus(query_template, start_time, end_time, step)
        for item in result:
            time_points_value = copy.deepcopy(time_points_dict)
            for timestamp, value in item["values"]:
                date_time = datetime.fromtimestamp(timestamp)
                date_time_str = date_time.strftime("%Y-%m-%d %H:%M:%S")
                time_points_value[date_time_str] = [date_time_str, f'{float(value):.2f}']
            info = {
                'channel_index': item['metric']['channel_index'],
                'target': item['metric']['target'],
                'interface_name': item['metric']['interface_name'],
                'values': list(time_points_value.values())
            }
            res.append(info)
    return res
