import logging
import os
import re
import signal
import time, datetime
import packaging.version
from functools import partial

from celery_app import my_celery_app
from celery_app.automation_task import AmpConBaseTask
from server import constants as C
from server.db.models import inventory
from server.db.models.monitor import monitor_db
from server.db.models.automation import automation_db
from server.util import str_helper, utils
from server.util import ssh_util as conn_client
from server.celery_app.automation_task import AutomationTask

db = inventory.inven_db

LOG = logging.getLogger(__name__)
DEFAULT_TIMEOUT = 360

global_sn = ''


def retry(n):
    def _wrap(f):
        
        def warp(*args, **kwargs):
            count = 0
            while count < n:
                try:
                    r = f(*args, **kwargs)
                    return r
                except Exception as e:
                    count += 1
                    if count >= n:
                        raise e
        
        return warp
    
    return _wrap


def is_version_gt(version1, version2):
    version1 = packaging.version.parse(re.sub(r'[^0-9.]', '', version1.split('-')[0]))
    version2 = packaging.version.parse(re.sub(r'[^0-9.]', '', version2.split('-')[0]))
    return version1 > version2


class UpgradeError(Exception):
    def __init__(self, sn, msg):
        self.sn = sn
        self.msg = msg
        self.message = 'upgrade switch %s failed msg [%s]' % (sn, msg)


def _handler_exit(sig, frame):
    try:
        LOG.error('%s upgrade stop by interrupt', global_sn)
        switch = db.get_model(inventory.Switch, filters={'sn': [global_sn]})
        LOG.error('%s upgrade stop by interrupt, status %s', switch)
        if switch:
            if switch.upgrade_status == C.outswitch_status.UPGRADING:
                LOG.error('switch %s %s', global_sn, 'process %s terminal by interrupt' % global_sn)
                monitor_db.add_event(global_sn, 'error', 'process %s terminal by interrupt' % global_sn)
                db.add_switch_log(global_sn, 'process %s terminal by interrupt' % global_sn, level='error')
                db.update_model(inventory.Switch, filters={'sn': [global_sn]},
                                updates={inventory.Switch.upgrade_status: C.outswitch_status.UPGRADE_FAILED})
    finally:
        exit(0)


class UpgradeService(object):
    
    def __init__(self, host, user, password, app_user, app_password, sn, switch_platform, files, task_name=None, job_name=None,
                 check_image_flag=False, job_time=datetime.datetime.now(), group_name='', image_name='', **kwargs):
        self.job_name = job_name
        self.host = host
        self.user = user
        self.password = password
        self.app_user = app_user
        self.app_password = app_password
        self.files = files
        self.switch_model = inventory.Switch
        self.switch_info = db.get_model(self.switch_model, filters={'sn': [sn]})
        if image_name:
            image_info = db.get_model(inventory.SwitchImage, filters={'image_name': [image_name]})
            if not image_info:
                raise UpgradeError(sn, 'image %s not found' % image_name)
            if image_info.platform != db.get_model(inventory.SwitchSystemInfo, filters={'model': [switch_platform]}).platform:
                raise UpgradeError(sn, 'image %s platform %s not match switch platform %s' %
                                   (image_name, image_info.platform, switch_platform))
            self.switch_platform = inventory.SwitchSystemInfo()
            self.switch_platform.platform = image_info.platform
            self.switch_platform.up_to_date_version = image_info.version + '/' + image_info.revision
            self.switch_platform.up_to_date_image_path = image_info.image_path
            self.switch_platform.up_to_date_image_md5_path = image_info.image_md5_path
            self.switch_platform.speed_for_license = "1G"
            self.switch_platform.model = switch_platform
            self.switch_platform.feature = "EE"
            self.switch_platform.up_to_date_onie_path = ""
            self.switch_platform.patched_tar_file = ""
            self.switch_platform.patched_install_script = ""
            self.switch_platform.script_file_path = ""
            self.switch_platform.manual_upgrade_scripts = ""
        else:
            self.switch_platform = db.get_model(inventory.SwitchSystemInfo, filters={'model': [switch_platform]})

        self.is_switch_use_mnt_path_instead_of_cftmp_when_upgrading = self.switch_platform.model in C.SWITCH_USE_MNT_PATH_INSTEAD_OF_CFTMP_WHEN_UPGRADING
        self.ssh_session = None
        self.check_image_flag = check_image_flag
        if self.is_switch_use_mnt_path_instead_of_cftmp_when_upgrading:
            self.d_image_path = '/mnt/open/' + os.path.basename(self.switch_platform.up_to_date_image_path)
            self.need_mv_image = False
            if self.switch_platform.up_to_date_image_md5_path:
                self.d_image_md5_path = '/mnt/open/' + os.path.basename(self.switch_platform.up_to_date_image_md5_path)
        else:
            self.d_image_path = C.BASE_DIR + os.path.basename(self.switch_platform.up_to_date_image_path)
            self.need_mv_image = True
            if self.switch_platform.up_to_date_image_md5_path:
                self.d_image_md5_path = C.BASE_DIR + os.path.basename(self.switch_platform.up_to_date_image_md5_path)
        self.job_time = job_time
        self.group_name = group_name
        self.task_name = task_name
        self.need_push_image_to_cftmp = False
        self.sn = sn
    
    def __get_host(self):
        try:
            db_session = db.get_session()
            tmp_host = db_session.query(self.switch_model).filter(self.switch_model.sn == self.sn).first().mgt_ip
            db_session.close()
       
            if tmp_host:
                tmp_ip = tmp_host
            else:
                tmp_ip = self.host
        except:
            tmp_ip = self.host
        return tmp_ip
    
    def __get_execute_new(self, cmd):
        return conn_client.interactive_shell_linux(cmd, hostname=self.__get_host(),
                                                   username=self.user, password=self.password)
    
    def __get_execute_cli_new(self, cmd):
        return conn_client.interactive_shell_cli(cmd, hostname=self.__get_host(),
                                                 username=self.user, password=self.password)
    
    def init_ssh_session(self):
        
        # reinit session close pre
        try:
            conn_client.close_session(self.ssh_session)
        except:
            pass
        
        ssh_session, status, _ = conn_client.get_interactive_session(self.host, username=self.user,
                                                                     password=self.password,
                                                                     timeout=DEFAULT_TIMEOUT)
        if status == C.RMA_ACTIVE:
            self.ssh_session = ssh_session
        else:
            self.group_format_log(self.switch_info.sn, 'connect to switch failed', level='info')
            raise UpgradeError(self.switch_info.sn, 'connect to switch failed')
    
    @retry(3)
    def _execute(self, cmd):
        if not self.ssh_session or self.ssh_session.closed:
            self.init_ssh_session()
        
        res, status = conn_client.interactive_shell_linux_with_conn(self.ssh_session, cmd)
        if status == C.RMA_ACTIVE:
            self.add_switch_log(self.switch_info.sn,
                                'execute cmd [%s] success, stdout:\n[%s]' % (cmd, res), level='info')
            return res, status
        else:
            self.add_switch_log(self.switch_info.sn,
                                'execute cmd [%s] failed, stdout:\n[%s]' % (cmd, res), level='error')
            time.sleep(10)
            self.init_ssh_session()
            time.sleep(3)
            raise UpgradeError(self.switch_info.sn,
                               'execute [%s] failed, stdout:[%s], status:[%s]' % (cmd, res, status))
    
    @retry(3)
    def _cp_file(self, source_file, dest_dir):
        if not self.ssh_session or self.ssh_session.closed:
            self.init_ssh_session()
        
        file_name = os.path.basename(source_file)
        cmd = 'sudo cp -f %s %s' % (source_file, dest_dir)
        res, status = conn_client.interactive_shell_linux_with_conn(self.ssh_session, cmd)
        check_cmd = 'sudo ls -al %s/%s ' % (dest_dir, file_name)
        ck_res, ck_status = conn_client.interactive_shell_linux_with_conn(self.ssh_session, check_cmd)
        if status == C.RMA_ACTIVE and ck_status == C.RMA_ACTIVE and 'No such' not in ck_res:
            
            self.add_switch_log(self.switch_info.sn,
                                'execute cmd [%s] success, stdout:\n[%s]' % (cmd, res), level='info')
            return res, status
        else:
            self.add_switch_log(self.switch_info.sn,
                                'execute cmd [%s] failed, stdout:\n[%s]' % (cmd, res), level='error')
            time.sleep(10)
            self.init_ssh_session()
            time.sleep(3)
            raise UpgradeError(self.switch_info.sn,
                               'execute [%s] failed, stdout:[%s], status:[%s]' % (cmd, res, status))
    
    @retry(3)
    def _mv_file(self, source_file, dest_dir):
        if not self.ssh_session or self.ssh_session.closed:
            self.init_ssh_session()
        
        file_name = os.path.basename(source_file)
        cmd = 'sudo mv -f %s %s' % (source_file, dest_dir)
        res, status = conn_client.interactive_shell_linux_with_conn(self.ssh_session, cmd)
        check_cmd = 'sudo ls -al %s/%s ' % (dest_dir, file_name)
        ck_res, ck_status = conn_client.interactive_shell_linux_with_conn(self.ssh_session, check_cmd)
        if status == C.RMA_ACTIVE and ck_status == C.RMA_ACTIVE and 'No such' not in ck_res:
            
            self.add_switch_log(self.switch_info.sn,
                                'execute cmd [%s] success, stdout:\n[%s]' % (cmd, res), level='info')
            return res, status
        else:
            self.add_switch_log(self.switch_info.sn,
                                'execute cmd [%s] failed, stdout:\n[%s]' % (cmd, res), level='error')
            time.sleep(10)
            self.init_ssh_session()
            time.sleep(3)
            raise UpgradeError(self.switch_info.sn,
                               'execute [%s] failed, stdout:[%s], status:[%s]' % (cmd, res, status))
    
    @retry(3)
    def _execute_cli(self, cmd):
        if not self.ssh_session or self.ssh_session.closed:
            self.init_ssh_session()
        
        res, status = self.__get_execute_cli_new(cmd)
        if status == C.RMA_ACTIVE:
            self.add_switch_log(self.switch_info.sn,
                                'execute cmd [%s] success, stdout:\n[%s]' % (cmd, res), level='info')
            return res, status
        else:
            self.add_switch_log(self.switch_info.sn,
                                'execute cmd [%s] failed, stdout:\n[%s]' % (cmd, res), level='error')
            time.sleep(10)
            self.init_ssh_session()
            time.sleep(3)
            raise UpgradeError(self.switch_info.sn,
                               'execute [%s] failed, stdout:[%s], status:[%s]' % (cmd, res, status))
    
    def _push_file(self, local_path, remote_path, md5_file_tag=False, **kwargs):
        
        self.group_format_log(self.switch_info.sn, 'start to push file [%s]' % local_path, level='info')
        
        if md5_file_tag:
            time.sleep(1)
            self.init_ssh_session()
            time.sleep(2)
        else:
            time.sleep(10)
            self.init_ssh_session()
            time.sleep(5)
        res, status = conn_client.interactive_push_file_with_conn(self.ssh_session, local_path, remote_path,
                                                                  retry_time=1, **kwargs)
        check_cmd = 'sudo ls -al %s ' % remote_path
        ck_res, ck_status = conn_client.interactive_shell_linux_with_conn(self.ssh_session, check_cmd)
        if status == C.RMA_ACTIVE and ck_status == C.RMA_ACTIVE and 'No such' not in ck_res:
            self.group_format_log(self.switch_info.sn,
                                  'complete push file [%s]' % local_path, level='info')
            return res, status
        else:
            self.add_switch_log(self.switch_info.sn,
                                'push file [%s] failed' % local_path, level='error')
            # time.sleep(10)
            # self.init_ssh_session()
            # time.sleep(3)
            raise UpgradeError(self.switch_info.sn,
                               'push [%s] to [%s] failed, stdout:[%s], status:[%s]' %
                               (local_path, remote_path, res, status))
    
    def get_switch_reversion(self):
        res, status = self._execute('version')
        LOG.debug('re %s, status %s', res, status)
        current_version = list(map(lambda x: x[0] if x[0] else x[1] if x[1] else x[2], re.findall("L2/L3 Version/Revision.*:\s*(.*?)\n|PICOS Release/Commit.*:\s*(.*?)\n|Software Version.*:\s*(.*?)\n", res)))
        current_version = current_version[0].strip()
        version, seg, revision = current_version.rpartition('/')
        revision = revision.split('-')[0]
        return current_version, version, revision
    
    def start(self):

        if self.job_name:
            automation_db.update_switch_common_job(self.job_name, state="running", log='', start_time=int(datetime.datetime.now().timestamp()), end_time=None)

        # add process exit unexpectedly handler
        global global_sn
        global_sn = self.switch_info.sn
        signal.signal(signal.SIGTERM, _handler_exit)
        signal.signal(signal.SIGINT, _handler_exit)
        
        self.add_switch_log(self.switch_info.sn, 'start to upgrade switch image', level='info')
        self.init_ssh_session()
        time.sleep(3)
        
        current_version, version, revision = self.get_switch_reversion()

        revision = revision.split('-')[0]
        if revision == self.switch_platform.up_to_date_version.split('/')[1]:
            self.add_switch_log(self.switch_info.sn,
                                 'upgrade failed, current version is %s, request version is %s' %
                               (current_version, self.switch_platform.up_to_date_version))
            db.update_model(self.switch_model, filters={'sn': [self.switch_info.sn]},
                            updates={self.switch_model.upgrade_status: C.outswitch_status.NO_UPGRADE})
            return

        # if parse version error will continue upgrade, version 9.8.7 is a special version, will continue upgrade
        if utils.is_image_name_comply_with_release_rules(self.switch_platform.up_to_date_image_path.split('/')[-1]) and version != '9.8.7':
            should_abort = False
            try:
                if is_version_gt(version, self.switch_platform.up_to_date_version.split('/')[0]):
                    error_msg = (
                        f'The version to be upgraded is lower than the existing version, '
                        f'switch version {current_version}, '
                        f'request version {self.switch_platform.up_to_date_version}'
                    )
                    LOG.error(error_msg)

                    db.update_model(self.switch_model, filters={'sn': [self.switch_info.sn]},
                                    updates={self.switch_model.upgrade_status: C.outswitch_status.UPGRADE_FAILED})
                    should_abort = True
                    raise UpgradeError(self.switch_info.sn, error_msg)
            except:
                LOG.warning('switch %s parse version failed, current version %s, up to date version %s',
                            self.switch_info.sn, version, self.switch_platform.up_to_date_version)
                if should_abort:
                    raise UpgradeError(self.switch_info.sn, error_msg)

        # read the domain name from /etc/resov.conf
        domain_name = self._read_domain_name()
        # push the domain name to /home/<USER>/
        if domain_name:
            self._push_domain_name(domain_name)
        
        # remove other image files
        self.add_switch_log(self.switch_info.sn,
                            'remove other images file in /home ', level='info')

        if self.is_switch_use_mnt_path_instead_of_cftmp_when_upgrading:
            res, status = self._execute('df -h | awk \'/\/mnt\/open$/ {print $4 }\' | head -n 1')
            min_space = 100
        else:
            res, status = self._execute('df -h | awk \'/\/$/ {print $4 }\' | head -n 1')
            min_space = 60 if self.switch_platform.model in C.SWITCH_S3270_MODEL else 200

        avail = res.strip()
        if avail and not self._check_switch_space(avail, min_space):
            monitor_db.add_event(self.switch_info.sn, 'warn',
                                 'has not space for upgrade, current available size is %s' % avail)
            self.add_switch_log(self.switch_info.sn, 'has not space for upgrade, current available size is %s' % avail,
                                level='warn')
            LOG.warn('switch %s has no space for upgrade, current available size is %s',
                     self.switch_info.sn, avail)
            db.update_model(self.switch_model, filters={'sn': [self.switch_info.sn]},
                            updates={self.switch_model.upgrade_status: C.outswitch_status.UPGRADE_FAILED})
            raise UpgradeError(self.switch_info.sn, 'switch %s has no space for upgrade, current available size is %s'
                               % (self.switch_info.sn, avail))

        if self.switch_platform.model in C.SWITCH_MODEL_SAVE_CONFIG_BEFORE_STOP_PICOS:
            self.add_switch_log(self.switch_info.sn, 'Switch model is {}, need to save config first!'.format(str(self.switch_platform.model)), level='info')
            res, status = conn_client.interactive_upgrade_with_conn(self.ssh_session,
                                                                    'sudo save_config')
            if status != C.RMA_ACTIVE:
                raise UpgradeError(self.switch_info.sn, 'Save config failed, stdout:%s' % res)
        
        self.add_switch_log(self.switch_info.sn, 'check switch disk ok, current available size is %s' % avail,
                            level='info')
        
        db.update_model(self.switch_model, filters={'sn': [self.switch_info.sn]},
                        updates={self.switch_model.upgrade_status: C.outswitch_status.UPGRADING})
        
        # step0: push tag
        pre_configs, status = self._execute_cli("show running-config | no-more")
        
        self._change_switch_flag('upgrading')
        
        # step2: check & push image
        self.check_image_by_size()
        self.push_image_by_check()
        
        # upgrade agent
        self._push_agent_files()
        
        filenames = []
        if self.files:
            for filename, path in self.files.items():
                res, status = self._push_file(path, '/cftmp/auto/%s' % filename)
                LOG.debug('push %s re %s, status %s', filename, res, status)
                filenames.append(filename)
            res, status = self._execute('cd /cftmp/auto && sudo chmod +x *')
        else:
            # Check whether there is a system default script
            if self.switch_platform.manual_upgrade_scripts != '':
                LOG.info('load default files %s', self.switch_platform.manual_upgrade_scripts)
                scripts = self.switch_platform.manual_upgrade_scripts.split(',')
                for script in scripts:
                    filename = os.path.basename(script)
                    res, status = self._push_file(script, '/cftmp/auto/%s' % filename)
                    # self.add_switch_log(self.switch_info.sn,
                    #                     'push file %s' % filename,
                    #                     level='info')
                    LOG.info('push %s re %s, status %s', filename, res, status)
                    filenames.append(filename)
                res, status = self._execute('cd /cftmp/auto && sudo chmod +x *')
        
        # step3: upgrade
        # res, status = self._execute('sudo cp %s %s' % (self.d_image_path, '/cftmp'))
        if self.need_mv_image:
            self.add_switch_log(self.switch_info.sn, 'start to move image file %s to /cftmp' % self.d_image_path,
                                level='info')
            self._mv_file(self.d_image_path, '/cftmp')
            LOG.debug('re %s, status %s', res, status)
            self.add_switch_log(self.switch_info.sn, 'complete move image file %s to /cftmp' % self.d_image_path,
                                level='info')
            
            if self.switch_info.version < '3.0' and self.switch_platform.up_to_date_image_md5_path:
                self.add_switch_log(self.switch_info.sn,
                                    'start to move image md5 file %s to /cftmp' % self.d_image_md5_path,
                                    level='info')
                self._mv_file(self.d_image_md5_path, '/cftmp')
                LOG.debug('re %s, status %s', res, status)
                self.add_switch_log(self.switch_info.sn,
                                    'complete move image md5 file %s to /cftmp' % self.d_image_md5_path,
                                    level='info')
             
        # check switch need do save_config
        if utils.switch_need_save_config(self.switch_platform.model):
            self.add_switch_log(self.switch_info.sn, 'start to do save_config', level='info')
            self._add_file_to_backup('/cftmp/auto/')
            self._add_file_to_backup(C.FLAG_FILE)
            self._save_config()
            self.add_switch_log(self.switch_info.sn, 'complete do save_config', level='info')

        image_name = os.path.basename(self.d_image_path)

        if self.switch_platform.model in C.SWITCH_MODEL_USE_UPGRADE_INSTEAD_OF_UPGRADE2:
            self.add_switch_log(self.switch_info.sn, 'start to run upgrade', level='info')
            upgrade_command = f'sudo upgrade /cftmp/{image_name}'
        else:
            self.add_switch_log(self.switch_info.sn, 'start to run upgrade2', level='info')
            if self.is_switch_use_mnt_path_instead_of_cftmp_when_upgrading:
                self.add_switch_log(self.switch_info.sn, 'start to run upgrade2 with sudo upgrade /mnt/open/%s' % image_name, level='info')
                upgrade_command = f'sudo upgrade2 /mnt/open/{image_name}'
            else:
                upgrade_command = f'sudo upgrade2 /cftmp/{image_name}'

        # only current version is lower than 4.4.5.7 and switch model is in C.SWITCH_MODEL_STOP_PICOS_BEFORE_UPGRADE, need to stop picos first
        if not is_version_gt(version, '4.4.5.6') and self.switch_platform.model in C.SWITCH_MODEL_STOP_PICOS_BEFORE_UPGRADE:
            self.add_switch_log(self.switch_info.sn, 'Switch model is {}, need to stop picos first!'.format(str(self.switch_platform.model)), level='info')
            if self.switch_platform.model in C.BUSY_BOX_MODEL_LIST:
                self.add_switch_log(self.switch_info.sn, 'start to stop picos with sudo /etc/init.d/picos stop', level='info')
                stop_picos_command = 'sudo /etc/init.d/picos stop'
            else:
                self.add_switch_log(self.switch_info.sn, 'start to stop picos with sudo systemctl stop picos', level='info')
                stop_picos_command = 'sudo systemctl stop picos'
            res, status = conn_client.interactive_upgrade_with_conn(self.ssh_session, f'''sudo nohup bash -c '({stop_picos_command} || true) && ({upgrade_command} || true) && sudo reboot' &''')
        else:
            res, status = conn_client.interactive_upgrade_with_conn(self.ssh_session, upgrade_command)

        # If need to stop picos, will use nohup to run upgrade command
        if self.switch_platform.model in C.SWITCH_MODEL_STOP_PICOS_BEFORE_UPGRADE:
            time.sleep(240)

        if status != C.RMA_ACTIVE:
            raise UpgradeError(self.switch_info.sn, 'execute upgrade2 failed, stdout:%s' % res)
        
        LOG.debug('re %s, status %s', res, status)
        self.add_switch_log(self.switch_info.sn, 'complete run upgrade2, will be finished 10~20 mins', level='info')
        
        try:
            self.ssh_session.close()
        except:
            pass
        
        if not self.wait_switch(timeout=360):
            raise UpgradeError(self.switch_info.sn,
                               'switch %s after upgrade2 reconnect failed' % self.switch_info.sn)
        
        self.init_ssh_session()
        self.add_switch_log(self.switch_info.sn, 'switch reconnected, start to validate upgrade', level='info')
        
        # step4: check version
        current_version, version, revision = self.get_switch_reversion()
        revision = revision.split('-')[0]
        if revision != self.switch_platform.up_to_date_version.split('/')[1]:
            raise UpgradeError(self.switch_info.sn, 'upgrade failed, current version is %s, request version is %s' %
                               (current_version, self.switch_platform.up_to_date_version))
        
        self.add_switch_log(self.switch_info.sn,
                            'after upgrade check switch, current is [%s], request version is [%s]'
                            % (current_version, self.switch_platform.up_to_date_version),
                            level='info')
        
        # step5: check scripts
        # res, status = self._execute('ls -l /cftmp/auto/')
        # LOG.debug('re %s, status %s', res, status)
        # for filename in filenames:
        #     if filename not in res:
        #         raise UpgradeError(self.switch_info.sn, '%s script not found in /cftmp/auto' % filename)
        #
        # self.add_switch_log(self.switch_info.sn,
        #                     'after upgrade check upgrades scripts, need files [%s], exits files [%s]'
        #                     % (','.join(filenames), res),
        #                     level='info')
        
        # step6: remove tmp
        # shutil.rmtree(script_parent_dir)
        post_configs, status = self._execute_cli("show running-config | no-more")
        
        # post_configs = str_helper.strip_cli_stdout('show all | no-more', post_configs)
        LOG.info('src configs [%s], post configs [%s]', pre_configs, post_configs)
        
        pre_configs = pre_configs.replace('\r', '')
        post_configs = post_configs.replace('\r', '')
        pre_flag, missed_lines = str_helper.compare_tree_configs(pre_configs, post_configs)
        post_flag, added_lines = str_helper.compare_tree_configs(post_configs, pre_configs)
        missed_lines = missed_lines or []
        added_lines = added_lines or []
        # missed_str = str_helper.flat_to_tree(missed_lines)
        # added_str = str_helper.flat_to_tree(added_lines)
        LOG.info('missed_lines %s', missed_lines)
        LOG.info('added_lines %s', added_lines)
        missed_str = '\n'.join(missed_lines)
        added_str = '\n'.join(added_lines)
        self.add_switch_log(self.switch_info.sn,
                            'upgrade config changes: missed:\n[%s] \n added:\n[%s]' % (missed_str, added_str),
                            level='info')
        
        LOG.info('switch %s upgrade success to %s', self.switch_info.sn, self.switch_platform.up_to_date_version)
        
        self._change_switch_flag('deployed')
        
        db.update_model(self.switch_model, filters={'sn': [self.switch_info.sn]},
                        updates={self.switch_model.upgrade_status: C.outswitch_status.UPGRADED,
                                 self.switch_model.version: version, self.switch_model.revision: revision})
        self.add_switch_log(self.switch_info.sn, 'upgrade switch image complete', level='info')
        return
    
    def _push_agent_files(self):
        remote_agent_path = '/opt/auto-deploy/'
        self._push_file('agent/agent.sh', remote_agent_path + 'agent.sh')
        self._push_file('agent/auto-deploy.py', remote_agent_path + 'auto-deploy.py')
        self._push_file('agent/restore_pica8_config.sh', remote_agent_path + 'restore_pica8_config.sh')
        self._push_file('agent/status_collector.py', remote_agent_path + 'status_collector.py')
        
        self._push_file('agent/enable_switch_vpn.sh', remote_agent_path + 'enable_switch_vpn.sh')
        self._push_file('agent/manual_deploy.sh', remote_agent_path + 'manual_deploy.sh')
        self._push_file('agent/restart_ovpn.sh', remote_agent_path + 'restart_ovpn.sh')
        self._push_file('agent/vpn_daemon.py', remote_agent_path + 'vpn_daemon.py')
        
        auto_deploy_conf = inventory.inven_db.get_switch_agent_conf(self.switch_info.sn)
        if auto_deploy_conf:
            with open('config_gen/' + self.switch_info.platform_model + '/auto-deploy.conf', 'w+') as f:
                f.write(auto_deploy_conf)
            self.add_switch_log(self.switch_info.sn, 'overwrite auto-deploy.conf from db', level='info')
        
        self._push_file('config_gen/' + self.switch_info.platform_model + '/auto-deploy.conf',
                        remote_agent_path + 'auto-deploy.conf')
    
    def _push_image(self):
        res, status = self._push_file(self.switch_platform.up_to_date_image_path, self.d_image_path, continue_at=False)
        LOG.info('push file %s res %s', self.switch_platform.up_to_date_image_path, res)
        if self.need_push_image_to_cftmp:
            self._mv_file(self.d_image_path, '/cftmp')
        # self.add_switch_log(self.switch_info.sn, 'push file %s' % self.switch_platform.up_to_date_image_path,
        #                     level='info')
        
        if self.switch_info.version < '3.0':
            if 'tar.gz' not in self.switch_platform.up_to_date_image_path:
                raise UpgradeError(self.switch_info.sn, 'switch version 2.x, picos image must be *.tar.gz')
    
    def _clear_other_image(self, image_name):
        # remove other image files
        retry_times = 0
        while retry_times < 3:
            if self.is_switch_use_mnt_path_instead_of_cftmp_when_upgrading:
                res, status = self._execute("sudo find /mnt/open -maxdepth 2 -type f | grep -E '.*\\.bin$|.*\\.tar\\.gz$'")
            else:
                res, status = self._execute("sudo find /home -maxdepth 2 -type f | grep -E '.*\\.bin$|.*\\.tar\\.gz$'")
            # for fix busybox switch, find will return 'xxx.tar.gz
            if res.startswith('\''):
                res = res[1:]
            LOG.info('images files %s', res)
            if status == C.RMA_ACTIVE and res and res != '':
                image_files = res.split('\n')
                for image_file in image_files :
                    if image_name not in image_file and (image_file.endswith('.bin') or image_file.endswith('.tar.gz')):
                        self._execute('sudo rm -f %s' % image_file)
                return
            else:
                retry_times += 1
                continue
    
    def _change_switch_flag(self, flag):
        
        change_flag = False
        retry_times = 0
        
        while not change_flag and retry_times < 3:
            
            res, status = self._execute("rm -f %s && echo '%s' > %s" % (C.FLAG_FILE, flag, C.FLAG_FILE))
            LOG.debug('re %s, status %s', res, status)
            
            try:
                res, status = self._execute("cat %s" % C.FLAG_FILE)
            except:
                pass
            LOG.debug('re %s, status %s', res, status)
            
            if flag in res:
                change_flag = True
                break
            
            retry_times += 1
        
        if not change_flag:
            raise UpgradeError(self.switch_info.sn,
                               'change flag file to %s failed' % flag)
    
    def _read_domain_name(self):
        
        retry_times = 0
        
        while retry_times < 3:
            res, status = self._execute('cat /etc/resolv.conf')
            if res:
                break
            retry_times += 1
        
        if not res:
            # raise UpgradeError(self.switch_info.sn,
            #                    "read domain name from '/etc/resolv.conf' failed")
            LOG.warning('switch %s read domain name from "/etc/resolv.conf" failed', self.switch_info.sn)
            return
        domain_name = re.findall('domain (([a-z0-9--]{1,200})\.([a-z]{2,10})(\.[a-z]{2,10})?)', res)
        if not domain_name:
            # raise UpgradeError(self.switch_info.sn, "no domain name in '/etc/resolv.conf'")
            LOG.warning('switch %s no domain name in "/etc/resolv.conf"', self.switch_info.sn)
            return
        return domain_name[0][0]
    
    def _push_domain_name(self, domain_name):
        
        change_flag = False
        retry_times = 0
        domain_path = C.BASE_DIR + 'domain'
        
        while not change_flag and retry_times < 3:
            
            res, status = self._execute("rm -f %s && echo '%s' > %s" % (domain_path, domain_name, domain_path))
            LOG.debug('re %s, status %s', res, status)
            
            try:
                res, status = self._execute("cat %s" % domain_path)
            except:
                pass
            LOG.debug('re %s, status %s', res, status)
            
            if domain_name in res:
                change_flag = True
                break
            
            retry_times += 1
        
        if not change_flag:
            raise UpgradeError(self.switch_info.sn,
                               'push domain name file to %s failed' % domain_path)
    
    def _save_config(self):
        
        changed = False
        retry_times = 0
        
        while not changed and retry_times < 3:
            
            res, status = self._execute("sudo save_config")
            LOG.debug('re %s, status %s', res, status)
            
            if status == C.RMA_ACTIVE:
                changed = True
                break
            
            retry_times += 1
        
        if not changed:
            raise UpgradeError(self.switch_info.sn, "save_config failed")
    
    def _add_file_to_backup(self, file_path):
        changed = False
        retry_times = 0
        
        while not changed and retry_times < 3:
            
            try:
                # res, status = self._execute("sudo cat %s | grep %s | wc -l" % (C.BACKUP_FILE, C.FLAG_FILE))
                res, status = self._execute("sudo cat %s | grep %s | wc -l" % (C.BACKUP_FILE, file_path))
                LOG.debug('re %s, status %s', res, status)
                if res and int(res) > 1:
                    changed = True
                    break
            except:
                pass
            
            self._execute("sudo chmod 666 %s" % C.BACKUP_FILE)
            res, status = self._execute("sudo echo '%s' >> %s && sudo echo '%s' >> %s" % (
                file_path, C.BACKUP_FILE, file_path, C.BACKUP_FILE))
            LOG.debug('re %s, status %s', res, status)
            retry_times += 1
        
        if not changed:
            raise UpgradeError(self.switch_info.sn, "add '%s' to %s failed" % (file_path, C.BACKUP_FILE))
    
    @staticmethod
    def _check_switch_space(avail_info, min):
        number = 0
        if '.' in avail_info:
            number = float(avail_info[:-1])
        else:
            number = int(avail_info[:-1])
        
        if 'K' in avail_info:
            number = number / 1024
        elif 'M' in avail_info:
            pass
        elif 'G' in avail_info:
            number = number * 1024
        else:
            return False
        
        if number < min:
            return False
        return True
    
    def wait_switch(self, timeout=C.REBOOT_SLEEP_TIME):
        LOG.info('%s: start to wait switch %s seconds', self.switch_info.sn, timeout)
        time.sleep(timeout)
        LOG.info('%s wake up', self.switch_info.sn)
        fail_count = 0
        while fail_count < 3:
            if self.ensure_switch_ready(retry_times=10):
                return True
            
            fail_count += 1
        
        return False
    
    def ensure_switch_ready(self, retry_interval=30, retry_times=-1):
        while retry_times != 0:
            res, status = self.__get_execute_new('version')
            if status == C.RMA_ACTIVE:
                return True
            retry_times -= 1
            time.sleep(retry_interval)
        
        return False
    
    def handle_failed(self, msg, flag):
        LOG.error('switch %s %s', self.switch_info.sn, msg)
        monitor_db.add_event(self.switch_info.sn, 'error', msg)
        self.add_switch_log(self.switch_info.sn, msg, level='error')
        if self.task_name:
            db.update_model(AutomationTask, filters={'task_name': [self.task_name]},
                            updates={AutomationTask.task_status: 'failure'})
        if flag:
            db.update_model(self.switch_model, filters={'sn': [self.switch_info.sn]},
                            updates={self.switch_model.upgrade_status: C.outswitch_status.UPGRADE_FAILED})
    
    def add_switch_log(self, *args, **kwargs):
        db.add_switch_log(report_action='upgrade', *args, **kwargs)

    def check_image_by_size(self):
        if not self.ssh_session or self.ssh_session.closed:
            self.init_ssh_session()

        if self.is_switch_use_mnt_path_instead_of_cftmp_when_upgrading:
            self.d_image_path = "/mnt/open/" + os.path.basename(self.switch_platform.up_to_date_image_path)
        else:
            self.d_image_path = C.BASE_DIR + os.path.basename(self.switch_platform.up_to_date_image_path)
        current_version, version, revision = self.get_switch_reversion()
        revision = revision.split('-')[0]
        if revision == self.switch_platform.up_to_date_version.split('/')[1]:
            self.group_format_log(self.switch_info.sn,
                                  'version is current, %s, no need to push' % current_version, level="info")
            return

        # if parse version error will continue push image, version 9.8.7 is a special version, will continue push image
        if utils.is_image_name_comply_with_release_rules(self.switch_platform.up_to_date_image_path.split('/')[0]) and version != '9.8.7':
            try:
                if is_version_gt(version, self.switch_platform.up_to_date_version.split('/')[0]):
                    self.group_format_log(self.switch_info.sn,
                                        'The version to be pushed is lower than the existing version, switch version %s, current version is %s, no need to push'
                                        % (current_version, self.switch_platform.up_to_date_version), level="info")
                    LOG.info('The version to be upgraded is lower than the existing version, switch version %s, current version is %s'
                                        % (current_version, self.switch_platform.up_to_date_version))
                    return
            except:
                LOG.warning('switch %s parse version failed, current version %s, up to date version %s', self.switch_info.sn, version, self.switch_platform.up_to_date_version)

        if self.is_switch_use_mnt_path_instead_of_cftmp_when_upgrading:
            cftmp_image_path = "/mnt/open/" + os.path.basename(self.switch_platform.up_to_date_image_path)
        else:
            cftmp_image_path = "/cftmp/" + os.path.basename(self.switch_platform.up_to_date_image_path)
        res, status = conn_client.interactive_shell_linux_with_conn(self.ssh_session,
                                                                    'sudo ls -l %s' % cftmp_image_path)
        if 'No such' not in res:
            self.group_format_log(self.switch_info.sn, 'find image file in /mnt/open/..' if self.is_switch_use_mnt_path_instead_of_cftmp_when_upgrading else 'find image file in /cftmp/..', level="info")
            # check image & md5 file in /cftmp/
            if self.switch_platform.up_to_date_image_md5_path:
                if self.is_switch_use_mnt_path_instead_of_cftmp_when_upgrading:
                    res, status = self._push_file(self.switch_platform.up_to_date_image_md5_path, self.d_image_md5_path, md5_file_tag=True)
                    LOG.info('push md5 file %s res %s, status %s', self.switch_platform.up_to_date_image_md5_path, res,
                             status)
                    md5_file_name = os.path.basename(self.switch_platform.up_to_date_image_md5_path)
                    res, status = self._execute('cd /mnt/open/ && md5sum -c %s' % md5_file_name)
                else:
                    res, status = self._push_file(self.switch_platform.up_to_date_image_md5_path, self.d_image_md5_path, md5_file_tag=True)
                    LOG.info('push md5 file %s res %s, status %s', self.switch_platform.up_to_date_image_md5_path, res,
                             status)
                    self._mv_file(self.d_image_md5_path, "/cftmp")
                    md5_file_name = os.path.basename(self.switch_platform.up_to_date_image_md5_path)
                    res, status = self._execute('cd /cftmp && md5sum -c %s' % md5_file_name)
                if status == C.RMA_ACTIVE and 'OK' in res:
                    self.group_format_log(self.switch_info.sn, 'check image md5 success, no need push',
                                          level="info")
                    self.check_image_flag = 'SAME'
                    self.need_mv_image = False
                else:
                    self.group_format_log(self.switch_info.sn, 'check image md5 failed, need to push', level="info")
                    self.check_image_flag = True
                    if not self.is_switch_use_mnt_path_instead_of_cftmp_when_upgrading:
                        self.need_push_image_to_cftmp = True
                    return
            else:
                self.group_format_log(self.switch_info.sn, 'image exist & check image integrity...', level='info')
                cmd = 'sudo python -c "%s"' % "import os; print(os.path.getsize('%s'))" % cftmp_image_path
                res, status = conn_client.interactive_shell_linux_with_conn(self.ssh_session, cmd)
                if status == C.RMA_ACTIVE:
                    upload_size = os.path.getsize(
                        os.path.join(C.AMPCON_BASE_DIR, self.switch_platform.up_to_date_image_path))
                    if str(upload_size) == res:
                        self.group_format_log(self.switch_info.sn, 'image is complete, no need to push.', level='info')
                        self.check_image_flag = 'SAME'
                        self.need_mv_image = False
                    else:
                        self.group_format_log(self.switch_info.sn,
                                              'image is not complete, current image size: %s, upload image size: %s  need to push' % (
                                                  res, upload_size), level='info')
                        self.check_image_flag = True
                        if not self.is_switch_use_mnt_path_instead_of_cftmp_when_upgrading:
                            self.need_push_image_to_cftmp = True
                        
                else:
                    self.add_switch_log(self.switch_info.sn, 'get image file size failed', level='info')
    
        else:
            # check image & md5 file in /home/<USER>
            res, status = conn_client.interactive_shell_linux_with_conn(self.ssh_session,
                                                                        'sudo ls -l %s' % self.d_image_path)
            if 'No such' not in res:
                self.group_format_log(self.switch_info.sn, 'find image file in /mnt/open/..' if self.is_switch_use_mnt_path_instead_of_cftmp_when_upgrading else 'find image file in /home/<USER>/..', level="info")
                if self.switch_platform.up_to_date_image_md5_path:
                    res, status = self._push_file(self.switch_platform.up_to_date_image_md5_path, self.d_image_md5_path, md5_file_tag=True)
                    LOG.info('push md5 file %s res %s, status %s', self.switch_platform.up_to_date_image_md5_path, res,
                             status)
                    md5_file_name = os.path.basename(self.switch_platform.up_to_date_image_md5_path)
                    if self.is_switch_use_mnt_path_instead_of_cftmp_when_upgrading:
                        res, status = self._execute('cd /mnt/open && md5sum -c %s' % md5_file_name)
                    else:
                        res, status = self._execute('cd /home/<USER>' % md5_file_name)
                    if status == C.RMA_ACTIVE and 'OK' in res:
                        self.group_format_log(self.switch_info.sn, 'check image md5 success, no need push',
                                              level="info")
                        self.check_image_flag = 'SAME'
                    else:
                        self.group_format_log(self.switch_info.sn, 'check image md5 failed, need to push', level="info")
                        self.check_image_flag = True
                        return
                else:
                    self.group_format_log(self.switch_info.sn, 'image exist & check image integrity...', level='info')
                    cmd = 'sudo python -c "%s"' % "import os; print(os.path.getsize('%s'))" % self.d_image_path
                    res, status = conn_client.interactive_shell_linux_with_conn(self.ssh_session, cmd)
                    if status == C.RMA_ACTIVE:
                        upload_size = os.path.getsize(
                            os.path.join(C.AMPCON_BASE_DIR, self.switch_platform.up_to_date_image_path))
                        if str(upload_size) == res:
                            self.group_format_log(self.switch_info.sn, 'image is complete, no need to push.',
                                                  level='info')
                            self.check_image_flag = 'SAME'
                        else:
                            self.group_format_log(self.switch_info.sn,
                                                  'image is not complete, current image size: %s, upload image size: %s  need to push' % (
                                                      res, upload_size), level='info')
                            self.check_image_flag = True
                    else:
                        self.add_switch_log(self.switch_info.sn, 'get image file size failed', level='info')
            else:
                self.group_format_log(self.switch_info.sn, 'check image is not exist, need to push', level='info')
                self.check_image_flag = True

    def push_image_by_check(self):
        if self.check_image_flag and self.check_image_flag != 'SAME':
            self._clear_other_image(os.path.basename(self.switch_platform.up_to_date_image_path))
            self._push_image()
    
    def group_format_log(self, sn, msg, level):
        if self.group_name:
            self.add_switch_log(sn, '[%s %s] %s %s' % (self.group_name, self.job_time, sn, msg), level=level)
        else:
            self.add_switch_log(sn, msg, level=level)


@my_celery_app.task(name="push_image", base=AmpConBaseTask)
def push_image(*args, **kwargs):
    service = UpgradeService(*args, **kwargs)
    try:
        if not service.check_image_flag:
            service.check_image_by_size()
        service.push_image_by_check()
    except Exception as e:
        LOG.exception(e)
        service.handle_failed('Push image failed, ' + str(e), False)
        raise Exception(str(e))
    finally:
        if service.ssh_session:
            service.ssh_session.close()


@my_celery_app.task(name="start_upgrade_service", base=AmpConBaseTask)
def start(*args, **kwargs):
    # task_type = conform_task_type()
    # if task_type != 'all':
    #     if task_type == 'deploy' and kwargs['outer']:
    #         return
    #
    #     if task_type == 'import' and not kwargs['outer']:
    #         return
    LOG.error('start upgrade service')
    service = UpgradeService(*args, **kwargs)
    try:
        service.start()
        if len(args) >= 10 and args[9]:
            automation_db.update_switch_common_job(args[9], state="success", log='', start_time=None, end_time=int(datetime.datetime.now().timestamp()))
    except Exception as e:
        if len(args) >= 10 and args[9]:
            automation_db.update_switch_common_job(args[9], state="failed", log='', start_time=None, end_time=int(datetime.datetime.now().timestamp()))
        LOG.error(' upgrade service error')
        LOG.exception(e)
        service.handle_failed('Upgrade Failed, ' + str(e), True)
        raise Exception(str(e))
    finally:
        LOG.error('end upgrade service')
        if service.ssh_session:
            service.ssh_session.close()


if __name__ == '__main__':
    from server import cfg
    from server.db.models.automation import AnsibleJobResult
    
    mgt_ip = '***********'
    user = 'admin'
    password = 'pica8'
    app_user = {'name': 'admin', 'passwd': 'admin'}
    switch = 'EC1815000436'
    switch_platform = 'AS7726_32X'
    tmp_files = []
    upgrade_service = UpgradeService(mgt_ip, user, password, app_user['name'],
                                     app_user['passwd'], switch, switch_platform, tmp_files)
    
    print(upgrade_service.check_image_by_size())
    
    # print upgrade_service._execute('version')
    # time.sleep(10)
    # print upgrade_service._execute('version')
    # time.sleep(10)
    # print upgrade_service._execute('version')
    # time.sleep(10)
    # print upgrade_service._execute('version')
