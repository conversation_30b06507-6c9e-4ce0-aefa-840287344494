import os
import copy
import json
import logging
import traceback
import requests
import uuid
from jinja2 import Environment, FileSystemLoader
from datetime import datetime, timedelta
from pygnmi.client import gNMIclient
from concurrent.futures import ThreadPoolExecutor, as_completed
from flask import Blueprint, render_template, jsonify, request, flash, redirect, send_file
from sqlalchemy import or_, and_, func, desc, asc

from server.db.redis_common import RedisQueue
from server.db.models.otn import OtnDeviceBasic
from server.db.models.monitor import Event, OperationLog, ModulesLink, DDMEvents
from server.db.models.monitor import monitor_db
from server.db.models.inventory import SystemConfig, License, Switch, MonitorTarget, Topology, TopologyEdge, \
    TopologyNode, Fabric, ClientDeviceInfo, AssociationFabric, Site, AssociationSite
from server.db.models.inventory import inven_db, PushConfigTask, PushConfigTaskDetails, Fabric
from server.db.models.dc_blueprint import DCFabricTopologyN<PERSON>, DCFabricTopology
from server.db.models.automation import Ansible<PERSON>ev<PERSON>, HostInfo
from server.db.models.user import User
from server.db.models.user import user_db
from server.util import utils
from server.util import ssh_util as conn_client
from server import cfg, constants
from server.util.permission import admin_permission
from server.util.yaml_util import YAMLEditor
from server.util.encrypt_util import encrypt_with_static_key
from server.util import prometheus_util
from server.util.terminal_identification_util import get_organization_name_by_mac, format_mac_address
from server.util.ddm_msg_processor import process_ddm_msg
import re


new_monitor_mold = Blueprint("new_monitor_mold", __name__, template_folder='templates')

LOG = logging.getLogger(__name__)

timestamp_format = '%Y-%m-%d %H:%M'

roce_nvidia_metricName_dict = {
    "prioX_rx_bytes": 'node_ethtool_received_prio{prioX}_bytes',
    "prioX_rx_packets": 'node_ethtool_received_prio{prioX}_packets',
    "prioX_rx_pause": 'node_ethtool_received_prio{prioX}_pause',
    "prioX_tx_bytes": 'node_ethtool_transmitted_prio{prioX}_bytes',
    "prioX_tx_packets": 'node_ethtool_transmitted_prio{prioX}_packets',
    "prioX_tx_pause": 'node_ethtool_transmitted_prio{prioX}_pause',
    "rx_ecn_marked_pkts": 'node_ethtool_received_ecn_mark'
}

roce_broadcom_metricName_dict = {
    "prioX_rx_bytes": 'node_ethtool_received_bytes_pri{prioX}',
    "prioX_rx_packets": 'node_ethtool_received_packets_pri{prioX}',
    "prioX_rx_pause": 'node_ethtool_received_pfc_ena_frames_pri{prioX}',
    "prioX_tx_bytes": 'node_ethtool_transmitted_bytes_pri{prioX}',
    "prioX_tx_packets": 'node_ethtool_transmitted_packets_pri{prioX}',
    "prioX_tx_pause": 'node_ethtool_transmitted_pfc_ena_frames_pri{prioX}',
    "rx_ecn_marked_pkts": 'roce_ecn_packets_total'
}

# @monitor_mold.route('/server/status', methods=['GET'])
# def server_status():
#     ram_status = utils.get_memory_state()
#     ram_status.pop('free')
#     return jsonify({
#         'cpu': utils.get_CPU_state(),
#         'memory': ram_status,
#         'disk': utils.get_disk_satate()
#     })


# @monitor_mold.route('/server/available')
# def server_available():
#     try:
#         user_db.get_collection(User)
#         return 'Application and database are up and functioning'
#     except:
#         return 'db error'


# @monitor_mold.route('/switch_alarm')
# def switch_alarm():
#     # db_session = db.get_session()
#     # all_event = db_session.query(Event).filter(Event.status == 'unread').all()
#     active = ('monitor', 'switch_alarm')
#     return render_template('monitor/switch_alarm.html', active=active)


# @monitor_mold.route('/switch_alarm/data')
# def get_alarm_data():
#     search_value = request.args.get('search[value]')

#     rule = [Event.sn.in_(list(map(lambda x: x.sn, utils.query_switch())))]

#     status_filter = Event.status.in_(['unread'])

#     if search_value and search_value != '':
#         search_value = '%' + search_value + '%'
#         rule.append(or_(*[Event.sn.like(search_value),
#                      Event.type.like(search_value),
#                      Event.msg.like(search_value)]))
#     rule = and_(*rule)
#     return utils.page_helper(request.args, Event, rule=rule, extra_filter=status_filter)


# @monitor_mold.route('/read/<int:event_id>')
# def switch_alarm_read(event_id):
#     monitor_db.update_event_status(event_id, 'read')
#     return 'ok'


# @monitor_mold.route('/all_message')
# def switch_alarm_all_message():
#     active = ('monitor', 'switch_alarm')
#     return render_template('monitor/switch_alarm_all_data.html', active=active)


# @monitor_mold.route('/del/<int:event_id>')
# @admin_permission.require(http_exception=403)
# def switch_alarm_del(event_id):
#     inven_db.delete_collection(Event, filters={'id': [event_id]})
#     return 'ok'


# @monitor_mold.route('/all_message/data')
# def get_alarm_all_data():
#     search_value = request.args.get('search[value]')
#     rule = [Event.sn.in_(list(map(lambda x: x.sn, utils.query_switch())))]

#     if search_value and search_value != '':
#         search_value = '%' + search_value + '%'
#         rule.append(or_(*[Event.sn.like(search_value),
#                      Event.type.like(search_value),
#                      Event.msg.like(search_value)]))
#     rule = and_(*rule)
#     return utils.page_helper(request.args, Event, rule=rule)


# @monitor_mold.route('/operation_log/view')
# def operation_log_view():
#     active = ('monitor', 'operation_log')
#     return render_template('monitor/operation_log.html', active=active)


# @monitor_mold.route('/operation_log/data')
# def operation_log():
#     search_value = request.args.get('search[value]')
#     rule = None
#     if search_value and search_value != '':
#         search_value = '%' + search_value + '%'
#         rule = or_(*[OperationLog.user.like(search_value),
#                      OperationLog.path.like(search_value),
#                      OperationLog.content_original.like(search_value),
#                      OperationLog.status.like(search_value)])

#     return utils.page_helper(request.args, OperationLog, rule=rule)


# @monitor_mold.route('/push_config_logs/view')
# def push_config_tasks_view():
#     active = ('monitor', 'push_config_logs')
#     return render_template('monitor/push_config_tasks.html', active=active)


# @monitor_mold.route('/all_config_logs')
# def all_config_tasks():
#     active = ('monitor', 'push_config_logs')
#     return render_template('monitor/push_config_tasks_all.html', active=active)


# @monitor_mold.route('/push_config_tasks/data')
# def push_config_tasks():
#     search_value = request.args.get('search[value]')
#     rule = None
#     status_filter = PushConfigTask.read_tag.in_([0])
#     if search_value and search_value != '':
#         search_value = '%' + search_value + '%'
#         rule = or_(*[PushConfigTask.user.like(search_value),
#                      PushConfigTask.path.like(search_value),
#                      PushConfigTask.content_original.like(search_value),
#                      PushConfigTask.status.like(search_value)])

#     return utils.page_helper(request.args, PushConfigTask, rule=rule, extra_filter=status_filter)


@new_monitor_mold.route('/push_config_tasks/data_all', methods=['POST'])
def push_config_all_tasks():
    # search_value = request.args.get('search[value]')
    # rule = None
    # if search_value and search_value != '':
    #     search_value = '%' + search_value + '%'
    #     rule = or_(*[PushConfigTask.user.like(search_value),
    #                  PushConfigTask.path.like(search_value),
    #                  PushConfigTask.content_original.like(search_value),
    #                  PushConfigTask.status.like(search_value)])

    # # return utils.page_helper(request.args, PushConfigTask, rule=rule)
    # db_session = inven_db.get_session()
    # # local_licenses = db_session.query(PushConfigTask).filter(inventory.License.local_lic.isnot(None))
    page_num, page_size, total_count, query_pushconfig = utils.query_helper(PushConfigTask)
    return jsonify({"data": [task.make_dict() for task in query_pushconfig], "page": page_num,
                    "pageSize": page_size,
                    "total": total_count,
                    "status": 200})


@new_monitor_mold.route('/show_switch_ret/<string:task_name>/<string:sn>')
def show_switch_ret(sn, task_name):
    db_session = monitor_db.get_session()
    switch_ret_obj = db_session.query(PushConfigTaskDetails).filter(PushConfigTaskDetails.name == task_name,
                                                                    PushConfigTaskDetails.sn == sn).first()
    return jsonify({"status": 200, "data": switch_ret_obj.push_ret})


@new_monitor_mold.route('/get_config_content/<string:task_name>')
def get_config_content(task_name):
    db_session = monitor_db.get_session()
    config_content_obj = db_session.query(PushConfigTask).filter(PushConfigTask.task_name == task_name).first()
    return jsonify({"status": 200, "data": config_content_obj.task_content})


@new_monitor_mold.route('/del_push_config/<string:task_name>')
def del_push_config(task_name):
    db_session = monitor_db.get_session()
    details_to_delete = db_session.query(PushConfigTaskDetails).filter(PushConfigTaskDetails.name == task_name).all()
    if details_to_delete:
        for detail in details_to_delete:
            db_session.delete(detail)
    db_session.query(PushConfigTask).filter(PushConfigTask.task_name == task_name).delete()
    return jsonify({"status": 200})


@new_monitor_mold.route('/read_config/<int:task_id>')
def read_push_config(task_id):
    db_session = monitor_db.get_session()
    db_session.query(PushConfigTask).filter(PushConfigTask.id == task_id).update({"read_tag": 1})
    return jsonify({"status": 200})


# @monitor_mold.route('/read_all_config')
# def read_all_config():
#     db_session = monitor_db.get_session()
#     db_session.query(PushConfigTask).update({"read_tag": 1})
#     return 'ok'


@new_monitor_mold.route('/get_push_config_task_items/<task_name>', methods=['GET'])
def get_push_config_task_items(task_name):
    db_session = monitor_db.get_session()
    push_config_task_details = db_session.query(PushConfigTaskDetails).filter(
        PushConfigTaskDetails.name == task_name).all()
    config_data_list = list()
    for dt in push_config_task_details:
        config_data = dict()
        config_data['sn'] = dt.sn
        config_data['status'] = dt.push_status
        config_data['push_time'] = dt.push_time
        config_data_list.append(config_data)
    return jsonify({"status": 200, "data": config_data_list})


# @monitor_mold.route('/operation_log/export_backend_log')
# def export_backend_log():
#     logPath = "/var/log/automation/gunicorn_app_server.log"
#     return send_file(logPath, as_attachment=True, download_name='system.log')

DATE_FORMAT = '%Y-%m-%d %H:%M:%S'


@new_monitor_mold.route('/get_switch_alarm', methods=['POST'])
def get_switch_alarm():
    return get_common_switch_alarm()


@new_monitor_mold.route('/get_history_switch_alarm', methods=['POST'])
def get_history_switch_alarm():
    return get_common_switch_alarm("history")


def get_common_switch_alarm(tag=None):
    info = {}
    try:
        data = request.get_json()
        start_time = data.get("startTime", "")
        end_time = data.get("endTime", "")
        session = monitor_db.get_session()
        FMT_list = session.query(OtnDeviceBasic.ip).filter(OtnDeviceBasic.model == "FMT").all()
        event_objs = session.query(Event).filter(Event.type != "info")
        fmt_list = [ip[0] for ip in FMT_list]
        if start_time:
            start_time = datetime.fromtimestamp(start_time).strptime(DATE_FORMAT)
            event_objs = session.query(Event).filter(Event.create_time >= start_time)

        if end_time:
            end_time = datetime.fromtimestamp(end_time).strptime(DATE_FORMAT)
            event_objs = event_objs.filter(Event.create_time <= end_time).all()

        if tag == "history":
            event_objs = event_objs.filter(Event.status != "unread")
        else:
            event_objs = event_objs.filter(Event.status == "unread")

        warn_level = {
            "warn": "MAJOR",
            "error": "CRITICAL",
            "info": "WARNING"
        }

        resp = list()
        for event_obj in event_objs:
            ret = dict()
            if event_obj.sn in fmt_list:
                ret["ne_id"] = f"{event_obj.sn}"
                ret["data"] = dict()
                ret["data"]["id"] = event_obj.id
                ret["data"]["state"] = dict()
                ret["data"]["state"]["resource"] = "FMT"
                ret["data"]["state"]["deviceType"] = 3
            else:
                ret["ne_id"] = f"switch:{event_obj.id}:{event_obj.sn}"
                ret["data"] = dict()
                ret["data"]["id"] = event_obj.id
                ret["data"]["state"] = dict()
                ret["data"]["state"]["resource"] = "switch"
                ret["data"]["state"]["deviceType"] = 1

            ret["data"]["state"]["text"] = event_obj.msg
            ret["data"]["state"]["time-created"] = str(
                int(event_obj.create_time.timestamp() * 10 ** 9)) if event_obj.create_time else ""
            ret["data"]["state"]["time-cleared"] = str(
                int(event_obj.modified_time.timestamp() * 10 ** 9)) if event_obj.modified_time else ""
            ret["data"]["state"]["time-acknowledged"] = str(
                int(event_obj.operator_time.timestamp() * 10 ** 9)) if event_obj.operator_time else ""
            ret["data"]["state"]["operator-name"] = event_obj.operator_name
            ret["data"]["state"]["operator-text"] = event_obj.operator_text
            ret["data"]["state"]["severity"] = warn_level.get(event_obj.type, "")
            ret["data"]["state"]["alarm-abbreviate"] = "/"
            ret["data"]["state"]["alarm-count"] = event_obj.count
            resp.append(ret)
    except Exception as e:
        LOG.error(f"get_switch_alarm error: {traceback.format_exc()}")
        info = {"status": 500, "msg": "Get_switch_alarm error"}
    else:
        info = {"status": 200, "data": resp}
    finally:
        return jsonify(info)


@new_monitor_mold.route('/clear_switch_history_alarm', methods=['post'])
def clear_history_switch_alarm():
    info = request.get_json()
    id_list = info.get("idList", '')
    fail_list = []
    success_list = []
    for item in id_list:
        alarm_data_by_id = monitor_db.get_model(Event, filters={'id': [item]})
        if not alarm_data_by_id:
            fail_list.append(item)
        else:
            success_list.append(item)
    if len(success_list) > 0:
        monitor_db.delete_collection(Event, filters={'id': success_list})
        if len(fail_list) > 0:
            info = {"status": 200, "msg": "Success clear {} switch history alarm, but {} failed, maybe no switch "
                                          "history alarm".format(len(success_list), len(fail_list))}
        else:
            info = {"status": 200, "msg": "Success clear {} switch history alarm".format(len(success_list))}
    else:
        LOG.error(f"clear_switch_history_alarm error: {traceback.format_exc()}")
        info = {"status": 500, "msg": "No switch history alarm with id {}".format(fail_list)}
    return jsonify(info)


@new_monitor_mold.route('/ack_switch_alarm', methods=['POST'])
def ack_switch_alarm():
    info = {}
    try:
        data = request.get_json()
        event_id = data.get("eventId", "")
        operator_name = data.get("operatorName", "")
        operator_text = data.get("operatorText", "")
        session = monitor_db.get_session()
        session.query(Event).filter(Event.id == event_id).update(
            {"status": "read", "operator_name": operator_name, "operator_text": operator_text,
             "operator_time": datetime.now()})
    except Exception as e:
        LOG.error(f"ack_switch_alarm error: {traceback.format_exc()}")
        info = {"status": 500, "msg": "Ack_switch_alarm error"}
    else:
        info = {"status": 200, "msg": "Ack_switch_alarm success"}
    finally:
        return jsonify(info)


@new_monitor_mold.route('/get_all_alarm', methods=['post'])
def get_all_alarm():
    info = {}
    data = request.get_json()
    try:
        session = monitor_db.get_session()
        switches = utils.query_switch().all()
        available_switches_sn = list(set(sw.sn for sw in switches))
        available_switches_ip = list(set(sw.mgt_ip for sw in switches if sw.mgt_ip))
        pre_query = session.query(Event).filter(
            or_(
                Event.status == "read",
                Event.status == "unread"
            ),
            or_(
                Event.sn.in_(available_switches_sn),
                Event.sn.in_(available_switches_ip)
            )
        )
        filter_fields = data.get("filterFields", [])
        modified_time_filter = next((f for f in filter_fields if f["field"] == "modified_time"), None)

        if modified_time_filter and modified_time_filter["filters"]:
            start_time, end_time = modified_time_filter["filters"][0]["value"]
            modified_time_filter["filters"] = []
            if start_time and end_time:
                pre_query = pre_query.filter(Event.modified_time.between(start_time, end_time))
        page_num, page_size, total_count, alarms_list = utils.query_helper(Event, pre_query=pre_query)
    except Exception as e:
        info = {"status": 500, "msg": "Get_all_alarm error"}
    else:
        info = {"data": [pk.make_dict() for pk in alarms_list],
                "page": page_num,
                "pageSize": page_size,
                "total": total_count,
                "status": 200}
    finally:
        return jsonify(info)


@new_monitor_mold.route('/update_alarm_status', methods=['post'])
def update_alarm_status():
    info = {}
    data = request.get_json()
    try:
        alarm_id = data.get("id", "")
        db_session = monitor_db.get_session()
        alarm_event = db_session.query(Event).filter(Event.id == alarm_id)
        if alarm_event:
            monitor_db.update_event_status(alarm_id, "read")
            info = {"status": 200, "msg": "Update alarm status success"}
        else:
            info = {"status": 500, "msg": "Update alarm status failed"}
    except Exception as e:
        info = {"status": 500, "msg": "Update alarm status failed"}
        LOG.error(f"update_alarm_status error: {traceback.format_exc()}")
    finally:
        return jsonify(info)


@new_monitor_mold.route('/get_unread_alarm', methods=['post'])
def get_unread_alarm():
    info = {}
    data = request.get_json()
    try:
        session = monitor_db.get_session()
        switches = utils.query_switch().all()
        available_switches_sn = list(set(sw.sn for sw in switches))
        available_switches_ip = list(set(sw.mgt_ip for sw in switches if sw.mgt_ip))
        pre_query = session.query(Event).filter(
            Event.status == "unread",
            or_(
                Event.sn.in_(available_switches_sn),
                Event.sn.in_(available_switches_ip)
            )
        )

        filter_fields = data.get("filterFields", [])
        modified_time_filter = next((f for f in filter_fields if f["field"] == "modified_time"), None)

        if modified_time_filter and modified_time_filter["filters"]:
            start_time, end_time = modified_time_filter["filters"][0]["value"]
            modified_time_filter["filters"] = []
            if start_time and end_time:
                pre_query = pre_query.filter(Event.modified_time.between(start_time, end_time))
        critical_count = pre_query.filter(Event.type == "error").count()
        warning_count = pre_query.filter(Event.type == "warn").count()
        info_count = pre_query.filter(Event.type == "info").count()
        page_num, page_size, total_count, alarms_list = utils.query_helper(Event, pre_query=pre_query)
    except Exception as e:
        LOG.error(f"get_unread_alarm error: {traceback.format_exc()}")
        info = {"status": 500, "msg": "Get_unread_alarm error"}
    else:
        info = {"data": [pk.make_dict() for pk in alarms_list],
                "page": page_num,
                "pageSize": page_size,
                "total": total_count,
                "criticalCount": critical_count,
                "warningCount": warning_count,
                "infoCount": info_count,
                "status": 200}
    finally:
        return jsonify(info)


@new_monitor_mold.route("/delete_alarm/<int:alarm_id>", methods=['POST'])
def delete_alarm(alarm_id):
    db_session = monitor_db.get_session()
    try:
        alarm = db_session.query(Event).filter_by(id=alarm_id).first()
        if not alarm:
            info = {"status": 404, "msg": "Alarm not found"}
            raise ValueError(info["msg"])
        with db_session.begin(subtransactions=True):
            db_session.delete(alarm)
            db_session.commit()
        info = {"status": 200, "msg": "Delete Alarm Successful!"}
    except Exception as e:
        print(str(e))
        db_session.rollback()
        LOG.error(traceback.format_exc())
        info = {"status": 500, "msg": "Failed to delete alarm: {}".format(str(e))}
    finally:
        db_session.close()
    return jsonify(info)


@new_monitor_mold.route("/alert_log", methods=['POST', 'GET'])
def alert_log():
    content = request.data
    data = json.loads(content)
    # db_session = monitor_db.get_session()
    hasThreshold = data.get("hasThreshold", "")
    if isinstance(hasThreshold, bool):
        process_ddm_msg(data)
    else:
        redis_queue = RedisQueue('alert_queue')
        for alert in data.get("alerts", []):
            LOG.info("put alert to redis: %s", alert)
            redis_queue.put(alert)
        # monitor_db.add_event(alert["labels"]["target"], alert["labels"]["severity"],
        #                      alert["annotations"]["description"], db_session)
        # email_handler(alert, db_session)
    # db_session.close()
    return jsonify({"status": 200})


# Prometheus
@new_monitor_mold.route('/get_montior_target', methods=['GET'])
def get_montior_target():
    try:
        session_query = monitor_db.get_session()
        db_session = inven_db.get_session()

        monitor_query = session_query.query(MonitorTarget).filter(MonitorTarget.device_type == 1).all()
        res = []
        for target in monitor_query:
            config = db_session.query(SystemConfig).filter(SystemConfig.id == target.switch.system_config_id).first()
            if config is None:
                continue
            if target.switch.mgt_ip and target.switch.status in [constants.SwitchStatus.PROVISIONING_SUCCESS,
                                                                 constants.SwitchStatus.IMPORTED]:
                info = {
                    "name": target.sn,
                    "address": target.switch.mgt_ip + ":" + str(target.port),
                    "username": config.switch_op_user,
                    "password": encrypt_with_static_key(config.switch_op_password),
                    "enable": target.enable
                }
                res.append(info)
        msg = {'status': 200, 'data': res}
        return jsonify(msg)
    except Exception as e:
        LOG.error(traceback.format_exc())
        msg = {'info': 'Failed to get monitor target', 'status': 500, 'data': []}
        return jsonify(msg)


@new_monitor_mold.route('/get_snmp_target', methods=['GET'])
def get_snmp_target():
    #todo
    pass


@new_monitor_mold.route('/add_topology', methods=['POST'])
@admin_permission.require(http_exception=403)
def add_topology():
    try:
        data = request.get_json()
        topology_name = data.get("name")
        description = data.get("description")
        if topology_name == "" or len(topology_name) > 32 or " " in topology_name:
            msg = {'info': 'Topology name is invalid', 'status': 500}
            return jsonify(msg)
        if len(description) > 128:
            msg = {'info': 'Topology description is invalid', 'status': 500}
            return jsonify(msg)
        session = monitor_db.get_session()
        if session.query(Topology).filter(Topology.name == topology_name, Topology.topology_type == 'topology').first():
            msg = {'info': 'Topology name already exists', 'status': 500}
            return jsonify(msg)
        topology = Topology(name=topology_name, description=description, topology_type='topology')
        monitor_db.insert(topology, session)
        msg = {'info': 'Topology Added Successfully', 'status': 200}
        return jsonify(msg)
    except Exception as e:
        LOG.error(traceback.format_exc())
        msg = {'info': 'Topology add fail', 'status': 500}
        return jsonify(msg)


@new_monitor_mold.route('/edit_topology', methods=['POST'])
@admin_permission.require(http_exception=403)
def edit_topology():
    try:
        data = request.get_json()
        topology_id = data.get("id")
        topology_name = data.get("name")
        description = data.get("description")
        session = monitor_db.get_session()
        target_topology = session.query(Topology).filter(Topology.id == topology_id).first()
        if not target_topology:
            msg = {'info': 'Topology not exists', 'status': 500}
            return jsonify(msg)
        if len(description) > 128:
            msg = {'info': 'Topology description is invalid', 'status': 500}
            return jsonify(msg)
        if topology_name == "" or len(topology_name) > 32 or " " in topology_name:
            msg = {'info': 'Topology name is invalid', 'status': 500}
            return jsonify(msg)
        with session.begin(subtransactions=True):
            target_topology.name = topology_name
            target_topology.description = description
        msg = {'info': 'Topology save successfully', 'status': 200}
        return jsonify(msg)
    except Exception as e:
        LOG.error(traceback.format_exc())
        msg = {'info': 'Topology save fail', 'status': 500}
        return jsonify(msg)


@new_monitor_mold.route('/get_topology_list', methods=['GET'])
def get_topology_list():
    try:
        session = monitor_db.get_session()
        query = session.query(Topology).all()
        res = {
            'topology': [{"id": topology.id, "name": topology.name, "description": topology.description,
                          "isShowDefault": topology.is_show_default} for topology in query if
                         topology.topology_type == 'topology'],
            'fabric': [{"id": topology.id, "name": topology.name, "description": topology.description,
                        "isShowDefault": topology.is_show_default} for topology in query if
                       topology.topology_type == 'fabric'],
            'site': [{"id": topology.id, "name": topology.name, "description": topology.description,
                      "isShowDefault": topology.is_show_default} for topology in query if
                     topology.topology_type == 'site']
        }
        msg = {'info': 'Topology Added Successfully', 'status': 200, "data": res}
        return jsonify(msg)
    except Exception as e:
        LOG.error(traceback.format_exc())
        msg = {'info': 'Topology get fail', 'status': 500}
        return jsonify(msg)


@new_monitor_mold.route('/set_default_topology', methods=['POST'])
@admin_permission.require(http_exception=403)
def set_default_topology():
    data = request.get_json()
    session = monitor_db.get_session()
    topology_id = data.get("topologyId")
    is_show_default = data.get("isShowDefault")
    try:
        with session.begin():
            if is_show_default:
                session.query(Topology).update({"is_show_default": False})
            session.query(Topology).filter(Topology.id == topology_id).update({"is_show_default": is_show_default})
        msg = {'info': 'Topology set Successfully' if is_show_default else 'Topology unset Successfully', 'status': 200}
        return jsonify(msg)
    except Exception as e:
        session.rollback()
        LOG.error(traceback.format_exc())
        msg = {'info': 'Topology set fail', 'status': 500}
        return jsonify(msg)


@new_monitor_mold.route('/save_topology', methods=['POST'])
@admin_permission.require(http_exception=403)
def save_topology():
    data = request.get_json()
    topology_id = data.get("topologyId")
    topology_nodes = data.get("topologyNodes", None)
    topology_edges = data.get("topologyEdges", None)
    is_show_legend = data.get("isShowLegend", True)
    is_in_tree_mode = data.get("isInTreeMode", False)
    zoom = data.get("zoom", 1)
    translate_x = data.get("translateX", 1)
    translate_y = data.get("translateY", 1)

    session = monitor_db.get_session()
    topology = session.query(Topology).filter(Topology.id == topology_id).first()
    if not topology:
        msg = {'info': 'Topology not found', 'status': 500}
        return jsonify(msg)

    if is_show_legend not in [True, False]:
        msg = {'info': 'IsShowLegend is invalid', 'status': 500}
        return jsonify(msg)

    if is_in_tree_mode not in [True, False]:
        msg = {'info': 'IsInTreeMode is invalid', 'status': 500}
        return jsonify(msg)

    if str == type(zoom):
        try:
            zoom = float(zoom)
        except ValueError:
            msg = {'info': 'Zoom is invalid', 'status': 500}
            return jsonify(msg)

    if str == type(translate_x):
        try:
            translate_x = float(translate_x)
        except ValueError:
            msg = {'info': 'translateX is invalid', 'status': 500}
            return jsonify(msg)

    if str == type(translate_y):
        try:
            translate_y = float(translate_y)
        except ValueError:
            msg = {'info': 'TranslateY is invalid', 'status': 500}
            return jsonify(msg)

    try:
        with session.begin():
            topology.is_show_legend = is_show_legend
            topology.zoom = zoom
            topology.translate_x = translate_x
            topology.translate_y = translate_y
            topology.is_in_tree_mode = is_in_tree_mode
            if topology:
                # 删除原有的拓扑节点和拓扑边
                session.query(TopologyNode).filter(TopologyNode.topology_id == topology_id).delete()
                session.query(TopologyEdge).filter(TopologyEdge.topology_id == topology_id).delete()
                # 保存新的拓扑节点和拓扑边
                for node in topology_nodes:
                    new_node = TopologyNode(node_label=node["label"], topology_id=topology_id,
                                            monitor_target_id=node["monitorTargetId"], layer=node["layer"],
                                            position_x=node["positionX"], position_y=node["positionY"])
                    monitor_db.insert(new_node, session)
                # 获取新的拓扑节点label和id对应关系
                query_node = session.query(TopologyNode).filter(TopologyNode.topology_id == topology_id).all()
                node_id_map = {
                    node.monitor_target.name if node.monitor_target.device_type == 2 else node.monitor_target.sn: node.id
                    for node in query_node
                }
                for edge in topology_edges:
                    # ensure source_node_id < target_node_id
                    if node_id_map[edge["source"]] > node_id_map[edge["target"]]:
                        source_node_id = node_id_map[edge["target"]]
                        target_node_id = node_id_map[edge["source"]]
                        source_port = str(edge["targetPort"])
                        target_port = str(edge["sourcePort"])
                    else:
                        source_node_id = node_id_map[edge["source"]]
                        target_node_id = node_id_map[edge["target"]]
                        source_port = str(edge["sourcePort"])
                        target_port = str(edge["targetPort"])
                    source_node = session.query(TopologyNode).filter(TopologyNode.topology_id == topology_id) \
                        .filter(TopologyNode.id == source_node_id).first()
                    target_node = session.query(TopologyNode).filter(TopologyNode.topology_id == topology_id) \
                        .filter(TopologyNode.id == target_node_id).first()
                    new_edge = TopologyEdge(topology_id=topology_id, source_node=source_node, target_node=target_node,
                                            source_port=source_port, target_port=target_port)
                    monitor_db.insert(new_edge, session)
                # 开启节点的lldp监控        
                query_node = session.query(TopologyNode).filter(TopologyNode.topology_id == topology_id).all()
                for node in query_node:
                    if node.monitor_target.device_type == 1:
                        inven_db.update_switch_montior(node.monitor_target.sn, 1, lldp=True)
                msg = {'info': 'Topology save Successfully', 'status': 200}
            else:
                msg = {'info': 'Topology not found', 'status': 500}
        return jsonify(msg)
    except Exception as e:
        LOG.error(traceback.format_exc())
        session.rollback()
        msg = {'info': 'Topology save fail', 'status': 500}
        return jsonify(msg)


@new_monitor_mold.route('/get_topology', methods=['POST'])
def get_topology():
    try:
        data = request.get_json()
        topology_id = data.get("topologyId")
        date = data.get("date", None)
        if date:
            date = datetime.strptime(date, "%Y-%m-%d %H:%M:%S").timestamp()
        session = monitor_db.get_session()
        topology = session.query(Topology).filter(Topology.id == topology_id).first()
        if not topology:
            msg = {'info': 'Topology not found', 'status': 500}
            return jsonify(msg)
        query_node = session.query(TopologyNode).filter(TopologyNode.topology_id == topology_id).all()
        query_edge = session.query(TopologyEdge).filter(TopologyEdge.topology_id == topology_id).all()

        # 获取lldp状态，构建拓扑节点信息
        node_sn_list = []
        nodes = []
        otn_node_info = {}
        for node in query_node:
            # 暂时仅针对switch
            if node.monitor_target.device_type == 1:
                node_sn_list.append(node.monitor_target.sn)
                lldp_state = prometheus_util.query_lldp_state(node.monitor_target.sn,
                                                              node.monitor_target.switch.mac_addr, date)
                node_info = {
                    "id": node.id,
                    "label": node.node_label,
                    "switch_sn": node.monitor_target.sn,
                    "switch_id": node.monitor_target.switch.id,
                    "device_type": node.monitor_target.device_type,
                    "layer": node.layer,
                    "x": node.position_x,
                    "y": node.position_y,
                    "monitor_target_id": node.monitor_target_id,
                    "model": node.monitor_target.switch.platform_model,
                    "version": node.monitor_target.switch.version,
                    "mgt_ip": node.monitor_target.switch.mgt_ip,
                    "hwid": node.monitor_target.switch.hwid,
                    "status": node.monitor_target.switch.status,
                    "reachable_status": "online" if node.monitor_target.switch.reachable_status == 0 else "offline"
                }
                node_info.update(lldp_state)
                nodes.append(node_info)
            elif node.monitor_target.device_type == 2:
                otn_info = query_otn_platform_info(node.monitor_target.name)
                node_info = {
                    "id": node.id,
                    "label": node.node_label,
                    "ne_name": node.monitor_target.name,
                    "device_type": node.monitor_target.device_type,
                    "layer": node.layer,
                    "x": node.position_x,
                    "y": node.position_y,
                    "monitor_target_id": node.monitor_target_id,
                    "status": "online" if otn_info else "offline"
                }
                if otn_info:
                    otn_info.pop("subcomponent")
                    node_info.update(otn_info)
                    otn_node_info[node.id] = otn_info
                nodes.append(node_info)

        # 获取当前lldp neighbor信息，构建拓扑向量信息
        # 没去重 是否去重
        neighbors_state = {}
        if node_sn_list:
            neighbors_state = prometheus_util.query_lldp_neighbor_state(node_sn_list, date)

        edges_source_target_feature = {}
        # 当前neighbor新增的链路信息是否需要展示，暂未展示
        for edge in query_edge:
            # otn设备只能被动发现作为target, 但数据库中可能为source, 必须用switch sn_port作为key
            if edge.source_node.monitor_target.device_type == 2:
                source_sn = edge.target_node.monitor_target.sn
                source_port = edge.target_port
            else:
                source_sn = edge.source_node.monitor_target.sn
                source_port = edge.source_port
            key = f"{source_sn}_{source_port}"
            edge_info = {}
            neighbor = neighbors_state.get(key, {})
            if neighbor:
                # source为otn时需要用source的mac和port比较
                if edge.source_node.monitor_target.device_type == 2 and edge.source_node.monitor_target.mac.lower() == \
                        neighbor["target_mac"].lower() and edge.source_port == neighbor["target_port"]:
                    edge_info["status"] = "up"
                elif edge.target_node.monitor_target.device_type == 2 and edge.target_node.monitor_target.mac.lower() == \
                        neighbor["target_mac"].lower() and edge.target_port == neighbor["target_port"]:
                    edge_info["status"] = "up"
                elif edge.target_node.monitor_target.switch.mac_addr.lower() == neighbor[
                    "target_mac"].lower() and edge.target_port == neighbor["target_port"]:
                    edge_info["status"] = "up"
                else:
                    edge_info["status"] = "down"
            else:
                # 当前lldp neighbor中不存在这条
                edge_info["status"] = "down"
            if frozenset([edge.topology_id, edge.target_id, edge.source_id]) in edges_source_target_feature.keys():
                if edge_info["status"] != \
                        edges_source_target_feature[frozenset([edge.topology_id, edge.target_id, edge.source_id])][
                            "status"]:
                    edges_source_target_feature[frozenset([edge.topology_id, edge.target_id, edge.source_id])][
                        "status"] = "mixed"
                edges_source_target_feature[frozenset([edge.topology_id, edge.target_id, edge.source_id])][
                    "port_info"].append(
                    {"source_port": edge.source_port, "target_port": edge.target_port, "status": edge_info["status"]}
                )
            else:
                edges_source_target_feature[frozenset([edge.topology_id, edge.target_id, edge.source_id])] = {
                    "source": edge.source_id,
                    'source_sn': otn_node_info[edge.source_id].get("sn",
                                                                   None) if edge.source_node.monitor_target.device_type == 2 else edge.source_node.monitor_target.sn,
                    "source_label": edge.source_node.node_label,
                    "target": edge.target_id,
                    'target_sn': otn_node_info[edge.target_id].get("sn",
                                                                   None) if edge.target_node.monitor_target.device_type == 2 else edge.target_node.monitor_target.sn,
                    "target_label": edge.target_node.node_label,
                    "source_mac_addr": edge.source_node.monitor_target.mac if edge.source_node.monitor_target.device_type == 2 else edge.source_node.monitor_target.switch.mac_addr,
                    "target_mac_addr": edge.target_node.monitor_target.mac if edge.target_node.monitor_target.device_type == 2 else edge.target_node.monitor_target.switch.mac_addr,
                    "status": edge_info["status"],
                    "port_info": [
                        {"source_port": edge.source_port, "target_port": edge.target_port,
                         "status": edge_info["status"]}
                    ]
                }
        edges = list(edges_source_target_feature.values())
        query_topology = session.query(Topology).filter(Topology.id == topology_id).first()
        res = {
            "isShowLegend": query_topology.is_show_legend if query_topology else True,
            "zoom": query_topology.zoom if query_topology else 1,
            "translateX": query_topology.translate_x if query_topology else 0,
            "translateY": query_topology.translate_y if query_topology else 0,
            "isInTreeMode": query_topology.is_in_tree_mode if query_topology else False,
            "nodes": nodes,
            "edges": edges
        }
        msg = {'status': 200, 'data': res}
        return jsonify(msg)
    except Exception as e:
        LOG.error(traceback.format_exc())
        msg = {'info': 'Topology get fail', 'status': 500}
        return jsonify(msg)


@new_monitor_mold.route('/del_topology', methods=['POST'])
@admin_permission.require(http_exception=403)
def del_topology():
    try:
        data = request.get_json()
        topology_id = data.get("id")
        session = monitor_db.get_session()
        topology = session.query(Topology).filter(Topology.id == topology_id).first()
        if not topology:
            msg = {'info': 'Topology not found', 'status': 500}
            return jsonify(msg)
        session.query(Topology).filter(Topology.id == topology_id).delete()
        # 检查是否需要关闭lldp监控
        need_close_lldp_targets = session.query(MonitorTarget) \
            .outerjoin(TopologyNode, MonitorTarget.id == TopologyNode.monitor_target_id) \
            .filter(MonitorTarget.device_type == 1) \
            .filter(TopologyNode.monitor_target_id.is_(None)).all()
        for target in need_close_lldp_targets:
            inven_db.update_switch_montior(target.sn, 1, lldp=False)
        msg = {'info': 'Topology delete success', 'status': 200}
        return jsonify(msg)
    except Exception as e:
        LOG.error(traceback.format_exc())
        msg = {'info': 'Topology delete fail', 'status': 500}
        return jsonify(msg)


@new_monitor_mold.route('/refresh_lldp_info', methods=['POST'])
@admin_permission.require(http_exception=403)
def refresh_lldp_info():
    data = request.get_json()
    monitor_target_ids = data.get("monitorTargetIds", [])
    session = monitor_db.get_session()
    db_session = inven_db.get_session()
    try:
        # 目前只做switch侧的自动发现
        query_node = session.query(MonitorTarget).filter(MonitorTarget.id.in_(monitor_target_ids)).filter(
            MonitorTarget.device_type == 1).all()
        neighbors = []

        futures = []
        with ThreadPoolExecutor(max_workers=10) as executor:
            for monitor_target in query_node:
                system_config = db_session.query(SystemConfig).filter(
                    SystemConfig.id == monitor_target.switch.system_config_id).first()
                futures.append(
                    executor.submit(get_lldp_neighbor, monitor_target.switch.mgt_ip, system_config.switch_op_user,
                                    system_config.switch_op_password))

            for future in as_completed(futures):
                results = future.result()
                neighbors.extend(results)

        neighbors_info = {}
        for neighbor in neighbors:
            source_switch = session.query(MonitorTarget).join(MonitorTarget.switch).filter(
                Switch.mac_addr == neighbor["source_mac"]).first()
            target_switch = session.query(MonitorTarget).join(MonitorTarget.switch).filter(
                Switch.mac_addr == neighbor["target_mac"]).first()
            # 不在switch表，查看是否是OTN设备, switch单侧自动发现只匹配target是否是OTN
            if not target_switch:
                target_switch = session.query(MonitorTarget).filter(MonitorTarget.mac == neighbor["target_mac"]).first()
            # 不在监控范围内的数据暂时不存
            if source_switch and target_switch:
                key = f"{source_switch.id}_{target_switch.id}"
                link_info = f'{neighbor["source_port"]}_{neighbor["target_port"]}'

                # 用于去重
                dup_key = f"{target_switch.id}_{source_switch.id}"
                dup_link_info = f'{neighbor["target_port"]}_{neighbor["source_port"]}'

                if key not in neighbors_info and dup_key not in neighbors_info:
                    neighbors_info[key] = []
                elif dup_key in neighbors_info:
                    key = dup_key
                    link_info = dup_link_info

                if link_info not in neighbors_info[key]:
                    neighbors_info[key].append(link_info)

        res = []
        for key, value in neighbors_info.items():
            port_info = []
            for port_pair in value:
                source_port, target_port = port_pair.split('_')
                port_info.append({'target_port': target_port, 'source_port': source_port, "status": "up"})

            source_monitor_id, target_monitor_id = key.split('_')
            info = {
                "port_info": port_info,
                "source_monitor_id": int(source_monitor_id),  # 这里用monitor_target_id 没有使用sn 为了便于后续扩展otn设备
                "target_monitor_id": int(target_monitor_id)
            }
            res.append(info)
        msg = {'info': 'Topology refresh success', 'status': 200, 'data': res}
        return jsonify(msg)
    except Exception as e:
        LOG.error(traceback.format_exc())
        msg = {'info': 'Topology refresh fail', 'status': 500}
        return jsonify(msg)


@new_monitor_mold.route('/get_add_device_modal_table_data', methods=['POST'])
@admin_permission.require(http_exception=403)
def get_add_device_modal_table_data():
    try:
        data = request.get_json()
        sn_list = data.get("snList", [])
        page_num, page_size, total_count, query_obj = utils.query_helper(Switch,
                                                                         pre_query=utils.query_switch(sn_list=sn_list,
                                                                                                      is_show_active_only=True))
        if not sn_list:
            response = {
                "data": [{
                    "id": switch.id,
                    "host_name": switch.host_name,
                    "sn": switch.sn,
                    "create_time": switch.create_time.strftime('%Y-%m-%d %H:%M:%S') if switch.create_time else "",
                    "version": switch.version,
                    "revision": switch.revision,
                    "status": switch.status,
                    "mgt_ip": switch.mgt_ip,
                    "platform_model": switch.platform_model,
                    "selected": False
                } for switch in query_obj],
                "page": page_num,
                "pageSize": page_size,
                "total": total_count,
                "status": 200
            }
        else:
            response = {
                "data": [{
                    "id": switch.id,
                    "host_name": switch.host_name,
                    "sn": switch.sn,
                    "create_time": switch.create_time.strftime('%Y-%m-%d %H:%M:%S') if switch.create_time else "",
                    "version": switch.version,
                    "revision": switch.revision,
                    "status": switch.status,
                    "mgt_ip": switch.mgt_ip,
                    "platform_model": switch.platform_model,
                    "selected": selected
                } for switch, selected in query_obj],
                "page": page_num,
                "pageSize": page_size,
                "total": total_count,
                "status": 200
            }
        return jsonify(response)
    except Exception as e:
        LOG.error(traceback.format_exc())
        msg = {'info': 'Get add device modal table data fail', 'status': 500}
        return jsonify(msg)


@new_monitor_mold.route('/get_topology_to_be_added_switch', methods=['POST'])
@admin_permission.require(http_exception=403)
def get_topology_to_be_added_switch():
    try:
        data = request.get_json()
        switch_list_id = data.get("switchIdList", [])
        otn_ip_list = data.get("otnIpList", [])  # otn设备预留 需要传入otn设备ip
        session = monitor_db.get_session()
        nodes = []

        if otn_ip_list:
            failed_otn_list = []
            for otn_ip in otn_ip_list:
                # 调用 otn api 获取 lldp chassis-id
                lldp_info = query_otn_lldp_info(otn_ip)
                if lldp_info and lldp_info.get("state", {}).get("chassis-id", ""):
                    mac = lldp_info.get("state", {}).get("chassis-id", "")
                    inven_db.update_switch_montior(otn_ip, 2, mac=mac.replace('-', ':').lower())
                else:
                    failed_otn_list.append(otn_ip)

            if failed_otn_list:
                failed_otn = ",".join(failed_otn_list)
                msg = {'info': f"{failed_otn} get lldp info failed, can not add to topology", 'status': 500}
                return jsonify(msg)

            query_target = session.query(MonitorTarget).filter(MonitorTarget.name.in_(otn_ip_list)).all()
            for index, monitor_target in enumerate(query_target):
                otn_info = query_otn_platform_info(monitor_target.name)
                node_info = {
                    "id": str(uuid.uuid4()),
                    "label": monitor_target.name,
                    "ne_name": monitor_target.name,
                    "monitor_target_id": monitor_target.id if monitor_target else None,
                    "device_type": monitor_target.device_type if monitor_target else None,
                    "layer": 0,
                    "x": 50 + index * 100,
                    "y": 150,
                }
                if otn_info:
                    otn_info.pop("subcomponent")
                    node_info.update(otn_info)
                nodes.append(node_info)

        if switch_list_id:
            query_node = session.query(Switch, MonitorTarget).outerjoin(MonitorTarget,
                                                                        Switch.sn == MonitorTarget.sn).filter(
                Switch.id.in_(switch_list_id)).all()
            for index, node in enumerate(query_node):
                switch_info, monitor_target = node
                lldp_info = prometheus_util.query_lldp_state(switch_info.sn, switch_info.mac_addr)
                node_info = {
                    "id": str(uuid.uuid4()),
                    "label": switch_info.host_name if switch_info.host_name else 'PICOS',
                    "switch_sn": switch_info.sn,
                    "switch_id": switch_info.id,
                    "monitor_target_id": monitor_target.id if monitor_target else None,
                    "device_type": monitor_target.device_type if monitor_target else None,
                    "model": switch_info.platform_model,
                    "version": switch_info.version,
                    "mgt_ip": switch_info.mgt_ip,
                    "mac_addr": switch_info.mac_addr,
                    "layer": 0,
                    "x": 50 + index * 100,
                    "y": 50,
                    "status": "online" if switch_info.reachable_status == 0 else "offline"
                }
                node_info.update(lldp_info)
                nodes.append(node_info)
        msg = {'data': nodes, 'status': 200}
        return jsonify(msg)
    except Exception as e:
        LOG.error(traceback.format_exc())
        msg = {'info': 'Get topology to be added switch fail', 'status': 500}
        return jsonify(msg)


def get_lldp_neighbor(target_ip, username, passwd, port="9339"):
    res = []
    try:
        with gNMIclient(target=(target_ip, port), username=username, password=passwd, timeout=15) as gc:
            result = gc.get(path=['openconfig-lldp:lldp'])
            for notification in result["notification"]:
                for update in notification["update"]:
                    lldp_interface = update['val']['openconfig-lldp:interfaces']['interface']
                    for interface in lldp_interface:
                        if interface.get("neighbors", {}):
                            for neighbor in interface["neighbors"]["neighbor"]:
                                info = {
                                    "source_mac": update['val']['openconfig-lldp:state']['chassis-id'],
                                    "target_mac": neighbor["state"]['chassis-id'],
                                    "source_port": interface["name"],
                                    "target_port": neighbor["state"]['port-id']
                                }
                                res.append(info)
    except Exception as e:
        LOG.error(traceback.format_exc())
    return res


@new_monitor_mold.route('/get_interfaces_topk', methods=['POST'])
def get_interfaces_topk():
    try:
        data = request.get_json()
        metric_name = data.get("metricName",
                               None)  # in-octets in-pkts in-discards in-error in-fcs-errors out-octets out-discards out-errors
        topk = data.get("topK", 5)
        target = data.get("target", None)
        start_time = data.get("startTime", None)
        if start_time:
            start_time = datetime.strptime(start_time, timestamp_format).timestamp()
        end_time = data.get("endTime", None)
        if end_time:
            end_time = datetime.strptime(end_time, timestamp_format).timestamp()

        if metric_name is None:
            msg = {'info': 'MetricName is None', 'status': 500}
            return jsonify(msg)

        filter = data.get("filter", {})

        if "ecn" in metric_name:
            res = prometheus_util.query_counter_delta_topk(metric_name.replace("-", "_"),
                                                           metric_prefix="openconfig_qos:qos_interfaces_interface_state_fsconfig_qos_ai_extensions:",
                                                           topk=topk, target=target, start_time=start_time,
                                                           end_time=end_time, filter=filter)
        elif "pfc" in metric_name:
            res = prometheus_util.query_counter_delta_topk(metric_name.replace("-", "_"),
                                                           metric_prefix="openconfig_qos:qos_interfaces_interface_output_queues_queue_state_fsconfig_qos_ai_extensions:",
                                                           topk=topk, target=target, start_time=start_time,
                                                           end_time=end_time, filter=filter)
        else:
            res = prometheus_util.query_counter_delta_topk(metric_name.replace("-", "_"), topk=topk, target=target,
                                                           start_time=start_time, end_time=end_time, filter=filter)

        if metric_name in ["load-interval", "out-bits-rate", "in-bits-rate", "out-pkts-rate", "in-pkts-rate"]:
            res = prometheus_util.query_rate_topk(
                "openconfig_interfaces:interfaces_interface_openconfig_if_rates:rates_state_" + metric_name.replace("-",
                                                                                                                    "_"),
                topk=topk, target=target, start_time=start_time, end_time=end_time)

        msg = {'data': res, 'status': 200}
        return jsonify(msg)
    except Exception as e:
        LOG.error(traceback.format_exc())
        msg = {'info': 'Interfaces counters fail', 'status': 500}
        return jsonify(msg)


@new_monitor_mold.route('/get_interface_info', methods=['POST'])
def get_interface_info():
    try:
        data = request.get_json()
        target = data.get("target", None)
        date = data.get("date", None)
        if date:
            date = datetime.strptime(date, "%Y-%m-%d %H:%M:%S").timestamp()
        interface_list = prometheus_util.get_target_interfaces(target, date)
        interface_config = prometheus_util.query_metric_filter_by_interface(
            "openconfig_interfaces:interfaces_interface_config", target, interface_list, date)
        ethernet_config = prometheus_util.query_metric_filter_by_interface(
            "openconfig_interfaces:interfaces_interface_openconfig_if_ethernet:ethernet_config", target, interface_list,
            date)
        interface_state = prometheus_util.query_metric_filter_by_interface(
            "openconfig_interfaces:interfaces_interface_state", target, interface_list, date)
        etherenr_state = prometheus_util.query_metric_filter_by_interface(
            "openconfig_interfaces:interfaces_interface_openconfig_if_ethernet:ethernet_state", target, interface_list,
            date)
        counters_list = prometheus_util.query_counters_with_prefix(
            "openconfig_interfaces:interfaces_interface_state_counters_", target, interface_list, date)
        ethernet_list = prometheus_util.query_counters_with_prefix(
            "openconfig_interfaces:interfaces_interface_openconfig_if_ethernet:ethernet_state_counters_", target,
            interface_list, date)

        in_bits_port_speed_usage = prometheus_util.get_port_speed_usage(
            "openconfig_interfaces:interfaces_interface_openconfig_if_rates:rates_state_in_bits_rate", target,
            interface_list, "in_bits_port_speed_usage", date)
        out_bits_port_speed_usage = prometheus_util.get_port_speed_usage(
            "openconfig_interfaces:interfaces_interface_openconfig_if_rates:rates_state_out_bits_rate", target,
            interface_list, "out_bits_port_speed_usage", date)

        rates_state = prometheus_util.query_counters_with_prefix(
            "openconfig_interfaces:interfaces_interface_openconfig_if_rates:rates_state_", target, interface_list, date)

        interface_info = {
            key: {
                "config": interface_config.get(key, {}),
                "ethernet_config": ethernet_config.get(key, {}),
                "state": {
                    **interface_state.get(key, {}),
                    "counters": counters_list.get(key, {}),
                },
                "ethernet_state": {
                    **etherenr_state.get(key, {}),
                    "counters": ethernet_list.get(key, {}),
                    **in_bits_port_speed_usage.get(key, {}),
                    **out_bits_port_speed_usage.get(key, {}),
                    **rates_state.get(key, {})
                }
            }
            for key in set(interface_config) | set(ethernet_config) | set(interface_state) | set(etherenr_state) | set(
                counters_list) | set(ethernet_list)
        }

        msg = {'data': interface_info, 'status': 200}
        return jsonify(msg)
    except Exception as e:
        LOG.error(traceback.format_exc())
        msg = {'info': 'Get Interfaces info fail', 'status': 500}
        return jsonify(msg)


@new_monitor_mold.route('/get_modules_topk', methods=['POST'])
def get_modules_topk():
    try:
        data = request.get_json()
        metric_name = data.get("metricName", None)  # input-power output-power laser-temperature attenuation
        topk = data.get("topK", 5)
        target = data.get("target", None)
        start_time = data.get("startTime", None)
        if start_time:
            start_time = datetime.strptime(start_time, timestamp_format).timestamp()
        end_time = data.get("endTime", None)
        if end_time:
            end_time = datetime.strptime(end_time, timestamp_format).timestamp()

        if metric_name is None:
            msg = {'info': 'MetricName is None', 'status': 500}
            return jsonify(msg)

        if metric_name in ["input-power", "output-power"]:
            res = prometheus_util.query_modules_topk(
                "openconfig_platform:components_component_openconfig_platform_transceiver:transceiver_state_" + metric_name.replace(
                    "-", "_"),
                topk=topk, target=target, start_time=start_time, end_time=end_time, mode="bottomK")
        elif metric_name == "laser-temperature":
            res = prometheus_util.query_modules_topk(
                "openconfig_platform:components_component_openconfig_platform_transceiver:transceiver_state_fsconfig_platform_transceiver_extensions:" + metric_name.replace(
                    "-", "_"),
                topk=topk, target=target, start_time=start_time, end_time=end_time)
        else:
            res = prometheus_util.query_attenuation_topk(topk=topk, target=target, start_time=start_time,
                                                         end_time=end_time)

        msg = {'data': res, 'status': 200}
        return jsonify(msg)
    except Exception as e:
        LOG.error(traceback.format_exc())
        msg = {'info': 'Interfaces counters fail', 'status': 500}
        return jsonify(msg)


@new_monitor_mold.route('/get_modules_info', methods=['POST'])
def get_modules_info():
    try:
        data = request.get_json()
        target = data.get("target", None)
        date = data.get("date", None)
        if date:
            date = datetime.strptime(date, "%Y-%m-%d %H:%M:%S").timestamp()
        interface_list = prometheus_util.get_target_interfaces(target, date)
        transceiver_state = prometheus_util.query_metric_filter_by_interface(
            "openconfig_platform:components_component_openconfig_platform_transceiver:transceiver_state", target,
            interface_list, date)
        input_power = prometheus_util.query_counters(
            "openconfig_platform:components_component_openconfig_platform_transceiver:transceiver_state_input_power",
            target, interface_list, date, multiChannel=True)
        output_power = prometheus_util.query_counters(
            "openconfig_platform:components_component_openconfig_platform_transceiver:transceiver_state_output_power",
            target, interface_list, date, multiChannel=True)
        temperature = prometheus_util.query_counters(
            "openconfig_platform:components_component_openconfig_platform_transceiver:transceiver_state_fsconfig_platform_transceiver_extensions:laser_temperature",
            target, interface_list, date)

        interface_info = {
            key: {
                "transceiver_state": {
                    **transceiver_state.get(key, {}),
                    **({"input_power": input_power[key]} if input_power.get(key) is not None else {}),
                    **({"output_power": output_power[key]} if output_power.get(key) is not None else {}),
                    **({"temperature": temperature[key]} if temperature.get(key) is not None else {}),
                    **({
                        "attenuation": ", ".join(
                            _calculate_attenuation_list(input_power.get(key), output_power.get(key))
                        )
                    } if key in input_power and key in output_power else {})
                }
            }
            for key in set(transceiver_state) | set(input_power) | set(output_power) | set(temperature)
        }

        msg = {'data': interface_info, 'status': 200}
        return jsonify(msg)
    except Exception as e:
        LOG.error(traceback.format_exc())
        msg = {'info': 'Get modules info fail', 'status': 500}
        return jsonify(msg)

def _calculate_attenuation_list(input_str, output_str):
    # 示例输入: "12.34(C1), 13.45(C2)"
    if not input_str or not output_str:
        return []

    def parse_channel_values(power_str):
        result = {}
        for item in power_str.split(","):
            item = item.strip()
            match = re.match(r"([-+]?\d*\.?\d+)\(C(\d+)\)", item)
            if match:
                value = float(match.group(1))
                ch = match.group(2)
                result[ch] = value
        return result

    in_map = parse_channel_values(input_str)
    out_map = parse_channel_values(output_str)
    all_channels = set(in_map.keys()) | set(out_map.keys())

    result = []
    for ch in sorted(all_channels, key=lambda x: int(x)):
        if ch in in_map and ch in out_map:
            att = round(out_map[ch] - in_map[ch], 2)
            result.append(f"{att}(C{ch})")
        else:
            result.append(f"unknown(C{ch})")

    return result

@new_monitor_mold.route('/get_ai_info', methods=['POST'])
def get_ai_info():
    try:
        data = request.get_json()
        target = data.get("target", None)
        date = data.get("date", None)

        if date:
            date = datetime.strptime(date, "%Y-%m-%d %H:%M:%S").timestamp()
        interface_list = prometheus_util.get_target_interfaces(target, date)
        pfc_info = prometheus_util.query_ai_with_prefix(
            "openconfig_qos:qos_interfaces_interface_output_queues_queue_state_fsconfig_qos_ai_extensions:", target,
            interface_list, date)
        ecn_info = prometheus_util.query_ai_with_prefix(
            "openconfig_qos:qos_interfaces_interface_state_fsconfig_qos_ai_extensions:", target, interface_list, date)

        merged_infos = [
            {**pfc_info.get(interface, {}).get(q), **{'queue_name': q}, **{'interface_name': interface},
             **ecn_info.get(interface, {})}
            for interface in interface_list
            for q in set(pfc_info.get(interface, {}))
        ]

        sorted_merged_infos = sorted(
            merged_infos,
            key=lambda x: (x['interface_name'], x['queue_name'])
        )

        msg = {'data': sorted_merged_infos, 'status': 200}
        return jsonify(msg)
    except Exception as e:
        LOG.error(traceback.format_exc())
        msg = {'info': 'Get ai info fail', 'status': 500}
        return jsonify(msg)


@new_monitor_mold.route('/get_otn_port_info', methods=['POST'])
def get_otn_port_info():
    try:
        data = request.get_json()
        otn_ip = data.get("otnIp", None)

        otn_info = query_otn_platform_info(otn_ip)
        slots = otn_info["subcomponent"]
        card_info_list = []
        if slots:
            for slot in slots:
                card_name = slot["name"]
                slot_num = int(card_name[card_name.rindex("-") + 1:]) if "-" in card_name else -1
                if slot_num >= 1 and slot_num <= 8:
                    card_info = query_otn_card_info(otn_ip, card_name)
                    actual_type = card_info.get("state", {}).get("actual-vendor-type")
                    preconf_type = card_info.get("config", {}).get("vendor-type-preconf", None)
                    info = {
                        "slot_no": slot_num,
                        "name": card_name,
                        "card_type": card_name[:card_name.find("-")] if card_name.find("-") != -1 else card_name,
                        "card_name": actual_type if actual_type is not None else preconf_type,
                        "empty": card_info.get("state", {}).get("empty", "true"),
                        "equipment_mismatch": "true" if (
                                actual_type is not None and preconf_type is not None and actual_type != preconf_type) else "false",
                        "slot_note": card_info.get("config", {}).get("description")
                    }
                    card_info_list.append(info)

        msg = {'data': card_info_list, 'status': 200}
        return jsonify(msg)
    except Exception as e:
        LOG.error(traceback.format_exc())
        msg = {'info': 'Get OTN Port info fail', 'status': 500}
        return jsonify(msg)


@new_monitor_mold.route('/get_usage_topk', methods=['POST'])
def get_usage_topk():
    try:
        data = request.get_json()
        metric_name = data.get("metricName", None)
        topk = data.get("topK", 5)
        target = data.get("target", None)
        start_time = data.get("startTime", None)
        if start_time:
            start_time = datetime.strptime(start_time, timestamp_format).timestamp()
        end_time = data.get("endTime", None)
        if end_time:
            end_time = datetime.strptime(end_time, timestamp_format).timestamp()

        if metric_name is None:
            msg = {'info': 'MetricName is None', 'status': 500}
            return jsonify(msg)

        if metric_name == "cpu":
            res = prometheus_util.query_cpu_usage_topk(topk=topk, target=target, start_time=start_time,
                                                       end_time=end_time)
        elif metric_name == "memory":
            res = prometheus_util.query_memory_usage_topk(topk=topk, target=target, start_time=start_time,
                                                          end_time=end_time)
        elif metric_name == "both":
            if not end_time or not start_time:
                end_time = datetime.now().timestamp()
                start_time = end_time - 300
            cpu_res = prometheus_util.query_cpu_usage_topk(topk=topk, target=target, start_time=start_time,
                                                           end_time=end_time)
            memory_res = prometheus_util.query_memory_usage_topk(topk=topk, target=target, start_time=start_time,
                                                                 end_time=end_time)
            res = cpu_res + memory_res
        else:
            msg = {'info': 'MetricName is not supported', 'status': 500}
            return jsonify(msg)

        msg = {'data': res, 'status': 200}
        return jsonify(msg)
    except Exception as e:
        LOG.error(traceback.format_exc())
        msg = {'info': 'Interfaces counters fail', 'status': 500}
        return jsonify(msg)


@new_monitor_mold.route('/get_mac_table', methods=['POST'])
def get_mac_table():
    try:
        data = request.get_json()
        target = data.get("target", None)
        date = data.get("date", None)

        if date:
            date = datetime.strptime(date, "%Y-%m-%d %H:%M:%S").timestamp()

        mac_table_state = prometheus_util.query_metric(
            "openconfig_network_instance:network_instances_network_instance_fdb_mac_table_entries_entry_state", target,
            date)
        mac_table_interface = prometheus_util.query_metric(
            "openconfig_network_instance:network_instances_network_instance_fdb_mac_table_entries_entry_interface_interface_ref_state",
            target, date)
        # print(mac_table_interface, mac_table_state)

        mac_table_dict = {}
        for item in mac_table_state:
            mac_key = frozenset([item['entry_mac_address'], item['entry_vlan']])
            mac_table_dict[mac_key] = {**item}

        for item in mac_table_interface:
            mac_key = frozenset([item['entry_mac_address'], item['entry_vlan']])
            if mac_key in mac_table_dict:
                mac_table_dict[mac_key].update(item)
            else:
                mac_table_dict[mac_key] = {**item}

        mac_table_list = list(mac_table_dict.values())
        msg = {'data': mac_table_list, 'status': 200}
        return jsonify(msg)
    except Exception as e:
        LOG.error(traceback.format_exc())
        msg = {'info': 'Get mac table fail', 'status': 500}
        return jsonify(msg)


@new_monitor_mold.route('/get_arp_table', methods=['POST'])
def get_arp_table():
    try:
        data = request.get_json()
        target = data.get("target", None)
        date = data.get("date", None)

        if date:
            date = datetime.strptime(date, "%Y-%m-%d %H:%M:%S").timestamp()

        arp_config = prometheus_util.query_metric(
            "openconfig_interfaces:interfaces_interface_openconfig_vlan:routed_vlan_openconfig_if_ip:ipv4_neighbors_neighbor_config",
            target, date)
        arp_state = prometheus_util.query_metric(
            "openconfig_interfaces:interfaces_interface_openconfig_vlan:routed_vlan_openconfig_if_ip:ipv4_neighbors_neighbor_state",
            target, date)

        arp_table_dict = {}
        for item in arp_config:
            if item['interface_name'].startswith("eth"):  # 过滤管理口
                continue
            ip = item['ip']
            arp_table_dict[ip] = {**item}

        for item in arp_state:
            if item['interface_name'].startswith("eth"):
                continue
            neighbor_ip = item['neighbor_ip']
            if neighbor_ip in arp_table_dict:
                arp_table_dict[neighbor_ip].update(item)
            else:
                arp_table_dict[neighbor_ip] = {**item}

        arp_table_list = list(arp_table_dict.values())
        msg = {'data': arp_table_list, 'status': 200}
        return jsonify(msg)
    except Exception as e:
        LOG.error(traceback.format_exc())
        msg = {'info': 'Get arp table fail', 'status': 500}
        return jsonify(msg)


@new_monitor_mold.route('/get_ospf_table', methods=['POST'])
def get_ospf_table():
    try:
        data = request.get_json()
        target = data.get("target", None)
        date = data.get("date", None)

        if date:
            date = datetime.strptime(date, "%Y-%m-%d %H:%M:%S").timestamp()

        ospf_table_state = prometheus_util.query_metric(
            "openconfig_network_instance:network_instances_network_instance_protocols_protocol_ospfv2_areas_area_interfaces_interface_neighbors_neighbor_state",
            target, date)

        ospf_table_dict = {}
        for ospf_info in ospf_table_state:
            if ospf_info["metric_value"] == 0:
                continue
            ospf_key = frozenset([ospf_info["target"], ospf_info["neighbor_router_id"]])
            if ospf_key in ospf_table_dict.keys():
                # 由于dead_time会频繁变化 当数据变化时可能会出现多个指标(小概率) 因此根据dead_time判断哪个是最新的, dead_time需要格式话 放在前端做
                if ospf_info["dead_time"] > ospf_table_dict[ospf_key]["dead_time"]:
                    ospf_table_dict[ospf_key] = ospf_info
            else:
                ospf_table_dict[ospf_key] = ospf_info

        ospf_table_list = list(ospf_table_dict.values())
        msg = {'data': ospf_table_list, 'status': 200}
        return jsonify(msg)
    except Exception as e:
        LOG.error(traceback.format_exc())
        msg = {'info': 'Get ospf table fail', 'status': 500}
        return jsonify(msg)


@new_monitor_mold.route('/get_bgp_table', methods=['POST'])
def get_bgp_table():
    try:
        data = request.get_json()
        target = data.get("target", None)
        date = data.get("date", None)

        if date:
            date = datetime.strptime(date, "%Y-%m-%d %H:%M:%S").timestamp()

        bgp_neighbor_state = prometheus_util.query_metric(
            "openconfig_network_instance:network_instances_network_instance_protocols_protocol_bgp_neighbors_neighbor_state",
            target, date)
        bgp_timer_state = prometheus_util.query_metric(
            "openconfig_network_instance:network_instances_network_instance_protocols_protocol_bgp_neighbors_neighbor_timers_state",
            target, date)
        bgp_transport_state = prometheus_util.query_metric(
            "openconfig_network_instance:network_instances_network_instance_protocols_protocol_bgp_neighbors_neighbor_transport_state",
            target, date)
        bgp_restart_state = prometheus_util.query_metric(
            "openconfig_network_instance:network_instances_network_instance_protocols_protocol_bgp_neighbors_neighbor_graceful_restart_state",
            target, date)

        bgp_table_dict = {}

        def update_bgp_dict(bgp_info):
            # metric_value为0 说明采集时改指标已经过期 可能是被删除
            if bgp_info["metric_value"] == 0:
                return
            bgp_key = frozenset([bgp_info["target"], bgp_info["neighbor_neighbor_address"]])
            if bgp_key in bgp_table_dict.keys():
                bgp_table_dict[bgp_key].update(bgp_info)
            else:
                bgp_table_dict[bgp_key] = {**bgp_info}

        bgp_states = [bgp_neighbor_state, bgp_timer_state, bgp_transport_state, bgp_restart_state]
        for state_list in bgp_states:
            for bgp_info in state_list:
                update_bgp_dict(bgp_info)

        bgp_table_list = list(bgp_table_dict.values())
        msg = {'data': bgp_table_list, 'status': 200}
        return jsonify(msg)
    except Exception as e:
        LOG.error(traceback.format_exc())
        msg = {'info': 'Get ospf table fail', 'status': 500}
        return jsonify(msg)


@new_monitor_mold.route('/get_nic_info', methods=['POST'])
def get_nic_info():
    try:
        session_query = monitor_db.get_session()
        available_deviceNames = list(map(lambda x: x.ip, utils.query_host().all()))
        monitor_query = session_query.query(MonitorTarget).filter(MonitorTarget.device_type == 3,
                                                                MonitorTarget.name.in_(available_deviceNames)).all()

        data = request.get_json()

        search_fields = data.get("searchFields", {})

        server_search_fields = {}
        nic_search_fields = {}
        if search_fields:
            server_search_fields = {"fields": ["nodename"], "value": search_fields.get("value", "")}
            nic_search_fields = {"fields": ["name", "device"], "value": search_fields.get("value", "")}

        res = []
        key = 1
        for device in monitor_query:
            instance = device.name + ":9100"
            server_info = prometheus_util.query_node_metric(instance, metric="node_uname_info",
                                                            search_fields=server_search_fields)
            node_infos = prometheus_util.query_node_metric(instance, metric="node_nic_info",
                                                           search_fields=nic_search_fields)
            if server_info or node_infos:
                if not server_info:
                    server_info = prometheus_util.query_node_metric(instance, metric="node_uname_info")
                if not node_infos:
                    node_infos = prometheus_util.query_node_metric(instance, metric="node_nic_info")

                info = {
                    "name": server_info.get("nodename", ""),
                    "type": "server",
                    "deviceIp": device.name,
                    "id": key
                }
                network_infos = prometheus_util.query_node_metric(instance, metric="node_network_info")
                ethtool_infos = prometheus_util.query_node_metric(instance, metric="node_ethtool_info")

                nic_dicts = {}
                for device, net in network_infos.items():

                    if device not in node_infos:
                        continue

                    node_info = node_infos[device]
                    ethtool_info = ethtool_infos[device]

                    net["device"] = device
                    net["bus_info"] = ethtool_info["bus_info"]
                    new_nic = node_info
                    new_nic.update(ethtool_info)
                    new_nic.pop("bus_info")

                    bus_info = ethtool_info["bus_info"].split(".")[0]
                    # 如果后续有其他型号还需要继续增加匹配
                    chip_number = [word for word in new_nic["chip_number"].split() if
                                   'bcm' in word.lower() or 'mt' in word.lower()]
                    new_nic["chip_number"] = chip_number[0] if chip_number else new_nic["chip_number"]

                    new_nic.update(net)
                    new_nic["type"] = "nics"

                    nic_dicts[device] = new_nic
                    # nic_dicts[bus_info] = new_nic
                    # else:

                    #     new_nic["children"].append(network_info[device]) 

                nic_infos = list(nic_dicts.values())
                for index, nic in enumerate(nic_infos):
                    nic["host_port"] = 1
                    nic["id"] = int(str(key) + str(index + 1))
                    # for i, child in enumerate(nic["children"]):
                    #     child["id"] = int(str(key) + str(index+1) + str(i+1))

                info.update(children=nic_infos)

                key += 1
                res.append(info)

        msg = {'status': 200, "data": res, "total": len(res)}
        return jsonify(msg)
    except Exception as e:
        LOG.error(traceback.format_exc())
        msg = {'info': 'Nic info get fail', 'status': 500}
        return jsonify(msg)


@new_monitor_mold.route('/get_nic_history_info', methods=['POST'])
def get_nic_history_info():
    try:
        data = request.get_json()
        start_time = data.get("startTime", None)
        if start_time:
            start_time = datetime.strptime(start_time, timestamp_format).timestamp()
        end_time = data.get("endTime", None)
        if end_time:
            end_time = datetime.strptime(end_time, timestamp_format).timestamp()

        node_info = prometheus_util.query_node_range_metric(metric="node_uname_info", start_time=start_time,
                                                            end_time=end_time)
        node_nic_info = prometheus_util.query_node_range_metric(metric="node_nic_info", start_time=start_time,
                                                                end_time=end_time)

        res = []
        available_hosts_ip = list(set(map(lambda x: x.ip, utils.query_host().all())))
        for instance, nodes in node_info.items():
            if instance.split(":")[0] not in available_hosts_ip:
                continue
            nic_list = [nic.get("device") for nic in node_nic_info[instance] if nic.get("device") is not None]
            node_nic = {
                "hostname": nodes[0]["nodename"],
                "ip": instance.split(":")[0],
                "children": list(set(nic_list))
            }
            res.append(node_nic)

        msg = {'data': res, 'status': 200}
        return jsonify(msg)
    except Exception as e:
        LOG.error(traceback.format_exc())
        msg = {'info': 'Get nic history info fail', 'status': 500}
        return jsonify(msg)


@new_monitor_mold.route('/get_nic_topk', methods=['POST'])
def get_nic_topk():
    try:
        data = request.get_json()
        metric_name = data.get("metricName", None)
        topk = data.get("topK", 5)
        start_time = data.get("startTime", None)
        if start_time:
            start_time = datetime.strptime(start_time, timestamp_format).timestamp()
        end_time = data.get("endTime", None)
        if end_time:
            end_time = datetime.strptime(end_time, timestamp_format).timestamp()

        if metric_name is None:
            msg = {'info': 'MetricName is None', 'status': 500}
            return jsonify(msg)

        filter = data.get("filter", {})

        res = prometheus_util.query_node_topk(metric_name.replace("-", "_"), topk=topk, filter=filter,
                                              start_time=start_time, end_time=end_time)

        ip_hostname_map = {}
        available_hosts_ip = list(set(map(lambda x: x.ip, utils.query_host().all())))
        res = [value for value in res if value["instance"] in available_hosts_ip]
        for value in res:
            if value["instance"] in ip_hostname_map.keys():
                value["hostname"] = ip_hostname_map[value["instance"]]
            else:
                server_info = prometheus_util.query_node_metric(value["instance"] + ":9100", metric="node_uname_info")
                ip_hostname_map[value["instance"]] = server_info.get("nodename", "")
                value["hostname"] = server_info.get("nodename", "")

        msg = {'data': res, 'status': 200}
        return jsonify(msg)
    except Exception as e:
        LOG.error(traceback.format_exc())
        msg = {'info': 'Nic counters fail', 'status': 500}
        return jsonify(msg)


@new_monitor_mold.route('/get_target_interface', methods=['POST'])
def get_target_interface():
    try:
        data = request.get_json()
        target = data.get("target", None)
        date = data.get("date", None)
        if date:
            date = datetime.strptime(date, "%Y-%m-%d %H:%M:%S").timestamp()
        interface_list = prometheus_util.get_target_interfaces(target, date)
        msg = {'data': interface_list, 'status': 200}
        return jsonify(msg)
    except Exception as e:
        LOG.error(traceback.format_exc())
        msg = {'info': 'Get dlb info fail', 'status': 500}
        return jsonify(msg)


@new_monitor_mold.route('/get_dlb_topk', methods=['POST'])
def get_dlb_topk():
    try:
        data = request.get_json()
        metric_name = data.get("metricName", None)
        topk = data.get("topK", 5)
        target = data.get("target", None)
        start_time = data.get("startTime", None)
        if start_time:
            start_time = datetime.strptime(start_time, timestamp_format).timestamp()
        end_time = data.get("endTime", None)
        if end_time:
            end_time = datetime.strptime(end_time, timestamp_format).timestamp()

        if metric_name is None:
            msg = {'info': 'MetricName is none', 'status': 500}
            return jsonify(msg)

        filter = data.get("filter", [])

        if metric_name == "out-bindwidth-utilization":
            res = prometheus_util.query_dlb_rate_topk(
                dividend_metric_name="openconfig_interfaces:interfaces_interface_openconfig_if_rates:rates_state_out_bits_rate",
                divisor_metric_name="openconfig_interfaces:interfaces_interface_openconfig_if_ethernet:ethernet_state_port_speed",
                topk=topk, target=target, filter=filter, start_time=start_time, end_time=end_time)
        elif metric_name == "in-bindwidth-utilization":
            res = prometheus_util.query_dlb_rate_topk(
                dividend_metric_name="openconfig_interfaces:interfaces_interface_openconfig_if_rates:rates_state_in_bits_rate",
                divisor_metric_name="openconfig_interfaces:interfaces_interface_openconfig_if_ethernet:ethernet_state_port_speed",
                topk=topk, target=target, filter=filter, start_time=start_time, end_time=end_time)
        elif metric_name == "out-packet-loss-rate":
            res = prometheus_util.query_dlb_rate_topk(
                dividend_metric_name="openconfig_interfaces:interfaces_interface_state_counters_out_discards",
                divisor_metric_name="openconfig_interfaces:interfaces_interface_state_counters_out_pkts",
                topk=topk, target=target, filter=filter, start_time=start_time, end_time=end_time)
        elif metric_name == "in-packet-loss-rate":
            res = prometheus_util.query_dlb_rate_topk(
                dividend_metric_name="openconfig_interfaces:interfaces_interface_state_counters_in_discards",
                divisor_metric_name="openconfig_interfaces:interfaces_interface_state_counters_in_pkts",
                topk=topk, target=target, filter=filter, start_time=start_time, end_time=end_time)
        elif metric_name == "out-throughput":
            res = prometheus_util.query_rate_topk(
                "openconfig_interfaces:interfaces_interface_openconfig_if_rates:rates_state_out_pkts_rate",
                topk=topk, target=target, filter=filter, start_time=start_time, end_time=end_time)
        elif metric_name == "in-throughput":
            res = prometheus_util.query_rate_topk(
                "openconfig_interfaces:interfaces_interface_openconfig_if_rates:rates_state_in_pkts_rate",
                topk=topk, target=target, filter=filter, start_time=start_time, end_time=end_time)
        else:
            msg = {'info': 'MetricName is not supported', 'status': 500}
            return jsonify(msg)

        msg = {'data': res, 'status': 200}
        return jsonify(msg)
    except Exception as e:
        LOG.error(traceback.format_exc())
        msg = {'info': 'Get dlb topk fail', 'status': 500}
        return jsonify(msg)


@new_monitor_mold.route('/get_dlb_table', methods=['POST'])
def get_dlb_table():
    try:
        data = request.get_json()
        target = data.get("target", None)
        date = data.get("date", None)
        if date:
            date = datetime.strptime(date, "%Y-%m-%d %H:%M:%S").timestamp()
        interface_list = prometheus_util.get_target_interfaces(target, date)
        counters_list = prometheus_util.query_counters_with_prefix(
            "openconfig_interfaces:interfaces_interface_state_counters_", target, interface_list, date)
        rates_state = prometheus_util.query_counters_with_prefix(
            "openconfig_interfaces:interfaces_interface_openconfig_if_rates:rates_state_", target, interface_list, date)

        interface_info = {
            key: {
                **counters_list.get(key, {}),
                **rates_state.get(key, {})
            }
            for key in set(interface_list)
        }

        msg = {'data': interface_info, 'status': 200}
        return jsonify(msg)
    except Exception as e:
        LOG.error(traceback.format_exc())
        msg = {'info': 'Get dlb info fail', 'status': 500}
        return jsonify(msg)


@new_monitor_mold.route('/get_device_table', methods=['POST'])
def get_device_table():
    try:
        data = request.get_json()
        target = data.get("target", None)
        date = data.get("date", None)
        if date:
            date = datetime.strptime(date, "%Y-%m-%d %H:%M:%S").timestamp()

        fan_state = prometheus_util.query_metric(
            "openconfig_platform:components_component_fan_state_fsconfig_platform_fan_extensions:fan", target, date)
        fan_pwm = prometheus_util.query_metric(
            "openconfig_platform:components_component_fan_state_fsconfig_platform_fan_extensions:fan_pwm", target, date)
        fan_speed = prometheus_util.query_metric(
            "openconfig_platform:components_component_fan_state_fsconfig_platform_fan_extensions:fan_speed", target,
            date)
        rear_fan_state = prometheus_util.query_metric(
            "openconfig_platform:components_component_fan_state_fsconfig_platform_fan_extensions:rear_fan", target,
            date)
        rear_fan_pwm = prometheus_util.query_metric(
            "openconfig_platform:components_component_fan_state_fsconfig_platform_fan_extensions:rear_fan_pwm", target,
            date)
        rear_fan_speed = prometheus_util.query_metric(
            "openconfig_platform:components_component_fan_state_fsconfig_platform_fan_extensions:rear_fan_speed",
            target, date)

        rpsu_state = prometheus_util.query_metric(
            "openconfig_platform:components_component_power_supply_state_fsconfig_platform_psu_extensions:rpsu_psu",
            target, date)

        pwm_dict = {pwm['fan_index']: pwm['metric_value'] for pwm in fan_pwm}
        speed_dict = {speed['fan_index']: speed['metric_value'] for speed in fan_speed}
        for state in fan_state:
            fan_index = state['fan_index']
            if fan_index in pwm_dict:
                state['pwm'] = str(round(pwm_dict[fan_index] * 100, 2)) + "%"
            if fan_index in speed_dict:
                state['speed'] = int(speed_dict[fan_index])

        rear_pwm_dict = {pwm['rear_fan_index']: pwm['metric_value'] for pwm in rear_fan_pwm}
        rear_speed_dict = {speed['rear_fan_index']: speed['metric_value'] for speed in rear_fan_speed}
        for state in rear_fan_state:
            fan_index = state['rear_fan_index']
            if fan_index in rear_pwm_dict:
                state['pwm'] = str(round(rear_pwm_dict[fan_index] * 100, 2)) + "%"
            if fan_index in rear_speed_dict:
                state['speed'] = int(rear_speed_dict[fan_index])

        res = {
            "fan_state": fan_state + rear_fan_state,
            "rpsu_state": rpsu_state
        }

        msg = {'data': res, 'status': 200}
        return jsonify(msg)
    except Exception as e:
        LOG.error(traceback.format_exc())
        msg = {'info': 'get fan info fail', 'status': 500}
        return jsonify(msg)


@new_monitor_mold.route('/get_device_fan_data', methods=['POST'])
def get_device_fan_data():
    try:
        data = request.get_json()
        target = data.get("target", None)
        start_time = data.get("startTime", None)
        if start_time:
            start_time = datetime.strptime(start_time, timestamp_format).timestamp()
        end_time = data.get("endTime", None)
        if end_time:
            end_time = datetime.strptime(end_time, timestamp_format).timestamp()

        res = prometheus_util.query_fan_data(target, start_time, end_time)

        if not res:
            return jsonify({'info': 'The relevant telemetry data cannot be retrieved because gRPC is not enabled on this switch.', 'status': 400})

        msg = {'data': res, 'status': 200}
        return jsonify(msg)
    except Exception as e:
        LOG.error(traceback.format_exc())
        msg = {'info': 'get fan data fail', 'status': 500}
        return jsonify(msg)


@new_monitor_mold.route('/get_interface_nic_auto_discovery', methods=['POST'])
def get_interface_nic_auto_discovery():
    try:
        data = request.get_json()
        target = data.get("target", None)
        date = data.get("date", None)
        if date:
            date = datetime.strptime(date, "%Y-%m-%d %H:%M:%S").timestamp()

        res = prometheus_util.query_interface_nic(target, date)

        msg = {'data': res, 'status': 200}
        return jsonify(msg)
    except Exception as e:
        LOG.error(traceback.format_exc())
        msg = {'info': 'get interface nic data fail', 'status': 500}
        return jsonify(msg)


@new_monitor_mold.route('/fetch_switch_client_table_data', methods=['POST'])
def fetch_switch_client_table_data():
    try:
        data = request.get_json()
        switchSN = data.get("switchSN", None)
        date = data.get("date", None)
        res = []
        mac_address_in_client_device_info = []
        session = inven_db.get_session()
        if date is not None:
            date = datetime.strptime(date, "%Y-%m-%d %H:%M:%S").timestamp()
            mac_info_list = prometheus_util.query_interface_mac_address_by_target(switchSN, date)
            for client_device in session.query(ClientDeviceInfo).filter(ClientDeviceInfo.switch_sn == switchSN).all():
                dhcp_snooping_info = prometheus_util.query_dhcp_snooping_info_by_target_and_mac(switchSN, client_device.mac_address, client_device.port, date)
                res.append({
                    "client_name": client_device.client_name,
                    "state": any(
                        x['mac_address'] == client_device.mac_address and x['port'] == client_device.port
                        for x in mac_info_list
                    ),
                    "mac_address": client_device.mac_address,
                    "ip_address": "" if not dhcp_snooping_info else dhcp_snooping_info.get("ip_address", ""),
                    "port" : client_device.port,
                    "manufacturer": client_device.manufacturer if client_device.manufacturer else get_organization_name_by_mac(client_device.mac_address),
                    "terminal_type": client_device.terminal_type,
                })
                mac_address_in_client_device_info.append(client_device.mac_address)
            # do not display mac info data which device is managed switch in ampcon
            managed_switch_mac_info_list = list(map(lambda x: x[0], session.query(Switch.mac_addr).all()))
            mac_info_list = [temp for temp in mac_info_list if temp['mac_address'] not in mac_address_in_client_device_info and temp['mac_address'] not in managed_switch_mac_info_list]
            # for display mac info which not in table ClientDeviceInfo
            for mac_info in mac_info_list:
                dhcp_snooping_info = prometheus_util.query_dhcp_snooping_info_by_target_and_mac(switchSN, mac_info['mac_address'], mac_info['port'], date)
                res.append({
                    "client_name": mac_info['mac_address'],
                    "state": True,
                    "mac_address": mac_info['mac_address'],
                    "ip_address": "" if not dhcp_snooping_info else dhcp_snooping_info.get("ip_address", ""),
                    "port": mac_info['port'],
                    "manufacturer": get_organization_name_by_mac(mac_info['mac_address']),
                    "terminal_type": None,
                })
        else:
            for client_device in session.query(ClientDeviceInfo).filter(ClientDeviceInfo.switch_sn == switchSN).all():
                res.append({
                    "client_name": client_device.client_name,
                    "state": client_device.state,
                    "mac_address": client_device.mac_address,
                    "ip_address": client_device.ip_address,
                    "port": client_device.port,
                    "manufacturer": client_device.manufacturer if client_device.manufacturer else get_organization_name_by_mac(client_device.mac_address),
                    "terminal_type": client_device.terminal_type,
                })
        # sorted by state desc ip_address desc
        res.sort(key=lambda x: (str(x['state']), x['ip_address'] if x['ip_address'] is not None else ""), reverse=True)
        msg = {'data': res, 'status': 200}
        return jsonify(msg)
    except Exception as e:
        LOG.error(traceback.format_exc())
        msg = {'info': 'get switch client table data fail', 'status': 500}
        return jsonify(msg)


def query_otn_lldp_info(otn_ip):
    url = "http://tnms_web:8888/api/ne/get"
    payload = {
        "ne_id": otn_ip + ":830",
        "xml": {
            "lldp": {
                "$": {
                    "xmlns": "http://openconfig.net/yang/lldp"
                }
            }
        }
    }

    try:
        response = requests.post(url, json=payload)
        data = response.json()
        if data['message'] != 'SUCCESS':
            LOG.error(f"query lldp info failed: {data['message']}")
            return []
        else:
            # print(data)
            return data['data']['lldp']
    except Exception as e:
        LOG.error(f"query lldp info failed: {traceback.format_exc()}")
        return []


def query_otn_platform_info(otn_ip):
    url = "http://tnms_web:8888/api/ne/get"
    payload = {
        "ne_id": otn_ip + ":830",
        "xml": {
            "components": {
                "$": {
                    "xmlns": "http://openconfig.net/yang/platform"
                },
                "component": {
                    "name": "CHASSIS-1"
                }
            }
        }
    }

    try:
        response = requests.post(url, json=payload)
        data = response.json()
        if data['message'] != 'SUCCESS':
            LOG.error(f"query otn platform info failed: {data['message']}")
            return {}
        else:
            data = data['data']['components']['component']
            otn_info = {
                "chaasis_type": data.get("chassis", {}).get("state", {}).get("chassis-type", {}).get("_"),
                "hardware_version": data.get("state", {}).get("hardware-version"),
                "temperature": data.get("state", {}).get("temperature", {}).get("instant"),
                "sn": data.get("state", {}).get("serial-no"),
                "manufacture": data.get("state", {}).get("mfg-name"),
                "firmware_version": data.get("state", {}).get("firmware-version"),
                "actual_power": data.get("state", {}).get("used-power"),
                "pn": data.get("state", {}).get("part-no"),
                "production_date": data.get("state", {}).get("mfg-date"),
                "software_version": data.get("state", {}).get("software-version"),
                "other_info": data.get("state", {}).get("description"),
                "subcomponent": data.get("subcomponents", {}).get("subcomponent", [])
            }
            return otn_info
    except Exception as e:
        LOG.error(f"query otn platform info failed: {traceback.format_exc()}")
        return {}


def query_otn_card_info(otn_ip, card_name):
    url = "http://tnms_web:8888/api/data/get_key"
    payload = {
        "DBKey": f"ne:5:component:{otn_ip}:830:{card_name}"
    }

    try:
        response = requests.post(url, json=payload)
        data = response.json()
        if data.get("apiResult", "") == 'fail':
            LOG.error(f"query otn card info failed: {data}")
            return {}
        else:
            data = data['data']
            return data
    except Exception as e:
        LOG.error(f"query lldp info failed: {traceback.format_exc()}")
        return {}


@new_monitor_mold.route('/get_switch_lldp_links', methods=['POST'])
def get_switch_lldp_links():
    try:
        data = request.get_json()
        sn_list = data.get("snList", [])
        if not sn_list:
            msg = {'info': 'sn list is empty', 'status': 500}
            return jsonify(msg)
        res = prometheus_util.lldp_refresh(sn_list)
        msg = {'data': res, 'status': 200}
        return jsonify(msg)
    except Exception as e:
        LOG.error(traceback.format_exc())
        msg = {'info': 'get lldp status fail', 'status': 500}
        return jsonify(msg)


@new_monitor_mold.route('/get_roce_task_port', methods=['POST'])
def get_roce_task_port():
    try:
        data = request.get_json()
        port_type = data.get("type", "nvidia")

        if port_type == "nvidia":
            nic_info = prometheus_util.query_nvidia_nic_port()
        elif port_type == "broadcom":
            nic_info = prometheus_util.query_broadcom_nic_port()
        else:
            msg = {'info': 'nic vendors not support', 'status': 400}
            return jsonify(msg)

        res = []
        session = inven_db.get_session()
        available_devicenames = list(map(lambda x: x.device_name, utils.query_host().all()))
        for instance, item in nic_info.items():
            device = session.query(AnsibleDevice).filter(AnsibleDevice.ip == instance,
                                                         AnsibleDevice.device_name.in_(available_devicenames)).first()
            if not device:
                continue
            item["device_id"] = device.id
            res.append(item)

        msg = {'data': res, 'status': 200}
        return jsonify(msg)
    except Exception as e:
        LOG.error(traceback.format_exc())
        msg = {'info': 'get lldp status fail', 'status': 500}
        return jsonify(msg)


@new_monitor_mold.route('/get_host_info', methods=['POST'])
def get_host_info():
    try:
        session = inven_db.get_session()
        device_query = session.query(AnsibleDevice).all()
        available_device_ids = list(map(lambda x: x.id, utils.query_host().all()))

        # 更新host信息
        for device in device_query:
            instance = device.ip + ":9100"
            host_info = prometheus_util.query_node_metric(instance, metric="node_host_info")
            if host_info:
                try:
                    with session.begin(subtransactions=True):
                        existing_info = session.query(HostInfo).filter(HostInfo.device_id == device.id).first()
                        if existing_info:
                            existing_info.hostname = host_info["hostname"]
                            existing_info.os_version = host_info["os_version"]
                            existing_info.cpu = host_info["cpu"]
                            existing_info.memory = host_info["memory"]
                            existing_info.storage = host_info["storage"]
                            existing_info.last_seen = datetime.now()
                        else:
                            info = HostInfo(device_id=device.id, hostname=host_info["hostname"],
                                            os_version=host_info["os_version"],
                                            cpu=host_info["cpu"], memory=host_info["memory"],
                                            storage=host_info["storage"], last_seen=datetime.now())
                            session.add(info)
                except Exception as e:
                    LOG.error(traceback.format_exc())
                    continue

        page_num, page_size, total_count, query_info = utils.query_helper(HostInfo, pre_query=session.query(HostInfo).filter(
                                                                                    HostInfo.device_id.in_(available_device_ids)))
        res = []
        for info in query_info:
            data = info.make_dict()
            data["device_name"] = info.device.device_name
            res.append(data)

        return jsonify({"data": res, "page": page_num,
                        "pageSize": page_size,
                        "total": total_count,
                        "status": 200})
    except Exception as e:
        LOG.error(traceback.format_exc())
        msg = {'info': 'nic info get fail', 'status': 500}
        return jsonify(msg)


@new_monitor_mold.route('/get_roce_nic_port', methods=['POST'])
def get_roce_nic_port():
    try:
        data = request.get_json()
        start_time = data.get("startTime", None)
        if start_time:
            start_time = datetime.strptime(start_time, timestamp_format).timestamp()
        end_time = data.get("endTime", None)
        if end_time:
            end_time = datetime.strptime(end_time, timestamp_format).timestamp()

        type = data.get("type", "")

        nic_info = prometheus_util.query_rock_nic_port(start_time=start_time, end_time=end_time, type=type)

        res = []
        available_hosts_ip = list(set(map(lambda x: x.ip, utils.query_host().all())))
        for _, item in nic_info.items():
            if item["instance"] not in available_hosts_ip:
                continue
            res.append(item)

        msg = {'data': res, 'status': 200}
        return jsonify(msg)
    except Exception as e:
        LOG.error(traceback.format_exc())
        msg = {'info': 'get nic history info fail', 'status': 500}
        return jsonify(msg)


@new_monitor_mold.route('/get_roce_topk', methods=['POST'])
def get_roce_topk():
    try:
        data = request.get_json()
        metric_name = data.get("metricName", None)
        prioX = data.get("prioX", None)
        topk = data.get("topK", 5)
        start_time = data.get("startTime", None)
        if start_time:
            start_time = datetime.strptime(start_time, timestamp_format).timestamp()
        end_time = data.get("endTime", None)
        if end_time:
            end_time = datetime.strptime(end_time, timestamp_format).timestamp()

        if metric_name is None:
            msg = {'info': 'metricName is None', 'status': 500}
            return jsonify(msg)

        filter = data.get("filter", {})
        type = data.get("type", "nvidia")

        if type == "nvidia":
            metric_name = roce_nvidia_metricName_dict[metric_name].format(prioX=prioX)
        elif type == "broadcom":
            metric_name = roce_broadcom_metricName_dict[metric_name].format(prioX=prioX)

        res = prometheus_util.query_node_topk(metric_name, topk=topk, filter=filter, start_time=start_time,
                                              end_time=end_time)

        ip_hostname_map = {}
        available_hosts_ip = list(set(map(lambda x: x.ip, utils.query_host().all())))
        res = [value for value in res if value["instance"] in available_hosts_ip]
        for value in res:
            if value["instance"] in ip_hostname_map.keys():
                value["hostname"] = ip_hostname_map[value["instance"]]
            else:
                server_info = prometheus_util.query_node_metric(value["instance"] + ":9100", metric="node_uname_info")
                ip_hostname_map[value["instance"]] = server_info.get("nodename", "")
                value["hostname"] = server_info.get("nodename", "")

        msg = {'data': res, 'status': 200}
        return jsonify(msg)
    except Exception as e:
        LOG.error(traceback.format_exc())
        msg = {'info': 'nic counters fail', 'status': 500}
        return jsonify(msg)


@new_monitor_mold.route('/get_roce_modules_info', methods=['POST'])
def get_roce_modules_info():
    try:
        request_data = request.get_json()
        page = request_data.get("page", 1)
        pageSize = request_data.get("pageSize", 10)
        sortFields = request_data.get("sortFields", [])
        searchFields = request_data.get("searchFields", {})
        filter_fields = request_data.get("filterResult", [])
        res = prometheus_util.query_roce_sfp_info()
        if res:
            if filter_fields:
                filter_dict = {}
                for item in filter_fields:
                    if list(item.keys())[0] not in filter_dict:
                        filter_dict[list(item.keys())[0]] = [list(item.values())[0]]
                    else:
                        filter_dict[list(item.keys())[0]].append(list(item.values())[0])
                data = []
                for item in res:
                    if item.get("name", "") in filter_dict.keys():
                        single_data = {
                            "name": item.get("name", ""),
                            "id": item.get("id", ""),
                            "children": []
                        }
                        children = item.get("children", [])
                        for child in children:
                            if child.get("device", "") in filter_dict.get(item.get("name", "")):
                                single_data.get("children").append(child)
                        if single_data.get("children"):
                            data.append(single_data)
            else:
                data = res
            fields = searchFields.get("fields", [])
            value = searchFields.get("value", "")
            if fields and value != "":
                search_result = []
                for item in data:
                    match = {
                        "id": item.get("id"),
                        "name": item.get("name"),
                        "children": []
                    }
                    if value in item.get(fields[0]):
                        match.get("children").extend(item.get("children"))
                    else:
                        children = item.get("children")
                        if children:
                            for child in children:
                                if (value in child.get(fields[0], "")) or (value in child.get(fields[1], "")):
                                    match.get("children").append(child)
                    if match.get("children"):
                        search_result.append(match)
                data = search_result
            offset = (page - 1) * pageSize
            paged_data = data[offset:offset + pageSize]
            if sortFields:
                field = sortFields[0].get("field")
                order = sortFields[0].get("order")
                for item in paged_data:
                    item.get("children").sort(key=lambda x: x.get(field, ""), reverse=(True if order == "desc" else False))

            msg = {
                'status': 200,
                "data": paged_data,
                "page": page,
                "pageSize": pageSize,
                "total": len(res)
            }
            return jsonify(msg)
        else:
            return jsonify({
                'status': 200,
                "data": [],
                "page": page,
                "pageSize": pageSize,
                "total": len(res)
            })
    except Exception as e:
        LOG.error(traceback.format_exc())
        msg = {'info': 'nic info get fail', 'status': 500}
        return jsonify(msg)


@new_monitor_mold.route('/fetch_client_table_data', methods=['POST'])
def fetch_client_table_data():
    try:
        if not request.get_json()['sortFields']:
            data = request.get_json()
            data['sortFields'] = [{'field': 'state', 'order': 'desc'}, {'field': 'ip_address', 'order': 'desc'}]
            page_num, page_size, total_count, query_info = utils.query_helper(ClientDeviceInfo, pre_query=inven_db.get_session().query(ClientDeviceInfo, Switch.sn).outerjoin(Switch, ClientDeviceInfo.switch_sn == Switch.sn), data=data)
        else:
            page_num, page_size, total_count, query_info = utils.query_helper(ClientDeviceInfo, pre_query=inven_db.get_session().query(ClientDeviceInfo, Switch.sn).outerjoin(Switch, ClientDeviceInfo.switch_sn == Switch.sn))
        res = []
        for info in query_info:
            data = info[0].make_dict()
            data['is_switch_exist'] = info[1] is not None
            res.append(data)
        return jsonify({'status': 200, 'data': res, 'total': total_count, 'page': page_num, "pageSize": page_size})
    except Exception as e:
        LOG.error(traceback.format_exc())
        msg = {'info': 'get interface client data fail', 'status': 500}
        return jsonify(msg)

@new_monitor_mold.route('/get_client_select_info', methods=['POST'])
def get_client_select_info():
    try:
        session = inven_db.get_session()

        distinct_terminal_types = session.query(ClientDeviceInfo.terminal_type).filter(ClientDeviceInfo.terminal_type.isnot(None),ClientDeviceInfo.terminal_type != '').distinct().all()
        default_terminal_types = ["PC", "Printer", "IP Camera", "IP Phone", "WAP"]
        db_terminal_types = {t[0] for t in distinct_terminal_types}
        terminal_types = default_terminal_types + sorted(db_terminal_types - set(default_terminal_types))

        distinct_manufacturers = session.query(ClientDeviceInfo.manufacturer).filter(ClientDeviceInfo.manufacturer.isnot(None), ClientDeviceInfo.manufacturer != '').distinct().all()
        default_manufacturers = set()
        with open('./static/mac_oui_mapping.json', 'r', encoding='utf-8', errors='ignore') as f:
            mac_oui_dict = json.load(f)
            for value in mac_oui_dict.values():
                if isinstance(value, str):
                    if '(' in value and ')' in value:
                        start_idx = value.find('(') + 1
                        end_idx = value.find(')', start_idx)
                        if start_idx < end_idx:
                            english_part = value[start_idx:end_idx]
                            cleaned = ''.join(c for c in english_part if ord(c) < 128 and c.isprintable())
                            if cleaned:
                                default_manufacturers.add(cleaned.strip())
                                continue
                    cleaned = ''.join(c for c in value if 32 <= ord(c) <= 126)
                    if cleaned:
                        default_manufacturers.add(cleaned.strip())
        db_manufacturers = {a[0] for a in distinct_manufacturers}
        sorted_default_manufacturers = sorted(default_manufacturers)
        unique_db_manufacturers = sorted(db_manufacturers - default_manufacturers)
        manufacturers = sorted_default_manufacturers + unique_db_manufacturers

        return jsonify({'status': 200, 'manufacturers': manufacturers, 'terminalType': terminal_types})
    except Exception as e:
        LOG.error(traceback.format_exc())
        msg = {'info': 'get  client manufacturer info fail', 'status': 500}
        return jsonify(msg)

@new_monitor_mold.route('/create_client', methods=['POST'])
def create_client():
    try:
        data = request.get_json()
        switch_sn = data.get("switchSN", None)
        mac_address = format_mac_address(
            data.get("macAddress", None).replace(':', '').replace('-', '').lower()) if data.get("macAddress",
                                                                                                None) is not None else None
        client_name = data.get("clientName", None)
        terminal_type = data.get("terminalType", None)
        ip_address = data.get("ipAddress", None)
        port = data.get("port", None)

        if not switch_sn or not mac_address:
            msg = {'info': 'SN or MAC address is empty', 'status': 500}
            return jsonify(msg)

        session = inven_db.get_session()
        existing_client_device_by_mac = session.query(ClientDeviceInfo).filter(
            ClientDeviceInfo.mac_address == mac_address,
            ClientDeviceInfo.switch_sn == switch_sn,
            ClientDeviceInfo.port == port).first()

        if existing_client_device_by_mac:
            msg = {'info': 'Client device already exists', 'status': 500}
            return jsonify(msg)

        try:
            with session.begin(subtransactions=True):
                new_client_device = ClientDeviceInfo(
                    switch_sn=switch_sn,
                    mac_address=mac_address,
                    client_name=client_name,
                    terminal_type=terminal_type,
                    ip_address=ip_address,
                    manufacturer=get_organization_name_by_mac(mac_address),
                    state='offline',
                    update_time=None
                )
                session.add(new_client_device)
                msg = {'info': 'create client success', 'status': 200}
                return jsonify(msg)
        except Exception as e:
            session.rollback()
            LOG.error("Create client device fail")
            LOG.error(traceback.format_exc())
            return jsonify({'info': 'Create client device fail', 'status': 500})
    except Exception as e:
        LOG.error(traceback.format_exc())
        msg = {'info': 'create client fail', 'status': 500}
        return jsonify(msg)


@new_monitor_mold.route('/edit_client', methods=['POST'])
def edit_client():
    try:
        data = request.get_json()
        current_client_id = data.get("clientId", None)
        client_name = data.get("clientName", None)
        terminal_type = data.get("terminalType", None)
        ip_address=data.get("ipAddress", None)
        manufacturer=data.get("manufacturer", None)
        if not current_client_id:
            msg = {'info': 'Client ID is empty', 'status': 500}
            return jsonify(msg)
        session = inven_db.get_session()
        current_client_device = session.query(ClientDeviceInfo).filter(ClientDeviceInfo.id == current_client_id).first()
        if not current_client_device:
            msg = {'info': 'Client device not found', 'status': 500}
            return jsonify(msg)
        try:
            with session.begin(subtransactions=True):
                current_client_device.client_name = client_name
                current_client_device.terminal_type = terminal_type
                current_client_device.ip_address = ip_address
                current_client_device.manufacturer = manufacturer
                return jsonify({'info': 'Save client device success', 'status': 200})
        except Exception as e:
            session.rollback()
            LOG.error("Edit client device fail")
            LOG.error(traceback.format_exc())
            return jsonify({'info': 'Edit client device fail', 'status': 500})
    except Exception as e:
        LOG.error(traceback.format_exc())
        msg = {'info': 'edit client fail', 'status': 500}
        return jsonify(msg)

@new_monitor_mold.route('/delete_client', methods=['POST'])
def delete_client():
    try:
        data = request.get_json()
        current_client_id = data.get("clientId", None)
        if not current_client_id:
            msg = {'info': 'Client ID is empty', 'status': 500}
            return jsonify(msg)
        session = inven_db.get_session()
        current_client_device = session.query(ClientDeviceInfo).filter(ClientDeviceInfo.id == current_client_id).first()
        if not current_client_device:
            msg = {'info': 'Client device not found', 'status': 500}
            return jsonify(msg)
        try:
            with session.begin(subtransactions=True):
                session.delete(current_client_device)
                msg = {'info': 'Delete client device success', 'status': 200}
                return jsonify(msg)
        except Exception as e:
            session.rollback()
            LOG.error("Delete client device fail")
            LOG.error(traceback.format_exc())
            return jsonify({'info': 'Delete client device fail', 'status': 500})
    except Exception as e:
        LOG.error(traceback.format_exc())
        msg = {'info': 'delete client fail', 'status': 500}
        return jsonify(msg)


@new_monitor_mold.route('/get_switch_interface', methods=['POST'])
def get_switch_interface():
    try:
        data = request.get_json()
        fabric_list = data.get("fabric", [])
        role_list = data.get("role", [])

        all_switch_query = utils.query_switch(is_show_fabric=True)
        if fabric_list:
            all_switch_query = all_switch_query.filter(Fabric.fabric_name.in_(fabric_list))
        if role_list:
            all_switch_query = all_switch_query.filter(DCFabricTopologyNode.type.in_(role_list))

        res = []
        for switch, fabric, role in all_switch_query.all():
            interface_list = prometheus_util.get_target_interfaces(switch.sn)
            if interface_list:
                info = {
                    "sn": switch.sn,
                    "hostname": switch.host_name,
                    "fabric": fabric,
                    "role": role,
                    "children": interface_list
                }
                res.append(info)

        msg = {'data': res, 'status': 200}
        return jsonify(msg)
    except Exception as e:
        LOG.error(traceback.format_exc())
        msg = {'info': 'Get interface info fail', 'status': 500}
        return jsonify(msg)
    
    
@new_monitor_mold.route('/get_role_dropdown', methods=['POST'])
def get_role_dropdown():
    try:
        data = request.get_json()
        fabric_list = data.get("fabric", [])
        
        db_session = inven_db.get_session()
        
        if fabric_list:
            fabric_ids = list(map(lambda x: x[0], db_session.query(Fabric.id).filter(Fabric.fabric_name.in_(fabric_list)).all()))
        else:
            fabric_ids = list(map(lambda x: x[0], db_session.query(Fabric.id).all()))
        
        topo_ids = list(set(map(lambda x: x[0], db_session.query(DCFabricTopology.id).filter(DCFabricTopology.fabric_id.in_(fabric_ids)).all())))
        
        roles = list(set(map(lambda x: x[0], db_session.query(DCFabricTopologyNode.type).filter(DCFabricTopologyNode.fabric_topo_id.in_(topo_ids))
                                                                                        .filter(DCFabricTopologyNode.switch_sn != None)
                                                                                        .filter(DCFabricTopologyNode.switch_sn != '').all())))

        msg = {'data': roles, 'status': 200}
        return jsonify(msg)
    except Exception as e:
        LOG.error(traceback.format_exc())
        msg = {'info': 'Get interface info fail', 'status': 500}
        return jsonify(msg)
    

@new_monitor_mold.route('/get_ai_info_by_filter', methods=['POST'])
def get_ai_info_by_filter():
    try:
        data = request.get_json()
        filters = data.get("filters", [])
        topk = data.get("topK", None)
        start_time = data.get("startTime", None)
        end_time = data.get("endTime", None)

        if start_time:
            start_time = datetime.strptime(start_time, timestamp_format).timestamp()
        if end_time:
            end_time = datetime.strptime(end_time, timestamp_format).timestamp()

        result = []
        
        # 构造 PromQL 查询条件
        if filters:
            # 如果提供了过滤条件,构造对应的过滤器
            sns = [f.get("sn") for f in filters]
            interface_names = []
            for f in filters:
                if f.get("interfaceNames"):
                    interface_names.extend(f.get("interfaceNames"))
            
            target_filter = 'target=~"' + '|'.join(sns) + '"'
            interface_filter = ''
            if interface_names:
                interface_filter = ', interface_name=~"' + '|'.join(interface_names) + '"'
            
            pql = f'openconfig_qos:qos_interfaces_interface_state_fsconfig_qos_ai_extensions:ecn_marked_packets{{{target_filter}{interface_filter}}}'
        else:
            # 如果没有过滤条件,查询所有数据
            pql = 'openconfig_qos:qos_interfaces_interface_state_fsconfig_qos_ai_extensions:ecn_marked_packets'

        # 如果指定了 topK,添加 topk 函数
        if topk and isinstance(topk, int) and topk > 0:
            pql = f'topk({topk}, {pql})'
            
        active_targets = prometheus_util.query_prometheus(pql, time=end_time)
        
        # 构建新的 filters
        target_interfaces = {}
        for item in active_targets:
            target = item["metric"]["target"]
            interface_name = item["metric"]["interface_name"]
            if not interface_name.startswith("ae"):  # 过滤管理口
                if target not in target_interfaces:
                    target_interfaces[target] = []
                target_interfaces[target].append(interface_name)
        
        filters = [{"sn": target, "interfaceNames": interfaces} 
                  for target, interfaces in target_interfaces.items()]

        # 获取交换机的额外信息
        switch_info = {}
        all_switch_query = utils.query_switch(is_show_fabric=True)
        for switch, fabric, role in all_switch_query.all():
            switch_info[switch.sn] = {
                "hostname": switch.host_name,
                "fabric": fabric,
                "role": role
            }

        for filter_item in filters:
            sn = filter_item.get("sn")
            interface_names = filter_item.get("interfaceNames", [])
            
            # 如果没有指定接口名称,则获取该交换机的所有接口
            if not interface_names:
                interface_names = prometheus_util.get_target_interfaces(sn)
            
            # 获取 PFC 相关信息 
            pfc_info = prometheus_util.query_ai_with_prefix(
                "openconfig_qos:qos_interfaces_interface_output_queues_queue_state_fsconfig_qos_ai_extensions:",
                sn,
                interface_names,
                end_time
            )
            
            # 获取 ECN 相关信息
            ecn_info = prometheus_util.query_ai_with_prefix(
                "openconfig_qos:qos_interfaces_interface_state_fsconfig_qos_ai_extensions:",
                sn,
                interface_names,
                end_time
            )

            # 合并 PFC 和 ECN 信息
            merged_infos = [
                {
                    "sn": sn,
                    "hostname": switch_info.get(sn, {}).get("hostname", ""),
                    "fabric": switch_info.get(sn, {}).get("fabric", ""),
                    "role": switch_info.get(sn, {}).get("role", ""),
                    **pfc_info.get(interface, {}).get(q, {}),
                    **{'queue_name': q},
                    **{'interface_name': interface},
                    **ecn_info.get(interface, {})
                }
                for interface in interface_names
                for q in set(pfc_info.get(interface, {}))
            ]

            # 按接口名称和队列名称排序
            sorted_merged_infos = sorted(
                merged_infos,
                key=lambda x: (x['interface_name'], x['queue_name'])
            )

            result.extend(sorted_merged_infos)

        msg = {'data': result, 'status': 200}
        return jsonify(msg)
    except Exception as e:
        LOG.error(traceback.format_exc())
        msg = {'info': 'Get ai info fail', 'status': 500}
        return jsonify(msg)

@new_monitor_mold.route('/get_route_info', methods=['POST'])
def get_route_info():
    """Get routing information for a specific target switch.
    
    Args:
        target: The switch SN to query routes for
        date: Optional timestamp to query historical data
        
    Returns:
        JSON response containing:
        - List of route information including destination, protocol, next hops etc.
        - Each route contains basic info, route details and next hop information
    """
    try:
        data = request.get_json()
        target = data.get("target", None) 
        date = data.get("date", None)

        if not target:
            msg = {'info': 'Target is empty', 'status': 500}
            return jsonify(msg)
        
        if date:
            date = datetime.strptime(date, "%Y-%m-%d %H:%M:%S").timestamp()

        # Base PQL prefix for route queries
        base_pql = 'openconfig_network_instance:network_instances_network_instance_protocols_protocol_fsconfig_network_instance_route_extensions:'
        
        # Query route details and next hops in parallel
        route_queries = [
            (f'{base_pql}routes_route{{target="{target}"}}', 'routes'),
            (f'{base_pql}routes_route_next_hops_next_hop{{target="{target}"}}', 'next_hops')
        ]
        
        results = {}
        for query, key in route_queries:
            results[key] = prometheus_util.query_prometheus(query, time=date)

        # Create route key from metric for consistent mapping
        def get_route_key(metric):
            return (
                metric.get("routes_destination_network"),
                metric.get("protocol_name"), 
                metric.get("protocol_identifier")
            )

        # Process routes and build route information map
        routes_map = {}
        for route in results['routes']:
            metric = route["metric"]
            route_key = get_route_key(metric)
            
            routes_map[route_key] = {
                "destination": route_key[0],
                "protocol_name": route_key[1],
                "protocol": route_key[2].replace("openconfig-policy-types:", "") if route_key[2] else "",
                "network_instance": metric.get("network_instance_name", ""),
                "target": target,
                "metric": metric.get("metric", ""),
                "route_preference": metric.get("route_preference", ""),
                "route_type": metric.get("route_type", "").replace("openconfig-policy-types:", ""),
                "uptime": metric.get("uptime", ""),
                "next_hops": []
            }

        # Add next hop information to corresponding routes
        for next_hop in results['next_hops']:
            metric = next_hop["metric"]
            route_key = get_route_key(metric)
            
            if route_key in routes_map:
                routes_map[route_key]["next_hops"].append({
                    "id": metric.get("next_hop_id", ""),
                    "outgoing_interface": metric.get("outgoing_interface", ""),
                    "ip_address": metric.get("ip_address", "-")
                })

        # Convert map to sorted list
        route_info = sorted(routes_map.values(), key=lambda x: x["destination"])

        msg = {'data': route_info, 'status': 200}
        return jsonify(msg)
    except Exception as e:
        LOG.error(traceback.format_exc())
        msg = {'info': 'Get route info fail', 'status': 500}
        return jsonify(msg)


@new_monitor_mold.route('/get_mlag_info', methods=['POST'])
def get_mlag_info():
    try:
        data = request.get_json()
        target = data.get("target", None)
        date = data.get("date", None)

        if not target:
            msg = {'info': 'Target is empty', 'status': 500}
            return jsonify(msg)

        if date:
            date = datetime.strptime(date, "%Y-%m-%d %H:%M:%S").timestamp()

        data = prometheus_util.query_mlag_info_by_target(target, date)

        return jsonify({'data': data, 'status': 200})
    except Exception as e:
        LOG.error(traceback.format_exc())
        msg = {'info': 'Get mlag info fail', 'status': 500}
        return jsonify(msg)

@new_monitor_mold.route('/get_poe_info', methods=['POST'])
def get_poe_info():
    try:
        data = request.get_json()
        target = data.get("target", None)
        date = data.get("date", None)

        if not target:
            msg = {'info': 'Target is empty', 'status': 500}
            return jsonify(msg)

        if date:
            date = datetime.strptime(date, "%Y-%m-%d %H:%M:%S").timestamp()

        data = prometheus_util.query_poe_info_by_target(target, date)

        return jsonify({'data': data, 'status': 200})
    except Exception as e:
        LOG.error(traceback.format_exc())
        msg = {'info': 'Get poe info fail', 'status': 500}
        return jsonify(msg)



@new_monitor_mold.route('/get_modules_link', methods=['POST'])
def get_modules_link():
    try:
        data = request.get_json()
        res = []
        try:
            session = monitor_db.get_session()

            query_link = session.query(ModulesLink).filter(ModulesLink.link_status == True).all()

            for link in query_link:
                link_info={
                    "source_sn": link.source_sn,
                    "source_port": link.source_port,
                    "target_sn": link.target_sn,
                    "target_port": link.target_port,
                    "light_attenuation_threshold": link.light_attenuation_threshold
                }
                res.append(link_info)

        except Exception as e:
            LOG.error(traceback.format_exc())

        msg = {'data': res, 'status': 200}
        return jsonify(msg)
    except Exception as e:
        LOG.error(traceback.format_exc())
        msg = {'info': 'Get modules link', 'status': 500}
        return jsonify(msg)

@new_monitor_mold.route('/update_modules_link_threshold', methods=['POST'])
def update_modules_link_threshold():
    try:
        data = request.get_json()
        link_threshold = data.get("link_threshold", [])

        session = monitor_db.get_session()
        with session.begin(subtransactions=True):
            for link in link_threshold:
                session.query(ModulesLink).filter(ModulesLink.source_sn == link["source_sn"], ModulesLink.target_sn == link["target_sn"], ModulesLink.source_port == link["source_port"],
                                                    ModulesLink.target_port == link["target_port"]).update({"light_attenuation_threshold": link["light_attenuation_threshold"]})

        msg = {'info': 'update modules link threshold success', 'status': 200}
        return jsonify(msg)
    except Exception as e:
        LOG.error(traceback.format_exc())
        msg = {'info': 'update modules link threshold fail', 'status': 500}
        return jsonify(msg)

@new_monitor_mold.route('/get_modules_count', methods=['POST'])
def get_modules_count():
    msg = {}
    try:
        data = request.get_json()
        target = data.get("target", None)
        res = prometheus_util.get_modules_count(target=target)
        msg = {'data': res, 'status': 200}
    except Exception as e:
        LOG.error(traceback.format_exc())
        msg = {'info': 'get modules count fail', 'status': 500}
    finally:
        return jsonify(msg)

@new_monitor_mold.route('/get_modules_history_info', methods=['POST'])
def get_modules_history_info():
    msg = {}
    try:
        data = request.get_json()
        target = data.get("target", None)
        start_time = data.get("startTime", None)
        if start_time:
            start_time = datetime.strptime(start_time, timestamp_format).timestamp()
        end_time = data.get("endTime", None)
        if end_time:
            end_time = datetime.strptime(end_time, timestamp_format).timestamp()
        res = prometheus_util.get_modules_history_info(target=target, start_time=start_time, end_time=end_time)
        msg = {'data': res, 'status': 200}
    except Exception as e:
        LOG.error(traceback.format_exc())
        msg = {'info': 'get modules history info fail', 'status': 500}
    finally:
        return jsonify(msg)

@new_monitor_mold.route('/get_modules_port_status', methods=['POST'])
def get_modules_port_status():
    msg = {}
    try:
        data = request.get_json()
        target = data.get("target")
        port = data.get("port")
        metric = data.get("metric")
        channel_index = data.get("channel_index")
        start_time = data.get("startTime", None)
        if start_time:
            start_time = datetime.strptime(start_time, timestamp_format).timestamp()
        end_time = data.get("endTime", None)
        if end_time:
            end_time = datetime.strptime(end_time, timestamp_format).timestamp()
        res = prometheus_util.get_modules_port_status(target=target, port=port, metric=metric, start_time=start_time,
                                                      end_time=end_time, channel_index=channel_index)
        msg = {'data': res, 'status': 200}
    except Exception as e:
        LOG.error(traceback.format_exc())
        msg = {'info': 'get modules port status fail', 'status': 500}
    finally:
        return jsonify(msg)

@new_monitor_mold.route('/get_device_ddm_info', methods=['POST'])
def get_device_ddm_info():
    try:
        data = request.get_json()
        sn = data.get("sn", None)
        startTime = data.get("startTime", None)
        endTime = data.get("endTime", None)
        level = data.get("level", None)
        is_echarts = data.get("isEcharts", False)
        session = monitor_db.get_session()
        query = session.query(DDMEvents)
        filter = ""
        if sn:
            filter = f'target=~"{"|".join(sn)}"'
            if isinstance(sn, list):
                query = query.filter(DDMEvents.switch_sn.in_(sn))
            else:
                query = query.filter(DDMEvents.switch_sn == sn)

        if startTime and endTime:
            query = query.filter(or_(DDMEvents.create_time <= endTime, DDMEvents.last_alert_time >= startTime))

        if level:
            query = query.filter(DDMEvents.alert_level == level)

        result = query.all()
        session.close()

        ddm_status = {}
        pql = f'openconfig_platform:components_component_openconfig_platform_transceiver:transceiver_state{{{filter}}}'
        pro_result = prometheus_util.query_prometheus(pql)
        for metric in pro_result:
            sn = metric['metric']['target']
            interface_name = metric['metric']['interface_name']
            if sn not in ddm_status:
                ddm_status[sn] = []
            ddm_status[sn].append(interface_name)
        # Group events by interface, alert_type, and channel
        grouped_events = {}
        for event in result:
            key = (event.switch_sn, event.interface, event.alert_type, event.channel)
            if key not in grouped_events or not grouped_events[key].resolved:
                grouped_events[key] = event
            elif grouped_events[key].resolved and event.modified_time > grouped_events[key].modified_time:
                grouped_events[key] = event

        switch_sns = list({event.switch_sn for event in grouped_events.values()})

        session = monitor_db.get_session()
        switches = session.query(Switch.sn, Switch.platform_model, Switch.host_name, Switch.mgt_ip).filter(
            Switch.sn.in_(switch_sns)).all()
        session.close()

        sn_info = {
            sw.sn: {
                'platform_model': sw.platform_model,
                'host_name': sw.host_name,
                'mgt_ip': sw.mgt_ip
            } for sw in switches
        }

        if is_echarts:
            unresolved_events = [event for event in grouped_events.values() if not event.resolved]
            seen_keys = set()
            echarts_data = []
            for event in unresolved_events:
                key = (event.switch_sn, event.interface)
                if key in seen_keys:
                    continue
                seen_keys.add(key)
                echarts_data.append({
                    'sn': event.switch_sn,
                    'platform_model': sn_info.get(event.switch_sn, {}).get('platform_model'),
                    'host_name': sn_info.get(event.switch_sn, {}).get('host_name'),
                    'mgt_ip': sn_info.get(event.switch_sn, {}).get('mgt_ip'),
                    'speed': event.module_type,
                    'interface': event.interface,
                    'alert_level': event.alert_level
                })

            return jsonify({'data': echarts_data, 'ddm_status': ddm_status, 'status': 200})
        else:
            res_data = [{
                'sn': event.switch_sn,
                'speed': event.module_type,
                'anomaly_time': event.modified_time.strftime(timestamp_format),
                'alert_level': event.alert_level,
                'count': event.count,
                'alert_type': event.alert_type,
                'alert_msg': event.alert_msg,
                'channel': event.channel,
                'resolved': event.resolved,
                'interface': event.interface
            } for event in grouped_events.values()]

            return jsonify({'data': res_data, 'ddm_status': ddm_status, 'status': 200})
    except Exception as e:
        LOG.error(traceback.format_exc())
        msg = {'info': str(e), 'status': 500}
        return jsonify(msg)

@new_monitor_mold.route('/get_interface_ddm_info', methods=['POST'])
def get_interface_ddm_info():
    try:
        data = request.get_json()
        sn = data.get("sn", None)
        interface = data.get("interfaceName", None)
        session = monitor_db.get_session()
        query = session.query(DDMEvents).filter(DDMEvents.switch_sn == sn, DDMEvents.interface == interface, DDMEvents.resolved == True)
        result = query.all()
        session.close()
        res_data = [{
            'resolved_time': event.resolved_time.strftime(timestamp_format),
            'alert_level': event.alert_level,
            'count': event.count,
            'sysname': event.module_name,
            'interface': event.interface,
            'alert_type': event.alert_type,
            'channel': event.channel,
            'alert_msg': event.alert_msg
        } for event in result]
        return jsonify({'data': res_data, 'status': 200})
    except Exception as e:
        LOG.error(traceback.format_exc())
        msg = {'info': str(e), 'status': 500}
        return jsonify(msg)

@new_monitor_mold.route('/get_optical_fiber_link', methods=['POST'])
def get_optical_fiber_link():
    try:
        data = request.get_json()
        sn = data['sn']
        session = monitor_db.get_session()
        query = session.query(ModulesLink).filter(ModulesLink.source_sn == sn).all()
        session.close()
        res = {}
        for link in query:
            link_info = f'{link.source_sn}-{link.target_sn}'
            if link_info not in res:
                res[link_info] = []
                res[link_info].append({
                    "link_id": link.id,
                    "source_sn": link.source_sn,
                    "source_port": link.source_port,
                    "target_port": link.target_port,
                    "target_sn": link.target_sn,
                    "target_mac": link.target_mac,
                    "threshold": link.light_attenuation_threshold
                })
            else:
                res[link_info].append({
                    "link_id": link.id,
                    "source_sn": link.source_sn,
                    "source_port": link.source_port,
                    "target_port": link.target_port,
                    "target_sn": link.target_sn,
                    "target_mac": link.target_mac,
                    "threshold": link.light_attenuation_threshold
                })
        return jsonify({'data': res, 'status': 200})
    except Exception as e:
        LOG.error(traceback.format_exc())
        msg = {'info': str(e), 'status': 500}
        return jsonify(msg)

@new_monitor_mold.route('/get_light_attenuation_info', methods=['POST'])
def get_light_attenuation_info():
    msg = {}
    try:
        data = request.get_json()
        port_info = data['port_info']
        start_time = data.get("startTime", None)
        if start_time:
            start_time = datetime.strptime(start_time, timestamp_format).timestamp()
        end_time = data.get("endTime", None)
        if end_time:
            end_time = datetime.strptime(end_time, timestamp_format).timestamp()
        res = prometheus_util.get_light_attenuation(port_info=port_info, start_time=start_time, end_time=end_time)
        msg = {'data': res, 'status': 200}
    except Exception as e:
        LOG.error(traceback.format_exc())
        msg = {'info': 'get light attenuation info fail', 'status': 500}
    finally:
        return jsonify(msg)

@new_monitor_mold.route('/update_optical_links_threshold', methods=['POST'])
def update_optical_links_threshold():
    session = monitor_db.get_session()
    try:
        data = request.get_json()
        apply_type = data.get("apply_type", None)
        sn = data.get("sn", None)
        threshold = data.get("threshold", None)
        with session.begin(subtransactions=True):
            if apply_type == "all":
                query = session.query(ModulesLink).filter(ModulesLink.source_sn == sn).all()
                for link in query:
                    link.light_attenuation_threshold = threshold
            else:
                links_selected = data.get("links_selected", [])
                query = session.query(ModulesLink).filter(ModulesLink.id.in_(links_selected)).all()
                for link in query:
                    link.light_attenuation_threshold = threshold
            session.commit()
        return jsonify({'info': 'update optical links threshold success', 'status': 200})
    except Exception as e:
        LOG.error(traceback.format_exc())
        msg = {'info': 'update optical links threshold fail', 'status': 500}
        return jsonify(msg)

@new_monitor_mold.route('/get_device_ddm_table', methods=['POST'])
def get_device_ddm_table():
    try:
        params = request.get_json()
        sn = params.get("sn", None)
        alert_level = params.get("alertLevel", None)
        start_time = params.get("startTime", None)
        end_time = params.get("endTime", None)

        pql = 'openconfig_platform:components_component_openconfig_platform_transceiver:transceiver_state{target="' + sn + '"}'
        pro_result = prometheus_util.query_prometheus(pql)
        online_interfaces = list(set([metric['metric']['interface_name'] for metric in pro_result]))

        from sqlalchemy import or_, and_, func, case
        session = monitor_db.get_session()

        base_query = session.query(DDMEvents).filter(
            DDMEvents.switch_sn == sn,
            DDMEvents.interface.in_(online_interfaces)
        )

        if alert_level:
            base_query = base_query.filter(DDMEvents.alert_level == alert_level)
        if start_time and end_time:
            base_query = base_query.filter(
                or_(
                    and_(DDMEvents.resolved.is_(True), DDMEvents.resolved_time > start_time, DDMEvents.resolved_time < end_time),
                    and_(DDMEvents.resolved.is_(True), DDMEvents.resolved_time > end_time, DDMEvents.create_time < end_time),
                    and_(DDMEvents.resolved.is_(False), DDMEvents.create_time < end_time)
                )
            )


        id_subquery = session.query(
            DDMEvents.id,
            func.row_number().over(
                partition_by=[DDMEvents.interface, DDMEvents.alert_type, DDMEvents.channel],
                order_by=[
                    case([(DDMEvents.resolved.is_(False), 0)], else_=1),
                    DDMEvents.modified_time.desc()
                ]
            ).label('row_num')
        ).filter(
            DDMEvents.switch_sn == sn,
            DDMEvents.interface.in_(online_interfaces)
        )


        if alert_level:
            id_subquery = id_subquery.filter(DDMEvents.alert_level == alert_level)
        if start_time and end_time:
            id_subquery = id_subquery.filter(
                or_(
                    and_(DDMEvents.resolved.is_(True), DDMEvents.resolved_time > start_time, DDMEvents.resolved_time < end_time),
                    and_(DDMEvents.resolved.is_(True), DDMEvents.resolved_time > end_time, DDMEvents.create_time < end_time),
                    and_(DDMEvents.resolved.is_(False), DDMEvents.create_time < end_time)
                )
            )

        ranked_ids = id_subquery.subquery()

        selected_ids = session.query(ranked_ids.c.id).filter(ranked_ids.c.row_num == 1)

        pre_query = session.query(DDMEvents).filter(DDMEvents.id.in_(selected_ids))

        page, pageSize, total_count, query_results = utils.query_helper(DDMEvents, pre_query=pre_query)

        res = []
        for event in query_results:
            event_dict = event.make_dict()
            event_dict['modified_time'] = event.modified_time.strftime(timestamp_format)
            res.append(event_dict)

        session.close()
        return jsonify({
            'status': 200,
            'data': res,
            'page': page,
            'pageSize': pageSize,
            'total': total_count
        })
    except Exception as e:
        LOG.error(traceback.format_exc())
        msg = {'info': 'get device ddm table fail', 'status': 500}
        return jsonify(msg)