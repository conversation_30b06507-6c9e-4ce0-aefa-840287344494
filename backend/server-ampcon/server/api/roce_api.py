import json
import logging
import traceback
import threading
from flask import Blueprint, jsonify, Response, request
from datetime import timedelta, datetime, date
from jinja2 import Environment, FileSystemLoader
import os
from sqlalchemy.orm import aliased
import copy
from sqlalchemy import func, Text, or_, text
import re
from functools import wraps

from server.util.permission import admin_permission, readonly_permission
from server.db.models.campus_blueprint import campus_blueprint_db, CampusTopologyConfig
from server.db.models import inventory
from server.db.models.inventory import inven_db
from server.db.models.roce import RoceDBCommon, PfcConfiguration, RoceEasyDeployConfiguration, DLBConfiguration, \
    PfcWdConfiguration, EcnConfiguration, EcnConfigurationDetail, QosConfiguration, QosIngressConfiguration, \
        QosEgressConfiguration, PfcBufferIngressConfiguration, PfcBufferEgressConfiguration, roce_db
from server.db.models.automation import AnsibleDevice, RoceTask
from ansible_lib.ansible_utils import execute_roce_script, roce_start_task_ansible
from server.util.prometheus_util import query_lldp_state
from celery_app import my_celery_app
from server.celery_app.roce_task import roce_check_task
from server.util import utils
from server.config_distribution import config_deploy
from server.db.models.inventory import Switch
from server.db.models.inventory import AssociationFabric, Fabric

roce_mold = Blueprint("roce", __name__, template_folder="templates")
LOG = logging.getLogger(__name__)

@roce_mold.route("/server/configure", methods=["POST"])
@admin_permission.require(http_exception=403)
def server_configure():
    """
    {
        "portInfo": {
            "device_id": ["port_name", ]
        }
        "device_type": "nvidia",
        "script_params": {
            "pfc_param": "0,0,0,1,0,0,0,0",
            "dscp_param": "30,3",
            "ecn_np_param": "3",
            "ecn_rp_param": "3",
            "roce_np": "48",
            "mtu": "4096",
            "cma_roce_tos": "120",
            "traffic_class": "120"
        }
    }
    """
    try:
        data = request.get_json()
        if not data:
            return jsonify({"info": "Invalid request data", "status": 400})

        port_info = data.get("portInfo", {})
        script_params = data.get("script_params")
        device_type = data.get("device_type", "nvidia")

        # 设置模板目录
        template_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), "monitor", "roce")
        env = Environment(loader=FileSystemLoader(template_dir))

        if device_type == "nvidia":
            # 获取模板
            template = env.get_template("nvidia_configure.j2")

            # 渲染模板，添加脚本头
            script_content = template.render(**script_params)

        elif device_type == "broadcom":
            # 获取模板
            template = env.get_template("broadcom_configure.j2")

            # 渲染模板，添加脚本头
            script_content = template.render(**script_params)

        session = inven_db.get_session()
        available_device_ids = list(map(lambda x: x.id, utils.query_host().all()))
        for device_id, port in port_info.items():
            # 获取设备信息
            device = session.query(AnsibleDevice).filter(AnsibleDevice.id == device_id, 
                                                         AnsibleDevice.id.in_(available_device_ids)).first()
            if not device:
                continue
            port_str= ' '.join(port)
            shell_env = ""
            if device_type == "nvidia":
                shell_env = f"ibdev='{port_str}'"
            elif device_type == "broadcom":
                shell_env = f"ethdev='{port_str}'"
            # 执行脚本
            my_celery_app.send_task("execute_roce_script",
                                    kwargs={
                                        "device_id": device.id,
                                        "type": "RoCE Script",
                                        "scripts": script_content,
                                        "task_name": f"Roce_Script:{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}:{device.device_name}",
                                        "shell_env": shell_env
                                    })

        return jsonify({"info": "Task submitted successfully", "status": 200})

    except Exception as e:
        LOG.error(f"Error in server_configure: {str(e)}")
        LOG.error(traceback.format_exc())
        return jsonify({"info": "Task submitted failed", "status": 400})


@roce_mold.route("/template/preview", methods=["POST"])
@admin_permission.require(http_exception=403)
def template_preview():
    """
    {
        "sever_type": "nvida"
    }
    """
    try:
        data = request.get_json()
        if not data:
            return Response("Invalid request data", status=400)

        # 设置模板目录
        template_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), "monitor", "roce")

        server_type = data.get("server_type")
        if server_type == "nvidia":
            script_params = {
                "pfc_param": "0,0,0,1,0,0,0,0",
                "dscp_param": "30,3",
                "cnp_802p_prio": "3",
                "roce_np": "48"
            }

            validation_rules = {
                "cnp_802p_prio": {"min": 0, "max": 7},
                "roce_np": {"min": 0, "max": 63},
                "dscp_param": {"pattern": r"^(6[0-3]|[0-5]?\d),(7|[0-6])$"},
                "pfc_param" : {"pattern": r"^\s*(0|1)(?:\s*,\s*(0|1)){7}\s*$"}
            }

            with open(os.path.join(template_dir, "nvidia_configure.j2"), 'r') as file:
                template_text = file.read()

            return jsonify({"data": {"template": template_text, "default_param": script_params, "validation_rules": validation_rules}, "status": 200})


        elif server_type == "broadcom":
            script_params = {
                "m_param": "3",
                "s_param": "26",
                "p_param": "48",
                "r_param": "3",
                "c_param": "7"
            }

            validation_rules = {
                "m_param": {"min": 1, "max": 3},
                "s_param": {"min": 0, "max": 63},
                "p_param": {"min": 0, "max": 63},
                "r_param": {"min": 0, "max": 7},
                "c_param": {"min": 0, "max": 7}
            }

            with open(os.path.join(template_dir, "broadcom_configure.j2"), 'r') as file:
                template_text = file.read()

            return jsonify({"data": {"template": template_text, "default_param": script_params,
                                     "validation_rules": validation_rules}, "status": 200})

        else:
            return Response("Unsupported server type", status=400)

    except Exception as e:
        LOG.error(f"Error in server_configure: {str(e)}")
        LOG.error(traceback.format_exc())
        return Response(f"Internal server error: {str(e)}", status=500)


@roce_mold.route("/server/roce_check_api", methods=["GET"])
@admin_permission.require(http_exception=403)
def roce_check_api():
    db_session = inven_db.get_session()
    try:
        id = request.args.get('id')
        ansibleDevice = db_session.query(AnsibleDevice).filter(AnsibleDevice.id == id).first()
        if ansibleDevice:
            info_dict = {
                "device_id": ansibleDevice.id,
                "device_ip": ansibleDevice.ip,
                "device_name": ansibleDevice.device_name,
                "device_user": ansibleDevice.device_user,
                "device_pwd": ansibleDevice.device_pwd,
                "task_name": f"check_{ansibleDevice.device_name}_{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
                "type": "check",
                "status": "RUNNING",
                "result": ""
            }
            roce_check_task.delay(**info_dict)
            return jsonify({
                "code": 200,
                'msg': 'success',
                "data": None
            })
        else:
            return jsonify({
                "code": 404,
                'msg': 'No ansible_device record corresponding to this ID was found',
                "data": None
            })
    except Exception as e:
        return jsonify({"error": str(e)}), 500
    finally:
        db_session.close()


@roce_mold.route("/event/get_roce_task_table", methods=["POST"])
@readonly_permission.require(http_exception=403)
def get_roce_task_table():
    db_session = inven_db.get_session()
    available_device_ids = list(map(lambda x: x.id, utils.query_host().all()))
    roce_task = db_session.query(RoceTask).filter(RoceTask.type != "check", RoceTask.device_id.in_(available_device_ids))
    page_num, page_size, total_count, query_task = utils.query_helper(RoceTask, pre_query=roce_task)
    res = []
    for task in query_task:
        info = {
            "id": task.id,
            "device_ip": task.device.ip,
            "device_name": task.device.device_name,
            "device_user": task.device.device_user,
            "type": task.type,
            "status": task.status,
            "create_time": task.create_time.strftime("%Y-%m-%d %H:%M:%S")
        }
        res.append(info)
    return jsonify({"data": res, "page": page_num,
                    "pageSize": page_size,
                    "total": total_count,
                    "status": 200})


@roce_mold.route("/event/get_result", methods=["POST"])
@readonly_permission.require(http_exception=403)
def get_result():
    try:
        data = request.get_json()
        if not data:
            return jsonify({"info": "Invalid request data", "status": 400})

        task_id = data.get("task_id")
        session = inven_db.get_session()
        available_device_ids = list(map(lambda x: x.id, utils.query_host().all()))
        task = session.query(RoceTask).filter(RoceTask.id == task_id, RoceTask.device_id.in_(available_device_ids)).first()
        if task:
            return jsonify({"data": task.result, "status": 200})
        else:
            return jsonify({"info": "task result not found", "status": 400})

    except Exception as e:
        LOG.error(traceback.format_exc())
        return jsonify({"info": "get task result failed", "status": 500})


@roce_mold.route("/server/roce_batch_check_api", methods=["POST"])
@admin_permission.require(http_exception=403)
def roce_batch_check_api():
    db_session = inven_db.get_session()
    try:
        request_json = request.get_json()
        id_list = request_json.get("id_list")
        check_config_list = request_json.get("check_config")
        if id_list:
            for id in id_list:
                ansibleDevice = db_session.query(AnsibleDevice).filter(AnsibleDevice.id == id).first()
                if ansibleDevice:
                    info_dict = {
                        "device_id": ansibleDevice.id,
                        "device_ip": ansibleDevice.ip,
                        "device_name": ansibleDevice.device_name,
                        "device_user": ansibleDevice.device_user,
                        "device_pwd": ansibleDevice.device_pwd,
                        "task_name": f"check_{ansibleDevice.device_name}_{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
                        "type": "check",
                        "status": "RUNNING",
                        "result": ""
                    }
                    if "all" in check_config_list:
                        roce_check_task.delay(**info_dict)
                        # TODO: check configuration 检查项预留
                    else:
                        if "roce" in check_config_list:
                            roce_check_task.delay(**info_dict)
                        if "other" in check_config_list:
                            pass
            return jsonify({
                "code": 200,
                'msg': 'success',
                "data": None
            })
        else:
            return jsonify({
                "code": 404,
                'msg': 'id_list is empty.',
                "data": None
            })
    except Exception as e:
        return jsonify({"error": str(e)}), 500
    finally:
        db_session.close()

@roce_mold.route("/server/get_roce_check_result", methods=["GET"])
@admin_permission.require(http_exception=403)
def get_roce_check_result():
    db_session = inven_db.get_session()
    try:
        id = request.args.get("id")
        task = (db_session.query(RoceTask)
                .filter(RoceTask.device_id == id, RoceTask.type == "check")
                .order_by(RoceTask.create_time.desc())
                .first())
        if task:
            return jsonify({
                "status": 200,
                "info": 'success',
                "data": task.create_time.strftime("%Y-%m-%d %H:%M:%S") + "\n" + task.result
            })
        else:
            return jsonify({
                "status": 400,
                "info": "task result not found.",
                "data": None
            })
    except Exception as e:
        LOG.error(traceback.format_exc())
        return jsonify({"info": "get check result failed", "status": 500})
    finally:
        db_session.close()


from flask_login import current_user

@roce_mold.route("/server/configuration_by_form", methods=["POST"])
@admin_permission.require(http_exception=403)
def roce_configuration_by_form():
    db_session = inven_db.get_session()
    try:
        data = request.get_json()
        if not data:
            return jsonify({"info": "Invalid request data", "status": 400})

        port_list = data.get("nic_ports", [])
        port_info = {}
        for item in port_list:
            prefix, port = item.split('-', 1)
            port_info.setdefault(prefix, []).append(port)

        script_params = data.get("script_params")
        device_type = data.get("nic_vendor", "")
        create_user = current_user.id

        dispatched_devices = []

        for device_id, ports in port_info.items():
            ansibleDevice = db_session.query(AnsibleDevice).filter(AnsibleDevice.id == device_id).first()
            if not ansibleDevice:
                continue

            task_name = f"RoCEv2_Config_for_{ansibleDevice.device_name}_{datetime.now().strftime('%Y-%m-%d %H:%M:%S.%f')}"

            info_dict = {
                "device_id": ansibleDevice.id,
                "device_ip": ansibleDevice.ip,
                "device_name": ansibleDevice.device_name,
                "device_user": ansibleDevice.device_user,
                "device_pwd": ansibleDevice.device_pwd,
                "task_name": task_name,
                "type": "RoCE Form",
                "status": "RUNNING",
                "result": "",
                "device_ssh_key_path": ansibleDevice.device_ssh_key_path,
                "device_port": ansibleDevice.device_port,
                "device_sudo_pass": ansibleDevice.device_pwd,
                "create_user": create_user,
                "target_ports": ports,
                "device_type": device_type,
                "script_params": script_params
            }
            task_result = roce_start_task_ansible.apply_async(kwargs=info_dict)
            dispatched_devices.append(ansibleDevice.device_name)

        if dispatched_devices:
            return jsonify({
                "status": 200,
                "info": f"The RoCEv2 Configration execute success in background on {', '.join(dispatched_devices)}",
                "job_name": info_dict.get("task_name")
            })
        else:
            return jsonify({
                "status": 404,
                "info": "No valid device found to RoCEv2 Configration.",
            })

    except Exception as e:
        return jsonify({"error": str(e)}), 500
    finally:
        db_session.close()

@roce_mold.route("/server/roce_batch_check_ansible_api", methods=["POST"])
@admin_permission.require(http_exception=403)
def roce_batch_check_ansible_api():
    db_session = inven_db.get_session()
    try:
        request_json = request.get_json()
        id_list = request_json.get("id_list")
        check_config_list = request_json.get("check_config")
        create_user = current_user.id
        if id_list:
            for id in id_list:
                ansibleDevice = db_session.query(AnsibleDevice).filter(AnsibleDevice.id == id).first()
                if ansibleDevice:
                    info_dict = {
                        "device_id": ansibleDevice.id,
                        "device_ip": ansibleDevice.ip,
                        "device_name": ansibleDevice.device_name,
                        "device_user": ansibleDevice.device_user,
                        "device_pwd": ansibleDevice.device_pwd,
                        "task_name": f"check_{ansibleDevice.device_name}_{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
                        "type": "check",
                        "status": "RUNNING",
                        "result": "",
                        "device_ssh_key_path": ansibleDevice.device_ssh_key_path,
                        "device_port": ansibleDevice.device_port,
                        "device_sudo_pass": ansibleDevice.device_pwd,
                        "create_user": create_user
                    }
                    from server.ansible_lib.ansible_utils import roce_check_task_ansible
                    if "all" in check_config_list:
                        roce_check_task_ansible.delay(**info_dict)
                        # TODO: check configuration 检查项预留
                    else:
                        if "roce" in check_config_list:
                            roce_check_task_ansible.delay(**info_dict)
                        if "other" in check_config_list:
                            pass
            return jsonify({
                "code": 200,
                'msg': 'success',
                "data": {}
            })
        else:
            return jsonify({
                "code": 404,
                'msg': 'id_list is empty.',
                "data": None
            })
    except Exception as e:
        return jsonify({"error": str(e)}), 500
    finally:
        db_session.close()


def api_exception_handler(f):
    """
    Exception handler decorator for API view layer
    Provides unified exception handling and response formatting
    """
    @wraps(f)
    def wrapper(*args, **kwargs):
        try:
            return f(*args, **kwargs)
        except Exception as e:
            LOG.error(f"Unexpected error in {f.__name__}: {str(e)}", exc_info=True)
            return jsonify({
                'status': 400,
                'msg': f'Error: {str(e)}',
                'data': None
            })
    return wrapper

@roce_mold.route('/get_fabric_switches', methods=["POST"])
@admin_permission.require(http_exception=403)
@api_exception_handler
def get_fabric_switches():
    """
    Get fabric switches information
    Returns:
        JSON response with fabric switches data organized by fabric
    """
    
    # Get fabric switches data from service layer
    fabric_switches_data = roce_db.get_fabric_switches_data()
    
    return jsonify({
        "status": 200,
        "data": fabric_switches_data
    })

@roce_mold.route('/get_ports_by_switch_sn', methods=['POST'])
@admin_permission.require(http_exception=403)
@api_exception_handler
def get_ports_by_switch_sn():
    """
    Get ports by switch serial number
    Returns:
        JSON response with port data
    """
    data = request.get_json()
    switch_sn = data.get("switch_sn")
    
    # Get ports data from service layer
    port_list = roce_db.get_ports_by_switch_sn_data(switch_sn)
    
    return jsonify({
        "status": 200,
        "data": port_list
    })
    
@roce_mold.route('/get_filter_ports_by_switch_sn', methods=['POST'])
@admin_permission.require(http_exception=403)
@api_exception_handler
def get_filter_ports_by_switch_sn():
    """
    Get filtered (unused) ports by switch serial number and configuration model
    Returns:
        JSON response with filtered port data
    """
    data = request.get_json()
    switch_sn = data.get("switch_sn")
    query_model = data.get("query_model")
    
    # Get filtered ports data from service layer
    port_list = roce_db.get_filter_ports_by_switch_sn_data(switch_sn, query_model)
    
    return jsonify({
        "status": 200,
        "data": port_list
    })

@roce_mold.route('/get_filter_queues_by_switch_sn', methods=['POST'])
@admin_permission.require(http_exception=403)
@api_exception_handler
def get_filter_queues_by_switch_sn():
    data = request.get_json()
    switch_sn = data.get("switch_sn")
    query_model = data.get("query_model")
    
    # Get filtered ports data from service layer
    queue_list = roce_db.get_filter_queues_by_switch_sn_data(switch_sn, query_model)
    
    return jsonify({
        "status": 200,
        "data": queue_list
    })

def deploy_roce_configuration(switch, config_data):
    """
    Deploy RoCE configuration to switch
    Args:
        switch: Switch object
        config_data: dict, Configuration data to deploy
    Returns:
        dict: Deployment result
    """
    from server.config_distribution.config_deploy import CLIConfigDeployer
    
    cli_deployer = CLIConfigDeployer(
        host=switch.mgt_ip,
        username=switch.system_config.switch_op_user,
        password=switch.system_config.switch_op_password,
        port=22
    )
    
    return cli_deployer.deploy_cli_roce_config(**config_data)

def validate_easydeploy_uniqueness(f):
    """
    装饰器：验证 RoceEasyDeployConfiguration 的唯一性
    - 确保一台交换机只能有一条记录
    - config_id 为空时创建，不为空时更新
    - 创建时如果记录已存在返回 400 错误
    """
    @wraps(f)
    def decorated_function(*args, **kwargs):
        try:
            data = request.get_json()
            if not data:
                return jsonify({
                    "status": 400,
                    "msg": "No configuration data provided"
                })

            configurations = data.get("configurations", [])
            if not configurations:
                return jsonify({
                    "status": 400,
                    "msg": "No configurations provided"
                })

            session = roce_db.get_session()
            
            for config in configurations:
                switch_sns = config.get("switch_sn")
                config_id = config.get("config_id")
                
                if not switch_sns:
                    return jsonify({
                        "status": 400,
                        "msg": "switch_sn is required"
                    })

                for switch_sn in switch_sns:
                    # 查询是否已存在该交换机的配置
                    existing_config = session.query(RoceEasyDeployConfiguration).filter_by(
                        switch_sn=switch_sn
                    ).first()

                    if not config_id or config_id == "":
                        # 创建操作：检查是否已存在记录
                        if existing_config:
                            return jsonify({
                                "status": 400,
                                "msg": "config easydeploy exists"
                            })
                    else:
                        # 更新操作：检查 config_id 是否存在且属于该交换机
                        target_config = session.query(RoceEasyDeployConfiguration).filter_by(
                            id=config_id
                        ).first()
                        
                        if not target_config:
                            return jsonify({
                                "status": 404,
                                "msg": f"Configuration with ID {config_id} not found"
                            })
                        
                        if target_config.switch_sn != switch_sn:
                            return jsonify({
                                "status": 400,
                                "msg": f"Configuration ID {config_id} does not belong to switch {switch_sn}"
                            })

            # 验证通过，执行原函数
            return f(*args, **kwargs)
            
        except Exception as e:
            LOG.error(f"Validation failed: {str(e)}")
            LOG.error(traceback.format_exc())
            return jsonify({
                "status": 500,
                "msg": f"Validation failed: {str(e)}"
            })
    
    return decorated_function

@roce_mold.route("/easydeploy_config/validate", methods=["POST"])
@admin_permission.require(http_exception=403)
@validate_easydeploy_uniqueness  # 复用现有装饰器进行校验
def roce_easydeploy_config_validate():
    """
    校验 RoCE easy deploy 配置的唯一性
    Request body:
    {
        "configurations": [
            {
                "config_id": "",
                "sysname": "string",
                "switch_sn": "string"
            }
        ]
    }
    Response:
    {
        "status": 200,
        "msg": "Validation passed"
    }
    """
    # 如果装饰器校验通过，直接返回成功
    return jsonify({
        "status": 200,
        "msg": "RoCE easy deploy configuration validation passed"
    })
        
@roce_mold.route("/easydeploy_config/<string:type>", methods=["POST"])
@admin_permission.require(http_exception=403)
@validate_easydeploy_uniqueness
def roce_easydeploy_config_save(type):
    """
    Save or update RoCE easy deploy configurations
    Request body:
    {
        "enable_pfc": "string",
        "mode": "string",
        "configurations": [
            {
                "config_id": "",
                "sysname": "string",
                "switch_sn": "string",
                "ports": [
                    "string",
                    "string"
                ],
                "queue_num": [
                    "string",
                    "string"
                ]
            }
        ]
    }
    """
    try:
        data = request.get_json()
        session = roce_db.get_session()
        if not data:
            return jsonify({
                "status": 400,
                "msg": "No configuration data provided"
            })

        configurations = data.get("configurations", [])
        if not configurations:
            return jsonify({
                "status": 400,
                "msg": "No configurations provided"
            })
        cli_commands = []
        for config in configurations:
            sysnames = config.get("sysname")
            if not sysnames:
                continue

            switch_sns = config.get("switch_sn")
            for index, switch_sn in enumerate(switch_sns):
                existing_config = session.query(RoceEasyDeployConfiguration).filter(RoceEasyDeployConfiguration.switch_sn == switch_sn).first()
                config_id = existing_config.id if existing_config else None
                # 准备配置数据
                db_config = {
                    "sysname": sysnames[index],
                    "switch_sn": switch_sn,
                    "port": config.get("ports"),
                    "queue": config.get("queue_num"),
                    "enabled": data.get("enable_pfc") == "enable",
                    "mode": data.get("mode", None)
                }

                # if type == "save":
                #     if existing_config:
                #         # 更新已存在的配置
                #         roce_db.update_roce_easy_deploy_configuration(
                #             existing_config.id, 
                #             db_config
                #         )
                #     else:
                #         # 创建新配置
                #         existing_config = None
                #         config_id = roce_db.add_roce_easy_deploy_configuration(db_config)

                config_data = _build_easy_config_data(db_config, existing_config)
                session = roce_db.get_session()
                switch = session.query(Switch).filter(Switch.sn == switch_sn).first()

                if type == "save":
                    deploy_status = deploy_roce_configuration(switch, config_data)
                    if deploy_status.get("status") == 200:
                        if existing_config:
                            # 更新已存在的配置
                            roce_db.update_roce_easy_deploy_configuration(
                                existing_config.id, 
                                db_config
                            )
                        else:
                            # 创建新配置
                            existing_config = None
                            config_id = roce_db.add_roce_easy_deploy_configuration(db_config)
                        roce_db.update_roce_easy_deploy_configuration(
                            config_id, 
                            dict(config_data=json.dumps(config_data.get("new_val")))  # 将字典转换为 JSON 字符串
                        )
                    else:
                        return jsonify({
                            "status": 500,
                            "msg": f"Deploy easydeploy configuration {sysnames[index]}:{switch_sn} failed: {deploy_status.get('msg')}"
                        })
                if type == "preview":
                    from server.config_distribution.config_deploy import CLIConfigDeployer
                    cli_deployer = CLIConfigDeployer(host=switch.mgt_ip,
                                                    username=switch.system_config.switch_op_user,
                                                    password=switch.system_config.switch_op_password,
                                                    port=22)
                    cli_commands.append({
                        "sysname": sysnames[index],
                        "switch_sn": switch_sn,
                        "cli": cli_deployer.build_cli_roce_config_commands("preview", **config_data)
                    })
        
        if type == "preview":
            return jsonify({
                    "status": 200,
                    "commands": cli_commands
                })
        
        return jsonify({
            "status": 200,
            "msg": "RoCE easy deploy configurations saved successfully"
        })

    except Exception as e:
        LOG.error(f"Error in roce_easydeploy_config_save: {str(e)}")
        LOG.error(traceback.format_exc())
        return jsonify({
            "status": 500,
            "msg": f"{type} RoCE easy deploy configurations failed: {str(e)}"
        })

def _build_easy_config_data(config, existing_config=None):
    """
    {
        'sysname': str,
        'switch_sn': str,
        'port': list,      
        'queue': list,     
        'enabled': bool,
        'mode': str        
    }
    """

    # 构建配置字典
    config_data = {
        "new_val": {
            "roce_configuring": {
                "roce_mode": config.get('mode', None),
                "roce_ports": config.get('port', []),
                "queues": config.get('queue', [])
            }
        },
        "old_val": json.loads(existing_config.config_data) if existing_config and existing_config.config_data else {}
    }
    
        
    # 转换为 JSON 字符串
    return config_data

@roce_mold.route("/easydeploy_config/list", methods=["POST"])
@admin_permission.require(http_exception=403)
def roce_easydeploy_config_list():
    """
    获取 RoCE easy deploy 配置列表
    """
    try:
        session = roce_db.get_session()
        data = request.get_json()
        if not data:
            return jsonify({
                "status": 400,
                "msg": "No query parameters provided"
            })

        # 关联表别名
        SwitchAlias = aliased(Switch)
        AssocAlias = aliased(AssociationFabric)
        FabricAlias = aliased(Fabric)

        # 构建基础查询
        pre_query = (
            session.query(
                RoceEasyDeployConfiguration,
                SwitchAlias,
                AssocAlias,
                FabricAlias
            )
            .outerjoin(SwitchAlias, RoceEasyDeployConfiguration.switch_sn == SwitchAlias.sn)
            .outerjoin(AssocAlias, SwitchAlias.id == AssocAlias.switch_id)
            .outerjoin(FabricAlias, AssocAlias.fabric_id == FabricAlias.id)
        )

        # 搜索条件
        search_conditions = []
        searchFields = data.get("searchFields", {}).get("fields", [])
        search_value = data.get("searchFields", {}).get("value", "")
        if "port" in searchFields:
            from sqlalchemy import Text, or_
            column = getattr(RoceEasyDeployConfiguration, "port")
            search_conditions.append(column.cast(Text).ilike(f'%{search_value}%'))
            pre_query = pre_query.filter(or_(*search_conditions))

        # 分页
        page_num, page_size, total_count, query_result = utils.query_helper(
            RoceEasyDeployConfiguration,
            pre_query=pre_query,
            data=request.get_json()
        )

        # 组装数据
        configs = []
        for config, switch, assoc, fabric in query_result:
            config_dict = config.make_dict()
            if fabric:
                config_dict["fabric"] = fabric.fabric_name
            else:
                config_dict["fabric"] = None
            configs.append(config_dict)

        return jsonify({
            "status": 200,
            "total": total_count,
            "page": page_num,
            "pageSize": page_size,
            "data": configs
        })

    except Exception as e:
        LOG.error(f"Error in roce_easydeploy_config_list: {str(e)}")
        LOG.error(traceback.format_exc())
        return jsonify({
            "status": 500,
            "msg": f"Query RoCE easy deploy configurations failed: {str(e)}"
        })

@roce_mold.route("/easydeploy_config/delete", methods=["POST"])
@admin_permission.require(http_exception=403)
def roce_easydeploy_config_delete():
    """
    删除 RoCE easy deploy 配置
    Request body:
    {
        "config_id": str  # 配置ID
    }
    """
    try:
        data = request.get_json()
        
        if not data:
            return jsonify({
                "status": 400,
                "msg": "No configuration ID provided"
            })

        config_id = data.get("config_id")
        if not config_id:
            return jsonify({
                "status": 400,
                "msg": "Configuration ID is required"
            })

        # 查询配置是否存在
        session = roce_db.get_session()
        config = session.query(RoceEasyDeployConfiguration).filter_by(id=config_id).first()
        if not config:
            return jsonify({
                "status": 404,
                "msg": f"Configuration with ID {config_id} not found"
            })

        switch_sn = config.switch_sn
        switch = session.query(Switch).filter(Switch.sn == switch_sn).first()
        if switch:
            config_data = _build_easy_config_data({}, config)
            deploy_status = deploy_roce_configuration(switch, config_data)
            if deploy_status.get("status") != 200:
                return jsonify({
                    'status': 500,
                    'msg': f'Failed to deploy delete id {config_id} configuration: {deploy_status.get("msg")}'
                })
            # 删除配置
            if roce_db.delete_roce_easy_deploy_configuration(config_id):
                return jsonify({
                    "status": 200,
                    "msg": "Configuration deleted successfully"
                })
            else:
                return jsonify({
                    "status": 500,
                    "msg": "Failed to delete configuration"
                })
        else:
            return jsonify({
                "status": 500,
                "msg": f"Switch with SN {switch_sn} not found"
            })

    except Exception as e:
        LOG.error(f"Error in roce_easydeploy_config_delete: {str(e)}")
        LOG.error(traceback.format_exc())
        return jsonify({
            "status": 500,
            "msg": f"Delete RoCE easy deploy configuration failed: {str(e)}"
        })

def parse_roce_config_output(raw_text: str) -> dict:
    # === Step 1: Preprocessing ===
    text = raw_text.replace('\r\r\n', '\n').strip()
    lines = text.splitlines()

    result = {}
    idx = 0
    n = len(lines)

    # === Step 2: Helpers ===
    def parse_key_value_block(start):
        block = {}
        i = start
        while i < n and lines[i].strip() and not re.match(r'^[^\s]', lines[i]):
            line = lines[i].strip()
            if re.search(r'\s{2,}', line):
                k, v = re.split(r'\s{2,}', line, maxsplit=1)
                block[k.strip().replace('-', '_')] = v.strip()
            else:
                block[line.strip()] = ""
            i += 1
        return block, i

    def parse_mapping_table(start, columns):
        mapping = {}
        i = start
        while i < n and re.match(r'^\s*\d+', lines[i]):
            line = lines[i].strip()
            parts = re.split(r'\s{2,}', line)
            key = parts[0]
            if len(parts) == 2:
                values = [int(x) for x in parts[1].split(',') if x.strip().isdigit()]
                mapping[key] = values
            elif len(parts) == 3:
                mapping[key] = dict(zip(columns[1:], parts[1:]))
            i += 1
        return mapping, i

    # === Step 3: Parse line by line ===
    while idx < n:
        line = lines[idx].strip()
        if not line:
            idx += 1
            continue

        if line.startswith('run '):
            result['run'] = line[4:]
            idx += 1
        elif line in ('congestion-control', 'pfc', 'trust'):
            block, idx = parse_key_value_block(idx + 1)
            if line == 'congestion-control' and 'enabled_queue' in block:
                block['enabled_queue'] = [int(x) for x in block['enabled_queue'].split(',') if x.strip().isdigit()]
            result[line.replace('-', '_')] = block
        elif 'status' in line or 'mode' in line:
            if re.search(r'\s{2,}', line):
                k, v = re.split(r'\s{2,}', line, maxsplit=1)
                result[k.strip().lower()] = v.strip()
            idx += 1
        elif line.startswith('RoCE PCP/DSCP->LP'):
            idx += 2  # skip title and === line
            idx += 1  # skip column header
            mapping, idx = parse_mapping_table(idx, ['local-priority', 'dscp'])
            result['pcp_dscp_mapping'] = mapping
        elif line.startswith('RoCE LP->FC'):
            idx += 2  # skip title and column header
            idx += 1  # skip dash line
            mapping, idx = parse_mapping_table(idx, ['local-priority', 'forwarding-class', 'scheduler-weight'])
            result['lp_fc_mapping'] = mapping
        else:
            idx += 1

    return result

@roce_mold.route("/easydeploy_config/overview", methods=["POST"])
@admin_permission.require(http_exception=403)
def easydeploy_overview():
    """
    获取 RoCE easy deploy 配置概览信息
    Request body:
    {
        "sysname": "string",
        "overview_type": "string"  # basic/pcp_dscp/lp
    }
    Response:
    {
        "status": 200,
        "info": "string"
    }
    """
    try:
        data = request.get_json()
        
        if not data:
            return jsonify({
                "status": 400,
                "msg": "No request data provided"
            })

        sysname = data.get("sysname")
        sn = data.get("switch_sn")
        overview_type = data.get("overview_type", "basic")

        if not sysname or not sn:
            return jsonify({
                "status": 400,
                "msg": "Sysname and SN is required"
            })

        if not overview_type or overview_type not in ["basic", "pcp_dscp", "lp"]:
            return jsonify({
                "status": 400,
                "msg": "Invalid overview type. Must be one of: basic, pcp_dscp, lp"
            })
        
        # 获取配置
        session = roce_db.get_session()
        switch = session.query(Switch).filter(Switch.sn == sn).first()
        if not switch:
            return jsonify({
                "status": 400,
                "msg": f"Switch with sn {sn} not found"
            })
        from server.util.ssh_util import interactive_shell_configure
        result, _ = interactive_shell_configure(cmd="run show class-of-service roce", hostname=switch.mgt_ip,
                                                username=switch.system_config.switch_op_user,
                                                password=switch.system_config.switch_op_password, port=22)
        LOG.info(f"RoCE easy deploy overview command output: {str(result)}")
        result_dict = parse_roce_config_output(str(result))
        basic_info = {
            "roce_easydeploy_status": result_dict.get("status", ""),
            "roce_mode": result_dict.get("mode", ""),
            "congestion_control": result_dict.get("congestion_control", {}),
            "pfc": result_dict.get("pfc", {}),
            "trust": result_dict.get("trust", {})
        }
        pcp_dscp_info = result_dict.get("pcp_dscp_mapping", {})
        lp_info = result_dict.get("lp_fc_mapping", {})

        config = roce_db.get_configuration_overview_by_sn(sn)

        if config:
            roce_db.update_configuration_overview(config.id, {
                "basic_info": basic_info,
                "pcp_dscp_info": pcp_dscp_info,
                "lp_info": lp_info
            })
        else:
            roce_db.add_configuration_overview(
                sysname=sysname,
                switch_sn=sn,
                basic_info=basic_info,
                pcp_dscp_info=pcp_dscp_info,
                lp_info=lp_info
            )

        info = {
            "sn": sn,
            "sysname": sysname,
            "basic_info": basic_info,
            "pcp_dscp_info": pcp_dscp_info,
            "lp_info": lp_info
        }

        return jsonify({
            "status": 200,
            "info": info
        })

    except Exception as e:
        LOG.error(f"Error in easydeploy_overview: {str(e)}")
        LOG.error(traceback.format_exc())
        return jsonify({
            "status": 500,
            "msg": f"Get overview information failed: {str(e)}"
        })


@roce_mold.route("/dlb_config/<string:type>", methods=["POST"])
@admin_permission.require(http_exception=403)
def dlb_config_save(type):
    """
    保存或更新 DLB 配置
    Request body:
    {
        "switch": [
            {"sysname": "string", "switch_sn": "string"}
        ],
        "configuration": {
            "enabled": "string",
            "dlb_mode": "string"
        }
    }
    """
    try:
        data = request.get_json()
        
        if not data:
            return jsonify({
                "status": 400,
                "msg": "No configuration data provided"
            })

        switches = data.get("switch", [])
        configuration = data.get("configuration", {})

        if not switches:
            return jsonify({
                "status": 400,
                "msg": "No switches provided"
            })

        if not configuration:
            return jsonify({
                "status": 400,
                "msg": "No configuration provided"
            })

        commands_list = []
        # 处理每个交换机
        for switch in switches:
            sysname = switch.get("sysname")
            switch_sn = switch.get("switch_sn")

            # 查询现有配置
            existing_config = roce_db.get_dlb_configuration_by_sn(switch_sn)

            # 准备配置数据
            db_config = {
                "sysname": sysname,
                "switch_sn": switch_sn,
                "dlb_enabled": configuration.get("enabled"),
                "mode": configuration.get("dlb_mode", "dlb-normal")
            }

            if type == "save":
                if existing_config:
                    # 更新已存在的配置
                    roce_db.update_dlb_configuration(
                        existing_config.id, 
                        db_config
                    )
                    config_id = existing_config.id
                else:
                    # 创建新配置
                    existing_config = None
                    config_id = roce_db.add_dlb_configuration(db_config)

            config_data = _build_dlb_config_data(db_config, existing_config)
            session = roce_db.get_session()
            switch = session.query(Switch).filter(Switch.sn == switch_sn).first()

            if type == "save":
                deploy_status = deploy_roce_configuration(switch, config_data)
                if deploy_status.get("status") == 200:
                    roce_db.update_dlb_configuration(
                        config_id, 
                        dict(config_data=json.dumps(config_data.get("new_val")))  # 将字典转换为 JSON 字符串
                    )
                else:
                    return jsonify({
                        "status": 500,
                        "msg": f"Deploy dlb configuration failed: {deploy_status.get('msg')}"
                    })
            if type == "preview":
                from server.config_distribution.config_deploy import CLIConfigDeployer
                cli_deployer = CLIConfigDeployer(host=switch.mgt_ip,
                                                 username=switch.system_config.switch_op_user,
                                                 password=switch.system_config.switch_op_password,
                                                 port=22)
                commands_list.append({
                    "sysname": sysname,
                    "cli": cli_deployer.build_cli_roce_config_commands("preview", **config_data)
                })

        if type == "preview":
            return jsonify({
                "status": 200,
                "commands": commands_list
            })
            
        return jsonify({
            "status": 200,
            "msg": "DLB configurations saved successfully"
        })

    except Exception as e:
        LOG.error(f"Error in dlb_config_save: {str(e)}")
        LOG.error(traceback.format_exc())
        return jsonify({
            "status": 500,
            "msg": f"{type} DLB configurations failed: {str(e)}"
        })
    
def _build_dlb_config_data(config, existing_config=None):
    """
    构建 DLB 配置数据
    Args:
        config: dict, 配置信息
            {
                "mode": str  # DLB 模式 ('dlb-normal', 'dlb-optimal', 'dlb-assigned')
            }
    Returns:
        dict: DLB 配置数据
    """
    # 构建配置字典
    config_data = {
        "new_val": {
            "dlb": {
                "dlb_mode": config.get('mode', 'dlb-normal')  # 默认模式为 'normal'
            }
        },
        "old_val": json.loads(existing_config.config_data) if existing_config and existing_config.config_data else {}
    }
    
    return config_data

@roce_mold.route("/dlb_config/list", methods=["POST"])
@admin_permission.require(http_exception=403)
def dlb_config_list():
    try:
        session = roce_db.get_session()
        data = request.get_json()
        
        if not data:
            return jsonify({
                "status": 400,
                "msg": "No query parameters provided"
            })


        # 关联表别名
        SwitchAlias = aliased(Switch)
        AssocAlias = aliased(AssociationFabric)
        FabricAlias = aliased(Fabric)

        # 构建基础查询
        pre_query = (
            session.query(
                DLBConfiguration,
                SwitchAlias,
                AssocAlias,
                FabricAlias
            )
            .outerjoin(SwitchAlias, DLBConfiguration.switch_sn == SwitchAlias.sn)
            .outerjoin(AssocAlias, SwitchAlias.id == AssocAlias.switch_id)
            .outerjoin(FabricAlias, AssocAlias.fabric_id == FabricAlias.id)
        )

        # 获取分页和搜索结果
        page_num, page_size, total_count, query_result = utils.query_helper(
            DLBConfiguration,
            pre_query=pre_query,
            data=request.get_json()
        )

        configs = []
        for config, switch, assoc, fabric in query_result:
            config_dict = config.make_dict()
            if fabric:
                config_dict["fabric"] = fabric.fabric_name
            else:
                config_dict["fabric"] = None
            
            if switch:
                config_dict["online"] = switch.reachable_status
            else:
                config_dict["online"] = 2
            configs.append(config_dict)

        return jsonify({
            "status": 200,
            "data": configs,
            "total": total_count,
            "page": page_num,
            "pageSize": page_size
        })

    except Exception as e:
        LOG.error(f"Error in dlb_config_list: {str(e)}")
        LOG.error(traceback.format_exc())
        return jsonify({
            "status": 500,
            "msg": f"Query dlb configurations failed: {str(e)}"
        })

def validate_pfc_uniqueness(f):
    """
    Decorator: Validate PFC configuration uniqueness for creation
    - Ensure only one record per switch_sn + profile_name combination
    - Only for creation operations (no config_id should be provided)
    """
    @wraps(f)
    def decorated_function(*args, **kwargs):
        data = request.get_json()
        if not data:
            raise ValueError("No configuration data provided")

        configurations = data.get("configurations", [])
        if not configurations:
            raise ValueError("No configurations provided")

        # Check for uniqueness using service layer
        for config in configurations:
            switch_sn = config.get("switch_sn")
            profile_name = config.get("profile_name")
            config_id = config.get("config_id")
            
            if not switch_sn:
                raise ValueError("switch_sn is required")
            
            if not profile_name:
                raise ValueError("profile_name is required")
            
            # For creation, config_id should not be provided
            if config_id and not roce_db.check_pfc_profile_exists(switch_sn, profile_name):
                raise ValueError(f"PFC profile '{profile_name}' not found on switch {switch_sn}")

            # Check if profile already exists on this switch
            if not config_id and roce_db.check_pfc_profile_exists(switch_sn, profile_name):
                raise ValueError(f"PFC profile '{profile_name}' already exists on switch {switch_sn}")

        # Validation passed, execute original function
        return f(*args, **kwargs)
            
    return decorated_function


def validate_ecn_uniqueness(f):
    """
    Decorator: Validate ECN main configuration uniqueness for creation
    - Ensure only one ECN main configuration per switch_sn
    - Only for creation operations (no config_id should be provided)
    """
    @wraps(f)
    def decorated_function(*args, **kwargs):
        data = request.get_json()
        if not data:
            raise ValueError("No configuration data provided")

        configurations = data.get("configurations", [])
        if not configurations:
            raise ValueError("No configurations provided")

        # Check for uniqueness using service layer
        for config in configurations:
            switch_sn = config.get("switch_sn")
            config_id = config.get("config_id")
            
            if not switch_sn:
                raise ValueError("switch_sn is required")
            
            # For creation operations, config_id should not be provided
            if config_id:
                if not roce_db.check_ecn_main_config_exists(switch_sn):
                    raise ValueError(f"ECN main configuration not found on switch {switch_sn}.")
            
            # Check if ECN main configuration already exists on this switch
            if not config_id:
                if roce_db.check_ecn_main_config_exists(switch_sn):
                    raise ValueError(f"ECN main configuration already exists on switch {switch_sn}. Each switch can only have one ECN configuration.")

        # Validation passed, execute original function
        return f(*args, **kwargs)
            
    return decorated_function


@roce_mold.route("/pfc_config/validate", methods=["POST"])
@admin_permission.require(http_exception=403)
@api_exception_handler
@validate_pfc_uniqueness
def roce_pfc_config_validate():
    """
    校验 PFC 配置的唯一性
    Request body:
    {
        "configurations": [
            {
                "config_id": "",
                "sysname": "string",
                "switch_sn": "string",
                "profile_name": "string"
            }
        ]
    }
    Response:
    {
        "status": 200,
        "msg": "Validation passed"
    }
    """
    # 如果装饰器校验通过，直接返回成功
    return jsonify({
        "status": 200,
        "msg": "PFC configuration validation passed"
        })


@roce_mold.route("/ecn_config/validate", methods=["POST"])
@admin_permission.require(http_exception=403)
@api_exception_handler
@validate_ecn_uniqueness
def roce_ecn_config_validate():
    """
    校验 ECN 配置的唯一性
    Request body:
    {
        "configurations": [
            {
                "config_id": "",
                "sysname": "string",
                "switch_sn": "string"
            }
        ]
    }
    Response:
    {
        "status": 200,
        "msg": "Validation passed"
    }
    """
    # 如果装饰器校验通过，直接返回成功
    return jsonify({
        "status": 200,
        "msg": "ECN configuration validation passed"
        })

def _build_pfc_config_data(config, existing_config=None):
    """
    Build PFC configuration data for deployment
    Args:
        config: dict, PFC configuration input (empty for delete operations)
        existing_config: PfcConfiguration, Existing configuration object (None for creation)
    Returns:
        dict: Built configuration data
    """
    config_data = {
        "new_val": {
            "pfc": {
                "pfc_profile_name": config.get("profile_name", ""),
                "ports": config.get("port", []),
                "code_points": config.get("queue", []),
                "drop_enable": str(config.get("enabled")).lower(),
            }
        } if config else {},  # Empty for delete operations
        "old_val": json.loads(existing_config.config_data) if existing_config and existing_config.config_data else {}
    }
    return config_data

@roce_mold.route("/pfc_config/save", methods=["POST"])
@admin_permission.require(http_exception=403)
@api_exception_handler
@validate_pfc_uniqueness
def roce_pfc_config_save():
    """
    Save PFC configurations
    Request body:
    {
        "configurations": [
            {
                "sysname": "string",
                "switch_sn": "switch_sn",
                "port": ["string"],
                "queue": ["string"],
                "profile_name": "string",
                "enabled": true,
                "is_all_ports": false,
                "is_all_queues": false
            }
        ]
    }
    Returns:
        JSON response with operation status
    """
    data = request.get_json()
    configurations = data.get("configurations", [])
    
    if not configurations:
        raise ValueError("No configurations provided")
    
    # Prepare database records after successful deployment
    db_configurations = []
    
    for config in configurations:
        switch_sn = config.get("switch_sn")
        if not switch_sn:
            raise ValueError("switch_sn is required")
        
        # Get switch for deployment using service layer
        switch = roce_db.get_switch_by_sn(switch_sn)
        if not switch:
            raise ValueError(f"Switch with SN {switch_sn} not found")
        
        # Build configuration data using view layer tool function
        config_data = _build_pfc_config_data(config)
        
        # Deploy configuration using view layer tool function
        deploy_result = deploy_roce_configuration(switch, config_data)
        if deploy_result.get("status") != 200:
            raise Exception(f"Deploy PFC configuration failed: {deploy_result.get('msg')}")
        
        # Prepare database record
        db_config = {
            "sysname": config.get("sysname"),
            "switch_sn": switch_sn,
            "profile_name": config.get("profile_name"),
            "port": config.get("port"),
            "queue": config.get("queue"),
            "enabled": config.get("enabled"),
            "is_all_ports": config.get("is_all_ports", False),
            "is_all_queues": config.get("is_all_queues", False),
            "config_data": json.dumps(config_data.get("new_val"))
        }
        db_configurations.append(db_config)
    
    # Save all configurations to database via service layer
    config_ids = roce_db.save_pfc_configurations(db_configurations)
    
    return jsonify({
        "status": 200,
        "msg": "PFC configurations saved successfully",
        "data": {"config_ids": config_ids}
    })


@roce_mold.route("/pfc_config/update", methods=["POST"])
@admin_permission.require(http_exception=403)
@api_exception_handler
def roce_pfc_config_update():
    """
    Update PFC configurations for a switch (bulk update)
    Request body:
    {
        "switch_sn": "string",
        "configurations": [
            {
                "config_id": "string",  # optional for new configs
                "sysname": "string",
                "switch_sn": "switch_sn",
                "port": ["string"],
                "queue": ["string"],
                "profile_name": "string",
                "enabled": true,
                "is_all_ports": false,
                "is_all_queues": false
            }
        ]
    }
    Returns:
        JSON response with operation status
    """
    data = request.get_json()
    switch_sn = data.get("switch_sn")
    configurations = data.get("configurations", [])
    
    if not switch_sn:
        raise ValueError("switch_sn is required")
    
    # Get switch for deployment using service layer
    switch = roce_db.get_switch_by_sn(switch_sn)
    if not switch:
        raise ValueError(f"Switch with SN {switch_sn} not found")
    
    # Analyze operations using service layer
    operations = roce_db.bulk_update_pfc_configurations(switch_sn, configurations)
    
    # 1. Handle delete operations
    for config_id in operations.get("config_ids_to_delete", []):
        existing_config = operations.get("existing_configs", {}).get(str(config_id))
        
        if not existing_config:
            raise ValueError(f"Configuration {config_id} not found for deletion")
        
        # Build delete configuration data using view layer tool function
        config_data = _build_pfc_config_data({}, existing_config)
        
        # Deploy delete configuration using view layer tool function
        deploy_result = deploy_roce_configuration(switch, config_data)
        if deploy_result.get("status") != 200:
            raise Exception(f"Failed to deploy delete configuration {config_id}: {deploy_result.get('msg')}")
    
    # 2. Handle update operations
    update_operations = []
    for config in operations.get("configs_to_update", []):
        config_id = config.get("config_id")
        existing_config = operations.get("existing_configs", {}).get(str(config_id))
        
        if not existing_config:
            raise ValueError(f"Configuration {config_id} not found for update")
        
        # Build update configuration data using view layer tool function
        config_data = _build_pfc_config_data(config, existing_config)
        
        # Deploy update configuration using view layer tool function
        deploy_result = deploy_roce_configuration(switch, config_data)
        if deploy_result.get("status") != 200:
            raise Exception(f"Failed to deploy update configuration {config_id}: {deploy_result.get('msg')}")
        
        # Prepare database update
        update_data = {
            "port": config.get("port"),
            "queue": config.get("queue"),
            "enabled": config.get("enabled"),
            "is_all_queues": config.get("is_all_queues", False),
            "is_all_ports": config.get("is_all_ports", False),
            "sysname": config.get("sysname"),
            "switch_sn": switch_sn,
            "profile_name": config.get("profile_name"),
            "config_data": json.dumps(config_data.get("new_val"))
        }
        update_operations.append({
            "config_id": config_id,
            "updates": update_data
        })
    
    # 3. Handle create operations
    create_operations = []
    for config in operations.get("configs_to_create", []):
        # Build create configuration data using view layer tool function
        config_data = _build_pfc_config_data(config)
        
        # Deploy create configuration using view layer tool function
        deploy_result = deploy_roce_configuration(switch, config_data)
        if deploy_result.get("status") != 200:
            raise Exception(f"Failed to deploy new configuration: {deploy_result.get('msg')}")
        
        # Prepare database create
        db_config = {
            "sysname": config.get("sysname"),
            "switch_sn": switch_sn,
            "profile_name": config.get("profile_name"),
            "port": config.get("port"),
            "queue": config.get("queue"),
            "enabled": config.get("enabled"),
            "is_all_ports": config.get("is_all_ports", False),
            "is_all_queues": config.get("is_all_queues", False),
            "config_data": json.dumps(config_data.get("new_val"))
        }
        create_operations.append(db_config)
    
    # Execute all database operations via service layer
    operations_data = {
        "config_ids_to_delete": operations.get("config_ids_to_delete", []),
        "update_operations": update_operations,
        "create_operations": create_operations
    }
    roce_db.execute_pfc_bulk_operations(operations_data)
    
    return jsonify({
        "status": 200,
        "msg": "PFC configurations updated successfully"
    })

@roce_mold.route("/pfc_config/detail_by_switch", methods=["POST"])
@admin_permission.require(http_exception=403)
@api_exception_handler
def roce_pfc_config_detail_by_switch():
    """
    Get all PFC configurations for a specific switch
    Request body:
    {
        "switch_sn": "string"
    }
    Response:
    {
        "status": 200,
        "msg": "Get pfc config detail successfully",
        "data": [configuration list]
    }
    """
    data = request.get_json()
    if not data:
        raise ValueError("No request data provided")
    
    switch_sn = data.get("switch_sn")
    if not switch_sn:
        raise ValueError("switch_sn is required")

    # Get PFC configurations via service layer
    configs = roce_db.get_pfc_configs_detail_by_switch_sn(switch_sn)

    return jsonify({
        "status": 200,
        "msg": "Get pfc config detail successfully",
        "data": configs
    })


@roce_mold.route('/pfc_config/list', methods=['post'])
@admin_permission.require(http_exception=403)
@api_exception_handler
def roce_pfc_config_list():
    """
    Get PFC configuration list with tree structure and pagination.
    
    Request body:
    {
        "searchFields": {
            "fields": ["port"],  
            "value": "search_value"
        },
        "page": 1,
        "pageSize": 20
    }
    """
    # Get request data
    request_data = request.get_json()
    
    # Call service layer to get paginated tree data
    result = roce_db.get_pfc_configs_list_data(request_data)
    
    return jsonify({
        "status": 200,
        "data": result.get("data"),
        "page": result.get("page"),
        "pageSize": result.get("pageSize"),
        "total": result.get("total")
    })

@roce_mold.route('/pfc_config/delete', methods=['post'])
@admin_permission.require(http_exception=403)
@api_exception_handler
def roce_pfc_config_delete():
    """
    Delete PFC configuration.
    
    Request body:
    {
        "config_id": "string"
    }
    """
    # Get request data
    request_data = request.get_json()
    config_id = request_data.get("config_id")
    
    if not config_id:
        raise ValueError("config_id is required")
    
    # Call service layer to get configuration and switch info for deletion
    result = roce_db.get_pfc_configuration_for_deletion(config_id)
    
    config = result.get("config")
    switch = result.get("switch")
    
    # Build delete configuration data using _build_pfc_config_data function
    config_data = _build_pfc_config_data({}, config)
    
    # Deploy delete configuration to switch using existing deploy_roce_configuration
    deploy_status = deploy_roce_configuration(switch, config_data)
    
    # Check deployment result
    if deploy_status.get("status") != 200:
        raise ValueError(f"Failed to deploy delete configuration: {deploy_status.get('msg')}")
    
    # Delete the configuration from database after successful deployment
    roce_db.delete_pfc_configuration(config_id)
    
    return jsonify({
        "status": 200,
        "msg": "Delete PFC config successfully"
    })


@roce_mold.route("/pfc_buffer_config/save", methods=["POST"])
@admin_permission.require(http_exception=403)
@api_exception_handler
def roce_pfc_buffer_config_save():
    """
    Save PFC Buffer configurations
    Request body:
    {
        "configurations": [
            {
                "sysname": "string",
                "switch_sn": "string",
                "traffic_type": "ingress|egress",
                "port": ["string"],
                "queue": ["string"],
                "shared_ratio": "string",
                "threshold": "string",
                "guaranteed": "string",  # for ingress only
                "reset_offset": "string",  # for ingress only
                "headroom": "string",  # for ingress only
                "is_all_ports": false,
                "is_all_queues": false
            }
        ]
    }
    Returns:
        JSON response with operation status
    """
    data = request.get_json()
    configurations = data.get("configurations", [])
    
    if not configurations:
        raise ValueError("No configurations provided")
    
    # Save configurations to database via service layer
    result = roce_db.save_pfc_buffer_configurations(configurations)
    saved_configs = result.get("saved_configs", [])
    
    # Phase 2: Deploy configurations to switches
    for saved_config in saved_configs:
        config_id = saved_config.get("config_id")
        config = saved_config.get("config")
        traffic_type = saved_config.get("traffic_type")
        switch = saved_config.get("switch")
        
        # Build configuration data using view layer tool functions
        if traffic_type == "ingress":
            config_data = _build_single_ingress_config_data(config)
        else:
            config_data = _build_single_egress_config_data(config)
        
        # Deploy configuration using view layer tool function
        deploy_result = deploy_roce_configuration(switch, config_data)
        if deploy_result.get("status") != 200:
            raise Exception(f"Deploy PFC buffer configuration failed: {deploy_result.get('msg')}")
        
        # Update config_data field after successful deployment
        roce_db.update_pfc_buffer_config_data(
            config_id, 
            traffic_type, 
            json.dumps(config_data.get("new_val"))
        )
    
    return jsonify({
        "status": 200,
        "msg": "PFC buffer configurations created successfully"
    })


@roce_mold.route("/pfc_buffer_config/update", methods=["POST"])
@admin_permission.require(http_exception=403)
@api_exception_handler
def roce_pfc_buffer_config_update():
    """
    Update PFC Buffer configurations for a switch (bulk update)
    Request body:
    {
        "switch_sn": "string",
        "configurations": [
            {
                "config_id": "string",  # optional, update if exists, create if not
                "sysname": "string",
                "switch_sn": "string",
                "traffic_type": "ingress|egress",
                "port": ["string"],
                "queue": ["string"],
                "shared_ratio": "string",
                "threshold": "string",
                "guaranteed": "string",  # for ingress only
                "reset_offset": "string",  # for ingress only
                "headroom": "string",  # for ingress only
                "is_all_ports": false,
                "is_all_queues": false
            }
        ]
    }
    Returns:
        JSON response with operation status
    """
    data = request.get_json()
    switch_sn = data.get("switch_sn")
    configurations = data.get("configurations", [])
    
    if not switch_sn:
        raise ValueError("switch_sn is required")
    
    # Analyze operations using service layer
    operations = roce_db.bulk_update_pfc_buffer_configurations(switch_sn, configurations)
    switch = operations.get("switch")
    
    # 1. Handle delete operations
    for config_id in operations.get("ingress_ids_to_delete", []):
        # Get configuration data for deletion
        delete_config = roce_db.get_pfc_buffer_config_for_deletion(config_id, "ingress")
        
        # Build delete configuration data using view layer tool function
        config_data = _build_single_ingress_config_data(delete_config)
        
        # Deploy delete configuration using view layer tool function
        deploy_result = deploy_roce_configuration(switch, config_data)
        if deploy_result.get("status") != 200:
            raise Exception(f"Failed to deploy delete ingress configuration {config_id}: {deploy_result.get('msg')}")
    
    for config_id in operations.get("egress_ids_to_delete", []):
        # Get configuration data for deletion
        delete_config = roce_db.get_pfc_buffer_config_for_deletion(config_id, "egress")
        
        # Build delete configuration data using view layer tool function
        config_data = _build_single_egress_config_data(delete_config)
        
        # Deploy delete configuration using view layer tool function
        deploy_result = deploy_roce_configuration(switch, config_data)
        if deploy_result.get("status") != 200:
            raise Exception(f"Failed to deploy delete egress configuration {config_id}: {deploy_result.get('msg')}")
    
    # 2. Handle update operations
    update_operations = []
    for config in operations.get("configs_to_update", []):
        config_id = config.get("config_id")
        traffic_type = config.get("traffic_type", "").lower()
        
        # Get existing configuration data for update
        update_data = roce_db.get_pfc_buffer_config_for_update(config_id, traffic_type)
        config.update({"old_val": update_data.get("old_val", {})})
        
        # Build update configuration data using view layer tool functions
        if traffic_type == "ingress":
            config_data = _build_single_ingress_config_data(config)
        else:
            config_data = _build_single_egress_config_data(config)
        
        # Deploy update configuration using view layer tool function
        deploy_result = deploy_roce_configuration(switch, config_data)
        if deploy_result.get("status") != 200:
            raise Exception(f"Failed to deploy update {traffic_type} configuration {config_id}: {deploy_result.get('msg')}")
        
        # Prepare database update
        if traffic_type == "ingress":
            update_data = {
                "sysname": config.get("sysname"),
                "switch_sn": switch_sn,
                "port": config.get("port", []),
                "queue": config.get("queue", []),
                "shared_ratio": config.get("shared_ratio"),
                "threshold": config.get("threshold"),
                "guaranteed": config.get("guaranteed"),
                "reset_offset": config.get("reset_offset"),
                "headroom": config.get("headroom"),
                "is_all_ports": config.get("is_all_ports", False),
                "is_all_queues": config.get("is_all_queues", False),
                "config_data": json.dumps(config_data.get("new_val"))
            }
        else:
            update_data = {
                "sysname": config.get("sysname"),
                "switch_sn": switch_sn,
                "port": config.get("port", []),
                "queue": config.get("queue", []),
                "shared_ratio": config.get("shared_ratio"),
                "threshold": config.get("threshold"),
                "is_all_ports": config.get("is_all_ports", False),
                "is_all_queues": config.get("is_all_queues", False),
                "config_data": json.dumps(config_data.get("new_val"))
            }
        
        update_operations.append({
            "config_id": config_id,
            "traffic_type": traffic_type,
            "updates": update_data
        })
    
    # 3. Handle create operations
    create_operations = []
    for config in operations.get("configs_to_create", []):
        traffic_type = config.get("traffic_type", "").lower()
        
        # Build create configuration data using view layer tool functions
        config.update({"old_val": {}})  # Empty for creation operations
        if traffic_type == "ingress":
            config_data = _build_single_ingress_config_data(config)
        else:
            config_data = _build_single_egress_config_data(config)
        
        # Deploy create configuration using view layer tool function
        deploy_result = deploy_roce_configuration(switch, config_data)
        if deploy_result.get("status") != 200:
            raise Exception(f"Failed to deploy new {traffic_type} configuration: {deploy_result.get('msg')}")
        
        # Prepare database create
        create_data = {
            "sysname": config.get("sysname"),
            "switch_sn": switch_sn,
            "port": config.get("port", []),
            "queue": config.get("queue", []),
            "shared_ratio": config.get("shared_ratio"),
            "threshold": config.get("threshold"),
            "is_all_ports": config.get("is_all_ports", False),
            "is_all_queues": config.get("is_all_queues", False),
            "config_data": json.dumps(config_data.get("new_val"))
        }
        
        if traffic_type == "ingress":
            create_data.update({
                "guaranteed": config.get("guaranteed"),
                "reset_offset": config.get("reset_offset"),
                "headroom": config.get("headroom")
            })
        
        create_operations.append({
            "traffic_type": traffic_type,
            "config_data": create_data
        })
    
    # Execute all database operations via service layer
    operations_data = {
        "ingress_ids_to_delete": operations.get("ingress_ids_to_delete", []),
        "egress_ids_to_delete": operations.get("egress_ids_to_delete", []),
        "update_operations": update_operations,
        "create_operations": create_operations
    }
    roce_db.execute_pfc_buffer_bulk_operations(operations_data)
    
    return jsonify({
        "status": 200,
        "msg": "PFC buffer configurations updated successfully"
    })

def _build_single_ingress_config_data(config):
    """
    构建单个 ingress 配置的下发数据
    Args:
        config: dict, ingress 配置信息，包含 old_val 属性
    Returns:
        dict: {"new_val": ..., "old_val": ...}
    """
    new_val = {
        "pfc_buffer": {
            "ingress": [
                {
                    "port": config.get("port", []),
                    "queue": config.get("queue", []),
                    "ingress_params": {
                        "guaranteed": config.get("guaranteed"),
                        "shared_ratio": config.get("shared_ratio"),
                        "threshold": config.get("threshold"),
                        "reset_offset": config.get("reset_offset"),
                        "headroom": config.get("headroom")
                    }
                }
            ]
        }
    }

    return {
        "new_val": new_val,
        "old_val": config.get("old_val", {})
    }

def _build_single_egress_config_data(config):
    """
    构建单个 egress 配置的下发数据
    Args:
        config: dict, egress 配置信息，包含 old_val 属性
    Returns:
        dict: {"new_val": ..., "old_val": ...}
    """
    new_val = {
        "pfc_buffer": {
            "egress": [
                {
                    "port": config.get("port", []),
                    "queue": config.get("queue", []),
                    "egress_params": {
                        "shared_ratio": config.get("shared_ratio"),
                        "threshold": config.get("threshold")
                    }
                }
            ]
        }
    }

    return {
        "new_val": new_val,
        "old_val": config.get("old_val", {})
    }

@roce_mold.route('/pfc_buffer_config/list', methods=['post'])
@admin_permission.require(http_exception=403)
@api_exception_handler
def roce_pfc_buffer_config_list():
    """
    Get PFC Buffer configurations list with filtering and tree structure
    Request body:
    {
        "traffic_type": "ingress|egress",  # default: "ingress"
        "switch_sn": ["string"],  # optional: filter by switch serial numbers
        "port": ["string"],  # optional: filter by port names
        "searchFields": {  # optional: search conditions
            "fields": ["port"],
            "value": "string"
        },
        "page": 1,  # pagination
        "pageSize": 10
    }
    Returns:
        JSON response with tree-structured data and pagination
    """
    data = request.get_json()
    
    # Get configurations list data via service layer
    result = roce_db.get_pfc_buffer_configs_list_data(data)
    
    return jsonify({
        "status": 200,
        "data": result.get("tree_data", []),
        "page": result.get("page_num", 1),
        "pageSize": result.get("page_size", 10),
        "total": result.get("total_count", 0)
    })
    
@roce_mold.route('/pfc_buffer_config/delete', methods=['POST'])
@admin_permission.require(http_exception=403)
@api_exception_handler
def pfc_buffer_config_delete():
    """
    Delete PFC Buffer configuration
    Request body:
    {
        "config_id": int,
        "traffic_type": "ingress|egress"
    }
    Returns:
        JSON response with operation status
    """
    data = request.get_json()
    
    if not data or 'config_id' not in data:
        raise ValueError('Missing configuration ID')
    
    config_id = data.get('config_id')
    traffic_type = data.get('traffic_type')
    
    if not traffic_type:
        raise ValueError('traffic_type is required')
    
    # Get configuration and switch information via service layer
    deletion_data = roce_db.delete_pfc_buffer_configuration_and_get_switch(config_id, traffic_type)
    
    switch = deletion_data.get("switch")
    old_val = deletion_data.get("old_val", {})
    
    # Build delete configuration data using view layer tool functions
    if traffic_type == "ingress":
        config_data = _build_single_ingress_config_data({"old_val": old_val})
    else:
        config_data = _build_single_egress_config_data({"old_val": old_val})
    
    # Deploy delete configuration using view layer tool function
    deploy_result = deploy_roce_configuration(switch, config_data)
    if deploy_result.get("status") != 200:
        raise Exception(f'Failed to deploy delete configuration {config_id}: {deploy_result.get("msg")}')
    
    # Execute database deletion via service layer
    roce_db.execute_pfc_buffer_config_deletion(config_id, traffic_type)
    
    return jsonify({
        'status': 200,
        'msg': 'Configuration deleted successfully'
    })

@roce_mold.route("/pfc_buffer_config/detail_by_switch", methods=["POST"])
@admin_permission.require(http_exception=403)
@api_exception_handler
def pfc_buffer_config_detail_by_switch():
    """
    Get all PFC Buffer configuration records for a specific switch
    Request body:
    {
        "switch_sn": "string"
    }
    Response:
    {
        "status": 200,
        "msg": "Get pfc buffer config detail successfully",
        "data": {
            "ingress": [configuration list],
            "egress": [configuration list]
        }
    }
    """
    request_json = request.get_json()
    
    if not request_json:
        raise ValueError("No request data provided")
    
    switch_sn = request_json.get("switch_sn")
    if not switch_sn:
        raise ValueError("switch_sn is required")
    
    # Get configurations detail via service layer
    result = roce_db.get_pfc_buffer_configs_detail_by_switch_sn(switch_sn)
    
    return jsonify({
        "status": 200,
        "msg": "Get pfc buffer config detail successfully",
        "data": {
            "ingress": result.get("ingress", []),
            "egress": result.get("egress", [])
        }
    })

@roce_mold.route("/pfc_wd_config/save", methods=["POST"])
@admin_permission.require(http_exception=403)
@api_exception_handler
def pfc_wd_config_save():
    """
    Create new PFC Watchdog configurations
    Request body:
    {
        "configurations": [
            {
                "sysname": "string",
                "switch_sn": "string",
                "port": ["string"],
                "queue": ["string"],
                "enabled": bool,
                "granularity": "string",
                "restore_mode": "manual|auto",
                "restore_action": "forward|drop",
                "detection_interval": "string",
                "restore_interval": "string",
                "threshold_period": "string",
                "threshold_count": "string",
                "is_all_ports": bool,
                "is_all_queues": bool
            }
        ]
    }
    Returns:
        JSON response with operation status
    """
    data = request.get_json()
    
    if not data or "configurations" not in data:
        raise ValueError("No configuration data provided")
    
    configurations = data.get("configurations", [])
    if not configurations:
        raise ValueError("No configurations provided")
    
    # Validate configurations via service layer
    validated_configs = roce_db.validate_pfc_wd_configurations(configurations)
    
    # Deploy configurations first, then save if successful
    config_data_list = []
    for config in configurations:
        switch_sn = config.get("switch_sn")
        
        # Get switch information via service layer
        switch = roce_db.get_switch_by_sn(switch_sn)
        if not switch:
            raise ValueError(f"Switch with SN {switch_sn} not found")
        
        # Build configuration data using view layer tool function
        config_data = _build_pfc_wd_config_data(config, None)
        
        # Deploy configuration using view layer tool function
        deploy_result = deploy_roce_configuration(switch, config_data)
        if deploy_result.get("status") != 200:
            raise Exception(f"Deploy PFC watchdog configuration failed: {deploy_result.get('msg')}")
        
        params = {
            "granularity": config.get("granularity", ""),
            "restore_action": config.get("restore_action", None),
            "threshold_period": config.get("threshold_period", ""),
            "threshold_count": config.get("threshold_count", "")
        }
        global_params = {k: v for k, v in params.items() if v not in [None, ""]}
        roce_db.update_global_param_for_all_data(switch_sn, "PfcWdConfiguration", global_params)

        config_data_list.append(config_data)
    
    # Save configurations to database after all deployments succeed
    created_ids = roce_db.save_pfc_wd_configurations(validated_configs, config_data_list)
    
    return jsonify({
        "status": 200,
        "msg": "PFC watchdog configurations created successfully"
    })

def pfc_wd_is_delete(config_data, existing_config):
    session = roce_db.get_session()
    deletable_fields = {
        "restore_action": ("forward", "is_delete_action"),
        "granularity": ("10", "is_delete_granularity"),
        "threshold_period": ("20", "is_delete_period"),
        "threshold_count": ("30", "is_delete_count")
    }
    for index, config in enumerate(config_data["old_val"]["pfc_watchdog"]):
        watchdog_params = config["watchdog_params"]

        for field, (default_val, flag_key) in deletable_fields.items():
            val = getattr(existing_config, field)
            watchdog_params[field] = val
            if val and val == default_val:
                count = session.query(PfcWdConfiguration).filter(
                    PfcWdConfiguration.switch_sn == existing_config.switch_sn,
                    getattr(PfcWdConfiguration, field) == val
                ).count()
                if count == 1:
                    config_data["old_val"]["pfc_watchdog"][index][flag_key] = True
    return config_data

@roce_mold.route("/pfc_wd_config/update", methods=["POST"])
@admin_permission.require(http_exception=403)
@api_exception_handler
def pfc_wd_config_update():
    """
    Update PFC Watchdog configurations for a switch (bulk edit)
    Request body:
    {
        "switch_sn": "string",
        "configurations": [
            {
                "config_id": "string",  # optional, update if exists, create if not
                "sysname": "string",
                "switch_sn": "string",
                "port": ["string"],
                "queue": ["string"],
                "enabled": bool,
                "granularity": "string",
                "restore_mode": "manual|auto",
                "restore_action": "forward|drop",
                "detection_interval": "string",
                "restore_interval": "string",
                "threshold_period": "string",
                "threshold_count": "string",
                "is_all_ports": bool,
                "is_all_queues": bool
            }
        ]
    }
    Returns:
        JSON response with operation status
    """
    data = request.get_json()
    
    if not data:
        raise ValueError("No configuration data provided")
    
    switch_sn = data.get("switch_sn")
    configurations = data.get("configurations", [])
    
    if not switch_sn:
        raise ValueError("switch_sn is required")
    
    # Prepare bulk operations via service layer
    operations_data = roce_db.bulk_update_pfc_wd_configurations(switch_sn, configurations)
    
    # Deploy all configurations first, then save if successful
    # 1. Deploy delete operations
    for config_id in operations_data.get("delete_ids", []):
        existing_configs = operations_data.get("existing_configs", {})
        existing_config = existing_configs.get(str(config_id))
        if existing_config:
            # Build delete configuration data using view layer tool function
            config_data = _build_pfc_wd_config_data({}, existing_config)
            config_data = pfc_wd_is_delete(config_data, existing_config)
            # Deploy delete configuration using view layer tool function
            switch = operations_data.get("switch")
            deploy_result = deploy_roce_configuration(switch, config_data)
            if deploy_result.get("status") != 200:
                raise Exception(f"Failed to deploy delete configuration {config_id}: {deploy_result.get('msg')}")
    
    # 2. Deploy update operations
    for config in operations_data.get("update_configs", []):
        config_id = config.get("config_id")
        existing_configs = operations_data.get("existing_configs", {})
        existing_config = existing_configs.get(str(config_id))
        
        if existing_config:
            # Build update configuration data using view layer tool function
            config_data = _build_pfc_wd_config_data(config, existing_config)
            config_data = pfc_wd_is_delete(config_data, existing_config)
            # Deploy update configuration using view layer tool function
            switch = operations_data.get("switch")
            deploy_result = deploy_roce_configuration(switch, config_data)
            if deploy_result.get("status") != 200:
                raise Exception(f"Failed to deploy update configuration {config_id}: {deploy_result.get('msg')}")
            params = {
                "granularity": config.get("granularity", ""),
                "restore_action": config.get("restore_action", None),
                "threshold_period": config.get("threshold_period", ""),
                "threshold_count": config.get("threshold_count", "")
            }
            global_params = {k: v for k, v in params.items() if v not in [None, ""]}
            roce_db.update_global_param_for_all_data(switch_sn, "PfcWdConfiguration", global_params)
            # Store deployed config data for database update
            config["config_data"] = json.dumps(config_data.get("new_val"))
    
    # 3. Deploy create operations
    for config in operations_data.get("create_configs", []):
        # Build create configuration data using view layer tool function
        config_data = _build_pfc_wd_config_data(config, None)
        
        # Deploy create configuration using view layer tool function
        switch = operations_data.get("switch")
        deploy_result = deploy_roce_configuration(switch, config_data)
        if deploy_result.get("status") != 200:
            raise Exception(f"Failed to deploy new configuration: {deploy_result.get('msg')}")
        params = {
            "granularity": config.get("granularity", ""),
            "restore_action": config.get("restore_action", None),
            "threshold_period": config.get("threshold_period", ""),
            "threshold_count": config.get("threshold_count", "")
        }
        global_params = {k: v for k, v in params.items() if v not in [None, ""]}
        roce_db.update_global_param_for_all_data(switch_sn, "PfcWdConfiguration", global_params)
        # Store deployed config data for database update
        config["config_data"] = json.dumps(config_data.get("new_val"))
    
    # Execute bulk database operations after all deployments succeed
    roce_db.execute_pfc_wd_bulk_operations(operations_data)
    
    return jsonify({
        "status": 200,
        "msg": "PFC watchdog configurations updated successfully"
    })

def _build_pfc_wd_config_data(config, existing_config=None):
    config_data = {
        "new_val": {
            "pfc_watchdog": [
                {
                    "port": config.get("port", []),
                    "code_point": config.get("queue", []),
                    "enable": str(config.get("enabled")).lower(),
                    "restore_mode": config.get("restore_mode", None),
                    "watchdog_params": {
                        "granularity": config.get("granularity", ""),
                        "restore_action": config.get("restore_action", None),
                        "detect_interval": config.get("detection_interval", ""),
                        "restore_interval": config.get("restore_interval", ""),
                        "threshold_period": config.get("threshold_period", ""),
                        "threshold_count": config.get("threshold_count", "")
                    }
                }
            ]
        },
        "old_val": json.loads(existing_config.config_data) if existing_config and getattr(existing_config, "config_data", None) else {}
    }
    return config_data

@roce_mold.route("/pfc_wd_config/list", methods=["POST"])
@admin_permission.require(http_exception=403)
@api_exception_handler
def pfc_wd_config_list():
    """
    Get PFC Watchdog configurations list with tree structure
    Request body:
    {
        "searchFields": {
            "fields": ["port"],
            "value": "search_value"
        },
        "page": 1,
        "pageSize": 10
    }
    Returns:
        JSON response with paginated tree structure data
    """
    data = request.get_json()
    
    if not data:
        raise ValueError("No query parameters provided")
    
    # Get configurations list via service layer
    result = roce_db.get_pfc_wd_configs_list_data(data)
    
    return jsonify({
        "status": 200,
        "data": result.get("tree_data", []),
        "page": result.get("page"),
        "pageSize": result.get("pageSize"),
        "total": result.get("total")
    })


@roce_mold.route("/pfc_wd_config/detail_by_switch", methods=["POST"])
@admin_permission.require(http_exception=403)
@api_exception_handler
def pfc_wd_config_detail_by_switch():
    """
    Get all PFC Watchdog configuration records for a specific switch
    Request body:
    {
        "switch_sn": "string"
    }
    Response:
    {
        "status": 200,
        "msg": "Get pfc wd config detail successfully",
        "data": [configuration list]
    }
    """
    data = request.get_json()
    
    if not data:
        raise ValueError("No request data provided")
    
    switch_sn = data.get("switch_sn")
    if not switch_sn:
        raise ValueError("switch_sn is required")
    
    # Get configurations detail via service layer
    configs = roce_db.get_pfc_wd_configs_detail_by_switch_sn(switch_sn)
    
    return jsonify({
        "status": 200,
        "msg": "Get pfc wd config detail successfully",
        "data": configs
    })


@roce_mold.route("/pfc_wd_config/delete", methods=["POST"])
@admin_permission.require(http_exception=403)
@api_exception_handler
def pfc_wd_config_delete():
    """
    Delete PFC Watchdog configuration
    Request body:
    {
        "config_id": "string"
    }
    Returns:
        JSON response with operation status
    """
    data = request.get_json()
    
    if not data or not data.get("config_id"):
        raise ValueError("No configuration ID provided")
    
    config_id = data.get("config_id")
    
    # Get configuration and switch info for deletion via service layer
    deletion_data = roce_db.get_pfc_wd_configuration_for_deletion(config_id)
    config = deletion_data.get("config")
    switch = deletion_data.get("switch")
    
    # Build delete configuration data using view layer tool function
    config_data = _build_pfc_wd_config_data({}, config)
    config_data = pfc_wd_is_delete(config_data, config)
    # Deploy delete configuration using view layer tool function
    deploy_result = deploy_roce_configuration(switch, config_data)
    if deploy_result.get("status") != 200:
        raise Exception(f"Failed to deploy delete configuration {config_id}: {deploy_result.get('msg')}")
    
    # Delete configuration from database after successful deployment
    roce_db.delete_pfc_wd_configuration(config_id)
    
    return jsonify({
        "status": 200,
        "msg": "Configuration deleted successfully"
    })

@roce_mold.route("/ecn_config/save", methods=["POST"])
@admin_permission.require(http_exception=403)
@api_exception_handler
@validate_ecn_uniqueness
def ecn_config_save():
    """
    Save ECN configurations with enhanced business rules
    Business Rules:
    1. Each switch_sn can only have one ECN main configuration
    2. When enabled=false, at least one detail is required
    3. When enabled=true, no details should be provided
    4. Deploy by detail granularity: deploy first, then save to database
    
    Request body:
    {
        "configurations": [
            {
                "sysname": "string",
                "switch_sn": "string", 
                "enabled": bool,
                "mode": "string",
                "details": [
                    {
                        "port": ["string"],
                        "queue": ["string"],
                        "max_threshold": int,
                        "min_threshold": int,
                        "drop_probability": int,
                        "ecn_threshold": int,
                        "wred_enable": bool,
                        "is_all_ports": bool,
                        "is_all_queues": bool
                    }
                ]
            }
        ]
    }
    Returns:
        JSON response with operation status
    """
    data = request.get_json()
    
    if not data or not data.get("configurations"):
        raise ValueError("No configuration data provided")
    
    configurations = data.get("configurations", [])
    
    # Phase 1: Validate configurations with business rules
    result = roce_db.validate_ecn_configurations(configurations)
    validated_configs = result.get("validated_configs", [])
    
    # Phase 2: Deploy configurations by detail granularity (deploy first, then save to database)
    for validated_config in validated_configs:
        switch = validated_config.get("switch")
        main_config = validated_config.get("main_config")
        details = validated_config.get("details", [])
        
        if not details:
            # enabled=true case: Only main configuration, no detail configurations
            config_data = _build_ecn_config_data(ecn_config=main_config)
            
            # Deploy main configuration using view layer tool function
            deploy_result = deploy_roce_configuration(switch, config_data)
            if deploy_result.get("status") != 200:
                raise Exception(f"Deploy ECN main configuration failed: {deploy_result.get('msg')}")
            
            # Save main configuration only to database
            roce_db.save_ecn_main_config_only(main_config)
        else:
            # enabled=false case: Has detail configurations, deploy each detail
            for detail in details:
                # Build detail configuration data using view layer tool function
                config_data = _build_ecn_config_data(ecn_config=main_config, ecn_config_detail=detail, existing_detail_config=None)
                
                # Deploy detail configuration using view layer tool function
                deploy_result = deploy_roce_configuration(switch, config_data)
                if deploy_result.get("status") != 200:
                    raise Exception(f"Deploy ECN detail configuration failed: {deploy_result.get('msg')}")
                
                # Save to database after successful deployment
                roce_db.save_ecn_detail_to_db(main_config, detail, config_data)
    
    return jsonify({
        "status": 200,
        "msg": "ECN configurations saved successfully"
    })


@roce_mold.route("/ecn_config/update", methods=["POST"])
@admin_permission.require(http_exception=403)
@api_exception_handler
@validate_ecn_uniqueness
def ecn_config_update():
    """
    Update ECN configurations for a switch (bulk update with enhanced business rules)
    Business Rules:
    1. Each switch_sn can only have one ECN main configuration
    2. When enabled=false, at least one detail is required
    3. When enabled=true, no details should be provided
    4. Deploy by detail granularity: deploy first, then save to database
    
    Request body:
    {
        "switch_sn": "string",
        "configurations": [
            {
                "config_id": "string",  # optional, update if exists, create if not
                "sysname": "string",
                "switch_sn": "string",
                "enabled": bool,
                "mode": "string",
                "details": [
                    {
                        "detail_id": "string",  # optional, update if exists, create if not
                        "port": ["string"],
                        "queue": ["string"],
                        "max_threshold": int,
                        "min_threshold": int,
                        "drop_probability": int,
                        "ecn_threshold": int,
                        "wred_enable": bool,
                        "is_all_ports": bool,
                        "is_all_queues": bool
                    }
                ]
            }
        ]
    }
    Returns:
        JSON response with operation status
    """
    data = request.get_json()
    
    if not data:
        raise ValueError("No configuration data provided")
    
    switch_sn = data.get("switch_sn")
    configurations = data.get("configurations", [])
    
    if not switch_sn:
        raise ValueError("switch_sn is required")
    
    # Phase 1: Analyze operations with business rules via service layer
    operations_data = roce_db.bulk_update_ecn_configurations(switch_sn, configurations)
    switch = operations_data.get("switch")
    
    # Phase 2: Deploy delete operations first (deploy first, then save to database)
    for detail_id in operations_data.get("detail_ids_to_delete", []):
        existing_detail = operations_data.get("existing_details", {}).get(str(detail_id))
        if existing_detail:
            # Build delete configuration data using view layer tool function
            config_data = _build_ecn_config_data(
                ecn_config={
                    "enabled": False,  # For delete operation
                    "mode": None
                },
                ecn_config_detail={},  # Empty for deletion
                existing_detail_config=existing_detail
            )
            
            # Deploy delete configuration using view layer tool function
            deploy_result = deploy_roce_configuration(switch, config_data)
            if deploy_result.get("status") != 200:
                raise Exception(f"Failed to deploy delete detail configuration {detail_id}: {deploy_result.get('msg')}")
    
    for main_id in operations_data.get("main_ids_to_delete", []):
        existing_config = operations_data.get("existing_configs", {}).get(str(main_id))
        if existing_config:
            # Build delete configuration data using view layer tool function
            config_data = _build_ecn_config_data(
                ecn_config={
                    "enabled": existing_config.enabled,
                    "mode": existing_config.mode
                },
                ecn_config_detail={},  # Empty for deletion
                existing_detail_config=None
            )
            config_data["old_val"] = config_data["new_val"]
            config_data["new_val"] = {}
            
            # Deploy delete configuration using view layer tool function
            deploy_result = deploy_roce_configuration(switch, config_data)
            if deploy_result.get("status") != 200:
                raise Exception(f"Failed to deploy delete main configuration {main_id}: {deploy_result.get('msg')}")
    
    # Phase 3: Deploy update operations
    update_operations = []
    for config in operations_data.get("configs_to_update", []):
        config_id = config.get("config_id")
        # Update main config only - details will be handled separately
        update_operations.append({
            "config_id": config_id,
            "updates": {
                "sysname": config.get("sysname"),
                "switch_sn": switch_sn,
                "enabled": config.get("enabled", True),
                "mode": config.get("mode", None)
            }
        })
    
    detail_update_operations = []
    for detail in operations_data.get("details_to_update", []):
        detail_id = detail.get("detail_id")
        main_config = detail.get("main_config")
        existing_detail = operations_data.get("existing_details", {}).get(str(detail_id))
        
        if existing_detail:
            # Build detail configuration data
            detail_config_data = {
                "port": detail.get("port", []),
                "queue": detail.get("queue", []),
                "max_threshold": detail.get("max_threshold", None),
                "min_threshold": detail.get("min_threshold", None),
                "drop_probability": detail.get("drop_probability", None),
                "ecn_threshold": detail.get("ecn_threshold", None),
                "wred_enable": detail.get("wred_enable", None),
                "is_all_ports": detail.get("is_all_ports", False),
                "is_all_queues": detail.get("is_all_queues", False)
            }
            
            # Build update configuration data using view layer tool function
            config_data = _build_ecn_config_data(
                ecn_config=main_config,
                ecn_config_detail=detail_config_data,
                existing_detail_config=existing_detail
            )
            
            # Deploy update configuration using view layer tool function
            deploy_result = deploy_roce_configuration(switch, config_data)
            if deploy_result.get("status") != 200:
                raise Exception(f"Failed to deploy update detail configuration {detail_id}: {deploy_result.get('msg')}")
            
            # Prepare database update
            detail_update_operations.append({
                "detail_id": detail_id,
                "updates": {
                    "sysname": main_config.get("sysname"),
                    "switch_sn": switch_sn,
                    "port": detail.get("port", []),
                    "queue": detail.get("queue", []),
                    "max_threshold": detail.get("max_threshold", None),
                    "min_threshold": detail.get("min_threshold", None),
                    "drop_probability": detail.get("drop_probability", None),
                    "ecn_threshold": detail.get("ecn_threshold", None),
                    "wred_enable": detail.get("wred_enable", None),
                    "is_all_ports": detail.get("is_all_ports", False),
                    "is_all_queues": detail.get("is_all_queues", False),
                    "config_data": json.dumps(config_data.get("new_val"))
                }
            })
    
    # Phase 4: Deploy create operations
    create_operations = []
    for config in operations_data.get("configs_to_create", []):
        # Create main configuration database data
        create_operations.append({
            "sysname": config.get("sysname"),
            "switch_sn": switch_sn,
            "enabled": config.get("enabled", True),
            "mode": config.get("mode", None)
        })
    
    detail_create_operations = []
    for detail in operations_data.get("details_to_create", []):
        main_config = detail.get("main_config")
        
        # Build detail configuration data
        detail_config_data = {
            "port": detail.get("port", []),
            "queue": detail.get("queue", []),
            "max_threshold": detail.get("max_threshold", None),
            "min_threshold": detail.get("min_threshold", None),
            "drop_probability": detail.get("drop_probability", None),
            "ecn_threshold": detail.get("ecn_threshold", None),
            "wred_enable": detail.get("wred_enable", None),
            "is_all_ports": detail.get("is_all_ports", False),
            "is_all_queues": detail.get("is_all_queues", False)
        }
        
        # Build create configuration data using view layer tool function
        config_data = _build_ecn_config_data(
            ecn_config=main_config,
            ecn_config_detail=detail_config_data,
            existing_detail_config=None
        )
        
        # Deploy create configuration using view layer tool function
        deploy_result = deploy_roce_configuration(switch, config_data)
        if deploy_result.get("status") != 200:
            raise Exception(f"Failed to deploy new detail configuration: {deploy_result.get('msg')}")
        
        # Prepare database create - will be linked to main config after creation
        detail_create_operations.append({
            "main_config": main_config,
            "detail_data": {
                "sysname": main_config.get("sysname"),
                "switch_sn": switch_sn,
                "port": detail.get("port", []),
                "queue": detail.get("queue", []),
                "max_threshold": detail.get("max_threshold", None),
                "min_threshold": detail.get("min_threshold", None),
                "drop_probability": detail.get("drop_probability", None),
                "ecn_threshold": detail.get("ecn_threshold", None),
                "wred_enable": detail.get("wred_enable", None),
                "is_all_ports": detail.get("is_all_ports", False),
                "is_all_queues": detail.get("is_all_queues", False),
                "config_data": json.dumps(config_data.get("new_val"))
            }
        })
    
    # Phase 5: Execute all database operations after successful deployments
    bulk_operations_data = {
        "main_ids_to_delete": operations_data.get("main_ids_to_delete", []),
        "detail_ids_to_delete": operations_data.get("detail_ids_to_delete", []),
        "update_operations": update_operations,
        "detail_update_operations": detail_update_operations,
        "create_operations": create_operations,
        "detail_create_operations": detail_create_operations
    }
    
    roce_db.execute_ecn_bulk_operations(bulk_operations_data)
    
    return jsonify({
        "status": 200,
        "msg": "ECN configurations updated successfully"
    })


def _build_ecn_config_data(ecn_config: dict, ecn_config_detail: dict = None, existing_detail_config: object = None):
    """
    Build ECN configuration data for deployment
    Args:
        ecn_config: dict, ECN main configuration info
        ecn_config_detail: dict, ECN detail configuration info (can be None for main config only)
        existing_detail_config: object, Existing detail configuration object (can be None)
    Returns:
        dict: ECN configuration data for deployment
    """
    # Safe access to detail config - handle None case
    detail_config = ecn_config_detail or {}
    
    config_data = {
        "new_val": {
            "ecn": {
                "mode": ecn_config.get("mode", None),
                "enable": str(ecn_config.get("enabled", False)).lower(),
                "port": detail_config.get("port", []),
                "queue": detail_config.get("queue", []),
                "wred_enable": str(detail_config.get("wred_enable", None)).lower() if detail_config.get("wred_enable") is not None else "none",
                "ecn_params": {
                    "max_thresh": detail_config.get("max_threshold", None),
                    "min_thresh": detail_config.get("min_threshold", None),
                    "drop_probability": detail_config.get("drop_probability", None),
                    "ecn_thresh": detail_config.get("ecn_threshold", None)
                }
            }
        },
        "old_val": json.loads(existing_detail_config.config_data) if existing_detail_config and existing_detail_config.config_data else {}
    }
    
    return config_data

@roce_mold.route("/ecn_config/list", methods=["POST"])
@admin_permission.require(http_exception=403)
@api_exception_handler
def ecn_config_list():
    """
    Get ECN configurations list with pagination
    Request body:
    {
        "search": "string",  # optional: search text
        "page": 1,           # optional: page number
        "pageSize": 10       # optional: page size
    }
    Returns:
        JSON response with flat list data and pagination
    """
    data = request.get_json()
    
    # Get configurations list data via service layer
    result = roce_db.get_ecn_configs_list_data(data)
    
    return jsonify({
        "status": 200,
        "data": result.get("data", []),
        "page": result.get("page", 1),
        "pageSize": result.get("pageSize", 10),
        "total": result.get("total", 0)
    })

@roce_mold.route("/ecn_config/port/list", methods=["POST"])
@admin_permission.require(http_exception=403)
@api_exception_handler
def ecn_port_config_list():
    """
    Get ECN port configurations list with tree structure and pagination
    Request body:
    {
        "page": 1,           # optional: page number
        "pageSize": 10       # optional: page size
    }
    Returns:
        JSON response with tree-structured data and pagination
    """
    data = request.get_json()
    
    # Get ECN port configurations list data via service layer
    result = roce_db.get_ecn_port_configs_list_data(data)
    
    return jsonify({
        "status": 200,
        "data": result.get("tree_data", []),
        "page": result.get("page", 1),
        "pageSize": result.get("pageSize", 10),
        "total": result.get("total", 0)
    })

@roce_mold.route("/ecn_config/delete", methods=["POST"])
@admin_permission.require(http_exception=403)
@api_exception_handler
def ecn_config_delete():
    """
    Delete ECN configuration
    Request body:
    {
        "config_id": "string"
    }
    Returns:
        JSON response with operation status
    """
    data = request.get_json()
    config_id = data.get("config_id")
    
    if not config_id:
        raise ValueError("config_id is required")
    
    # Get configuration and switch info for deletion via service layer
    deletion_data = roce_db.get_ecn_configuration_for_deletion(config_id)
    
    config = deletion_data.get("config")
    detail_configs = deletion_data.get("detail_configs", [])
    switch = deletion_data.get("switch")
    
    # Build delete configurations and deploy them
    if detail_configs:
        # Deploy delete configuration for each detail configuration
        for detail_config in detail_configs:
            config_data = _build_ecn_config_data(
                ecn_config={
                    "sysname": config.sysname,
                    "switch_sn": config.switch_sn,
                    "enabled": config.enabled,
                    "mode": config.mode
                },
                ecn_config_detail={},  # Empty for deletion
                existing_detail_config=detail_config
            )
            
            # Deploy delete configuration using view layer tool function
            deploy_result = deploy_roce_configuration(switch, config_data)
            if deploy_result.get("status") != 200:
                raise Exception(f"Failed to deploy delete detail configuration: {deploy_result.get('msg')}")
    
    if not detail_configs:
        # No detail configurations, deploy delete configuration for main config
        config_data = _build_ecn_config_data(
            ecn_config={
                "sysname": config.sysname,
                "switch_sn": config.switch_sn,
                "enabled": config.enabled,
                "mode": config.mode
            },
            ecn_config_detail={},  # Empty for deletion
            existing_detail_config=None
        )
        config_data["old_val"] = config_data["new_val"]
        config_data["new_val"] = {}
        deploy_result = deploy_roce_configuration(switch, config_data)
        if deploy_result.get("status") != 200:
            raise Exception(f"Failed to deploy delete main configuration: {deploy_result.get('msg')}")
    
    # Delete all configurations from database after successful deployment
    roce_db.delete_ecn_configuration(config_id)
    
    return jsonify({
        "status": 200,
        "msg": "Configuration deleted successfully"
    })

@roce_mold.route("/ecn_config/detail_by_switch", methods=["POST"])
@admin_permission.require(http_exception=403)
@api_exception_handler
def ecn_config_detail_by_switch():
    """
    Query all ECN configuration records for a specific switch by switch_sn
    Request body:
    {
        "switch_sn": "string"
    }
    Response:
    {
        "status": 200,
        "msg": "Get ecn config detail successfully",
        "data": [configuration list]
    }
    """
    data = request.get_json()
    
    if not data:
        raise ValueError("No request data provided")
    
    switch_sn = data.get("switch_sn")
    if not switch_sn:
        raise ValueError("switch_sn is required")

    # Get ECN configurations detail via service layer
    configs = roce_db.get_ecn_configs_detail_by_switch_sn(switch_sn)

    return jsonify({
        "status": 200,
        "msg": "Get ecn config detail successfully",
        "data": configs
    })


@roce_mold.route("/qos_config/save", methods=["POST"])
@admin_permission.require(http_exception=403)
@api_exception_handler
def qos_config_save():
    """
    Save QoS configurations with enhanced architecture
    Request body:
    {
        "configurations": [
            {
                "sysname": "switch-01",
                "switch_sn": "string",
                "forwarding_class": "fc1",
                "local_priority": 1,
                "scheduler": "Ethernet1/1",
                "mode": "SP",
                "weight": 10,
                "guaranteed_rate": 100
            }
        ],
        "ingress_configurations": [
            {
                "sysname": "switch-01",
                "switch_sn": "string",
                "classifier": "fc1",
                "trust_mode": "dscp",
                "port": ["Ethernet1/1"],
                "forwarding_class": "fc1",
                "queue": ["3"],
                "is_all_ports": false,
                "is_all_queues": false
            }
        ],
        "egress_configurations": [
            {
                "sysname": "switch-01",
                "switch_sn": "string",
                "scheduler_profile": "profile1",
                "scheduler": "scheduler1",
                "port": ["Ethernet1/1"],
                "forwarding_class": "fc1",
                "local_priority": 1,
                "is_all_ports": false
            }
        ]
    }
    Returns:
        JSON response with operation status
    """
    data = request.get_json()
    
    # Phase 1: Validate and process configurations via service layer
    result = roce_db.save_qos_configurations(data)
    processed_configs = result.get("processed_configs", [])
    
    # Phase 2: Deploy configurations first (deploy first, then save to database)
    deployed_configs = []
    for item in processed_configs:
        config = item.get("config")
        switch = item.get("switch")
        
        # Build configuration data using view layer tool function
        config_data = _build_qos_config_data({
            "forwarding_class": config.get("forwarding_class"),
            "local_priority": config.get("local_priority"),
            "scheduler": config.get("scheduler"),
            "mode": config.get("mode"),
            "weight": config.get("weight"),
            "guaranteed_rate": config.get("guaranteed_rate"),
            "ingress_configurations": config.get("ingress_configurations", []),
            "egress_configurations": config.get("egress_configurations", []),
            "config_data": {}  # Empty for creation operations
        })
        
        # Deploy configuration using view layer tool function
        deploy_result = deploy_roce_configuration(switch, config_data)
        if deploy_result.get("status") != 200:
            raise Exception(f"Deploy QoS configuration failed: {deploy_result.get('msg')}")
        
        deployed_configs.append({
            "config": config,
            "config_data": config_data
        })
    
    # Phase 3: Save configurations to database after successful deployment
    saved_configs = roce_db.create_qos_configs_in_db(processed_configs)
    
    # Phase 4: Update config_data field after successful deployment
    for i, saved_config in enumerate(saved_configs):
        config_id = saved_config.get("config_id")
        if i < len(deployed_configs):
            config_data = deployed_configs[i].get("config_data")
            roce_db.update_qos_config_data_after_deployment(
                config_id,
                json.dumps(config_data.get("new_val"))
            )
    
    return jsonify({
        "status": 200,
        "msg": "QoS configurations created successfully"
    })

@roce_mold.route("/qos_config/update", methods=["POST"])
@admin_permission.require(http_exception=403)
@api_exception_handler
def qos_config_update():
    """
    Update QoS configurations for a switch (bulk update) with enhanced architecture
    Request body:
    {
        "switch_sn": "string",
        "configurations": [
            {
                "config_id": "int",  # optional, update if exists, create if not
                "sysname": "string",
                "forwarding_class": "string",
                "local_priority": "int",
                "scheduler": "string",
                "mode": "string",
                "weight": "int",
                "guaranteed_rate": "int",
                "ingress_configurations": [
                    {
                        "ingress_id": "int",  # optional, update if exists, create if not
                        "classifier": "string",
                        "trust_mode": "string",
                        "port": ["string"],
                        "forwarding_class": "string",
                        "queue": ["string"],
                        "is_all_ports": "bool",
                        "is_all_queues": "bool"
                    }
                ],
                "egress_configurations": [
                    {
                        "egress_id": "int",  # optional, update if exists, create if not
                        "scheduler_profile": "string",
                        "scheduler": "string",
                        "port": ["string"],
                        "forwarding_class": "string",
                        "local_priority": "int",
                        "is_all_ports": "bool"
                    }
                ]
            }
        ]
    }
    Returns:
        JSON response with operation status
    """
    data = request.get_json()
    
    if not data:
        raise ValueError("No configuration data provided")
    
    switch_sn = data.get("switch_sn")
    configurations = data.get("configurations", [])
    
    if not switch_sn:
        raise ValueError("switch_sn is required")
    
    # Phase 1: Analyze operations via service layer
    operations = roce_db.bulk_update_qos_configurations(switch_sn, configurations)
    switch = operations.get("switch")
    
    # Phase 2: Deploy all configurations first (deploy first, then save to database)
    # 1. Deploy delete operations
    for main_id in operations.get("main_ids_to_delete", []):
        existing_config = operations.get("existing_main_configs", {}).get(str(main_id))
        if existing_config:
            # Build delete configuration data using view layer tool function
            old_val = {}
            if existing_config.config_data:
                try:
                    old_val = json.loads(existing_config.config_data)
                except (json.JSONDecodeError, TypeError):
                    old_val = {}
            
            config_data = _build_qos_config_data({"config_data": old_val})
            deploy_result = deploy_roce_configuration(switch, config_data)
            if deploy_result.get("status") != 200:
                raise Exception(f"Failed to deploy delete main configuration {main_id}: {deploy_result.get('msg')}")
    
    for ingress_id in operations.get("ingress_ids_to_delete", []):
        existing_config = operations.get("existing_ingress_configs", {}).get(str(ingress_id))
        if existing_config:
            # Build delete configuration data using view layer tool function
            old_val = {}
            if existing_config.config_data:
                try:
                    old_val = json.loads(existing_config.config_data)
                except (json.JSONDecodeError, TypeError):
                    old_val = {}
            
            config_data = _build_qos_config_data({"config_data": old_val})
            deploy_result = deploy_roce_configuration(switch, config_data)
            if deploy_result.get("status") != 200:
                raise Exception(f"Failed to deploy delete ingress configuration {ingress_id}: {deploy_result.get('msg')}")
    
    for egress_id in operations.get("egress_ids_to_delete", []):
        existing_config = operations.get("existing_egress_configs", {}).get(str(egress_id))
        if existing_config:
            # Build delete configuration data using view layer tool function
            old_val = {}
            if existing_config.config_data:
                try:
                    old_val = json.loads(existing_config.config_data)
                except (json.JSONDecodeError, TypeError):
                    old_val = {}
            
            config_data = _build_qos_config_data({"config_data": old_val})
            deploy_result = deploy_roce_configuration(switch, config_data)
            if deploy_result.get("status") != 200:
                raise Exception(f"Failed to deploy delete egress configuration {egress_id}: {deploy_result.get('msg')}")
    
    # 2. Deploy update operations
    main_update_operations = []
    for config in operations.get("main_configs_to_update", []):
        main_config_id = config.get("config_id")
        
        # Prepare main config update data
        update_data = {
            "sysname": config.get("sysname"),
            "switch_sn": switch_sn,
            "forwarding_class": config.get("forwarding_class"),
            "local_priority": config.get("local_priority"),
            "scheduler": config.get("scheduler"),
            "mode": config.get("mode"),
            "weight": config.get("weight"),
            "guaranteed_rate": config.get("guaranteed_rate")
        }
        main_update_operations.append({
            "config_id": main_config_id,
            "updates": update_data
        })
    
    ingress_update_operations = []
    for ingress in operations.get("ingress_configs_to_update", []):
        ingress_id = ingress.get("ingress_id")
        main_config = ingress.get("main_config")
        existing_config = operations.get("existing_ingress_configs", {}).get(str(ingress_id))
        
        if existing_config:
            # Get old configuration data
            old_val = {}
            if existing_config.config_data:
                try:
                    old_val = json.loads(existing_config.config_data)
                except (json.JSONDecodeError, TypeError):
                    old_val = {}
            
            # Build update configuration data using view layer tool function
            config_data = _build_qos_config_data({
                "forwarding_class": main_config.get("forwarding_class"),
                "local_priority": main_config.get("local_priority"),
                "scheduler": main_config.get("scheduler"),
                "mode": main_config.get("mode"),
                "weight": main_config.get("weight"),
                "guaranteed_rate": main_config.get("guaranteed_rate"),
                "ingress_configurations": [ingress],
                "egress_configurations": [],
                "config_data": old_val
            })
            
            # Deploy update configuration using view layer tool function
            deploy_result = deploy_roce_configuration(switch, config_data)
            if deploy_result.get("status") != 200:
                raise Exception(f"Failed to deploy update ingress configuration {ingress_id}: {deploy_result.get('msg')}")
            
            # Prepare database update
            update_data = {
                "sysname": ingress.get("sysname"),
                "switch_sn": switch_sn,
                "classifier": ingress.get("classifier"),
                "trust_mode": ingress.get("trust_mode"),
                "port": json.dumps(ingress.get("port", [])),
                "forwarding_class": ingress.get("forwarding_class"),
                "queue": json.dumps(ingress.get("queue", [])),
                "is_all_ports": ingress.get("is_all_ports", False),
                "is_all_queues": ingress.get("is_all_queues", False),
                "config_data": json.dumps(config_data.get("new_val"))
            }
            ingress_update_operations.append({
                "config_id": ingress_id,
                "updates": update_data
            })
    
    egress_update_operations = []
    for egress in operations.get("egress_configs_to_update", []):
        egress_id = egress.get("egress_id")
        main_config = egress.get("main_config")
        existing_config = operations.get("existing_egress_configs", {}).get(str(egress_id))
        
        if existing_config:
            # Get old configuration data
            old_val = {}
            if existing_config.config_data:
                try:
                    old_val = json.loads(existing_config.config_data)
                except (json.JSONDecodeError, TypeError):
                    old_val = {}
            
            # Build update configuration data using view layer tool function
            config_data = _build_qos_config_data({
                "forwarding_class": main_config.get("forwarding_class"),
                "local_priority": main_config.get("local_priority"),
                "scheduler": main_config.get("scheduler"),
                "mode": main_config.get("mode"),
                "weight": main_config.get("weight"),
                "guaranteed_rate": main_config.get("guaranteed_rate"),
                "ingress_configurations": [],
                "egress_configurations": [egress],
                "config_data": old_val
            })
            
            # Deploy update configuration using view layer tool function
            deploy_result = deploy_roce_configuration(switch, config_data)
            if deploy_result.get("status") != 200:
                raise Exception(f"Failed to deploy update egress configuration {egress_id}: {deploy_result.get('msg')}")
            
            # Prepare database update
            update_data = {
                "sysname": egress.get("sysname"),
                "switch_sn": switch_sn,
                "scheduler_profile": egress.get("scheduler_profile"),
                "scheduler": egress.get("scheduler"),
                "port": json.dumps(egress.get("port", [])),
                "forwarding_class": egress.get("forwarding_class"),
                "local_priority": egress.get("local_priority"),
                "is_all_ports": egress.get("is_all_ports", False),
                "config_data": json.dumps(config_data.get("new_val"))
            }
            egress_update_operations.append({
                "config_id": egress_id,
                "updates": update_data
            })
    
    # 3. Deploy create operations
    main_create_operations = []
    for config in operations.get("main_configs_to_create", []):
        # Prepare main configuration data
        qos_config = {
            "sysname": config.get("sysname"),
            "switch_sn": switch_sn,
            "forwarding_class": config.get("forwarding_class"),
            "local_priority": config.get("local_priority"),
            "scheduler": config.get("scheduler"),
            "mode": config.get("mode"),
            "weight": config.get("weight"),
            "guaranteed_rate": config.get("guaranteed_rate")
        }
        main_create_operations.append({
            "config": qos_config
        })
        config["prepared_config"] = qos_config  # Store for reference
    
    ingress_create_operations = []
    for ingress in operations.get("ingress_configs_to_create", []):
        main_config = ingress.get("main_config")
        prepared_config = main_config.get("prepared_config")
        
        if prepared_config:
            # Build create configuration data using view layer tool function
            config_data = _build_qos_config_data({
                "forwarding_class": prepared_config.get("forwarding_class"),
                "local_priority": prepared_config.get("local_priority"),
                "scheduler": prepared_config.get("scheduler"),
                "mode": prepared_config.get("mode"),
                "weight": prepared_config.get("weight"),
                "guaranteed_rate": prepared_config.get("guaranteed_rate"),
                "ingress_configurations": [ingress],
                "egress_configurations": [],
                "config_data": {}
            })
            
            # Deploy create configuration using view layer tool function
            deploy_result = deploy_roce_configuration(switch, config_data)
            if deploy_result.get("status") != 200:
                raise Exception(f"Failed to deploy new ingress configuration: {deploy_result.get('msg')}")
            
            # Prepare database create
            create_data = {
                "sysname": ingress.get("sysname"),
                "switch_sn": switch_sn,
                "qos_config_id": None,  # Will be set after main config creation
                "forwarding_class": ingress.get("forwarding_class"),
                "classifier": ingress.get("classifier"),
                "port": json.dumps(ingress.get("port", [])),
                "queue": json.dumps(ingress.get("queue", [])),
                "trust_mode": ingress.get("trust_mode"),
                "is_all_ports": ingress.get("is_all_ports", False),
                "is_all_queues": ingress.get("is_all_queues", False),
                "config_data": json.dumps(config_data.get("new_val"))
            }
            ingress_create_operations.append({
                "config_data": create_data,
                "main_config_ref": main_config  # Reference to main config for linking
            })
    
    egress_create_operations = []
    for egress in operations.get("egress_configs_to_create", []):
        main_config = egress.get("main_config")
        prepared_config = main_config.get("prepared_config")
        
        if prepared_config:
            # Build create configuration data using view layer tool function
            config_data = _build_qos_config_data({
                "forwarding_class": prepared_config.get("forwarding_class"),
                "local_priority": prepared_config.get("local_priority"),
                "scheduler": prepared_config.get("scheduler"),
                "mode": prepared_config.get("mode"),
                "weight": prepared_config.get("weight"),
                "guaranteed_rate": prepared_config.get("guaranteed_rate"),
                "ingress_configurations": [],
                "egress_configurations": [egress],
                "config_data": {}
            })
            
            # Deploy create configuration using view layer tool function
            deploy_result = deploy_roce_configuration(switch, config_data)
            if deploy_result.get("status") != 200:
                raise Exception(f"Failed to deploy new egress configuration: {deploy_result.get('msg')}")
            
            # Prepare database create
            create_data = {
                "sysname": egress.get("sysname"),
                "switch_sn": switch_sn,
                "qos_config_id": None,  # Will be set after main config creation
                "scheduler": egress.get("scheduler"),
                "scheduler_profile": egress.get("scheduler_profile"),
                "port": json.dumps(egress.get("port", [])),
                "local_priority": egress.get("local_priority"),
                "forwarding_class": egress.get("forwarding_class"),
                "is_all_ports": egress.get("is_all_ports", False),
                "config_data": json.dumps(config_data.get("new_val"))
            }
            egress_create_operations.append({
                "config_data": create_data,
                "main_config_ref": main_config  # Reference to main config for linking
            })
    
    # Phase 3: Execute all database operations after successful deployment
    operations_data = {
        "main_ids_to_delete": operations.get("main_ids_to_delete", []),
        "ingress_ids_to_delete": operations.get("ingress_ids_to_delete", []),
        "egress_ids_to_delete": operations.get("egress_ids_to_delete", []),
        "main_update_operations": main_update_operations,
        "ingress_update_operations": ingress_update_operations,
        "egress_update_operations": egress_update_operations,
        "main_create_operations": main_create_operations,
        "ingress_create_operations": ingress_create_operations,
        "egress_create_operations": egress_create_operations
    }
    
    # Execute bulk database operations after successful deployments
    roce_db.execute_qos_bulk_operations(operations_data)
    
    # Link ingress/egress configs to main configs after creation
    for create_op in main_create_operations:
        if create_op.get("created_id"):
            main_config_id = create_op.get("created_id")
            
            # Update ingress configs with main_config_id
            for ingress_op in ingress_create_operations:
                if ingress_op.get("main_config_ref") == create_op.get("config"):
                    ingress_op["config_data"]["qos_config_id"] = main_config_id
            
            # Update egress configs with main_config_id  
            for egress_op in egress_create_operations:
                if egress_op.get("main_config_ref") == create_op.get("config"):
                    egress_op["config_data"]["qos_config_id"] = main_config_id
    
    return jsonify({
        "status": 200,
        "msg": "QoS configurations updated successfully"
    })

def _build_qos_config_data(config):
    """构建 QoS 配置下发数据"""
    # 获取现有配置
    old_val = {}
    if config.get("config_data"):
        try:
            old_val = json.loads(config.get("config_data", {}))
        except (json.JSONDecodeError, TypeError):
            old_val = {}

    # 构建新配置
    new_val = {
        "service_scheduler": {
            "configuration": [{
                "forwarding_class_name": config.get("forwarding_class"),
                "local_priority": config.get("local_priority"),
                "scheduler_name": config.get("scheduler"),
                "mode": config.get("mode"),
                "scheduler_params": {
                    "weight": config.get("weight"),
                    "guaranteed_rate": config.get("guaranteed_rate")
                }
            }],
            "ingress": [],
            "egress": []
        }
    }

    # 添加入站配置
    for ingress in config.get("ingress_configurations", []):
        new_val["service_scheduler"]["ingress"].append({
            "classifier_name": ingress.get("classifier"),
            "trust_mode": ingress.get("trust_mode"),
            "ports": ingress.get("port", []),
            "forwarding_class_name": ingress.get("forwarding_class"),
            "code_point": ingress.get("queue", [])
        })

    # 添加出站配置
    for egress in config.get("egress_configurations", []):
        new_val["service_scheduler"]["egress"].append({
            "scheduler_profile_name": egress.get("scheduler_profile"),
            "scheduler_name": egress.get("scheduler"),
            "ports": egress.get("port", []),
            "forwarding_class_name": egress.get("forwarding_class"),
            "local_priority": egress.get("local_priority")
        })

    return {
        "new_val": new_val,
        "old_val": old_val
    }


@roce_mold.route("/qos_config/list", methods=["POST"])
@admin_permission.require(http_exception=403)
@api_exception_handler
def qos_config_list():
    """
    Get QoS configurations list with tree structure and pagination
    Request body:
    {
        "switch_sn": ["string"],  # optional: filter by switch serial numbers
        "page": 1,                # optional: page number
        "pageSize": 10            # optional: page size
    }
    Returns:
        JSON response with tree-structured data and pagination
    """
    data = request.get_json()
    
    # Get configurations list data via service layer
    result = roce_db.get_qos_configs_list_data(data)
    
    return jsonify({
        "status": 200,
        "data": result.get("tree_data", []),
        "page": result.get("page", 1),
        "pageSize": result.get("pageSize", 10),
        "total": result.get("total", 0)
    })
    
        
@roce_mold.route("/qos_port_config/list", methods=["POST"])
@admin_permission.require(http_exception=403)
@api_exception_handler
def qos_port_config_list():
    """
    Get QoS port configurations list with tree structure and pagination
    Request body:
    {
        "traffic_type": "ingress|egress",  # required: traffic direction
        "switch_sn": ["string"],           # optional: filter by switch serial numbers
        "page": 1,                        # optional: page number
        "pageSize": 10                    # optional: page size
    }
    Returns:
        JSON response with tree-structured data and pagination
    """
    data = request.get_json()
    
    # Get port configurations list data via service layer
    result = roce_db.get_qos_port_configs_list_data(data)
    
    return jsonify({
        "status": 200,
        "data": result.get("tree_data", []),
        "page": result.get("page", 1),
        "pageSize": result.get("pageSize", 10),
        "total": result.get("total", 0)
    })

@roce_mold.route("/qos_config/scheduler_list", methods=["POST"])
@admin_permission.require(http_exception=403)
@api_exception_handler
def qos_schduler_list():
    """
    Get QoS scheduler list for a specific switch
    Request body:
    {
        "switch_sn": "string"  # required: switch serial number
    }
    Returns:
        JSON response with scheduler list
    """
    data = request.get_json()
    switch_sn = data.get('switch_sn') if data else None
    
    # Get scheduler list data via service layer
    scheduler_list = roce_db.get_qos_scheduler_list_data(switch_sn)
    
    return jsonify({
        'status': 200,
        'msg': 'success',
        'data': scheduler_list
    })

@roce_mold.route("/qos_config/delete", methods=["POST"])
@admin_permission.require(http_exception=403)
@api_exception_handler
def qos_config_delete():
    """
    Delete QoS configuration with enhanced architecture
    Request body:
    {
        "config_id": "string"
    }
    Returns:
        JSON response with operation status
    """
    data = request.get_json()
    config_id = data.get("config_id")
    
    if not config_id:
        raise ValueError("config_id is required")
    
    # Get configuration and switch info for deletion via service layer
    deletion_data = roce_db.delete_qos_configuration_with_deployment(config_id)
    
    config = deletion_data.get("config")
    switch = deletion_data.get("switch")
    config_data = deletion_data.get("config_data")
    
    # Build delete configuration data using view layer tool function
    delete_config_data = _build_qos_config_data({"config_data": config_data})
    
    # Deploy delete configuration using view layer tool function
    deploy_result = deploy_roce_configuration(switch, delete_config_data)
    if deploy_result.get("status") != 200:
        raise Exception(f"Failed to deploy delete configuration {config_id}: {deploy_result.get('msg')}")
    
    # Delete configuration from database after successful deployment
    roce_db.execute_qos_configuration_deletion(config_id)
    
    return jsonify({
        "status": 200,
        "msg": "QoS configuration deleted successfully"
    })

@roce_mold.route("/qos_config/detail_by_switch", methods=["POST"])
@admin_permission.require(http_exception=403)
@api_exception_handler
def qos_config_detail_by_switch():
    """
    Get QoS configuration details for a specific switch with enhanced architecture
    Request body:
    {
        "switch_sn": "string"
    }
    Response:
    {
        "status": 200,
        "msg": "Get qos config detail successfully",
        "data": [configuration list with ingress/egress details]
    }
    """
    data = request.get_json()
    
    if not data:
        raise ValueError("No request data provided")
    
    switch_sn = data.get("switch_sn")
    if not switch_sn:
        raise ValueError("switch_sn is required")

    # Get QoS configurations detail via service layer
    configs = roce_db.get_qos_configs_detail_by_switch_sn(switch_sn)

    return jsonify({
        "status": 200,
        "msg": "Get qos config detail successfully",
        "data": configs
    })