import logging
from flask import Blueprint, jsonify, request
import requests
import hashlib
from server.util.permission import super_user_permission
from server.db.pg_engine import get_pg_session
from server.db.models.wireless_openwifi import Devices
LOG = logging.getLogger(__name__)
wireless_device_mold = Blueprint("wireless_device_mold", __name__)

GATEWAY_BASE_URL = "https://owgw:17002/api/v1/devices"
GW_DISCONNECT_URL = "https://owgw:17002/api/v1/disconnect"
def compute_hash(*args):
    sha256 = hashlib.sha256()
    for arg in args:
        if isinstance(arg, (int, float)):
            sha256.update(str(arg).encode('utf-8'))
        else:
            sha256.update(arg.encode('utf-8'))
    return sha256.hexdigest()

INTERNAL_HEADER = {
    "X-API-KEY": compute_hash('https://openwifi.wlan.local:16002'),
    "X-INTERNAL-NAME": "owgw",
}
@wireless_device_mold.route('/batch_delete', methods=['DELETE'])
@super_user_permission.require(http_exception=403)
def batch_delete():
    try:
        data = request.get_json()
        sn_list = data.get("snList")
        if not sn_list or not isinstance(sn_list, list):
            return jsonify({"status": 400, "info": "Missing or invalid 'snList' parameter"}), 400
        try:
            json_data = {"snList": sn_list}
            gw_response = requests.post(GW_DISCONNECT_URL, json=json_data,headers=INTERNAL_HEADER, verify=False, timeout=5)
            gw_data = gw_response.json()
        except Exception as gw_error:
            return jsonify({"status": 502, "info": f"Failed to contact GW disconnect API: {str(gw_error)}"}), 502

        if gw_data.get("Code") != 0:
            return jsonify(
                {"status": 409, "info": 'Some devices could not be disconnected. Cannot proceed with deletion.{}_{}'.format(gw_data,json_data)}), 409
        # Step 2: 删除设备
        try:
            with (get_pg_session() as pg_session):
                devices_items = pg_session.query(Devices).filter(
                    Devices.serialnumber.in_(sn_list)
                ).delete(synchronize_session=False)
            return jsonify({"status": 200, "info": 'Batch delete device success.{}'.format(devices_items)}), 200
        except Exception as e:
            return jsonify({"status": 500, "info": f"Error batch delete: {str(e)}"}), 500
    except Exception as e:
        return jsonify({"status": 500, "info": f"Error batch delete: {str(e)}"}), 500
