import json
import logging
import traceback
import threading
import ipaddress
from flask import Blueprint, jsonify, Response, request
from datetime import timedelta, datetime, date
from sqlalchemy import func

from server.util import utils, fabric_topology_util
from server.util.permission import admin_permission
from server.db.models.dc_blueprint import dc_fabric_db, DCFabricUnit, DCFabricTemplate, DCFabricTopology, DCFabricTopologyNode, DCLogicalNetwork, DCLogicalRouter, DCLogicalSwitch, DCLogicalPort, DCVirtualNetwork, DCLogicalNetworkConfigDetail, DCLogicalInterface, DCLogicalInterfaceConfig
from server.db.models.dc_virtual_resource import DCVirtualResourceHostLinkPort, DCVirtualResourceNetwork, NodeNicPortgroup, SwitchPortgroup, dc_virtual_resource_db, VlanDomainGroup, DCVirtualResourceVPC, DCVirtualResourcePoolAZ, DCVirtualHostNetworkMapping
from server.db.models.resource_pool import resource_pool_vlandomain_db, is_ranges_conflict, ResourcePoolVlanDomain, resource_pool_vni_db
from server.util.fabric_topology_util import TopologyBuilder, update_topology_by_template, update_topology_node_info, allocate_asn, allocate_ip, allocate_ip_pair, delete_ip_values
from server.db.models import inventory
from server.util.prometheus_util import lldp_refresh, get_target_interfaces
from celery_app.config_distribution_task import config_distribution_cli
from server.constants import OverlayStatus, DeployStatus

inven_db = inventory.inven_db

dc_blueprint_mold = Blueprint("dc_blueprint", __name__, template_folder="templates")
LOG = logging.getLogger(__name__)

dc_task_status = ["PENDING", "RUNNING", "SUCCEED", "FAILED"]

@dc_blueprint_mold.route("/fabric_unit/list", methods=["POST"])
@admin_permission.require(http_exception=403)
def fabric_unit_list():
    try:
        page_num, page_size, total_count, query_obj = utils.query_helper(DCFabricUnit)
        response = {
            "data": [{
                "id": unit.id,
                "name": unit.name,
                "description": unit.description if unit.description else "--",
                "leaf_count": len(unit.unit_info.get("leaf", [])),
                "mlag_count": sum(1 for leaf in unit.unit_info.get("leaf", []) if leaf["strategy"] == "MLAG"),
                "access_count": len(unit.unit_info.get("access", [])),
                "unit_info": unit.unit_info,
            } for unit in query_obj],
            "page": page_num,
            "pageSize": page_size,
            "total": total_count,
            "status": 200
        }
        return jsonify(response)
    except Exception as e:
        return jsonify({'status': 400, 'info': 'Failed to list unit'})


@dc_blueprint_mold.route("/fabric_unit/save", methods=["POST"])
@admin_permission.require(http_exception=403)
def fabric_unit_save():
    try:
        data = request.get_json()
        if not data:
            return jsonify({'info': "Invalid request data", 'status': 400})

        id = data.get('id', None)
        name = data.get('name')
        description = data.get('description')
        unit_info = data.get('unit_info', {})
        
        res, msg = validate_unit_info(unit_info)
        if res:
            dc_fabric_db.update_fabric_unit(id, name, unit_info, description)
            return jsonify({'status': 200, 'info': 'Save unit successed.'})
        else:
            return jsonify({'status': 400, 'info': f'Save unit failed: {msg}'})
    except Exception as e:
        LOG.error(traceback.format_exc())
        return jsonify({'status': 400, 'info': f'Save unit failed. {str(e)}'})


def validate_unit_info(unit_info):
    if "leaf" in unit_info:
        if not isinstance(unit_info["leaf"], list):
            return False, "leaf should be a list."

        for item in unit_info["leaf"]:
            if not isinstance(item, dict):
                return False, "Each item in leaf should be a dictionary."
    
    if "access" in unit_info:
        if not isinstance(unit_info["access"], list):
            return False, "access should be a list."

        for item in unit_info["access"]:
            if not isinstance(item, dict):
                return False, "Each item in access should be a dictionary."
    
    return True, "Validation successful."


@dc_blueprint_mold.route("/fabric_unit/view", methods=["POST"])
@admin_permission.require(http_exception=403)
def fabric_unit_view():
    try:
        data = request.get_json()
        if not data:
            return jsonify({'info': "Invalid request data", 'status': 400})

        unit_id = data.get('unit_id')
        unit = dc_fabric_db.get_fabric_unit_by_id(unit_id)

        return jsonify({'status': 200, 'info': 'Get unit successed.', "data": unit.make_dict()})
    except Exception as e:
        LOG.error(traceback.format_exc())
        return jsonify({'status': 400, 'info': 'Get unit failed.'})


@dc_blueprint_mold.route("/fabric_unit/delete", methods=["POST"])
@admin_permission.require(http_exception=403)
def fabric_unit_delete():
    try:
        data = request.get_json()
        if not data:
            return jsonify({'info': "Invalid request data", 'status': 400})

        unit_id = data.get('unit_id')
        dc_fabric_db.del_fabric_unit_by_id(unit_id)

        return jsonify({'status': 200, 'info': 'Delete unit successed.'})
    except Exception as e:
        LOG.error(traceback.format_exc())
        return jsonify({'status': 400, 'info': 'Delete unit failed.'})


@dc_blueprint_mold.route("/fabric_unit/clone", methods=["POST"])
@admin_permission.require(http_exception=403)
def fabric_unit_clone():
    try:
        data = request.get_json()
        if not data:
            return jsonify({'info': "Invalid request data", 'status': 400})

        unit_id = data.get('clone_unit_id')
        name = data.get('name')
        description = data.get('description')
        
        exist = dc_fabric_db.check_fabric_unit_name(name)
        if exist:
            return jsonify({'status': 400, 'info': 'Unit name existed.'})
        
        unit = dc_fabric_db.get_fabric_unit_by_id(unit_id)
        dc_fabric_db.update_fabric_unit(unit_id=None, unit_name=name, unit_info=unit.unit_info, description=description)

        return jsonify({'status': 200, 'info': 'Clone unit successed.'})
    except Exception as e:
        LOG.error(traceback.format_exc())
        return jsonify({'status': 400, 'info': 'Clone unit failed.'})


@dc_blueprint_mold.route("/fabric_template/list", methods=["POST"])
@admin_permission.require(http_exception=403)
def fabric_template_list():
    try:
        page_num, page_size, total_count, query_obj = utils.query_helper(DCFabricTemplate)
        response = {
            "data": [{
                "id": template.id,
                "name": template.name,
                "description": template.description if template.description else "--",
                "type": template.type,
                "underlay_routing_protocol": template.underlay_routing_protocol,
                "overlay_control_protocol": template.overlay_control_protocol,
            } for template in query_obj],
            "page": page_num,
            "pageSize": page_size,
            "total": total_count,
            "status": 200
        }
        return jsonify(response)
    except Exception as e:
        return jsonify({'status': 400, 'info': 'Failed to list template'})
    

@dc_blueprint_mold.route("/fabric_template/save", methods=["POST"])
@admin_permission.require(http_exception=403)
def fabric_template_save():
    try:
        data = request.get_json()
        if not data:
            return jsonify({'info': "Invalid request data", 'status': 400})

        id = data.get('id', None)
        name = data.get('name')
        description = data.get('description')
        type = data.get("type")
        underlay_routing_protocol = data.get("underlay_routing_protocol")
        overlay_control_protocol = data.get("overlay_control_protocol")
        template_info = data.get('template_info', {})
        
        # 将unit全量快照存在template中
        if type == "3-stage":
            new_unit_list = []
            for unit in template_info.get("unit"):
                unit_id = unit.get("id")
                unit_info = dc_fabric_db.get_fabric_unit_by_id(unit_id)
                if unit_info:
                    unit_dict = unit_info.make_dict()
                    unit_dict["count"] = unit.get("count", 1)
                    new_unit_list.append(unit_dict)
                else:
                    return jsonify({'info': f"Unit {unit_id} not found", 'status': 400})
            template_info["unit"] = new_unit_list
        elif type == "5-stage":                
            for pod in template_info.get("pod"):
                new_unit_list = []
                for unit in pod.get("unit", []):
                    unit_id = unit.get("id")
                    unit_info = dc_fabric_db.get_fabric_unit_by_id(unit_id)
                    if unit_info:
                        unit_dict = unit_info.make_dict()
                        unit_dict["count"] = unit.get("count", 1)
                        new_unit_list.append(unit_dict)
                    else:
                        return jsonify({'info': f"Unit {unit_id} not found", 'status': 400})
                pod["unit"] = new_unit_list
        template_info['type'] = type
        template_info["underlay_routing_protocol"] = underlay_routing_protocol
        template_info["overlay_control_protocol"] = overlay_control_protocol

        dc_fabric_db.update_fabric_template(id, name, type=type, underlay_routing_protocol=underlay_routing_protocol, 
                                            overlay_control_protocol=overlay_control_protocol, template_info=template_info, description=description)
        return jsonify({'status': 200, 'info': 'Save template successed.'})
    except Exception as e:
        LOG.error(traceback.format_exc())
        return jsonify({'status': 400, 'info': f'Save template failed. {str(e)}'})


@dc_blueprint_mold.route("/fabric_template/view", methods=["POST"])
@admin_permission.require(http_exception=403)
def fabric_template_view():
    try:
        data = request.get_json()
        if not data:
            return jsonify({'info': "Invalid request data", 'status': 400})

        template_id = data.get('template_id')
        template = dc_fabric_db.get_fabric_template_by_id(template_id)

        return jsonify({'status': 200, 'info': 'Get template successed.', "data": template.make_dict()})
    except Exception as e:
        LOG.error(traceback.format_exc())
        return jsonify({'status': 400, 'info': 'Get template failed.'})


@dc_blueprint_mold.route("/fabric_template/delete", methods=["POST"])
@admin_permission.require(http_exception=403)
def fabric_template_delete():
    try:
        data = request.get_json()
        if not data:
            return jsonify({'info': "Invalid request data", 'status': 400})

        template_id = data.get('template_id')
        dc_fabric_db.del_fabric_template_by_id(template_id)

        return jsonify({'status': 200, 'info': 'Delete template successed.'})
    except Exception as e:
        LOG.error(traceback.format_exc())
        return jsonify({'status': 400, 'info': 'Delete template failed.'})
    

@dc_blueprint_mold.route("/fabric_template/clone", methods=["POST"])
@admin_permission.require(http_exception=403)
def fabric_template_clone():
    try:
        data = request.get_json()
        if not data:
            return jsonify({'info': "Invalid request data", 'status': 400})

        template_id = data.get('clone_template_id')
        name = data.get('name')
        description = data.get('description')
        
        exist = dc_fabric_db.check_fabric_template_name(name)
        if exist:
            return jsonify({'status': 400, 'info': 'Template name existed.'})
        
        template = dc_fabric_db.get_fabric_template_by_id(template_id)
        dc_fabric_db.update_fabric_template(template_id=None, template_name=name, type=template.type, underlay_routing_protocol=template.underlay_routing_protocol, 
                                            overlay_control_protocol=template.overlay_control_protocol, template_info=template.template_info, 
                                            description=description)

        return jsonify({'status': 200, 'info': 'Clone template successed.'})
    except Exception as e:
        LOG.error(traceback.format_exc())
        return jsonify({'status': 400, 'info': 'Clone template failed.'})


@dc_blueprint_mold.route("/fabric_topo/create", methods=["POST"])
@admin_permission.require(http_exception=403)
def fabric_topo_create():
    try:
        data = request.get_json()
        if not data:
            return jsonify({'info': "Invalid request data", 'status': 400})

        fabric_name = data.get('fabric_name')
        fabric_template_id = data.get('fabric_template_id')
        
        db_session = inven_db.get_session()
        fabric = db_session.query(inventory.Fabric).filter(inventory.Fabric.fabric_name == fabric_name).first()
        
        template = dc_fabric_db.get_fabric_template_by_id(fabric_template_id)
        
        #构建拓扑 
        template_info = template.template_info
        builder = TopologyBuilder(template_info, template.type, template.underlay_routing_protocol, template.overlay_control_protocol)
        topology = builder.build_topology()
        template_info['topology']= topology
        fabric_topology = dc_fabric_db.add_fabric_topology(fabric_id=fabric.id, template_name=template.name, fabric_config=template.template_info)
        #创建node节点
        update_topology_node_info(topology, fabric_topology.id)

        return jsonify({'status': 200, 'info': 'Create fabric topology successed.', 'data': fabric_topology.make_dict()})
    except Exception as e:
        LOG.error(traceback.format_exc())
        return jsonify({'status': 400, 'info': 'Create fabric topology failed.'})


@dc_blueprint_mold.route("/fabric_topo/edit", methods=["POST"])
@admin_permission.require(http_exception=403)
def fabric_topo_edit():
    try:
        data = request.get_json()
        if not data:
            return jsonify({'info': "Invalid request data", 'status': 400})

        fabric_name = data.get('fabric_name')
        template_name = data.get('template_name')
        fabric_config = data.get('fabric_config')
        
        db_session = inven_db.get_session()
        fabric = db_session.query(inventory.Fabric).filter(inventory.Fabric.fabric_name == fabric_name).first()
        topo = db_session.query(DCFabricTopology).filter(DCFabricTopology.fabric_id == fabric.id).first()
     
        #更新拓扑结构 
        type = fabric_config.get("type")
        topology = fabric_config.get("topology")
        update_topology_by_template(topology, fabric_config, type)
        fabric_config['topology']= topology
        #更新node节点
        update_topology_node_info(topology, topo.id)
        dc_fabric_db.update_fabric_topology(fabric_id=fabric.id, template_name=template_name, fabric_config=fabric_config)
        fabric_topology = dc_fabric_db.get_fabric_topo_by_id(topo.id)
        return jsonify({'status': 200, 'info': 'Edit fabric topology successed.', 'data': fabric_topology.make_dict()})
    except Exception as e:
        LOG.error(traceback.format_exc())
        return jsonify({'status': 400, 'info': 'Edit fabric topology failed.'})


@dc_blueprint_mold.route("/fabric_topo/view", methods=["POST"])
@admin_permission.require(http_exception=403)
def fabric_topo_view():
    try:
        data = request.get_json()
        if not data:
            return jsonify({'info': "Invalid request data", 'status': 400})

        fabric_topo_id = data.get('fabric_topo_id')
        topology = dc_fabric_db.get_fabric_topo_by_id(fabric_topo_id)
        if topology:
            res = topology.make_dict()
        else:
            res = {}
        return jsonify({'status': 200, 'info': 'Get template successed.', "data": res})
    except Exception as e:
        LOG.error(traceback.format_exc())
        return jsonify({'status': 400, 'info': 'Get template failed.'})


@dc_blueprint_mold.route("/fabric_topo/list", methods=["POST"])
@admin_permission.require(http_exception=403)
def fabric_topo_list():
    try:
        page_num, page_size, total_count, query_obj = utils.query_helper(DCFabricTopology)
        
        data = []
        for topo in query_obj:
            topo_data = topo.make_dict()
            info = {
                "id": topo.id,
                "fabric_name": topo.fabric.fabric_name,
                "template_name": topo.template_name,
                "switch_num": len(topo.fabric_config.get("topology", {}).get("nodes", [])),
                "underlay_routing_protocol": topo.fabric_config.get("underlay_routing_protocol", ""),
                "status": topo.status,
                "modified_time" : topo_data.get("modified_time")
            }
            data.append(info)
        
        response = {
            "data": data,
            "page": page_num,
            "pageSize": page_size,
            "total": total_count,
            "status": 200
        }
        return jsonify(response)
    except Exception as e:
        LOG.error(traceback.format_exc())
        return jsonify({'status': 400, 'info': 'Failed to list template'})
    
    
@dc_blueprint_mold.route("/fabric_topo/delete", methods=["POST"])
@admin_permission.require(http_exception=403)
def fabric_topo_delete():    
    try:
        data = request.get_json()
        if not data:
            return jsonify({'info': "Invalid request data", 'status': 400})

        fabric_topo_id = data.get('fabric_topo_id')
        dc_fabric_db.del_fabric_topo_by_id(fabric_topo_id)

        return jsonify({'status': 200, 'info': 'Delete topo successed.'})
    except Exception as e:
        LOG.error(traceback.format_exc())
        return jsonify({'status': 400, 'info': 'Delete topo failed.'})
    
    
@dc_blueprint_mold.route("/fabric_topo/auto_link", methods=["POST"])
@admin_permission.require(http_exception=403)
def fabric_topo_auto_link():    
    try:
        data = request.get_json()
        if not data:
            return jsonify({'info': "Invalid request data", 'status': 400})

        fabric_topo_id = data.get('fabric_topo_id')
        topo = dc_fabric_db.get_fabric_topo_by_id(fabric_topo_id)
        fabric_config = topo.fabric_config.copy()
        
        node_sn_list = [node["switch_sn"] for node in fabric_config["topology"]["nodes"] if node["switch_sn"]]
        lldp_info = lldp_refresh(node_sn_list)
        # print(lldp_info)
        
        for edge in fabric_config["topology"]["edges"]:
            if edge["source_sn"] in lldp_info and edge["target_sn"] in lldp_info:
                if lldp_info.get(edge["source_sn"], {}).get(edge["target_sn"], []):
                    port_info = lldp_info[edge["source_sn"]][edge["target_sn"]]
                    for index, link in enumerate(edge["link_info"]):
                        if index < len(port_info):
                            link["source_port"] = port_info[index]["source_port"]
                            link["target_port"] = port_info[index]["target_port"]
                            link["link_status"] = "passed"
                        
                elif lldp_info.get(edge["target_sn"], {}).get(edge["source_sn"], []):
                    port_info = lldp_info[edge["target_sn"]][edge["source_sn"]]
                    for index, link in enumerate(edge["link_info"]):
                        if index < len(port_info):
                            link["source_port"] = port_info[index]["target_port"]
                            link["target_port"] = port_info[index]["source_port"]
                            link["link_status"] = "passed"
            else:
                for index, link in enumerate(edge["link_info"]):
                    link["link_status"] = "failed"
        
        #更新node节点
        update_topology_node_info(fabric_config["topology"], topo.id)  
        dc_fabric_db.update_fabric_topology(fabric_id=topo.fabric_id, template_name=topo.template_name, fabric_config=fabric_config)
        fabric_topology = dc_fabric_db.get_fabric_topo_by_id(fabric_topo_id)
                    
        return jsonify({'status': 200, 'info': 'Auto Link topo successed.', 'data': fabric_topology.make_dict()})
    except Exception as e:
        LOG.error(traceback.format_exc())
        return jsonify({'status': 400, 'info': 'Auto Link topo failed.'})
    
    
@dc_blueprint_mold.route("/fabric_topo/deployment", methods=["POST"])
@admin_permission.require(http_exception=403)
def fabric_topo_deployment():    
    try:
        data = request.get_json()
        if not data:
            return jsonify({'info': "Invalid request data", 'status': 400})

        fabric_topo_id = data.get('fabric_topo_id')
        topology = dc_fabric_db.get_fabric_topo_by_id(fabric_topo_id)
        if topology.status == "Deploying":
            return jsonify({'info': "Config distribution task is running, please wait a few minutes.", 'status': 400})
        nodes = dc_fabric_db.get_fabric_topology_node(fabric_topo_id)
        
        config_dict = {}
        for node in nodes:
            new_node_config = {}
            if not node.switch_sn:
                continue
            new_node_config["hostname"] = node.node_info["hostname"]
            if node.type == "access":
                pass
            elif node.type == "leaf" or node.type == "spine":
                peer_link_info = {}
                if node.node_info.get("strategy", "") == "MLAG":
                    new_node_config["mlag"] = {
                            "peer_vlan_id": node.node_info["peer_vlan_id"], 
                            "mlag_l3_interface_ip_address": node.node_info["mlag_l3_interface_ip_address"],  
                            "mlag_peer_lag_interface_name": node.node_info["mlag_peer_lag_interface_name"], 
                            "mlag_interface_name": [peer_link["source_port"] for peer_link in node.node_info["links"]["peer_link"]],
                            "peer_ipv4_address": node.node_info["peer_ipv4_address"], 
                            "domain_id": node.node_info["domain_id"], 
                            "domain_id_node": node.node_info["domain_id_node"]
                    }
                    peer_link_info["mlag_l3_interface_ip_address"] = node.node_info["mlag_l3_interface_ip_address"]
                    peer_link_info["peer_ipv4_address"] = node.node_info["peer_ipv4_address"]
                    peer_link_info["mlag_l3_interface_network_address"] = str(ipaddress.IPv4Interface(node.node_info["mlag_l3_interface_ip_address"]).network.network_address) + "/31"
                    
                spine_link = []
                for link in node.node_info["links"]["spine_link"]:
                    spine_link.append({
                        "interface_name": link["source_port"],
                        "description": f'linking_{link["logic_name"].split("<->")[1].split("[")[0]}_{link["target_port"]}',
                        "routed_interface_ip_address": link["routed_interface_address"],
                        "routed_interface_target_address": link["routed_interface_target_address"],
                        "routed_interface_network_address": str(ipaddress.IPv4Interface(link["routed_interface_address"]).network.network_address) + "/31"
                    })
                    
                if node.node_info.get("underlay_routing_protocol", "") == "BGP":
                    new_node_config["bgp"] = {
                        "asn": node.node_info["asn"], 
                        "bgp_router_id": node.node_info["router_id"],   
                        "vtep_interface": node.node_info["vtep_interface"],
                        "spine_link": spine_link,
                        "peer_link": peer_link_info
                    }
                    new_node_config["overlay"] = {
                            "bgp_router_id": node.node_info["router_id"],
                            "vtep_interface": node.node_info["vtep_interface"], 
                            "neighbor_router_id": node.node_info["neighbor_router_id_list"] 
                    }
                else:
                    new_node_config["ospf"] = {
                        "area_id": node.node_info["area_id"], 
                        "ospf_router_id": node.node_info["router_id"],   
                        "vtep_interface": node.node_info["vtep_interface"],
                        "spine_link": spine_link,
                        "peer_link": peer_link_info
                    }
                    new_node_config["overlay"] = {
                            "overlay_ibgp_asn": node.node_info["asn"], 
                            "ospf_router_id": node.node_info["router_id"],
                            "vtep_interface": node.node_info["vtep_interface"], 
                            "neighbor_router_id": node.node_info["neighbor_router_id_list"] 
                    }
                    
                new_node_config["link"] = {  
                        "reserved_vlan": node.node_info["reserved_vlan"],
                        "spine_link": spine_link
                    }
                
            config_dict[node.switch_sn] = {
                "meta": {
                    "role": node.type,
                    "fabric_topo_id": fabric_topo_id,
                    "logic_name": node.logic_name,
                },
                "old_val": node.node_config if node.node_config else {},
                "new_val": new_node_config
            }
            
            # dc_fabric_db.update_fabric_topology_node_config(node.logic_name, fabric_topo_id, new_node_config)   
            
        LOG.info(config_dict)
        session = dc_fabric_db.get_session()
        session.query(DCFabricTopology).filter(
                    DCFabricTopology.id == fabric_topo_id).update({"status": "Deploying"})
        # 调用配置下发接口
        return config_distribution_cli(config_dict)
    except Exception as e:
        LOG.error(traceback.format_exc())
        return jsonify({'status': 400, 'info': 'Deploy topo failed.'})


@dc_blueprint_mold.route("/fabric_topo/deployment_status", methods=["POST"])
@admin_permission.require(http_exception=403)
def fabric_topo_deployment_status():    
    try:
        data = request.get_json()
        if not data:
            return jsonify({'info': "Invalid request data", 'status': 400})

        fabric_topo_id = data.get('fabric_topo_id')

        session = inven_db.get_session()

        nodes = dc_fabric_db.get_fabric_topology_node(fabric_topo_id)
        node_sn_list = [node.switch_sn for node in nodes if node.switch_sn]

        subquery = session.query(
            inventory.ConfigDistributionTaskForDC,
            func.row_number().over(
                partition_by=[inventory.ConfigDistributionTaskForDC.sn,
                              inventory.ConfigDistributionTaskForDC.type,
                              inventory.ConfigDistributionTaskForDC.fabric_id],
                order_by=inventory.ConfigDistributionTaskForDC.create_time.desc()
            ).label('row_num')
        ) \
        .filter(inventory.ConfigDistributionTaskForDC.fabric_id == fabric_topo_id,
                inventory.ConfigDistributionTaskForDC.type == "underlay",
                inventory.ConfigDistributionTaskForDC.sn.in_(node_sn_list)
            ).subquery()

        task = session.query(subquery).filter(subquery.c.row_num == 1).all()

        sns = [item.sn for item in task if item.sn]
        mgt_ip_map = {}
        if sns:
            switch_rows = session.query(inventory.Switch.sn, inventory.Switch.link_ip_addr).filter(
                inventory.Switch.sn.in_(sns)
            ).all()
            mgt_ip_map = {row.sn: row.link_ip_addr for row in switch_rows}

        res = []
        for item in task:
            info = {
                "logic_device": item.logic_name,
                "task_status": dc_task_status[item.task_status],
                "task_name": item.task_name,
                "role": item.task_role,
                "id": item.id,
                "task_log": item.task_traceback_info or "",
                "mgt_ip": mgt_ip_map.get(item.sn, "")
            }
            res.append(info)
        
        return jsonify({'status': 200, 'data': res})
    except Exception as e:
        LOG.error(traceback.format_exc())
        return jsonify({'status': 400, 'info': 'Get deploy task status failed.'})  
    

@dc_blueprint_mold.route("/fabric_topo/check_link", methods=["POST"])
@admin_permission.require(http_exception=403)
def fabric_topo_check_link():    
    try:
        data = request.get_json()
        if not data:
            return jsonify({'info': "Invalid request data", 'status': 400})

        fabric_topo_id = data.get('fabric_topo_id')
        check_list = data.get("check_list", [])
        topo = dc_fabric_db.get_fabric_topo_by_id(fabric_topo_id)
        fabric_config = topo.fabric_config.copy()
        
        node_sn_list = [node["switch_sn"] for node in fabric_config["topology"]["nodes"] if node["switch_sn"]]
        lldp_info = lldp_refresh(node_sn_list)
        
        for edge in fabric_config["topology"]["edges"]:
            source_sn = edge["source_sn"]
            target_sn = edge["target_sn"]
            for link_info in edge["link_info"]:
                if link_info["logic_link"] in check_list:
                    check_status = check_ports(lldp_info, source_sn, target_sn, link_info["source_port"], link_info["target_port"])
                    if check_status:
                        link_info["link_status"] = "passed"
                    else:
                        link_info["link_status"] = "failed"
        
        #更新node节点
        update_topology_node_info(fabric_config["topology"], topo.id)  
        dc_fabric_db.update_fabric_topology(fabric_id=topo.fabric_id, template_name=topo.template_name, fabric_config=fabric_config)
        fabric_topology = dc_fabric_db.get_fabric_topo_by_id(fabric_topo_id)
                    
        return jsonify({'status': 200, 'info': 'Auto Link topo successed.', 'data': fabric_topology.make_dict()})
    except Exception as e:
        LOG.error(traceback.format_exc())
        return jsonify({'status': 400, 'info': 'Auto Link topo failed.'})
    
    
@dc_blueprint_mold.route("/fabric_topo/allocate_resource", methods=["POST"])
@admin_permission.require(http_exception=403)
def fabric_topo_allocate_resource():
    try:
        data = request.get_json()
        if not data:
            return jsonify({'info': "Invalid request data", 'status': 400}) 
    
        session = inven_db.get_session()
        fabric_topo_id = data.get('fabric_topo_id')
        routed_interface_address_pool = data.get('routed_interface_address_pool')
        
        topo = dc_fabric_db.get_fabric_topo_by_id(fabric_topo_id, session)
        fabric_config = topo.fabric_config.copy()
        
        topology = fabric_config["topology"]
        underlay_routing_protocol = fabric_config["underlay_routing_protocol"]
        old_routed_interface_address_pool = fabric_config.get("routed_interface_address_pool", None)
        print(underlay_routing_protocol, old_routed_interface_address_pool)
        
        node_num = len(topology["nodes"])
        leaf_name_list = []
        mlag_peer_pool_dict = {}
        spine_link_num = 0
        spine_num = 0
        spine_link_list = []
        need_asn = 0
        need_router_id = 0
        need_vtep_interface = 0
        need_peer_link = 0
        err_msg = ""
        
        node_info_dict = {}
        
        for node in topology["nodes"]:
            node_info = node["node_info"]
            node_type = node["type"]
            if node_type == "leaf":
                leaf_name = f"{node['group']}_{node['leaf_name']}"
                if leaf_name not in leaf_name_list:
                    leaf_name_list.append(leaf_name)
                if node["strategy"] == "MLAG":
                    if leaf_name not in mlag_peer_pool_dict:
                        mlag_peer_pool_dict[leaf_name] = node['node_info'].get('mlag_l3_interface_ip_address_pool')
                    if not node['node_info'].get('mlag_l3_interface_ip_address', ""):
                        need_peer_link +=1    
                if not node_info.get('vtep_interface', ""):
                    need_vtep_interface += 1
                    
            elif node_type == "spine":
                spine_num += 1
                
            if not node_info.get('asn', ""):
                need_asn += 1
            if not node_info.get('router_id', ""):
                need_router_id += 1       
                
            node_info_dict[node["logic_device"]] = node
                
        for edge in topology["edges"]:
            if edge["type"] == "spine_link":
                print(len(edge["link_info"]))
                spine_link_num += len(edge["link_info"])
                for link_info in edge["link_info"]:
                    if link_info.get("source_routed_address") != "":
                        spine_link_list.append(link_info["source_routed_address"])
                    if link_info.get("target_routed_address") != "":
                        spine_link_list.append(link_info["target_routed_address"])
                
        print(spine_link_num, spine_link_list)   
        
        # 新分配 spine link为空 或者 需要换池
        if spine_link_num*2 != len(spine_link_list) or routed_interface_address_pool != old_routed_interface_address_pool:
            link_ip_list = []
            try:
                link_ip_list = allocate_ip_pair(routed_interface_address_pool, spine_link_num, session)
            except Exception as e: 
                LOG.error(str(e))
            if not link_ip_list:
                    err_msg += "Allocate routed interface address failed;"
            else:
                if routed_interface_address_pool != old_routed_interface_address_pool and old_routed_interface_address_pool != None:
                    #删除旧值
                    int_ips = [int(ipaddress.IPv4Address(ip.split('/')[0])) for ip in spine_link_list]
                    print("delete ", int_ips)
                    delete_ip_values(old_routed_interface_address_pool, int_ips, session)

                for edge in topology["edges"]:
                    if edge["type"] == "spine_link":
                        for link_info in edge["link_info"]:
                            link_ip = link_ip_list.pop(0)
                            link_info["source_routed_address"] = link_ip[0]
                            link_info["target_routed_address"] = link_ip[1]
                            
        if err_msg == "":     
            fabric_config["routed_interface_address_pool"] = routed_interface_address_pool
            
        print(node_num, leaf_name_list, spine_num, need_asn, need_router_id, need_vtep_interface, need_peer_link)
        
        if need_asn != 0:
            if underlay_routing_protocol == "BGP":
                asn_num = spine_num + len(leaf_name_list)
                asn_pool_id = fabric_config.get('underlay_ebgp_asn')
                asn_list = []
                try:
                    asn_list = allocate_asn(asn_pool_id, asn_num, session)
                except Exception as e: 
                    LOG.error(str(e))
                print(asn_list)
                if asn_list:
                    asn_dict = {}
                    for index, leaf_name in enumerate(leaf_name_list):
                        asn_dict[leaf_name] = asn_list[index+1] 
                        
                    for index, (key, node) in enumerate(node_info_dict.items()):
                        if node["type"] == "leaf":
                            leaf_name = node["group"] + "_" + node["leaf_name"]
                            if leaf_name in leaf_name_list:
                                node["node_info"]["asn"] = asn_dict[leaf_name]
                        elif node["type"] == "spine":
                            node["node_info"]["asn"] = asn_list[0]
                else:
                    err_msg += "Allocate asn failed;"
            elif underlay_routing_protocol == "OSPF":
                asn_num = 1
                asn_pool_id = fabric_config.get('overlay_ibgp_asn')
                asn_list = []
                try:
                    asn_list = allocate_asn(asn_pool_id, asn_num, session)
                except Exception as e: 
                    LOG.error(str(e))
                print(asn_list)
                if asn_list:
                    for key, node in node_info_dict.items():
                        node["node_info"]["asn"] = asn_list[0]
                else:
                    err_msg += "Allocate asn failed;"
                    
        if need_router_id != 0:
            router_id_pool = fabric_config.get('bgp_router_id')
            route_id_list = []
            try:
                route_id_list = allocate_ip(router_id_pool, node_num, session)
            except Exception as e: 
                LOG.error(str(e))
            if not route_id_list:
                err_msg += "Allocate router id failed;"
            else:
                for index, (key, node) in enumerate(node_info_dict.items()):
                    node["node_info"]["router_id"] = route_id_list[index]
                
        if need_vtep_interface != 0:
            vtep_num = len(leaf_name_list)
            vtep_interface_pool = fabric_config.get('vtep_interface')
            vtep_list = []
            try:
                vtep_list = allocate_ip(vtep_interface_pool, vtep_num, session)
            except Exception as e: 
                LOG.error(str(e))
            if not vtep_list:
                err_msg += "Allocate vtep interface failed;"
            else:
                vtep_dict = {leaf_name: vtep_list[index] for index, leaf_name in enumerate(leaf_name_list)}
                for index, (key, node) in enumerate(node_info_dict.items()):
                    if node["type"] == "leaf":
                        leaf_name = f"{node['group']}_{node['leaf_name']}"
                        node["node_info"]["vtep_interface"] = vtep_dict[leaf_name]
                        
        if need_peer_link != 0:
            peer_link_dict = {}
            error_occurred = False
            for leaf_name, pool_id in mlag_peer_pool_dict.items():
                peer_link_list = []
                try:
                    peer_link_list = allocate_ip_pair(pool_id, 2, session)
                except Exception as e: 
                    LOG.error(str(e))
                if not peer_link_list:
                    err_msg += "Allocate peer link failed;"
                    error_occurred = True
                else:
                    peer_link_dict[leaf_name] = peer_link_list[0]
            if not error_occurred:
                for index, (key, node) in enumerate(node_info_dict.items()):
                    if node["type"] == "leaf" and node["strategy"] == "MLAG":
                        leaf_name = node["group"] + "_" + node["leaf_name"]
                        leaf_index = int(node["logic_device"].split(leaf_name+"_")[-1])
                        node["node_info"]["mlag_l3_interface_ip_address"], node["node_info"]["peer_ipv4_address"] = peer_link_dict[leaf_name][leaf_index - 1], peer_link_dict[leaf_name][1 - (leaf_index - 1)]
            else:
                # 分配失败时归还所有已分配的值
                for leaf_name, ip_list in peer_link_dict.items():
                    delete_ip_values(mlag_peer_pool_dict[leaf_name], ip_list, session)
            
        
        for node in topology["nodes"]:
            node["node_info"] = node_info_dict[node["logic_device"]]["node_info"]
            
        print(topology)
                 
        dc_fabric_db.update_fabric_topology(fabric_id=topo.fabric_id, template_name=topo.template_name, fabric_config=fabric_config, session=session)
            
        if err_msg != "":
            return jsonify({'status': 400, 'info': err_msg})
        else:
            return jsonify({'status': 200, 'info': 'Allocate resource successed.'})
    
    except Exception as e:
        LOG.error(traceback.format_exc())
        return jsonify({'status': 400, 'info': 'Allocate resource failed.'})
    

def check_ports(data, source_sn, target_sn, source_port, target_port):
    # 正向检查
    if source_sn in data:
        if target_sn in data[source_sn]:
            for entry in data[source_sn][target_sn]:
                if entry['source_port'] == source_port and entry['target_port'] == target_port:
                    return True
    # 反向检查     
    if target_sn in data:
        if source_sn in data[target_sn]:
            for entry in data[target_sn][source_sn]:
                if entry['target_port'] == source_port and entry['source_port'] == target_port:
                    return True
    return False
    
    
@dc_blueprint_mold.route("/fabric_topo/add_vlan_domain", methods=["POST"])
@admin_permission.require(http_exception=403)
def fabric_topo_add_vlan_domain():    
    try:
        data = request.get_json()
        if not data:
            return jsonify({'info': "Invalid request data", 'status': 400})
        
        vlan_domain_info = data.get('vlan_domain_info', [])
        group_name = data.get('vlan_domain_group_name', "")
        description = data.get('description', "")
        session = inven_db.get_session()
        
        session.begin()
        if vlan_domain_info:
            fabric_topo_id = vlan_domain_info[0].get("fabric_topo_id")
            topo = dc_fabric_db.get_fabric_topo_by_id(fabric_topo_id)
            fabric_id = topo.fabric_id
            dc_virtual_resource_db.update_vlan_domain_group(group_name=group_name, description=description, fabric_id=fabric_id, session=session)
        else:
            return jsonify({'status': 400, 'info': f'Auto vlan domain failed. vlan domain info not found'})
        
        for vlan_domain in vlan_domain_info:
            vd_name = vlan_domain.get("vlan_domain_name")
            if not vd_name or vd_name == "":
                session.rollback()
                return jsonify({'status': 500, 'info': 'Auto vlan domain failed.'})
            node_logic_names = vlan_domain.get("node_logic_names", [])
            bridge_domain_pool_range = vlan_domain.get("bridge_domain_pool_range", [])
            vrf_vlan_pool_range = vlan_domain.get("vrf_vlan_pool_range", [])
            device_type = vlan_domain.get("device_type", "server")
            
            res, vd_pool_id = resource_pool_vlandomain_db.add_vlan_domain_pool(name=vd_name, fabric_id=fabric_id, device_type=device_type,
                                                                            bd_ranges=bridge_domain_pool_range, vrf_ranges=vrf_vlan_pool_range, session=session)
            if res:
                try:                                      
                    session.query(DCFabricTopologyNode).filter(DCFabricTopologyNode.fabric_topo_id == fabric_topo_id)  \
                                                    .filter(DCFabricTopologyNode.logic_name.in_(node_logic_names)).update({"vlan_domain_pool_id": vd_pool_id})
                    topo.modified_time = func.now()
                    session.flush()
                except Exception as e:
                    session.rollback()
                    return jsonify({'status': 500, 'info': 'Auto vlan domain failed.'})
            else:
                session.rollback()
                return jsonify({'status': 500, 'info': 'Auto vlan domain failed.'})
             
        session.commit() 
        return jsonify({'status': 200, 'info': 'Auto vlan domain successed.'})
    except Exception as e:
        LOG.error(traceback.format_exc())
        session.rollback()
        return jsonify({'status': 400, 'info': f'Auto vlan domain failed. {str(e)}'})

  
@dc_blueprint_mold.route("/fabric_topo/list_vlan_domain", methods=["POST"])
@admin_permission.require(http_exception=403)
def fabric_topo_list_vlan_domain():  
    try:
        data = request.get_json()
        if not data:
            return jsonify({'info': "Invalid request data", 'status': 400})

        session = inven_db.get_session()
        fabric_topo_id = data.get('fabric_topo_id')
        topo = dc_fabric_db.get_fabric_topo_by_id(fabric_topo_id, session)
        nodes = dc_fabric_db.get_fabric_topology_node(fabric_topo_id, session)
        
        vd_pool = resource_pool_vlandomain_db.list_vlan_domain_pool(topo.fabric_id, session)
        
        res = []
        for node in nodes:
            if node.type == "leaf":  # 目前只有leaf 后续可能有access
                info = {
                    "logic_name":node.logic_name,
                    "logic_device_id": node.id,
                    "vlan_domain_id": node.vlan_domain_pool_id,
                    "bridge_domain": vd_pool[node.vlan_domain_pool_id]["bridge_domain"] if node.vlan_domain_pool_id in vd_pool else {},
                    "vrf_vlan": vd_pool[node.vlan_domain_pool_id]["var_vlan"] if node.vlan_domain_pool_id in vd_pool else {},
                    "vlan_domain_name": vd_pool[node.vlan_domain_pool_id]["vlan_domain_name"] if node.vlan_domain_pool_id in vd_pool else ""
                }
                res.append(info)
   
        return jsonify({'status': 200, 'info': 'List vlan domain successed.', 'data': res})
    except Exception as e:
        LOG.error(traceback.format_exc())
        return jsonify({'status': 400, 'info': 'List vlan domain failed.'})
    

@dc_blueprint_mold.route("/fabric_topo/edit_vlan_domain", methods=["POST"])
@admin_permission.require(http_exception=403)
def fabric_topo_edit_vlan_domain():
    session = dc_fabric_db.get_session()
    try:
        data = request.get_json()
        vlan_domain_info = data.get('vlan_domain_info', [])
        group_name = data.get('vlan_domain_group_name', "")
        vd_group_id = data.get('vlan_domain_group_id', None)
        description = data.get('description', "")
        
        session.begin()
        for vlan_domain in vlan_domain_info:
            name = vlan_domain.get('pool_name')
            bridge_domain_ranges = vlan_domain.get('bridge_domain_ranges')
            vrf_vlan_ranges = vlan_domain.get('vrf_vlan_ranges')
            vlan_domain_id = vlan_domain.get('vlan_domain_id')
            
            bd_old_range = bridge_domain_ranges.get("old_range", [])
            bd_new_range = bridge_domain_ranges.get("new_range", [])
            bd_range = process_ranges(bd_old_range, bd_new_range)
        
            bd_add_ranges = bd_range.get('add', [])
            bd_modify_ranges = bd_range.get('modify', [])
            if is_ranges_conflict(bd_add_ranges + bd_modify_ranges):
                return jsonify({'status': 500, 'info': 'Bridge Domain Ranges conflict'})
        
            vrf_old_range = vrf_vlan_ranges.get("old_range", [])
            vrf_new_range = vrf_vlan_ranges.get("new_range", [])
            vrf_range = process_ranges(vrf_old_range, vrf_new_range)
        
            vrf_add_ranges = vrf_range.get('add', [])
            vrf_modify_ranges = vrf_range.get('modify', [])
            if is_ranges_conflict(vrf_add_ranges + vrf_modify_ranges):
                return jsonify({'status': 500, 'info': 'VRF Vlan Ranges conflict'})
            
            resource_pool_vlandomain_db.edit_vlan_domain_pool(vlan_domain_id, name, bd_range, vrf_range, session)
            
            # 更新fabric topo修改时间
            vlan_domain_pool = session.query(ResourcePoolVlanDomain).filter(ResourcePoolVlanDomain.id == vlan_domain_id).first()
            session.query(DCFabricTopology).filter(DCFabricTopology.fabric_id == vlan_domain_pool.fabric_id).update({"modified_time": func.now()})
            dc_virtual_resource_db.update_vlan_domain_group(group_name=group_name, description=description, fabric_id=vlan_domain_pool.fabric_id, vd_group_id=vd_group_id)
            session.query(VlanDomainGroup).filter(VlanDomainGroup.fabric_id == vlan_domain_pool.fabric_id).update({"modified_time": func.now()})
           
        session.commit() 
        return jsonify({'status': 200, 'info': 'Edit resource pool successfully'})
    except Exception as e:
        LOG.error(traceback.format_exc())
        session.rollback()
        return jsonify({'status': 500, 'info': 'Failed to edit resource pool'})


def process_ranges(existing_ranges, new_ranges):
    result = {
        "delete": [],
        "modify": [],
        "add": []
    }
    
    # 将现有范围转换为更易于操作的格式
    existing_dict = {r['id']: r for r in existing_ranges}

    checked_ids = []  # 记录已经修改范围的id 和不需要修改范围的id
    for new_range in new_ranges:
        new_start = new_range['start']
        new_end = new_range['end']
        need_add = True
        
        for existing_id, existing_range in existing_dict.items():
            existing_start = existing_range['start_value']
            existing_end = existing_range['end_value']
            
            # 检查是否需要更新
            if new_start <= existing_start and new_end >= existing_end:
                if new_start == existing_start and new_end == existing_end:
                    checked_ids.append(existing_id)
                    need_add = False
                else:
                    result['modify'].append({
                        "rangeId": existing_id,
                        "start": new_start,  # 保留原始起始值
                        "end": new_end  # 修改为新的结束值
                    })
                    need_add = False
                    checked_ids.append(existing_id)
                    continue

        # 检查是否添加新的范围
        if need_add:
            result['add'].append({
                "start": new_start,
                "end": new_end
            })
    
    # 剩余id需要删除       
    for id in existing_dict:
        if id not in checked_ids:
            result['delete'].append({"rangeId": id})

    return result


@dc_blueprint_mold.route("/fabric_topo/list_vd_dropdown_data", methods=["POST"])
@admin_permission.require(http_exception=403)
def list_vd_dropdown_data():  
    try:
        data = request.get_json()
        if not data:
            return jsonify({'info': "Invalid request data", 'status': 400})

        session = inven_db.get_session()
        fabric_id = data.get('fabric_id')
        topo = dc_fabric_db.get_fabric_topo_by_fabric_id(fabric_id, session)
        nodes = dc_fabric_db.get_fabric_topology_node(topo.id, session)
        
        vd_pool = resource_pool_vlandomain_db.list_vlan_domain_pool(topo.fabric_id, session)
        
        vd_nodes = {}
        for node in nodes:
            if node.vlan_domain_pool_id:
                vd_id = node.vlan_domain_pool_id
                if vd_id not in vd_nodes:
                    vd_nodes[vd_id] = []
                vd_nodes[vd_id].append(node)
                
        res = []
        for vd_id, vd_info in vd_pool.items():
            info = {
                "id": vd_id,
                "name": vd_info["vlan_domain_name"],
                "switch_info": []
            }
            for node in vd_nodes[vd_id]:
                switch_info = {
                    "logic_device": node.logic_name,
                    "logic_device_id": node.id,
                    "hostname": node.node_info.get("hostname", ""),
                    "type": node.type,
                    "sn": node.switch_sn
                } 
                switch = session.query(inventory.Switch).filter(inventory.Switch.sn == node.switch_sn).first()
                switch_info["mgmt_ip"] = switch.link_ip_addr if switch else "" 
                info["switch_info"].append(switch_info)
                
            res.append(info)
        
        return jsonify({'status': 200, 'info': 'List vlan domain successed.', 'data': res})
    except Exception as e:
        LOG.error(traceback.format_exc())
        return jsonify({'status': 400, 'info': 'List vlan domain failed.'})


@dc_blueprint_mold.route("/fabric_topo/list_port_dropdown_data", methods=["POST"])
@admin_permission.require(http_exception=403)
def list_port_dropdown_data():  
    try:
        data = request.get_json()
        if not data:
            return jsonify({'info': "Invalid request data", 'status': 400})

        logic_device_ids = data.get('logic_device_ids', [])
        
        res = {}
        session = inven_db.get_session()
        for logic_device_id in logic_device_ids:
            node = dc_fabric_db.get_fabric_topology_node_info(logic_device_id, session)
            
            interface_list = get_target_interfaces(node.switch_sn)
            
            ports = []
            for _, links in node.node_info["links"].items():
                for link in links:
                    # 检查 source_sn 和 target_sn 是否与输入相同
                    if link['source_sn'] == node.switch_sn:
                        ports.append(link['source_port'])
                    if link['target_sn'] == node.switch_sn:
                        ports.append(link['target_port'])
                        
            linkport = session.query(DCVirtualResourceHostLinkPort).filter(DCVirtualResourceHostLinkPort.logic_device_id == logic_device_id).all()
            for port_info in linkport:
                ports.extend([port.strip() for port in port_info.port_name.split(',')])
                        
            print(interface_list, ports)
            result = [item for item in interface_list if item not in ports]
            res[logic_device_id] = result
        
        return jsonify({'status': 200, 'info': 'List port successed.', 'data': res})
    except Exception as e:
        LOG.error(traceback.format_exc())
        return jsonify({'status': 400, 'info': 'List port failed.'})
    
    
## overlay
@dc_blueprint_mold.route("/logical_network/list", methods=["POST"])
@admin_permission.require(http_exception=403)
def logical_network_list():
    try:
        res=[]
        page_num, page_size, total_count, query_obj = utils.query_helper(DCLogicalNetwork)
        
        for info in query_obj:
            data = info.make_dict()
            data["virtual_network"] = len(info.dc_virtual_network) 
            data["logical_device"] = len(info.dc_logical_router) + len(info.dc_logical_switch)
            res.append(data)

        return jsonify({"data": res, "page": page_num,
                        "pageSize": page_size,
                        "total": total_count,
                        "status": 200})
    except Exception as e:
        return jsonify({'status': 400, 'info': 'Failed to list logical network'})


@dc_blueprint_mold.route("/logical_network/save", methods=["POST"])
@admin_permission.require(http_exception=403)
def logical_network_save():
    try:
        data = request.get_json()
        if not data:
            return jsonify({'info': "Invalid request data", 'status': 400})

        id = data.get('id', None)
        name = data.get('name')
        description = data.get('description', None)
        vpc_lock = data.get('vpcLock', None)

        if not utils.is_name_valid(name):
            return jsonify({'status': 400, 'info': 'Logical network name is invalid.'})
        
        dc_fabric_db.update_logical_network(ln_id=id, name=name, description=description, vpc_lock=vpc_lock)
        return jsonify({'status': 200, 'info': 'Save logical network successed.'})
    except Exception as e:
        LOG.error(traceback.format_exc())
        return jsonify({'status': 400, 'info': f'Save logical network failed. {str(e)}'})

@dc_blueprint_mold.route("/logical_network/delete/<int:ln_id>", methods=["POST"])
@admin_permission.require(http_exception=403)
def delete_logical_network(ln_id):
    try:
        dc_fabric_db.delete_logical_network_by_id(ln_id)
        return jsonify({'status': 200, 'info': 'Delete logical network successed.'})
    except Exception as e:
        LOG.error(traceback.format_exc())
        return jsonify({'status': 400, 'info': f'Delete logical network failed. {str(e)}'})

@dc_blueprint_mold.route("/logical_network/detail", methods=["POST"])
@admin_permission.require(http_exception=403)
def logical_network_detail():
    try:
        data = request.get_json()
        if not data:
            return jsonify({'info': "Invalid request data", 'status': 400})

        id = data.get('id')
        ## 构建逻辑网元拓扑结构
        nodes = []
        edges = []
        
        def create_node(entity, entity_type):
            topology_id = f"{entity_type}_{entity.id}"
            node = entity.make_dict()
            node.update({"type": entity_type, "topology_id": topology_id})
            return topology_id, node
        
        def collect_error_messages(configs):
            err_msgs = []
            for config in configs:
                if config.status == DeployStatus.FAILED:
                    err_msgs.append(f"{config.logic_device.switch_sn}: {config.err_msg}")
            return err_msgs
        
        logical_network = dc_fabric_db.get_logical_network_by_id(id)
        for lr in logical_network.dc_logical_router:
            src_id, node = create_node(lr, "logical_router")
            node["port_count"] = len(lr.dc_logical_interface) if lr.dc_logical_interface else 0
            nodes.append(node)
            for li in lr.dc_logical_interface or []:
                err_msgs = []
                if lr.status in {OverlayStatus.CREATE_ERROR, OverlayStatus.DELETE_ERROR}:
                    configs = dc_fabric_db.get_logical_router_config_by_router_id(lr.id)
                    err_msgs = collect_error_messages(configs)
                elif li.status in {OverlayStatus.CREATE_ERROR, OverlayStatus.DELETE_ERROR}:
                    configs = dc_fabric_db.get_logical_interface_config_by_interface(li.id)
                    err_msgs = collect_error_messages(configs)
                
                edges.append({
                    **li.make_dict(),
                    "type": "logical_interface",
                    "source": src_id,
                    "target": f"logical_switch_{li.logical_switch.id}",
                    "err_msgs": err_msgs
                })
            
        for ls in logical_network.dc_logical_switch:
            src_id, node = create_node(ls, "logical_switch")
            node["arp_nd_suppress"] = False if ls.arp_nd_suppress else True
            nodes.append(node)
            for lp in ls.dc_logical_port or []:
                device_dict = {}
                az = dc_virtual_resource_db.get_virtual_resource_pool_az_by_id(lp.virtual_network.az_id)
                devices = fabric_topology_util.get_virtual_network_connect_info(lp.virtual_network)
                for device_info in devices.values():
                    if device_info["status"] != "Disconnected" and device_info["host_ip"] not in device_dict:
                        device_info["type"] = "BareMetal" if device_info.get("nic_pg_id", 0) else "Cloud"
                        device_info["link_id"] = device_info.get("nic_pg_id") if device_info.get("nic_pg_id", 0) else device_info.get("cloud_link_id")
                        device_dict[device_info["host_ip"]] = device_info
                err_msgs = []
                if lp.status in {OverlayStatus.CREATE_ERROR, OverlayStatus.DELETE_ERROR}:
                    configs = dc_fabric_db.get_logical_port_config_by_port(lp.id)
                    err_msgs = collect_error_messages(configs) 
                
                edges.append({
                    **lp.make_dict(),
                    "type": "logical_port",
                    "az_name": az.az_name,
                    "connect_info": list(device_dict.values()),
                    "source": src_id,
                    "target": f"virtual_network_{lp.virtual_network.id}",
                    "allocated_network": lp.virtual_network.name,
                    "err_msgs": err_msgs
                })
            
        for vn in logical_network.dc_virtual_network:
            _, node = create_node(vn, "virtual_network")
            nodes.append(node)
        
        res = {
            "nodes": nodes,
            "edges": edges
        }

        return jsonify({'status': 200, 'info': 'Get logical network detail successed.', 'data': res})
    except Exception as e:
        LOG.error(traceback.format_exc())
        return jsonify({'status': 400, 'info': f'Get logical network detail failed. {str(e)}'})
    

@dc_blueprint_mold.route("/logical_network/update_position", methods=["POST"])
@admin_permission.require(http_exception=403)
def logical_network_update_position():
    try:
        data = request.get_json()
        if not data:
            return jsonify({'info': "Invalid request data", 'status': 400})

        id = data.get('id')
        type = data.get('type')
        position_x = data.get('position_x')
        position_y = data.get('position_y')

        if type == "logical_router":
            dc_fabric_db.update_logical_router(position_x=position_x,position_y=position_y, lr_id=id)
        elif type == "logical_switch":
            dc_fabric_db.update_logical_switch(position_x=position_x,position_y=position_y, ls_id=id)
        elif type == "virtual_network":
            dc_fabric_db.update_virtual_network(position_x=position_x,position_y=position_y, vn_id=id)
        else:
            return jsonify({'info': "Invalid logical network type", 'status': 400})

        return jsonify({'status': 200, 'info': 'Logical network update successed.'})
    except Exception as e:
        LOG.error(traceback.format_exc())
        return jsonify({'status': 400, 'info': f'Logical network update failed. {str(e)}'})   


@dc_blueprint_mold.route("/logical_network/history_configuration_list", methods=["POST"])
@admin_permission.require(http_exception=403)
def logical_network_history_configuration_list():
    try:
        data = request.get_json()
        if not data:
            return jsonify({'info': "Invalid request data", 'status': 400})

        if data.get('sortFields', None) and data.get('sortFields')[0].get('field', None) == "index":
            data['sortFields'][0]['field'] = "id"

        ln_id = data.get('ln_id')
        start_time = data.get('startTime', None)
        end_time = data.get('endTime', None)
        if not ln_id:
            return jsonify({'status': 400, 'info': 'Logical network ID is required.'})
        
        session = dc_fabric_db.get_session()
        pre_query = session.query(DCLogicalNetworkConfigDetail).filter(DCLogicalNetworkConfigDetail.logical_network_id == ln_id)
        if start_time and end_time:
            pre_query = pre_query.filter(DCLogicalNetworkConfigDetail.create_time.between(start_time, end_time))
        page_num, page_size, total_count, query_obj = utils.query_helper(DCLogicalNetworkConfigDetail, pre_query=pre_query, data=data)
        res = []
        for (index,info) in enumerate(query_obj):
            data = info.make_dict()
            data["index"] = index + 1
            res.append(data)
        return jsonify({"data": res, "page": page_num,
                        "pageSize": page_size,
                        "total": total_count,
                        "status": 200})
    except Exception as e:
        LOG.error(traceback.format_exc())
        return jsonify({'status': 400, 'info': f'Get logical network history configuration list failed. {str(e)}'})

@dc_blueprint_mold.route("/logical_network/delete_configuration/<int:config_id>", methods=["POST"])
@admin_permission.require(http_exception=403)
def delete_logical_network_configuration(config_id):
    try:
        dc_fabric_db.delete_logical_network_config_detail_by_id(config_id)
        return jsonify({'status': 200, 'info': 'Delete logical network configuration successed.'})
    except Exception as e:
        LOG.error(traceback.format_exc())
        return jsonify({'status': 400, 'info': f'Delete logical network configuration failed. {str(e)}'})

@dc_blueprint_mold.route("/logical_network/save_configuration", methods=["POST"])
@admin_permission.require(http_exception=403)
def save_logical_network_configuration():
    try:
        data = request.get_json()
        if not data:
            return jsonify({'info': "Invalid request data", 'status': 400})
        dc_fabric_db.update_logical_network_config_detail(**data)
        return jsonify({'status': 200, 'info': 'Save logical network configuration successed.'})
    except Exception as e:
        LOG.error(traceback.format_exc())
        return jsonify({'status': 400, 'info': f'Save logical network configuration failed. {str(e)}'})

@dc_blueprint_mold.route("/logical_network/current_configuration", methods=["POST"])
@admin_permission.require(http_exception=403)
def get_logical_network_current_configuration():
    try:
        data = request.get_json()
        if not data:
            return jsonify({'info': "Invalid request data", 'status': 400})
        
        ln_id = data.get("id")
        res = fabric_topology_util.get_current_configuration(ln_id)
        return jsonify({'status': 200, 'info': 'Get logical network current configuration successed.', 'data': res})
    except Exception as e:
        LOG.error(traceback.format_exc())
        return jsonify({'status': 400, 'info': f'Get logical network current configuration failed. {str(e)}'})


@dc_blueprint_mold.route("/logical_router/list", methods=["POST"])
@admin_permission.require(http_exception=403)
def logical_router_list():
    try:
        db_session = dc_fabric_db.get_session()

        data = request.get_json()
        logical_network_id = data.get("network_id", None)

        routers = db_session.query(DCLogicalRouter)
        if logical_network_id is not None:
            routers = routers.filter(DCLogicalRouter.logical_network_id == logical_network_id)

        res = []
        page_num, page_size, total_count, query_obj = utils.query_helper(DCLogicalRouter, pre_query=routers)
        
        for info in query_obj:
            data = info.make_dict()
            data["description"] = info.description if info.description else "--"
            data["fabric_name"] = info.fabric_name
            data["logical_network_name"] = info.logical_network_name
            data["ports"] = len(info.dc_logical_interface)
            res.append(data)

        return jsonify({"data": res, "page": page_num,
                        "pageSize": page_size,
                        "total": total_count,
                        "status": 200})
    except Exception as e:
        LOG.error(traceback.format_exc())
        return jsonify({'status': 400, 'info': 'Failed to list logical router'})
    

@dc_blueprint_mold.route("/logical_router/save", methods=["POST"])
@admin_permission.require(http_exception=403)
def logical_router_save():
    try:
        data = request.get_json()
        if not data:
            return jsonify({'info': "Invalid request data", 'status': 400})

        id = data.get('id', None)
        name = data.get('name', None)
        vrf_mode = data.get('vrfMode', None)
        vrf_name = data.get('vrfName', None)
        description = data.get('description', None)
        fabric_id = data.get('fabricId', None)
        ln_id = data.get('logicalNetworkId', None)
        type = data.get('type', "Auto")
        l3_mac_range = data.get("l3_mac_range", "")
        position_x = data.get('positionX', None)
        position_y = data.get('positionY', None)
        
        if not utils.is_name_valid(name):
            return jsonify({'status': 400, 'info': 'Logical router name is invalid.'})
    
        if id is not None:
            try:
                dc_fabric_db.update_logical_router(lr_id=id, name=name, description=description)
                return jsonify({'status': 200, 'info': 'Update logical router successed.'})
            except Exception as e:
                LOG.error(traceback.format_exc())
                return jsonify({'status': 400, 'info': f'Update logical router failed. {str(e)}'})
        
        _, vni_res = resource_pool_vni_db.generate_resource_by_fabric(fabric_id, count=1)
        
        lr = dc_fabric_db.update_logical_router(ln_id=ln_id, name=name, description=description, fabric_id=fabric_id, vrf_mode=vrf_mode, l3_anycast_mac_range=l3_mac_range,
                                                vrf_name=vrf_name, lr_id=id, l3vni=vni_res[0], position_x=position_x, position_y=position_y, type=type)
        
        if vrf_mode == "auto":
            vrf_name = f"VRF{fabric_id}-{lr.id}"
            dc_fabric_db.update_logical_router(lr_id=lr.id, name=name, vrf_name=vrf_name, vrf_mode=vrf_mode)
        
        return jsonify({'status': 200, 'info': 'Save logical router successed.'})
    except Exception as e:
        LOG.error(traceback.format_exc())
        return jsonify({'status': 400, 'info': f'Save logical router failed. {str(e)}'})
    

@dc_blueprint_mold.route("/logical_router/delete", methods=["POST"])
@admin_permission.require(http_exception=403)
def delete_logical_router():
    try:
        data = request.get_json()
        if not data:
            return jsonify({'info': "Invalid request data", 'status': 400})

        lr_id = data.get('id', None)
        
        session = dc_fabric_db.get_session()
        li = session.query(DCLogicalInterface).filter(DCLogicalInterface.logical_router_id == lr_id).first()
        if li:
            return jsonify({'status': 200, 'info': 'Logical router is in using.'})
        lr = dc_fabric_db.get_logical_router_by_id(lr_id=lr_id)
        resource_pool_vlandomain_db.delete_vrfvlan_from_pool_by_logical_router(lr.vrf_vlan, lr.id)
        resource_pool_vni_db.delete_resource_by_fabric(lr.fabric_id, lr.l3vni)
        dc_fabric_db.delete_logical_router_by_id(lr_id=lr_id)
        return jsonify({'status': 200, 'info': 'Delete logical router successfully.'})
    except Exception as e:
        LOG.error(traceback.format_exc())
        return jsonify({'status': 400, 'info': f'Delete logical router failed. {str(e)}'})

@dc_blueprint_mold.route("/logical_router/detail", methods=["POST"])
# @admin_permission.require(http_exception=403)
def logical_router_detail():
    try:
        data = request.get_json()
        lr_id = data.get('id')
        lr = dc_fabric_db.get_logical_router_by_id(lr_id)
        if not lr:
           return jsonify({'info': "Logical router not found", 'status': 404})
        return jsonify({'status': 200, 'data': lr.make_dict()})
    except Exception as e:
        LOG.error(traceback.format_exc())
        return jsonify({'status': 400, 'info': f'Get logical router detail failed. {str(e)}'})

@dc_blueprint_mold.route("/logical_router/port_detail", methods=["POST"])
# @admin_permission.require(http_exception=403)
def logical_router_port_detail():
    try:
        data = request.get_json()
        lr_id = data.get('id')
        session = dc_fabric_db.get_session()
        pre_query = session.query(DCLogicalInterface).filter(DCLogicalInterface.logical_router_id == lr_id)
        page_num, page_size, total_count, query_obj = utils.query_helper(DCLogicalInterface, pre_query=pre_query, data=data)
        res = []
        for (index, li) in enumerate(query_obj):
            data = li.make_dict()
            data["port"] = "Port " + str(li.id)
            data["index"] = index + 1
            res.append(data)
        return jsonify({"data": res, "page": page_num,
                        "pageSize": page_size,
                        "total": total_count,
                        "status": 200})
    except Exception as e:
        LOG.error(traceback.format_exc())
        return jsonify({'status': 400, 'info': f'Get logical router port detail failed. {str(e)}'})

@dc_blueprint_mold.route("/logical_switch/list", methods=["POST"])
@admin_permission.require(http_exception=403)
def logical_switch_list():
    try:
        db_session = dc_fabric_db.get_session()

        data = request.get_json()
        logical_network_id = data.get("network_id", None)

        switches = db_session.query(DCLogicalSwitch)
        if logical_network_id is not None:
            switches = switches.filter(DCLogicalSwitch.logical_network_id == logical_network_id)

        res = []
        page_num, page_size, total_count, query_obj = utils.query_helper(DCLogicalSwitch, pre_query=switches)
        
        for info in query_obj:
            ports = 0
            if info.dc_logical_port:
                connect_info = fabric_topology_util.get_virtual_network_connect_info(info.dc_logical_port[0].virtual_network)
                ports = len(connect_info.values())         
            data = info.make_dict()
            data["description"] = info.description if info.description else "--"
            data["fabric_name"] = info.fabric_name
            data["logical_network_name"] = info.logical_network_name
            data["ports"] = ports
            data["arp_nd_suppress"] = False if info.arp_nd_suppress else True
            res.append(data)

        return jsonify({"data": res, "page": page_num,
                        "pageSize": page_size,
                        "total": total_count,
                        "status": 200})
    except Exception as e:
        LOG.error(traceback.format_exc())
        return jsonify({'status': 400, 'info': 'Failed to list logical switch'})
    

@dc_blueprint_mold.route("/logical_switch/save", methods=["POST"])
@admin_permission.require(http_exception=403)
def logical_switch_save():
    try:
        data = request.get_json()
        if not data:
            return jsonify({'info': "Invalid request data", 'status': 400})

        id = data.get('id', None)
        name = data.get('name', None)
        description = data.get('description', None)
        arp_nd_suppress = data.get('arpNdSuppress', True)
        fabric_id = data.get('fabricId', None)
        ln_id = data.get('logicalNetworkId', None)
        type = data.get('type', "Auto")
        position_x = data.get('positionX', None)
        position_y = data.get('positionY', None)

        if not utils.is_name_valid(name):
            return jsonify({'status': 400, 'info': 'Logical switch name is invalid.'})

        if id is not None:
            try:
                dc_fabric_db.update_logical_switch(name=name, description=description, ls_id=id)
                return jsonify({'status': 200, 'info': 'Update logical switch successed.'})
            except Exception as e:
                LOG.error(traceback.format_exc())
                return jsonify({'status': 400, 'info': f'Update logical switch failed. {str(e)}'})
        
        if arp_nd_suppress:
            arp_nd_suppress= False
        else:
            arp_nd_suppress=True
        dc_fabric_db.update_logical_switch(name=name, ln_id=ln_id, fabric_id=fabric_id, description=description, arp_nd_suppress=arp_nd_suppress, 
                                           ls_id=id, position_x=position_x, position_y=position_y, type=type)
        
        return jsonify({'status': 200, 'info': 'Save logical switch successed.'})
    except Exception as e:
        LOG.error(traceback.format_exc())
        return jsonify({'status': 400, 'info': f'Save logical switch failed. {str(e)}'})
 

@dc_blueprint_mold.route("/logical_switch/delete", methods=["POST"])
@admin_permission.require(http_exception=403)
def delete_logical_switch():
    try:
        data = request.get_json()
        if not data:
            return jsonify({'info': "Invalid request data", 'status': 400})

        ls_id = data.get('id', None)
        session = dc_fabric_db.get_session()
        ls = session.query(DCLogicalInterface).filter(DCLogicalInterface.logical_switch_id == ls_id).first()
        if ls:
            return jsonify({'status': 200, 'info': 'Logical switch is in using.'})
        
        ls = session.query(DCLogicalPort).filter(DCLogicalPort.logical_switch_id == ls_id).first()
        if ls:
            return jsonify({'status': 200, 'info': 'Logical switch is in using.'})
        
        dc_fabric_db.delete_logical_switch_by_id(ls_id=ls_id)
        return jsonify({'status': 200, 'info': 'Delete logical switch successfully.'})
    except Exception as e:
        LOG.error(traceback.format_exc())
        return jsonify({'status': 400, 'info': f'Delete logical switch failed. {str(e)}'}) 
  
@dc_blueprint_mold.route("/logical_switch/port_detail", methods=["POST"])
def logical_switch_port_detail():
    try:
        data = request.get_json()
        ls_id = data.get('id')
        session = dc_fabric_db.get_session()
        lp = session.query(DCLogicalPort).filter(DCLogicalPort.logical_switch_id == ls_id).first()
        res = []
        if lp:
            connect_info = fabric_topology_util.get_virtual_network_connect_info(lp.virtual_network)
            for connect in connect_info.values():
                if connect.get("status", "Disconnected") == "Disconnected":
                    continue
                connect["type"] = "BareMetal" if connect.get("nic_pg_id", 0) else "Cloud"
                connect["link_id"] = connect.get("nic_pg_id") if connect.get("nic_pg_id", 0) else connect.get("cloud_link_id")
                connect["port_id"] = lp.id
                res.append(connect)
        return jsonify({"data": res, "status": 200})
    except Exception as e:
        LOG.error(traceback.format_exc())
        return jsonify({'status': 400, 'info': f'Get logical switch port detail failed. {str(e)}'})  


@dc_blueprint_mold.route("/logical_switch/pod_detail", methods=["POST"])
def logical_switch_pod_detail():
    try:
        data = request.get_json()
        ls_id = data.get('id')
        session = dc_fabric_db.get_session()
        lp = session.query(DCLogicalPort).filter(DCLogicalPort.logical_switch_id == ls_id).first()
        res = {}
        if lp:
            connect_info = fabric_topology_util.get_virtual_network_connect_info(lp.virtual_network)
            for connect in connect_info.values():
                print(connect)
                if connect.get("status", "Disconnected") == "Disconnected":
                    continue
                if connect.get("nic_pg_id", None) is not None:
                    nic_pg = dc_virtual_resource_db.get_node_nic_portgroup_by_id(connect["nic_pg_id"])
                    for pg_info in nic_pg.switch_portgroup.switch_portgroup_info:
                        if pg_info.switch_sn not in res:
                            vlan_domain_pool = session.query(ResourcePoolVlanDomain).filter(ResourcePoolVlanDomain.id == nic_pg.switch_portgroup.vlan_domain_id).first()
                            switch = session.query(inventory.Switch).filter(inventory.Switch.sn == pg_info.switch_sn).first()
                            info = {
                                "az": nic_pg.node_host.node_group.az_name,
                                "vlan_domain": vlan_domain_pool.name if vlan_domain_pool else "--",
                                "switch_sn": pg_info.switch_sn,
                                "mgmt_ip": switch.link_ip_addr,
                                "vtep_id": pg_info.logic_device.node_info.get("vtep_interface", "--")
                            }
                            res[pg_info.switch_sn] = info
                else:
                    cloud_link = session.query(DCVirtualHostNetworkMapping).filter(DCVirtualHostNetworkMapping.id == connect["cloud_link_id"]).first()
                    host_link = dc_virtual_resource_db.get_virtual_resource_host_link_by_id(cloud_link.hostlink_id) 
                    for link_info in host_link.link_ports:
                        if link_info.switch_sn not in res:
                            vlan_domain_pool = session.query(ResourcePoolVlanDomain).filter(ResourcePoolVlanDomain.id == host_link.vlan_domain_id).first()
                            switch = session.query(inventory.Switch).filter(inventory.Switch.sn == link_info.switch_sn).first()
                            info = {
                                "az": host_link.host.az.az_name,
                                "vlan_domain": vlan_domain_pool.name if vlan_domain_pool else "--",
                                "switch_sn": link_info.switch_sn,
                                "mgmt_ip": switch.link_ip_addr,
                                "vtep_id": link_info.logic_device.node_info.get("vtep_interface", "--")
                            }
                            res[link_info.switch_sn] = info

            
        return jsonify({"data": list(res.values()), "status": 200})
    except Exception as e:
        LOG.error(traceback.format_exc())
        return jsonify({'status': 400, 'info': f'Get logical switch pod detail failed. {str(e)}'}) 
    

@dc_blueprint_mold.route("/logical_switch/status_monitor", methods=["POST"])
def logical_switch_status_monitor():
    try:
        data = request.get_json()
        ls_id = data.get('id')
        session = dc_fabric_db.get_session()
        lp = session.query(DCLogicalPort).filter(DCLogicalPort.logical_switch_id == ls_id).first()
        li = session.query(DCLogicalInterface).filter(DCLogicalInterface.logical_switch_id == ls_id).first()
        res = {}
        if lp:
            connect_info = fabric_topology_util.get_virtual_network_connect_info(lp.virtual_network)
            for connect in connect_info.values():
                print(connect)
                if connect.get("status", "Disconnected") == "Disconnected":
                    continue
                if connect.get("nic_pg_id", None) is not None:
                    nic_pg = dc_virtual_resource_db.get_node_nic_portgroup_by_id(connect["nic_pg_id"])
                    for pg_info in nic_pg.switch_portgroup.switch_portgroup_info:
                        if pg_info.switch_sn not in res:
                            vlan_domain_pool = session.query(ResourcePoolVlanDomain).filter(ResourcePoolVlanDomain.id == nic_pg.switch_portgroup.vlan_domain_id).first()
                            info = {
                                "vlan_domain": vlan_domain_pool.name if vlan_domain_pool else "--",
                                "switch_sn": pg_info.switch_sn,
                                "l2_vni": lp.l2vni,
                                "l2_vlan": lp.vlan,
                                "rd": "Auto",
                                "rt": "Auto",
                                "connected_lr": li.logical_router.name if li.logical_router else "--"

                            }
                            res[pg_info.switch_sn] = info
                else:
                    cloud_link = session.query(DCVirtualHostNetworkMapping).filter(DCVirtualHostNetworkMapping.id == connect["cloud_link_id"]).first()
                    host_link = dc_virtual_resource_db.get_virtual_resource_host_link_by_id(cloud_link.hostlink_id) 
                    for link_info in host_link.link_ports:
                        if link_info.switch_sn not in res:
                            vlan_domain_pool = session.query(ResourcePoolVlanDomain).filter(ResourcePoolVlanDomain.id == host_link.vlan_domain_id).first()
                            info = {
                                "vlan_domain": vlan_domain_pool.name if vlan_domain_pool else "--",
                                "switch_sn": link_info.switch_sn,
                                "l2_vni": lp.l2vni,
                                "l2_vlan": lp.vlan,
                                "rd": "Auto",
                                "rt": "Auto",
                                "connected_lr": li.logical_router.name if li and li.logical_router else "--"
                            }
                            res[link_info.switch_sn] = info

            
        return jsonify({"data": list(res.values()), "status": 200})
    except Exception as e:
        LOG.error(traceback.format_exc())
        return jsonify({'status': 400, 'info': f'Get logical switch status monitor failed. {str(e)}'})  
    

@dc_blueprint_mold.route("/logical_router/status_monitor", methods=["POST"])
def logical_router_status_monitor():
    try:
        data = request.get_json()
        lr_id = data.get('id')
        session = dc_fabric_db.get_session()
        li_list = session.query(DCLogicalInterface).filter(DCLogicalInterface.logical_router_id == lr_id).all()
        res = {}
        
        for li in li_list:    
            lp = session.query(DCLogicalPort).filter(DCLogicalPort.logical_switch_id == li.logical_switch_id).first()
            if lp:
                gw_interface = f"{li.anycast_ipv4}({lp.vlan})"
                connect_info = fabric_topology_util.get_virtual_network_connect_info(lp.virtual_network)
                for connect in connect_info.values():
                    print(connect)
                    if connect.get("status", "Disconnected") == "Disconnected":
                        continue
                    if connect.get("nic_pg_id", None) is not None:
                        nic_pg = dc_virtual_resource_db.get_node_nic_portgroup_by_id(connect["nic_pg_id"])
                        for pg_info in nic_pg.switch_portgroup.switch_portgroup_info:
                            li_config = session.query(DCLogicalInterfaceConfig).filter(DCLogicalInterfaceConfig.logic_device_id == pg_info.logic_device_id, 
                                                                                       DCLogicalInterfaceConfig.logical_interface_id == li.id).first()
                            if pg_info.switch_sn not in res:
                                vlan_domain_pool = session.query(ResourcePoolVlanDomain).filter(ResourcePoolVlanDomain.id == nic_pg.switch_portgroup.vlan_domain_id).first()
                                info = {
                                    "az": nic_pg.node_host.node_group.az_name,
                                    "vlan_domain": vlan_domain_pool.name if vlan_domain_pool else "--",
                                    "switch_sn": pg_info.switch_sn,
                                    "vrf_vlan": li.logical_router.vrf_vlan,
                                    "rd": "Auto",
                                    "rt": "Auto",
                                    "gw_interface": [gw_interface],
                                    "virtual_ip": [li_config.virtual_ip] if li_config else []
                                }
                                res[pg_info.switch_sn] = info
                            else:
                                if gw_interface not in res[pg_info.switch_sn]["gw_interface"]:
                                    res[pg_info.switch_sn]["gw_interface"].append(gw_interface)
                                if li_config and li_config.virtual_ip not in res[pg_info.switch_sn]["virtual_ip"]:
                                    res[pg_info.switch_sn]["virtual_ip"].append(li_config.virtual_ip)
                    else:
                        cloud_link = session.query(DCVirtualHostNetworkMapping).filter(DCVirtualHostNetworkMapping.id == connect["cloud_link_id"]).first()
                        host_link = dc_virtual_resource_db.get_virtual_resource_host_link_by_id(cloud_link.hostlink_id) 
                        for link_info in host_link.link_ports:
                            li_config = session.query(DCLogicalInterfaceConfig).filter(DCLogicalInterfaceConfig.logic_device_id == link_info.logic_device_id, 
                                                                                       DCLogicalInterfaceConfig.logical_interface_id == li.id).first()
                            if link_info.switch_sn not in res:
                                vlan_domain_pool = session.query(ResourcePoolVlanDomain).filter(ResourcePoolVlanDomain.id == host_link.vlan_domain_id).first()
                                info = {
                                    "az": host_link.host.az.az_name,
                                    "vlan_domain": vlan_domain_pool.name if vlan_domain_pool else "--",
                                    "switch_sn": link_info.switch_sn,
                                    "vrf_vlan": li.logical_router.vrf_vlan,
                                    "rd": "Auto",
                                    "rt": "Auto",
                                    "gw_interface": [gw_interface],
                                    "virtual_ip": [li_config.virtual_ip] if li_config else []
                                }
                                res[link_info.switch_sn] = info
                            else:
                                if gw_interface not in res[link_info.switch_sn]["gw_interface"]:
                                    res[link_info.switch_sn]["gw_interface"].append(gw_interface)
                                if li_config and li_config.virtual_ip not in res[link_info.switch_sn]["virtual_ip"]:
                                    res[link_info.switch_sn]["virtual_ip"].append(li_config.virtual_ip)
            
        return jsonify({"data": list(res.values()), "status": 200})
    except Exception as e:
        LOG.error(traceback.format_exc())
        return jsonify({'status': 400, 'info': f'Get logical switch status monitor failed. {str(e)}'})  
     
     

@dc_blueprint_mold.route("/virtual_network/save", methods=["POST"])
@admin_permission.require(http_exception=403)
def virtual_network_save():
    db_session = inven_db.get_session()
    try:
        data = request.get_json()
        if not data:
            return jsonify({'info': "Invalid request data", 'status': 400})

        network_id_list = data.get('networkList', [])
        fabric_id = data.get('fabricId')
        ln_id = data.get('logicalNetworkId')
        az_id = data.get('azId')
        position_x = data.get('positionX')
        position_y = data.get('positionY')
        
        network_list = db_session.query(DCVirtualResourceNetwork).filter(DCVirtualResourceNetwork.id.in_(network_id_list))
        db_session.begin()
        for index, network in enumerate(network_list):
            if not network.vlan_id:
                raise ValueError("network vlan not found")
            virtual_network=dc_fabric_db.update_virtual_network(name=network.network_name, ln_id=ln_id, fabric_id=fabric_id, az_id=az_id, 
                                                                position_x=position_x + index*100, position_y=position_y, session=db_session)
            network.virtual_network_id = virtual_network.id
        db_session.commit()
        return jsonify({'status': 200, 'info': 'Save virtual network successed.'})
    except Exception as e:
        LOG.error(traceback.format_exc())
        db_session.rollback()
        return jsonify({'status': 400, 'info': f'Save virtual network failed. {str(e)}'})


@dc_blueprint_mold.route("/virtual_network/list_network_access", methods=["POST"])
@admin_permission.require(http_exception=403)
def list_network_access():
    try:
        data = request.get_json()
        fabric_id = data.get('fabricId', None)
        az_id = data.get('azId', None)
        vpc_name = data.get("vpcName", "")
        
        db_session = inven_db.get_session()
        
        vpc_query = db_session.query(DCVirtualResourceVPC)
        if fabric_id:
            vpc_query = vpc_query.filter(DCVirtualResourceVPC.fabric_id == fabric_id)
        if az_id:
            vpc_query = vpc_query.filter(DCVirtualResourceVPC.az_id == az_id)
        if vpc_name:
            vpc_query = vpc_query.filter(DCVirtualResourceVPC.vpc_name == vpc_name)
        
        vpc_id_list = [vpc.id for vpc in vpc_query.all()]
        pre_query = db_session.query(DCVirtualResourceNetwork).filter(DCVirtualResourceNetwork.vpc_id.in_(vpc_id_list), DCVirtualResourceNetwork.virtual_network_id == None, DCVirtualResourceNetwork.vlan_id != 0)
        
        res=[]
        page_num, page_size, total_count, query_obj = utils.query_helper(DCVirtualResourceNetwork, pre_query=pre_query)
        
        for info in query_obj:
            data = info.make_dict()
            data["vpc_name"] = info.vpc_name
            res.append(data)

        return jsonify({"data": res, "page": page_num,
                        "pageSize": page_size,
                        "total": total_count,
                        "status": 200})
    except Exception as e:
        return jsonify({'status': 400, 'info': 'Failed to list logical switch'})

@dc_blueprint_mold.route("/virtual_network/delete", methods=["POST"])
@admin_permission.require(http_exception=403)
def delete_virtual_network():
    try:
        data = request.get_json()
        if not data:
            return jsonify({'info': "Invalid request data", 'status': 400})

        vn_id = data.get('id', None)
        session = dc_fabric_db.get_session()
        vn = session.query(DCLogicalPort).filter(DCLogicalPort.virtual_network_id == vn_id).first()
        if vn:
            return jsonify({'status': 200, 'info': 'virtual network is in using.'})
        
        dc_fabric_db.delete_virtual_network_by_id(vn_id=vn_id)
        return jsonify({'status': 200, 'info': 'Delete virtual network successfully.'})
    except Exception as e:
        LOG.error(traceback.format_exc())
        return jsonify({'status': 400, 'info': f'Delete virtual network failed. {str(e)}'})   
    
@dc_blueprint_mold.route("/logical_interface/generate_virtual_ip_range", methods=["POST"])
@admin_permission.require(http_exception=403)
def generate_virtual_ip_range():   
    try:
        data = request.get_json()
        if not data:
            return jsonify({'info': "Invalid request data", 'status': 400})

        fabric_id = data.get('fabricId')
        gateway = data.get("AnycastIp")
        res, res_string = fabric_topology_util.allocate_virtual_ip_range(fabric_id, gateway)
        if res:
            return jsonify({'status': 200, 'data': res_string})
        else:
            return jsonify({'status': 500, 'info': res_string})
    except Exception as e:
        LOG.error(traceback.format_exc())
        return jsonify({'status': 400, 'info': f'Generate Virtual Ip Range failed. {str(e)}'})  
   
   
@dc_blueprint_mold.route("/logical_interface/save", methods=["POST"])
@admin_permission.require(http_exception=403)
def logical_interface_save():
    try:
        data = request.get_json()
        if not data:
            return jsonify({'info': "Invalid request data", 'status': 400})

        li_id = data.get('logicalInterfaceId', None)
        ln_id = data.get('logicalNetworkId')
        ls_id = data.get('logicalSwitchId')
        lr_id = data.get('logicalRouterId')
        anycast_ipv4 = data.get('anycast_ipv4', "")
        anycast_mac = data.get('anycast_mac', "")
        virtual_ipv4_range = data.get('virtual_ipv4_range', "")
        
        logical_switch = dc_fabric_db.get_logical_switch_by_id(ls_id)
        logical_router = dc_fabric_db.get_logical_router_by_id(lr_id)
        
        if logical_switch.fabric_id != logical_router.fabric_id:
            return jsonify({'info': "Cannot connect logical router and logical switch of different fabric", 'status': 400})
        
        if fabric_topology_util.check_anycast_subnet_conflict(anycast_ipv4):
            return jsonify({'info': f"anycast ipv4: {anycast_ipv4} conflict with others", 'status': 400})
        
        db_session = inven_db.get_session()
        logical_port = dc_fabric_db.get_logical_port_by_logical_switch_id(ls_id)
        if logical_port:
            ## 存在逻辑交换机与虚拟网络的连接 则可以分配vrf_vlan
            if not logical_router.vrf_vlan:
                network = db_session.query(DCVirtualResourceNetwork).filter(DCVirtualResourceNetwork.virtual_network_id == logical_port.virtual_network_id).first()
                _, available_nums = fabric_topology_util.allocate_vrf_vlan_by_bm_network(network, lr_id)
                if available_nums:
                    dc_fabric_db.update_logical_router(name=logical_router.name, lr_id=lr_id, vrf_vlan=available_nums)
                else:
                    return jsonify({'info': "Cannot allocate logical router vrf_vlan", 'status': 400})

        dc_fabric_db.update_logical_interface(ln_id=ln_id, ls_id=ls_id, lr_id=lr_id, anycast_ipv4=anycast_ipv4, virtual_ipv4_range=virtual_ipv4_range, 
                                              anycast_mac=anycast_mac, li_id=li_id)

        return jsonify({'status': 200, 'info': 'Save logical interface successed.'})
    except Exception as e:
        LOG.error(traceback.format_exc())
        return jsonify({'status': 400, 'info': f'Save logical interface failed. {str(e)}'})   


@dc_blueprint_mold.route("/logical_interface/delete", methods=["POST"])
@admin_permission.require(http_exception=403)
def delete_logical_interface():
    try:
        data = request.get_json()
        if not data:
            return jsonify({'info': "Invalid request data", 'status': 400})

        li_id = data.get('id', None)
        dc_fabric_db.update_logical_interface(status=OverlayStatus.DELETE_NORMAL, li_id=li_id)
        return jsonify({'status': 200, 'info': 'Delete logical interface task will execute.'})
    except Exception as e:
        LOG.error(traceback.format_exc())
        return jsonify({'status': 400, 'info': f'Delete logical interface failed. {str(e)}'})  
   
@dc_blueprint_mold.route("/logical_port/list_virtual_network", methods=["POST"])
@admin_permission.require(http_exception=403)
def list_virtual_network():
    try:
        data = request.get_json()
        if not data:
            return jsonify({'info': "Invalid request data", 'status': 400})

        ls_id = data.get('logicalSwitchId')
        logical_switch = dc_fabric_db.get_logical_switch_by_id(ls_id)
        
        db_session = inven_db.get_session()
        
        # 获取相同logicalnetwork 相同fabric 且没有连线的virtual network
        query_vn = db_session.query(DCVirtualNetwork).outerjoin(
                        DCLogicalPort, 
                        DCLogicalPort.virtual_network_id == DCVirtualNetwork.id
                    ).filter(
                        DCVirtualNetwork.fabric_id == logical_switch.fabric_id,
                        DCVirtualNetwork.logical_network_id == logical_switch.logical_network_id,
                        DCLogicalPort.id.is_(None)
                    ).all()
        
        res = {}
        for vn in query_vn:
            if vn.az_id not in res:
                az = db_session.query(DCVirtualResourcePoolAZ).filter(DCVirtualResourcePoolAZ.id == vn.az_id).first()
                res[vn.az_id] = {
                    "az_id": az.id,
                    "az_name": az.az_name,
                    "virtual_network": []
                }
                
            res[vn.az_id]["virtual_network"].append({
                "vn_id": vn.id,
                "name": vn.name
            })
        
        return jsonify({'status': 200, 'data': list(res.values())})
    except Exception as e:
        LOG.error(traceback.format_exc())
        return jsonify({'status': 400, 'info': f'List Virtual Network failed. {str(e)}'}) 
    

@dc_blueprint_mold.route("/logical_port/save", methods=["POST"])
@admin_permission.require(http_exception=403)
def logical_port_save():
    try:
        data = request.get_json()
        if not data:
            return jsonify({'info': "Invalid request data", 'status': 400})

        ln_id = data.get('logicalNetworkId')
        az_id = data.get('azId')
        ls_id = data.get('logicalSwitchId')
        vn_id = data.get('virtualNetworkId')
        
        db_session = inven_db.get_session()
        
        logical_switch = dc_fabric_db.get_logical_switch_by_id(ls_id)
        virtual_network = dc_fabric_db.get_virtual_network_by_id(vn_id)
        
        if logical_switch.fabric_id != virtual_network.fabric_id:
            return jsonify({'info': "Cannot connect logical switch and virtual network of different fabric", 'status': 400})
        
        lp = db_session.query(DCLogicalPort).filter(DCLogicalPort.logical_switch_id == ls_id, DCLogicalPort.virtual_network_id == vn_id).first()
        if lp:
            return jsonify({'info': "logical switch and virtual network is connected", 'status': 400})
        
        # 分配bd vlan 和 vni
        vni_pool_id, vni_res = resource_pool_vni_db.generate_resource_by_fabric(logical_switch.fabric_id, count=1)
        l2vni= vni_res[0]
        
        network = db_session.query(DCVirtualResourceNetwork).filter(DCVirtualResourceNetwork.virtual_network_id == vn_id).first()
        bd_vlan_res, msg = fabric_topology_util.allocate_bd_vlan_by_bm_network(network, ls_id)
        if not bd_vlan_res:
            # 回收vni
            resource_pool_vni_db.delete_record_by_value(vni_pool_id, vni_res)
            return jsonify({'info': msg, 'status': 400})
              
        try:  
            logical_interface = dc_fabric_db.get_logical_interface_by_logical_switch_id(ls_id)
            if logical_interface:
                ## 存在lr和ls的连接则检查vrf_vlan和里vni是否已经配置 
                if not logical_interface.logical_router.vrf_vlan:
                    network = db_session.query(DCVirtualResourceNetwork).filter(DCVirtualResourceNetwork.virtual_network_id == virtual_network.id).first()
                    _, available_vlan = fabric_topology_util.allocate_vrf_vlan_by_bm_network(network, logical_interface.logical_router.id)
                    if available_vlan:
                        dc_fabric_db.update_logical_router(name=logical_interface.logical_router.name, lr_id=logical_interface.logical_router.id, vrf_vlan=available_vlan)
                    else:
                        raise ValueError("Cannot allocate logical router vrf_vlan")
                    
            ## 更新所有节点上联nigpg的状态
            devices = fabric_topology_util.get_virtual_network_connect_info(virtual_network)
            for device in devices.values():
                if device.get("nic_pg_id", 0):
                    dc_virtual_resource_db.update_node_nic_portgroup(status="Connect Successful", nic_id=device.get("nic_pg_id"))
                if device.get("cloud_link_id", 0):
                    db_session.query(DCVirtualHostNetworkMapping).filter(DCVirtualHostNetworkMapping.id == device.get("cloud_link_id")).update({"status": "Connect Successful"})      
                
        except Exception as e:
            ## 回收已分配的值
            resource_pool_vni_db.delete_record_by_value(vni_pool_id, vni_res)
            for vd_id, values in bd_vlan_res.items():
                resource_pool_vlandomain_db.delete_bdvlan_from_pool_by_value(vd_id, values)
            raise ValueError(f"{str(e)}")       

        dc_fabric_db.update_logical_port(az_id=az_id, ls_id=ls_id, ln_id=ln_id, vn_id=vn_id, l2vni=l2vni, vlan=network.vlan_id)

        return jsonify({'status': 200, 'info': 'Save logical port successed.'})
    except Exception as e:
        LOG.error(traceback.format_exc())
        return jsonify({'status': 400, 'info': f'Save logical port failed. {str(e)}'})


@dc_blueprint_mold.route("/logical_port/delete", methods=["POST"])
@admin_permission.require(http_exception=403)
def delete_logical_port():
    try:
        data = request.get_json()
        if not data:
            return jsonify({'info': "Invalid request data", 'status': 400})

        lp_id = data.get('id', None)
        link_id = data.get('link_id', None)
        type = data.get('type', None)
        
        session = dc_fabric_db.get_session()
        lp = dc_fabric_db.get_logical_port_by_id(lp_id)
        connect_infos = fabric_topology_util.get_virtual_network_connect_info(lp.virtual_network)
        print(connect_infos, len(connect_infos))
        if type == "Cloud":
            session.query(DCVirtualHostNetworkMapping).filter(DCVirtualHostNetworkMapping.id == link_id).update({"status": "Disconnecting"})
            mapping_ids = [connect_info["cloud_link_id"] for connect_info in connect_infos.values()]
            connected_link = session.query(DCVirtualHostNetworkMapping).filter(DCVirtualHostNetworkMapping.id.in_(mapping_ids), 
                                                                               DCVirtualHostNetworkMapping.status.in_(['Connecting', 'Connect Successful', 'Connect Failed'])).all()
            if len(connected_link) == 0:
                dc_fabric_db.update_logical_port(status=OverlayStatus.DELETE_NORMAL, lp_id=lp_id)
        elif type == "BareMetal":
            dc_virtual_resource_db.update_node_nic_portgroup(nic_id=link_id, status="Disconnecting")

            nic_pg_ids = [connect_info["nic_pg_id"] for connect_info in connect_infos.values()]
            connected_link = session.query(NodeNicPortgroup).filter(NodeNicPortgroup.id.in_(nic_pg_ids), 
                                                                    NodeNicPortgroup.status.in_(['Connecting', 'Connect Successful', 'Connect Failed'])).all()
            
            if len(connected_link) == 0:
                dc_fabric_db.update_logical_port(status=OverlayStatus.DELETE_NORMAL, lp_id=lp_id)
        
        return jsonify({'status': 200, 'info': 'Delete logical port task will execute.'})
    except Exception as e:
        LOG.error(traceback.format_exc())
        return jsonify({'status': 400, 'info': f'Delete logical port failed. {str(e)}'}) 


# @dc_blueprint_mold.route("/overlay_deploy_test", methods=["POST"])
# @admin_permission.require(http_exception=403)
# def overlay_deploy_test():
#     try:

#         fabric_topology_util.check_overlay_config()

#         return jsonify({'status': 200, 'info': 'success'})
#     except Exception as e:
#         LOG.error(traceback.format_exc())
#         return jsonify({'status': 400, 'info': 'Failed'})

@dc_blueprint_mold.route("/logical_networks_table_data", methods=["POST"])
@admin_permission.require(http_exception=403)
def logical_networks_table_data():
    try:
        data = request.get_json()
        sort_fields = data.get("sortFields", [])
        if sort_fields:
            sort_field = sort_fields[0].get("field")
            order = sort_fields[0].get("order", "asc")
        else:
            sort_field = ""

        session = dc_fabric_db.get_session()

        logical_switch_count = (
            session.query(func.count(DCLogicalSwitch.id))
            .filter(DCLogicalSwitch.logical_network_id == DCLogicalNetwork.id)
            .correlate(DCLogicalNetwork)
            .scalar_subquery()
        )
        logical_router_count = (
            session.query(func.count(DCLogicalRouter.id))
            .filter(DCLogicalRouter.logical_network_id == DCLogicalNetwork.id)
            .correlate(DCLogicalNetwork)
            .scalar_subquery()
        )
        logical_device_count = (logical_switch_count + logical_router_count).label("logical_device_count")

        virtual_network_count = (
            session.query(func.count(DCVirtualNetwork.id))
            .filter(DCVirtualNetwork.logical_network_id == DCLogicalNetwork.id)
            .correlate(DCLogicalNetwork)
            .scalar_subquery()
            .label("virtual_network_count")
        )

        pre_query = (
            session.query(
                DCLogicalNetwork,
                logical_device_count,
                virtual_network_count
            )
        )

        db_fields = ["name", "create_time", "vpc_lock"]
        if sort_field and sort_field not in db_fields:
            if order == "asc":
                pre_query = pre_query.order_by(logical_device_count.asc()) if sort_field == "logical_device_count" else pre_query.order_by(virtual_network_count.asc())
            else:
                pre_query = pre_query.order_by(logical_device_count.desc()) if sort_field == "logical_device_count" else pre_query.order_by(virtual_network_count.desc())
            page_num, page_size, total_count, query_obj = utils.query_helper(DCLogicalNetwork, skip_sort=True, pre_query=pre_query)
        else:
            page_num, page_size, total_count, query_obj = utils.query_helper(DCLogicalNetwork, pre_query=pre_query)

        res = []
        for info, logical_device_count, virtual_network_count in query_obj:
            data = info.make_dict()
            data["logical_device_count"] = logical_device_count
            data["virtual_network_count"] = virtual_network_count
            data["user"] = "admin"
            res.append(data)
        return jsonify({"data": res, "page": page_num,
                        "pageSize": page_size,
                        "total": total_count,
                        "status": 200})
    except Exception as e:
        LOG.error(traceback.format_exc())
        return jsonify({'status': 400, 'info': 'Failed to get logical networks table data'})
