import logging
import json
import re

from flask import Blueprint, request, jsonify
from datetime import datetime
from server.db.models.wireless import WirelessConfigureSsid,WirelessEthernetPorts, WirelessProfileUsage, WirelessProfile, WirelessChannel, WirelessDhcpService
from server.db.models.wireless_openwifi import Configurations
from server.db.pg_engine import get_pg_session
from server.util import utils

LOG = logging.getLogger(__name__)
configure_mold = Blueprint("configure_mold", __name__, template_folder='templates')


def get_profile_id(session, s):
    match = re.search(r'__variableBlock__<<(.+?)>>', s)
    if match:
        vid = match.group(1)
    else:
        vid = s
    profile = session.query(WirelessProfile).filter(WirelessProfile.variable_id == vid).first()
    if profile:
        return profile.id
    else:
        raise Exception(f'WirelessProfile can not find variable_id: {vid}')

def update_single_profile_usage(session, site_id, referee_id, s, is_variable=True):
    if s:
        if is_variable:
            profile_id = get_profile_id(session, s)
        else:
            profile_id = s
        wpu = WirelessProfileUsage()
        wpu.site_id = site_id
        wpu.referee_id = str(referee_id)
        wpu.profile_id = profile_id
        session.add(wpu)

# 更新引用方法，kind: 1 新增，2 编辑，3 删除
def update_profile_usage(session, kind, site_id, referee_id, configure=None, network_name=None):
    has_profile = False
    if kind != 1:
        # 清空所有
        session.query(WirelessProfileUsage).filter(
            WirelessProfileUsage.referee_id == str(referee_id),
            WirelessProfileUsage.site_id == site_id,
        ).delete()
    if kind == 3:
        return has_profile
    if network_name:
        profile = session.query(WirelessProfile).filter(
            WirelessProfile.name == network_name,
            WirelessProfile.site_id == site_id,
            WirelessProfile.type == 1,
        ).first()
        if profile:
            update_single_profile_usage(session, site_id, referee_id, profile.id, False)
            has_profile = True
    if configure:
        if isinstance(configure, str):
            config_json = json.loads(configure)
        else:
            config_json = configure
        if 'schedule' in config_json:
            update_single_profile_usage(session, site_id, referee_id, config_json['schedule'])
            has_profile = True
        if 'multi-psk' in config_json:
            update_single_profile_usage(session, site_id, referee_id, config_json['multi-psk'])
            has_profile = True
        if 'radius' in config_json:
            update_single_profile_usage(session, site_id, referee_id, config_json['radius'])
            has_profile = True
        if 'captive' in config_json and 'web-root' in config_json['captive']:
            update_single_profile_usage(session, site_id, referee_id, config_json['captive']['web-root'])
            has_profile = True
    return has_profile

@configure_mold.route('/ssid', methods=['POST'])
def create_configure():
    msg = {'status': 200, 'info': 'Create configure success.'}
    try:
        data = request.get_json(force=True)
        name = data.get('name')
        site_id = data.get('site_id')
        ssid_configure = data.get('ssid_configure')
        network_name = data.get('network_name')
        if not name:
            return jsonify({'status': 400, 'info': 'Name can not be empty.'})
        if site_id is None:
            return jsonify({'status': 400, 'info': 'siteId can not be empty.'})
        with get_pg_session() as session:
            with session.begin():
                configure = session.query(WirelessConfigureSsid).filter(
                    WirelessConfigureSsid.name == name,
                    WirelessConfigureSsid.site_id == site_id
                ).first()
                if configure:
                    return jsonify({'status': 400, 'info': 'The name already exists.'})
                configure = WirelessConfigureSsid()
                configure.name = name
                configure.site_id = site_id
                configure.security = data.get('security')
                configure.radio = data.get('radio')
                configure.network_name = network_name
                configure.group_name = 'all'
                configure.is_enable = 1
                configure.ssid_configure = ssid_configure
                session.add(configure)
                session.flush()
                update_profile_usage(session, 1, site_id, configure.id, ssid_configure, network_name)
    except Exception as e:
        msg = {'status': 500, 'info': str(e)}
    return jsonify(msg)


@configure_mold.route('/ssid', methods=['PUT'])
def modify_configure():
    msg = {'status': 200, 'info': 'Modify configure success.'}
    try:
        data = request.get_json(force=True)
        is_enable = data.get('is_enable')
        group_name = data.get('group_name')
        ssid_configure = data.get('ssid_configure')
        network_name = data.get('network_name')
        with get_pg_session() as session:
            with session.begin():
                config = session.query(WirelessConfigureSsid).filter(WirelessConfigureSsid.id == data.get('id')).first()
                if not config:
                    return jsonify({'status': 404, 'info': 'Configure not found.'})
                # 开关
                if is_enable is not None:
                    config.is_enable = int(is_enable)
                # 编辑组
                elif group_name:
                    config.group_name = group_name
                else:
                    config.name = data.get('name')
                    config.security = data.get('security')
                    config.radio = data.get('radio')
                    config.network_name = network_name
                    config.ssid_configure = ssid_configure
                    update_profile_usage(session, 2, config.site_id, config.id, ssid_configure, network_name)
    except Exception as e:
        msg = {'status': 500, 'info': str(e)}
    return jsonify(msg)


@configure_mold.route('/ssid', methods=['DELETE'])
def delete_configure():
    msg = {'status': 200, 'info': 'Delete configure success.'}
    try:
        data = request.get_json(force=True)
        with get_pg_session() as session:
            with session.begin():
                config = session.query(WirelessConfigureSsid).filter(WirelessConfigureSsid.id == data.get('id')).first()
                if not config:
                    return jsonify({'status': 404, 'info': 'Configure not found.'})
                update_profile_usage(session, 3, config.site_id, config.id)
                session.delete(config)
    except Exception as e:
        msg = {'status': 500, 'info': str(e)}
    return jsonify(msg)


@configure_mold.route('/ssid/list', methods=['POST'])
def list_configures():
    try:
        data = request.get_json(force=True)
        site_id = data.get('site_id')
        with get_pg_session() as session:
            pre_query = session.query(WirelessConfigureSsid)
            if site_id is not None:
                pre_query = pre_query.filter(WirelessConfigureSsid.site_id == site_id)
            page_num, page_size, total_count, query_obj = utils.query_helper(WirelessConfigureSsid, pre_query=pre_query)
            response = {
                "info": [obj.make_dict() for obj in query_obj],
                "page": page_num,
                "pageSize": page_size,
                "total": total_count,
                "status": 200
            }
    except Exception as e:
        response = {"status": 500, "info": str(e)}
    return jsonify(response)


@configure_mold.route('/ssid', methods=['GET'])
def get_configure_detail():
    try:
        config_id = request.args.get('id')
        with get_pg_session() as session:
            config = session.query(WirelessConfigureSsid).filter(WirelessConfigureSsid.id == config_id).first()
            if not config:
                return jsonify({'status': 404, 'info': 'Configure not found.'})
            result = config.make_dict()
            return jsonify({'status': 200, 'info': result})
    except Exception as e:
        return jsonify({'status': 500, 'info': str(e)})


@configure_mold.route('/general', methods=['PUT'])
def update_configure_general():
    try:
        data = request.get_json(force=True)
        cid = data.get('id')
        site_id = data.get('site_id')
        config = data.get('config')
        if not cid:
            return jsonify({'status': 400, 'info': 'ID can not be empty.'})
        with get_pg_session() as session:
            with session.begin():
                c = session.query(Configurations).filter(Configurations.id == cid).first()
                if not c:
                    return jsonify({'status': 404, 'info': 'Configure not found.'})
                c.configuration = config
                # 处理引用关系
                config_json = json.loads(config)
                for cj in config_json:
                    content = json.loads(cj['configuration'])
                    if 'radios' in content:
                        for r in content['radios']:
                            has_profile = update_profile_usage(session, 2, site_id, cid, r)
                            if has_profile:
                                break
                return jsonify({'status': 200, 'info': 'Modify general config success.'})
    except Exception as e:
        return jsonify({'status': 500, 'info': str(e)})


@configure_mold.route('/channel', methods=['GET'])
def get_wireless_channel():
    country_code = request.args.get('countryCode', type=str)
    if not country_code:
        return jsonify({'status': 400, 'info': 'countryCode is required.'})
    try:
        with get_pg_session() as session:
            result = session.query(
                WirelessChannel.country_code,
                WirelessChannel._2g_channel,
                WirelessChannel._5g_channel,
                WirelessChannel._6g_channel,
            ).filter(
                WirelessChannel.country_code == country_code.upper()
            ).first()
            if not result:
                return jsonify({'status': 404, 'info': f"No channel found for country {country_code}."})
            return jsonify({
                "country_code": result.country_code,
                "2g_channel": result._2g_channel,
                "5g_channel": result._5g_channel,
                "6g_channel": result._6g_channel
            })

    except Exception as e:
        LOG.error(f"Failed to get channel config: {str(e)}")
        return jsonify({'status': 500, 'info': str(e)})


# DHCP服务接口 
# 新增DHCP服务接口
@configure_mold.route('/dhcp_service', methods=['POST'])
def create_dhcp_service():
    msg = {'status': 200, 'info': 'Add dhcp service success.'}
    try:
        data = request.get_json(force=True)
        name = data.get('name')
        site_id = data.get('site_id')
        subnet = data.get('subnet')
        vlan = data.get('vlan', '-')
        dhcp_configure = data.get('dhcp_configure')
        description = data.get('description')

        if not name:
            return jsonify({'status': 400, 'info': 'Name can not be empty.'})
        if site_id is None:
            return jsonify({'status': 400, 'info': 'site_id can not be empty.'})
        if not subnet:
            return jsonify({'status': 400, 'info': 'subnet can not be empty.'})
        if not dhcp_configure:
            return jsonify({'status': 400, 'info': 'dhcp_configure can not be empty.'})

        with get_pg_session() as session:
            with session.begin():
                # 检查名称是否已存在
                existing = session.query(WirelessDhcpService).filter(
                    WirelessDhcpService.name == name,
                    WirelessDhcpService.site_id == site_id
                ).first()
                if existing:
                    return jsonify({'status': 400, 'info': 'The name already exists.'})

                # 创建新的DHCP服务
                dhcp_service = WirelessDhcpService()
                dhcp_service.name = name
                dhcp_service.site_id = site_id
                dhcp_service.subnet = subnet
                dhcp_service.vlan = vlan
                dhcp_service.dhcp_configure = dhcp_configure if isinstance(dhcp_configure, dict) else json.loads(dhcp_configure)
                dhcp_service.description = description
                session.add(dhcp_service)
                session.flush()

    except Exception as e:
        LOG.error(f"Failed to create dhcp service: {str(e)}")
        msg = {'status': 500, 'info': str(e)}
    return jsonify(msg)


# 分页查询DHCP服务列表接口
@configure_mold.route('/dhcp_service_list', methods=['GET'])
def list_dhcp_services():
    try:
        site_id = request.args.get('siteId', type=int)
        sort_by = request.args.get('sortBy')
        sort_type = request.args.get('sortType', 'desc')
        page_num = request.args.get('pageNum', 1, type=int)
        page_size = request.args.get('pageSize', 10, type=int)

        if site_id is None:
            return jsonify({'status': 400, 'info': 'siteId is required.'})

        with get_pg_session() as session:
            query = session.query(WirelessDhcpService).filter(
                WirelessDhcpService.site_id == site_id
            )

            # 排序
            if sort_by:
                if hasattr(WirelessDhcpService, sort_by):
                    sort_column = getattr(WirelessDhcpService, sort_by)
                    if sort_type.lower() == 'asc':
                        query = query.order_by(sort_column.asc())
                    else:
                        query = query.order_by(sort_column.desc())

            # 分页
            total = query.count()
            offset = (page_num - 1) * page_size
            results = query.offset(offset).limit(page_size).all()

            # 构造返回数据
            info = []
            for service in results:
                info.append({
                    'id': service.id,
                    'site_id': service.site_id,
                    'name': service.name,
                    'subnet': service.subnet,
                    'vlan': service.vlan,
                    'dhcp_configure': service.dhcp_configure,
                    'description': service.description,
                    'modified_time': service.modified_time
                })

            response = {
                'info': info,
                'status': 200,
                'pageNum': page_num,
                'pageSize': page_size,
                'total': total
            }

    except Exception as e:
        LOG.error(f"Failed to list dhcp services: {str(e)}")
        response = {'status': 500, 'info': str(e)}
    return jsonify(response)


# 模糊查询所有DHCP服务名称
@configure_mold.route('/dhcp_service_filter', methods=['GET'])
def filter_dhcp_services():
    try:
        site_id = request.args.get('siteId', type=int)
        key = request.args.get('key', '')

        if site_id is None:
            return jsonify({'status': 400, 'info': 'siteId is required.'})

        with get_pg_session() as session:
            query = session.query(WirelessDhcpService.name).filter(
                WirelessDhcpService.site_id == site_id
            )

            # 模糊查询
            if key:
                query = query.filter(WirelessDhcpService.name.ilike(f'%{key}%'))

            results = query.all()
            names = [result.name for result in results]

            response = {
                'info': names,
                'status': 200
            }

    except Exception as e:
        LOG.error(f"Failed to filter dhcp services: {str(e)}")
        response = {'status': 500, 'info': str(e)}
    return jsonify(response)

# 查询DHCP服务详情接口
@configure_mold.route('/dhcp_service', methods=['GET'])
def get_dhcp_service_detail():
    try:
        service_id = request.args.get('id', type=int)

        if service_id is None:
            return jsonify({'status': 400, 'info': 'id is required.'})

        with get_pg_session() as session:
            service = session.query(WirelessDhcpService).filter(
                WirelessDhcpService.id == service_id
            ).first()

            if not service:
                return jsonify({'status': 404, 'info': 'DHCP service not found.'})

            info = {
                'id': service.id,
                'site_id': service.site_id,
                'name': service.name,
                'subnet': service.subnet,
                'vlan': service.vlan,
                'dhcp_configure': service.dhcp_configure,
                'description': service.description
            }

            response = {
                'info': info,
                'status': 200
            }

    except Exception as e:
        LOG.error(f"Failed to get dhcp service detail: {str(e)}")
        response = {'status': 500, 'info': str(e)}
    return jsonify(response)

# 更新DHCP服务接口
@configure_mold.route('/dhcp_service', methods=['PUT'])
def update_dhcp_service():
    msg = {'status': 200, 'info': 'Modify dhcp service success.'}
    try:
        data = request.get_json(force=True)
        service_id = data.get('id')
        subnet = data.get('subnet')
        vlan = data.get('vlan')
        dhcp_configure = data.get('dhcp_configure')
        description = data.get('description')

        if service_id is None:
            return jsonify({'status': 400, 'info': 'id can not be empty.'})

        with get_pg_session() as session:
            with session.begin():
                service = session.query(WirelessDhcpService).filter(
                    WirelessDhcpService.id == service_id
                ).first()

                if not service:
                    return jsonify({'status': 404, 'info': 'DHCP service not found.'})

                # 更新字段
                if subnet is not None:
                    service.subnet = subnet
                if vlan is not None:
                    service.vlan = vlan
                if dhcp_configure is not None:
                    service.dhcp_configure = dhcp_configure if isinstance(dhcp_configure, dict) else json.loads(dhcp_configure)
                if description is not None:
                    service.description = description

    except Exception as e:
        LOG.error(f"Failed to update dhcp service: {str(e)}")
        msg = {'status': 500, 'info': str(e)}
    return jsonify(msg)


#   删除DHCP服务接口
#  删除dhcp-service时，要根据其名字xx，去wireless_configure_ssid、wireless_ethernet_ports  这两张表中查network_type=3 and vlan_or_dhcp_name= 'xx' 是否存在，存在则不能删除此dhcp-serivce
@configure_mold.route('/dhcp_service', methods=['DELETE'])
def delete_dhcp_service():
    msg = {'status': 200, 'info': 'Delete dhcp service success.'}
    try:
        data = request.get_json(force=True)
        service_id = data.get('id')
        
        with get_pg_session() as session:
            with session.begin():

                # 根据id查询DHCP的name
                name = session.query(WirelessDhcpService.name).filter(
                    WirelessDhcpService.id == service_id
                ).first()

                # 在wireless_configure_ssid表中查询是否有network_type=3并且vlan_or_dhcp_name= name
                if session.query(WirelessConfigureSsid).filter(
                        WirelessConfigureSsid.network_type == 3,
                        WirelessConfigureSsid.vlan_or_dhcp_name == name
                ).first():
                    return jsonify({'status': 400, 'info': 'This DHCP service is used by a wireless configure ssid.'})

                # 在wireless_ethernet_ports表中查询是否有network_type=3并且vlan_or_dhcp_name= name
                if session.query(WirelessEthernetPorts).filter(
                        WirelessEthernetPorts.network_type == 3,
                        WirelessEthernetPorts.vlan_or_dhcp_name == name
                ).first():
                    return jsonify({'status': 400, 'info': 'This DHCP service is used by a wireless ethernet ports.'})

                if service_id is None:
                    return jsonify({'status': 400, 'info': 'id can not be empty.'})
                service = session.query(WirelessDhcpService).filter(
                    WirelessDhcpService.id == service_id
                ).first()

                if not service:
                    return jsonify({'status': 404, 'info': 'DHCP service not found.'})

                session.delete(service)

    except Exception as e:
        LOG.error(f"Failed to delete dhcp service: {str(e)}")
        msg = {'status': 500, 'info': str(e)}
    return jsonify(msg)

@configure_mold.route('/ethernet_ports', methods=['POST'])
def add_ethernet_port():
    msg = {'status': 200, 'info': 'Add ethernet port success.'}
    try:
        data = request.get_json(force=True)
        site_id = data.get('site_id')
        port_name = data.get('port')
        network_type = data.get('network_type')
        
        if not site_id:
            return jsonify({'status': 400, 'info': 'site_id can not be empty.'})
        if not port_name:
            return jsonify({'status': 400, 'info': 'port can not be empty.'})
        if not network_type:
            return jsonify({'status': 400, 'info': 'network_type can not be empty.'})

        with get_pg_session() as session:
            with session.begin():
                existing_port = session.query(WirelessEthernetPorts).filter(
                    WirelessEthernetPorts.site_id == site_id,
                    WirelessEthernetPorts.port == port_name
                ).first()
                if existing_port:
                    return jsonify({'status': 400, 'info': 'The port already exists in this site.'})

                new_port = WirelessEthernetPorts()
                new_port.site_id = site_id
                new_port.port = port_name
                new_port.mac = data.get('mac')
                new_port.network_type = network_type
                new_port.vlan_or_dhcp_name = data.get('vlan_or_dhcp_name')
                new_port.multicast = data.get('multicast', 1)
                new_port.learning = data.get('learning', 1)
                new_port.reverse_path = data.get('reverse_path', 2)
                new_port.vlan_tag = data.get('vlan_tag', 1)
                new_port.create_time = datetime.now()
                new_port.modified_time = datetime.now()

                session.add(new_port)

    except Exception as e:
        msg = {'status': 500, 'info': str(e)}

    return jsonify(msg)

@configure_mold.route('/ethernet_ports', methods=['GET'])
def get_ethernet_ports():
    msg = {'status': 200, 'info': []}
    try:
        site_id = request.args.get('siteId', type=int)
        page_num = request.args.get('pageNum', 1, type=int)
        page_size = request.args.get('pageSize', 10, type=int)  
        sort_by = request.args.get('sortBy', 'id')
        sort_type = request.args.get('sortType', 'desc')

        if not site_id:
            return jsonify({'status': 400, 'info': 'siteId can not be empty.'})
        if page_num < 1 or page_size < 1:
            return jsonify({'status': 400, 'info': 'pageNum and pageSize must be positive.'})

        with get_pg_session() as session:
            query = session.query(WirelessEthernetPorts).filter(
                WirelessEthernetPorts.site_id == site_id
            )
            
            if sort_type.lower() == 'asc':
                query = query.order_by(getattr(WirelessEthernetPorts, sort_by).asc())
            else:
                query = query.order_by(getattr(WirelessEthernetPorts, sort_by).desc())
            
            ports = query.limit(page_size).offset((page_num - 1) * page_size).all()
            total = query.count()

            port_list = [{
                'create_time': port.create_time.isoformat() if port.create_time else None,
                'modified_time': port.modified_time.isoformat() if port.modified_time else None,
                'site_id': port.site_id,
                'port': port.port,
                'mac': port.mac,
                'network_type': port.network_type,
                'vlan_or_dhcp_name': port.vlan_or_dhcp_name,  
                'multicast': port.multicast,
                'learning': port.learning,
                'reverse_path': port.reverse_path,
                'vlan_tag': port.vlan_tag
            } for port in ports]

            msg['info'] = port_list
            msg['pageNum'] = page_num
            msg['pageSize'] = page_size
            msg['total'] = total

    except Exception as e:
        msg = {'status': 500, 'info': str(e)}

    return jsonify(msg)

@configure_mold.route('/ethernet_ports', methods=['DELETE'])
def delete_ethernet_port():
    msg = {'status': 200, 'info': 'Delete ethernet port success.'}
    try:
        data = request.get_json(force=True)
        port_id = data.get('id')

        if not port_id:
            return jsonify({'status': 400, 'info': 'id is required'})

        with get_pg_session() as session:
            with session.begin():
                port = session.query(WirelessEthernetPorts).get(port_id)
                if not port:
                    return jsonify({'status': 404, 'info': 'Ethernet port not found'})
                
                session.delete(port)

    except Exception as e:
        msg = {'status': 500, 'info': str(e)}

    return jsonify(msg)
