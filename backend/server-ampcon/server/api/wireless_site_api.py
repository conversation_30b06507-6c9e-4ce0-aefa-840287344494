import logging
import json
from datetime import datetime
from flask import Blueprint, request, jsonify
from sqlalchemy.exc import IntegrityError
from sqlalchemy import func
from server.db.models.wireless import WirelessConfigureSsid, WirelessSiteLabel, WirelessChannel
from server.db.models.wireless_openwifi import Inventory, Configurations
from server.db.pg_engine import get_pg_session
from server.util.permission import super_user_permission

LOG = logging.getLogger(__name__)
wireless_site_mold = Blueprint("wireless_site_mold", __name__, template_folder='templates')


@wireless_site_mold.route('/channel', methods=['GET'])
def get_site_channel():

    site_id = request.args.get('siteId', type=str)

    if not site_id:
        return jsonify({"status": 400, "info": "siteId is required"})

    try:
        with get_pg_session() as session:
            # 1. 从configurations表中获取:venue字段等于站点ID，name字段等于radio的数据
            config = session.query(Configurations).filter(
                Configurations.venue == site_id,
                func.lower(Configurations.name) == "radio"
            ).first()

            if not config:
                return jsonify({"status": 404, "info": f"No radio configuration found for site {site_id}"})

            # 2. 解析configuration字段中的JSON内容
            try:
                config_data = json.loads(config.configuration)
            except (json.JSONDecodeError, TypeError) as e:
                LOG.error(f"Failed to parse configuration JSON: {str(e)}")
                return jsonify({"status": 500, "info": "Invalid configuration JSON format"})

            # 3. 获取第一个radio的country字段
            country_code = None
            if isinstance(config_data, dict) and "radios" in config_data:
                radios = config_data["radios"]
                if isinstance(radios, list) and len(radios) > 0:
                    first_radio = radios[0]
                    if isinstance(first_radio, dict) and "country" in first_radio:
                        country_code = first_radio["country"]

            if not country_code:
                return jsonify({"status": 404, "info": "No country code found in radio configuration"})

            # 4. 根据国家码从wireless_channel表获取信道信息
            channel_info = session.query(   
                WirelessChannel.country_code,
                WirelessChannel._2g_channel,
                WirelessChannel._5g_channel,
                WirelessChannel._6g_channel
            ).filter(
                WirelessChannel.country_code == country_code.upper()
            ).first()

            if not channel_info:
                return jsonify({"status": 404, "info": f"No channel information found for country code {country_code}"})

            # 5. 构造返回结果
            result = {
                "country_code": channel_info.country_code,
                "2g_channel": channel_info._2g_channel or {},
                "5g_channel": channel_info._5g_channel or {},
                "6g_channel": channel_info._6g_channel or {}
            }

            return jsonify({"status": 200, "info": result})

    except Exception as e:
        LOG.error(f"Failed to get site channel information: {str(e)}")
        return jsonify({"status": 500, "info": str(e)})

# 创建group
@wireless_site_mold.route('/label', methods=['POST'])
@super_user_permission.require(http_exception=403)
def add_label():
    try:
        info = request.get_json()
        site_id = info.get('site_id')
        name = info.get('name')

        if not site_id or not name:
            msg = {"status": 400, "info": "site_id and name are required."}
            return jsonify(msg)

        # 开启事务
        with get_pg_session() as session:
            with session.begin():
                new_label = WirelessSiteLabel()
                new_label.site_id = site_id
                new_label.name = name
                new_label.create_time = datetime.now()
                new_label.modified_time = datetime.now()
                session.add(new_label)
            msg = {"status": 200, "info": "Create label success."}
    except IntegrityError:
        msg = {"status": 400, "info": "A label with the same site_id and name already exists."}
    except Exception as e:
        msg = {"status": 500, "info": str(e)}
    finally:
        return jsonify(msg)


#get label list
@wireless_site_mold.route('/label', methods=['GET'])
@super_user_permission.require(http_exception=403)
def get_labels():
    site_id = request.args.get('siteId', type=int)
    key = request.args.get('key', '', type=str)

    try:
        with get_pg_session() as session:
            query = session.query(WirelessSiteLabel).filter_by(site_id=site_id)

            if key:
                query = query.filter(WirelessSiteLabel.name.ilike(f"%{key}%"))

            label = query.all()
            result = [{
                "id": g.id,
                "name": g.name,
                "site_id": g.site_id
            } for g in label]

            return jsonify({"status": 200, "info": result})
    except Exception as e:
        LOG.error(f"Failed to get site labels: {str(e)}")
        return jsonify({"status": 500, "info": str(e)})

#delete label
@wireless_site_mold.route('/label', methods=['DELETE'])
@super_user_permission.require(http_exception=403)
def delete_label():
    info = request.get_json()
    label_id = info.get('id')
    try:
        with get_pg_session() as session:
            with session.begin():
                label = session.query(WirelessSiteLabel).filter_by(id=label_id).first()
                if not label:
                    return jsonify({"status": 403, "info": "Label does not exist, deletion not allowed."})
                site_id = label.site_id
                label_name = label.name
                # Inventory删除label
                session.execute(
                    """
                    UPDATE inventory
                    SET labelsname = NULLIF(
                        array_to_string(
                            array_remove(
                                string_to_array(labelsname, ','),
                                :label_name
                            ),
                            ','
                        ),
                        ''
                    )
                    WHERE venue = :site_id AND labelsname LIKE '%' || :label_name || '%'
                    """,
                    {"label_name": label_name, "site_id": str(site_id)}
                )
                # wireless_configure_ssid删除jsonb数组中的label_name
                session.execute(
                    """
                    UPDATE wireless_configure_ssid
                    SET labels_name = COALESCE((
                        SELECT jsonb_agg(value)
                        FROM jsonb_array_elements_text(labels_name) AS value
                        WHERE value != :label_name
                    ), '[]'::jsonb)
                    WHERE site_id = :site_id AND labels_name @> to_jsonb(ARRAY[:label_name])
                    """,
                    {"label_name": label_name, "site_id": site_id}
                )

                session.delete(label)
            return jsonify({"status": 200, "info": "Delete label success."})
    except Exception as e:
        LOG.error(f"Delete label failed: {e}")
        return jsonify({"status": 500, "info": str(e)})



