import logging
import re

from ncclient import manager
import sys, os
import traceback
import json
import ipaddress
import crypt
from lxml import etree
from datetime import datetime
import socket
from ncclient.transport.errors import SSHError, AuthenticationError, SSHUnknownHostError


BASE_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(BASE_DIR)

from server.util.ssh_util import interactive_shell_configure
from server import constants as C
from server.db.models.dc_blueprint import DCFabricTopologyNode
from server.db.models.campus_blueprint import CampusSiteNodes
from server.db.models.dc_virtual_resource import SwitchPortgroupConfig
from server.db.models.dc_blueprint import DCLogicalInterfaceConfig, DCLogicalPortConfig, DCLogicalRouterConfig

LOG = logging.getLogger(__name__)


class ConfigDeployer:
    def __init__(self, host, username, password, port=22, session=None):
        """
        :param host: 主机地址
        :param username: SSH用户名
        :param password: SSH密码
        :param port: 端口，默认22
        :param protocol: 配置下发协议，'cli' 或 'netconf'
        """
        self.host = host
        self.username = username
        self.password = password
        self.port = port
        self.session = session

    def deploy_cli_config(self, role_type, meta_data, **kwargs):
        """
        通过 CLI 协议下发配置
        """
        raise NotImplementedError("Subclasses should implement this method")

    def deploy_netconf_config(self, role_type, meta_data, **kwargs):
        """
        通过 NetConf 协议下发配置
        """
        raise NotImplementedError("Subclasses should implement this method")


class CLIConfigDeployer(ConfigDeployer):
    """
        task_type ==> BGP / OSPF / LINK / MLAG_LEAF / MLAG_ACCESS / HOSTNAME
    """

    def __init__(self, host, username, password, port=22, session=None):
        super().__init__(host, username, password, port, session)

    @staticmethod
    def common_config(op, **kwargs):
        hostname = kwargs.get("hostname")
        del_cmn_conf = [
            f"{op} ip routing enable",
            f"{op} protocols lldp",
            f"{op} protocols evpn",
            f"{op} system hostname",
            f"{op} protocols spanning-tree enable"
        ]
        set_cmn_conf = [
            f"{op} ip routing enable true",
            f"{op} protocols lldp enable true",
            f"{op} protocols evpn enable true",
            f"{op} system hostname {hostname}",
            f"{op} protocols spanning-tree enable false",
        ]

        return set_cmn_conf if op == "set" else del_cmn_conf

    @staticmethod
    def cli_bgp(role_type, op, **kwargs):
        """
        ## 环回口配置
        set l3-interface loopback loopback0 address <bgp-router-id ipv4-address> prefix-length 32
        set l3-interface loopback loopback1 address <vtep-interface ipv4-address> prefix-length 32

        ## BGP配置
        # EBGP
        set protocols bgp local-as <ASN>
        set protocols bgp ebgp-requires-policy false
        set protocols bgp router-id <bgp-router-id ipv4-address>
        #使用直连地址建立EBGP对等体，remote-as为设置为external  对端ip即routed_interface_ip_address
        set protocols bgp neighbor <routed_interface_ip_address> remote-as "external"
        set protocols bgp neighbor <routed_interface_ip_address> ipv4-unicast
        set protocols bgp neighbor <routed_interface_ip_address> evpn activate true

        # 对于mlag leaf 还需要配置peerlink
        set protocols bgp neighbor <对端l3 interface ip> remote-as "internal"
        set protocols bgp neighbor <对端l3 interface ip> update-source "<本端l3 interface ip>"
        set protocols bgp neighbor <对端l3 interface ip> evpn activate true

        set protocols bgp ipv4-unicast network <routed_interface_network_address>/<mask>
        set protocols bgp ipv4-unicast network <loopback0 bgp-router-id>/32
        set protocols bgp ipv4-unicast network <loopback1 vtep_interface>/32
        """
        bgp = kwargs.get("bgp", {})
        asn, bgp_router_id, vtep_interface, spine_link, peer_link = (
            bgp.get("asn", ""), bgp.get("bgp_router_id", ""), bgp.get("vtep_interface", ""), bgp.get("spine_link", []),
            bgp.get("peer_link", {})
        )
        del_bgp_template = [
            f"{op} l3-interface loopback loopback0",
            f"{op} l3-interface loopback loopback1" if role_type == "leaf" else "",
            f"{op} protocols bgp ebgp-requires-policy",
            f"{op} protocols bgp router-id",
            f"{op} protocols bgp ipv4-unicast network {bgp_router_id}/32",
            f"{op} protocols bgp ipv4-unicast network {vtep_interface}/32" if role_type == "leaf" else ""
        ]
        set_bgp_template = [
            f"{op} l3-interface loopback loopback0 address {bgp_router_id} prefix-length 32",
            f"{op} l3-interface loopback loopback1 address {vtep_interface} prefix-length 32" if role_type == "leaf" else "",
            f"{op} protocols bgp local-as {asn}",
            f"{op} protocols bgp ebgp-requires-policy false",
            f"{op} protocols bgp router-id {bgp_router_id}",
            f"{op} protocols bgp ipv4-unicast network {bgp_router_id}/32",
            f"{op} protocols bgp ipv4-unicast network {vtep_interface}/32" if role_type == "leaf" else ""
        ]
        for k in spine_link:
            routed_interface_target_address = k.get("routed_interface_target_address", "").split('/')[0]
            routed_interface_network_address = k.get("routed_interface_network_address", "")
            del_bgp_template.extend([
                f"{op} protocols bgp neighbor {routed_interface_target_address}",
                f"{op} protocols bgp ipv4-unicast network {routed_interface_network_address}",
            ])
            set_bgp_template.extend([
                f"{op} protocols bgp neighbor {routed_interface_target_address} remote-as external",
                f"{op} protocols bgp neighbor {routed_interface_target_address} ipv4-unicast",
                f"{op} protocols bgp neighbor {routed_interface_target_address} evpn activate true",
                f"{op} protocols bgp ipv4-unicast network {routed_interface_network_address}",
            ])
        if role_type == "leaf" and peer_link:
            peer_ipv4_address = peer_link.get("peer_ipv4_address", "").split('/')[0]
            mlag_l3_interface_ip_address = peer_link.get("mlag_l3_interface_ip_address", "").split('/')[0]
            del_bgp_template.extend([
                f"{op} protocols bgp neighbor {peer_ipv4_address}",
            ])
            set_bgp_template.extend([
                f"{op} protocols bgp neighbor {peer_ipv4_address} remote-as internal",
                f"{op} protocols bgp neighbor {peer_ipv4_address} update-source {mlag_l3_interface_ip_address}",
                f"{op} protocols bgp neighbor {peer_ipv4_address} evpn activate true",
            ])
        del_bgp_template.append(f"commit\n{op} protocols bgp local-as")

        if op == "set":
            bgp_config = set_bgp_template
        else:
            bgp_config = del_bgp_template

        if role_type:
            ...

        return bgp_config

    @staticmethod
    def cli_ospf(role_type, op, **kwargs):
        """
        ## 环回口配置
        set l3-interface loopback loopback0 address <ospf-router-id ipv4-address> prefix-length 32
        # loopback1不做他用
        set l3-interface loopback loopback1 address <vtep-interface ipv4-address> prefix-length 32

        set protocols ospf router-id <ospf-router-id ipv4-address>

        #宣告直连口地址，环回口地址到area0  2个环回口+所有上联/下联端口
        set protocols ospf network <ospf-router-id-with-mask ipv4-subnet> area <area-id ASN>
        """
        ospf = kwargs.get("ospf", {})
        vtep_interface, ospf_router_id, area_id, spine_link, peer_link = (
            ospf.get("vtep_interface", ""), ospf.get("ospf_router_id", ""), ospf.get("area_id", ""),
            ospf.get("spine_link", []), ospf.get("peer_link", {})
        )
        del_ospf_template = [
            f"{op} l3-interface loopback loopback0",
            f"{op} l3-interface loopback loopback1" if role_type == "leaf" else "",
            f"{op} protocols ospf router-id",
            f"{op} protocols ospf area {area_id}",
            f"{op} protocols ospf network {ospf_router_id}/32",
            f"{op} protocols ospf network {vtep_interface}/32" if role_type == "leaf" else "",
        ]
        set_ospf_template = [
            f"{op} l3-interface loopback loopback0 address {ospf_router_id} prefix-length 32",
            f"{op} l3-interface loopback loopback1 address {vtep_interface} prefix-length 32" if role_type == "leaf" else "",
            f"{op} protocols ospf router-id {ospf_router_id}",
            f"{op} protocols ospf area {area_id}",
            f"{op} protocols ospf network {ospf_router_id}/32 area {area_id}",
            f"{op} protocols ospf network {vtep_interface}/32 area {area_id}" if role_type == "leaf" else "",
        ]

        for k in spine_link:
            interface_name = k.get("interface_name", "")
            l3name = "rif-" + interface_name.split('-')[0] + interface_name.split('/')[-1]
            routed_interface_network_address = k.get("routed_interface_network_address", "")
            del_ospf_template.extend([
                f"{op} protocols ospf interface {l3name}",
                f"{op} protocols ospf network {routed_interface_network_address}"
            ])
            set_ospf_template.extend([
                f"{op} protocols ospf interface {l3name} network point-to-point",
                f"{op} protocols ospf network {routed_interface_network_address} area {area_id}"
            ])
        if role_type == "leaf" and peer_link:
            mlag_l3_interface_network_address = peer_link.get("mlag_l3_interface_network_address", "")
            del_ospf_template.extend([
                f"{op} protocols ospf network {mlag_l3_interface_network_address}"
            ])
            set_ospf_template.extend([
                f"{op} protocols ospf network {mlag_l3_interface_network_address} area {area_id}"
            ])

        if op == "set":
            ospf_config = set_ospf_template
        else:
            ospf_config = del_ospf_template

        if role_type:
            ...

        return ospf_config

    @staticmethod
    def cli_link(role_type, op, **kwargs):
        """
        ## 直连路由口配置
        set ip routing enable true

        #保留vlan，保留vlan的需要提前预留足够的数量
        set vlans reserved-vlan <reserved-vlan>
        set interface gigabit-ethernet <interface-name> routed-interface enable true
        set interface gigabit-ethernet <interface-name> routed-interface name <l3name-string>
        **The name must start with "rif-", for example, rif-ge1.**

        #路由口ip地址
        set l3-interface routed-interface <l3name-string> address <routed-interface-ip-address> prefix-length <routed-interface-ip-address-mask>

        #接口和端口MTU
        set interface gigabit-ethernet <interface-name> mtu <value 9216>

        ##端口描述信息，eg:linking_hostname_portnum
        set interface gigabit-ethernet <interface-name> description <description string>
        """
        link = kwargs.get("link", {})
        reserved_vlan, spine_link = (
            link.get("reserved_vlan", ""), link.get("spine_link", [])
        )
        del_link_template = [
            f"{op} vlans reserved-vlan",
        ]
        set_link_template = [
            f"{op} vlans reserved-vlan {reserved_vlan}",
        ]
        for k in spine_link:
            interface_name = k.get("interface_name", "")
            l3name = "rif-" + interface_name.split('-')[0] + interface_name.split('/')[-1]
            description = k.get("description", "")
            routed_interface_ip_address = k.get("routed_interface_ip_address", "")
            del_link_template.extend([
                f"{op} l3-interface routed-interface {l3name}",
                f"{op} interface gigabit-ethernet {interface_name} routed-interface",
                f"{op} interface gigabit-ethernet {interface_name} mtu",
                f"{op} interface gigabit-ethernet {interface_name} description",
            ])
            set_link_template.extend([
                f"{op} interface gigabit-ethernet {interface_name} routed-interface enable true",
                f"{op} interface gigabit-ethernet {interface_name} routed-interface name {l3name}",
                f"{op} l3-interface routed-interface {l3name} address {routed_interface_ip_address.split('/')[0]} prefix-length {routed_interface_ip_address.split('/')[1]}",
                f"{op} interface gigabit-ethernet {interface_name} mtu 9216",
                f"{op} interface gigabit-ethernet {interface_name} description {description}",
            ])

        link_config = []
        if op == "set":
            link_config = set_link_template
        else:
            link_config = del_link_template

        if role_type:
            ...

        return link_config

    @staticmethod
    def cli_mlag(role_type, op, **kwargs):
        """
        ## MLAG 配置
        #配置 VLAN ID：peerlink vlan；mlag group vlan
        set vlans vlan-id <mlag-vlan-id>
        set vlans <mlag-vlan-id> l3-interface <mlag-l3-interface-name> # "vlan" + id
        set l3-interface vlan-interface <mlag-l3-interface-name> address <mlag-l3-interface-ip-address> prefix-length <number>

        #设置peerlink
        set interface aggregate-ethernet {mlag_peer_lag_interface_name} family ethernet-switching port-mode trunk
        #peerlink的native vlan
        set interface aggregate-ethernet {mlag_peer_lag_interface_name} family ethernet-switching native-vlan-id <mlag-peer-native-vlan-id>
        #mlag group vlan   先不配 待确认
        set interface aggregate-ethernet {mlag_peer_lag_interface_name} family ethernet-switching vlan members <mlag-group-vlan-id>
        #端口加入peerlink汇聚口
        set interface gigabit-ethernet <mlag_interface_name> ether-options 802.3ad {mlag_peer_lag_interface_name}

        ## MLAG域配置
        set protocols mlag domain <domain-id>
        set protocols mlag domain <domain-id> node <domain_id_node>
        # MLAG对等VLAN
        set protocols mlag domain <domain-id> peer-ip <peer-ipv4-address> peer-link {mlag_peer_lag_interface_name}
        set protocols mlag domain <domain-id> peer-ip <peer-ipv4-address> peer-vlan <peer_vlan_id>
        """
        mlag = kwargs.get("mlag", {})
        peer_vlan_id, mlag_l3_interface_ip_address, mlag_peer_lag_interface_name, mlag_interface_name, peer_ipv4_address, domain_id, domain_id_node = (
            mlag.get("peer_vlan_id", ""), mlag.get("mlag_l3_interface_ip_address", ""),
            mlag.get("mlag_peer_lag_interface_name", ""),
            mlag.get("mlag_interface_name", []), mlag.get("peer_ipv4_address", ""), mlag.get("domain_id", ""),
            mlag.get("domain_id_node", "")
        )
        del_mlag_template, set_mlag_template = [], []
        if role_type == "leaf":
            del_mlag_template.extend([
                f"{op} l3-interface vlan-interface vlan{peer_vlan_id}",
                f"{op} vlans vlan-id {peer_vlan_id}",
                f"{op} interface aggregate-ethernet {mlag_peer_lag_interface_name}",
                f"{op} protocols mlag domain {domain_id}"
            ])
            set_mlag_template.extend([
                f"{op} vlans vlan-id {peer_vlan_id}",
                f"{op} vlans vlan-id {peer_vlan_id} l3-interface vlan{peer_vlan_id}",
                f"{op} l3-interface vlan-interface vlan{peer_vlan_id} address {mlag_l3_interface_ip_address.split('/')[0]} prefix-length {mlag_l3_interface_ip_address.split('/')[1]}",
                f"{op} interface aggregate-ethernet {mlag_peer_lag_interface_name} family ethernet-switching port-mode trunk",
                f"{op} interface aggregate-ethernet {mlag_peer_lag_interface_name} family ethernet-switching native-vlan-id {peer_vlan_id}",
                f"{op} interface aggregate-ethernet {mlag_peer_lag_interface_name} family ethernet-switching vlan members {peer_vlan_id}",
                f"{op} protocols mlag domain {domain_id}",
                f"{op} protocols mlag domain {domain_id} node {domain_id_node}",
                f"{op} protocols mlag domain {domain_id} peer-ip {peer_ipv4_address.split('/')[0]} peer-link {mlag_peer_lag_interface_name}",
                f"{op} protocols mlag domain {domain_id} peer-ip {peer_ipv4_address.split('/')[0]} peer-vlan {peer_vlan_id}"
            ])
            for k in mlag_interface_name:
                del_mlag_template.append(f"{op} interface gigabit-ethernet {k} ether-options")
                set_mlag_template.append(
                    f"{op} interface gigabit-ethernet {k} ether-options 802.3ad {mlag_peer_lag_interface_name}")

        if op == "set":
            cli_mlag = set_mlag_template
        else:
            cli_mlag = del_mlag_template

        return cli_mlag

    @staticmethod
    def cli_overlay(role_type, op, **kwargs):
        overlay = kwargs.get("overlay", {})
        overlay_ibgp_asn, ospf_router_id, bgp_router_id, vtep_interface, neighbor_router_id = (
            overlay.get("overlay_ibgp_asn", ""), overlay.get("ospf_router_id", ""), overlay.get("bgp_router_id", ""),
            overlay.get("vtep_interface", ""), overlay.get("neighbor_router_id", [])
        )
        del_overlay_template, set_overlay_template = [], []
        choice = True if "bgp" in kwargs else False
        if not choice:
            del_overlay_template.extend([
                f"{op} protocols bgp router-id",
            ]),
            set_overlay_template.extend([
                f"{op} protocols bgp local-as {overlay_ibgp_asn}",
                f"{op} protocols bgp router-id {ospf_router_id}",
            ])
            for nb in neighbor_router_id:
                del_overlay_template.extend([
                    f"{op} protocols bgp neighbor {nb}",
                ])
                set_overlay_template.extend([
                    f"{op} protocols bgp neighbor {nb} remote-as internal",
                    f"{op} protocols bgp neighbor {nb} update-source {ospf_router_id}",
                    f"{op} protocols bgp neighbor {nb} evpn activate true",
                    f"{op} protocols bgp neighbor {nb} evpn route-reflector-client" if role_type == "spine" else ""
                ])
        if role_type == "leaf":
            del_overlay_template.extend([
                f"{op} vxlans source-interface loopback1",
                f"{op} protocols bgp evpn advertise-all-vni",
                f"{op} protocols bgp evpn advertise-svi-ip"
            ])
            set_overlay_template.extend([
                f"{op} vxlans source-interface loopback1 address {vtep_interface}",
                f"{op} protocols bgp evpn advertise-all-vni",
                f"{op} protocols bgp evpn advertise-svi-ip"
            ])
        del_overlay_template.append(f"commit\n{op} protocols bgp local-as")
        if op == "set":
            cli_overlay = set_overlay_template
        else:
            cli_overlay = del_overlay_template

        return cli_overlay

    @staticmethod
    def adapt_template_data(req_json):
        return req_json

    def update_dc_topology(self, meta_data, config_content):
        fabric_topo_id = meta_data.get("fabric_topo_id", "")
        logic_name = meta_data.get("logic_name", "")

        topo_obj = self.session.query(DCFabricTopologyNode).filter(
            DCFabricTopologyNode.fabric_topo_id == fabric_topo_id,
            DCFabricTopologyNode.logic_name == logic_name)
        if topo_obj:
            topo_obj.update({"node_config": config_content})
        else:
            LOG.error(f"Topology node not found for fabric_topo_id: {fabric_topo_id}, logic_name: {logic_name}")

    def deploy_cli_config(self, role_type, meta_data, **template_data):
        deploy_status = {}
        config_commands = []
        unique_commands = []
        final_config = ""
        try:
            operations = {"old_val": "delete", "new_val": "set"}
            for key, op in operations.items():
                if key in template_data and template_data.get(key):
                    for k, v in template_data.get(key).items():
                        if not v:
                            continue
                        if k == "hostname":
                            config_commands.extend(self.common_config(op, **template_data.get(key)))
                        else:
                            func = getattr(self, "cli_%s" % k.lower())
                            config_commands.extend(
                                func(role_type, op, **self.adapt_template_data(template_data.get(key))))
                    deploy_config = "\n".join(list(reversed([cmd for cmd in reversed(config_commands) if
                                                             cmd == "commit" or cmd not in unique_commands and not unique_commands.append(
                                                                 cmd) and cmd]))) + "\ncommit"
                    final_config += deploy_config + "\n"
            result, status = interactive_shell_configure(cmd=final_config, hostname=self.host, username=self.username,
                                                         password=self.password, port=self.port)
            if status != C.RMA_ACTIVE:
                raise ValueError(f"Configuration failed on host {self.host}: {result}")
            self.update_dc_topology(meta_data, template_data.get("new_val", {}))
        except ValueError as v:
            LOG.error(f"{str(v)}")
            deploy_status["status"] = 400
            deploy_status["msg"] = str(v)
        except Exception as e:
            LOG.error(f"Error while deploying CLI config: {str(e)}")
            deploy_status["status"] = 500
            deploy_status["msg"] = str(traceback.format_exc())
        else:
            deploy_status["status"] = 200
            deploy_status["msg"] = "Configuration and commit successful."
        finally:
            return deploy_status

    @staticmethod
    def cli_dlb(op, **kwargs):
        dlb = kwargs.get("dlb", {})
        dlb_mode = dlb.get("dlb_mode", "")
        dlb_config = []
        if dlb_mode:
            dlb_config = [f"{op} interface ecmp hash-mapping {dlb_mode}"]
        return dlb_config

    @staticmethod
    def cli_roce_configuring(op, **kwargs):
        roce_configuring = kwargs.get("roce_configuring", {})
        roce_mode, roce_ports, queues = roce_configuring.get("roce_mode", ""), roce_configuring.get("roce_ports", []), roce_configuring.get("queues", [])
        set_roce_config = []
        del_roce_config = []
        if roce_mode:
            set_roce_config.append(f"set class-of-service roce mode {roce_mode}")
            del_roce_config.append("delete class-of-service roce")
        if "all_ports" in roce_ports:
            set_roce_config.append(f"set class-of-service roce apply all")
        else:
            for port in roce_ports:
                set_roce_config.append(f"set class-of-service roce apply interface {port}")
        for queue in queues:
            set_roce_config.append(f"set class-of-service roce queue {queue}")
        return set_roce_config if op == "set" else del_roce_config

    @staticmethod
    def cli_pfc(op, **kwargs):
        pfc = kwargs.get("pfc", {})
        pfc_profile_name, pfc_ports, code_points, drop_enable = (
            pfc.get("pfc_profile_name", ""), pfc.get("ports", []), pfc.get("code_points", []), pfc.get("drop_enable", "false"))
        set_pfc_config = []
        del_pfc_config = []
        if pfc_profile_name:
            set_pfc_config.append(f"set class-of-service pfc-profile {pfc_profile_name}")
            del_pfc_config.append(f"delete class-of-service pfc-profile {pfc_profile_name}")
        for code_point in code_points:
            set_pfc_config.append(f"set class-of-service pfc-profile {pfc_profile_name} code-point {code_point} drop {drop_enable}")
        for port in pfc_ports:
            set_pfc_config.append(f"set class-of-service interface {port} pfc-profile {pfc_profile_name}")
            del_pfc_config.append(f"delete class-of-service interface {port} pfc-profile")
        return set_pfc_config if op == "set" else del_pfc_config

    @staticmethod
    def cli_pfc_buffer(op, **kwargs):
        pfc_buffer = kwargs.get("pfc_buffer", {})
        ingress, egress = pfc_buffer.get("ingress", []), pfc_buffer.get("egress", [])
        set_pfc_buffer_config = []
        del_pfc_buffer_config = []
        for item in ingress:
            ports = item.get("port", [])
            queues = item.get("queue", [])
            ingress_params = item.get("ingress_params", {})
            for port in ports:
                for queue in queues:
                    for k, v in ingress_params.items():
                        if k and v:
                            key = k.replace("_", "-")
                            set_pfc_buffer_config.append(f"set interface gigabit-ethernet {port} ethernet-switching-options buffer ingress-queue {queue} {key} {v}")
                    del_pfc_buffer_config.append(f"delete interface gigabit-ethernet {port} ethernet-switching-options buffer ingress-queue {queue}")
        for item in egress:
            ports = item.get("port", [])
            queues = item.get("queue", [])
            engress_params = item.get("egress_params", {})
            for port in ports:
                for queue in queues:
                    for k, v in engress_params.items():
                        if k and v:
                            key = k.replace("_", "-")
                            set_pfc_buffer_config.append(f"set interface gigabit-ethernet {port} ethernet-switching-options buffer egress-queue {queue} {key} {v}")
                    del_pfc_buffer_config.append(f"delete interface gigabit-ethernet {port} ethernet-switching-options buffer egress-queue {queue}")
        return set_pfc_buffer_config if op == "set" else del_pfc_buffer_config

    @staticmethod
    def cli_pfc_watchdog(op, **kwargs):
        pfc_watchdog = kwargs.get("pfc_watchdog", [])
        set_pfc_watchdog_config = []
        del_pfc_watchdog_config = []
        for wd in pfc_watchdog:
            ports, code_points, enable, watchdog_params, restore_mode = (
                wd.get("port", []), wd.get("code_point", []), wd.get("enable", ""), wd.get("watchdog_params", {}), wd.get("restore_mode", "auto"))
            is_delete_action, is_delete_granularity, is_delete_period, is_delete_count = (wd.get("is_delete_action", False), wd.get("is_delete_granularity", False),
                                                                                          wd.get("is_delete_period", False), wd.get("is_delete_count", False))
            code_point_params = ["detect-interval", "restore-interval"]
            for port in ports:
                for code_point in code_points:
                    set_pfc_watchdog_config.append(f"set class-of-service interface {port} pfc-watchdog code-point {code_point} enable {enable}")
                    del_pfc_watchdog_config.append(f"delete class-of-service interface {port} pfc-watchdog code-point {code_point}")
                if restore_mode:
                    set_pfc_watchdog_config.append(f"set class-of-service interface {port} pfc-watchdog restore-mode {restore_mode}")
                    if restore_mode != "auto":
                        del_pfc_watchdog_config.append(f"delete class-of-service interface {port} pfc-watchdog restore-mode")
            for k, v in watchdog_params.items():
                if k and v:
                    key = k.replace("_", "-")
                    if key not in code_point_params and "threshold" not in k:
                        set_pfc_watchdog_config.append(f"set class-of-service pfc-watchdog {key} {v}")
                    elif "threshold" in k:
                        key = k.split("_")[1]
                        set_pfc_watchdog_config.append(f"set class-of-service pfc-watchdog threshold {key} {v}")
                    else:
                        for code_point in code_points:
                            set_pfc_watchdog_config.append(f"set class-of-service pfc-watchdog code-point {code_point} {key} {v}")
                            del_pfc_watchdog_config.append(f"delete class-of-service pfc-watchdog code-point {code_point}")
            if is_delete_action:
                del_pfc_watchdog_config.append(f"delete class-of-service pfc-watchdog restore-action")
            if is_delete_granularity:
                del_pfc_watchdog_config.append(f"delete class-of-service pfc-watchdog granularity")
            if is_delete_period:
                del_pfc_watchdog_config.append(f"delete class-of-service pfc-watchdog threshold period")
            if is_delete_count:
                del_pfc_watchdog_config.append(f"delete class-of-service pfc-watchdog threshold count")
        return set_pfc_watchdog_config if op == "set" else del_pfc_watchdog_config

    @staticmethod
    def cli_ecn(op, **kwargs):
        ecn = kwargs.get("ecn", {})
        mode, ports, queues, enable, ecn_params = ecn.get("mode", ""), ecn.get("port", []), ecn.get("queue", []), ecn.get("enable", ""), ecn.get("ecn_params", {})
        set_ecn_config = []
        del_ecn_config = []
        if mode:
            set_ecn_config.append(f"set class-of-service easy-ecn mode {mode}")
            del_ecn_config.append(f"delete class-of-service easy-ecn")
        for queue in queues:
            for port in ports:
                set_ecn_config.append(f"set interface gigabit-ethernet {port} wred queue {queue} enable {enable}")
                del_ecn_config.append(f"delete interface gigabit-ethernet {port} wred queue {queue}")
                for k, v in ecn_params.items():
                    if k and v:
                        set_ecn_config.append(f"set interface gigabit-ethernet {port} wred queue {queue} {k} {v}")
        return set_ecn_config if op == "set" else del_ecn_config

    @staticmethod
    def cli_service_scheduler(op, **kwargs):
        service_scheduler = kwargs.get("service_scheduler", {})
        configurations = service_scheduler.get("configuration", [])
        ingress = service_scheduler.get("ingress", [])
        egress = service_scheduler.get("egress", [])
        set_service_scheduler_config = []
        del_service_scheduler_config = []
        for configuration in configurations:
            forwarding_class_name, local_priority, scheduler_name, mode, scheduler_params = (
                configuration.get("forwarding_class_name", ""), configuration.get("local_priority", ""),
                configuration.get("scheduler_name", ""), configuration.get("mode", ""), configuration.get("scheduler_params", {}))
            if forwarding_class_name and scheduler_name:
                set_service_scheduler_config.extend([
                    f"set class-of-service forwarding-class {forwarding_class_name} local-priority {local_priority}",
                    f"set class-of-service scheduler {scheduler_name} mode {mode}"
                ])
                del_service_scheduler_config.extend([
                    f"delete class-of-service scheduler {scheduler_name}",
                    f"delete class-of-service forwarding-class {forwarding_class_name}",
                ])
            for k, v in scheduler_params.items():
                if k and v:
                    key = k.replace("_", "-")
                    set_service_scheduler_config.append(f"set class-of-service scheduler {scheduler_name} {key} {v}")
        for item in ingress:
            classifier_name, trust_mode, ports, forwarding_class_name, code_points = (
                item.get("classifier_name", ""), item.get("trust_mode", ""), item.get("ports", []),
                item.get("forwarding_class_name", ""), item.get("code_points", []))
            set_service_scheduler_config.append(f"set class-of-service classifier {classifier_name} trust-mode {trust_mode}")
            del_service_scheduler_config.append(f"delete class-of-service classifier {classifier_name}")
            for code_point in code_points:
                set_service_scheduler_config.append(f"set class-of-service classifier {classifier_name} forwarding-class {forwarding_class_name} code-point {code_point}")
            for port in ports:
                set_service_scheduler_config.append(f"set class-of-service interface {port} classifier {classifier_name}")
                del_service_scheduler_config.append(f"delete class-of-service interface {port} classifier")
        for item in egress:
            profile_name, scheduler_name, ports, forwarding_class_name = (
                item.get("scheduler_profile_name", ""), item.get("scheduler_name", ""), item.get("ports", []), item.get("forwarding_class_name", ""))
            set_service_scheduler_config.append(f"set class-of-service scheduler-profile {profile_name} forwarding-class {forwarding_class_name} scheduler {scheduler_name}")
            del_service_scheduler_config.append(f"delete class-of-service scheduler-profile {profile_name}")
            for port in ports:
                set_service_scheduler_config.append(f"set class-of-service interface {port} scheduler-profile {profile_name}")
                del_service_scheduler_config.append(f"delete class-of-service interface {port} scheduler-profile")
        return set_service_scheduler_config if op == "set" else del_service_scheduler_config

    def build_cli_roce_config_commands(self, type, **template_data):
        config_commands = []
        unique_commands = set()
        operations = {"old_val": "delete", "new_val": "set"}
        for key, op in operations.items():
            if key not in template_data or not template_data.get(key):
                continue
            for k, v in template_data.get(key).items():
                if not v:
                    continue
                func = getattr(self, "cli_%s" % k.lower())
                cmds = func(op, **template_data.get(key))
                for cmd in cmds:
                    if cmd not in unique_commands or cmd == "commit":
                        config_commands.append(cmd)
                        unique_commands.add(cmd)
            if type == "apply":
                config_commands.append("commit")
        return config_commands

    def deploy_cli_roce_config(self, **template_data):
        deploy_status = {}
        try:
            config_commands = self.build_cli_roce_config_commands("apply", **template_data)
            deploy_config = "\n".join(config_commands) + "\n"
            result, status = interactive_shell_configure(cmd=deploy_config, hostname=self.host,
                                                         username=self.username, password=self.password,port=self.port)
            if status != C.RMA_ACTIVE:
                raise ValueError(f"Configuration failed on host {self.host}: {result}")
        except ValueError as v:
            LOG.error(f"{str(v)}")
            deploy_status["status"] = 400
            deploy_status["msg"] = str(v)
        except Exception as e:
            LOG.error(f"Error while deploying CLI config: {str(e)}")
            deploy_status["status"] = 500
            deploy_status["msg"] = str(traceback.format_exc())
        else:
            deploy_status["status"] = 200
            deploy_status["msg"] = "Configuration and commit successful."
        finally:
            return deploy_status

class NetConfConfigDeployer(ConfigDeployer):
    """
    for AmpCon-Campus
    """

    def __init__(self, host, username, password, port=22, session=None):
        super().__init__(host, username, password, port, session)

    """
    for Ampcon-DC NetCONF
    """

    @staticmethod
    def dc_common_config(op, **kwargs):
        operation = "" if op == "" else f'operation="{op}"'
        config_dict = {"ip_xml": [], "evpn_xml": [], "lldp_xml": [], "system_xml": [], "spanning-tree_xml": []}
        hostname = kwargs.get("hostname")
        config_dict["ip_xml"].append(f"""
            <routing>
                <enable {operation}>true</enable>
            </routing>
        """)
        # config_dict["lldp_xml"].append(f"""
        #     <enable {operation}>true</enable>
        # """)
        config_dict["evpn_xml"].append(f"""
            <enable {operation}>true</enable>
        """)
        if hostname:
            config_dict["system_xml"].append(f"""
                <hostname {operation}>{hostname}</hostname>
            """)
        config_dict["spanning-tree_xml"].append(f"""
            <enable {operation}>false</enable>
        """)

        return config_dict

    @staticmethod
    def dc_bgp(role_type, op, **kwargs):
        bgp = kwargs.get("bgp", {})
        asn, bgp_router_id, vtep_interface, spine_link, peer_link = (
            bgp.get("asn", ""), bgp.get("bgp_router_id", ""), bgp.get("vtep_interface", ""), bgp.get("spine_link", []),
            bgp.get("peer_link", {})
        )
        operation = "" if op == "" else f'operation="{op}"'
        config_dict = {"bgp_xml": [], "l3-interface_xml": []}
        if bgp:
            ipv4_unicast = ""
            config_dict["l3-interface_xml"].append(f"""
                <loopback {operation}>
                    <name>loopback0</name>
                    <address>
                        <ip>{bgp_router_id}</ip>
                        <prefix-length>32</prefix-length>
                    </address>
                </loopback>
            """)
            if role_type == "leaf":
                config_dict["l3-interface_xml"].append(f"""
                    <loopback {operation}>
                        <name>loopback1</name>
                        <address>
                            <ip>{vtep_interface}</ip>
                            <prefix-length>32</prefix-length>
                        </address>
                    </loopback>
                """)
                ipv4_unicast = f"""
                    <network>
                        <net>{bgp_router_id}/32</net>
                    </network>
                """
            if op == "":
                config_dict["bgp_xml"].append(f"""
                    <local-as>{asn}</local-as>
                """)
            for link_interface in spine_link:
                routed_interface_target_address = link_interface.get("routed_interface_target_address", "").split('/')[0]
                routed_interface_network_address = link_interface.get("routed_interface_network_address", "")
                config_dict["bgp_xml"].append(f"""
                    <neighbor {operation}>
                        <ip>{routed_interface_target_address}</ip>
                        <remote-as>external</remote-as>
                        <ipv4-unicast>
                            <activate>true</activate>
                        </ipv4-unicast>
                    </neighbor>
                """)
                ipv4_unicast += f"""
                    <network>
                        <net>{routed_interface_network_address}</net>
                    </network>
                """
            if role_type == "leaf" and peer_link:
                peer_ipv4_address = peer_link.get("peer_ipv4_address", "").split('/')[0]
                mlag_l3_interface_ip_address = peer_link.get("mlag_l3_interface_ip_address", "").split('/')[0]
                config_dict["bgp_xml"].append(f"""
                    <neighbor {operation}>
                        <ip>{peer_ipv4_address}</ip>
                        <remote-as>internal</remote-as>
                        <update-source>{mlag_l3_interface_ip_address}</update-source>
                        <evpn>
                            <activate>true</activate>
                        </evpn>
                    </neighbor>
                """)
            config_dict["bgp_xml"].append(f"""
                <ebgp-requires-policy {operation}>false</ebgp-requires-policy>
                <router-id {operation}>{bgp_router_id}</router-id>
                <ipv4-unicast {operation}>
                    {ipv4_unicast}
                </ipv4-unicast>
            """)

        return config_dict

    @staticmethod
    def dc_ospf(role_type, op, **kwargs):
        operation = "" if op == "" else f'operation="{op}"'
        config_dict = {"ospf_xml": [], "l3-interface_xml": []}
        ospf = kwargs.get("ospf", {})
        vtep_interface, ospf_router_id, area_id, spine_link, peer_link = (
            ospf.get("vtep_interface", ""), ospf.get("ospf_router_id", ""), ospf.get("area_id", ""),
            ospf.get("spine_link", []), ospf.get("peer_link", {})
        )
        if ospf:
            config_dict["l3-interface_xml"].append(f"""
                <loopback {operation}>
                    <name>loopback0</name>
                    <address>
                        <ip>{ospf_router_id}</ip>
                        <prefix-length>32</prefix-length>
                    </address>
                </loopback>
            """)
            if role_type == "leaf":
                config_dict["l3-interface_xml"].append(f"""
                    <loopback {operation}>
                        <name>loopback1</name>
                        <address>
                            <ip>{vtep_interface}</ip>
                            <prefix-length>32</prefix-length>
                        </address>
                    </loopback>
                """)
                config_dict["ospf_xml"].append(f"""
                    <network {operation}>
                        <net>{vtep_interface}/32</net>
                        <area>{area_id}</area> 
                    </network>
                """)
            config_dict["ospf_xml"].append(f"""
                <router-id {operation}>{ospf_router_id}</router-id>
                <area {operation}>
                    <ip>1</ip>
                </area>
                <network {operation}>
                    <net>{ospf_router_id}/32</net>
                    <area>{area_id}</area>
                </network> 
            """)
            for link_interface in spine_link:
                interface_name = link_interface.get("interface_name", "")
                l3name = "rif-" + interface_name.split('-')[0] + interface_name.split('/')[-1]
                routed_interface_network_address = link_interface.get("routed_interface_network_address", "")
                config_dict["ospf_xml"].append(f"""
                    <interface {operation}>
                        <name>{l3name}</name>
                        <network>point-to-point</network>
                    </interface>
                    <network {operation}>
                        <net>{routed_interface_network_address}/32</net>
                        <area>{area_id}</area>
                    </network>
                """)
            if role_type == "leaf" and peer_link:
                mlag_l3_interface_network_address = peer_link.get("mlag_l3_interface_network_address", "")
                config_dict["ospf_xml"].append(f"""
                    <network {operation}>
                        <net>{mlag_l3_interface_network_address}</net>
                        <area>{area_id}</area>
                    </network>
                """)

        return config_dict

    @staticmethod
    def dc_link(role_type, op, **kwargs):
        operation = "" if op == "" else f'operation="{op}"'
        config_dict = {"vlans_xml": [], "l3-interface_xml": [], "interface_xml": []}
        link = kwargs.get("link", {})
        reserved_vlan, spine_link = (
            link.get("reserved_vlan", ""), link.get("spine_link", [])
        )
        if link:
            config_dict["vlans_xml"].append(f"""
                <reserved-vlan {operation}>{reserved_vlan}</reserved-vlan>
            """)
            for link_interface in spine_link:
                interface_name = link_interface.get("interface_name", "")
                l3name = "rif-" + interface_name.split('-')[0] + interface_name.split('/')[-1]
                description = link_interface.get("description", "")
                routed_interface_ip_address = link_interface.get("routed_interface_ip_address", "")
                config_dict["interface_xml"].append(f"""
                    <gigabit-ethernet>
                        <name>{interface_name}</name>
                        <mtu {operation}>9216</mtu>
                        <description {operation}>{description}</description>
                        <routed-interface {operation}>
                            <name>{l3name}</name>
                            <enable>true</enable>
                        </routed-interface>
                    </gigabit-ethernet>
                """)
                config_dict["l3-interface_xml"].append(f"""
                    <routed-interface {operation}>
                        <name>{l3name}</name>
                        <address>
                            <ip>{routed_interface_ip_address.split("/")[0]}</ip>
                            <prefix-length>{routed_interface_ip_address.split("/")[1]}</prefix-length>
                        </address>
                    </routed-interface>
                """)
        return config_dict

    @staticmethod
    def dc_mlag(role_type, op, **kwargs):
        operation = "" if op == "" else f'operation="{op}"'
        config_dict = {"vlans_xml": [], "mlag_xml": [], "l3-interface_xml": [], "interface_xml": []}
        mlag = kwargs.get("mlag", {})
        peer_vlan_id, mlag_l3_interface_ip_address, mlag_peer_lag_interface_name, mlag_interface_name, peer_ipv4_address, domain_id, domain_id_node = (
            mlag.get("peer_vlan_id", ""), mlag.get("mlag_l3_interface_ip_address", ""),
            mlag.get("mlag_peer_lag_interface_name", ""),
            mlag.get("mlag_interface_name", []), mlag.get("peer_ipv4_address", ""), mlag.get("domain_id", ""),
            mlag.get("domain_id_node", "")
        )
        if mlag and role_type == "leaf":
            config_dict["vlans_xml"].append(f"""
                <vlan-id {operation}>
                    <id>{peer_vlan_id}</id>
                    <l3-interface>vlan{peer_vlan_id}</l3-interface>
                </vlan-id>
            """)
            config_dict["l3-interface_xml"].append(f"""
                <vlan-interface {operation}>
                    <name>vlan{peer_vlan_id}</name>
                    <address>
                        <ip>{mlag_l3_interface_ip_address.split('/')[0]}</ip>
                        <prefix-length>{mlag_l3_interface_ip_address.split('/')[1]}</prefix-length>
                    </address>
                </vlan-interface>
            """)
            config_dict["interface_xml"].append(f"""
                <aggregate-ethernet {operation}>
                    <name>{mlag_peer_lag_interface_name}</name>
                    <family>
                        <ethernet-switching>
                            <native-vlan-id>{peer_vlan_id}</native-vlan-id>
                            <port-mode>trunk</port-mode>
                            <vlan>
                                <members><id>{peer_vlan_id}</id></members>
                            </vlan>
                        </ethernet-switching>
                    </family>
                </aggregate-ethernet>
            """)
            config_dict["mlag_xml"].append(f"""
                <domain {operation}>
                    <id>{domain_id}</id>
                    <node>{domain_id_node}</node>
                    <peer-ip>
                        <ip>{peer_ipv4_address.split('/')[0]}</ip>
                        <peer-link>{mlag_peer_lag_interface_name}</peer-link>
                        <peer-vlan>{peer_vlan_id}</peer-vlan>
                    </peer-ip>
                </domain>
            """)
            for interface in mlag_interface_name:
                config_dict["interface_xml"].append(f"""
                        <gigabit-ethernet>
                            <name>{interface}</name>
                            <ether-options {operation}>
                                <_802.3ad>{mlag_peer_lag_interface_name}</_802.3ad>
                            </ether-options>
                        </gigabit-ethernet>
                    """)
        return config_dict

    @staticmethod
    def dc_overlay(role_type, op, **kwargs):
        operation = "" if op == "" else f'operation="{op}"'
        config_dict = {"bgp_xml": [], "vxlans_xml": [], "bgp_evpn_xml": []}
        overlay = kwargs.get("overlay", {})
        overlay_ibgp_asn, ospf_router_id, bgp_router_id, vtep_interface, neighbor_router_id = (
            overlay.get("overlay_ibgp_asn", ""), overlay.get("ospf_router_id", ""), overlay.get("bgp_router_id", ""),
            overlay.get("vtep_interface", ""), overlay.get("neighbor_router_id", [])
        )
        choice = True if "bgp" in kwargs else False
        if not choice:
            if op == "":
                config_dict["bgp_xml"].append(f"""
                    <local-as>{overlay_ibgp_asn}</local-as>
                """)
            config_dict["bgp_xml"].append(f"""
                <router-id {operation}>{bgp_router_id}</router-id>
            """)
            for neighbor in neighbor_router_id:
                config_dict["bgp_xml"].append(f"""
                    <neighbor {operation}>
                        <ip>{neighbor}</ip>
                        <remote-as>internal</remote-as>
                        <update-source>{ospf_router_id}</update-source>
                        <evpn>
                            <activate>true</activate>
                        </evpn>
                    </neighbor>
                """)
                config_dict["bgp_evpn_xml"].append(f"""
                    <neighbor {operation}>
                        <ip>{neighbor}</ip>
                        <evpn>
                            <activate>true</activate>
                            <route-reflector-client/>
                        </evpn>
                    </neighbor>
                """)
        if role_type == "leaf":
            config_dict["vxlans_xml"].append(f"""
                <source-interface {operation}>
                    <name>loopback1</name>
                    <address>{vtep_interface}</address>
                </source-interface>
            """)
            config_dict["bgp_evpn_xml"].append(f"""
                <evpn {operation}>
                    <advertise-svi-ip/>
                </evpn>
            """)
            config_dict["bgp_xml"].append(f"""
                <evpn {operation}>
                    <advertise-all-vni/>
                </evpn>
            """)
        return config_dict

    @staticmethod
    def dc_nic_uplink(op, **kwargs):
        operation = "" if op == "" else f'operation="{op}"'
        config_dict = {"interface_xml": [], "mlag_xml": []}
        access_port_list, trunk_port_list, lag_list = kwargs.get("access_port_list", []), kwargs.get("trunk_port_list", []), kwargs.get("lag_list", [])
        for access_port in access_port_list:
            config_dict["interface_xml"].append(f"""
                <gigabit-ethernet>
                    <name>{access_port}</name>
                    <family {operation}>
                        <ethernet-switching>
                            <port-mode>access</port-mode>
                        </ethernet-switching>
                    </family>
                </gigabit-ethernet>
            """)
        for trunk_port in trunk_port_list:
            config_dict["interface_xml"].append(f"""
                <gigabit-ethernet>
                    <name>{trunk_port}</name>
                    <family {operation}>
                        <ethernet-switching>
                            <port-mode>trunk</port-mode>
                        </ethernet-switching>
                    </family>
                </gigabit-ethernet>
            """)
        for lag in lag_list:
            config_dict["interface_xml"].append(f"""
                <aggregate-ethernet {operation}>
                    <name>{lag["lag_name"]}</name>
                    <aggregated-ether-options>
                        <lacp>
                            <enable>true</enable>
                        </lacp>
                    </aggregated-ether-options>
                    <family>
                        <ethernet-switching>
                            <port-mode>{lag["port_mode"]}</port-mode>
                        </ethernet-switching>
                    </family>
                </aggregate-ethernet>
            """)
            for port in lag.get("lag_port_list", []):
                config_dict["interface_xml"].append(f"""
                    <gigabit-ethernet>
                        <name>{port}</name>
                        <ether-options {operation}>
                            <_802.3ad>{lag["lag_name"]}</_802.3ad>
                        </ether-options>
                    </gigabit-ethernet>
                """)
            if lag.get("domain_id", ""):
                config_dict["mlag_xml"].append(f"""
                    <domain>
                        <id>{lag["domain_id"]}</id>
                        <interface {operation}>
                            <name>{lag["lag_name"]}</name>
                            <link>{lag["link_id"]}</link>
                        </interface>
                    </domain>
                """)
        return config_dict

    @staticmethod
    def dc_logical_router(op, **kwargs):
        operation = "" if op == "" else f'operation="{op}"'
        logical_router = kwargs.get("logical_router", {})
        config_dict = {"ip_xml": [], "vxlans_xml": [], "vlans_xml": [], "l3-interface_xml": [], "bgp_xml": []}
        vrf_name, l3vni, l3vlan, l3anycast_mac, asn = (logical_router.get("vrf_name", ""), logical_router.get("l3vni", ""),
                                                       logical_router.get("l3vlan", ""), logical_router.get("l3anycast_mac", ""),
                                                       logical_router.get("asn", ""))
        if logical_router:
            config_dict["ip_xml"].append(f"""
                <vrf {operation}>
                    <name>{vrf_name}</name>
                </vrf>
            """)
            config_dict["vxlans_xml"].append(f"""
                <vni {operation}>
                    <id>{l3vni}</id>
                    <vlan>{l3vlan}</vlan>
                </vni>
                <vrf {operation}>
                    <name>{vrf_name}</name>
                    <l3-vni>
                        <vni>{l3vni}</vni>
                    </l3-vni>
                </vrf>
            """)
            config_dict["vlans_xml"].append(f"""
                <vlan-id {operation}>
                    <id>{l3vlan}</id>
                    <l3-interface>vlan{l3vlan}</l3-interface>
                </vlan-id>
            """)
            config_dict["l3-interface_xml"].append(f"""
                <vlan-interface {operation}>
                    <name>vlan{l3vlan}</name>
                    <vrf>{vrf_name}</vrf>
                    <anycast>
                        <mac>{l3anycast_mac}</mac>
                    </anycast>
                </vlan-interface>
            """)
            config_dict["bgp_xml"].append(f"""
                <vrf {operation}>
                    <name>{vrf_name}</name>
                    <local-as>{asn}</local-as>
                    <evpn>
                        <advertise>
                            <ipv4-unicast/>
                        </advertise>
                    </evpn>
                </vrf>
            """)

        ## vxlan相关配置单独删除
        if op == "delete":
            config_dict.pop("vxlans_xml")

        return config_dict

    @staticmethod
    def dc_logical_router_delete_vxlans(op, **kwargs):
        operation = "" if op == "" else f'operation="{op}"'
        logical_router = kwargs.get("logical_router", {})
        config_dict = {"vxlans_xml": [], }
        vrf_name, l3vni, l3vlan, l3anycast_mac, asn = (logical_router.get("vrf_name", ""), logical_router.get("l3vni", ""),
                                                       logical_router.get("l3vlan", ""), logical_router.get("l3anycast_mac", ""),
                                                       logical_router.get("asn", ""))
        if logical_router:
            config_dict["vxlans_xml"].append(f"""
                <vni {operation}>
                    <id>{l3vni}</id>
                    <vlan>{l3vlan}</vlan>
                </vni>
                <vrf {operation}>
                    <name>{vrf_name}</name>
                    <l3-vni>
                        <vni>{l3vni}</vni>
                    </l3-vni>
                </vrf>
            """)
            return config_dict
        else:
            return {}

    @staticmethod
    def dc_router_to_switch(op, **kwargs):
        operation = "" if op == "" else f'operation="{op}"'
        router_to_switch = kwargs.get("router_to_switch", {})
        l2vlan, vrf_name, anycast_ip, virtual_ip, anycast_mac = (
            router_to_switch.get("l2vlan", ""), router_to_switch.get("vrf_name", ""), router_to_switch.get("anycast_ip", ""),
            router_to_switch.get("virtual_ip", ""), router_to_switch.get("anycast_mac", ""))
        config_dict = {"l3-interface_xml": [], "bgp_xml": [], "vlans_xml": []}
        if router_to_switch:
            config_dict["l3-interface_xml"].append(f"""
                <vlan-interface>
                    <name>vlan{l2vlan}</name>
                    <vrf {operation}>{vrf_name}</vrf>
                    <address {operation}>
                        <ip>{virtual_ip.split("/")[0]}</ip>
                        <prefix-length>{virtual_ip.split("/")[1]}</prefix-length>
                    </address>
                    <anycast {operation}>
                        <address>
                            <ip>{anycast_ip.split('/')[0]}</ip>
                            <prefix-length>{anycast_ip.split('/')[1]}</prefix-length>
                        </address>
                        <mac>{anycast_mac}</mac>
                    </anycast>
                </vlan-interface>
            """)
            config_dict["bgp_xml"].append(f"""
                <vrf>
                    <name>{vrf_name}</name>
                    <ipv4-unicast>
                        <network {operation}>
                            <net>{ipaddress.ip_interface(anycast_ip)}</net>
                        </network>
                    </ipv4-unicast>
                </vrf>
            """)
            # config_dict["vlans_xml"].append(f"""
            #     <vlan-id {operation}>
            #         <id>{l2vlan}</id>
            #         <l3-interface>vlan{l2vlan}</l3-interface>
            #     </vlan-id>
            # """)

        ## lr-ls的bgp vrf配置 会在lr配置是删除 所以不需要再删除
        ## 默认lr配置不会变更 所以lr-ls的bgp vrf配置还是在lr-ls配置中删除
        # if op == "delete":
        #     config_dict.pop("bgp_xml")

        return config_dict

    @staticmethod
    def dc_logical_switch(op, **kwargs):
        operation = "" if op == "" else f'operation="{op}"'
        logical_switch = kwargs.get("logical_switch", {})
        config_dict = {"vxlans_xml": [], "vlans_xml": [], "interface_xml": [], "l3-interface_xml": []}
        l2vni, l2vlan, arp_nd_suppress, interface, lag = (
            logical_switch.get("l2vni", ""), logical_switch.get("l2vlan", ""), logical_switch.get("arp_nd_suppress", ""),
            logical_switch.get("interface", []), logical_switch.get("lag", []))
        if logical_switch:
            config_dict["vxlans_xml"].append(f"""
                <vni {operation}>
                    <id>{l2vni}</id>
                    <vlan>{l2vlan}</vlan>
                    <arp-nd-suppress>
                        <disable>{str(arp_nd_suppress).lower()}</disable>
                    </arp-nd-suppress>
                </vni>
            """)
            config_dict["vlans_xml"].append(f"""
                <vlan-id {operation}>
                    <id>{l2vlan}</id>
                    <l3-interface>vlan{l2vlan}</l3-interface>
                </vlan-id>
            """)
            config_dict["l3-interface_xml"].append(f"""
                <vlan-interface>
                    <name {operation}>vlan{l2vlan}</name>
                </vlan-interface>
            """)

            for interface_item in interface:
                interface_mode = interface_item['connect_mode']
                interface_list = interface_item['interface']
                ehternet_switching_xml = (f"""
                                            <vlan>
                                                <members {operation}>
                                                    <id>{l2vlan}</id>
                                                </members>
                                            </vlan> 
                                          """ if interface_mode == "trunk"
                                          else f"<native-vlan-id {operation}>{l2vlan}</native-vlan-id>")
                for interface_name in interface_list:
                    config_dict["interface_xml"].append(f"""
                        <gigabit-ethernet>
                            <name>{interface_name}</name>
                            <family>
                                <ethernet-switching>
                                    {ehternet_switching_xml}
                                </ethernet-switching>
                            </family>
                        </gigabit-ethernet>
                    """)
            for lag_item in lag:
                lag_mode = lag_item["connect_mode"]
                lag_name = lag_item["lag"]
                ethernet_switching_xml = (f"""
                                            <vlan>
                                                <members {operation}>
                                                    <id>{l2vlan}</id>
                                                </members>
                                            </vlan>
                                           """ if lag_mode == "trunk"
                                          else f"<native-vlan-id {operation}>{l2vlan}</native-vlan-id>")
                config_dict["interface_xml"].append(f"""
                    <aggregate-ethernet>
                        <name>{lag_name}</name>
                        <family>
                            <ethernet-switching>
                                {ethernet_switching_xml}
                            </ethernet-switching>
                        </family>
                    </aggregate-ethernet>
                """)
        return config_dict

    """
    for Ampcon-Campus
    """

    @staticmethod
    def core_common_config(op, protocol, **kwargs):
        operation = "" if op == "" else f'operation="{op}"'
        inband = kwargs.get("inband", {})
        inband_vlan_id, inband_vlan_address = inband.get("vlan_id", 3965), inband.get("ip_address", "")
        mlag = kwargs.get("mlag", {})
        mlag_peer_vlan_id, mlag_l3_interface_ip_address, mlag_peer_lag_interface_name, mlag_interface_name, peer_ipv4_address, domain_id, domain_id_node, vrrp_ip_address = (
            mlag.get("mlag_peer_vlan_id", ""), mlag.get("mlag_peer_l3_interface_ip", ""),
            mlag.get("mlag_peer_lag_interface_name", ""), mlag.get("mlag_peer_interface_name", []),
            mlag.get("peer_ipv4_address", ""), mlag.get("domain_id", ""), mlag.get("domain_id_node", ""),
            mlag.get("vrrp_ip_address", "")
        )
        network_vlan = kwargs.get("vlans", {})
        wan = kwargs.get("wan", {})
        dhcp_relay = kwargs.get("dhcp_relay", {})
        config_dict = {"vlans_xml": [], "l3-interface_xml": [], "interface_xml": [], "mlag_xml": [], "vrrp_xml": [],
                       "dhcp_xml": [], "system_xml": [], "ip_xml": [], "ospf_xml": [], "static_xml": []}
        network_vlans_id = []
        mlag_access_interface_xml_list = []
        config_dict["ip_xml"].append(f"""
            <routing>
                <enable {operation}>true</enable>
            </routing>
        """)
        for vlan in network_vlan:
            vlan_id = network_vlan[vlan]["vlan_id"]
            network_vlans_id.append(vlan_id)
            vlan_address = network_vlan[vlan]["ip_address"]
            vlan_subnet = network_vlan[vlan]["subnet"].split("/")[1]
            vrid = network_vlan[vlan]["vrid"]
            vrrp_ip = network_vlan[vlan]["vrrp_ip"]
            # network vlan
            config_dict["vlans_xml"].append(f"""
                <vlan-id {operation}>
                    <id>{vlan_id}</id>
                    <l3-interface>vlan{vlan_id}</l3-interface>
                </vlan-id>
            """)

            if protocol == "ospf":
                config_dict["ospf_xml"].append(f"""
                    <network {operation}>
                        <net>{network_vlan[vlan]["subnet"]}</net>
                        <area>0.0.0.0</area>
                    </network>
                """)

            # set network vlan address ect.
            config_dict["l3-interface_xml"].append(f"""
                <vlan-interface {operation}>
                    <name>vlan{vlan_id}</name>
                    <address>
                        <ip>{vlan_address}</ip>
                        <prefix-length>{vlan_subnet}</prefix-length>
                    </address>
                </vlan-interface>
            """)
            config_dict["vrrp_xml"].append(f"""
                <interface {operation}>
                    <name>vlan{vlan_id}</name>
                    <vrid>
                        <id>{vrid}</id>
                        <ip>
                            <ip>{vrrp_ip}</ip>
                        </ip>
                        <load-balance>
                            <disable>false</disable>
                        </load-balance>
                    </vrid>
                </interface>
            """)
        for interface_name in mlag_interface_name:
            # set to core gigabit interface
            config_dict["interface_xml"].append(f"""
                <gigabit-ethernet>
                    <name>{interface_name}</name>
                    <ether-options {operation}>
                        <_802.3ad>{mlag_peer_lag_interface_name}</_802.3ad>
                    </ether-options>
                </gigabit-ethernet>
            """)
        if mlag:
            if mlag["mlag_access_links"]:
                for access_link in mlag["mlag_access_links"]:
                    interface_name = access_link["access_interface_name"][0]
                    access_lag_interface_name = access_link["access_lag_interface_name"]
                    link_id = access_link["link_id"]
                    # set to access gigabit interface
                    config_dict["interface_xml"].append(f"""
                        <gigabit-ethernet>
                            <name>{interface_name}</name>
                            <ether-options {operation}>
                                <_802.3ad>{access_lag_interface_name}</_802.3ad>
                            </ether-options>
                        </gigabit-ethernet>
                    """)
                    config_dict["interface_xml"].append(f"""
                        <aggregate-ethernet {operation}>
                            <name>{access_lag_interface_name}</name>
                            <family>
                                <ethernet-switching>
                                    <native-vlan-id>{inband_vlan_id}</native-vlan-id>
                                    <port-mode>trunk</port-mode>
                                    <vlan>
                                        {"".join(f"<members><id>{vlan}</id></members>" for vlan in network_vlans_id)}
                                        <members><id>{inband_vlan_id}</id></members>
                                    </vlan>
                                </ethernet-switching>
                            </family>
                            <aggregated-ether-options>
                                <lacp>
                                    <enable>true</enable>
                                </lacp>
                            </aggregated-ether-options>
                        </aggregate-ethernet>
                    """)

                    mlag_access_interface_xml_list.append(f"""
                        <interface>
                            <name>{access_lag_interface_name}</name>
                            <link>{link_id}</link>
                        </interface>
                    """)
            mlag_access_interface_xml = "\n".join(mlag_access_interface_xml_list)
            config_dict["vlans_xml"].append(f"""
                <vlan-id {operation}>
                    <id>{mlag_peer_vlan_id}</id>
                    <l3-interface>vlan{mlag_peer_vlan_id}</l3-interface>
                </vlan-id>
            """)
            config_dict["interface_xml"].append(f"""
                <aggregate-ethernet {operation}>
                    <name>{mlag_peer_lag_interface_name}</name>
                    <family>
                        <ethernet-switching>
                            <native-vlan-id>{mlag_peer_vlan_id}</native-vlan-id>
                            <port-mode>trunk</port-mode>
                            <vlan>
                                {"".join(f"<members><id>{vlan}</id></members>" for vlan in network_vlans_id)}
                                <members><id>{mlag_peer_vlan_id},{inband_vlan_id}</id></members>
                            </vlan>
                        </ethernet-switching>
                    </family>
                    <aggregated-ether-options>
                        <lacp>
                            <enable>true</enable>
                        </lacp>
                    </aggregated-ether-options>
                </aggregate-ethernet>
            """)
            config_dict["mlag_xml"].append(f"""
                <domain {operation}>
                    <id>{domain_id}</id>
                    <node>{domain_id_node}</node>
                    <peer-ip>
                        <ip>{peer_ipv4_address}</ip>
                        <peer-link>{mlag_peer_lag_interface_name}</peer-link>
                        <peer-vlan>{mlag_peer_vlan_id}</peer-vlan>
                    </peer-ip>
                    {mlag_access_interface_xml}
                </domain>
            """)
            config_dict["l3-interface_xml"].append(f"""
                <vlan-interface {operation}>
                    <name>vlan{mlag_peer_vlan_id}</name>
                    <address>
                        <ip>{mlag_l3_interface_ip_address}</ip>
                        <prefix-length>30</prefix-length>
                    </address>
                </vlan-interface>
            """)

        if wan:
            interface_name = wan.get("wan_interface_name", [])[0]
            network_ip = ipaddress.IPv4Network(wan["wan_interface_ip"], strict=False)
            if interface_name:
                l3_name_string = "rif-" + interface_name.split("-")[0] + interface_name.split("/")[-1]
                config_dict["interface_xml"].append(f"""
                    <gigabit-ethernet>
                        <name>{interface_name}</name>
                        <routed-interface {operation}>
                            <name>{l3_name_string}</name>
                            <enable>true</enable>
                        </routed-interface>
                    </gigabit-ethernet>
                """)
                config_dict["l3-interface_xml"].append(f"""
                    <routed-interface {operation}>
                        <name>{l3_name_string}</name>
                        <address>
                            <ip>{wan["wan_interface_ip"].split("/")[0]}</ip>
                            <prefix-length>{wan["wan_interface_ip"].split("/")[1]}</prefix-length>
                        </address>
                    </routed-interface>
                """)

                if protocol == "ospf":
                    inband_ip_network = ipaddress.IPv4Network(inband["ip_address"], strict=False)
                    config_dict["ospf_xml"].append(f"""
                        <network {operation}>
                            <net>{network_ip}</net>
                            <area>0.0.0.0</area>
                        </network>
                        <network {operation}>
                            <net>{inband_ip_network}</net>
                            <area>0.0.0.0</area>
                        </network>
                    """)

            if wan.get("reserved_vlan", ""):
                config_dict["vlans_xml"].append(
                    f"""
                        <reserved-vlan {operation}>{wan["reserved_vlan"]}</reserved-vlan>
                    """)
            if wan.get("default_route_next_hop"):
                config_dict["static_xml"].append(f"""
                    <route {operation}>
                        <name>0.0.0.0/0</name>
                        <next-hop>
                            <ip-address>{wan["default_route_next_hop"]}</ip-address>
                        </next-hop>
                    </route>
                """)

        for dhcp in dhcp_relay:
            vlan_id = dhcp["vlan_id"]
            dhcp_server = dhcp["dhcp_server"]
            relay_address = network_vlan[dhcp["dhcp_network"]]["vrrp_ip"]
            config_dict["dhcp_xml"].append(f"""
                <interface>
                    <name>vlan{vlan_id}</name>
                    <disable>false</disable>
                    <dhcp-server-address>
                        <ip>{dhcp_server}</ip>
                    </dhcp-server-address>
                    <relay-agent-address>{relay_address}</relay-agent-address>
                </interface>
            """)
        dhcp_xml = "\n".join(config_dict["dhcp_xml"])
        config_dict["dhcp_xml"] = [f"""
            <relay {operation}>
                {dhcp_xml}
            </relay>
        """]

        # add vlans config
        if inband:
            vrid = inband["vrid"]
            vrrp_ip = inband["vrrp_ip"]
            config_dict["vlans_xml"].append(f"""
                <vlan-id {operation}>
                    <id>{inband_vlan_id}</id>
                    <l3-interface>vlan{inband_vlan_id}</l3-interface>
                </vlan-id>
            """)
            # add system config
            config_dict["system_xml"].append(f"""
                <inband {operation}>
                    <vlan-interface>
                        <name>vlan{inband_vlan_id}</name>
                    </vlan-interface>
                </inband>
            """)
            config_dict["l3-interface_xml"].append(f"""
                <vlan-interface {operation}>
                    <name>vlan{inband_vlan_id}</name>
                    <address>
                        <ip>{inband_vlan_address.split("/")[0]}</ip>
                        <prefix-length>{inband_vlan_address.split("/")[1]}</prefix-length>
                    </address>
                </vlan-interface>
            """)
            config_dict["vrrp_xml"].append(f"""
                <interface {operation}>
                    <name>vlan{inband_vlan_id}</name>
                    <vrid>
                        <id>{vrid}</id>
                        <ip>
                            <ip>{vrrp_ip}</ip>
                        </ip>
                        <load-balance>
                            <disable>false</disable>
                        </load-balance>
                    </vrid>
                </interface>
            """)

        return config_dict

    @staticmethod
    def access_common_config(op, protocol, **kwargs):
        operation = "" if op == "" else f'operation="{op}"'
        inband = kwargs.get("inband", {})
        inband_vlan_id, inband_vlan_address = inband.get("vlan_id", 3965), inband.get("ip_address", "")
        network_vlan = kwargs.get("vlans", {})
        mlag = kwargs.get("mlag", {})
        dhcp_snooping = kwargs.get("dhcp_snooping", [])
        nac = kwargs.get("nac", {})
        uplink_lag_interface_name, uplink_interface_name = mlag.get("uplink_lag_interface_name", "ae1"), mlag.get(
            "uplink_interface_name", [])
        config_dict = {"vlans_xml": [], "l3-interface_xml": [], "system_xml": [], "interface_xml": [], "dhcp_xml": [],
                       "dot1x_xml": [], "static_xml": [], "ip_xml": [], "mstp_xml": []}
        network_vlans_id = []
        dhcp_vlan_xml_list = []
        dot1x_interface_xml_list = []
        dot1x_aaa_xml_list = []
        config_dict["ip_xml"].append(f"""
                <routing>
                    <enable {operation}>true</enable>
                </routing>
            """)

        for vlan in network_vlan:
            vlan_id = network_vlan[vlan]["vlan_id"]
            network_vlans_id.append(vlan_id)
            # network vlan
            config_dict["vlans_xml"].append(f"""
                <vlan-id {operation}>
                    <id>{vlan_id}</id>
                </vlan-id>
            """)
        for interface_name in uplink_interface_name:
            config_dict["interface_xml"].append(f"""
                <gigabit-ethernet>
                    <name>{interface_name}</name>
                    <ether-options {operation}>
                        <_802.3ad>{uplink_lag_interface_name}</_802.3ad>
                    </ether-options>
                </gigabit-ethernet>
            """)
        if dhcp_snooping:
            for dhcp_snooping_id in dhcp_snooping:
                dhcp_vlan_xml_list.append(f"""
                    <vlan>
                        <id>{dhcp_snooping_id}</id>
                        <disable>false</disable>
                    </vlan>
                """)
            dhcp_vlan_xml = "\n".join(dhcp_vlan_xml_list)
            config_dict["dhcp_xml"].append(f"""
                <snooping {operation}>
                    <trust-port>
                        <name>{uplink_lag_interface_name}</name>
                    </trust-port>
                    {dhcp_vlan_xml}
                </snooping>
                """)
        if nac:
            for interface_name in nac["access_interface_name"]:
                config_dict["interface_xml"].append(f"""
                    <gigabit-ethernet>
                        <name>{interface_name}</name>
                        <family {operation}>
                            <ethernet-switching>
                                <port-mode>trunk</port-mode>
                            </ethernet-switching>
                        </family>
                        <storm-control {operation}>
                            <broadcast>
                                <ratio>1</ratio>
                            </broadcast>
                            <multicast>
                                <ratio>1</ratio>
                            </multicast>
                        </storm-control>
                    </gigabit-ethernet>
                """)

                config_dict["mstp_xml"].append(f"""
                    <mstp>
                        <interface>
                            <name>{interface_name}</name>
                            <bpdu-guard>true</bpdu-guard>
                        </interface>
                    </mstp>
                """)

                dot1x_interface_xml_list.append(f"""
                    <interface {operation}>
                        <name>{interface_name}</name>
                        <host-mode>multiple</host-mode>
                        <auth-mode>
                            <_802.1x/>
                            <mac-radius/>
                        </auth-mode>
                    </interface>
                """)
            for nac_server in nac["nac_servers"]:
                dot1x_aaa_xml_list.append(f"""
                    <server-ip {operation}>
                        <ip>{nac_server["server_address"]}</ip>
                        <shared-key>{nac_server["shared_secret"]}</shared-key>
                        <auth-port>{nac_server["port"]}</auth-port>
                    </server-ip>
                """)
            dotx1_interface_xml = "\n".join(dot1x_interface_xml_list)
            dot1x_aaa_xml = "\n".join(dot1x_aaa_xml_list)
            config_dict["dot1x_xml"].append(f"""
                {dotx1_interface_xml}
                <aaa {operation}>
                    <radius>
                        <authentication>
                            {dot1x_aaa_xml}
                        </authentication>
                        <nas-ip>{inband_vlan_address.split("/")[0]}</nas-ip>
                    </radius>
                </aaa>
            """)
        if inband:
            inband_vrrp_ip = inband["vrrp_ip"]
            config_dict["vlans_xml"].append(f"""
                <vlan-id {operation}>
                    <id>{inband_vlan_id}</id>
                    <l3-interface>vlan{inband_vlan_id}</l3-interface>
                </vlan-id>
            """)
            config_dict["system_xml"].append(f"""
                <inband {operation}>
                    <vlan-interface>
                        <name>vlan{inband_vlan_id}</name>
                    </vlan-interface>
                </inband>
            """)
            config_dict["l3-interface_xml"].append(f"""
                <vlan-interface {operation}>
                    <name>vlan{inband_vlan_id}</name>
                    <address>
                        <ip>{inband_vlan_address.split("/")[0]}</ip>
                        <prefix-length>{inband_vlan_address.split("/")[1]}</prefix-length>
                    </address>
                </vlan-interface>
            """)
            config_dict["static_xml"].append(f"""
                <route {operation}>
                    <name>0.0.0.0/0</name>
                    <next-hop>
                        <ip-address>{inband_vrrp_ip}</ip-address>
                    </next-hop>
                </route>
            """)
        if mlag:
            config_dict["interface_xml"].append(f"""
                <aggregate-ethernet {operation}>
                    <name>{uplink_lag_interface_name}</name>
                    <family>
                        <ethernet-switching>
                            <native-vlan-id>{inband_vlan_id}</native-vlan-id>
                            <port-mode>trunk</port-mode>
                            <vlan>
                                {"".join(f"<members><id>{vlan}</id></members>" for vlan in network_vlans_id)}
                                <members><id>{inband_vlan_id},{mlag["mlag_peer_vlan_id"]}</id></members>
                            </vlan>
                        </ethernet-switching>
                    </family>
                    <aggregated-ether-options>
                        <lacp>
                            <enable>true</enable>
                        </lacp>
                    </aggregated-ether-options>
                </aggregate-ethernet>
            """)
        return config_dict

    @staticmethod
    def common_bgp_and_ospf(op, config_dict, **kwargs):
        operation = "" if op == "" else f'operation="{op}"'
        router_id = kwargs.get("router_id", "")
        original_subnet = kwargs.get("original_subnet", "")
        config_dict["l3-interface_xml"].append(f"""
            <loopback {operation}>
                <name>loopback0</name>
                <address>
                    <ip>{router_id}</ip>
                    <prefix-length>32</prefix-length>
                </address>
            </loopback>
        """)
        config_dict["routing_xml"].append(f"""
            <prefix-list {operation}>
                <ipv4-family>
                    <name>list_default</name>
                    <permit>
                        <prefix>
                            <net>{original_subnet.split("/")[0]}/30</net>
                        </prefix>
                    </permit>
                </ipv4-family>
            </prefix-list>
            <route-map {operation}>
                <name>map_default</name>
                <order>
                    <seq>1</seq>
                    <matching-policy>permit</matching-policy>
                    <match>
                        <ipv4-addr>
                        <address>
                            <prefix-list>list_default</prefix-list>
                        </address>
                        </ipv4-addr>
                    </match>
                </order>
            </route-map>
        """)
        return config_dict

    @staticmethod
    def adapt_data_process(common_xml, bgp_or_ospf_xml=None):

        def merge_data(data_list):
            return {
                key: [item for dict_data in data_list for item in dict_data.get(key, [])]
                for key in {k for dict_data in data_list for k in dict_data}
            }

        merged_all_data = []
        if common_xml:
            merged_common_data = merge_data(common_xml)
            merged_all_data.append(merged_common_data)
        if bgp_or_ospf_xml:
            merged_bgp_or_ospf = merge_data(bgp_or_ospf_xml)
            merged_all_data.append(merged_bgp_or_ospf)

        merged_all_data = merge_data(merged_all_data) if merged_all_data else {}

        deploy_template_xml = "<config>"
        namespace_map = {
            "ip_xml": "http://pica8.com/xorplus/ip-routing",
            "routing_xml": "http://pica8.com/xorplus/routing",
            "bgp_xml": "http://pica8.com/xorplus/bgp",
            "ospf_xml": "http://pica8.com/xorplus/ospfv2",
            "l3-interface_xml": "http://pica8.com/xorplus/vlan-interface",
            "vlans_xml": "http://pica8.com/xorplus/vlans",
            "system_xml": "http://pica8.com/xorplus/system",
            "interface_xml": "http://pica8.com/xorplus/interface",
            "dhcp_xml": "http://pica8.com/xorplus/dhcp",
            "dot1x_xml": "http://pica8.com/xorplus/dot1x",
            "mlag_xml": "http://pica8.com/xorplus/mlag",
            "vrrp_xml": "http://pica8.com/xorplus/vrrp",
            "static_xml": "http://pica8.com/xorplus/static-routes",
            "evpn_xml": "http://pica8.com/xorplus/evpn",
            "vxlans_xml": "http://pica8.com/xorplus/vxlans",
            "mstp_xml": "http://pica8.com/xorplus/mstp",
            "lldp_xml": "http://pica8.com/xorplus/lldp",
            "spanning-tree_xml": "http://pica8.com/xorplus/mstp",
            "bgp_evpn_xml": "http://pica8.com/xorplus/bgp-evpn",
        }

        for key, namespace in namespace_map.items():
            key_data = "\n".join(merged_all_data.get(key, []))
            if key_data:
                deploy_template_xml += f"""
                                <{key.split('_')[0]} xmlns="{namespace}">
                                    {key_data}
                                </{key.split('_')[0]}>
                            """
        deploy_template_xml += "</config>"
        return deploy_template_xml

    def update_campus_topology(self, meta_data, config_content):
        site_topo_id = meta_data.get("site_topo_id", "")
        sn = meta_data.get("sn", "")
        topo_obj = self.session.query(CampusSiteNodes).filter(CampusSiteNodes.topology_config_id == site_topo_id,
                                                              CampusSiteNodes.switch_sn == sn)
        if topo_obj:
            topo_obj.update({"node_config_data": json.dumps(config_content)})
        else:
            LOG.error(f"Topology node not found for site_topo_id: {site_topo_id}")

    def update_dc_topology(self, meta_data, config_content):
        fabric_topo_id = meta_data.get("fabric_topo_id", "")
        logic_name = meta_data.get("logic_name", "")

        topo_obj = self.session.query(DCFabricTopologyNode).filter(
            DCFabricTopologyNode.fabric_topo_id == fabric_topo_id,
            DCFabricTopologyNode.logic_name == logic_name)
        if topo_obj:
            topo_obj.update({"node_config": config_content})
        else:
            LOG.error(f"Topology node not found for fabric_topo_id: {fabric_topo_id}, logic_name: {logic_name}")

    def update_uplink_topology(self, meta_data, status, config_content=None):
        id = meta_data.get("device_config_id", "")
        topo_obj = self.session.query(SwitchPortgroupConfig).filter(SwitchPortgroupConfig.id == id)
        if topo_obj:
            if config_content:
                topo_obj.update({"config": config_content, "status": status})
            else:
                topo_obj.update({"status": status})
        else:
            LOG.error(f"Not found device_config_id: {id}")

    def update_overlay_topology(self, meta_data, status, config_content=None):
        keys = meta_data.keys()
        topo_obj = None
        config_id = ''
        if 'port_config_id' in keys:
            config_id = meta_data.get("port_config_id", "")
            topo_obj = self.session.query(DCLogicalPortConfig).filter(DCLogicalPortConfig.id == config_id)
        elif 'interface_config_id' in keys:
            config_id = meta_data.get("interface_config_id", "")
            topo_obj = self.session.query(DCLogicalInterfaceConfig).filter(DCLogicalInterfaceConfig.id == config_id)
        else:
            config_id = meta_data.get("router_config_id", "")
            topo_obj = self.session.query(DCLogicalRouterConfig).filter(DCLogicalRouterConfig.id == config_id)
        if topo_obj:
            if config_content:
                topo_obj.update({"config": config_content, "status": status})
            else:
                topo_obj.update({"status": status})
        else:
            LOG.error(f"Not found logical_id: {config_id}")

    def core_bgp(self, op, **kwargs):
        operation = "" if op == "" else f'operation="{op}"'
        bgp_info = kwargs.get("bgp", {})
        vlans_info = kwargs.get("vlans", {})
        wan_info = kwargs.get("wan", {})
        inband_info = kwargs.get("inband", {})
        router_id, asn, neighbour_ip_address, neighbour_asn, original_subnet = (
            bgp_info.get('router_id', ''), bgp_info.get('asn', ''),
            bgp_info.get('neighbour_ip_address', ''),
            bgp_info.get('neighbour_asn', ''),
            bgp_info.get('original_subnet', ''))
        config_dict = {"bgp_xml": [], "l3-interface_xml": [], "routing_xml": []}
        config_dict = self.common_bgp_and_ospf(op, config_dict, **bgp_info)
        if op == "":
            config_dict["bgp_xml"].append(f"""
                <local-as {operation}>{asn}</local-as>
            """)

        ipv4_unicast_template_sub = ""
        for vlan in vlans_info:
            ipv4_unicast_template_sub += f"""
                <network>
                    <net>{vlans_info[vlan]["subnet"]}</net>
                </network>
            """

        inband_ip_network = ipaddress.IPv4Network(inband_info["ip_address"], strict=False)
        ipv4_unicast_template_sub += f"""
            <network>
                <net>{router_id}/32</net>
            </network>
            <network>
                <net>{inband_ip_network}</net>
            </network>
        """

        if wan_info:
            network_ip = ipaddress.IPv4Network(wan_info["wan_interface_ip"], strict=False)
            ipv4_unicast_template_sub += f"""
                <network>
                    <net>{network_ip}</net>
                </network>
            """

        config_dict["bgp_xml"].append(f"""
            <ipv4-unicast {operation}>
                {ipv4_unicast_template_sub}
                <network>
                    <net>{original_subnet}</net>
                </network>
                <redistribute>
                    <connected>
                        <route-map>map_default</route-map>
                    </connected>
                </redistribute>
            </ipv4-unicast>
            <ebgp-requires-policy {operation}>false</ebgp-requires-policy>
            <router-id {operation}>{router_id}</router-id>
            <neighbor {operation}>
                <ip>{neighbour_ip_address.split('/')[0]}</ip>
                <remote-as>{neighbour_asn}</remote-as>
            </neighbor>
        """)
        return config_dict

    def core_ospf(self, op, **kwargs):
        operation = "" if op == "" else f'operation="{op}"'
        ospf_info = kwargs.get("ospf", {})
        router_id, area_id, original_subnet = (
            ospf_info.get('router_id', ''), ospf_info.get('area_id', ''),
            ospf_info.get('original_subnet', ''))
        config_dict = {"ospf_xml": [], "l3-interface_xml": [], "routing_xml": []}
        config_dict = self.common_bgp_and_ospf(op, config_dict, **ospf_info)
        config_dict["ospf_xml"].append(f"""
            <router-id {operation}>{router_id}</router-id>
            <network {operation}>
                <net>{original_subnet.split("/")[0]}/30</net>
                <area>{area_id}</area>
            </network>      
            <redistribute {operation}>
                <connected>
                    <route-map>map_default</route-map>
                </connected>
            </redistribute>
        """)

        return config_dict

    @staticmethod
    def clos_common_direct_route_port(role_type, op, **kwargs):
        config_dict = {"vlans_xml": [], "l3-interface_xml": [], "system_xml": [], "interface_xml": [], "dhcp_xml": [],
                       "evpn_xml": [], "lldp_xml": [], "spanning-tree_xml": [], "dot1x_xml": [], "static_xml": [],
                       "mlag_xml": [], "bgp_xml": [], "routing_xml": [], "ip_xml": []}
        if role_type in ("distribution", "border", "access", "core"):
            operation = "" if op == "" else f'operation="{op}"'
            link_route, mlag, wan, vlans, vrfs = (kwargs.get("link_route", {}), kwargs.get("mlag", {}),
                                                  kwargs.get("wan", {}), kwargs.get("vlans", {}),
                                                  kwargs.get("vrfs", {}))
            reserved_vlan, link_interfaces, to_border_interfaces = (link_route.get("reserved_vlan", ""),
                                                                    link_route.get("link_interfaces", []),
                                                                    link_route.get("to_border_interfaces", []))
            asn = kwargs.get('underlay', {}).get('asn', '')
            link_interfaces_diff = list(set(link_interfaces) - set(to_border_interfaces))

            config_dict["ip_xml"].append(f"""
                <routing {operation}>
                    <enable>true</enable>
                </routing>
            """)
            config_dict["evpn_xml"].append(f"<enable {operation}>true</enable>")
            config_dict["lldp_xml"].append(f"<enable {operation}>true</enable>")
            config_dict["spanning-tree_xml"].append(f"<enable {operation}>false</enable>")
            config_dict["vlans_xml"].append(f"<reserved-vlan {operation}>{reserved_vlan}</reserved-vlan>")

            for interface_name in link_interfaces:
                link_interface_name = f"rif-{interface_name.split('-')[0]}{interface_name.split('/')[-1]}"
                config_dict["interface_xml"].append(f"""
                        <gigabit-ethernet>
                            <name>{interface_name}</name>
                            <routed-interface {operation}>
                                <name>{link_interface_name}</name>
                                <enable>true</enable>
                            </routed-interface>
                        </gigabit-ethernet>
                    """)
                config_dict["l3-interface_xml"].append(f"""
                        <routed-interface>
                            <name {operation}>{link_interface_name}</name>
                        </routed-interface>
                    """)

            if role_type in ("distribution", "access", "core"):
                for interface_name in link_interfaces_diff:
                    config_dict["interface_xml"].append(f"""
                            <gigabit-ethernet>
                                <name>{interface_name}</name>
                                <mtu {operation}>9216</mtu>
                            </gigabit-ethernet>
                        """)
            if role_type == "core" and mlag:
                mlag_peer_lag_interface_name, mlag_peer_vlan_id, domain_id, domain_id_node, peer_ipv4_address, mlag_peer_l3_interface_ip = (
                    mlag.get("mlag_peer_lag_interface_name", ""), mlag.get("mlag_peer_vlan_id", ""),
                    mlag.get("domain_id", ""),
                    mlag.get("domain_id_node", ""), mlag.get("peer_ipv4_address", ""),
                    mlag.get("mlag_peer_l3_interface_ip", ""))
                get_vlans_id = []
                for key in vlans:
                    vlan_id = vlans[key]["vlan_id"]
                    get_vlans_id.append(vlan_id)
                vlans_id = ",".join(str(vlan) for vlan in get_vlans_id)
                for mlag_peer_interface_name in mlag.get("mlag_peer_interface_name", []):
                    # set to access gigabit interface
                    config_dict["interface_xml"].append(f"""
                        <gigabit-ethernet>
                            <name>{mlag_peer_interface_name}</name>
                            <ether-options {operation}>
                                <_802.3ad>{mlag_peer_lag_interface_name}</_802.3ad>
                            </ether-options>
                        </gigabit-ethernet>
                    """)
                config_dict["interface_xml"].append(f"""
                    <aggregate-ethernet>
                        <name {operation}>{mlag_peer_lag_interface_name}</name>
                        <family>
                            <ethernet-switching>
                                <native-vlan-id>{mlag_peer_vlan_id}</native-vlan-id>
                                <port-mode>trunk</port-mode>
                                <vlan>
                                    <members>
                                        <id>{vlans_id},{mlag["mlag_peer_vlan_id"]}</id>
                                    </members>
                                </vlan>
                            </ethernet-switching>
                        </family>
                        <aggregated-ether-options>
                            <lacp>
                                <enable>true</enable>
                            </lacp>
                        </aggregated-ether-options>
                    </aggregate-ethernet>
                """)
                config_dict["vlans_xml"].append(f"""
                    <vlan-id>
                        <id {operation}>{mlag_peer_vlan_id}</id>
                        <l3-interface>vlan{mlag_peer_vlan_id}</l3-interface>
                    </vlan-id>
                """)
                config_dict["mlag_xml"].append(f"""
                    <domain>
                        <id {operation}>{domain_id}</id>
                        <node>{domain_id_node}</node>
                        <peer-ip>
                            <ip>{peer_ipv4_address}</ip>
                            <peer-link>{mlag_peer_lag_interface_name}</peer-link>
                            <peer-vlan>{mlag_peer_vlan_id}</peer-vlan>
                        </peer-ip>
                    </domain>
                """)
                config_dict["l3-interface_xml"].append(f"""
                    <vlan-interface>
                        <name {operation}>vlan{mlag_peer_vlan_id}</name>
                        <address>
                            <ip>{mlag_peer_l3_interface_ip}</ip>
                            <prefix-length>30</prefix-length>
                        </address>
                    </vlan-interface>
                """)
            if role_type in ("core", "border", "access"):
                for vrf in vrfs:
                    config_dict["ip_xml"].append(f"""
                        <vrf>
                            <name {operation}>{vrf}</name>
                        </vrf>
                    """)
            if role_type in ("core", "border"):
                if wan:
                    config_dict["ip_xml"].append(f"""
                        <vrf>
                            <name {operation}>vrfwan</name>
                        </vrf>
                    """)
                    if wan.get("default_route_next_hop"):
                        config_dict["static_xml"].append(f"""
                            <vrf>
                                <name {operation}>vrfwan</name>
                                <route>
                                    <name>0.0.0.0/0</name>
                                    <next-hop>
                                        <ip-address>{wan["default_route_next_hop"]}</ip-address>
                                    </next-hop>
                                </route>
                            </vrf>
                        """)
                    if wan.get("wan_interface_name"):
                        for interface in wan["wan_interface_name"]:
                            interface_name = "rif-" + interface.split("-")[0] + interface.split("/")[-1]
                            ipv4_address = kwargs.get("wan", {}).get("ipv4_address", "")
                            config_dict["interface_xml"].append(f"""
                                <gigabit-ethernet>
                                    <name>{interface}</name>
                                    <routed-interface {operation}>
                                        <name>{interface_name}</name>
                                        <enable>true</enable>
                                    </routed-interface>
                                </gigabit-ethernet>
                            """)
                            config_dict["l3-interface_xml"].append(f"""
                                <routed-interface>
                                    <name {operation}>{interface_name}</name>
                                    <vrf>vrfwan</vrf>
                                    <address>
                                        <ip>{ipv4_address}</ip>
                                        <prefix-length>30</prefix-length>
                                    </address>
                                </routed-interface>
                            """)

        return config_dict

    @staticmethod
    def clos_ebgp(role_type, op, **kwargs):
        config_dict = {"bgp_xml": [], "l3-interface_xml": [], "interface_xml": [], "system_xml": [], "vlans_xml": []}
        if role_type in ("distribution", "border"):
            operation = "" if op == "" else f'operation="{op}"'
            bgp, vlans = kwargs.get("bgp", {}), kwargs.get("vlans")
            router_id, asn = bgp.get("router_id", ""), bgp.get("asn", "")
            link_interfaces = kwargs.get("link_route", {}).get("link_interfaces", [])
            for index, vlan_name in enumerate(vlans):
                vlan = vlans[vlan_name]
                vlan_id = vlan["vlan_id"]
                config_dict["vlans_xml"].append(f"""
                        <vlan-id>
                            <id {operation}>{vlan_id}</id>
                        </vlan-id>
                        <vlan-id>
                            <id {operation}>{3965 - index}</id>
                        </vlan-id>
                    """)
            config_dict["vlans_xml"].append(f"""
                    <vlan-id>
                        <id {operation}>3966</id>
                    </vlan-id>
                """)
            config_dict["l3-interface_xml"].append(f"""
                            <loopback>
                                <name {operation}>lo</name>
                                <address {operation}>
                                    <ip>{router_id}</ip>
                                    <prefix-length>32</prefix-length>
                                </address>
                            </loopback>
                        """)
            config_dict["bgp_xml"].append(f"""
                    <router-id {operation}>{router_id}</router-id>
                    <ebgp-requires-policy {operation}>false</ebgp-requires-policy>
                    <ipv4-unicast>
                        <network>
                            <net {operation}>{router_id}/32</net>
                        </network>
                    </ipv4-unicast>
                """)

            config_dict["system_xml"].append(f"""
                    <inband>
                        <loopback>
                            <ip-address {operation}>{router_id}</ip-address>
                        </loopback>
                    </inband>
                """)

            for interface_name in link_interfaces:
                link_interface_name = f"rif-{interface_name.split('-')[0]}{interface_name.split('/')[-1]}"
                config_dict["bgp_xml"].append(f"""
                        <interface>
                            <name {operation}>{link_interface_name}</name>
                            <remote-as>external</remote-as>
                            <capability>
                                <extended-nexthop/>
                            </capability>
                            <ipv6-unicast>
                                <activate>true</activate>
                            </ipv6-unicast>
                            <evpn>
                                <activate>true</activate>
                            </evpn>
                        </interface>
                    """)
        return config_dict

    @staticmethod
    def clos_underlay(role_type, op, **kwargs):
        config_dict = {"bgp_xml": [], "l3-interface_xml": [], "system_xml": [], "evpn_xml": []}
        if role_type in ("access", "core"):
            operation = "" if op == "" else f'operation="{op}"'
            link_route = kwargs.get("link_route", {})
            underlay = kwargs.get("underlay", {})
            original_subnet = underlay.get("original_subnet", "").split("/")[0]
            if underlay:
                router_id = underlay.get("router_id", "")
                asn = underlay.get("asn", "")
                mlag_network_xml = ""
                config_dict["l3-interface_xml"].append(f"""
                        <loopback>
                            <name {operation}>lo</name>
                            <address>
                                <ip>{router_id}</ip>
                                <prefix-length>32</prefix-length>
                            </address>
                        </loopback>
                    """)
                config_dict["system_xml"].append(f"""
                    <inband>
                        <loopback>
                            <ip-address {operation}>{router_id}</ip-address>
                        </loopback>
                    </inband>
                """)

                bgp_interface_xml_list = []
                for interface in link_route["link_interfaces"]:
                    interface_name = "rif-" + interface.split("-")[0] + interface.split("/")[-1]

                    bgp_interface_xml_list.append(f"""
                        <interface>
                            <name {operation}>{interface_name}</name>
                            <remote-as>external</remote-as>
                            <capability>
                                <extended-nexthop/>
                            </capability>
                            <ipv6-unicast>
                                <activate>true</activate>
                            </ipv6-unicast>
                            <evpn>
                                <activate>true</activate>
                            </evpn>
                        </interface>
                    """)
                bgp_interface_xml = "\n".join(bgp_interface_xml_list)
                config_dict["bgp_xml"].append(f"""
                    <ebgp-requires-policy {operation}>false</ebgp-requires-policy>
                    <router-id {operation}>{router_id}</router-id>
                    {bgp_interface_xml}
                    <ipv4-unicast>
                        <network>
                            <net {operation}>{router_id}/32</net>
                        </network>
                    </ipv4-unicast>
                """)
        return config_dict

    @staticmethod
    def clos_vrf(role_type, op, **kwargs):
        operation = "" if op == "" else f'operation="{op}"'
        config_dict = {"l3-interface_xml": []}
        # todo set ip vrf? not
        if role_type in ("access", "core"):
            vlans = kwargs.get("vlans", {})
            vrfs = kwargs.get("vrfs", {})
            for vrf in vrfs:
                for vlan in vrfs[vrf]["network"]:
                    vlan_interface_name = "vlan" + vlans[vlan]["vlan_id"]
                    vlan_ip_address = vlans[vlan]["vlan_ip_address"]
                    vlan_subnet = vlans[vlan]["subnet"].split("/")[1]
                    vlan_anycast_ip = vlans[vlan]["vlan_anycast_ip_address"]
                    config_dict["l3-interface_xml"].append(f"""
                        <vlan-interface>
                            <name {operation}>{vlan_interface_name}</name>
                            <vrf>{vrf}</vrf>
                            <address>
                                <ip>{vlan_ip_address}</ip>
                                <prefix-length>{vlan_subnet}</prefix-length>
                            </address>
                            <anycast>
                                <address>
                                    <ip>{vlan_anycast_ip}</ip>
                                    <prefix-length>{vlan_subnet}</prefix-length>
                                </address>
                                <mac>00:00:20:00:00:FE</mac>
                            </anycast>
                        </vlan-interface>
                    """)
        return config_dict

    @staticmethod
    def clos_vxlan(role_type, op, **kwargs):
        config_dict = {"vxlans_xml": [], "vlans_xml": [], "l3-interface_xml": []}
        if role_type in ("access", "core"):
            operation = "" if op == "" else f'operation="{op}"'
            underlay, vlans, vrfs = (kwargs.get("underlay", {}), kwargs.get("vlans", {}), kwargs.get("vrfs", {}))
            router_id = underlay.get("router_id", "")
            vni_xml_list, vni_vlan_xml_list = [], []
            for vlan_name in vlans:
                vlan = vlans[vlan_name]
                vni_id = str(int(vlan["vlan_id"]) + 10000)
                vlan_id = vlan["vlan_id"]
                # vni_xml_list.append(f"""
                #     <vni>
                #         <id {operation}>{vni_id}</id>
                #         <decapsulation>
                #             <mode>service-vlan-per-port</mode>
                #         </decapsulation>
                #         <vlan>{vlan_id}</vlan>
                #         <arp-nd-suppress>
                #             <disable>false</disable>
                #         </arp-nd-suppress>
                #     </vni>
                # """)
                config_dict["vlans_xml"].append(f"""
                    <vlan-id>
                        <id {operation}>{vlan_id}</id>
                        <l3-interface>vlan{vlan_id}</l3-interface>
                    </vlan-id>
                """)
            if role_type == "access":
                config_dict["vlans_xml"].append(f"""
                    <vlan-id>
                        <id {operation}>3966</id>
                    </vlan-id>
                """)
            vlan_to_vrf = {}
            for vrf_name, vrf_data in vrfs.items():
                for vlan_key in vrf_data["network"]:
                    vlan_to_vrf[vlan_key] = vrf_name
            for index, (vlan_key, vlan_info) in enumerate(vlans.items()):
                assigned_vlan_id = 3965 - index
                vlan_name = f"vlan{assigned_vlan_id}"
                vrf_name = vlan_to_vrf.get(vlan_key)

                config_dict["vlans_xml"].append(f"""
                    <vlan-id>
                        <id>{assigned_vlan_id}</id>
                        <l3-interface>{vlan_name}</l3-interface>
                    </vlan-id>
                """)

                if vrf_name:
                    config_dict["l3-interface_xml"].append(f"""
                        <vlan-interface>
                            <name>{vlan_name}</name>
                            <vrf>{vrf_name}</vrf>
                        </vlan-interface>
                    """)
            # start_vni_id = 13965
            # for index, vrf_name in enumerate(vrfs):
            #     vni_vlan_id = start_vni_id - index
            #     vlan_calc_id = vni_vlan_id - 10000
            #     vni_vlan_xml_list.append(f"""
            #         <vni>
            #             <id {operation}>{vni_vlan_id}</id>
            #             <vlan>{vlan_calc_id}</vlan>
            #         </vni>
            #     """)
            # vni_xml = "\n".join(vni_xml_list)
            # vni_vlan_xml = "\n".join(vni_vlan_xml_list)
            # config_dict["vxlans_xml"].append(f"""
            #     <source-interface>
            #         <name {operation}>lo</name>
            #         <address>{router_id}</address>
            #     </source-interface>
            #     {vni_xml}
            #     {vni_vlan_xml}
            # """)
        return config_dict

    @staticmethod
    def clos_overlay(role_type, op, **kwargs):
        config_dict = {"bgp_xml": [], "ip_xml": [], "routing_xml": []}
        operation = "" if op == "" else f'operation="{op}"'
        underlay, vrfs, wan = (kwargs.get('underlay', {}), kwargs.get("vrfs", {}), kwargs.get("wan", {}))
        asn, router_id, original_subnet = (underlay.get("asn", ""), underlay.get("router_id", ""),
                                           underlay.get("original_subnet", ""))
        if role_type == "access" and vrfs:
            for vrf in vrfs:
                config_dict["bgp_xml"].append(f"""
                    <vrf>
                        <name {operation}>{vrf}</name>
                        <local-as>{asn}</local-as>
                        <router-id>{router_id}</router-id>
                        <evpn>
                            <advertise>
                                <ipv4-unicast/>
                                <ipv6-unicast/>
                            </advertise>
                        </evpn>
                    </vrf>
                """)
            config_dict["bgp_xml"].append(f"""
                <evpn {operation}>
                    <advertise-all-vni/>
                    <advertise>
                        <ipv4-unicast/>
                        <ipv6-unicast/>
                    </advertise>
                </evpn>
            """)
        if role_type == "core" and vrfs:
            vrf_xml = []
            if wan:
                default_route_next_hop = wan.get("default_route_next_hop", "")
                for vrf in vrfs:
                    vrf_xml.append(f"""
                        <vrf>
                            <name>{vrf}</name>
                        </vrf>
                    """)
                vrf_xml = "\n".join(vrf_xml)
                config_dict["bgp_xml"].append(f"""
                    <vrf>
                        <name {operation}>vrfwan</name>
                        <local-as>{asn}</local-as>
                        <router-id>{router_id}</router-id>
                        <neighbor>
                            <ip>{default_route_next_hop}</ip>
                            <remote-as>external</remote-as>
                            <ipv4-unicast>
                                <activate>true</activate>
                            </ipv4-unicast>
                        </neighbor>
                        <ipv4-unicast>
                            <import>
                                {vrf_xml}
                                <vrf>
                                    <name>default</name>
                                </vrf>
                            </import>
                            <network>
                                <net>{original_subnet.split("/")[0]}/30</net>
                            </network>
                        </ipv4-unicast>
                    </vrf>
                """)
            for vrf in vrfs:
                config_dict["bgp_xml"].append(f"""
                    <vrf>
                        <name {operation}>{vrf}</name>
                        <local-as>{asn}</local-as>
                        <router-id>{router_id}</router-id>
                        <ipv4-unicast>
                            <import>
                                <vrf-route-map>map1</vrf-route-map>
                            </import>
                        </ipv4-unicast>
                        <evpn>
                            <advertise>
                                <ipv4-unicast/>
                                <ipv6-unicast/>
                            </advertise>
                        </evpn>
                    </vrf>
                """)
            config_dict["bgp_xml"].append(f"""
                <ipv4-unicast {operation}>
                    <import>
                        <vrf-route-map>map1</vrf-route-map>
                    </import>
                </ipv4-unicast>
                <evpn {operation}>
                    <advertise-all-vni/>
                    <advertise>
                        <ipv4-unicast/>
                        <ipv6-unicast/>
                    </advertise>
                </evpn>
            """)
            config_dict["routing_xml"].append(f"""
                <prefix-list {operation}>
                    <ipv4-family>
                        <name>list1</name>
                        <permit>
                            <prefix>
                                <net>0.0.0.0/0</net>
                            </prefix>
                        </permit>
                    </ipv4-family>
                </prefix-list>
                <route-map>
                    <name {operation}>map1</name>
                    <order>
                        <seq>1</seq>
                        <matching-policy>permit</matching-policy>
                        <match>
                            <ipv4-addr>
                                <address>
                                    <prefix-list>list1</prefix-list>
                                </address>
                            </ipv4-addr>
                        </match>
                    </order>
                </route-map>
            """)
        return config_dict

    @staticmethod
    def clos_dhcp_relay(role_type, op, **kwargs):
        config_dict = {"dhcp_xml": []}
        if role_type == "access":
            operation = "" if op == "" else f'operation="{op}"'
            dhcp_relay, vlans = kwargs.get('dhcp_relay', []), kwargs.get("vlans", {})
            vlan_id_to_name = {v["vlan_id"]: k for k, v in vlans.items()}
            relay_list = []
            for k in dhcp_relay:
                vlan_id, dhcp_server = (k.get('vlan_id', ''),
                                        k.get('dhcp_server', ''))
                vlan_key = vlan_id_to_name.get(vlan_id)
                relay_address = vlans[vlan_key]["vlan_ip_address"]
                relay_list.append(f"""
                    <relay>
                        <interface>
                            <name {operation}>vlan{vlan_id}</name>
                            <disable>false</disable>
                            <dhcp-server-address>
                                <ip>{dhcp_server}</ip>
                            </dhcp-server-address>
                            <relay-agent-address>{relay_address}</relay-agent-address>
                        </interface>
                    </relay>
                """)
            relay_xml = "\n".join(relay_list)
            config_dict["dhcp_xml"].append(relay_xml)
        return config_dict

    @staticmethod
    def clos_nac(role_type, op, **kwargs):
        config_dict = {"dot1x_xml": [], "interface_xml": [], "spanning-tree_xml": []}
        if role_type == "access":
            operation = "" if op == "" else f'operation="{op}"'
            nac = kwargs.get('nac', {})
            interface_list, gigabit_ethernet_list, aaa_list, spanning_tree_mstp_list = [], [], [], []
            access_interface_name, nac_servers = (
                nac.get('access_interface_name', []), nac.get('nac_servers', []))
            for k in access_interface_name:
                interface_list.append(f"""
                    <interface>
                        <name {operation}>{k}</name>
                        <host-mode>multiple</host-mode>
                        <auth-mode>
                            <_802.1x/>
                            <mac-radius/>
                        </auth-mode>
                    </interface>
                """)
                gigabit_ethernet_list.append(f"""
                    <gigabit-ethernet>
                        <name>{k}</name>
                        <family {operation}>
                            <ethernet-switching>
                                <port-mode>trunk</port-mode>
                            </ethernet-switching>
                        </family>
                        <storm-control {operation}>
                            <broadcast>
                                <ratio>1</ratio>
                            </broadcast>
                            <multicast>
                                <ratio>1</ratio>
                            </multicast>
                        </storm-control>
                    </gigabit-ethernet>
                """)
                spanning_tree_mstp_list.append(f"""
                    <interface>
                        <name>{k}</name>
                        <bpdu-guard>true</bpdu-guard>
                    </interface>
                """)
            spanning_tree_mstp_list = "\n".join(spanning_tree_mstp_list)
            interface_xml = "\n".join(interface_list)
            gigabit_ethernet_xml = "\n".join(gigabit_ethernet_list)

            vlan_obj = kwargs.get("vlans", {})
            vlan_ip = next((vlan["vlan_ip_address"] for vlan in vlan_obj.values()), None)
            # router_id = kwargs.get("underlay", {}).get("router_id", "")
            for k in nac_servers:
                server_address, shared_secret, port = (
                    k.get('server_address', ''), k.get('shared_secret', ''), k.get('port', ''))
                aaa_list.append(f"""
                    <aaa {operation}>
                        <radius>
                            <authentication>
                                <server-ip>
                                    <ip>{server_address}</ip>
                                    <shared-key>{shared_secret}</shared-key>                     
                                    <auth-port>{port}</auth-port>
                                </server-ip>
                            </authentication>
                            <nas-ip>{vlan_ip.split("/")[0]}</nas-ip>
                        </radius>
                    </aaa>
                """)
            aaa_xml = "\n".join(aaa_list)
            config_dict["dot1x_xml"].append(f"""
                {interface_xml}
                {aaa_xml}
            """)
            config_dict["interface_xml"].append(f"""
                {gigabit_ethernet_xml}
            """)
            config_dict["spanning-tree_xml"].append(f"""
                <mstp {operation}>
                    {spanning_tree_mstp_list}
                </mstp>
            """)
        return config_dict

    def deploy_netconf_config_by_mlag(self, role_type, meta_data, **template_data):
        common_xml = []
        bgp_or_ospf_xml = []
        deploy_status = {}
        try:
            with manager.connect(
                    host=self.host,
                    port=self.port,
                    username=self.username,
                    password=self.password,
                    hostkey_verify=False,
                    timeout=300
            ) as m:
                try:
                    if meta_data["role"] == "access":
                        poe_xml = """
                            <config>
                                <poe xmlns="http://pica8.com/xorplus/poe">
                                    <interface>
                                        <all>
                                            <enable>true</enable>
                                        </all>
                                    </interface>
                                </poe>
                            </config>
                        """
                        response = m.edit_config(target='running', config=poe_xml)
                        LOG.info("set poe success: ", str(response))
                except Exception as e:
                    LOG.error("set poe enable failed: ", str(e))

                operations = {"old_val": "delete", "new_val": ""}
                for key, op in operations.items():
                    config = template_data.get(key, {})
                    if not config:
                        continue

                    if "bgp" in config and config.get("bgp", {}):
                        protocol = "bgp"
                    else:
                        protocol = "ospf"

                    common_config_data = {k: v for k, v in config.items() if k not in {"bgp", "ospf"}}

                    if common_config_data:
                        func = getattr(self, f"{role_type.lower()}_common_config")
                        common_xml.append(func(op, protocol, **common_config_data))

                    if role_type == "core":
                        if config.get(protocol):
                            handler = getattr(self, f"core_{protocol}")
                            if key == "old_val" and protocol == "bgp":
                                delete_bgp_dict = handler(op, **config)
                                bgp_or_ospf_xml.append({k: v for k, v in delete_bgp_dict.items() if k != "bgp_xml"})
                                delete_bgp_xml = "\n".join(delete_bgp_dict["bgp_xml"])
                                delete_bgp_xml = f"""
                                    <config>
                                        <bgp xmlns="http://pica8.com/xorplus/bgp">
                                            {delete_bgp_xml}
                                        </bgp>
                                    </config>
                                """
                                del_ret = m.edit_config(target='running', config=delete_bgp_xml)
                                if "ok" in str(del_ret):
                                    asn = config.get("asn", "")
                                    config_xml = f"""
                                        <config>
                                            <bgp xmlns="http://pica8.com/xorplus/bgp">
                                                <local-as operation="delete">{asn}</local-as>
                                            </bgp>
                                        </config>
                                    """
                                m.edit_config(target='running', config=config_xml)
                            else:
                                bgp_or_ospf_xml.append(handler(op, **config))
                deploy_config_xml = self.adapt_data_process(common_xml, bgp_or_ospf_xml)
                m.edit_config(target='running', config=deploy_config_xml)
                self.update_campus_topology(meta_data, template_data.get("new_val", {}))
        except Exception as e:
            LOG.error(f"Error while deploying NetConf config: {traceback.format_exc()}")
            deploy_status["status"] = 500
            deploy_status["msg"] = str(traceback.format_exc())
        else:
            deploy_status["status"] = 200
            deploy_status["msg"] = f"Configuration and commit successful."
        finally:
            return deploy_status

    def deploy_netconf_config_by_clos(self, role_type, meta_data, **template_data):
        deploy_info = []
        deploy_status = {}
        set_lines, del_lines = [], []
        final_config = ""
        try:
            with manager.connect(
                    host=self.host,
                    port=self.port,
                    username=self.username,
                    password=self.password,
                    hostkey_verify=False,
                    timeout=300
            ) as m:
                try:
                    if template_data["meta"]["role"] == "access":
                        poe_xml = """
                            <config>
                                <poe xmlns="http://pica8.com/xorplus/poe">
                                    <interface>
                                        <all>
                                            <enable>true</enable>
                                        </all>
                                    </interface>
                                </poe>
                            </config>
                        """
                        response = m.edit_config(target='running', config=poe_xml)
                        LOG.info("set poe success: ", str(response))
                except Exception as e:
                    LOG.error("set poe enable failed: ", str(e))

                start_l3_vni = 13965
                operations = {"old_val": "delete", "new_val": ""}
                for key, op in operations.items():
                    config = template_data.get(key, {})
                    if not config:
                        continue
                    if key == "new_val":
                        if role_type in ["distribution", "border"]:
                            asn = config.get("bgp", {}).get("asn", "")
                        elif role_type in ["access", "core"]:
                            asn = config.get("underlay", {}).get("asn", "")
                        set_cli_config = [
                            f"set protocols bgp local-as {asn}",
                            f"set protocols bgp bestpath as-path multipath-relax",
                            f"commit"
                        ]
                        final_config = "\n".join(set_cli_config)
                        result, status = interactive_shell_configure(cmd=final_config, hostname=self.host,
                                                                     username=self.username,
                                                                     password=self.password, port=22)
                        if status != C.RMA_ACTIVE:
                            LOG.error(f"Configuration failed on host {self.host}: {result}")
                    if key == "old_val" and role_type in ("access", "core"):
                        del_lines = [
                            f"delete vxlans",
                            f"commit"
                        ]
                        final_config = "\n".join(del_lines)
                        result, status = interactive_shell_configure(cmd=final_config, hostname=self.host,
                                                                     username=self.username,
                                                                     password=self.password, port=22)
                        if status != C.RMA_ACTIVE:
                            LOG.error(f"Configuration failed on host {self.host}: {result}")

                    # if role_type in ["distribution", "border"]:
                    #     deploy_info.append(self.clos_common_direct_route_port(role_type, op, **config))
                    #     if op == "delete":
                    #         delete_bgp_dict = self.clos_ebgp(role_type, op, **config)
                    #         deploy_info.append({k: v for k, v in delete_bgp_dict.items() if k != "bgp_xml"})
                    #         delete_bgp_xml = "\n".join(delete_bgp_dict["bgp_xml"])
                    #         delete_bgp_xml = f"""
                    #             <config>
                    #                 <bgp xmlns="http://pica8.com/xorplus/bgp">
                    #                     {delete_bgp_xml}
                    #                 </bgp>
                    #             </config>
                    #         """
                    #         del_ret = m.edit_config(target='running', config=delete_bgp_xml)
                    #         if "ok" in str(del_ret):
                    #             asn = config.get("asn", "")
                    #             config_xml = f"""
                    #                 <config>
                    #                     <bgp xmlns="http://pica8.com/xorplus/bgp">
                    #                         <local-as operation="delete">{asn}</local-as>
                    #                     </bgp>
                    #                 </config>
                    #             """
                    #             m.edit_config(target='running', config=config_xml)
                    #     deploy_info.append(self.clos_ebgp(role_type, op, **config))
                    # elif role_type in ["access", "core"]:
                    #     for func in [getattr(self, func) for func in dir(self) if
                    #                  func.startswith("clos_") and func != "clos_ebgp"]:
                    #         deploy_info.append(func(role_type, op, **config))
                    if role_type in ["distribution", "border"]:
                        deploy_info.append(self.clos_common_direct_route_port(role_type, op, **config))
                        deploy_info.append(self.clos_ebgp(role_type, op, **config))
                    elif role_type in ["access", "core"]:
                        for func in [getattr(self, func) for func in dir(self) if
                                     func.startswith("clos_") and func != "clos_ebgp"]:
                            deploy_info.append(func(role_type, op, **config))
                deploy_config_xml = self.adapt_data_process(deploy_info)
                print(deploy_config_xml)
                m.edit_config(target='running', config=deploy_config_xml)
                if template_data.get("new_val", {}) and role_type in ["access", "core"]:
                    new_val = template_data.get("new_val", {})
                    underlay, vlans, vrfs, wan = (new_val.get("underlay", {}), new_val.get("vlans", {}),
                                                  new_val.get("vrfs", {}), new_val.get("wan", {}))
                    router_id = underlay.get("router_id", "")
                    vni_xml_list = []
                    for vlan_name in vlans:
                        vlan = vlans[vlan_name]
                        vni_id = str(int(vlan["vlan_id"]) + 10000)
                        vlan_id = vlan["vlan_id"]
                        vni_xml_list.extend([
                            f"set vxlans vni {vni_id} decapsulation mode service-vlan-per-port",
                            f"set vxlans vni {vni_id} vlan {vlan_id}",
                            f"set vxlans vni {vni_id} arp-nd-suppress disable false"
                        ])
                    start_vni_id = 13965
                    vrfs = template_data.get("new_val", {}).get("vrfs", {})
                    set_lines = [
                        f"set vxlans source-interface lo address {router_id}",
                        f"set protocols bgp evpn advertise-svi-ip",
                    ]
                    if wan and role_type in ("core", "border"):
                        set_lines += [
                            f"set protocols bgp ipv4-unicast import vrf vrfwan",
                        ]
                        set_lines += [
                            f"set protocols bgp vrf {vrf} ipv4-unicast import vrf vrfwan" for vrf in vrfs
                        ]
                    set_lines += [
                        f"set vxlans vni {start_vni_id - index} vlan {start_vni_id - index - 10000}"
                        for index, vrf_name in enumerate(vrfs)
                    ]
                    set_lines += [
                        f"set vxlans vrf {vrf_name} l3-vni {start_l3_vni - index}"
                        for index, vrf_name in enumerate(vrfs)
                    ]
                    set_lines += vni_xml_list + ["commit"]
                    final_config = "\n".join(set_lines)
                    result, status = interactive_shell_configure(cmd=final_config, hostname=self.host,
                                                                 username=self.username,
                                                                 password=self.password, port=22)
                    if status != C.RMA_ACTIVE:
                        LOG.error(f"Configuration failed on host {self.host}: {result}")
                if template_data.get("old_val", {}):
                    del_cli_config = [
                        f"delete protocols bgp bestpath",
                        f"commit",
                        f"delete protocols bgp local-as",
                        f"commit"
                    ]
                    final_config = "\n".join(del_cli_config)
                    result, status = interactive_shell_configure(cmd=final_config, hostname=self.host,
                                                                 username=self.username,
                                                                 password=self.password, port=22)
                    if status != C.RMA_ACTIVE:
                        LOG.error(f"Configuration failed on host {self.host}: {result}")
                self.update_campus_topology(meta_data, template_data.get("new_val", {}))
        except Exception as e:
            LOG.error(f"Error while deploying NetConf config: {traceback.format_exc()}")
            deploy_status["status"] = 500
            deploy_status["msg"] = str(traceback.format_exc())
        else:
            deploy_status["status"] = 200
            deploy_status["msg"] = f"Configuration and commit successful."
        finally:
            return deploy_status

    def deploy_netconf_config_for_dc(self, role_type, meta_data, **template_data):
        config_xml = []
        delete_executed_keys = set()
        deploy_status = {}
        try:
            with manager.connect(
                host=self.host,
                port=self.port,
                username=self.username,
                password=self.password,
                hostkey_verify=False,
                timeout=300
            ) as m:
                operations = {"old_val": "delete", "new_val": ""}
                for key, op in operations.items():
                    config_data = template_data.get(key, {})
                    if not config_data:
                        continue
                    delete_bgp_tag = ""
                    delete_bgp_asn = ""
                    if "bgp" in config_data and config_data.get("bgp", {}):
                        delete_bgp_tag = "bgp"
                        delete_bgp_asn = config_data["bgp"].get("asn", "")
                    elif "overlay" in config_data and config_data.get("overlay", {}):
                        delete_bgp_tag = "overlay"
                        delete_bgp_asn = config_data["overlay"].get("overlay_ibgp_asn", "")
                    for k, _ in config_data.items():
                        if k == "hostname":
                            handler = getattr(self, "dc_common_config")
                            config_xml.append(handler(op, **config_data))
                        else:
                            handler = getattr(self, f"dc_{k.lower()}")
                            if key == "old_val" and k == delete_bgp_tag:
                                delete_bgp_dict = handler(role_type, op, **config_data)
                                config_xml.append({xml: item for xml, item in delete_bgp_dict.items() if xml != "bgp_xml"})
                                delete_bgp_xml = "\n".join(delete_bgp_dict["bgp_xml"])
                                delete_executed_keys.add(k)
                                if "overlay" in config_data and "overlay" != k:
                                    overlay_handler = getattr(self, "dc_overlay")
                                    overlay_dict = overlay_handler(role_type, op, **config_data)
                                    config_xml.append({xml: item for xml, item in overlay_dict.items() if xml != "bgp_xml" and xml != "bgp_evpn_xml"})
                                    delete_bgp_xml += "\n".join(overlay_dict["bgp_xml"])
                                    delete_executed_keys.add("overlay")
                                delete_bgp_xml = f"""
                                    <config>
                                        <bgp xmlns="http://pica8.com/xorplus/bgp">
                                            {delete_bgp_xml}
                                        </bgp>
                                    </config>
                                """
                                del_ret = m.edit_config(target='running', config=delete_bgp_xml)
                                if "ok" in str(del_ret):
                                    delete_config_xml = f"""
                                        <config>
                                            <bgp xmlns="http://pica8.com/xorplus/bgp">
                                                <local-as operation="delete">{delete_bgp_asn}</local-as>
                                            </bgp>
                                        </config>
                                    """
                                    m.edit_config(target='running', config=delete_config_xml)
                            elif (key == "old_val" and k not in delete_executed_keys) or key == "new_val":
                                handler = getattr(self, f"dc_{k.lower()}")
                                config_xml.append(handler(role_type, op, **config_data))
                deploy_config_xml = self.adapt_data_process(config_xml)
                result = m.edit_config(target='running', config=deploy_config_xml)
                self.update_dc_topology(meta_data, template_data.get("new_val", {}))
        except Exception as e:
            LOG.error(f"Error while deploying NetConf config: {traceback.format_exc()}")
            deploy_status["status"] = 500
            deploy_status["msg"] = str(traceback.format_exc())
        else:
            deploy_status["status"] = 200
            deploy_status["msg"] = f"Configuration and commit successful."
        finally:
            return deploy_status

    @staticmethod
    def st_netconf_network(op, network):
        if not network:
            return None
        xml_dict = {}
        network_info = network.get("networks", [])
        if not network_info:
            return None
        xml_dict["vlans_xml"] = []
        operation = "" if op == "" else f'operation="{op}"'
        for vlan in network_info:
            vlan_id = vlan.get("id")
            interface_name = vlan.get("name")
            # 生成添加 / 删除 VLAN 的 XML 配置字符串
            xml = f"""
                    <vlan-id {operation}>
                        <id>{vlan_id}</id>
                        <vlan-name>default</vlan-name>
                        <l3-interface>{interface_name}</l3-interface>
                    </vlan-id>
                """
            xml_dict["vlans_xml"].append(xml.replace("\n", ""))
        return xml_dict

    @staticmethod
    def st_netconf_ntp(op, info):
        if not info:
            return None
        xml_dict = {}
        ip_list = info.get("ip")
        if not ip_list:
            return None
        operation = "" if op == "" else f'operation="{op}"'
        ip_xml = ""
        for ip in ip_list:
            xml = f"""
                    <server-ip>
                        <ip-address>{ip}</ip-address>
                    </server-ip>
                """
            ip_xml += xml
        ntp_config_xml = "<ntp {}>{}</ntp>".format(operation, ip_xml)
        xml_dict["system_xml"] = [ntp_config_xml.replace("\n", "")]
        return xml_dict

    @staticmethod
    def st_netconf_dns(op, info):
        if not info:
            return None
        xml_dict = {}
        ip_list = info.get("ip")
        if not ip_list:
            return None
        operation = "" if op == "" else f'operation="{op}"'
        ip_xml = ""
        for ip in ip_list:
            xml = f"""
                    <dns-server-ip {operation}>
                        <ip-address>{ip}</ip-address>
                    </dns-server-ip>
                """
            ip_xml += xml
        xml_dict["system_xml"] = [ip_xml]
        return xml_dict

    @staticmethod
    def st_netconf_staticRoute(op, info):
        if not info or not info.get("configuration", ""):
            return None
        operation = "" if op == "" else f'operation="{op}"'
        xml_dict = {}

        config_status = info.get("configuration")
        xml_dict["ip_xml"] = [f"""
                <routing {operation}>
                    <enable>{"true" if config_status else "false"}</enable>
                </routing>
            """]

        routes = info.get("routes")
        static_xml = ""
        for route in routes:
            destination = route.get("destination")
            next_hop_list = route.get("nexthop")
            next_hop_xml = ""
            for next_hop in next_hop_list:
                xml = f"""
                        <next-hop>
                            <ip-address>{next_hop}</ip-address>
                        </next-hop>
                    """
                next_hop_xml += xml
            xml = f"""
                  <route {operation}>
                    <name>{destination}</name>
                    {next_hop_xml}
                  </route>
                """
            static_xml += xml
        xml_dict["static_xml"] = [static_xml]
        return xml_dict

    @staticmethod
    def st_netconf_vrf(op, info):
        if not info:
            return None
        operation = "" if op == "" else f'operation="{op}"'
        xml_dict = {}

        # status = info.get("configuration")
        # routing_xml = f"""
        #     <routing>
        #         <enable>{"true" if status else "false"}</enable>
        #     </routing>
        # """

        network_list = info.get("networks")
        if not network_list:
            return None
        vrf_xml = ""
        interface_xml = ""
        for item in network_list:
            vrf_name = item.get("name")
            vrf_name_xml = f"""
                <vrf {operation}>
                    <name>{vrf_name}</name>
                </vrf>
            """
            vrf_xml += vrf_name_xml

            interface_name_list = item.get("id")
            for interface_name in interface_name_list:
                interface_name = interface_name.split("(")[0]
                interface_name_xml = f"""
                    <vlan-interface {operation}>
                        <name>{interface_name}</name>
                        <disable>false</disable>
                        <dhcp>false</dhcp>
                        <vrf>{vrf_name}</vrf>
                        <mtu>1500</mtu>
                    </vlan-interface>
                """
                interface_xml += interface_name_xml

        ip_xml = vrf_xml  # routing_xml + vrf_xml
        xml_dict["ip_xml"] = [ip_xml]
        xml_dict["l3-interface_xml"] = [interface_xml]
        return xml_dict

    @staticmethod
    def st_netconf_ospf(op, info):
        if not info or not info.get("configuration", ""):
            return None
        operation = "" if op == "" else f'operation="{op}"'
        xml_dict = {}
        area_xml = ""
        ipv4_xml = ""
        ospf_list = info.get("areas")
        for item in ospf_list:
            Area_ID = item.get("id")
            Area_Type = item.get("type")
            if Area_Type != "default":
                xml = f"""
                    <area {operation}>
                        <ip>{Area_ID}</ip>
                        <area-type>{Area_Type}</area-type>
                    </area>
                """
                area_xml += xml
            IPv4_Prefixlen_list = item.get("ipv4")
            for ipv4 in IPv4_Prefixlen_list:
                xml = f"""
                    <network {operation}>
                        <net>{ipv4}</net>
                        <area>{Area_ID}</area>
                    </network>
                """
                ipv4_xml += xml
        ospf_xml = area_xml + ipv4_xml
        xml_dict["ospf_xml"] = [ospf_xml]
        return xml_dict


    @staticmethod
    def st_netconf_port_config(op, port, info):

        def extract_parentheses_content(text: str) -> str:
            """
            提取字符串中第一对圆括号里的内容。

            参数
            ----
            text : str
                输入字符串，例如 "vlan20(20)"。

            返回
            ----
            str
                括号里的内容；若找不到则返回空字符串。
            """
            match = re.search(r'\((.*?)\)', text)
            return match.group(1) if match else ""

        if port == "" or not info:
            return None
        operation = "" if op == "" else f'operation="{op}"'
        xml_dict = {}
        if info.get("portEnable") == "enable":
            port_enable = "false"
        elif info.get("portEnable") == "disable":
            port_enable = "true"
        else:
            return None

        port_mode = info.get("portMode")
        port_mode_xml = ""
        if port_mode == "trunk":
            trunk_network_list = info.get("trunkNetwork")
            if trunk_network_list[0] == "all networks":
                port_mode_xml = f"""
                                <port-mode>trunk</port-mode>
                                <vlan>
                                    <members>
                                        <id>1-4094</id>
                                    </members>
                                </vlan>
                            """
            else:
                vlan_id_list = []
                for vlan in trunk_network_list:
                    vlan_id = extract_parentheses_content(vlan)
                    vlan_id_list.append(vlan_id)
                vlan_ids_str = ",".join(vlan_id_list)
                port_mode_xml = f"""
                    <port-mode>trunk</port-mode>
                    <vlan>
                        <members>
                            <id>{vlan_ids_str}</id>
                        </members>
                    </vlan>
                """
        elif port_mode == "access":
            port_mode_xml = f"""
                <port-mode>access</port-mode>
            """
        else:
            return None

        port_network = extract_parentheses_content(info.get("portNetwork"))

        storm_control = info.get("stormControl")
        storm_control_xml = ""
        if storm_control == "enable":
            type_list = info.get("modes")
            control_method = info.get("controlMethond")
            value = info.get("percentage")
            method_value_xml = f"""
                <{control_method}>{value}</{control_method}>
            """
            type_xml = ""
            for type in type_list:
                if type == "broadcast":
                    xml = f"""
                        <broadcast>
                            {method_value_xml}
                        </broadcast>
                    """
                    type_xml += xml
                elif type == "multicast":
                    xml = f"""
                        <multicast>
                            {method_value_xml}
                        </multicast>
                    """
                    type_xml += xml
                elif type == "unicast":
                    xml = f"""
                        <unicast>
                            {method_value_xml}
                        </unicast>
                    """
                    type_xml += xml

            storm_control_xml = f"""
                <storm-control {operation}>
                    {type_xml}
                </storm-control>
            """

        xml = f"""
            <gigabit-ethernet>
                <name>{port}</name>
                <disable>{port_enable}</disable>
                <family {operation}>
                    <ethernet-switching>
                        <native-vlan-id>{port_network}</native-vlan-id>
                        {port_mode_xml}
                    </ethernet-switching>
                </family>
                {storm_control_xml}
            </gigabit-ethernet>
        """
        xml_dict["interface_xml"] = [xml]
        return xml_dict


    @staticmethod
    def st_netconf_localUser(op, info):
        # 密码加密
        def get_encrypted_password(clear_text_passwords):
            salt = "$6$" + os.urandom(8).hex() + "$"
            encrypted_password = crypt.crypt(clear_text_passwords, salt)
            return encrypted_password

        if not info:
            return None
        operation = "" if op == "" else f'operation="{op}"'
        xml_dict = {}
        user_list = info.get("users")
        if not user_list:
            return None
        user_xml = ""
        for user in user_list:
            name = user.get("name")
            level = user.get("level")
            password = get_encrypted_password(user.get("password"))
            xml = f"""
                    <user {operation}>
                        <name>{name}</name>
                        <authentication>
                            <plain-text-password>{password}</plain-text-password>
                        </authentication>
                        <class>{level}</class>
                    </user>
                """
            user_xml += xml
        xml_dict["system_xml"] = ["<login>" + user_xml + "</login>"]
        return xml_dict

    @staticmethod
    def st_netconf_loginBanner(op, info):
        if not info:
            return None
        operation = "" if op == "" else f'operation="{op}"'
        xml_dict = {}

        before = info.get("before")
        after = info.get("after")

        if before == "" and after == "":
            return None

        before_list = before.split('\n')
        after_list = after.split('\n')

        before_xml = ""
        if len(before_list) == 1 and before != "":
            before_xml = f"""
                    <banner {operation}>{before_list[0]}</banner>
                """
        elif len(before_list) > 1:
            for i, text in enumerate(before_list):
                xml = f"""
                        <multiline-banner {operation}>
                            <number>{i + 1}</number>
                            <message>{text}</message>
                        </multiline-banner>
                    """
                before_xml += xml

        after_xml = ""
        if len(after_list) == 1 and after != "":
            after_xml = f"""
                    <announcement {operation}>{after_list[0]}</announcement>
                """
        elif len(after_list) > 1:
            for i, text in enumerate(after_list):
                xml = f"""
                        <multiline-announcement {operation}>
                            <number>{i + 1}</number>
                            <message>{text}</message>
                        </multiline-announcement>
                    """
                after_xml += xml

        login_xml = f"""
                <login>
                    {before_xml}
                    {after_xml}
                </login>
            """
        xml_dict["system_xml"] = [login_xml]
        return xml_dict

    @staticmethod
    def st_netconf_timeout(op, info):
        if not info:
            return None
        operation = "" if op == "" else f'operation="{op}"'
        xml_dict = {}

        timeout = info
        xml = f"""
                <services {operation}>
                    <ssh>
                        <protocol-version>v2</protocol-version>
                        <idle-timeout>{timeout}</idle-timeout>
                    </ssh>
                </services>
            """
        xml_dict["system_xml"] = [xml]
        return xml_dict

    def switch_templates_delete_config(self, template_data):
        """
        删除交换机已有的template
        """
        def format_xml(xml_str):
            """
            整理xml字符串,去除多的table空格和换行
            """
            parser = etree.XMLParser(remove_blank_text=True)  # 移除多余空白
            root = etree.fromstring(xml_str.encode(), parser)
            return etree.tostring(root, pretty_print=True, encoding='unicode')

        deploy_status = {}
        delete_is_ok_dict = {}
        netconf_xml_dict = {
            "switch": {},
            "management": {}
        }
        try:
            with manager.connect(
                    host=self.host,
                    port=self.port,
                    username=self.username,
                    password=self.password,
                    hostkey_verify=False,
                    timeout=300,
            ) as m:
                # switch
                # 删除旧模板的配置,包括ospf,ntp,dns,staticRoute,vrf,network
                delete_config_name_list = ["ospf", "ntp", "dns", "staticRoute", "vrf", "network"]
                delete_switch_info = template_data.get("switch")
                if delete_switch_info:
                    for delete_config_name in delete_config_name_list:
                        handler = getattr(self, f"st_netconf_{delete_config_name}")
                        delete_xml_dict = handler("delete", delete_switch_info.get(delete_config_name, {}))
                        if delete_xml_dict:
                            delete_xml_list = [delete_xml_dict]
                            delete_xml = self.adapt_data_process(delete_xml_list)
                            formatted_xml = format_xml(delete_xml)
                            netconf_xml_dict["switch"][delete_config_name] = formatted_xml
                            print(f"Switch: Delete XML: {formatted_xml}")
                            try:
                                delete_response = m.edit_config(target="running", config=formatted_xml)
                            except Exception as e:
                                current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                                delete_is_ok_dict[
                                    delete_config_name] = f"{current_time} : {delete_config_name} configuration deleted failed!\n"
                                # raise
                            else:
                                current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                                print(f"Switch: Delete Response: {delete_response}")
                                if "ok" in str(delete_response):
                                    delete_is_ok_dict[
                                        delete_config_name] = f"{current_time} : {delete_config_name} configuration has been successfully deleted!\n"
                                else:
                                    delete_is_ok_dict[
                                        delete_config_name] = f"{current_time} : {delete_config_name} configuration deleted failed!\n"

                    # 删除旧的端口配置
                    old_port_configuration_list = delete_switch_info.get("portConfiguration")
                    old_port_config_application_list = delete_switch_info.get("portConfigApplication")
                    if old_port_configuration_list and old_port_config_application_list:
                        old_port_config_dict = {}
                        for port_config in old_port_configuration_list:
                            old_port_config_dict[port_config.get("name")] = port_config
                        print(old_port_config_dict)
                        for old_port in old_port_config_application_list:
                            delete_port_config_dict = NetConfConfigDeployer.st_netconf_port_config(
                                "delete",
                                old_port.get("port"),
                                old_port_config_dict[old_port.get("profile")]
                            )
                            print(delete_port_config_dict)
                            if delete_port_config_dict:
                                log_str = f"{old_port.get('port', '')}-{old_port_config_dict[old_port.get('profile', '')].get('name', '')}"
                                delete_xml_list = [delete_port_config_dict]
                                delete_xml = self.adapt_data_process(delete_xml_list)
                                formatted_xml = format_xml(delete_xml)
                                netconf_xml_dict["switch"]["port_config"] = formatted_xml
                                print(f"Switch: Delete XML: {formatted_xml}")
                                try:
                                    delete_response = m.edit_config(target="running", config=formatted_xml)
                                except Exception as e:
                                    current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                                    delete_is_ok_dict[
                                        "port_config"] = f"{current_time} : {log_str} port_config configuration deleted failed!\n"
                                    # raise
                                else:
                                    current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                                    print(f"Switch: Delete Response: {delete_response}")
                                    if "ok" in str(delete_response):
                                        delete_is_ok_dict[
                                            "port_config"] = f"{current_time} : {log_str} port_config configuration has been successfully deleted!\n"
                                    else:
                                        delete_is_ok_dict[
                                            "port_config"] = f"{current_time} : {log_str} port_config configuration deleted failed!\n"
                            try:
                                speed_port = old_port.get("port")
                                speed_value = old_port_config_dict[old_port.get("profile")].get("speed")
                                log_str = f"{old_port_config_dict[old_port.get('profile', '')].get('name', '')}-{speed_port}-{speed_value}"
                                if speed_port and speed_value:
                                    speed_xml = f"""
                                        <config>
                                            <interface xmlns="http://pica8.com/xorplus/interface">
                                                <gigabit-ethernet>
                                                    <name>{speed_port}</name>
                                                    <speed operation="delete">{speed_value}</speed>
                                                </gigabit-ethernet>
                                            </interface>
                                        </config>
                                    """
                                    speed_response = m.edit_config(target="running", config=speed_xml)
                                    current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                                    if "ok" in str(speed_response):
                                        delete_is_ok_dict[
                                            f"delete-{speed_port}-{speed_value}"] = f"{current_time} : {log_str} delete-speed configuration has been successfully deleted!\n"
                                    else:
                                        delete_is_ok_dict[
                                            f"delete-{speed_port}-{speed_value}"] = f"{current_time} : {log_str} delete-speed configuration deleted failed!\n"
                            except Exception as e:
                                current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                                delete_is_ok_dict[
                                    f"delete-{speed_port}-{speed_value}"] = f"{current_time} : {log_str} delete-speed configuration deleted failed!\n"
                                print(f"set poe enable failed: {str(e)}")

                # management
                # 删除旧的management
                management_delete_config_name_list = ["localUser", "loginBanner", "timeout"]
                delete_management_info = template_data.get("management")
                if delete_management_info:
                    for delete_config_name in management_delete_config_name_list:
                        handler = getattr(self, f"st_netconf_{delete_config_name}")
                        delete_xml_dict = handler("delete",
                                                  delete_management_info.get(delete_config_name, {}))
                        if delete_xml_dict:
                            delete_xml_list = [delete_xml_dict]
                            delete_xml = self.adapt_data_process(delete_xml_list)
                            formatted_xml = format_xml(delete_xml)
                            netconf_xml_dict["management"][delete_config_name] = formatted_xml
                            print(f"Management: Delete XML: {formatted_xml}")
                            try:
                                delete_response = m.edit_config(target="running", config=formatted_xml)
                            except Exception as e:
                                current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                                delete_is_ok_dict[
                                    delete_config_name] = f"{current_time} : {delete_config_name} configuration deleted failed!\n"
                                # raise
                            else:
                                current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                                print(f"Management: Delete Response: {delete_response}")
                                if "ok" in str(delete_response):
                                    delete_is_ok_dict[
                                        delete_config_name] = f"{current_time} : {delete_config_name} configuration has been successfully deleted!\n"
                                else:
                                    delete_is_ok_dict[
                                        delete_config_name] = f"{current_time} : {delete_config_name} configuration deleted failed!\n"

        except socket.timeout:
            current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            deploy_status["status"] = 501
            deploy_status["msg"] = f"{current_time} : [Timeout] Unable to connect to {self.host}:{self.port} within 300 seconds."
            deploy_status["delete_is_ok"] = {}
            deploy_status["delete_netconf_xml_dict"] = netconf_xml_dict
            print(f"{current_time} : [Timeout] Unable to connect to {self.host}:{self.port} within 300 seconds.\n{traceback.format_exc()}")
        except AuthenticationError:
            current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            deploy_status["status"] = 502
            deploy_status["msg"] = f"{current_time} : [Authentication failed] Username or password incorrect."
            deploy_status["delete_is_ok"] = {}
            deploy_status["delete_netconf_xml_dict"] = netconf_xml_dict
            print(f"{current_time} : [Authentication failed] Username or password incorrect.\n{traceback.format_exc()}")
        except SSHError as e:
            current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            deploy_status["status"] = 503
            deploy_status["msg"] = f"{current_time} : [SSH error] An error occurred while establishing an SSH connection with {self.host}:{self.port}. Please check if the device is functioning properly, or try again later."
            deploy_status["delete_is_ok"] = {}
            deploy_status["delete_netconf_xml_dict"] = netconf_xml_dict
            print(f"{current_time} : [SSH error] An error occurred while establishing an SSH connection with {self.host}:{self.port}. Please check if the device is functioning properly, or try again later.\n{traceback.format_exc()}")
        except Exception as e:
            current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            deploy_status["status"] = 500
            deploy_status["msg"] = f"{current_time} : [Unknown error] Some unknown errors have occurred, please try again later."
            deploy_status["delete_is_ok"] = delete_is_ok_dict
            deploy_status["delete_netconf_xml_dict"] = netconf_xml_dict
            print(f"{current_time} : [Unknown error] Some unknown errors have occurred, please try again later.\n{traceback.format_exc()}")
        else:
            current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            deploy_status["status"] = 200
            deploy_status["msg"] = f"{current_time} : Successfully deleted all template configurations"
            deploy_status["delete_is_ok"] = delete_is_ok_dict
            deploy_status["delete_netconf_xml_dict"] = netconf_xml_dict
            print(f"{current_time} : Successfully deleted all template configurations")
        finally:
            return deploy_status

    def switch_templates_add_config(self, template_data):
        """
        添加新的switch template
        """
        def format_xml(xml_str):
            """
            整理xml字符串,去除多的table空格和换行
            """
            parser = etree.XMLParser(remove_blank_text=True)  # 移除多余空白
            root = etree.fromstring(xml_str.encode(), parser)
            return etree.tostring(root, pretty_print=True, encoding='unicode')

        deploy_status = {}
        add_is_ok_dict = {}
        netconf_xml_dict = {
            "switch": {},
            "management": {}
        }
        try:
            with manager.connect(
                    host=self.host,
                    port=self.port,
                    username=self.username,
                    password=self.password,
                    hostkey_verify=False,
                    timeout=300,
            ) as m:
                # 配置新模板的配置,包括network,ospf,ntp,dns,vrf,staticRoute
                add_config_name_list = ["network", "ospf", "ntp", "dns", "vrf", "staticRoute"]
                add_switch_info = template_data.get("switch", {})
                if add_switch_info:
                    for add_config_name in add_config_name_list:
                        handler = getattr(self, f"st_netconf_{add_config_name}")
                        add_xml_dict = handler("", add_switch_info.get(add_config_name, {}))
                        if add_xml_dict:
                            add_xml_list = [add_xml_dict]
                            add_xml = self.adapt_data_process(add_xml_list)
                            formatted_xml = format_xml(add_xml)
                            netconf_xml_dict["switch"][add_config_name] = formatted_xml
                            print(f"Switch: Add XML: {formatted_xml}")
                            try:
                                add_response = m.edit_config(target="running", config=formatted_xml)
                            except Exception as e:
                                current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                                add_is_ok_dict[
                                    add_config_name] = f"{current_time} : {add_config_name} configuration added failed!\n"
                                raise
                            else:
                                current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                                print(f"Switch: Add Response: {add_response}")
                                if "ok" in str(add_response):
                                    add_is_ok_dict[
                                        add_config_name] = f"{current_time} : {add_config_name} configuration has been successfully added!\n"
                                else:
                                    add_is_ok_dict[
                                        add_config_name] = f"{current_time} : {add_config_name} configuration added failed!\n"

                    # 添加新的端口配置
                    new_port_configuration_list = add_switch_info.get("portConfiguration")
                    new_port_config_application_list = add_switch_info.get("portConfigApplication")
                    if new_port_configuration_list and new_port_config_application_list:
                        new_port_config_dict = {}
                        for port_config in new_port_configuration_list:
                            new_port_config_dict[port_config.get("name")] = port_config
                        print(new_port_config_dict)
                        for new_port in new_port_config_application_list:
                            add_port_config_dict = NetConfConfigDeployer.st_netconf_port_config(
                                "",
                                new_port.get("port"),
                                new_port_config_dict[new_port.get("profile")]
                            )
                            print(add_port_config_dict)
                            if add_port_config_dict:
                                log_str = f"{new_port.get('port', '')}-{new_port_config_dict[new_port.get('profile', '')].get('name', '')}"
                                add_xml_list = [add_port_config_dict]
                                add_xml = self.adapt_data_process(add_xml_list)
                                formatted_xml = format_xml(add_xml)
                                netconf_xml_dict["switch"]["port_config"] = formatted_xml
                                print(f"Switch: Add XML: {formatted_xml}")
                                try:
                                    add_response = m.edit_config(target="running", config=formatted_xml)
                                except Exception as e:
                                    current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                                    add_is_ok_dict[
                                        "port_config"] = f"{current_time} : {log_str} port_config configuration added failed!\n"
                                    raise
                                else:
                                    current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                                    print(f"Switch: Add Response: {add_response}")
                                    if "ok" in str(add_response):
                                        add_is_ok_dict[
                                            "port_config"] = f"{current_time} : {log_str} port_config configuration has been successfully added!\n"
                                    else:
                                        add_is_ok_dict[
                                            "port_config"] = f"{current_time} : {log_str} port_config configuration added failed!\n"
                            try:
                                if new_port.get("poe") == "enable":
                                    poe_status = "true"
                                elif new_port.get("poe") == "disable":
                                    poe_status = "false"
                                poe_xml = f"""
                                            <config>
                                                <poe xmlns="http://pica8.com/xorplus/poe">
                                                    <interface>
                                                        <all>
                                                            <enable>{poe_status}</enable>
                                                        </all>
                                                    </interface>
                                                </poe>
                                            </config>
                                        """
                                poe_response = m.edit_config(target="running", config=poe_xml)
                                current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                                if "ok" in str(poe_response):
                                    add_is_ok_dict[
                                        "poe"] = f"{current_time} : poe configuration has been successfully added!\n"
                                else:
                                    add_is_ok_dict["poe"] = f"{current_time} : poe configuration added failed!\n"
                            except Exception as e:
                                current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                                add_is_ok_dict["poe"] = f"{current_time} : poe configuration added failed!\n"
                                print(f"set poe enable failed: {str(e)}")
                            try:
                                speed_port = new_port.get("port")
                                speed_value = new_port_config_dict[new_port.get("profile")].get("speed")
                                log_str = f"{new_port_config_dict[new_port.get('profile', '')].get('name', '')}-{speed_port}-{speed_value}"
                                if speed_port and speed_value:
                                    speed_xml = f"""
                                        <config>
                                            <interface xmlns="http://pica8.com/xorplus/interface">
                                                <gigabit-ethernet>
                                                    <name>{speed_port}</name>
                                                    <speed>{speed_value}</speed>
                                                </gigabit-ethernet>
                                            </interface>
                                        </config>
                                    """
                                    speed_response = m.edit_config(target="running", config=speed_xml)
                                    current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                                    if "ok" in str(speed_response):
                                        add_is_ok_dict[
                                            f"add-{speed_port}-{speed_value}"] = f"{current_time} : {log_str} add-speed configuration has been successfully added!\n"
                                    else:
                                        add_is_ok_dict[
                                            f"add-{speed_port}-{speed_value}"] = f"{current_time} : {log_str} add-speed configuration added failed!\n"
                            except Exception as e:
                                current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                                add_is_ok_dict["add-speed"] = f"{current_time} : {log_str} add-speed configuration added failed!\n"
                                print(f"set poe enable failed: {str(e)}")

                # 添加新的management
                management_add_config_name_list = ["localUser", "loginBanner", "timeout"]
                add_management_info = template_data.get("management")
                if add_management_info:
                    for add_config_name in management_add_config_name_list:
                        handler = getattr(self, f"st_netconf_{add_config_name}")
                        add_xml_dict = handler("", add_management_info.get(add_config_name, {}))
                        if add_xml_dict:
                            add_xml_list = [add_xml_dict]
                            add_xml = self.adapt_data_process(add_xml_list)
                            formatted_xml = format_xml(add_xml)
                            netconf_xml_dict["management"][add_config_name] = formatted_xml
                            print(f"Management: Add XML: {formatted_xml}")
                            try:
                                add_response = m.edit_config(target="running", config=formatted_xml)
                            except Exception as e:
                                current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                                add_is_ok_dict[add_config_name] = f"{current_time} : {add_config_name} configuration added failed!\n"
                                raise
                            else:
                                current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                                print(f"Management: Add Response: {add_response}")
                                if "ok" in str(add_response):
                                    add_is_ok_dict[add_config_name] = f"{current_time} : {add_config_name} configuration has been successfully added!\n"
                                else:
                                    add_is_ok_dict[add_config_name] = f"{current_time} : {add_config_name} configuration added failed!\n"

        except socket.timeout:
            current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            deploy_status["status"] = 501
            deploy_status["msg"] = f"{current_time} : [Timeout] Unable to connect to {self.host}:{self.port} within 300 seconds."
            deploy_status["add_is_ok"] = {}
            deploy_status["add_netconf_xml_dict"] = netconf_xml_dict
            print(f"{current_time} : [Timeout] Unable to connect to {self.host}:{self.port} within 300 seconds.\n{traceback.format_exc()}")
        except AuthenticationError:
            current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            deploy_status["status"] = 502
            deploy_status["msg"] = f"{current_time} : [Authentication failed] Username or password incorrect."
            deploy_status["add_is_ok"] = {}
            deploy_status["add_netconf_xml_dict"] = netconf_xml_dict
            print(f"{current_time} : [Authentication failed] Username or password incorrect.\n{traceback.format_exc()}")
        except SSHError as e:
            current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            deploy_status["status"] = 503
            deploy_status["msg"] = f"{current_time} : [SSH error] An error occurred while establishing an SSH connection with {self.host}:{self.port}. Please check if the device is functioning properly, or try again later."
            deploy_status["add_is_ok"] = {}
            deploy_status["add_netconf_xml_dict"] = netconf_xml_dict
            print(f"{current_time} : [SSH error] An error occurred while establishing an SSH connection with {self.host}:{self.port}. Please check if the device is functioning properly, or try again later.\n{traceback.format_exc()}")
        except Exception as e:
            current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            deploy_status["status"] = 500
            deploy_status["msg"] = f"{current_time} : [Unknown error] Some unknown errors have occurred, please try again later."
            deploy_status["add_is_ok"] = add_is_ok_dict
            deploy_status["add_netconf_xml_dict"] = netconf_xml_dict
            print(f"{current_time} : [Unknown error] Some unknown errors have occurred, please try again later.\n{traceback.format_exc()}")
        else:
            current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            deploy_status["status"] = 200
            deploy_status["msg"] = f"{current_time} : Successfully add all template configurations."
            deploy_status["add_is_ok"] = add_is_ok_dict
            deploy_status["add_netconf_xml_dict"] = netconf_xml_dict
            print(f"{current_time} : Successfully add all template configurations.")
        finally:
            return deploy_status

    def generate_xml(self, template_data, delete=False):
        def format_xml(xml_str):
            """
            整理xml字符串,去除多的table空格和换行
            """
            parser = etree.XMLParser(remove_blank_text=True)  # 移除多余空白
            root = etree.fromstring(xml_str.encode(), parser)
            return etree.tostring(root, pretty_print=True, encoding='unicode')

        # 配置新模板的配置,包括network,ospf,ntp,dns,vrf,staticRoute
        if delete:
            config_name_list = ["ospf", "ntp", "dns", "staticRoute", "vrf", "network"]
            is_delete = "delete"
        else:
            config_name_list = ["network", "ospf", "ntp", "dns", "vrf", "staticRoute"]
            is_delete = ""
        netconf_xml_dict = {
            "switch": {},
            "management": {}
        }
        switch_info = template_data.get("switch")
        if switch_info:
            for config_name in config_name_list:
                handler = getattr(self, f"st_netconf_{config_name}")
                xml_dict = handler(is_delete, switch_info.get(config_name, {}))
                if xml_dict:
                    xml_list = [xml_dict]
                    add_xml = self.adapt_data_process(xml_list)
                    formatted_xml = format_xml(add_xml)
                    netconf_xml_dict["switch"][config_name] = formatted_xml

            # 添加新的端口配置
            port_configuration_list = switch_info.get("portConfiguration")
            port_config_application_list = switch_info.get("portConfigApplication")
            if port_configuration_list and port_config_application_list:
                port_config_dict = {}
                for port_config in port_configuration_list:
                    port_config_dict[port_config.get("name")] = port_config
                for port in port_config_application_list:
                    xml_dict = NetConfConfigDeployer.st_netconf_port_config(
                        is_delete,
                        port.get("port"),
                        port_config_dict[port.get("profile")]
                    )
                    if xml_dict:
                        xml_list = [xml_dict]
                        xml = self.adapt_data_process(xml_list)
                        formatted_xml = format_xml(xml)
                        netconf_xml_dict["switch"][f"{port.get('port')}-port_config"] = (formatted_xml)

                    if port.get("poe") == "enable":
                        poe_status = "true"
                    elif port.get("poe") == "disable":
                        poe_status = "false"
                    else:
                        poe_status = "false"
                    poe_xml = f"""
                        <config>
                            <poe xmlns="http://pica8.com/xorplus/poe">
                                <interface>
                                    <all>
                                        <enable>{poe_status}</enable>
                                    </all>
                                </interface>
                            </poe>
                        </config>
                    """
                    formatted_xml = format_xml(poe_xml)
                    netconf_xml_dict["switch"][f"{port.get('port')}-poe"] = formatted_xml

                    speed_port = port.get("port")
                    speed_value = port_config_dict[port.get("profile")].get("speed")
                    if speed_port and speed_value:
                        speed_xml = f"""
                            <config>
                                <interface xmlns="http://pica8.com/xorplus/interface">
                                    <gigabit-ethernet>
                                        <name>{speed_port}</name>
                                        <speed {"operation='delete'" if delete else ""}>{speed_value}</speed>
                                    </gigabit-ethernet>
                                </interface>
                            </config>
                        """
                        formatted_xml = format_xml(speed_xml)
                        netconf_xml_dict["switch"][f"{port.get('port')}-speed"] = formatted_xml

        # 添加新的management
        management_config_name_list = ["localUser", "loginBanner", "timeout"]
        management_info = template_data.get("management")
        if management_info:
            for config_name in management_config_name_list:
                handler = getattr(self, f"st_netconf_{config_name}")
                xml_dict = handler(is_delete, management_info.get(config_name, {}))
                if xml_dict:
                    xml_list = [xml_dict]
                    xml = self.adapt_data_process(xml_list)
                    formatted_xml = format_xml(xml)
                    netconf_xml_dict["management"][config_name] = formatted_xml

        return netconf_xml_dict

    def deploy_netconf_config_for_dc_uplink(self, meta_data, **template_data):
        config_xml = []
        deploy_status = {}
        try:
            with manager.connect(
                host=self.host,
                port=self.port,
                username=self.username,
                password=self.password,
                hostkey_verify=False,
                timeout=300
            ) as m:
                self.update_uplink_topology(meta_data, "Deploying")
                operations = {"old_val": "delete", "new_val": ""}
                for key, op in operations.items():
                    config_data = template_data.get(key, {})
                    config_xml.append(self.dc_nic_uplink(op, **config_data))
                deploy_config_xml = self.adapt_data_process(config_xml)
                ## 不能下发空配置 否则netconf会报错
                if deploy_config_xml != "<config></config>":
                    m.edit_config(target='running', config=deploy_config_xml)
        except Exception as e:
            LOG.error(f"Error while deploying NetConf config: {traceback.format_exc()}")
            deploy_status["status"] = 500
            deploy_status["msg"] = str(traceback.format_exc())
            self.update_uplink_topology(meta_data, "Deploy Failed")
        else:
            deploy_status["status"] = 200
            deploy_status["msg"] = f"Configuration and commit successful."
            self.update_uplink_topology(meta_data, "Deployed", template_data.get("new_val", {}))
        finally:
            return deploy_status

    def deploy_netconf_config_for_dc_overlay(self, meta_data, **template_data):
        config_xml = []
        deploy_status = {}
        try:
            with manager.connect(
                host=self.host,
                port=self.port,
                username=self.username,
                password=self.password,
                hostkey_verify = False,
                timeout=300
            ) as m:
                self.update_overlay_topology(meta_data, 1) # 1-deploying 2-succeed 3-failed
                operation = {"old_val": "delete", "new_val": ""}
                for key, op in operation.items():
                    config_data = template_data.get(key, {})
                    if not config_data:
                        continue
                    for k, _ in config_data.items():
                        if key == "old_val" and k.lower() == "logical_router":
                            del_xml = self.dc_logical_router_delete_vxlans(op, **config_data)
                            if del_xml:
                                del_config_xml = self.adapt_data_process([del_xml])
                                if del_config_xml != "<config></config>":
                                    m.edit_config(target='running', config=del_config_xml)

                        handel = getattr(self, f"dc_{k.lower()}")
                        config_xml.append(handel(op, **config_data))
                deploy_config_xml = self.adapt_data_process(config_xml)
                ## 不能下发空配置 否则netconf会报错
                if deploy_config_xml != "<config></config>":
                    m.edit_config(target='running', config=deploy_config_xml)
        except Exception as e:
            LOG.error(f"Error while deploying NetConf config: {traceback.format_exc()}")
            deploy_status["status"] = 500
            deploy_status["msg"] = str(traceback.format_exc())
            self.update_overlay_topology(meta_data, 3)
        else:
            deploy_status["status"] = 200
            deploy_status["msg"] = f"Configuration and commit successful."
            self.update_overlay_topology(meta_data, 2, template_data.get("new_val", {}))
        finally:
            return deploy_status

if __name__ == '__main__':
    clos_data = {
    "meta": {
        "role": "core-1",
        "site_topo_id": 149,
        "protocol": "BGP"
    },
    "old_val": {},
    "new_val": {
        "mlag":{
            "domain_id": "1",
            "domain_id_node": "0",
            "peer_ipv4_address": "********",
            "mlag_peer_lag_interface_name": "ae1",
            "mlag_peer_interface_name": ["te-1/1/5", "te-1/1/6"],
            "mlag_peer_vlan_id": "3966",
            "mlag_peer_l3_interface_ip":  "********0",
        },
        "wan": {
            "wan_interface_name": ["te-1/1/7"],
            "ipv4_address": "********",
            "default_route_next_hop": "********"
        },
        "link_route":{
            "reserved_vlan": "3967-4094",
            "link_interfaces": ["te-1/1/1", "te-1/1/2", "te-1/1/3", "te-1/1/4"]
        },
        "underlay": {
            "router_id": "********",
            "asn": "65515",
            "original_subnet": "********/24"
        },
        "vlans": {
            "v1": {
                "vlan_id": "10",
                "subnet": "************/24",
                "vlan_ip_address": "************",
                "vlan_anycast_ip_address":"**************"
            },
            "v2": {
                "vlan_id": "20",
                "subnet": "************/24",
                "vlan_ip_address": "************",
                "vlan_anycast_ip_address":"**************"
            },
            "v3": {
                "vlan_id": "30",
                "subnet": "************/24",
                "vlan_ip_address": "************",
                "vlan_anycast_ip_address":"**************"
            }
        },
        "vrfs": {
            "vrf1": {
                "network": [
                    "v1"
                ]
            },
            "vrf2": {
                "network": [
                    "v2"
                ]
            },
            "vrf3": {
                "network": [
                    "v3"
                ]
            },
        },
    }
}
    # request_core_data = {"new_val": {"mlag": {"domain_id": "2", "domain_id_node": "0", "peer_ipv4_address": "********", "mlag_peer_lag_interface_name": "ae49", "mlag_peer_interface_name": ["te-1/1/1", "te-1/1/2"], "mlag_peer_vlan_id": "3966", "mlag_peer_l3_interface_ip": "********", "mlag_access_links": [{"access_interface_name": ["te-1/1/3"], "access_lag_interface_name": "ae2", "link_id": "1"}, {"access_interface_name": ["te-1/1/4"], "access_lag_interface_name": "ae3", "link_id": "2"}]}, "bgp": {"asn": "65515", "router_id": "********", "neighbour_ip_address": "********", "neighbour_asn": "65516", "original_subnet": "********/24"}, "inband": {"vlan_id": "3965", "ip_address": "**********/24", "vrrp_ip": "**********", "vrid": 4}, "vlans": {"vlan10": {"vlan_id": "10", "ip_address": "**************", "vlan_name": "vlan10", "vrrp_ip": "**************", "subnet": "************/24", "vrid": "1"}, "vlan20": {"vlan_id": "20", "ip_address": "**************", "vlan_name": "vlan20", "vrrp_ip": "**************", "subnet": "************/24", "vrid": "2"}, "vlan30": {"vlan_id": "30", "ip_address": "**************", "vlan_name": "vlan30", "vrrp_ip": "**************", "subnet": "************/24", "vrid": "3"}}, "dhcp_relay": [{"dhcp_network": "vlan10", "dhcp_server": "**********", "vlan_id": "10"}, {"dhcp_network": "vlan20", "dhcp_server": "**********", "vlan_id": "20"}, {"dhcp_network": "vlan30", "dhcp_server": "**********", "vlan_id": "30"}], "wan": {"reserved_vlan": "3967-4094", "wan_interface_name": ["te-1/1/7"], "wan_interface_ip": "********/30", "default_route_next_hop": "********"}}}
    # request_core_data2 ={"old_val": {"mlag": {"domain_id": "2", "domain_id_node": "0", "peer_ipv4_address": "********", "mlag_peer_lag_interface_name": "ae49", "mlag_peer_interface_name": ["te-1/1/1", "te-1/1/2"], "mlag_peer_vlan_id": "3966", "mlag_peer_l3_interface_ip": "********", "mlag_access_links": [{"access_interface_name": ["te-1/1/3"], "access_lag_interface_name": "ae2", "link_id": "1"}, {"access_interface_name": ["te-1/1/4"], "access_lag_interface_name": "ae3", "link_id": "2"}]}, "bgp": {"asn": "65515", "router_id": "********", "neighbour_ip_address": "********", "neighbour_asn": "65516", "original_subnet": "********/24"}, "inband": {"vlan_id": "3965", "ip_address": "**********/24", "vrrp_ip": "**********", "vrid": 4}, "vlans": {"vlan10": {"vlan_id": "10", "ip_address": "**************", "vlan_name": "vlan10", "vrrp_ip": "**************", "subnet": "************/24", "vrid": "1"}, "vlan20": {"vlan_id": "20", "ip_address": "**************", "vlan_name": "vlan20", "vrrp_ip": "**************", "subnet": "************/24", "vrid": "2"}, "vlan30": {"vlan_id": "30", "ip_address": "**************", "vlan_name": "vlan30", "vrrp_ip": "**************", "subnet": "************/24", "vrid": "3"}}, "dhcp_relay": [{"dhcp_network": "vlan10", "dhcp_server": "**********", "vlan_id": "10"}, {"dhcp_network": "vlan20", "dhcp_server": "**********", "vlan_id": "20"}, {"dhcp_network": "vlan30", "dhcp_server": "**********", "vlan_id": "30"}], "wan": {"reserved_vlan": "3967-4094", "wan_interface_name": ["te-1/1/7"], "wan_interface_ip": "********/30", "default_route_next_hop": "********"}}, "new_val": {"mlag": {"domain_id": "2", "domain_id_node": "0", "peer_ipv4_address": "********", "mlag_peer_lag_interface_name": "ae49", "mlag_peer_interface_name": ["te-1/1/1", "te-1/1/2"], "mlag_peer_vlan_id": "3966", "mlag_peer_l3_interface_ip": "********", "mlag_access_links": [{"access_interface_name": ["te-1/1/3"], "access_lag_interface_name": "ae2", "link_id": "1"}, {"access_interface_name": ["te-1/1/4"], "access_lag_interface_name": "ae3", "link_id": "2"}]}, "ospf": {"router_id": "********", "area_id": "0.0.0.0", "original_subnet": "********/24"}, "inband": {"vlan_id": "3965", "ip_address": "**********/24", "vrrp_ip": "**********", "vrid": 4}, "vlans": {"vlan10": {"vlan_id": "10", "ip_address": "**************", "vlan_name": "vlan10", "vrrp_ip": "**************", "subnet": "************/24", "vrid": "1"}, "vlan20": {"vlan_id": "20", "ip_address": "**************", "vlan_name": "vlan20", "vrrp_ip": "**************", "subnet": "************/24", "vrid": "2"}, "vlan30": {"vlan_id": "30", "ip_address": "**************", "vlan_name": "vlan30", "vrrp_ip": "**************", "subnet": "************/24", "vrid": "3"}}, "dhcp_relay": [{"dhcp_network": "vlan10", "dhcp_server": "**********", "vlan_id": "10"}, {"dhcp_network": "vlan20", "dhcp_server": "**********", "vlan_id": "20"}, {"dhcp_network": "vlan30", "dhcp_server": "**********", "vlan_id": "30"}], "wan": {"reserved_vlan": "3967-4094", "wan_interface_name": ["te-1/1/7"], "wan_interface_ip": "********/30", "default_route_next_hop": "********"}}}
    #
    # request_core_data_ospf_3 = {"new_val": {"mlag": {"domain_id": "2", "domain_id_node": "1", "peer_ipv4_address": "********", "mlag_peer_lag_interface_name": "ae49", "mlag_peer_interface_name": ["te-1/1/1", "te-1/1/2"], "mlag_peer_vlan_id": "3966", "mlag_peer_l3_interface_ip": "********", "mlag_access_links": [{"access_interface_name": ["te-1/1/3"], "access_lag_interface_name": "ae2", "link_id": "1"}, {"access_interface_name": ["te-1/1/4"], "access_lag_interface_name": "ae3", "link_id": "2"}]}, "ospf": {"router_id": "********", "area_id": "0.0.0.0", "original_subnet": "********/24"}, "inband": {"vlan_id": "3965", "ip_address": "**********/24", "vrrp_ip": "**********", "vrid": 4}, "vlans": {"vlan10": {"vlan_id": "10", "ip_address": "**************", "vlan_name": "vlan10", "vrrp_ip": "**************", "subnet": "************/24", "vrid": "1"}, "vlan20": {"vlan_id": "20", "ip_address": "**************", "vlan_name": "vlan20", "vrrp_ip": "**************", "subnet": "************/24", "vrid": "2"}, "vlan30": {"vlan_id": "30", "ip_address": "**************", "vlan_name": "vlan30", "vrrp_ip": "**************", "subnet": "************/24", "vrid": "3"}}, "dhcp_relay": [{"dhcp_network": "vlan10", "dhcp_server": "**********", "vlan_id": "10"}, {"dhcp_network": "vlan20", "dhcp_server": "**********", "vlan_id": "20"}, {"dhcp_network": "vlan30", "dhcp_server": "**********", "vlan_id": "30"}], "wan": {"reserved_vlan": "3967-4094", "wan_interface_name": ["te-1/1/5"], "wan_interface_ip": "********/30", "default_route_next_hop": "********0"}}}
    # request_core_data_ospf_4 = {"old_val": {"mlag": {"domain_id": "2", "domain_id_node": "1", "peer_ipv4_address": "********", "mlag_peer_lag_interface_name": "ae49", "mlag_peer_interface_name": ["te-1/1/1", "te-1/1/2"], "mlag_peer_vlan_id": "3966", "mlag_peer_l3_interface_ip": "********", "mlag_access_links": [{"access_interface_name": ["te-1/1/3"], "access_lag_interface_name": "ae2", "link_id": "1"}, {"access_interface_name": ["te-1/1/4"], "access_lag_interface_name": "ae3", "link_id": "2"}]}, "ospf": {"router_id": "********", "area_id": "0.0.0.0", "original_subnet": "********/24"}, "inband": {"vlan_id": "3965", "ip_address": "**********/24", "vrrp_ip": "**********", "vrid": 4}, "vlans": {"vlan10": {"vlan_id": "10", "ip_address": "**************", "vlan_name": "vlan10", "vrrp_ip": "**************", "subnet": "************/24", "vrid": "1"}, "vlan20": {"vlan_id": "20", "ip_address": "**************", "vlan_name": "vlan20", "vrrp_ip": "**************", "subnet": "************/24", "vrid": "2"}, "vlan30": {"vlan_id": "30", "ip_address": "**************", "vlan_name": "vlan30", "vrrp_ip": "**************", "subnet": "************/24", "vrid": "3"}}, "dhcp_relay": [{"dhcp_network": "vlan10", "dhcp_server": "**********", "vlan_id": "10"}, {"dhcp_network": "vlan20", "dhcp_server": "**********", "vlan_id": "20"}, {"dhcp_network": "vlan30", "dhcp_server": "**********", "vlan_id": "30"}], "wan": {"reserved_vlan": "3967-4094", "wan_interface_name": ["te-1/1/5"], "wan_interface_ip": "********/30", "default_route_next_hop": "********0"}}, "new_val": {"mlag": {"domain_id": "2", "domain_id_node": "1", "peer_ipv4_address": "********", "mlag_peer_lag_interface_name": "ae49", "mlag_peer_interface_name": ["te-1/1/1", "te-1/1/2"], "mlag_peer_vlan_id": "3966", "mlag_peer_l3_interface_ip": "********", "mlag_access_links": [{"access_interface_name": ["te-1/1/3"], "access_lag_interface_name": "ae2", "link_id": "1"}, {"access_interface_name": ["te-1/1/4"], "access_lag_interface_name": "ae3", "link_id": "2"}]}, "bgp": {"asn": "65533", "router_id": "********", "neighbour_ip_address": "********", "neighbour_asn": "65532", "original_subnet": "********/24"}, "inband": {"vlan_id": "3965", "ip_address": "**********/24", "vrrp_ip": "**********", "vrid": 4}, "vlans": {"vlan10": {"vlan_id": "10", "ip_address": "**************", "vlan_name": "vlan10", "vrrp_ip": "**************", "subnet": "************/24", "vrid": "1"}, "vlan20": {"vlan_id": "20", "ip_address": "**************", "vlan_name": "vlan20", "vrrp_ip": "**************", "subnet": "************/24", "vrid": "2"}, "vlan30": {"vlan_id": "30", "ip_address": "**************", "vlan_name": "vlan30", "vrrp_ip": "**************", "subnet": "************/24", "vrid": "3"}}, "dhcp_relay": [{"dhcp_network": "vlan10", "dhcp_server": "**********", "vlan_id": "10"}, {"dhcp_network": "vlan20", "dhcp_server": "**********", "vlan_id": "20"}, {"dhcp_network": "vlan30", "dhcp_server": "**********", "vlan_id": "30"}], "wan": {"reserved_vlan": "3967-4094", "wan_interface_name": ["te-1/1/5"], "wan_interface_ip": "********/30", "default_route_next_hop": "********0"}}}

    # import time
    # start = time.time()
    # d1 = {"new_val": {"mlag": {"uplink_lag_interface_name": "ae1", "uplink_interface_name": ["ge-1/1/1", "ge-1/1/2"], "mlag_peer_vlan_id": "3966"}, "inband": {"vlan_id": "3965", "ip_address": "********/24", "vrrp_ip": "**********"}, "vlans": {"vlan10": {"vlan_id": "10", "subnet": "************/24"}, "vlan20": {"vlan_id": "20", "subnet": "************/24"}, "vlan30": {"vlan_id": "30", "subnet": "************/24"}}, "dhcp_snooping": ["10", "20", "30"], "wan_connect": {}, "nac": {"access_interface_name": ["ge-1/1/3", "ge-1/1/4", "ge-1/1/5", "ge-1/1/6", "ge-1/1/7", "ge-1/1/8", "ge-1/1/9", "ge-1/1/10", "ge-1/1/11", "ge-1/1/12", "ge-1/1/13", "ge-1/1/14", "ge-1/1/15", "ge-1/1/16", "ge-1/1/17", "ge-1/1/18", "ge-1/1/19", "ge-1/1/20", "ge-1/1/21", "ge-1/1/22", "ge-1/1/23", "ge-1/1/24", "ge-1/1/25", "ge-1/1/26", "ge-1/1/27", "ge-1/1/28", "ge-1/1/29", "ge-1/1/30", "ge-1/1/31", "ge-1/1/32", "ge-1/1/33", "ge-1/1/34", "ge-1/1/35", "ge-1/1/36", "ge-1/1/37", "ge-1/1/38", "ge-1/1/39", "ge-1/1/40", "ge-1/1/41", "ge-1/1/42", "ge-1/1/43", "ge-1/1/44", "ge-1/1/45", "ge-1/1/46", "ge-1/1/47", "ge-1/1/48"], "nac_servers": [{"server_name": "1", "server_address": "**********", "port": 1812, "shared_secret": "leontest"}]}}}
    # d2 = {"new_val": {"mlag": {"uplink_lag_interface_name": "ae1", "uplink_interface_name": ["te-1/1/1", "te-1/1/2"], "mlag_peer_vlan_id": "3966"}, "inband": {"vlan_id": "3965", "ip_address": "********/24", "vrrp_ip": "**********"}, "vlans": {"vlan10": {"vlan_id": "10", "subnet": "************/24"}, "vlan20": {"vlan_id": "20", "subnet": "************/24"}, "vlan30": {"vlan_id": "30", "subnet": "************/24"}}, "dhcp_snooping": ["10", "20", "30"], "wan_connect": {}, "nac": {"access_interface_name": ["te-1/1/3", "te-1/1/4", "te-1/1/5", "te-1/1/6", "te-1/1/7", "te-1/1/8", "te-1/1/9", "te-1/1/10", "te-1/1/11", "te-1/1/12", "te-1/1/13", "te-1/1/14", "te-1/1/15", "te-1/1/16", "te-1/1/17", "te-1/1/18", "te-1/1/19", "te-1/1/20", "te-1/1/21", "te-1/1/22", "te-1/1/23", "te-1/1/24", "te-1/1/25", "te-1/1/26", "te-1/1/27", "te-1/1/28", "te-1/1/29", "te-1/1/30", "te-1/1/31", "te-1/1/32"], "nac_servers": [{"server_name": "1", "server_address": "**********", "port": 1812, "shared_secret": "leontest"}]}}}
    clos_data = {
        "meta": {
            "role": "core",
            "site_topo_id": 1,
            "protocol": "BGP",
            "sn": "62A544A8C181"
        },
        "old_val": {},
        "new_val": {
            "mlag": {
                "domain_id": "1",
                "domain_id_node": "0",
                "peer_ipv4_address": "********",
                "mlag_peer_lag_interface_name": "ae49",
                "mlag_peer_interface_name": [
                    "te-1/1/10",
                    "te-1/1/8"
                ],
                "mlag_peer_vlan_id": "3966",
                "mlag_peer_l3_interface_ip": "********"
            },
            "link_route": {
                "reserved_vlan": "3967-4094",
                "link_interfaces": [
                    "te-1/1/9"
                ],
                "to_border_interfaces": []
            },
            "underlay": {
                "router_id": "********",
                "asn": "65501"
            },
            "vlans": {
                "vlan10": {
                    "vlan_id": "10",
                    "subnet": "************/24",
                    "vlan_ip_address": "************",
                    "vlan_anycast_ip_address": "**************"
                },
                "vlan20": {
                    "vlan_id": "20",
                    "subnet": "************/24",
                    "vlan_ip_address": "************",
                    "vlan_anycast_ip_address": "**************"
                }
            },
            "vrfs": {
                "10": {
                    "network": [
                        "vlan10"
                    ]
                }
            },
            "dhcp_relay": [],
            "nac": {
                "access_interface_name": [
                    "te-1/1/1",
                    "te-1/1/2",
                    "te-1/1/3",
                    "te-1/1/4",
                    "te-1/1/5",
                    "te-1/1/6",
                    "te-1/1/7",
                    "te-1/1/8",
                    "te-1/1/10",
                    "te-1/1/11",
                    "te-1/1/12",
                    "te-1/1/13",
                    "te-1/1/14",
                    "te-1/1/15",
                    "te-1/1/16",
                    "te-1/1/17",
                    "te-1/1/18",
                    "te-1/1/19",
                    "te-1/1/20",
                    "te-1/1/21",
                    "te-1/1/22",
                    "te-1/1/23",
                    "te-1/1/24",
                    "te-1/1/25",
                    "te-1/1/26",
                    "te-1/1/27",
                    "te-1/1/28",
                    "te-1/1/29",
                    "te-1/1/30",
                    "te-1/1/31",
                    "te-1/1/32",
                    "te-1/1/33",
                    "te-1/1/34",
                    "te-1/1/35",
                    "te-1/1/36",
                    "te-1/1/37",
                    "te-1/1/38",
                    "te-1/1/39",
                    "te-1/1/40",
                    "te-1/1/41",
                    "te-1/1/42",
                    "te-1/1/43",
                    "te-1/1/44",
                    "te-1/1/45",
                    "te-1/1/46",
                    "te-1/1/47",
                    "te-1/1/48"
                ],
                "nac_servers": []
            },
            "wan": {
                "wan_interface_name": [
                    "te-1/1/7"
                ],
                "default_route_next_hop": "********"
            }
        }
    }
    # request_core_data = {"new_val": {"mlag": {"domain_id": "2", "domain_id_node": "0", "peer_ipv4_address": "********", "mlag_peer_lag_interface_name": "ae49", "mlag_peer_interface_name": ["te-1/1/1", "te-1/1/2"], "mlag_peer_vlan_id": "3966", "mlag_peer_l3_interface_ip": "********", "mlag_access_links": [{"access_interface_name": ["te-1/1/3"], "access_lag_interface_name": "ae2", "link_id": "1"}, {"access_interface_name": ["te-1/1/4"], "access_lag_interface_name": "ae3", "link_id": "2"}]}, "bgp": {"asn": "65515", "router_id": "********", "neighbour_ip_address": "********", "neighbour_asn": "65516", "original_subnet": "********/24"}, "inband": {"vlan_id": "3965", "ip_address": "**********/24", "vrrp_ip": "**********", "vrid": 4}, "vlans": {"vlan10": {"vlan_id": "10", "ip_address": "**************", "vlan_name": "vlan10", "vrrp_ip": "**************", "subnet": "************/24", "vrid": "1"}, "vlan20": {"vlan_id": "20", "ip_address": "**************", "vlan_name": "vlan20", "vrrp_ip": "**************", "subnet": "************/24", "vrid": "2"}, "vlan30": {"vlan_id": "30", "ip_address": "**************", "vlan_name": "vlan30", "vrrp_ip": "**************", "subnet": "************/24", "vrid": "3"}}, "dhcp_relay": [{"dhcp_network": "vlan10", "dhcp_server": "**********", "vlan_id": "10"}, {"dhcp_network": "vlan20", "dhcp_server": "**********", "vlan_id": "20"}, {"dhcp_network": "vlan30", "dhcp_server": "**********", "vlan_id": "30"}], "wan": {"reserved_vlan": "3967-4094", "wan_interface_name": ["te-1/1/7"], "wan_interface_ip": "********/30", "default_route_next_hop": "********"}}}
    # request_core_data2 ={"old_val": {"mlag": {"domain_id": "2", "domain_id_node": "0", "peer_ipv4_address": "********", "mlag_peer_lag_interface_name": "ae49", "mlag_peer_interface_name": ["te-1/1/1", "te-1/1/2"], "mlag_peer_vlan_id": "3966", "mlag_peer_l3_interface_ip": "********", "mlag_access_links": [{"access_interface_name": ["te-1/1/3"], "access_lag_interface_name": "ae2", "link_id": "1"}, {"access_interface_name": ["te-1/1/4"], "access_lag_interface_name": "ae3", "link_id": "2"}]}, "bgp": {"asn": "65515", "router_id": "********", "neighbour_ip_address": "********", "neighbour_asn": "65516", "original_subnet": "********/24"}, "inband": {"vlan_id": "3965", "ip_address": "**********/24", "vrrp_ip": "**********", "vrid": 4}, "vlans": {"vlan10": {"vlan_id": "10", "ip_address": "**************", "vlan_name": "vlan10", "vrrp_ip": "**************", "subnet": "************/24", "vrid": "1"}, "vlan20": {"vlan_id": "20", "ip_address": "**************", "vlan_name": "vlan20", "vrrp_ip": "**************", "subnet": "************/24", "vrid": "2"}, "vlan30": {"vlan_id": "30", "ip_address": "**************", "vlan_name": "vlan30", "vrrp_ip": "**************", "subnet": "************/24", "vrid": "3"}}, "dhcp_relay": [{"dhcp_network": "vlan10", "dhcp_server": "**********", "vlan_id": "10"}, {"dhcp_network": "vlan20", "dhcp_server": "**********", "vlan_id": "20"}, {"dhcp_network": "vlan30", "dhcp_server": "**********", "vlan_id": "30"}], "wan": {"reserved_vlan": "3967-4094", "wan_interface_name": ["te-1/1/7"], "wan_interface_ip": "********/30", "default_route_next_hop": "********"}}, "new_val": {"mlag": {"domain_id": "2", "domain_id_node": "0", "peer_ipv4_address": "********", "mlag_peer_lag_interface_name": "ae49", "mlag_peer_interface_name": ["te-1/1/1", "te-1/1/2"], "mlag_peer_vlan_id": "3966", "mlag_peer_l3_interface_ip": "********", "mlag_access_links": [{"access_interface_name": ["te-1/1/3"], "access_lag_interface_name": "ae2", "link_id": "1"}, {"access_interface_name": ["te-1/1/4"], "access_lag_interface_name": "ae3", "link_id": "2"}]}, "ospf": {"router_id": "********", "area_id": "0.0.0.0", "original_subnet": "********/24"}, "inband": {"vlan_id": "3965", "ip_address": "**********/24", "vrrp_ip": "**********", "vrid": 4}, "vlans": {"vlan10": {"vlan_id": "10", "ip_address": "**************", "vlan_name": "vlan10", "vrrp_ip": "**************", "subnet": "************/24", "vrid": "1"}, "vlan20": {"vlan_id": "20", "ip_address": "**************", "vlan_name": "vlan20", "vrrp_ip": "**************", "subnet": "************/24", "vrid": "2"}, "vlan30": {"vlan_id": "30", "ip_address": "**************", "vlan_name": "vlan30", "vrrp_ip": "**************", "subnet": "************/24", "vrid": "3"}}, "dhcp_relay": [{"dhcp_network": "vlan10", "dhcp_server": "**********", "vlan_id": "10"}, {"dhcp_network": "vlan20", "dhcp_server": "**********", "vlan_id": "20"}, {"dhcp_network": "vlan30", "dhcp_server": "**********", "vlan_id": "30"}], "wan": {"reserved_vlan": "3967-4094", "wan_interface_name": ["te-1/1/7"], "wan_interface_ip": "********/30", "default_route_next_hop": "********"}}}
    #
    # request_core_data_ospf_3 = {"new_val": {"mlag": {"domain_id": "2", "domain_id_node": "1", "peer_ipv4_address": "********", "mlag_peer_lag_interface_name": "ae49", "mlag_peer_interface_name": ["te-1/1/1", "te-1/1/2"], "mlag_peer_vlan_id": "3966", "mlag_peer_l3_interface_ip": "********", "mlag_access_links": [{"access_interface_name": ["te-1/1/3"], "access_lag_interface_name": "ae2", "link_id": "1"}, {"access_interface_name": ["te-1/1/4"], "access_lag_interface_name": "ae3", "link_id": "2"}]}, "ospf": {"router_id": "********", "area_id": "0.0.0.0", "original_subnet": "********/24"}, "inband": {"vlan_id": "3965", "ip_address": "**********/24", "vrrp_ip": "**********", "vrid": 4}, "vlans": {"vlan10": {"vlan_id": "10", "ip_address": "**************", "vlan_name": "vlan10", "vrrp_ip": "**************", "subnet": "************/24", "vrid": "1"}, "vlan20": {"vlan_id": "20", "ip_address": "**************", "vlan_name": "vlan20", "vrrp_ip": "**************", "subnet": "************/24", "vrid": "2"}, "vlan30": {"vlan_id": "30", "ip_address": "**************", "vlan_name": "vlan30", "vrrp_ip": "**************", "subnet": "************/24", "vrid": "3"}}, "dhcp_relay": [{"dhcp_network": "vlan10", "dhcp_server": "**********", "vlan_id": "10"}, {"dhcp_network": "vlan20", "dhcp_server": "**********", "vlan_id": "20"}, {"dhcp_network": "vlan30", "dhcp_server": "**********", "vlan_id": "30"}], "wan": {"reserved_vlan": "3967-4094", "wan_interface_name": ["te-1/1/5"], "wan_interface_ip": "********/30", "default_route_next_hop": "********0"}}}
    # request_core_data_ospf_4 = {"old_val": {"mlag": {"domain_id": "2", "domain_id_node": "1", "peer_ipv4_address": "********", "mlag_peer_lag_interface_name": "ae49", "mlag_peer_interface_name": ["te-1/1/1", "te-1/1/2"], "mlag_peer_vlan_id": "3966", "mlag_peer_l3_interface_ip": "********", "mlag_access_links": [{"access_interface_name": ["te-1/1/3"], "access_lag_interface_name": "ae2", "link_id": "1"}, {"access_interface_name": ["te-1/1/4"], "access_lag_interface_name": "ae3", "link_id": "2"}]}, "ospf": {"router_id": "********", "area_id": "0.0.0.0", "original_subnet": "********/24"}, "inband": {"vlan_id": "3965", "ip_address": "**********/24", "vrrp_ip": "**********", "vrid": 4}, "vlans": {"vlan10": {"vlan_id": "10", "ip_address": "**************", "vlan_name": "vlan10", "vrrp_ip": "**************", "subnet": "************/24", "vrid": "1"}, "vlan20": {"vlan_id": "20", "ip_address": "**************", "vlan_name": "vlan20", "vrrp_ip": "**************", "subnet": "************/24", "vrid": "2"}, "vlan30": {"vlan_id": "30", "ip_address": "**************", "vlan_name": "vlan30", "vrrp_ip": "**************", "subnet": "************/24", "vrid": "3"}}, "dhcp_relay": [{"dhcp_network": "vlan10", "dhcp_server": "**********", "vlan_id": "10"}, {"dhcp_network": "vlan20", "dhcp_server": "**********", "vlan_id": "20"}, {"dhcp_network": "vlan30", "dhcp_server": "**********", "vlan_id": "30"}], "wan": {"reserved_vlan": "3967-4094", "wan_interface_name": ["te-1/1/5"], "wan_interface_ip": "********/30", "default_route_next_hop": "********0"}}, "new_val": {"mlag": {"domain_id": "2", "domain_id_node": "1", "peer_ipv4_address": "********", "mlag_peer_lag_interface_name": "ae49", "mlag_peer_interface_name": ["te-1/1/1", "te-1/1/2"], "mlag_peer_vlan_id": "3966", "mlag_peer_l3_interface_ip": "********", "mlag_access_links": [{"access_interface_name": ["te-1/1/3"], "access_lag_interface_name": "ae2", "link_id": "1"}, {"access_interface_name": ["te-1/1/4"], "access_lag_interface_name": "ae3", "link_id": "2"}]}, "bgp": {"asn": "65533", "router_id": "********", "neighbour_ip_address": "********", "neighbour_asn": "65532", "original_subnet": "********/24"}, "inband": {"vlan_id": "3965", "ip_address": "**********/24", "vrrp_ip": "**********", "vrid": 4}, "vlans": {"vlan10": {"vlan_id": "10", "ip_address": "**************", "vlan_name": "vlan10", "vrrp_ip": "**************", "subnet": "************/24", "vrid": "1"}, "vlan20": {"vlan_id": "20", "ip_address": "**************", "vlan_name": "vlan20", "vrrp_ip": "**************", "subnet": "************/24", "vrid": "2"}, "vlan30": {"vlan_id": "30", "ip_address": "**************", "vlan_name": "vlan30", "vrrp_ip": "**************", "subnet": "************/24", "vrid": "3"}}, "dhcp_relay": [{"dhcp_network": "vlan10", "dhcp_server": "**********", "vlan_id": "10"}, {"dhcp_network": "vlan20", "dhcp_server": "**********", "vlan_id": "20"}, {"dhcp_network": "vlan30", "dhcp_server": "**********", "vlan_id": "30"}], "wan": {"reserved_vlan": "3967-4094", "wan_interface_name": ["te-1/1/5"], "wan_interface_ip": "********/30", "default_route_next_hop": "********0"}}}

    # import time
    # start = time.time()
    # d1 = {"new_val": {"mlag": {"uplink_lag_interface_name": "ae1", "uplink_interface_name": ["ge-1/1/1", "ge-1/1/2"], "mlag_peer_vlan_id": "3966"}, "inband": {"vlan_id": "3965", "ip_address": "********/24", "vrrp_ip": "**********"}, "vlans": {"vlan10": {"vlan_id": "10", "subnet": "************/24"}, "vlan20": {"vlan_id": "20", "subnet": "************/24"}, "vlan30": {"vlan_id": "30", "subnet": "************/24"}}, "dhcp_snooping": ["10", "20", "30"], "wan_connect": {}, "nac": {"access_interface_name": ["ge-1/1/3", "ge-1/1/4", "ge-1/1/5", "ge-1/1/6", "ge-1/1/7", "ge-1/1/8", "ge-1/1/9", "ge-1/1/10", "ge-1/1/11", "ge-1/1/12", "ge-1/1/13", "ge-1/1/14", "ge-1/1/15", "ge-1/1/16", "ge-1/1/17", "ge-1/1/18", "ge-1/1/19", "ge-1/1/20", "ge-1/1/21", "ge-1/1/22", "ge-1/1/23", "ge-1/1/24", "ge-1/1/25", "ge-1/1/26", "ge-1/1/27", "ge-1/1/28", "ge-1/1/29", "ge-1/1/30", "ge-1/1/31", "ge-1/1/32", "ge-1/1/33", "ge-1/1/34", "ge-1/1/35", "ge-1/1/36", "ge-1/1/37", "ge-1/1/38", "ge-1/1/39", "ge-1/1/40", "ge-1/1/41", "ge-1/1/42", "ge-1/1/43", "ge-1/1/44", "ge-1/1/45", "ge-1/1/46", "ge-1/1/47", "ge-1/1/48"], "nac_servers": [{"server_name": "1", "server_address": "**********", "port": 1812, "shared_secret": "leontest"}]}}}
    # d2 = {"new_val": {"mlag": {"uplink_lag_interface_name": "ae1", "uplink_interface_name": ["te-1/1/1", "te-1/1/2"], "mlag_peer_vlan_id": "3966"}, "inband": {"vlan_id": "3965", "ip_address": "********/24", "vrrp_ip": "**********"}, "vlans": {"vlan10": {"vlan_id": "10", "subnet": "************/24"}, "vlan20": {"vlan_id": "20", "subnet": "************/24"}, "vlan30": {"vlan_id": "30", "subnet": "************/24"}}, "dhcp_snooping": ["10", "20", "30"], "wan_connect": {}, "nac": {"access_interface_name": ["te-1/1/3", "te-1/1/4", "te-1/1/5", "te-1/1/6", "te-1/1/7", "te-1/1/8", "te-1/1/9", "te-1/1/10", "te-1/1/11", "te-1/1/12", "te-1/1/13", "te-1/1/14", "te-1/1/15", "te-1/1/16", "te-1/1/17", "te-1/1/18", "te-1/1/19", "te-1/1/20", "te-1/1/21", "te-1/1/22", "te-1/1/23", "te-1/1/24", "te-1/1/25", "te-1/1/26", "te-1/1/27", "te-1/1/28", "te-1/1/29", "te-1/1/30", "te-1/1/31", "te-1/1/32"], "nac_servers": [{"server_name": "1", "server_address": "**********", "port": 1812, "shared_secret": "leontest"}]}}}
    # netconf_deployer = NetConfConfigDeployer(host='************', username='admin', password='12345678', port=830)
    # netconf_deployer = NetConfConfigDeployer(host='************', username='admin', password='adminpica8', port=830)
    # netconf_deployer = NetConfConfigDeployer(host='************', username='admin', password='pica8', port=830)
    # netconf_deployer = NetConfConfigDeployer(host='************', username='admin', password='admin123', port=830)
    # netconf_deployer = NetConfConfigDeployer(host='************', username='admin', password='12345678', port=830)
    # netconf_result = netconf_deployer.deploy_netconf_config_by_clos("core", {}, **clos_data)
    # netconf_result = netconf_deployer.deploy_netconf_config_by_mlag("access", {}, **d1)
    # end = time.time()
    # print(">>>>>>>>>>>>>>", end-start)
    # netconf_result = netconf_deployer.deploy_netconf_config_by_mlag("core", {"role": "core", "site_topo_id": 27, "protocol": "BGP"}, **request_core_data2)
    # netconf_result = netconf_deployer.deploy_netconf_config("core", **request_data)
    # print(netconf_result)
    request_data = {
        "meta": {
            "role": "leaf",
            "fabric_topo_id": 9,
            "logic_name": "mlag-unit_001_mlag-leaf_1"
        },
        "old_val": {
        },
        "new_val": {
            "hostname": "mlag-unit-001-mlag-leaf-2",
            "mlag": {
                "peer_vlan_id": "3960",
                "mlag_l3_interface_ip_address": "*********/31",
                "mlag_peer_lag_interface_name": "ae48",
                "mlag_interface_name": ["te-1/1/3", "te-1/1/4"],
                "peer_ipv4_address": "*********/31",
                "domain_id": 1,
                "domain_id_node": 0
            },
            "bgp": {
                "asn": 101,
                "bgp_router_id": "*******",
                "vtep_interface": "*******",
                "spine_link": [{
                        "interface_name": "te-1/1/1",
                        "description": "linking_mlag-unit-001-mlag-leaf-1_te-1/1/1",
                        "routed_interface_ip_address": "*********/31",
                        "routed_interface_target_address": "*********/31",
                        "routed_interface_network_address": "*********/32"
                    }, {
                        "interface_name": "te-1/1/2",
                        "description": "linking_mlag-unit-001-mlag-leaf-1_te-1/1/1",
                        "routed_interface_ip_address": "*********/31",
                        "routed_interface_target_address": "*********/31",
                        "routed_interface_network_address": "*********/32"
                    }
                ],
                "peer_link": {
                    "mlag_l3_interface_ip_address": "*********/31",
                    "peer_ipv4_address": "*********/31",
                    "mlag_l3_interface_network_address": "*********/32"
                }
            },
            "overlay": {
                "bgp_router_id": "*******",
                "vtep_interface": "*******",
                "neighbor_router_id": ["*******", "*******"]
            },
            "link": {
                "reserved_vlan": "3967-4094",
                "spine_link": [{
                        "interface_name": "te-1/1/1",
                        "description": "linking_mlag-unit-001-mlag-leaf-1_te-1/1/1",
                        "routed_interface_ip_address": "*********/31",
                        "routed_interface_target_address": "*********/31",
                        "routed_interface_network_address": "*********/32"
                    }, {
                        "interface_name": "te-1/1/2",
                        "description": "linking_mlag-unit-001-mlag-leaf-1_te-1/1/1",
                        "routed_interface_ip_address": "*********/31",
                        "routed_interface_target_address": "*********/31",
                        "routed_interface_network_address": "*********/32"
                    }
                ]
            }
        }
    }
    netconf_deployer = NetConfConfigDeployer(host='************', username='admin', password='12345678', port=830)
    netconf_result = netconf_deployer.deploy_netconf_config_for_dc("leaf", {}, **request_data)
    print(netconf_result)
