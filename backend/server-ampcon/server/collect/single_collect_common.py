import json
import logging
import re
import time

from ansible.plugins.callback import CallbackBase

from server import constants
from server.db.models.inventory import Switch, License
from server.db.models.inventory import inven_db
from server.db.models.monitor import monitor_db
from server.util import ssh_util as client
from celery_app import my_celery_app
from celery_app.automation_task import AmpConBaseTask
from server import constants as C

LOG = logging.getLogger(__name__)


class SingleResultCallback(CallbackBase):

    TIME_FORMAT = "%b %d %Y %H:%M:%S"

    def __init__(self):
        super(SingleResultCallback, self).__init__()
        self.result = {}

    def _add_result(self, result, category):
        log_result = {
            'task_name': result.task_name,
            'timestamp': time.strftime(self.TIME_FORMAT, time.localtime()),
            'category': category,
            'host': result._host,
            'result': result._result,
            'task_fields': result._task_fields
        }
        self.result[result.task_name] = log_result

    def v2_runner_on_ok(self, result, **kwargs):
        """Print a json representation of the result
        This method could store the result in an instance attribute for retrieval later
        """
        self._add_result(result, 'ok')

    def v2_runner_on_failed(self, result, **kwargs):
        """Print a json representation of the result
        This method could store the result in an instance attribute for retrieval later
        """
        self._add_result(result, 'failed')
        monitor_db.add_event(result._host, 'error', '%s switch failed [%s]' % (result._host, result._result['msg']))

    def v2_runner_on_unreachable(self, result, **kwargs):
        """Print a json representation of the result
        This method could store the result in an instance attribute for retrieval later
        """
        self._add_result(result, 'unreachable')
        monitor_db.add_event(result._host, 'error', '%s switch unreachable' % result._host)


def get_switch_sn(host, user, password):
    # sn_str, code = client.interactive_shell_linux('/pica/bin/system/fan_status -s',
    #                                               host, username=user, password=password)
    sn_str, code = client.interactive_shell_cli('show system serial-number | no-more',
                                                host, username=user, password=password)

    if code != constants.RMA_ACTIVE:
        return sn_str, code

    sn = re.findall("MotherBoard Serial Number : (.*)", sn_str)[0].strip()
    return sn, constants.RMA_ACTIVE


def get_switch_version(host, user, password):
    # res, code = client.interactive_shell_linux('version',
    #                                            host, username=user, password=password)
    res, code = client.interactive_shell_cli('show version',
                                             host, username=user, password=password)

    if code != constants.RMA_ACTIVE:
        error_msg = handler_error(res, code)
        monitor_db.add_event(host, 'error', 'import switch ip:%s can not get version info %s' % (host, error_msg))
        return None, error_msg, code

    # get hardware model by cli show version
    hardware_model = re.findall("Model.*:\s+(.*)\n", res)[0].strip()
    if 'as' in hardware_model.lower() and not ('as4625' in hardware_model.lower() or 'as4630' in hardware_model.lower()) or (hardware_model.lower() in ['ag9032', 'ag5648', 'ag7648']):
        hardware_model = hardware_model.lower()
    if hardware_model == 'as5835-54t':
        hardware_model = 'as5835_54t'
    elif hardware_model == 'as5835-54x':
        hardware_model = 'as5835_54x'
    current_version = list(map(lambda x: x[0] if x[0] else x[1] if x[1] else x[2], re.findall("L2/L3 Version/Revision.*:\s*(.*?)\n|PICOS Release/Commit.*:\s*(.*?)\n|Software Version.*:\s*(.*?)\n", res)))[0].strip()
    return hardware_model, current_version, code


def get_switch_mac(host, user, password):
    res, code = client.interactive_shell_linux('cat /sys/class/swmon/ctrl/asic_mac',
                                               host, username=user, password=password)
    if code != constants.RMA_ACTIVE:
        return res, code
    
    return res.strip().lower(), constants.RMA_ACTIVE


def get_switch_license(host, user, password):
    res, code = client.interactive_shell_linux('license -s',
                                               host, username=user, password=password)
    if code != constants.RMA_ACTIVE:
        return res, code

    s = res.find('{')
    e = res.find('}')

    if s == -1 or e == -1:
        LOG.warn('switch %s have no license yet', host)
        monitor_db.add_event(host, 'warn', 'switch %s have no license yet' % host)
        lines = res.split('\n')
        sw_type, _, sw_type_value = lines[1].strip().partition(':')
        sw_hwid, _, hwid_value = lines[2].strip().partition(':')
        return {'type': sw_type_value.strip(), 'Hardware ID': hwid_value.strip()}, constants.RMA_FAILED

    license_info_str = res[s:e + 1]
    license_info = json.loads(license_info_str)

    return license_info, code


def get_switch_host_name(host, user, password):
    retry_times = 0
    while retry_times < 3:
        res, code = client.interactive_shell_linux('hostname', host, username=user, password=password)
        if res and res != '' and code == constants.RMA_ACTIVE:
            return res, code

        retry_times += 1

    return None, constants.RMA_FAILED


def create_dir_automation(host, user, password):
    create_dir_flag = False
    retry_times = 0
    while not create_dir_flag and retry_times < 3:

        res, code = client.interactive_shell_linux('ls /home/<USER>/',
                                                   host, username=user, password=password)
        if code == constants.RMA_ACTIVE:
            create_dir_flag = True
            break

        client.interactive_shell_linux('sudo mkdir -m 777 /home/<USER>/',
                                        host, username=user, password=password)
        retry_times += 1

    if not create_dir_flag:
        return 'create /home/<USER>'

    create_dir_flag = False
    retry_times = 0
    while not create_dir_flag and retry_times < 3:
        res, code = client.interactive_shell_linux('ls /cftmp/auto/',
                                                   host, username=user, password=password)

        if code == constants.RMA_ACTIVE:
            create_dir_flag = True
            break

        client.interactive_shell_linux('sudo mkdir /cftmp/auto/',
                                        host, username=user, password=password)
        retry_times += 1

    if not create_dir_flag:
        return 'create /cftmp/auto/ dir failed'

    return 'ok'


def handler_error(res, status):
    if status == constants.RMA_FAILED or status == constants.RMA_UN_REACHABLE:
        return 'auth error'
    if status != 0:
        return res


@my_celery_app.task(name="collect_import_switches", base=AmpConBaseTask)
def collect_import_switches(hosts, user, password, **kwargs):
    res = {}
    for host in hosts:
        sn, status = get_switch_sn(host, user, password)
        if status != constants.RMA_ACTIVE:
            error_msg = handler_error(sn, status)
            res[host] = error_msg
            monitor_db.add_event(host, 'error', 'import switch ip:%s failed, can not get sn %s' % (host, error_msg))
            continue

        hardware_model, current_version, code = get_switch_version(host, user, password)
        if code != constants.RMA_ACTIVE:
            res[host] = current_version
            monitor_db.add_event(host, 'error', 'import switch ip:%s failed, can not get version info %s' % (host, current_version))
            continue
        
        mac_addr, code = get_switch_mac(host, user, password)
        if code != constants.RMA_ACTIVE:
            monitor_db.add_event(host, 'error', 'import switch ip:%s failed, can not get mac address' % (host))
            continue
        
        is_picos_v_skip_add_license = client.is_picos_v_need_to_skip_add_license(host, user, password)
        license_info, status = get_switch_license(host, user, password)
        if is_picos_v_skip_add_license:
            LOG.info("PICOS-V no need to generate and add license")
        else:
            if status != constants.RMA_ACTIVE:
                if license_info:
                    res[host] = 'switch no license, use below info create license \n' + str(license_info)
                    monitor_db.add_event(host, 'error', 'import switch ip:%s failed, no license' % host)
                else:
                    res[host] = 'get switch license failed'
                    monitor_db.add_event(host, 'error', 'import switch ip:%s failed, can not get license info' % host)

        version, seg, revision = current_version.rpartition('/')
        revision = revision.split('-')[0]

        hostname, code = get_switch_host_name(host, user, password)
        if code != constants.RMA_ACTIVE:
            res[host] = 'get switch hostname failed'
            monitor_db.add_event(host, 'error', 'get switch %s hostname failed' % host)
            continue

        # Need create /home/<USER>
        res[host] = create_dir_automation(host, user, password)
        if res[host] != 'ok':
            monitor_db.add_event(host, 'error', 'import switch ip:%s failed, %s' % (host, res[host]))
            continue

        switch = Switch(mgt_ip=host, platform_model=hardware_model,
                        sn=sn, version=version, revision=revision, hwid=license_info['Hardware ID'],
                        host_name=hostname, import_type=constants.ImportType.IMPORT,
                        status=constants.SwitchStatus.IMPORTED, enable=True,
                        reachable_status=constants.REACHABLE, mac_addr=mac_addr, is_picos_v=is_picos_v_skip_add_license)
        try:
            inven_db.insert_or_update(switch, primary_key='sn')
        except Exception as e:
            LOG.error(e)
            res[host] = 'no model config: ' + hardware_model + ' or duplicate with exist switches'
            monitor_db.add_event(host, 'error',
                                 'import switch ip:%s failed, no %s model config or duplicate with exist switches' % (host, hardware_model))
            continue
        monitor_db.add_event(host, 'info', 'import switch ip %s success' % host)
        if not is_picos_v_skip_add_license:
            try:
                if "Expire Date" in license_info.keys():
                    license_info.update({'Support End Date': license_info["Expire Date"]})

                license = License(sn_num=sn,
                                license_expired=license_info['Support End Date'],
                                lic_feature=','.join(license_info['Feature']),
                                lic_speed=license_info['Type'],
                                status=constants.LicenseStatus.ACTIVE)
                inven_db.insert(license)
            except Exception as e:
                LOG.error("insert license failed")

    return res


def update_import_switch_license_record(sn, host, user, pw):
    license_info, status = get_switch_license(host, user, pw)
    if "Expire Date" in license_info.keys():
        license_info.update({'Support End Date': license_info["Expire Date"]})
    if status == C.RMA_FAILED:
        license = License(sn_num=sn,
                          license_expired=None,
                          lic_feature='Base Product,Layer3,OpenFlow',
                          lic_speed=license_info['type'],
                          status=constants.LicenseStatus.ACTIVE)
    else:
        license = License(sn_num=sn,
                          license_expired=license_info['Support End Date'],
                          lic_feature=','.join(license_info['Feature']),
                          lic_speed=license_info['Type'],
                          status=constants.LicenseStatus.ACTIVE)
    inven_db.insert(license)


def collect_import_linux_server(hosts,sns,hostnames, user, password):
    res = {}
    for (host,sn, hostname) in zip(hosts,sns, hostnames):
        switch = Switch(mgt_ip=host,sn=sn, version="Linux", revision="Unknow", platform_model="Linux-Srv",
                        host_name=hostname, import_type=constants.ImportType.IMPORT,
                        status=constants.SwitchStatus.IMPORTED, enable=True,
                        reachable_status=constants.REACHABLE)
        res[host] = 'ok'
        try:
            inven_db.insert_or_update(switch, primary_key='sn')
        except Exception as e:
            LOG.error(e)
            res[host] = 'no model config: ' + "Linux-Srv" + ' or duplicate with exist switches'
            monitor_db.add_event(host, 'error',
                                 'import switch ip:%s failed, no %s model config or duplicate with exist switches' % (host, "Linux-Srv"))
            continue
        monitor_db.add_event(host, 'info', 'import switch ip %s success' % host)

    return res