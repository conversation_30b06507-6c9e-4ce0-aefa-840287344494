
import os
import re
import subprocess

# version and revision
import configparser

# VERSION = '1.13.1'
# try:
#     base_dir = os.path.dirname(os.path.abspath(__file__))
# except NameError:
#     base_dir = None
# if base_dir is not None and os.path.exists(os.path.join(base_dir, ".commit")):
#     with open(os.path.join(base_dir, ".commit")) as fp:
#         REVISION = fp.read().strip()
# else:
#     REVISION = None

VERSION = None
try:
    base_dir = os.path.dirname(os.path.abspath(__file__))
except NameError:
    base_dir = None
with open(os.path.join(base_dir, ".env"), "r") as file:
    for line in file:
        if line.startswith("REACT_APP_VERSION="):
            VERSION = line.split("=")[1].strip()



# static email address
RMA_EMAIL = '<EMAIL>'

# upgrade default post-xorplus scripts path
UPGRADE_DEFAULT_SCRIPTS = 'image/upgrade_scripts/'

# upgrade tmp post-xorplus scripts path
UPGRADE_TMP_SCRIPTS = 'tmp/'

# upgrade group post-xorplus scripts path
UPGRADE_GROUP_SCRIPTS = 'group/'

POST_XORPLUS = 'post-xorplus'

AGENT_CONF = '/opt/auto-deploy/auto-deploy.conf'

SYSTEM_BACKUP_DIR = 'system_backup'

# openvpn configuration file
OPENVPN_CONFIG_FILE = '/etc/openvpn/server.conf'

# automation.ini
AUTOMATION_CONFIG_FILE = '/usr/share/automation/server/automation.ini'

# ansible playbook dir
ANSIBLE_PLAYBOOK_DIR = '/usr/share/automation/server/ansible_playbook/'

# ampcon base dir
AMPCON_BASE_DIR = '/usr/share/automation/server/'

# template_form_format.json file
TEMPLATE_FORM_FORMAT_FILE = '/usr/share/automation/server/template_form_format.json'

# for parse image info
IMAGE_NAME_REGEX_MAPPING = {
    "4.6.0E_x86_or_x86h": re.compile(r'^PicOS-(?P<version>[a-zA-Z\d.]+)-EC\d+-(?P<revision>[a-zA-Z\d]+)-fs-(?P<platform>x86h|x86)\.bin$'),
    "4.6.0E_S5860_or_S5810": re.compile(r'^(?P<platform>S5860|S5810)-PicOS-(?P<version>[a-zA-Z\d.]+)-EC\d+-(?P<revision>[a-zA-Z\d]+)\.bin$'),
    "4.6.0E_S3410_or_S3270": re.compile(r'^(?P<platform>S3410|S3270)-PicOS-(?P<version>[a-zA-Z\d.]+)-EC\d+-(?P<revision>[a-zA-Z\d]+)\.bin$'),
    "4.6.0E_DCN_x86_or_x86h": re.compile(r'^PicOS-(?P<version>[a-zA-Z\d.]+)-DCN-(?P<revision>[a-zA-Z\d]+)-fs-(?P<platform>x86h|x86)\.bin$'),
    "4.6.0E_common": re.compile(r'^PicOS-(?P<version>[a-zA-Z\d.]+)-(?P<revision>[a-zA-Z\d]+)-(?P<platform>x86h|x86|as4610|N3000|N3100)\.bin$'),
    'white_box_stable_release': re.compile(r'^onie-installer-PicOS-(?P<version>[a-zA-Z\d.]+)-(?P<revision>[a-zA-Z\d]+)-(?P<platform>x86h|as4610|x86)\.bin$'),
    'black_box_stable_release': re.compile(r'^(?P<platform>[a-zA-Z\d]+)-PicOS-(?P<version>[a-zA-Z\d.]+)-(?P<revision>[a-zA-Z\d]+).bin$'),
    'white_box_stable_x86h_release': re.compile(r'^PicOS-(?P<version>[a-zA-Z\d.]+)-(?P<revision>[a-zA-Z\d]+)-fs-(?P<platform>x86h)\.bin$'),
    'black_box_x86_stable_release': re.compile(r'^PicOS-(?P<version>[a-zA-Z\d.]+)-(?P<revision>[a-zA-Z\d]+)-fs-(?P<platform>x86)\.bin$'),
    'old_white_box': re.compile(r'^onie-installer-picos-(?P<version>[\d.]+)-(?P<revision>[a-zA-Z\d]+)-(?P<platform>x86|as4610|n3100|n3000)\.bin$'),
    'new_white_box_stable_release': re.compile(r'^onie-installer-picos-(?P<version>[\d.]+)-(?P<platform>[a-zA-Z\d]+)\.bin'),
    'new_white_box_transition_release': re.compile(r'^onie-installer-PICOS-(?P<version>[\d.]+)-fs-(?P<platform>[a-zA-Z\d]+)\.bin$'),
    'new_white_box_transition_research': re.compile(r'^onie-installer-PICOS-(?P<version>[\d.]+)-(?P<revision>[a-zA-Z\d]+)-fs-(?P<platform>[a-zA-Z\d]+)\.bin$'),
    'new_black_box_data_center_research': re.compile(r'^picos-(?P<version>[\d.]+)-(?P<revision>[a-zA-Z\d]+)-fs-(?P<platform>[a-zA-Z\d]+)\.bin$'),
    'new_black_box_campus_research': re.compile(r'^(?P<platform>[a-zA-Z\d]+)_picos-(?P<version>[\d.]+)-(?P<revision>[a-zA-Z\d]+)-fs.bin$'),
    'new_black_box_data_center_release': re.compile(r'^picos-(?P<version>[\d.]+)-fs-(?P<platform>[a-zA-Z\d]+)\.bin$'),
    'new_black_box_campus_release': re.compile(r'^(?P<platform>[a-zA-Z\d]+)_picos-(?P<version>[\d.]+)-fs.bin$'),
    'new_s3410_busy_box_release': re.compile(r'^(?P<platform>[a-zA-Z\d]+)_picos-(?P<version>[\d.]+)-(?P<revision>[a-zA-Z\d]+).bin$')
}

class SSH_MODE:
    LINUX = 0
    CLI = 1
    CONFIGURE = 2


class ImportType:
    DEPLOY = 0
    IMPORT = 1
    RMA = 2


# switch status
class SwitchStatus:
    INIT = 'Init'
    STAGING = 'Registered Not-staged'
    CONFIGURED = 'Configured'
    STAGED = 'Staged'
    REGISTERED = 'Registered'
    PROVISIONING_SUCCESS = 'Provisioning Success'
    PROVISIONING_FAILED = 'Provisioning Failed'
    IMPORTED = 'Imported'
    DECOM = 'DECOM'
    DECOM_INIT = 'DECOM-Init'
    DECOM_PENDING = 'DECOM-Pending'
    DECOM_MANUAL = 'DECOM-Manual'
    RMA = 'RMA'


class LicenseStatus:
    ACTIVE = 'Active'
    EXPIRING = 'Expiring'
    NOLICENSE = 'No License'
    EXPIRED = 'Expired'
    UNKNOWN = 'Unknown'

class CloudPlatform:
    BAREMETAL = 'BareMetal'
    VSPHERE = 'vSphere'
    OPENSTACK = 'OpenStack'
    
class OverlayStatus:
    CREATE_NORMAL = 0
    CREATE_ERROR = 1
    DELETE_NORMAL = 2
    DELETE_ERROR = 3
    
class DeployStatus:
    # 1-deploying 2-succeed 3-failed
    DEPLOYING = 1
    SUCCEED = 2
    FAILED = 3

# switch ansible status
REACHABLE = 0
UN_REACHABLE = 1
UNKNOWN = 2

# rma_status
RMA_UNKNOWN = 0
RMA_UN_REACHABLE = 1
RMA_FAILED = 2
RMA_ACTIVE = 3
RMA_UPGRADING = 4

# rma_back_status
RMA_BACK_UNKNOWN = 0
RMA_BACK_AUTO = 1
RMA_BACK_MANUAL = 2

# switch_config_backup_status
SWITCH_BACK_UNKNOWN = 0
SWITCH_BACK_AUTO = 1
SWITCH_BACK_MANUAL = 2

# switch default user
DEFAULT_USER = 'admin'
DEFAULT_PASSWORD = 'pica8'

BASE_DIR = '/home/<USER>/'
CONFIG_PATH = BASE_DIR + 'auto.config'
FLAG_FILE = BASE_DIR + 'auto_flag'
REBOOT_SLEEP_TIME = 240
ANSIBLE_TMP = BASE_DIR + '.ansible'
BACKUP_FILE = '/etc/picos/backup_files.lst'


OVSDB_CONFIG_NORMAL = 'normal'
OVSDB_CONFIG_ERROR = 'error'


INTERNAL_PLAYBOOK_GIT_DOWNLOAD_URL = 'https://github.com/pica8/Ansible/archive/refs/heads/main.zip'
INTERNAL_TEMPLATE_GIT_DOWNLOAD_URL = 'https://github.com/pica8/Jinja_Templates/archive/refs/heads/main.zip'


PICOS_V_SN = 'PICOS-V'
try:
    PICOS_V_IP = re.search('ip=\'(.*?)\'', subprocess.getstatusoutput('''virsh net-dumpxml default |grep "mac='.*' name='.*' ip='.*'"''')[1]).group(1).strip()
except:
    PICOS_V_IP = '**************'
PICOS_V_USERNAME = 'admin'
PICOS_V_PASSWORD = 'pica8'
GROUP_TYPE_DICT = {'upgrade': 'group_upgrade', 'push': 'group_push_image'}
DEFAULT_MAX_BACKUP_COUNT = 3
RANGE_MAX_BACKUP_COUNT = (1, 20)

DB_BACKUP_IGNORE_TABLE = ('db_backup', 'ansible_playbook', 'ansible_job', 'ansible_job_result', 'switch_systeminfo', 'switch_image_info')

FAKE_PASSWORD = '********'

CLI_UPDATE_URL = 'https://csp.pica8.com/sw-prod/Ampcon/PICOS-cli-Templates/automation.zip'

CLI_UPDATE_SQL_PATH = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'tmp', 'cli_tree')

SWITCH_MODEL_UPDATE_URL = 'https://csp.pica8.com/sw-prod/Ampcon/PICOS-cli-Templates/switch_model_update.zip'

SWITCH_MODEL_UPDATE_PATH = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'tmp', 'switch_model')

# NAME_MATCH_REGEX = re.compile('^[\w_\-\s\:]+$')
NAME_MATCH_REGEX = re.compile(r'^[\w\-:]+$')

LATITUDE_REGEX = re.compile('^(\-|\+)?([0-8]?\d{1}\.\d{0,6}|90\.0{0,6}|[0-8]?\d{1}|90)$')

LONGITUDE_REGEX = re.compile('^(\-|\+)?(((\d|[1-9]\d|1[0-7]\d|0{1,3})\.\d{0,6})|(\d|[1-9]\d|1[0-7]\d|0{1,3})|180\.0{0,6}|180)$')

GLOBAL_CONFIG_TAG = 'Global'

BLACK_BOX_MODEL_LIST = ['S5860-20SQ', 'S5860-24XB-U', 'S5810-48TS-P', 'S5810-28TS', 'S5810-28FS', 'S5810-48TS', 'S5810-48FS', 'N8560-32C', 'S5860-24MG-U', 'S5860-48XMG-U', 'S5860-24XMG', 'S5860-48MG-U', 'S5860-48XMG']

BUSY_BOX_MODEL_LIST = ['S3410L-24TF', 'S3410L-24TF-P', 'S3410-24TS', 'S3410-24TS-P', 'S3410C-16TF', 'S3410C-16TF-P', 'S3410C-16TMS-P', 'S3410C-8TMS-P','S3270-10TM','S3270-24TM','S3270-48TM','S3270-10TM-P','S3270-24TM-P']

SWITCH_MODEL_USE_UPGRADE_INSTEAD_OF_UPGRADE2 = ['S5810-48TS-P', 'S5810-28TS', 'S5810-28FS', 'S5810-48TS', 'S5810-48FS','S3270-10TM','S3270-24TM','S3270-48TM','S3270-10TM-P','S3270-24TM-P']

SWITCH_MODEL_STOP_PICOS_BEFORE_UPGRADE = ['S5860-20SQ', 'S5860-24XB-U', 'S5810-48TS-P', 'S5810-28TS', 'S5810-28FS', 'S5810-48TS', 'S5810-48FS', 'S5860-24MG-U', 'S5860-48XMG-U', 'S5860-24XMG', 'S5860-48MG-U', 'S5860-48XMG', 'S3410L-24TF', 'S3410L-24TF-P', 'S3410-24TS', 'S3410-24TS-P', 'S3410C-16TF', 'S3410C-16TF-P', 'S3410C-16TMS-P', 'S3410C-8TMS-P']

SWITCH_MODEL_SAVE_CONFIG_BEFORE_STOP_PICOS = ['S5860-20SQ', 'S5860-24XB-U', 'S5810-48TS-P', 'S5810-28TS', 'S5810-28FS', 'S5810-48TS', 'S5810-48FS', 'S5860-24MG-U', 'S5860-48XMG-U', 'S5860-24XMG', 'S5860-48MG-U', 'S5860-48XMG', 'S3410L-24TF', 'S3410L-24TF-P', 'S3410-24TS', 'S3410-24TS-P', 'S3410C-16TF', 'S3410C-16TF-P', 'S3410C-16TMS-P', 'S3410C-8TMS-P']

SWITCH_MODEL_ENABLE_ECN_PFC = ["as7726_32x", "as7326_56x", "S5248F-ON", "S5296F-ON", "S5232F-ON", "N8560-32C", "as9716_32d", "N5850-48X6C", "N8550-24CD8D", "N9550-32D", "N9550-64D", "N8550-64C", "N8550-48B8C", "N8550-32C", "N5850-48S6Q", "N9600-64OD"]

SWITCH_USE_MNT_PATH_INSTEAD_OF_CFTMP_WHEN_UPGRADING = ['S3410L-24TF', 'S3410L-24TF-P', 'S3410-24TS', 'S3410-24TS-P', 'S3410C-16TF', 'S3410C-16TF-P', 'S3410C-16TMS-P', 'S3410C-8TMS-P', 'S3410L-48TF', 'S3410-48TS', 'S3410-48TS-P']

SWITCH_S3270_MODEL = ['S3270-10TM','S3270-24TM','S3270-48TM','S3270-10TM-P','S3270-24TM-P']

CORE_SWITCH_MODEL = ["S5860-20SQ", "S6860-24CD8D", "S4720-48BC", "N5850-48S6Q", "N5850-48X6C", "N8550-32C", "N8550-48B8C", "N8550-64C", "N8560-32C", "N9550-32D", "N5850-48S6C", "S6860-24CD8D", "S5890-32C", "S4720-48BC","as4610_30p", "as4610_30t", "as4610_54p", "as4610_54t", "as4610_54t_b", "AS4625-54P", "AS4625-54T", "AS4630-54NPE", "AS4630-54PE", "AS4630-54TE", "as5712_54x", "as5812_54t", "as5812_54x", "as5835_54t", "as5835_54x", "as6812_32x", "as7312_54x", "as7326_56x", "as7712_32x", "as7726_32x", "as7816_64x", "HPE AL 6921-54T", "HPE AL 6921-54X", "N2224PX-ON", "N2224X-ON", "N2248PX-ON", "N2248X-ON", "N3024EP-ON", "N3024ET-ON", "N3048EP-ON", "N3048ET-ON", "N3132PX-ON", "N3208PX-ON", "N3224X-ON", "N3224F-ON", "N3224P-ON", "N3224PX-ON", "N3224T-ON", "N3248P-ON", "N3248PXE-ON", "N3248TE-ON", "N3248X-ON", "S4048-ON", "S4128F-ON", "S4128T-ON", "S4148F-ON", "S4148T-ON", "S5212F-ON", "S5224F-ON", "S5232F-ON", "S5248F-ON", "S5296F-ON", "Z9100-ON", "Z9264F-ON", "ag5648", "ag7648", "ag9032"]

ACCESS_SWITCH_MODEL = ["S5810-28FS", "S5810-28TS", "S5810-48FS", "S5810-48TS", "S5810-28FS-P", "S5860-20SQ", "S5860-24MG-U", "S5860-24XB-U", "S5860-24XMG", "S5860-48MG-U", "S5860-48XMG", "S5860-48XMG-U", "S5870-48T6BC", "S5870-48T6BC-U", "S5870-48T6S", "S5870-48T6S-U", "S5870-48MX6BC-U", "S4520-48X6C", "S4320M-48MX6BC-U","as4610_30p", "as4610_30t", "as4610_54p", "as4610_54t", "as4610_54t_b", "AS4625-54P", "AS4625-54T", "AS4630-54NPE", "AS4630-54PE", "AS4630-54TE", "as5712_54x", "as5812_54t", "as5812_54x", "as5835_54t", "as5835_54x", "as6812_32x", "as7312_54x", "as7326_56x", "as7712_32x", "as7726_32x", "as7816_64x", "HPE AL 6921-54T", "HPE AL 6921-54X", "N2224PX-ON", "N2224X-ON", "N2248PX-ON", "N2248X-ON", "N3024EP-ON", "N3024ET-ON", "N3048EP-ON", "N3048ET-ON", "N3132PX-ON", "N3208PX-ON", "N3224X-ON", "N3224F-ON", "N3224P-ON", "N3224PX-ON", "N3224T-ON", "N3248P-ON", "N3248PXE-ON", "N3248TE-ON", "N3248X-ON", "S4048-ON", "S4128F-ON", "S4128T-ON", "S4148F-ON", "S4148T-ON", "S5212F-ON", "S5224F-ON", "S5232F-ON", "S5248F-ON", "S5296F-ON", "Z9100-ON", "Z9264F-ON", "ag5648", "ag7648", "ag9032"]

IPCLOS_CORE_SWITCH_MODEL = ['as7726_32x', 'as6812_32x', 'as7326_56x', 'as5835_54t', 'as5835_54x', 'as5812_54t', 'as5812_54x', 'N3248P-ON', 'S5212F-ON', 'N3224PX-ON', 'N3248TE-ON', 'N3224F-ON', 'S5224F-ON', 'S5248F-ON', 'S5296F-ON', 'N3224T-ON', 'S5232F-ON', 'N3248X-ON', 'N3248PXE-ON', 'AS4630-54NPE', 'N3224P-ON', 'AS4630-54PE', 'N8550-32C', 'N8560-32C', 'S5890-32C']

IPCLOS_ACCESS_SWITCH_MODEL = ['as7726_32x', 'as6812_32x', 'as7326_56x', 'as5835_54t', 'as5835_54x', 'as5812_54t', 'as5812_54x', 'N3248P-ON', 'S5212F-ON', 'N3224PX-ON', 'N3248TE-ON', 'N3224F-ON', 'S5224F-ON', 'S5248F-ON', 'S5296F-ON', 'N3224T-ON', 'S5232F-ON', 'N3248X-ON', 'N3248PXE-ON', 'AS4630-54NPE', 'N3224P-ON', 'AS4630-54PE', 'S5870-48T6BC', 'S5870-48T6BC-U', 'S5870-48T6S', 'S5870-48T6S-U', 'S5870-48MX6BC-U']

IPCLOS_DISTRIBUTION_SWITCH_MODEL = ['as7726_32x', 'as6812_32x', 'as7326_56x', 'as5835_54t', 'as5835_54x', 'as5812_54t', 'as5812_54x', 'N3248P-ON', 'S5212F-ON', 'N3224PX-ON', 'N3248TE-ON', 'N3224F-ON', 'S5224F-ON', 'S5248F-ON', 'S5296F-ON', 'N3224T-ON', 'S5232F-ON', 'N3248X-ON', 'N3248PXE-ON', 'AS4630-54NPE', 'N3224P-ON', 'AS4630-54PE', 'N8550-48B8C', 'N8550-32C', 'N8550-64C', 'N8560-32C', 'S5890-32C', 'S5580-48Y']

IPCLOS_BORDER_SWITCH_MODEL = ['as7726_32x', 'as6812_32x', 'as7326_56x', 'as5835_54t', 'as5835_54x', 'as5812_54t', 'as5812_54x', 'N3248P-ON', 'S5212F-ON', 'N3224PX-ON', 'N3248TE-ON', 'N3224F-ON', 'S5224F-ON', 'S5248F-ON', 'S5296F-ON', 'N3224T-ON', 'S5232F-ON', 'N3248X-ON', 'N3248PXE-ON', 'AS4630-54NPE', 'N3224P-ON', 'AS4630-54PE']

DC_SUPPORT_SWITCH_MODELS = ["N5850-48S6Q", "N5850-48X6C", "N8550-48B8C", "N8550-32C", "N8560-32C", "N8550-64C", "N9550-32D", "N5850-48S6C", "N8550-24CD8D", "as4610_30p", "as4610_30t", "as4610_54p", "as4610_54t", "as4610_54t_b", "AS4625-54P", "AS4625-54T", "AS4630-54NPE", "AS4630-54PE", "AS4630-54TE", "as5712_54x", "as5812_54t", "as5812_54x", "as5835_54t", "as5835_54x", "as6812_32x", "as7312_54x", "as7326_56x", "as7712_32x", "as7726_32x", "as7816_64x", "N2224PX-ON", "N2224X-ON", "N2248PX-ON", "N2248X-ON", "N3208PX-ON", "N3224F-ON", "N3224P-ON", "N3224PX-ON", "N3224T-ON", "N3248P-ON", "N3248PXE-ON", "N3248TE-ON", "N3248X-ON", "N3024EP-ON", "N3024ET-ON", "N3048EP-ON", "N3048ET-ON", "N3132PX-ON", "S4048-ON", "S4128F-ON", "S4128T-ON", "S4148F-ON", "S4148T-ON", "S5212F-ON", "S5224F-ON", "S5232F-ON", "S5248F-ON", "S5296F-ON", "Z9100-ON", "Z9264F-ON", "ag5648", "ag7648", "ag9032", "HPE AL 6921-54T", "HPE AL 6921-54X"]

CAMPUS_SUPPORT_SWITCH_MODELS = ['as4610_30p', 'as4610_30t', 'as4610_54p', 'as4610_54t', 'AS4625-54P', 'AS4625-54T', 'AS4630-54NPE', 'AS4630-54PE', 'AS4630-54TE', 'as5712_54x', 'as5812_54t', 'as5812_54x', 'as5835_54t', 'as5835_54x', 'as6812_32x', 'as7312_54x', 'as7326_56x', 'as7712_32x', 'as7726_32x', 'as7816_64x', 'N2224PX-ON', 'N2224X-ON', 'N2248PX-ON', 'N2248X-ON', 'N3208PX-ON', 'N3224F-ON', 'N3224P-ON', 'N3224PX-ON', 'N3224T-ON', 'N3248P-ON', 'N3248PXE-ON', 'N3248TE-ON', 'N3248X-ON', 'N3024EP-ON', 'N3024ET-ON', 'N3048EP-ON', 'N3048ET-ON', 'N3132PX-ON', 'S4048-ON', 'S4128F-ON', 'S4128T-ON', 'S4148F-ON', 'S4148T-ON', 'S5212F-ON', 'S5224F-ON', 'S5232F-ON', 'S5248F-ON', 'S5296F-ON', 'Z9100-ON', 'Z9264F-ON', 'ag5648', 'ag7648', 'ag9032', 'HPE AL 6921-54T', 'HPE AL 6921-54X', 'S6860-24CD8D', 'S5890-32C', 'S5870-48T6BC', 'S5870-48T6BC-U', 'S5870-48T6S', 'S5870-48T6S-U', 'S5870-48MX6BC-U', 'S5860-20SQ', 'S5860-24MG-U', 'S5860-24XB-U', 'S5860-24XMG', 'S5860-48MG-U', 'S5860-48XMG', 'S5860-48XMG-U', 'S5810-28FS', 'S5810-28TS', 'S5810-48FS', 'S5810-48TS', 'S5810-48TS-P', 'S3410-24TS', 'S3410-24TS-P', 'S3410C-16TF', 'S3410C-16TF-P', 'S3410C-16TMS-P', 'S3410C-8TMS-P', 'S3410L-24TF', 'S3410L-24TF-P', 'S3270-10TM', 'S3270-24TM', 'S3270-48TM', 'S3270-10TM-P', 'S3270-24TM-P', 'N5850-48S6Q', 'N5850-48X6C', 'N5850-48S6C', 'S4720-48BC', 'S3410-48TS', 'S3410-48TS-P', 'S3410L-48TF', 'S5580-48Y', 'S5580-48S']

CSP_IMAGE_JSON_DOWNLOAD_URL = "http://10.56.20.242:8000/csp_image_test/images.json"

server_config = configparser.ConfigParser()
server_config.read(AUTOMATION_CONFIG_FILE)
try:
    connect_config = server_config.get('database', 'connection')
    pattern = re.compile(r'mysql\+pymysql://(.*)@')
    database_connection = pattern.findall(connect_config)[0]
    DATABASE_USER, DATABASE_PASSWD = database_connection.split(':')
except:
    DATABASE_USER = 'root'
    DATABASE_PASSWD = 'root'

class outswitch_status:
    UNKNOWN = 0
    UN_REACHABLE = 1
    FAILED = 2
    ACTIVE = 3
    DECOM = 4
    # DECOM_PENDING = 5
    DECOM_MANUAL = 5
    UPGRADING = 0
    UPGRADED = 1
    UPGRADE_FAILED = 2
    NO_UPGRADE = 3
    SCHEDULED = 6

class temperature_status:
    LOW_MAJOR = -10
    LOW_WARNING = -5
    HIGH_MAJOR = 75
    HIGH_WARNING = 70

class voltage_status:
    LOW_MAJOR = 2.97
    LOW_WARNING = 3.13
    HIGH_MAJOR = 3.63
    HIGH_WARNING = 3.46

power_thresholds = {
    # CFP series
    "openconfig-transport-types:CFP": {
        "outputPowerMin": -8, "outputPowerMax": 2,
        "inputPowerMin": -10, "inputPowerMax": 0,
    },
    "openconfig-transport-types:CFP2": {
        "outputPowerMin": -8, "outputPowerMax": 2,
        "inputPowerMin": -10, "inputPowerMax": 0,
    },
    "openconfig-transport-types:CFP2_ACO": {
        "outputPowerMin": -5, "outputPowerMax": 5,
        "inputPowerMin": -10, "inputPowerMax": 0,
    },
    "openconfig-transport-types:CFP4": {
        "outputPowerMin": -8, "outputPowerMax": 2,
        "inputPowerMin": -10, "inputPowerMax": 0,
    },

    # QSFP series
    "openconfig-transport-types:QSFP": {
        "outputPowerMin": -8, "outputPowerMax": 2,
        "inputPowerMin": -10, "inputPowerMax": 0,
    },
    "openconfig-transport-types:QSFP28": {
        "outputPowerMin": -8, "outputPowerMax": 2,
        "inputPowerMin": -10, "inputPowerMax": 0,
    },
    "openconfig-transport-types:QSFP28_DD": {
        "outputPowerMin": -8, "outputPowerMax": 2,
        "inputPowerMin": -10, "inputPowerMax": 0,
    },
    "openconfig-transport-types:QSFP56": {
        "outputPowerMin": -8, "outputPowerMax": 2,
        "inputPowerMin": -10, "inputPowerMax": 0,
    },
    "openconfig-transport-types:QSFP56_DD": {
        "outputPowerMin": -8, "outputPowerMax": 2,
        "inputPowerMin": -10, "inputPowerMax": 0,
    },
    "openconfig-transport-types:QSFP56_DD_TYPE1": {
        "outputPowerMin": -8, "outputPowerMax": 2,
        "inputPowerMin": -10, "inputPowerMax": 0,
    },
    "openconfig-transport-types:QSFP56_DD_TYPE2": {
        "outputPowerMin": -8, "outputPowerMax": 2,
        "inputPowerMin": -10, "inputPowerMax": 0,
    },
    "openconfig-transport-types:QSFP_PLUS": {
        "outputPowerMin": -8, "outputPowerMax": 2,
        "inputPowerMin": -10, "inputPowerMax": 0,
    },

    # SFP series
    "openconfig-transport-types:SFP": {
        "outputPowerMin": -9, "outputPowerMax": -1,
        "inputPowerMin": -17, "inputPowerMax": -1,
    },
    "openconfig-transport-types:SFP_PLUS": {
        "outputPowerMin": -9, "outputPowerMax": -1,
        "inputPowerMin": -17, "inputPowerMax": -1,
    },
    "openconfig-transport-types:SFP28": {
        "outputPowerMin": -8, "outputPowerMax": 4,
        "inputPowerMin": -12, "inputPowerMax": -1,
    },
    "openconfig-transport-types:SFP56": {
        "outputPowerMin": -8, "outputPowerMax": 4,
        "inputPowerMin": -12, "inputPowerMax": -1,
    },
    "openconfig-transport-types:SFP_DD": {
        "outputPowerMin": -8, "outputPowerMax": 4,
        "inputPowerMin": -12, "inputPowerMax": -1,
    },

    # Other types
    "openconfig-transport-types:CPAK": {
        "outputPowerMin": -8, "outputPowerMax": 2,
        "inputPowerMin": -10, "inputPowerMax": 0,
    },
    "openconfig-transport-types:CSFP": {
        "outputPowerMin": -9, "outputPowerMax": -1,
        "inputPowerMin": -17, "inputPowerMax": -1,
    },
    "openconfig-transport-types:DSFP": {
        "outputPowerMin": -8, "outputPowerMax": 4,
        "inputPowerMin": -12, "inputPowerMax": -1,
    },
    "openconfig-transport-types:XFP": {
        "outputPowerMin": -8, "outputPowerMax": 2,
        "inputPowerMin": -10, "inputPowerMax": 0,
    },
    "openconfig-transport-types:X2": {
        "outputPowerMin": -8, "outputPowerMax": 2,
        "inputPowerMin": -10, "inputPowerMax": 0,
    },
    "openconfig-transport-types:OSFP": {
        "outputPowerMin": -8, "outputPowerMax": 2,
        "inputPowerMin": -10, "inputPowerMax": 0,
    },
}