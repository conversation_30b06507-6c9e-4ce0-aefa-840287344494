import re
import configparser


import pymysql

automation_config_path = "/usr/share/automation/server/automation.ini"
update_automation_config_path = "/usr/share/automation/server/upgrade_automation.ini"

server_config = configparser.ConfigParser()
server_config.read(automation_config_path)
database_connection = server_config.get('database', 'connection')
database_parse_result = re.search(r'.*://(\w+):(\w+)@([\w-]+)/.*', database_connection)

database_user = database_parse_result.group(1)
database_passwd = database_parse_result.group(2)
database_host = database_parse_result.group(3)

support_models_delta = []

conn = pymysql.connect(
    host=database_host,
    port=3306,
    user=database_user,
    password=database_passwd,
    database='automation',
    charset='utf8'
)

cursor = conn.cursor(cursor=pymysql.cursors.DictCursor)


def reset_automation_task_running_status():
    cursor.execute('''UPDATE automation_task SET task_status = 'failure' WHERE task_status = 'running';''')
    conn.commit()


def reset_switch_upgrade_running_status():
    cursor.execute('''UPDATE switch SET upgrade_status = 2 WHERE upgrade_status = 0;''')
    conn.commit()


def reset_ansible_job_running_status():
    cursor.execute('''UPDATE ansible_job SET status = 'EXECUTED' WHERE status = 'RUNNING';''')
    conn.commit()


def reset_config_distribution_task_for_campus_running_status():
    cursor.execute('''UPDATE config_distribution_task_for_campus SET task_status = 3 WHERE task_status in (0, 1);''')
    conn.commit()


def reset_config_distribution_task_for_dc_running_status():
    cursor.execute('''UPDATE config_distribution_task_for_dc SET task_status = 3 WHERE task_status in (0, 1);''')
    conn.commit()


if __name__ == '__main__':
    reset_automation_task_running_status()
    reset_switch_upgrade_running_status()
    reset_ansible_job_running_status()
    reset_config_distribution_task_for_campus_running_status()
    reset_config_distribution_task_for_dc_running_status()