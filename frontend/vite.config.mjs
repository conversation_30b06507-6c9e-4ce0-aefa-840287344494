import {defineConfig} from "vite";
import react from "@vitejs/plugin-react-swc";
import path from "path";
import svgr from "vite-plugin-svgr";

export default defineConfig({
    envPrefix: ["VITE_", "REACT_"],
    css: {
        preprocessorOptions: {
            scss: {
                api: "modern-compiler"
            }
        }
    },
    plugins: [
        svgr({
            svgrOptions: {
                icon: false,
                dimensions: true,
                expandProps: "end"
            }
        }),
        react({
            swcOptions: {
                jsc: {
                    cache: true,
                    parser: {
                        syntax: "ecmascript",
                        jsx: true
                    },
                    transform: {
                        react: {
                            runtime: "automatic"
                        }
                    }
                }
            }
        })
    ],
    optimizeDeps: {
        include: ["react", "react-dom", "antd", "@ant-design/icons"],
        exclude: ["@antv/x6", "@antv/x6-react-shape"]
    },
    assetsInclude: ["**/*.png", "**/*.htm"],
    resolve: {
        alias: {
            "@": path.resolve(__dirname, "src")
        }
    },
    server: {
        proxy: {
            "/ampcon": {
                // target: "https://************",
                target: "https://************",
                changeOrigin: true,
                secure: false

            },
            "/smb": {
                target: "https://************",
                changeOrigin: true,
                secure: false
            }
            // "/otn": {
            //     target: "https://10.56.20.139",
            //     changeOrigin: true,
            //     secure: false
            // },
            // "/otn/api/ws": {
            //     target: "wss://10.56.20.216",
            //     changeOrigin: true,
            //     ws: true,
            //     secure: false
            // }
        },
        port: 3000,
        open: false,
        hot: true
    }
});
