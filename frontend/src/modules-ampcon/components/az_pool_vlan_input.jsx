import React, {useEffect, useState} from "react";
import {InputNumber, Row, Col, Form} from "antd";

export const AzPoolVlanInput = ({
    startOnBlurValidator,
    endOnBlurValidator,
    startDefaultValue = "",
    endDefaultValue = "",
    index,
    label
}) => {
    const [isShownError, setIsShownError] = useState([false, false]);
    const [errorMessageList, setErrorMessageList] = useState([]);
    const [shownErrorMessageStr, setShownErrorMessageStr] = useState("");
    const [startInputValue, setStartInputValue] = useState(startDefaultValue);
    const [endInputValue, setEndInputValue] = useState(endDefaultValue);
    const [isOnBlur, setIsOnBlur] = useState(false);
    useEffect(() => {
        const checkIntervalValidatorId = setInterval(() => {
            if (isOnBlur) {
                return;
            }
            if (isShownError[0]) {
                inputStartValidator().then(() => {});
            } else if (isShownError[1]) {
                inputEndValidator().then(() => {});
            }
        }, 500);
        return () => clearInterval(checkIntervalValidatorId);
    }, [isShownError]);
    useEffect(() => {
        if (errorMessageList.length !== 0) {
            setShownErrorMessageStr(errorMessageList[0]);
        } else {
            setShownErrorMessageStr("");
            setIsShownError([false, false]);
        }
    }, [errorMessageList]);

    const inputStartValidator = async () => {
        let isStartInputValueValid = true;
        let isEndInputValueValid = true;
        setIsOnBlur(true);
        const startInputInvalidMessageList = [];
        await startOnBlurValidator(startInputValue).catch(err => {
            isStartInputValueValid = false;
            startInputInvalidMessageList.push(err.message);
        });
        await endOnBlurValidator(endInputValue).catch(() => {
            isEndInputValueValid = false;
        });
        setIsShownError(prevState => [!isStartInputValueValid, prevState[1]]);
        if (isStartInputValueValid && isEndInputValueValid) {
            setErrorMessageList([]);
        } else if (!isStartInputValueValid) {
            setErrorMessageList([...startInputInvalidMessageList]);
        }
    };

    const inputEndValidator = async () => {
        let isStartInputValueValid = true;
        let isEndInputValueValid = true;
        setIsOnBlur(true);
        const endInputInvalidMessageList = [];
        await startOnBlurValidator(startInputValue).catch(() => {
            isStartInputValueValid = false;
        });
        await endOnBlurValidator(endInputValue).catch(err => {
            isEndInputValueValid = false;
            endInputInvalidMessageList.push(err.message);
        });
        setIsShownError(prevState => [prevState[0], !isEndInputValueValid]);
        if (isStartInputValueValid && isEndInputValueValid) {
            setErrorMessageList([]);
        } else if (!isEndInputValueValid) {
            setErrorMessageList([...endInputInvalidMessageList]);
        }
    };

    return (
        <>
            <Row gutter={8}>
                <Col>
                    <Form.Item
                        name={[index, "startVLAN"]}
                        label={label}
                        labelCol={{style: {width: "154px"}}}
                        validateTrigger={["onBlur", "onSubmit"]}
                        validateStatus={isShownError[0] ? "error" : ""}
                        className="vlan-range-list"
                        required
                        rules={[
                            {
                                required: true,
                                message: "Please input VLAN"
                            }
                        ]}
                    >
                        <InputNumber
                            style={{
                                width: 123
                            }}
                            onChange={value => {
                                setStartInputValue(value);
                                setIsOnBlur(false);
                            }}
                            onBlur={inputStartValidator}
                        />
                    </Form.Item>
                </Col>
                <Col align="center">
                    <div style={{width: 16, marginTop: "3px"}}>-</div>
                </Col>
                <Col>
                    <Form.Item
                        name={[index, "endVLAN"]}
                        validateTrigger={["onBlur", "onSubmit"]}
                        validateStatus={isShownError[1] ? "error" : ""}
                        className="vlan-range-list"
                        rules={[
                            {
                                required: true,
                                message: "Please input VLAN"
                            }
                        ]}
                    >
                        <InputNumber
                            style={{
                                width: 123
                            }}
                            onChange={value => {
                                setEndInputValue(value);
                                setIsOnBlur(false);
                            }}
                            onBlur={inputEndValidator}
                        />
                    </Form.Item>
                </Col>
            </Row>
            <Row>
                {isShownError[0] || isShownError[1] ? (
                    <div
                        className="errmsg"
                        style={{
                            width: "280px",
                            color: "#ff4d4f",
                            marginLeft: "154px",
                            marginTop: "-23px"
                        }}
                    >
                        {shownErrorMessageStr}
                    </div>
                ) : null}
            </Row>
        </>
    );
};
