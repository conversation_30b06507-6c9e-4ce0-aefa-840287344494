import {Button, Divider, Flex, Input, Modal} from "antd";

const ConfigFilePreviewModal = ({
    title,
    isOpen,
    globalConfigContent,
    siteConfigContent,
    saveCallback,
    cancelButtonCallback,
    exportButtonCallback
}) => {
    const readonlyStyle = {
        minHeight: "240px",
        resize: "vertical",
        border: "2px solid rgb(206, 212, 218)",
        fontSize: "16px",
        borderRadius: "4px",
        boxShadow: "none",
        marginBottom: "30px"
    };

    return (
        <Modal
            className="ampcon-middle-modal"
            title={
                <div>
                    {title}
                    <Divider style={{marginTop: 12, marginBottom: 0}} />
                </div>
            }
            open={isOpen}
            onCancel={cancelButtonCallback}
            footer={
                <>
                    <Divider style={{marginTop: 0, marginBottom: 20}} />
                    <Button key="export" onClick={exportButtonCallback}>
                        Export
                    </Button>
                    <Button key="cancel" onClick={cancelButtonCallback}>
                        Cancel
                    </Button>
                    <Button key="submit" type="primary" onClick={saveCallback}>
                        Save
                    </Button>
                </>
            }
        >
            <Flex vertical style={{flex: 1}}>
                <Flex layout="horizontal" style={{flex: 1, marginTop: -25}}>
                    <h4>Global Config</h4>
                </Flex>
                <Input.TextArea
                    autoSize={{minRows: 1}}
                    style={{
                        border: "none",
                        backgroundColor: "#F8FAFB",
                        fontSize: "16px",
                        borderRadius: "4px",
                        boxShadow: "none",
                        resize: "none",
                        padding: "16px"
                    }}
                    value={globalConfigContent}
                    readOnly
                />
                <Flex layout="horizontal" style={{flex: 1}}>
                    <h4>Generated Template Config</h4>
                </Flex>
                <Input.TextArea
                    autoSize={{minRows: 1}}
                    style={{
                        border: "none",
                        backgroundColor: "#F8FAFB",
                        fontSize: "16px",
                        borderRadius: "4px",
                        boxShadow: "none",
                        resize: "none",
                        padding: "16px"
                    }}
                    value={siteConfigContent}
                    readOnly
                />
            </Flex>
        </Modal>
    );
};

export default ConfigFilePreviewModal;
