import {request} from "@/utils/common/request";

const baseURL = "/ampcon/rma";

export function queryConfig(ipaddr) {
    return request({
        url: `${baseURL}/config/${ipaddr}`,
        method: "GET"
    });
}

export function uploadConfig(configFile, sn, ip) {
    const formData = new FormData();
    formData.append("config_file", configFile);
    formData.append("sn", sn);
    formData.append("ip", ip);
    return request({
        url: `${baseURL}/up_config`,
        method: "POST",
        data: formData,
        headers: {
            "Content-Type": "multipart/form-data"
        }
    });
}

export function rollbackConfig(sn, snapshot_id, commit_wait_time) {
    return request({
        url: `${baseURL}/rollback_config`,
        method: "POST",
        data: {
            sn,
            snapshot_id,
            commit_wait_time
        }
    });
}

export function importVPNSwitch(ip, systemConfigName, group, fabric, site) {
    return request({
        url: `${baseURL}/manual/vpn/import/${ip}`,
        method: "POST",
        data: {
            systemConfigName,
            group,
            fabric,
            site
        },
        timeout: 60000
    });
}

export function adoptSwitch(sn, group) {
    return request({
        url: `${baseURL}/manual/vpn/${sn}`,
        method: "POST",
        data: {
            group
        }
    });
}

export function doRMA(oldSN, oldIp, newSN, staged, systemConfigName) {
    return request({
        url: `${baseURL}/rma/do`,
        method: "POST",
        data: {
            oldSN,
            oldIp,
            newSN,
            staged,
            systemConfigName
        }
    });
}

export function doDecom(sn) {
    return request({
        url: `${baseURL}/dcom`,
        method: "POST",
        data: {
            sn
        }
    });
}

export function upgradeSwitch(sn) {
    const formData = new FormData();
    formData.append("sn", sn);
    return request({
        url: `${baseURL}/upgrade`,
        method: "POST",
        data: formData,
        headers: {
            "Content-Type": "multipart/form-data"
        }
    });
}

export function batchUpgradeSwitch(imageName, snList, scriptFileList) {
    const formData = new FormData();
    formData.append("imageName", imageName);
    scriptFileList.forEach(file => {
        formData.append("file", file);
    });
    snList.forEach(sn => {
        formData.append("sn", sn);
    });
    return request({
        url: `${baseURL}/batch_upgrade`,
        method: "POST",
        data: formData,
        headers: {
            "Content-Type": "multipart/form-data"
        }
    });
}

export function batchPushImageSwitch(imageName, snList) {
    const formData = new FormData();
    formData.append("imageName", imageName);
    snList.forEach(sn => {
        formData.append("sn", sn);
    });
    return request({
        url: `${baseURL}/batch_push_image`,
        method: "POST",
        data: formData,
        headers: {
            "Content-Type": "multipart/form-data"
        }
    });
}

export function scheduleUpgradeSwitch(imageName, snList, scriptFileList, startDate) {
    const formData = new FormData();
    formData.append("imageName", imageName);
    scriptFileList.forEach(file => {
        formData.append("file", file);
    });
    snList.forEach(sn => {
        formData.append("sn", sn);
    });
    formData.append("startDate", startDate);
    return request({
        url: `${baseURL}/switch/schedule/upgrade`,
        method: "POST",
        headers: {
            "Content-Type": "multipart/form-data"
        },
        data: formData
    });
}

export function pushImage(sn, imageName) {
    return request({
        url: `${baseURL}/push_image`,
        method: "POST",
        data: {
            sn,
            imageName
        }
    });
}

export function checkImage(sn, imageName) {
    return request({
        url: `${baseURL}/check_image`,
        method: "POST",
        data: {
            sn,
            imageName
        }
    });
}

export function fetchUpgradeTaskTableData(page, pageSize, filterFields = [], sortFields = [], searchFields = {}) {
    return request({
        url: `${baseURL}/fetch_upgrade_task_table_data`,
        method: "POST",
        data: {
            page,
            pageSize,
            filterFields,
            sortFields,
            searchFields
        }
    });
}

export function cancelUpgradeTask(jobIds) {
    return request({
        url: `${baseURL}/cancel_upgrade_task`,
        method: "POST",
        data: {
            jobIds
        }
    });
}

export function deleteUpgradeTask(jobIds) {
    return request({
        url: `${baseURL}/delete_upgrade_task`,
        method: "POST",
        data: {
            jobIds
        }
    });
}

export function fetchUpgradeTaskLog(jobId) {
    return request({
        url: `${baseURL}/fetch_upgrade_task_log`,
        method: "POST",
        data: {
            jobId
        }
    });
}

export function updateUpgradeTask(jobIds, start_date) {
    return request({
        url: `${baseURL}/update_upgrade_task`,
        method: "POST",
        data: {
            jobIds,
            start_date
        }
    });
}

export function upgradeTaskExecuteNow(jobIds) {
    return request({
        url: `${baseURL}/upgrade_task_execute_now`,
        method: "POST",
        data: {
            jobIds
        }
    });
}
