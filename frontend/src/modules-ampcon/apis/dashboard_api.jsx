import {request} from "@/utils/common/request";

const baseURL = "/ampcon";

export function queryLog(sn) {
    return request({
        url: `${baseURL}/display_log/${sn}`,
        method: "GET"
    });
}

export function queryReport(sn) {
    return request({
        url: `${baseURL}/report/${sn}`,
        method: "GET"
    });
}

export function queryConfig(sn) {
    return request({
        url: `${baseURL}/display_conf/${sn}`,
        method: "GET"
    });
}

export function fetchPlatformModel() {
    return request({
        url: `${baseURL}/platform_model`,
        method: "GET"
    });
}

export function fetchSwitchStatus() {
    return request({
        url: `${baseURL}/switch_status`,
        method: "GET"
    });
}

export function fetchLicenseStatus() {
    return request({
        url: `${baseURL}/license/portal/status`,
        method: "GET"
    });
}

export function fetchVlanStatics() {
    return request({
        url: `${baseURL}/host/access/vlan/statics`,
        method: "GET"
    });
}

export function fetchVxlanStatics() {
    return request({
        url: `${baseURL}/virtual/statics`,
        method: "GET"
    });
}

export function fetchLicenseExpiring() {
    return request({
        url: `${baseURL}/switch/license/expiring`,
        method: "GET"
    });
}

export function fetchConfigTemplate() {
    return request({
        url: `${baseURL}/config_template`,
        method: "GET"
    });
}

export function fetchStatistic() {
    return request({
        url: `${baseURL}/statistic`,
        method: "GET"
    });
}

export function fetchStaticHistory() {
    return request({
        url: `${baseURL}/get_machine_history_info`,
        method: "GET"
    });
}

export function fetchDeploymentTask() {
    return request({
        url: `${baseURL}/deployment_task`,
        method: "GET"
    });
}

export function fetchDeploymentOnlyTask() {
    return request({
        url: `${baseURL}/deployment_only_task`,
        method: "GET"
    });
}

export function fetchAllSwitch(page, pageSize, filterFields = [], sortFields = [], searchFields = {}) {
    return request({
        url: `${baseURL}/all_switch_table/data`,
        method: "POST",
        data: {
            filterFields,
            sortFields,
            searchFields,
            page,
            pageSize
        }
    });
}

export function fetchAllSwitchWithFabric(page, pageSize, filterFields = [], sortFields = [], searchFields = {}) {
    return request({
        url: `${baseURL}/all_switch_table_with_fabirc/data`,
        method: "POST",
        data: {
            filterFields,
            sortFields,
            searchFields,
            page,
            pageSize
        }
    });
}

export function fetchImportedAndProvisioningSuccessSwitch(
    page,
    pageSize,
    filterFields = [],
    sortFields = [],
    searchFields = {}
) {
    return request({
        url: `${baseURL}/imported_and_provisioning_success_switch_table/data`,
        method: "POST",
        data: {
            filterFields,
            sortFields,
            searchFields,
            page,
            pageSize
        }
    });
}

export function setDebugState(flag) {
    return request({
        url: `${baseURL}/debug/${flag}`,
        method: "GET"
    });
}

export function updateSwitchStageStatus(sn, stageStatus) {
    // stageStatus: stage or unStage
    return request({
        url: `${baseURL}/switch/${sn}/${stageStatus}`,
        method: "GET"
    });
}

export function queryAgentConfig(sn) {
    return request({
        url: `${baseURL}/switch/${sn}/agent_conf`,
        method: "GET"
    });
}

export function delSwitch(sn) {
    return request({
        url: `${baseURL}/del_switch`,
        method: "POST",
        data: {
            sn
        }
    });
}

export function getLicenseInfo() {
    return request({
        url: `${baseURL}/get_lic`,
        method: "GET"
    });
}

export function importLicense(license) {
    return request({
        url: `${baseURL}/install_lic`,
        method: "POST",
        data: {
            data: license
        }
    });
}

export function importLicenseFile(data) {
    const formData = new FormData();
    Object.keys(data).forEach(key => {
        if (key === "licensekey") {
            formData.append(key, data[key].target.files[0]);
        } else {
            formData.append(key, data[key]);
        }
    });
    return request({
        headers: {
            "Content-Type": "multipart/form-data"
        },
        data: formData,
        url: `${baseURL}/install_lic`,
        method: "POST"
    });
}

export function invalidateLicense(licenseId, hwids) {
    return request({
        url: `${baseURL}/invalidate_lic`,
        method: "POST",
        data: {
            licenseId,
            hwids
        }
    });
}

export function getLicenseLog(page, pageSize, filterFields = [], sortFields = [], searchFields = {}) {
    return request({
        url: `${baseURL}/get_lic_log`,
        method: "POST",
        data: {
            filterFields,
            sortFields,
            searchFields,
            page,
            pageSize
        }
    });
}

export function delSwitchWithCheckLic(license, hwid) {
    return request({
        url: `${baseURL}/del_switch_with_check`,
        method: "POST",
        data: {
            license,
            hwid
        }
    });
}

export function getLocalKey() {
    return request({
        url: `${baseURL}/get_local_key`,
        method: "GET"
    });
}

export function checkLicense() {
    return request({
        url: `${baseURL}/check_lic`,
        method: "GET"
    });
}

export function getRecentAlarmCount(startTime, endTime) {
    return request({
        url: `${baseURL}/get_recent_alarm_count`,
        method: "GET",
        params: {
            start_time: startTime,
            end_time: endTime
        }
    });
}

export function fetchSnmpInfo(page, pageSize, filterFields = [], sortFields = [], searchFields = {}) {
    return request({
        url: `${baseURL}/snmp_table/data`,
        method: "POST",
        data: {
            filterFields,
            sortFields,
            searchFields,
            page,
            pageSize
        }
    });
}

export function updateSnmpDevice(data) {
    return request({
        url: `${baseURL}/update_snmp_device`,
        method: "POST",
        data
    });
}

export function batchUpdateSnmpDevice(formData) {
    return request({
        headers: {
            "Content-Type": "multipart/form-data"
        },
        url: `${baseURL}/update_snmp_device`,
        method: "POST",
        data: formData
    });
}

export function removeSnmpDevice(data) {
    return request({
        url: `${baseURL}/remove_snmp_device`,
        method: "POST",
        data
    });
}
