import {request} from "@/utils/common/request";
import {message} from "antd";

const baseURL = "/ampcon/roce";

export function getRoceTaskTable(page, pageSize, filterFields = [], sortFields = [], searchFields = {}) {
    return request({
        url: `${baseURL}/event/get_roce_task_table`,
        method: "POST",
        data: {page, pageSize, filterFields, sortFields, searchFields}
    });
}

export function getResultInfo(taskId) {
    return request({
        url: `${baseURL}/event/get_result`,
        method: "POST",
        data: {task_id: taskId}
    });
}

// 后端配置下发预览："/template/preview"
export function fetchTemplatePreview(serverType) {
    return request({
        url: `${baseURL}/template/preview`,
        method: "POST",
        data: {
            // serverType
            server_type: serverType
        }
    });
}

// 配置下发："/server/configure"
export function fetchServerConfig(portInfo, deviceType, scriptParams) {
    return request({
        url: `${baseURL}/server/configure`,
        method: "POST",
        data: {
            portInfo,
            device_type: deviceType,
            script_params: scriptParams
        }
    });
}

export function roceConfigurationByForm(playBookValue) {
    return request({
        url: `${baseURL}/server/configuration_by_form`,
        method: "POST",
        data: {
            nic_vendor: playBookValue.nic_vendor,
            nic_ports: playBookValue.nic_ports,
            script_params: playBookValue.script_params
        }
    });
}

export function roceGetFabricSwitches(datas) {
    return request({
        url: `${baseURL}/get_fabric_switches`,
        method: "POST",
        data: datas
    });
}

export function saveDlbConfiguration(switchList, configuration, type) {
    // 构建符合后端要求的请求体
    const requestBody = {
        switch: switchList.map(switches => ({
            sysname: switches.sysname,
            switch_sn: switches.switch_sn
        })),
        configuration: {
            enabled: configuration.enabled,
            dlb_mode: configuration.dlb_mode
        }
    };
    return request({
        url: `${baseURL}/dlb_config/${type}`,
        method: "POST",
        data: requestBody,
        headers: {
            "Content-Type": "application/json"
        }
    });
}

export function roceDlbConfigList(page, pageSize, filterFields = [], sortFields = [], searchFields = {}) {
    return request({
        url: `${baseURL}/dlb_config/list`,
        method: "POST",
        data: {
            page,
            pageSize,
            filterFields,
            sortFields,
            searchFields
        }
    });
}

export function easydeployConfigSave(data, type) {
    return request({
        url: `${baseURL}/easydeploy_config/${type}`,
        method: "POST",
        data
    });
}

export function easydeployConfigList(page, pageSize, filterFields = [], sortFields = [], searchFields = {}) {
    return request({
        url: `${baseURL}/easydeploy_config/list`,
        method: "POST",
        data: {
            page,
            pageSize,
            filterFields,
            sortFields,
            searchFields
        }
    });
}

// export function roceDlbConfigPreview(synameList, configuration) {
//     const requestBody = {
//         sysname: synameList,
//         configuration: {
//             enabled: configuration.enabled,
//             dlb_mode: String(configuration.dlb_mode)
//         }
//     };
//     return request({
//         url: `${baseURL}/dlb_config/preview`,
//         method: "POST",
//         data: requestBody,
//         headers: {
//             "Content-Type": "application/json"
//         }
//     });
// }

export function easydeployConfigDelete(data) {
    return request({
        url: `${baseURL}/easydeploy_config/delete`,
        method: "POST",
        data
    });
}

export function getFabricSwitches() {
    return request({
        url: `${baseURL}/get_fabric_switches`,
        method: "POST"
    });
}

export function getSwitchPorts(data) {
    return request({
        url: `${baseURL}/get_ports_by_switch_sn`,
        method: "POST",
        data
    });
}

export function getFilterSwitchPorts(data) {
    return request({
        url: `${baseURL}/get_filter_ports_by_switch_sn`,
        method: "POST",
        data
    });
}

export function getFilterSwitchQueues(data) {
    return request({
        url: `${baseURL}/get_filter_queues_by_switch_sn`,
        method: "POST",
        data
    });
}

// export function easydeployConfigDetails(data) {
//     return request({
//         url: `${baseURL}/easydeploy_config/preview`,
//         method: "POST",
//         data
//     });
// }

export function validateEasydeployConfig(data) {
    return request({
        url: `${baseURL}/easydeploy_config/validate`,
        method: "POST",
        data
    });
}

export function easydeployConfigOverview(data) {
    return request({
        url: `${baseURL}/easydeploy_config/overview`,
        method: "POST",
        data
    });
}

export function getPfcConfigList(page, pageSize, filterFields = [], sortFields = [], searchFields = {}) {
    return request({
        url: `${baseURL}/pfc_config/list`,
        method: "POST",
        data: {
            page,
            pageSize,
            filterFields,
            sortFields,
            searchFields
        }
    });
}

export function deletePfcConfig(config_id) {
    return request({
        url: `${baseURL}/pfc_config/delete`,
        method: "POST",
        data: {
            config_id: String(config_id)
        }
    });
}

export function savePfcConfig(data) {
    return request({
        url: `${baseURL}/pfc_config/save`,
        method: "POST",
        data
    });
}

export function updatePfcConfig(data) {
    return request({
        url: `${baseURL}/pfc_config/update`,
        method: "POST",
        data
    });
}

export function validatePfcConfig(data) {
    return request({
        url: `${baseURL}/pfc_config/validate`,
        method: "POST",
        data
    });
}

export function getPfcConfigDetailBySwitch(data) {
    return request({
        url: `${baseURL}/pfc_config/detail_by_switch`,
        method: "POST",
        data
    });
}

export function getPfcBufferTrafficConfigList(
    page,
    pageSize,
    filterFields = [],
    sortFields = [],
    searchFields = {},
    extraParams = {}
) {
    return request({
        url: `${baseURL}/pfc_buffer_config/list`,
        method: "POST",
        data: {
            page,
            pageSize,
            filterFields,
            sortFields,
            searchFields,
            ...extraParams
        }
    });
}

export function savePfcBufferConfig(data) {
    return request({
        url: `${baseURL}/pfc_buffer_config/save`,
        method: "POST",
        data
    });
}

export function updatePfcBufferConfig(data) {
    return request({
        url: `${baseURL}/pfc_buffer_config/update`,
        method: "POST",
        data
    });
}

export function deletePfcBufferConfig(data) {
    return request({
        url: `${baseURL}/pfc_buffer_config/delete`,
        method: "POST",
        data
    });
}

export function getPfcBufferConfigDetailBySwitch(data) {
    return request({
        url: `${baseURL}/pfc_buffer_config/detail_by_switch`,
        method: "POST",
        data
    });
}

export function getPfcWdConfigList(page, pageSize, filterFields = [], sortFields = [], searchFields = {}) {
    return request({
        url: `${baseURL}/pfc_wd_config/list`,
        method: "POST",
        data: {
            page,
            pageSize,
            filterFields,
            sortFields,
            searchFields
        }
    });
}

export function savePfcWdConfig(data) {
    return request({
        url: `${baseURL}/pfc_wd_config/save`,
        method: "POST",
        data
    });
}

export function updatePfcWdConfig(data) {
    return request({
        url: `${baseURL}/pfc_wd_config/update`,
        method: "POST",
        data
    });
}

export function deletePfcWdConfig(data) {
    return request({
        url: `${baseURL}/pfc_wd_config/delete`,
        method: "POST",
        data
    });
}

export function getPfcWdConfigDetailBySwitch(data) {
    return request({
        url: `${baseURL}/pfc_wd_config/detail_by_switch`,
        method: "POST",
        data
    });
}

export function saveEcnConfig(data) {
    return request({
        url: `${baseURL}/ecn_config/save`,
        method: "POST",
        data
    });
}

export function getEcnConfigList(page, pageSize, filterFields = [], sortFields = [], searchFields = {}) {
    return request({
        url: `${baseURL}/ecn_config/list`,
        method: "POST",
        data: {
            page,
            pageSize,
            filterFields,
            sortFields,
            searchFields
        }
    });
}

export function getEcnPortConfigList(page, pageSize, filterFields = [], sortFields = [], searchFields = {}) {
    return request({
        url: `${baseURL}/ecn_config/port/list`,
        method: "POST",
        data: {
            page,
            pageSize,
            filterFields,
            sortFields,
            searchFields
        }
    });
}

export function deleteEcnConfig(data) {
    return request({
        url: `${baseURL}/ecn_config/delete`,
        method: "POST",
        data
    });
}

export function getEcnConfigDetailBySwitch(data) {
    return request({
        url: `${baseURL}/ecn_config/detail_by_switch`,
        method: "POST",
        data
    });
}

export function updateEcnConfig(data) {
    return request({
        url: `${baseURL}/ecn_config/update`,
        method: "POST",
        data
    });
}

export function saveSchedulingConfig(data) {
    return request({
        url: `${baseURL}/qos_config/save`,
        method: "POST",
        data
    });
}

export function updateSchedulingConfig(data) {
    return request({
        url: `${baseURL}/qos_config/update`,
        method: "POST",
        data
    });
}

export function getSchedulingConfigList(
    page,
    pageSize,
    filterFields = [],
    sortFields = [],
    searchFields = {},
    extraParams = {}
) {
    return request({
        url: `${baseURL}/qos_config/list`,
        method: "POST",
        data: {
            page,
            pageSize,
            filterFields,
            sortFields,
            searchFields,
            ...extraParams
        }
    });
}

export function getSchedulingTrafficConfigList(
    page,
    pageSize,
    filterFields = [],
    sortFields = [],
    searchFields = {},
    extraParams = {}
) {
    return request({
        url: `${baseURL}/qos_port_config/list`,
        method: "POST",
        data: {
            page,
            pageSize,
            filterFields,
            sortFields,
            searchFields,
            ...extraParams
        }
    });
}

export function getSchedulerList(data) {
    return request({
        url: `${baseURL}/qos_config/scheduler_list`,
        method: "POST",
        data
    });
}

export function deleteSchedulingConfig(data) {
    return request({
        url: `${baseURL}/qos_config/delete`,
        method: "POST",
        data
    });
}

export function getQosConfigDetailBySwitch(data) {
    return request({
        url: `${baseURL}/qos_config/detail_by_switch`,
        method: "POST",
        data
    });
}

export function updateQosConfig(data) {
    return request({
        url: `${baseURL}/qos_config/update`,
        method: "POST",
        data
    });
}
