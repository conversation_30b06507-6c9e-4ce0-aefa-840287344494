import {Card, Empty, DatePicker, Select, TreeSelect, message} from "antd";
import {useEffect, useRef, useState, useMemo} from "react";
import {getModulesCount, getModulesHistoryInfo, fetchDeviceDDMInfo} from "@/modules-ampcon/apis/monitor_api";
import {getAllFabricSwitch} from "@/modules-ampcon/apis/lifecycle_api";
import {CustomLineChart, Static2DBarEcharts, StaticBarEcharts} from "@/modules-ampcon/components/echarts_common";
import AnomalyDevicesModal from "@/modules-ampcon/pages/Telemetry/Switch/anomaly_devices_model";
import EmptyPic from "@/assets/images/App/empty.png";
import Icon from "@ant-design/icons/lib/components/Icon";
import styles from "./optical_modules.module.scss";
import filterSvg from "../../Service/Nics/Monitoring/resource/filter.svg?react";
import shrinkSvg from "../../Service/Nics/Monitoring/resource/shrink.svg?react";
import unfoldSvg from "../../Service/Nics/Monitoring/resource/unfold.svg?react";
import shrinkHoverSvg from "../../Service/Nics/Monitoring/resource/shrink_hover.svg?react";
import unfoldHoverSvg from "../../Service/Nics/Monitoring/resource/unfold_hover.svg?react";
import * as echarts from "echarts";

const {RangePicker} = DatePicker;
const {Option} = Select;
const OpticalModules = () => {
    const [timeRange, setTimeRange] = useState(["", ""]);
    const [moduleHistory, setModuleHistory] = useState([]);
    const [treeData, setTreeData] = useState([]);
    const [allData, setAllData] = useState({keys: [], values: []});
    const [abnormalData, setAbormalData] = useState({keys: [], values: []});
    const [hoveredIcons, setHoveredIcons] = useState({});
    const [xAxisInterval, setXAxisInterval] = useState(10);
    const [treeValue, setTreeValue] = useState(undefined);
    const [snList, setSNList] = useState([]);
    const [portSData, setPortsData] = useState([]);
    const AnomalyDevicesModalRef = useRef(null);
    const target = treeValue;

    // const handleSelectChange = value => {
    //     setSelectedNetwork(value);
    //     setTreeValue(undefined);
    // };

    const handleTreeSelectChange = selectedValues => {
        const expandedValues = [];

        const collectAllChildren = node => {
            expandedValues.push(node.value);
            if (node.children) {
                node.children.forEach(child => collectAllChildren(child));
            }
        };

        const findNodeByValue = (value, nodes) => {
            for (const node of nodes) {
                if (node.value === value) {
                    return node;
                }
                if (node.children) {
                    const found = findNodeByValue(value, node.children);
                    if (found) return found;
                }
            }
            return null;
        };

        selectedValues.forEach(val => {
            const node = findNodeByValue(val, treeData);
            if (node) {
                collectAllChildren(node);
            } else {
                expandedValues.push(val); // fallback
            }
        });

        setTreeValue([...new Set(expandedValues)]); // 去重，避免重复值
    };

    const fetchModuleCount = async () => {
        try {
            const res = await getModulesCount(target);
            if (res?.status === 200) {
                const rawSpeedTypes = ["10Gbps", "25Gbps", "40Gbps", "100Gbps", "400Gbps", "800Gbps"];
                const result = {};
                const otherDetailMap = {};

                rawSpeedTypes.forEach(type => {
                    result[type] = 0;
                });
                result.Other = 0;

                Object.entries(res.data || {}).forEach(([type, count]) => {
                    if (rawSpeedTypes.includes(type)) {
                        result[type] += Number(count);
                    } else {
                        result.Other += Number(count);
                        otherDetailMap[type] = Number(count);
                    }
                });

                const formattedData = {
                    keys: [...rawSpeedTypes.map(s => s.replace("Gbps", "G")), "Other"],
                    values: [...rawSpeedTypes.map(type => result[type]), result.Other],
                    otherDetails: otherDetailMap
                };

                setAllData(formattedData);
            } else {
                message.error(res.info);
            }
        } catch (error) {
            console.error(error);
        }
    };

    const fetchModuleHistory = async () => {
        try {
            const res = await getModulesHistoryInfo(target, timeRange[0], timeRange[1]);
            if (res?.status === 200) {
                const rawData = res.data.value;
                const historyData = rawData.map(([time, valueStr, abnormalStr]) => ({
                    time,
                    value: typeof valueStr === "string" ? parseFloat(valueStr.replace("%", "")) : 0,
                    abnormal: parseInt(abnormalStr || "0", 10)
                }));

                const xAxisData = Array.from(new Set(res.data.value.map(item => item[0]))).sort();

                const totalPoints = xAxisData.length;
                if (timeRange[0] && timeRange[1]) {
                    const interval = totalPoints <= 20 ? 0 : Math.ceil(totalPoints / 20);
                    setXAxisInterval(interval);
                } else {
                    setXAxisInterval(0);
                }

                setModuleHistory(historyData);
            }
        } catch (error) {
            console.error(error);
        }
    };

    const fetchAbnormalData = async () => {
        try {
            const response = await fetchDeviceDDMInfo(target, "", "", "", true);
            if (response?.status === 200) {
                const mergedMap = new Map();

                // 先构造一个包含合法 sn||baseIface 的 Set，只处理 ddm_status 中存在的端口
                const validPortSet = new Set();
                Object.entries(response.ddm_status).forEach(([sn, interfaces]) => {
                    interfaces.forEach(iface => {
                        const baseIface = iface.includes(".") ? iface.split(".")[0] : iface;
                        validPortSet.add(`${sn}||${baseIface}`);
                    });
                });

                // 只整合 ddm_status 中的端口
                response.data.forEach(item => {
                    const iface = item.interface;
                    const baseIface = iface.includes(".") ? iface.split(".")[0] : iface;
                    const key = `${item.sn}||${baseIface}`;

                    if (!validPortSet.has(key)) return;

                    if (!mergedMap.has(key)) {
                        mergedMap.set(key, {...item, interface: baseIface});
                    } else {
                        const existing = mergedMap.get(key);
                        const levels = ["info", "warning", "error"];
                        if (levels.indexOf(item.alert_level) > levels.indexOf(existing.alert_level)) {
                            existing.alert_level = item.alert_level;
                        }
                    }
                });

                const rawData = Array.from(mergedMap.values());

                const infoInterface = response.ddm_status;

                const stats = {};
                const snList = {};
                const grouped = {};

                rawData.forEach(item => {
                    const speed = (item.speed || "Unknow").replace("Gbps", "G");

                    const {sn} = item;

                    if (!stats[speed]) {
                        stats[speed] = {type: speed, warning: 0, alarm: 0};
                    }

                    if (item.alert_level === "warning") {
                        stats[speed].warning += 1;
                    } else if (item.alert_level === "error") {
                        stats[speed].alarm += 1;
                    }

                    if (!snList[speed]) {
                        snList[speed] = {alarm: new Set(), warning: new Set()};
                    }

                    if (item.alert_level === "warning") {
                        snList[speed].warning.add(sn);
                    } else if (item.alert_level === "error") {
                        snList[speed].alarm.add(sn);
                    }
                });

                const types = ["10G", "25G", "40G", "100G", "400G", "800G"];
                const formattedData = types.map(t => stats[t] || {type: t, warning: 0, alarm: 0});

                const other = Object.keys(stats)
                    .filter(key => !types.includes(key))
                    .reduce(
                        (acc, key) => {
                            acc.warning += stats[key].warning;
                            acc.alarm += stats[key].alarm;
                            return acc;
                        },
                        {type: "Other", warning: 0, alarm: 0}
                    );

                formattedData.push(other);

                const snToNameMap = {};
                rawData.forEach(item => {
                    snToNameMap[item.sn] = item.host_name || "PICOS";
                });

                const snData = Object.keys(snList).map(speed => ({
                    type: speed,
                    alarm: Array.from(snList[speed].alarm).map(sn => ({
                        sn,
                        name: snToNameMap[sn] || sn
                    })),
                    warning: Array.from(snList[speed].warning).map(sn => ({
                        sn,
                        name: snToNameMap[sn] || sn
                    }))
                }));

                const echartsFormattedData = {
                    keys: formattedData.map(item => item.type.replace("Gbps", "G")),
                    values: formattedData.map(item => [item.warning, item.alarm]),
                    seriesNames: ["Warning", "Alarm"]
                };

                rawData.forEach(item => {
                    const {sn, platform_model, host_name, mgt_ip, interface: iface, alert_level} = item;
                    if (!grouped[sn]) {
                        grouped[sn] = {
                            sn,
                            host_name,
                            mgt_ip,
                            platform_model,
                            ports: []
                        };
                    }

                    if (!grouped[sn].ports.find(p => p.interface === iface)) {
                        grouped[sn].ports.push({interface: iface, alert_level});
                    }
                });

                const resultArray = Object.values(grouped);
                const rawSnSet = new Set(rawData.map(item => item.sn));

                Object.entries(infoInterface).forEach(([sn, interfaces]) => {
                    // 仅处理出现在 rawData 中的 sn
                    if (!rawSnSet.has(sn)) return;

                    const device = resultArray.find(item => item.sn === sn);
                    if (!device) return;

                    interfaces.forEach(iface => {
                        const exists = device.ports.some(p => p.interface === iface);
                        if (!exists) {
                            device.ports.push({
                                interface: iface,
                                alert_level: "info"
                            });
                        }
                    });
                });

                setPortsData(resultArray);
                setAbormalData(echartsFormattedData);
                setSNList(snData);
            } else {
                message.error(response.info);
            }
        } catch (error) {
            console.error(error);
        }
    };

    const fetchTreeData = async () => {
        try {
            const res = await getAllFabricSwitch();
            if (res?.status === 200 && Array.isArray(res.data)) {
                const treeFormatted = res.data.map(fabricItem => ({
                    title: fabricItem.fabric,
                    value: fabricItem.fabric,
                    children: fabricItem.children?.map(child => {
                        const childName = child.title;
                        const childValue = child.value;
                        const childDisplay = childName.length > 3 ? `${childName.slice(0, 3)}...` : childName;
                        const shortValue = childValue.length > 10 ? `${childValue.slice(0, 10)}...` : childValue;
                        return {
                            title: (
                                <span title={`${childName} (${childValue})`}>
                                    {childDisplay} ({shortValue})
                                </span>
                            ),
                            value: childValue
                        };
                    })
                }));

                // 判断是否为空数组，决定是否包一层
                const finalTree =
                    treeFormatted.length > 0
                        ? [
                              {
                                  title: "Fabrics",
                                  value: "fabrics",
                                  children: treeFormatted
                              }
                          ]
                        : [];

                setTreeData(finalTree);
            } else {
                message.error(res.info || "Unexpected response");
            }
        } catch (error) {
            console.error("Failed to load tree data:", error);
            message.error("Failed to fetch tree data");
        }
    };

    const handleMouseEnter = id => {
        setHoveredIcons(prev => ({...prev, [id]: true}));
    };

    const handleMouseLeave = id => {
        setHoveredIcons(prev => ({...prev, [id]: false}));
    };

    const getIconComponent = (expanded, isHovered) => {
        if (expanded) {
            return isHovered ? shrinkHoverSvg : shrinkSvg;
        }
        return isHovered ? unfoldHoverSvg : unfoldSvg;
    };

    const switcherIcon = ({expanded, id}) => {
        const IconComponent = getIconComponent(expanded, hoveredIcons[id]);

        return (
            <IconComponent
                style={{width: "16px", height: "16px", marginTop: "4px", marginRight: "8px"}}
                alt={expanded ? "shrink" : "unfold"}
                onMouseEnter={() => handleMouseEnter(id)}
                onMouseLeave={() => handleMouseLeave(id)}
            />
        );
    };

    const option = useMemo(() => {
        return {
            title: {
                text: "Normal Module Ratio (%)",
                left: "left",
                top: 12,
                textStyle: {
                    fontSize: 12,
                    fontWeight: 400,
                    color: "#212519"
                }
            },
            grid: {
                left: "3.5%",
                right: "3.5%",
                top: "20%",
                bottom: "15%"
            },
            tooltip: {
                trigger: "axis",
                axisPointer: {
                    type: "line",
                    axis: "x",
                    lineStyle: {
                        color: "#aaa",
                        width: 1,
                        type: "dashed"
                    }
                },
                formatter: params => {
                    const sortedParams = params.sort((a, b) => b.value[1] - a.value[1]);
                    let content = `
                        <div style="width: 100%; margin: 0; padding: 0;">
                            <div style="background-color: #F8FAFB; width: calc(100% + 22px); padding: 10px 5px 0px 5px;padding-left:14px; height:40px; margin: -11px -12px 10px -11px;border-bottom: 1px solid #F2F2F2;">
                                <div style="font-size: 16px;front-weight: 600 ; color: #212519FF">${params[0].name}</div>
                            </div>
                        `;
                    sortedParams.forEach(item => {
                        const isValueNaN = isNaN(item.data.value);
                        const normalModuleRatio = isNaN(item.data.value) ? "--" : item.data.value.toFixed(2);
                        const abnormalModuleNumber = item.data.abnormal;
                        content += `
                            <div style="line-height: 2.2; font-size: 14px;">
                                <div style="display: flex; justify-content: flex-start;">
                                    <span style="color: #929A9E; margin-left: 6px;">Normal Module Ratio</span>
                                    <span style="font-size: 14px; font-weight: 400; color:#212519; margin-left: 50px; margin-right: 6px;">${normalModuleRatio}${isValueNaN ? "" : "%"}</span>
                                </div>
                                <div style="display: flex; justify-content: flex-start;">
                                    <span style="color: #929A9E; margin-left: 6px;">Abnormal Module Number</span>
                                    <span style="font-size: 14px; font-weight: 400; color:#212519; margin-left: 14px; margin-right: 6px;">${abnormalModuleNumber}</span>
                                </div>
                            `;
                    });
                    content += `</div>`;

                    return content;
                }
            },
            xAxis: {
                type: "category",
                boundaryGap: false,
                data: moduleHistory.map(item => item.time),
                offset: 10,
                axisLabel: {
                    interval: "auto",
                    formatter(value) {
                        const date = new Date(value);
                        const startDate = new Date(timeRange[0] || Date.now() - 5 * 60 * 1000);
                        const endDate = new Date(timeRange[1] || Date.now());
                        const hour = date.getHours().toString().padStart(2, "0");
                        const minute = date.getMinutes().toString().padStart(2, "0");
                        const second = date.getSeconds().toString().padStart(2, "0");
                        if (startDate.getFullYear() !== endDate.getFullYear()) {
                            return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, "0")}-${date.getDate().toString().padStart(2, "0")} ${hour}:${minute}`;
                        }
                        if (startDate.getMonth() !== endDate.getMonth()) {
                            return `${(date.getMonth() + 1).toString().padStart(2, "0")}-${date.getDate().toString().padStart(2, "0")} ${hour}:${minute}`;
                        }
                        if (startDate.getDate() !== endDate.getDate()) {
                            return `${(date.getMonth() + 1).toString().padStart(2, "0")}-${date.getDate().toString().padStart(2, "0")} ${hour}:${minute}`;
                        }
                        return `${hour}:${minute}:${second}`;
                    }
                }
            },
            yAxis: {
                type: "value",
                min: 0,
                max: 100,
                axisLabel: {
                    formatter: "{value} "
                }
            },
            series: [
                {
                    name: "Normal Module Ratio",
                    type: "line",
                    smooth: true,
                    showSymbol: false,
                    areaStyle: {
                        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                            {
                                offset: 0,
                                color: "rgba(20, 201, 187, 0.2)"
                            },
                            {
                                offset: 1,
                                color: "rgba(20, 201, 187, 0)"
                            }
                        ])
                    },
                    lineStyle: {
                        color: "#14C9BB"
                    },
                    data: moduleHistory.map(item => ({
                        value: item.value,
                        abnormal: item.abnormal
                    }))
                }
            ]
        };
    }, [moduleHistory]);

    const allBarTooltip = {
        trigger: "axis",
        axisPointer: {
            type: "none"
        },
        formatter: params => {
            const {name, value} = params[0];
            let html = `
                <div style="line-height: 2.2; font-size: 14px;">
                    <div style="display: flex; justify-content: flex-start;">
                        <span style="color: #929A9E; margin-left: 6px;">Type</span>
                        <span style="font-size: 14px; font-weight: 400; color:#212519; margin-left: 22px; margin-right: 6px;">${name}</span>
                    </div>
                    <div style="display: flex; justify-content: flex-start;">
                        <span style="color: #929A9E; margin-left: 6px;">Count</span>
                        <span style="font-size: 14px; font-weight: 400; color:#212519; margin-left: 14px; margin-right: 6px;">${value}</span>
                    </div>
            `;

            if (name === "Other" && allData.otherDetails) {
                const details = Object.entries(allData.otherDetails)
                    .sort(([a], [b]) => {
                        const parseValue = str => parseFloat(str.replace(/[^\d.]/g, ""));
                        return parseValue(a) - parseValue(b);
                    })
                    .map(
                        ([subType, subCount]) =>
                            `<tr>
                            <td style="width: 52px;color:#929A9E;margin-left: 6px;">${subType.replace("Gbps", "G")}</td>
                            <td style="color:#212519;">${subCount}</td>
                        </tr>`
                    )
                    .join("");

                html += `
                    <div style="margin-left: 4px;">
                        <table style="font-size: 14px;">${details}</table>
                    </div>
                `;
            }

            html += `</div>`;
            return html;
        }
    };

    const abnormalBarTooltip = {
        trigger: "item",
        borderColor: "transparent",
        backgroundColor: "#FFFFFF",
        enterable: true,
        position(point, params) {
            if (params.seriesIndex === 0 || params.seriesIndex === 1) {
                return [point[0] - 150, point[1] - 120];
            }
            return point;
        },
        formatter(params) {
            const standardTypes = ["10G", "25G", "40G", "100G", "400G", "800G"];
            const {seriesName} = params;
            const level = seriesName.toLowerCase();

            let tagColor = "#FFBB00";
            let tagBgColor = "rgba(255,187,0,0.1)";
            if (level === "alarm") {
                tagColor = "#F53F3F";
                tagBgColor = "rgba(245,63,63,0.1)";
            }

            // 初始化变量
            let content = "";
            if (params.name === "Other") {
                const otherSnList = snList.filter(item => !standardTypes.includes(item.type));
                const grouped = {};

                otherSnList.forEach(item => {
                    const entries = item[level] || [];
                    if (!grouped[item.type]) {
                        grouped[item.type] = [];
                    }
                    grouped[item.type].push(...entries.map(entry => ({...entry})));
                });

                const total = Object.values(grouped).reduce((sum, list) => sum + list.length, 0);

                content += `
                    <div style="
                        margin-top: 8px; 
                        margin-left: 6px; 
                        display: flex; 
                        justify-content: space-between; 
                        width: 140px;
                        color: #929A9E;">
                        <span>Total</span>
                        <span style="color: #212519;">${total}</span>
                    </div>
                    `;

                Object.entries(grouped).forEach(([type, items]) => {
                    content += `
                        <div style="
                            margin-top: 8px; 
                            margin-left: 6px; 
                            display: flex; 
                            justify-content: space-between; 
                            width: 140px; 
                            color: #929A9E;">
                            <span>${type}</span>
                            <span style="color: #212519;">${items.length}</span>
                        </div>
                    `;

                    items.forEach(({sn, name}) => {
                        content += `
                        <div 
                            style="display: flex; align-items: center; margin-top: 4px; cursor: pointer;" 
                            onclick="tooltipItemClicked('${sn}')"
                            onmouseenter="this.querySelector('.sysname-text').style.color='#14C9BB'; 
                            this.querySelector('.sysname-dot').style.backgroundColor='#14C9BB';"
                            onmouseleave="this.querySelector('.sysname-text').style.color='#212519'; 
                            this.querySelector('.sysname-dot').style.backgroundColor='#212519';"
                        >
                            <span class="sysname-dot" style="
                                width: 6px;
                                height: 6px;
                                background-color: #212519;
                                border-radius: 50%;
                                display: inline-block;
                                margin-left: 12px;
                                margin-right: 6px;
                            "></span>
                            <span class="sysname-text" style="margin-left: 6px;font-weight: 400;color: #212519">${name} (${sn})</span>
                        </div>
                    `;
                    });
                });
            } else {
                const list = snList.find(item => item.type === params.name)?.[level] || [];

                content += `
                    <div style="margin-top: 8px; color: #929A9E;margin-left: 6px;">Sysname</div>
                `;
                content += list
                    .map(
                        ({sn, name}) => `
                    <div 
                        style="display: flex; align-items: center; margin-top: 4px; cursor: pointer;" 
                        onclick="tooltipItemClicked('${sn}')"
                        onmouseenter="this.querySelector('.sysname-text').style.color='#14C9BB'; 
                        this.querySelector('.sysname-dot').style.backgroundColor='#14C9BB';"
                        onmouseleave="this.querySelector('.sysname-text').style.color='#212519'; 
                        this.querySelector('.sysname-dot').style.backgroundColor='#212519';"
                    >
                        <span class="sysname-dot" style="
                            width: 6px;
                            height: 6px;
                            background-color: #212519;
                            border-radius: 50%;
                            display: inline-block;
                            margin-left: 6px;
                            margin-right: 6px;
                        "></span>
                        <span class="sysname-text" style="margin-left: 6px;font-weight: 400;color: #212519">${name} (${sn})</span>
                    </div>
                `
                    )
                    .join("");
            }

            return `
            <div style="line-height: 1.8; font-size: 14px;">
                <div style="display: flex; align-items: center;margin-left: 6px;">
                    <span style="color: #929A9E;">Exception Level</span>
                    <span style="
                        display: inline-block;
                        margin-left: 24px;
                        margin-right: 6px;
                        padding: 0px 6px 0px 6px;
                        font-size: 13px;
                        font-weight: 400;
                        height: 24px;
                        color: ${tagColor};
                        background: ${tagBgColor};
                        border: 1px solid ${tagColor};
                        border-radius: 2px;
                    ">${seriesName}</span>
                </div>
                ${content}
            </div>
        `;
        }
    };

    useEffect(() => {
        fetchTreeData();
    }, []);

    useEffect(() => {
        fetchModuleHistory();
    }, [timeRange, target]);

    useEffect(() => {
        fetchModuleCount();
        fetchAbnormalData();
    }, [target]);

    // const warningTotal = abnormalData.values.reduce((sum, [warning]) => sum + warning, 0);
    // const alarmTotal = abnormalData.values.reduce((sum, [, alarm]) => sum + alarm, 0);

    return (
        <div style={{display: "flex", flex: 1}}>
            <div style={{width: "100%", marginBottom: "24px"}}>
                <AnomalyDevicesModal ref={AnomalyDevicesModalRef} />
                <div style={{width: "100%"}}>
                    <div style={{display: "flex", justifyContent: "space-between"}}>
                        <h2 style={{margin: "8px 0 20px"}}>Optical Modules</h2>
                        <div
                            style={{
                                display: "flex",
                                alignItems: "center",
                                marginTop: "8px",
                                marginBottom: "20px"
                            }}
                        >
                            <div style={{fontSize: "16px"}}>Fabrics</div>
                            {/* <Select
                            value={selectedNetwork}
                            onChange={handleSelectChange}
                            style={{marginLeft: 32, width: 120}}
                        >
                            <Option value="All Networks">All Networks</Option>
                            <Option value="Fabric">Fabric</Option>
                        </Select> */}

                            <TreeSelect
                                maxTagCount={1}
                                maxTagTextLength={2}
                                value={treeValue}
                                treeData={treeData}
                                treeCheckable
                                onChange={handleTreeSelectChange}
                                style={{marginLeft: 16, width: 280, marginRight: 60}}
                                switcherIcon={({expanded, value}) => switcherIcon({expanded, id: value})}
                                placeholder="Filter"
                                allowClear
                                suffixIcon={<Icon component={filterSvg} />}
                            />

                            <div style={{fontSize: "16px"}}>Time</div>

                            <RangePicker
                                showTime={{format: "HH:mm"}}
                                format="YYYY-MM-DD HH:mm"
                                style={{height: "32px", marginLeft: "32px"}}
                                onChange={(_, dateString) => {
                                    setTimeRange(dateString);
                                }}
                                disabledDate={current => {
                                    const now = new Date();
                                    now.setHours(23);
                                    now.setMinutes(59);
                                    const oneMonthAgo = new Date();
                                    oneMonthAgo.setMonth(now.getMonth() - 1);
                                    oneMonthAgo.setHours(23);
                                    return current && (current > now || current < oneMonthAgo);
                                }}
                                disabledTime={current => {
                                    const now = new Date();
                                    const currentDay = now.getDate();
                                    const currentHours = now.getHours();
                                    const currentMinutes = now.getMinutes();

                                    const targetDate = new Date(current);
                                    const targetDay = targetDate.getDate();
                                    const targetHours = targetDate.getHours();
                                    const disabledTimeLists = {};
                                    if (!current || targetDay === currentDay) {
                                        disabledTimeLists.disabledHours = () =>
                                            Array.from(
                                                {length: 24 - currentHours + 1},
                                                (_, hour) => hour + currentHours + 1
                                            );
                                        disabledTimeLists.disabledMinutes = () =>
                                            !current || targetHours === currentHours
                                                ? Array.from(
                                                      {length: 60 - currentMinutes + 1},
                                                      (_, minute) => minute + currentMinutes + 1
                                                  )
                                                : [];
                                    }
                                    return disabledTimeLists;
                                }}
                            />
                        </div>
                    </div>

                    <div className={styles.opticalModules}>
                        <Card
                            title={<div className={styles.opticalModules_custom_title}>History Status</div>}
                            // extra={
                            //     <div style={{display: "flex", gap: 32}}>
                            //         <span>Abnormal Module Number: {warningTotal + alarmTotal}</span>
                            //         <span>Warning: {warningTotal}</span>
                            //         <span>Alarm: {alarmTotal}</span>
                            //     </div>
                            // }
                            style={{height: "100%", width: "100%"}}
                            className="optical-modules"
                        >
                            {!moduleHistory || moduleHistory.length === 0 ? (
                                <Empty image={EmptyPic} description="No Data" />
                            ) : (
                                <CustomLineChart chartOption={option} name="History Status" style={{height: "100%"}} />
                            )}
                        </Card>

                        <Card
                            title={
                                <div className={styles.opticalModules_custom_title}>
                                    Transmission Types for All Modules
                                </div>
                            }
                            style={{height: "100%"}}
                            className="barStyle"
                        >
                            {!allData ||
                            !Array.isArray(allData.values) ||
                            (allData.values.every(num => num === 0) &&
                                (!allData.otherDetails || Object.keys(allData.otherDetails).length === 0)) ? (
                                <Empty
                                    image={
                                        <img
                                            src={EmptyPic}
                                            style={{width: 100, height: 100, marginTop: 160}}
                                            alt="No Data"
                                        />
                                    }
                                    description={<div style={{marginBottom: "120px"}}>No Data</div>}
                                />
                            ) : (
                                <StaticBarEcharts
                                    data={allData}
                                    colorList={["#34DCCF"]}
                                    width="15%"
                                    title="Module Count"
                                    tooltipStyle={allBarTooltip}
                                />
                            )}
                        </Card>

                        <Card
                            title={
                                <div className={styles.opticalModules_custom_title}>
                                    Transmission Types for Abnormal Modules
                                </div>
                            }
                            style={{height: "100%"}}
                            className="barStyle"
                        >
                            {!abnormalData ||
                            !Array.isArray(abnormalData.values) ||
                            abnormalData.values.every(arr => Array.isArray(arr) && arr.every(num => num === 0)) ? (
                                <Empty
                                    image={
                                        <img
                                            src={EmptyPic}
                                            style={{width: 100, height: 100, marginTop: 160}}
                                            alt="No Data"
                                        />
                                    }
                                    description={<div style={{marginBottom: "120px"}}>No Data</div>}
                                />
                            ) : (
                                <Static2DBarEcharts
                                    data={abnormalData}
                                    colorList={["#FFBB00", "#F76565"]}
                                    width="15%"
                                    title="Abnormal Module Count"
                                    tooltipStyle={abnormalBarTooltip}
                                    tooltipOnItemClick={name => {
                                        AnomalyDevicesModalRef.current.showAddDeviceModal(name, portSData);
                                    }}
                                />
                            )}
                        </Card>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default OpticalModules;
