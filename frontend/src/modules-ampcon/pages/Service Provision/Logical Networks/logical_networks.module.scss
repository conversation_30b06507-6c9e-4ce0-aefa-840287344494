.leftSider {
    background-color: #FFFFFF;
    width: 140px;
    transition: width 0.3s ease;
    // align-content: center;
    // align-items: center;
    // justify-content: center;
    // justify-items: center;
    margin-left: -24px;
    text-align: center;
    padding-top: 32px;
}

.leftFloatMenu {
    width: 142px;
    position: absolute;
    display: inline-flex;
    // background-color: aqua;
    justify-content: space-around;
    gap: 12px;
}

.baseHover {
    color: #B4BECD;

    &:hover {
        color: #14C9BB;
    }
}

.collapsedLeftSider {
    width: 24px;
    transition: width 0.3s ease;
}

.baseBtn {
    width: 32px;
    height: 32px;
    background: #FFFFFF;

    color: #212519;
    border: 1px solid #E6E8EA;
}

.logicalRouterBtn {
    @extend .baseBtn;
    left: 56px;
    top: 160px;
}

.logicalSwitchBtn {
    @extend .baseBtn;
    left: 56px;
    top: 192px;
}

.virtualNetworkBtn {
    @extend .baseBtn;
    left: 56px;
    top: 224px
}

.reloadBtn {
    @extend .baseBtn;
    // margin-right: 12px;
    box-shadow: 0px 1px 12px 1px #E6E8EA;
    border-radius: 4px;
}


.zoomInBtn {
    @extend .baseBtn;
    border-radius: 4px 0 0 4px;
    // left: 644px;
    // top: 170px;
}

.zoomResetBtn {
    @extend .baseBtn;
    // left: 676px;
    // top: 170px;
}

.zoomOutBtn {
    @extend .baseBtn;
    border-radius: 0 4px 4px 0;
    // left: 708px;
    // top: 170px;
}

.resetBtn {
    @extend .baseBtn;
    left: 56px;
    top: 420px
}