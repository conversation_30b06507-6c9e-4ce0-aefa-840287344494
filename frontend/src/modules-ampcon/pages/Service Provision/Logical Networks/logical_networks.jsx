import React, {useEffect, useRef, useState, forwardRef} from "react";
import {Button, Space, Card, Progress, Table, message, Tag, Switch, Form, Input, Select} from "antd";
import {
    AmpConCustomTable,
    createColumnConfigMultipleParams,
    TableFilterDropdown,
    createColumnConfig,
    TableSelectFilterDropdown,
    AmpConCustomModalForm
} from "@/modules-ampcon/components/custom_table";
import {addSvg} from "@/utils/common/iconSvg";
import Icon from "@ant-design/icons";
import {
    fetchLogicalNetworksTableData,
    saveLogicalNetwork,
    deleteLogicalNetwork
} from "@/modules-ampcon/apis/dc_template_api";
import {confirmModalAction} from "@/modules-ampcon/components/custom_modal";
import {useNavigate} from "react-router-dom";

const LogicalNetworks = () => {
    const navigate = useNavigate();
    const [isModalVisible, setIsModalVisible] = useState(false);
    const [iniValues, setIniValues] = useState({});
    function handleDelete(record) {
        return async () => {
            if (record.vpc_lock) {
                message.error("Cannot delete Logical Network while it is locked");
                return;
            }
            if (record.virtual_network_count + record.logical_device_count > 0) {
                message.error(
                    `Cannot delete Logical Network ${record.name} while it has associated Virtual Networks or Logical Devices`
                );
                return;
            }
            try {
                const response = await deleteLogicalNetwork(record.id);
                if (response.status === 200) {
                    message.success("Logical Network deleted successfully");
                    refreashTable();
                } else {
                    message.error(`Failed to delete Logical Network: ${response.info}`);
                }
            } catch (error) {
                console.error("Error deleting Logical Network:", error);
            }
        };
    }
    const columns = [
        {
            ...createColumnConfig("Name", "name"),
            render: (text, record) => (
                <Space className="actionLink" size="large">
                    <a
                        onClick={() => {
                            console.log(`Clicked on ${text}`);
                            navigate(`/service_provision/logical_networks/${text}`, {
                                state: {name: text, id: record.id}
                            });
                        }}
                    >
                        {text}
                    </a>
                </Space>
            )
        },
        {...createColumnConfig("Description", "description"), sorter: null},
        {...createColumnConfig("User", "user"), sorter: (a, b) => a.user.localeCompare(b.user)},
        createColumnConfig("Virtual Network Count", "virtual_network_count"),
        createColumnConfig("Logical Device Count", "logical_device_count"),
        createColumnConfig("Create Time", "create_time"),
        {
            ...createColumnConfig("Lock/Unlock", "vpc_lock"),
            render: (text, record) => {
                return (
                    <Switch
                        checked={record.vpc_lock}
                        onChange={async checked => {
                            try {
                                const response = await saveLogicalNetwork({
                                    id: record.id,
                                    name: record.name,
                                    description: record.description,
                                    user: record.user,
                                    vpcLock: checked
                                });
                                if (response.status === 200) {
                                    message.success(`Logical Network ${checked ? "locked" : "unlocked"} successfully`);
                                    refreashTable();
                                } else {
                                    message.error(
                                        `Failed to ${checked ? "lock" : "unlock"} Logical Network: ${response.info}`
                                    );
                                }
                            } catch (error) {
                                console.error(`Error ${checked ? "locking" : "unlocking"} Logical Network:`, error);
                            }
                        }}
                    />
                );
            }
        },

        {
            title: "Operation",
            render: (_, record) => {
                return (
                    <div>
                        <Space size="large" className="actionLink">
                            <a
                                onClick={() => {
                                    setIniValues(record);
                                    setIsModalVisible(true);
                                }}
                            >
                                Edit
                            </a>
                            <a
                                onClick={() => {
                                    confirmModalAction(
                                        `Are you sure you want to delete Logical Network ${record.name} ?`,
                                        () => {
                                            handleDelete(record)();
                                        }
                                    );
                                }}
                            >
                                Delete
                            </a>
                        </Space>
                    </div>
                );
            }
        }
    ];

    function refreashTable() {
        if (tableRef.current) {
            tableRef.current.refreshTable();
        }
    }

    const tableRef = useRef(null);
    return (
        <div style={{minHeight: "100%"}}>
            <Card style={{display: "flex", flex: 1, minHeight: "100%"}}>
                <h2 style={{marginTop: "8px", marginBottom: "20px"}}>Logical Networks</h2>
                <LogicalNetworkInfoModal
                    isModalVisible={isModalVisible}
                    setIsModalVisible={setIsModalVisible}
                    iniValues={iniValues}
                    setIniValues={setIniValues}
                    refreashTable={refreashTable}
                />
                <AmpConCustomTable
                    columns={columns}
                    ref={tableRef}
                    searchFieldsList={["name"]}
                    extraButton={
                        <Button
                            type="primary"
                            onClick={() => {
                                setIsModalVisible(true);
                            }}
                            icon={<Icon component={addSvg} />}
                        >
                            Logical Network
                        </Button>
                    }
                    matchFieldsList={[]}
                    fetchAPIInfo={fetchLogicalNetworksTableData}
                />
            </Card>
        </div>
    );
};

const LogicalNetworkInfoModal = ({isModalVisible, setIsModalVisible, iniValues, setIniValues, refreashTable}) => {
    const [formRef] = Form.useForm();
    const [isEditing, setIsEditing] = useState(false);
    useEffect(() => {
        if (isModalVisible && Object.keys(iniValues).length > 0) {
            setIsEditing(true);
            formRef.setFieldsValue(iniValues);
        }
    }, [isModalVisible]);

    const handleCancel = () => {
        setIsModalVisible(false);
        setIniValues({});
        setIsEditing(false);
        formRef.resetFields();
    };
    const handleSubmit = async values => {
        const data = isEditing
            ? {
                  id: iniValues.id,
                  description: values.description,
                  vpcLock: values.vpc_lock,
                  name: values.name
              }
            : {
                  name: values.name,
                  description: values.description,
                  vpcLock: values.vpc_lock
              };
        const response = await saveLogicalNetwork(data);
        if (response.status === 200) {
            message.success(
                isEditing ? "Logical Network updated successfully" : "Logical Network created successfully"
            );
            handleCancel();
            refreashTable();
        } else {
            message.error(
                isEditing
                    ? `Failed to update Logical Network: ${response.info}`
                    : `Failed to create Logical Network: ${response.info}`
            );
        }
    };
    const customFormItems = () => {
        return (
            <div>
                <Form.Item
                    name="name"
                    label="Name"
                    rules={[
                        {required: true, message: "Please input the name!"},
                        {max: 64, message: "Name cannot exceed 64 characters"},
                        {
                            pattern: /^[\w:-]+$/,
                            message: (
                                <span>
                                    Name can only contain letters, numbers, underscores,
                                    <br />
                                    hyphens and colons.
                                </span>
                            )
                        }
                    ]}
                >
                    <Input style={{width: "280px"}} />
                </Form.Item>
                <Form.Item
                    name="description"
                    label="Description"
                    rules={[{max: 256, message: "Description cannot exceed 256 characters"}]}
                >
                    <Input.TextArea style={{width: "280px"}} rows={4} />
                </Form.Item>
                <Form.Item
                    name="user"
                    label="User"
                    rules={[{required: true, message: "Please input the user!"}]}
                    initialValue="admin"
                >
                    <Select
                        placeholder="Select user"
                        style={{width: "280px"}}
                        options={[{label: "admin", value: "admin"}]}
                        value="admin"
                    />
                </Form.Item>
                <Form.Item name="vpc_lock" label="VPC Lock" valuePropName="checked" initialValue={false}>
                    <Switch />
                </Form.Item>
            </div>
        );
    };
    return (
        <AmpConCustomModalForm
            modalClass="ampcon-middle-modal"
            title={isEditing ? "Edit Logical Network" : "Create Logical Network"}
            isModalOpen={isModalVisible}
            formInstance={formRef}
            layoutProps={{
                labelCol: {
                    span: 5
                }
            }}
            CustomFormItems={customFormItems}
            onCancel={handleCancel}
            onSubmit={handleSubmit}
        />
    );
};

export default LogicalNetworks;
