import React, {useRef, useState, useEffect} from "react";
import {useNavigate} from "react-router-dom";
import Icon from "@ant-design/icons";
import {useForm} from "antd/es/form/Form";
import {Space, Button, Input, Form, message, Divider, Tooltip, Tag, Card, Select} from "antd";

import {
    fetchLogicalNewtworkList,
    fetchLogicalSwitchList,
    createLogicalSwitch
} from "@/modules-ampcon/apis/dc_template_api";
import {createColumnConfig, AmpConCustomModalForm, AmpConCustomTable} from "@/modules-ampcon/components/custom_table";
import {refreshSvg} from "@/utils/common/iconSvg";

import styles from "@/modules-ampcon/pages/Service Provision/Logical Switches/logical_switch.module.scss";

const {Option} = Select;

const LogicalSwitches = () => {
    const tableRef = useRef(null);
    const navigate = useNavigate();
    const [form] = useForm();

    const [networkData, setNetworkData] = useState([]);
    const [networkList, setNetworkList] = useState([]);
    const [networkId, setNetworkId] = useState();
    const [editLogicalModalOpen, setEditLogicalModalOpen] = useState(false);
    const [lsID, setLsID] = useState(null);

    const nameClick = record => {
        navigate(`/service_provision/logical_switches/${record.name}`, {state: {actionType: "Edit", data: record}});
    };

    const editSwitch = record => {
        form.setFieldsValue({
            name: record.name,
            description: record.description
        });
        setLsID(record.id);
        setEditLogicalModalOpen(true);
    };

    const columns = [
        createColumnConfig("ID", "id"),
        {
            ...createColumnConfig("Name", "name"),
            render: (_, record) => {
                return (
                    <Space size="large" className="actionLink">
                        <a onClick={() => nameClick(record)}>{record.name}</a>
                    </Space>
                );
            }
        },
        {
            title: "Description",
            dataIndex: "description",
            width: 200,
            render: (_, record) =>
                record?.description?.length > 50 ? (
                    <Tooltip placement="bottom" title={record.description}>
                        <p style={{maxWidth: 200, overflow: "hidden", textOverflow: "ellipsis"}}>
                            {record.description}
                        </p>
                    </Tooltip>
                ) : (
                    <span>{record.description}</span>
                )
        },
        createColumnConfig("Type", "type"),
        {
            title: "Status",
            dataIndex: "status",
            sorter: false,
            render: (_, record) => {
                let tagClass;
                let status;
                switch (record.status) {
                    case 0:
                        tagClass = styles.successTag;
                        status = ["Create", "Normal"];
                        break;
                    case 1:
                        tagClass = styles.failedTag;
                        status = ["Create", "Error"];
                        break;
                    case 2:
                        tagClass = styles.successTag;
                        status = ["Delete", "Normal"];
                        break;
                    case 3:
                        tagClass = styles.failedTag;
                        status = ["Delete", "Error"];
                        break;
                    default:
                        tagClass = styles.pendingTag;
                        status = ["Other"];
                        break;
                }
                return status.map(item => <Tag className={tagClass}>{item}</Tag>);
            }
        },
        {
            title: "User",
            dataIndex: "user",
            sorter: false,
            render: (_, record) => {
                return record.user ? record.user : "admin";
            }
        },
        {title: "Logical Network", dataIndex: "logical_network_name", sorter: false},
        {title: "Fabric", dataIndex: "fabric_name", sorter: false},
        {title: "Port Count", dataIndex: "ports", sorter: false},
        createColumnConfig("Create Time", "create_time"),
        {
            title: "Operation",
            render: (_, record) => {
                return (
                    <div>
                        <Space size="large" className="actionLink">
                            <a onClick={() => editSwitch(record)}>Edit</a>
                        </Space>
                    </div>
                );
            }
        }
    ];

    const searchFieldsList = ["name"];
    const matchFieldsList = [{name: "name", matchMode: "fuzzy"}];

    const formItems = () => {
        return (
            <div>
                <Form.Item
                    name="name"
                    label="Name"
                    rules={[
                        {required: true, message: "Please input the name!"},
                        {
                            max: 64,
                            message: "Name cannot exceed 64 characters"
                        },
                        {
                            pattern: /^[\w:-]+$/,
                            message: (
                                <span>
                                    Name can only contain letters, numbers, underscores,
                                    <br />
                                    hyphens and colons.
                                </span>
                            )
                        }
                    ]}
                >
                    <Input placeholder="Enter logical switch name" style={{width: "280px"}} />
                </Form.Item>
                <Form.Item
                    name="description"
                    label="Description"
                    rules={[{max: 256, message: "Description cannot exceed 256 characters"}]}
                >
                    <Input.TextArea placeholder="Enter description" style={{width: "280px"}} rows={4} />
                </Form.Item>
            </div>
        );
    };

    const fetchNetwork = async () => {
        let response = await fetchLogicalNewtworkList(1, 10, [], [], {});
        if (response.status === 200) {
            let allData = response.data;
            if (response.total > 10) {
                response = await fetchLogicalNewtworkList(1, response.total, [], [], {});
                if (response.status === 200) {
                    allData = response.data;
                }
            }
            const networkList = [...new Set(allData.map(item => item.name))];
            setNetworkData(allData);
            setNetworkList(networkList);
        } else {
            message.error(response.info);
        }
    };

    const onSubmit = async values => {
        const data = {
            ...values,
            id: lsID
        };
        const res = await createLogicalSwitch(data);
        if (res.status === 200) {
            message.success("Logical switch updated successfully");
            setEditLogicalModalOpen(false);
            tableRef.current.refreshTable();
        } else {
            message.error(res.info || "Failed to update logical switch");
        }
    };

    useEffect(() => {
        fetchNetwork();
    }, []);

    return (
        <Card style={{display: "flex", flex: 1}}>
            <h2 style={{margin: "8px 0 20px"}}>Logical Switches</h2>
            <Form layout="inline" style={{flexWrap: "nowrap", marginBottom: "30px"}}>
                <Form.Item name="network" label="Logical Network" wrapperCol={{style: {marginLeft: 20, width: 280}}}>
                    <Select
                        onChange={value => {
                            const checkedNetwork = networkData.find(item => item.name === value);
                            setNetworkId(checkedNetwork ? checkedNetwork.id : null);
                        }}
                        placeholder="Logical Network"
                        allowClear
                    >
                        {networkList.map(option => (
                            <Option key={option} value={option}>
                                {option}
                            </Option>
                        ))}
                    </Select>
                </Form.Item>
            </Form>
            <AmpConCustomModalForm
                title="Edit Logical Switch"
                isModalOpen={editLogicalModalOpen}
                formInstance={form}
                layoutProps={{
                    labelCol: {
                        span: 5
                    }
                }}
                CustomFormItems={formItems}
                onCancel={() => setEditLogicalModalOpen(false)}
                onSubmit={onSubmit}
                modalClass="ampcon-middle-modal"
                footer={[
                    <Divider style={{marginTop: 0, marginBottom: 20}} />,
                    <Button key="cancel" onClick={() => setEditLogicalModalOpen(false)}>
                        Cancel
                    </Button>,
                    <Button key="ok" type="primary" onClick={form.submit}>
                        Apply
                    </Button>
                ]}
            />
            <AmpConCustomTable
                columns={columns}
                fetchAPIInfo={fetchLogicalSwitchList}
                fetchAPIParams={[networkId]}
                searchFieldsList={searchFieldsList}
                matchFieldsList={matchFieldsList}
                extraButton={
                    <Button
                        style={{width: 104, height: 32}}
                        onClick={() => {
                            tableRef.current.refreshTable();
                        }}
                    >
                        <Icon component={refreshSvg} />
                        Refresh
                    </Button>
                }
                ref={tableRef}
            />
        </Card>
    );
};

export default LogicalSwitches;
