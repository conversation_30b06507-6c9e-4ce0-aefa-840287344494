import React, {useState, useEffect} from "react";
import {useNavigate, useLocation} from "react-router-dom";
import {ArrowLeftOutlined} from "@ant-design/icons";
import {Tooltip, Tag, Card, Tabs} from "antd";

import {
    fetchLogicalRouterDetail,
    fetchLogicalRouterPortDetail,
    fetchLogicalRouterStatusMonitor
} from "@/modules-ampcon/apis/dc_template_api";
import PrevFormTable from "@/modules-ampcon/components/prevform_table";

import styles from "@/modules-ampcon/pages/Service Provision/Logical Routers/logical_router.module.scss";

const LogicalRouterDetail = ({showModal = false, lr_id = null, lr_info = null}) => {
    const navigate = useNavigate();
    const location = useLocation();
    const [id, setId] = useState(location?.state?.data?.id || lr_id || "");

    const [activeKey, setActiveKey] = useState("Device Detail");

    const deviceColumns = [
        {title: "ID", dataIndex: "id", sorter: (a, b) => a.id.localeCompare(b.id)},
        {title: "Port", dataIndex: "port", sorter: (a, b) => a.port.localeCompare(b.port)},
        {title: "IPv4", dataIndex: "anycast_ipv4", sorter: (a, b) => a.anycast_ipv4.localeCompare(b.anycast_ipv4)},
        {
            title: "Port Status",
            dataIndex: "status",
            sorter: false,
            render: (text, record) => {
                let tagClass;
                let status;
                switch (record.status) {
                    case 0:
                        tagClass = styles.successTag;
                        status = ["Connect", "Successful"];
                        break;
                    case 1:
                        tagClass = styles.failedTag;
                        status = ["Connect", "Failed"];
                        break;
                    case 2:
                        tagClass = styles.runningTag;
                        status = ["Disconnecting"];
                        break;
                    case 3:
                        tagClass = styles.failedTag;
                        status = ["Disconnect", "Failed"];
                        break;
                    default:
                        tagClass = styles.pendingTag;
                        status = ["Other"];
                        break;
                }
                return status.map(item => <Tag className={tagClass}>{item}</Tag>);
            }
        }
    ];

    const [prevDeviceForm, setPrevDeviceForm] = useState({
        name: "",
        status: "",
        l3_VNI: ""
    });

    const searchDeviceList = ["port", "anycast_ipv4"];
    const matchDeviceList = [
        {name: "port", matchMode: "fuzzy"},
        {name: "anycast_ipv4", matchMode: "fuzzy"}
    ];

    const monitorColumns = [
        {
            title: "ID",
            render: (_, record, index) => {
                return index + 1;
            }
        },
        {title: "PoD Resource", dataIndex: "az", sorter: (a, b) => a.az.localeCompare(b.az)},
        {title: "VLAN Domain", dataIndex: "vlan_domain", sorter: (a, b) => a.vlan_domain.localeCompare(b.vlan_domain)},
        {title: "Leaf Switch", dataIndex: "switch_sn", sorter: (a, b) => a.switch_sn.localeCompare(b.switch_sn)},
        {title: "RD", dataIndex: "rd", sorter: (a, b) => a.rd.localeCompare(b.rd)},
        {title: "RT", dataIndex: "rt", sorter: (a, b) => a.rt.localeCompare(b.rt)},
        {title: "VRF VLAN", dataIndex: "vrf_vlan", sorter: (a, b) => a.vrf_vlan.localeCompare(b.vrf_vlan)},
        {
            title: "GW Interface (IP/VLAN)",
            dataIndex: "gw_interface",
            sorter: (a, b) => a.gw_interface.localeCompare(b.gw_interface),
            render: text => <span style={{whiteSpace: "pre-line"}}>{text.join("\n")}</span>
        },
        {
            title: "Virtual IP",
            dataIndex: "virtual_ip",
            sorter: (a, b) => a.virtual_ip.localeCompare(b.virtual_ip),
            render: text => <span style={{whiteSpace: "pre-line"}}>{text.join("\n")}</span>
        }
    ];

    const [prevMonitorForm, setPrevMonitorForm] = useState({
        name: "",
        l3_VNI: "",
        VRF_name: "",
        intra_interface_nums: ""
    });

    const searchMonitorList = ["az", "vlan_domain", "switch_sn", "gw_interface", "virtual_ip"];
    const matchMonitorList = [
        {name: "az", matchMode: "fuzzy"},
        {name: "vlan_domain", matchMode: "fuzzy"},
        {name: "switch_sn", matchMode: "fuzzy"},
        {name: "gw_interface", matchMode: "fuzzy"},
        {name: "virtual_ip", matchMode: "fuzzy"}
    ];

    const items = [
        {
            key: "Device Detail",
            label: "Device Detail",
            children: (
                <PrevFormTable
                    title="Logical Port Detail"
                    prevForm={prevDeviceForm}
                    columns={deviceColumns}
                    fetchApi={fetchLogicalRouterPortDetail}
                    fetchAPIParams={[id]}
                    searchFieldsList={searchDeviceList}
                    matchFieldsList={matchDeviceList}
                />
            )
        },
        {
            key: "Status Monitor",
            label: "Status Monitor",
            children: (
                <PrevFormTable
                    title="Logical Port Detail"
                    prevForm={prevMonitorForm}
                    columns={monitorColumns}
                    fetchApi={fetchLogicalRouterStatusMonitor}
                    fetchAPIParams={[id]}
                    searchFieldsList={searchMonitorList}
                    matchFieldsList={matchMonitorList}
                />
            )
        }
    ];

    const fetchPrevDeviceForm = async () => {
        const prevData = showModal ? lr_info : location?.state?.data;
        if (prevData) {
            let status;
            if (prevData.status === 0) status = ["Create", "Normal"];
            if (prevData.status === 1) status = ["Create", "Error"];
            if (prevData.status === 2) status = ["Delete", "Normal"];
            if (prevData.status === 3) status = ["Delete", "Error"];
            setPrevDeviceForm({
                name: prevData.name,
                status,
                l3_VNI: prevData.l3vni
            });

            setPrevMonitorForm({
                name: prevData.name,
                l3_VNI: prevData.l3vni,
                VRF_name: prevData.vrf_name,
                intra_interface_nums: prevData.ports
            });
        }
    };

    useEffect(() => {
        fetchPrevDeviceForm();
    }, []);

    return !showModal ? (
        <Card style={{display: "flex", flex: 1, paddingTop: "12px"}}>
            {/* <p
                className={styles.goBack}
                onClick={() => navigate("/service_provision/logical_routers")}
                style={{marginBottom: "23px"}}
            >
                <ArrowLeftOutlined style={{marginRight: "8px"}} />
                <span>Back</span>
            </p> */}
            <div className={styles.logicalTab}>
                <Tabs onChange={setActiveKey} activeKey={activeKey} items={items} />
            </div>
        </Card>
    ) : (
        <div className={styles.inModalLogicalTab}>
            <Tabs onChange={setActiveKey} activeKey={activeKey} items={items} />
        </div>
    );
};

export default LogicalRouterDetail;
