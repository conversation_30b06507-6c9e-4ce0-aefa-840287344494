import {forwardRef, useImperativeHandle, useState, useEffect, useRef, useMemo} from "react";
import {Button, Divider, Flex, Table, Modal, message, Input} from "antd";
import {fetchNetworkIAccessAvailableDeviceDetailInfo} from "@/modules-ampcon/apis/dc_virtual_resource_api";
import {createColumnConfigMultipleParams, TableFilterDropdown} from "@/modules-ampcon/components/custom_table";
import {searchSvg} from "@/utils/common/iconSvg";
import Icon from "@ant-design/icons/lib/components/Icon";
import ExpandIcon from "@/modules-ampcon/components/expand_icon";

const SelectDeviceModal = forwardRef((props, ref) => {
    useImperativeHandle(ref, () => ({
        showSelectDeviceModal: (nodeGroupId, pgIds, disabledPgIds = []) => {
            setIsShowModal(true);
            setnodeGroupId(nodeGroupId);
            setPgIds(pgIds);
            setDisabledPgIds(disabledPgIds);
            setSelectedRowKeys([]);
            setSelectedRows([]);
            setSelectedRowsState([]);
            setExpandedRowKeys([]);
            fetchData(nodeGroupId);
        },
        hideSelectDeviceModal: () => {
            setIsShowModal(false);
        },
        refreshDeviceList: () => {
            if (nodeGroupId) {
                fetchData();
            }
        }
    }));
    const [isShowModal, setIsShowModal] = useState(false);
    const [nodeGroupId, setnodeGroupId] = useState(null);
    const [data, setData] = useState([]);
    const [pgIds, setPgIds] = useState([]);
    const [loading, setLoading] = useState(false);
    const [expandedRowKeys, setExpandedRowKeys] = useState([]);
    const tableModalRef = useRef(null);
    const {saveCallback} = props;

    const [selectedRowKeys, setSelectedRowKeys] = useState([]);
    const [selectedRows, setSelectedRows] = useState([]);
    const [selectedRowsState, setSelectedRowsState] = useState([]);
    const [disabledPgIds, setDisabledPgIds] = useState([]);

    const [searchKeyword, setSearchKeyword] = useState("");
    const [currentPage, setCurrentPage] = useState(1);
    const [pageSize, setPageSize] = useState(10);

    const filteredData = useMemo(() => {
        if (!searchKeyword) return data;
        const keyword = searchKeyword.toLowerCase();
        return data
            .map(parent => {
                const matchedChildren = (parent.children || []).filter(child =>
                    [child.host_name, child.pg_name, child.switch_pg_name].some(field =>
                        field?.toLowerCase().includes(keyword)
                    )
                );
                const isParentMatch = [parent.host_name].some(field => field?.toLowerCase().includes(keyword));
                if (isParentMatch || matchedChildren.length > 0) {
                    return {
                        ...parent,
                        children: matchedChildren.length > 0 ? matchedChildren : parent.children
                    };
                }
                return null;
            })
            .filter(item => item !== null);
    }, [searchKeyword, data]);

    useEffect(() => {
        setCurrentPage(1);
    }, [searchKeyword]);

    const onSelect = (record, selected, selectedRows) => {
        let newSelectedRowKeys = [...selectedRowKeys];
        let newSelectedRows = [...selectedRowsState];

        if (record.children && record.children.length > 0) {
            const childKeys = record.children.map(child => child.key);
            const pgNames = record.children.map(c => c.pg_name);
            const allSameGroup = pgNames.every(pg => pg === pgNames[0]);

            if (selected) {
                if (!allSameGroup) {
                    message.warning("For each node device, you can select only one NIC port group.");
                    return;
                }

                const keysToAdd = [record.key, ...childKeys];
                const rowsToAdd = [record, ...record.children];

                newSelectedRowKeys = Array.from(new Set([...newSelectedRowKeys, ...keysToAdd]));
                newSelectedRows = Array.from(new Set([...newSelectedRows, ...rowsToAdd]));
            } else {
                const keysToRemove = [record.key, ...childKeys];
                newSelectedRowKeys = newSelectedRowKeys.filter(key => !keysToRemove.includes(key));
                newSelectedRows = newSelectedRows.filter(row => !keysToRemove.includes(row.key));
            }

            setSelectedRowKeys(newSelectedRowKeys);
            setSelectedRowsState(newSelectedRows);
            return;
        }

        if (selected) {
            const parent = data.find(p => p.children?.some(c => c.key === record.key));
            if (parent) {
                const siblings = parent.children;
                const siblingPgNames = siblings.map(s => s.pg_name);
                const allSameGroup = siblingPgNames.every(pg => pg === siblingPgNames[0]);

                const selectedSibling = newSelectedRows.find(r => siblings.some(s => s.key === r.key));
                if (selectedSibling && !allSameGroup) {
                    message.warning("For each node device, you can select only one NIC port group.");
                    return;
                }
            }

            newSelectedRowKeys = [...newSelectedRowKeys, record.key];
            newSelectedRows = [...newSelectedRows, record];
        } else {
            newSelectedRowKeys = newSelectedRowKeys.filter(key => key !== record.key);
            newSelectedRows = newSelectedRows.filter(row => row.key !== record.key);
        }

        setSelectedRowKeys(newSelectedRowKeys);
        setSelectedRowsState(newSelectedRows);
        setSelectedRows(newSelectedRows);
    };

    const onSelectAll = (selected, selectedRowsCurrent) => {
        if (selected) {
            // 获取当前可见数据中所有的“有效子节点”
            const allChildRows = [];

            selectedRowsCurrent.forEach(row => {
                if (row.children && row.children.length > 0) {
                    allChildRows.push(...row.children);
                } else {
                    allChildRows.push(row);
                }
            });

            if (allChildRows.length === 0) {
                setSelectedRowKeys([]);
                setSelectedRows([]);
                setSelectedRowsState([]);
                return;
            }

            const groupName = allChildRows[0].pg_name;
            const sameGroupRows = allChildRows.filter(row => row.pg_name === groupName);

            if (sameGroupRows.length !== allChildRows.length) {
                message.warning(
                    "Some node devices contain more than 1 NIC group, and thus you can't select all node devices."
                );
                return;
            }

            const keys = sameGroupRows.map(row => row.key);
            setSelectedRowKeys(keys);
            setSelectedRows(sameGroupRows);
            setSelectedRowsState(sameGroupRows);
        } else {
            setSelectedRowKeys([]);
            setSelectedRows([]);
            setSelectedRowsState([]);
        }
    };

    const handleSave = async () => {
        try {
            const rowsToSave = selectedRowsState;

            if (!rowsToSave) {
                message.warning("Please select at least one device.");
                return;
            }

            const pg_ids = rowsToSave.map(row => row.pg_id).filter(id => id !== undefined && id !== null);
            const hostMap = new Map();

            rowsToSave.forEach(row => {
                const parent = data.find(p => p.children?.some(c => c.key === row.key));
                const hostName = parent?.host_name;
                if (hostName) {
                    if (!hostMap.has(hostName)) {
                        hostMap.set(hostName, []);
                    }
                    hostMap.get(hostName).push(row.pg_name);
                }
            });

            // 构造 host_name:pg_name1,pg_name2 格式
            const pg_names = data
                .map(parent => {
                    const matchedChildren = (parent.children || []).filter(child =>
                        selectedRowKeys.includes(child.key)
                    );
                    if (matchedChildren.length > 0) {
                        const pgNameList = Array.from(new Set(matchedChildren.map(c => c.pg_name))).join(", ");
                        return `${parent.host_name}:${pgNameList}`;
                    }
                    return null;
                })
                .filter(Boolean); // 过滤掉没有匹配项的父节点

            if (saveCallback && typeof saveCallback === "function") {
                await saveCallback({pg_ids, pg_names});
            }

            setIsShowModal(false);
        } catch (error) {
            console.error("Save failed:", error);
            message.error("Failed to save device selection.");
        }
    };

    useEffect(() => {
        if (!data.length || !pgIds.length) return;

        const matchedKeys = [];
        const matchedRows = [];

        data.forEach(parent => {
            if (parent.children && parent.children.length > 0) {
                parent.children.forEach(child => {
                    if (pgIds.includes(child.pg_id)) {
                        matchedKeys.push(child.key);
                        matchedRows.push(child);
                    }
                });
            }
        });

        if (matchedKeys.length) {
            setSelectedRowKeys(matchedKeys);
            setSelectedRows(matchedRows);
            setSelectedRowsState(matchedRows);
        }
    }, [data, pgIds]);

    const fetchData = async nodeGroupId => {
        setLoading(true);
        try {
            const res = await fetchNetworkIAccessAvailableDeviceDetailInfo(nodeGroupId);
            if (Array.isArray(res?.data)) {
                const transformedData = res.data.map((item, index) => {
                    const children = Array.isArray(item.pg_info)
                        ? item.pg_info.map((pg, i) => ({
                              key: `${index}-${i}`,
                              ...pg
                          }))
                        : [];
                    return {
                        key: index,
                        host_name: item.host_name,
                        children
                    };
                });
                setData(transformedData);
            } else {
                console.warn("Invalid data format: res.data is not an array", res?.data);
                setData([]);
            }
        } catch (error) {
            console.error("Failed to fetch network access device info:", error);
            setData([]);
        } finally {
            setLoading(false);
        }
    };

    const switchColumns = [
        {
            ...createColumnConfigMultipleParams({
                title: " ",
                dataIndex: " ",
                filterDropdownComponent: TableFilterDropdown,
                enableFilter: false,
                enableSorter: false
            }),
            width: "1%"
        },
        {
            ...createColumnConfigMultipleParams({
                title: "ID",
                dataIndex: "pg_id",
                filterDropdownComponent: TableFilterDropdown,
                enableFilter: false
            }),
            width: "25%",
            sorter: (a, b) => String(a.pg_id).localeCompare(String(b.pg_id)),
            render: (_, record) => (record.children ? null : record.pg_id)
        },
        {
            ...createColumnConfigMultipleParams({
                title: "Sysname",
                dataIndex: "host_name",
                filterDropdownComponent: TableFilterDropdown,
                enableFilter: false
            }),
            width: "25%",
            sorter: (a, b) => a.host_name?.localeCompare(b.host_name),
            render: (_, record) => (record.children ? record.host_name : null)
        },
        {
            ...createColumnConfigMultipleParams({
                title: "NIC Port Group",
                dataIndex: "pg_name",
                filterDropdownComponent: TableFilterDropdown,
                enableFilter: false
            }),
            width: "25%",
            sorter: (a, b) => a.pg_name?.localeCompare(b.pg_name),
            render: (_, record) => (record.children ? null : record.pg_name)
        },
        {
            ...createColumnConfigMultipleParams({
                title: "Switch Port Group",
                dataIndex: "switch_pg_name",
                filterDropdownComponent: TableFilterDropdown,
                enableFilter: false
            }),
            width: "25%",
            sorter: (a, b) => a.switch_pg_name?.localeCompare(b.switch_pg_name),
            render: (_, record) => (record.children ? null : record.switch_pg_name)
        }
    ];

    return (
        <div>
            <Modal
                className="ampcon-max-modal"
                title={
                    <div>
                        Select Device
                        <Divider style={{margin: "16px -24px 16px -24px"}} />
                    </div>
                }
                open={isShowModal}
                onCancel={() => {
                    setIsShowModal(false);
                    setSelectedRowKeys([]);
                    setSelectedRows([]);
                    setSelectedRowsState([]);
                }}
                footer={
                    <Flex vertical>
                        <Divider style={{margin: "16px -24px 16px -24px"}} />
                        <Flex justify="flex-end">
                            <Button
                                onClick={() => {
                                    setIsShowModal(false);
                                    setSelectedRowKeys([]);
                                    setSelectedRows([]);
                                    setSelectedRowsState([]);
                                }}
                            >
                                Cancel
                            </Button>
                            <Button type="primary" onClick={handleSave}>
                                Apply
                            </Button>
                        </Flex>
                    </Flex>
                }
            >
                <Flex justify="flex-end" style={{marginBottom: 20, marginTop: -12}}>
                    <Input
                        placeholder="Search Sysname / NIC Port Group / Switch Port Group"
                        allowClear
                        onSearch={value => setSearchKeyword(value)}
                        onChange={e => setSearchKeyword(e.target.value)}
                        style={{width: 280}}
                        prefix={<Icon component={searchSvg} />}
                    />
                </Flex>
                <Table
                    ref={tableModalRef}
                    rowSelection={{
                        selectedRowKeys,
                        onSelect,
                        onSelectAll,
                        checkStrictly: false,
                        getCheckboxProps: record => {
                            if (!record.children) {
                                return {
                                    disabled: disabledPgIds.includes(record.pg_id)
                                };
                            }

                            const allChildrenDisabled = (record.children || []).every(child =>
                                disabledPgIds.includes(child.pg_id)
                            );
                            return {
                                disabled: allChildrenDisabled
                            };
                        }
                    }}
                    columns={switchColumns}
                    bordered
                    expandable={{
                        expandIcon: props => <ExpandIcon {...props} isTable={false} />,
                        rowExpandable: record => !!record.children,
                        expandedRowKeys,
                        onExpand: (expanded, record) => {
                            if (expanded) {
                                setExpandedRowKeys(prev => [...prev, record.key]);
                            } else {
                                setExpandedRowKeys(prev => prev.filter(k => k !== record.key));
                            }
                        }
                    }}
                    dataSource={filteredData}
                    pagination={{
                        current: currentPage,
                        pageSize,
                        showSizeChanger: true,
                        pageSizeOptions: ["10", "20", "50", "100"],
                        showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`,
                        total: filteredData.length,
                        onChange: (page, size) => {
                            setCurrentPage(page);
                            setPageSize(size);
                        }
                    }}
                />
            </Modal>
        </div>
    );
});

export default SelectDeviceModal;
