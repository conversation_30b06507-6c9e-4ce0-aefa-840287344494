import {forwardRef, useImperativeHandle, useState} from "react";
import {Modal, Form, Input, Select, Button, Divider, Flex, message} from "antd";
import {addNetworkIAccess} from "@/modules-ampcon/apis/dc_virtual_resource_api";

const EditVL2Modal = forwardRef((props, ref) => {
    useImperativeHandle(ref, () => ({
        showEditVL2Modal: record => {
            setIsShowModal(true);
            setExtraId(record.id);
            form.setFieldsValue({
                name: record.network_name,
                fabric: record.fabric_id,
                fabricName: record.fabric_name,
                pod: record.az_id,
                podName: record.az_name,
                vlan: record.vlan_id
            });
        },
        hideEditVL2Modal: () => {
            setIsShowModal(false);
            form.resetFields();
        }
    }));
    const [isShowModal, setIsShowModal] = useState(false);
    const [extraId, setExtraId] = useState(null);

    const [form] = Form.useForm();
    const {saveCallback} = props;
    const VLANTooltip = "If VLAN is not filled in, it will be assigned automatically by AmpCon-DC.";

    const handleSave = async () => {
        try {
            const values = await form.validateFields();

            const response = await addNetworkIAccess(
                extraId, // id
                values.name, // vl2name
                values.pod, // azId
                values.fabric, // fabricId
                values.vlan // vlanId
            );

            if (response.status === 200) {
                setIsShowModal(false);
                form.resetFields();
                message.success(`${response?.info}`);
            } else {
                message.error(`${response?.info}`);
            }
            if (saveCallback) {
                saveCallback();
            }
        } catch (error) {
            console.error("Validation failed:", error);
        }
    };

    return (
        <Modal
            className="ampcon-middle-modal"
            title={
                <div>
                    Edit VL2
                    <Divider style={{marginTop: 8, marginBottom: 0}} />
                </div>
            }
            open={isShowModal}
            onCancel={() => {
                setIsShowModal(false);
                form.resetFields();
            }}
            footer={
                <Flex vertical>
                    <Divider style={{marginTop: 0, marginBottom: 20}} />
                    <Flex justify="flex-end">
                        <Button
                            onClick={() => {
                                setIsShowModal(false);
                                form.resetFields();
                            }}
                        >
                            Cancel
                        </Button>
                        <Button type="primary" onClick={handleSave}>
                            Apply
                        </Button>
                    </Flex>
                </Flex>
            }
        >
            <Form
                form={form}
                layout="horizontal"
                labelAlign="left"
                labelCol={{span: 6}}
                wrapperCol={{span: 18}}
                style={{
                    width: "384px",
                    minHeight: "267.23px"
                }}
            >
                <Form.Item
                    label="Name"
                    name="name"
                    rules={[
                        {required: true, message: "VL2 name cannot be empty."},
                        {
                            max: 64,
                            message: "VL2 name cannot exceed 64 characters."
                        },
                        {
                            validator: (_, value) => {
                                if (!value) {
                                    return Promise.resolve();
                                }

                                const pattern = /^[\w-]+$/;

                                if (!pattern.test(value)) {
                                    return Promise.reject(
                                        new Error("Only letters, numbers, underscores and hyphens are allowed!")
                                    );
                                }

                                return Promise.resolve();
                            }
                        }
                    ]}
                >
                    <Input style={{width: "280px"}} />
                </Form.Item>

                <Form.Item label="Fabric" name="fabric" rules={[{required: true, message: "Please select Fabric"}]}>
                    <Select style={{width: 280}} disabled value={form.getFieldValue("fabric")}>
                        <Select.Option value={form.getFieldValue("fabric")}>
                            {form.getFieldValue("fabricName")}
                        </Select.Option>
                    </Select>
                </Form.Item>

                <Form.Item label="PoD" name="pod" rules={[{required: true, message: "Please select PoD"}]}>
                    <Select style={{width: 280}} disabled value={form.getFieldValue("pod")}>
                        <Select.Option value={form.getFieldValue("pod")}>{form.getFieldValue("podName")}</Select.Option>
                    </Select>
                </Form.Item>

                <Form.Item label="VLAN" name="vlan" tooltip={VLANTooltip}>
                    <Input style={{width: "280px"}} disabled />
                </Form.Item>
            </Form>
        </Modal>
    );
});

export default EditVL2Modal;
