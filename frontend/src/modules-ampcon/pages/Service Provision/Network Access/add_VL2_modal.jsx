import {forwardRef, useImperative<PERSON><PERSON><PERSON>, useState, useEffect} from "react";
import {Modal, Form, Input, Select, Button, Divider, Flex, message} from "antd";
import {fetchFabricInfo} from "@/modules-ampcon/apis/node_addition_api";
import {fetchAZInfo} from "@/modules-ampcon/apis/dc_template_api";
import {addNetworkIAccess} from "@/modules-ampcon/apis/dc_virtual_resource_api";

const AddVL2Modal = forwardRef((props, ref) => {
    useImperativeHandle(ref, () => ({
        showAddVL2Modal: () => {
            setIsShowModal(true);
        },
        hideAddVL2Modal: () => {
            setIsShowModal(false);
        }
    }));
    const [isShowModal, setIsShowModal] = useState(false);
    const [form] = Form.useForm();
    const {saveCallback} = props;
    const [fabricOptions, setFabricOptions] = useState([]);
    const [podOptions, setPodOptions] = useState([]);
    const VLANTooltip = "If VLAN is not filled in, it will be assigned automatically by AmpCon-DC.";

    const fetchFabrics = async () => {
        try {
            const response = await fetchFabricInfo();
            const filtered = (response.data || []).filter(item => item.has_vlan_domain === true);
            return filtered;
        } catch (error) {
            console.error("Failed to fetch fabrics:", error);
            return [];
        }
    };

    const fetchPods = async fabric_id => {
        if (!fabric_id) return [];

        try {
            const response = await fetchAZInfo({
                fabric_id,
                type: "BareMetal"
            });
            return response.data || [];
        } catch (error) {
            console.error("Failed to fetch pods:", error);
            return [];
        }
    };

    useEffect(() => {
        const loadFabrics = async () => {
            const fabrics = await fetchFabrics();
            setFabricOptions(fabrics);
        };
        const loadPods = async () => {
            const pods = await fetchPods();
            setPodOptions(pods);
        };
        loadFabrics();
        loadPods();
    }, []);

    const handleSave = async () => {
        try {
            const values = await form.validateFields();

            const response = await addNetworkIAccess(
                null, // id
                values.name, // vl2name
                values.pod, // azId
                values.fabric, // fabricId
                values.vlan // vlanId
            );

            if (response.status === 200) {
                setIsShowModal(false);
                form.resetFields();
                message.success(`${response?.info}`);
            } else {
                message.error(`${response?.info}`);
            }
            if (saveCallback) {
                saveCallback();
            }
        } catch (error) {
            console.error("Validation failed:", error);
        }
    };

    return (
        <Modal
            className="ampcon-middle-modal"
            title={
                <div>
                    Create VL2
                    <Divider style={{marginTop: 8, marginBottom: 0}} />
                </div>
            }
            open={isShowModal}
            onCancel={() => {
                setIsShowModal(false);
                form.resetFields();
            }}
            footer={
                <Flex vertical>
                    <Divider style={{marginTop: 0, marginBottom: 20}} />
                    <Flex justify="flex-end">
                        <Button
                            onClick={() => {
                                setIsShowModal(false);
                                form.resetFields();
                            }}
                        >
                            Cancel
                        </Button>
                        <Button type="primary" onClick={handleSave}>
                            Apply
                        </Button>
                    </Flex>
                </Flex>
            }
        >
            <Form
                form={form}
                layout="horizontal"
                labelAlign="left"
                labelCol={{span: 6}}
                wrapperCol={{span: 18}}
                style={{
                    width: "384px",
                    minHeight: "267.23px"
                }}
            >
                <Form.Item
                    label="Name"
                    name="name"
                    rules={[
                        {required: true, message: "VL2 name cannot be empty."},
                        {
                            max: 64,
                            message: "VL2 name cannot exceed 64 characters."
                        },
                        {
                            validator: (_, value) => {
                                if (!value) {
                                    return Promise.resolve();
                                }

                                const pattern = /^[\w-]+$/;

                                if (!pattern.test(value)) {
                                    return Promise.reject(
                                        new Error("Only letters, numbers, underscores and hyphens are allowed!")
                                    );
                                }

                                return Promise.resolve();
                            }
                        }
                    ]}
                >
                    <Input style={{width: "280px"}} />
                </Form.Item>

                <Form.Item label="Fabric" name="fabric" rules={[{required: true, message: "Please select Fabric"}]}>
                    <Select
                        placeholder="Select Fabric"
                        style={{width: "280px"}}
                        onChange={async value => {
                            form.setFieldsValue({pod: undefined});
                            const pods = await fetchPods(value);
                            setPodOptions(pods);
                        }}
                    >
                        {fabricOptions.map(fabric => (
                            <Select.Option key={fabric.id} value={fabric.id}>
                                {fabric.fabric_name}
                            </Select.Option>
                        ))}
                    </Select>
                </Form.Item>

                <Form.Item label="PoD" name="pod" rules={[{required: true, message: "Please select PoD"}]}>
                    <Select placeholder="Select PoD" style={{width: "280px"}}>
                        {podOptions.map(pod => (
                            <Select.Option key={pod.id} value={pod.id}>
                                {pod.az_name}
                            </Select.Option>
                        ))}
                    </Select>
                </Form.Item>

                <Form.Item
                    label="VLAN"
                    name="vlan"
                    tooltip={VLANTooltip}
                    rules={[
                        {
                            validator: (_, value) => {
                                if (!value) return Promise.resolve(); // 可为空
                                const num = Number(value);
                                if (!/^\d+$/.test(value)) {
                                    return Promise.reject(new Error("VLAN must be a number."));
                                }
                                if (/^0\d+/.test(value)) {
                                    return Promise.reject(new Error("VLAN cannot start with 0."));
                                }
                                if (num < 2 || num > 4094) {
                                    return Promise.reject(new Error("VLAN must be between 2 and 4094."));
                                }
                                return Promise.resolve();
                            }
                        }
                    ]}
                >
                    <Input style={{width: "280px"}} />
                </Form.Item>
            </Form>
        </Modal>
    );
});

export default AddVL2Modal;
