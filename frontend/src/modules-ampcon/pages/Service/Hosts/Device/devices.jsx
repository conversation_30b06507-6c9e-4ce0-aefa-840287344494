import {
    createColumnConfig,
    AmpConCustomTable,
    TableFilterDropdown,
    AmpConCustomModalForm,
    AmpConCustomModal
} from "@/modules-ampcon/components/custom_table";
import Icon, {
    EyeOutlined,
    EyeInvisibleOutlined,
    CheckCircleOutlined,
    CloseCircleOutlined,
    LoadingOutlined,
    InboxOutlined
} from "@ant-design/icons";
import {
    Space,
    Button,
    Input,
    Form,
    message,
    Card,
    Select,
    Spin,
    Divider,
    TreeSelect,
    Flex,
    Radio,
    Tabs,
    Tooltip,
    InputNumber
} from "antd";
import Dragger from "antd/es/upload/Dragger";
import * as XLSX from "xlsx";

import React, {useRef, useState, useEffect} from "react";
import {useForm} from "antd/es/form/Form";
import {
    addDeviceInfo,
    checkPingDevicesAPI,
    delDeviceInfo,
    fetchDeviceInfo,
    enableDeviceMonitor,
    batchAddDevice,
    deviceReloadAPI,
    devicePowerUpAPI,
    devicePowerDownAPI
} from "@/modules-ampcon/apis/automation_api";
import {roceBatchCheckAPI, getRoceCheckResultAPI} from "@/modules-ampcon/apis/roce_mold_api";
import {formValidateRules} from "@/modules-ampcon/utils/util";
import {
    addSvg,
    CheckCircleIcon,
    SelectedCheckCircleIcon,
    searchSvg,
    pingSvg,
    UploadTemplateSvg,
    SuccessSvg,
    ErrorSvg,
    QuestionSvg
} from "@/utils/common/iconSvg";
import TextArea from "antd/es/input/TextArea";
import {confirmModalAction} from "@/modules-ampcon/components/custom_modal";
import filterSvg from "../../Nics/Monitoring/resource/filter.svg?react";
import shrinkSvg from "../../Nics/Monitoring/resource/shrink.svg?react";
import unfoldSvg from "../../Nics/Monitoring/resource/unfold.svg?react";
import shrinkHoverSvg from "../../Nics/Monitoring/resource/shrink_hover.svg?react";
import unfoldHoverSvg from "../../Nics/Monitoring/resource/unfold_hover.svg?react";
import DownloadSvg from "@/modules-ampcon/pages/Topo/Topology/resource/download.svg?react";
import Papa from "papaparse";
import {getUserGroup} from "@/modules-ampcon/apis/inventory_api";
import {useSelector} from "react-redux";

const error_ip_msg = "You entered the wrong IP";
const error_username_msg = "You entered the wrong Username";
const error_password_msg = "You entered the wrong Password";
const error_port_msg = "You entered the wrong Port";
const error_deviceName_msg = "You entered the wrong Device Name";
const same_device_msg = "You entered the same device name";
const unfilled_sections_msg = "You have unfilled sections";
const template_header = ["Device Name", "IP", "Username", "Password", "Port"];
const exampleRow = [
    "Required. The name of the device (1-255 characters in length).",
    "Required. The IPv4 address for the third-party server.",
    "Required. The username (1-255 characters in length)of the third party server.",
    "Required. The password (1-255 characters in length)of the third-party server.",
    "Required. The port number (1-65535) of the third-party server."
];

const OtherDeviceButton = ({setIsModalOpen, onPing, setIsCheckConfigModalOpen, selectedRowKeys}) => {
    return (
        <>
            <Button
                type="primary"
                style={{width: 100, height: 32}}
                onClick={() => {
                    setIsModalOpen(true);
                }}
            >
                <Icon component={addSvg} />
                Device
            </Button>
            <Button onClick={onPing} style={{width: 88, height: 32}}>
                <Icon component={pingSvg} />
                Ping
            </Button>
            <Button
                onClick={() => {
                    if (selectedRowKeys.length !== 0) {
                        setIsCheckConfigModalOpen(true);
                    }
                }}
                style={{
                    width: 98,
                    height: 32,
                    backgroundColor: selectedRowKeys.length === 0 ? "rgba(0, 0, 0, 0.04)" : "#fff",
                    borderColor: selectedRowKeys.length === 0 ? "#d9d9d9" : "#14c9bb", // 动态设置边框颜色
                    color: selectedRowKeys.length === 0 ? "rgba(0, 0, 0, 0.25)" : "#14c9bb", // 可选：动态设置文字颜色
                    cursor: selectedRowKeys.length === 0 ? "not-allowed" : "pointer"
                }}
            >
                <Icon component={selectedRowKeys.length === 0 ? CheckCircleIcon : SelectedCheckCircleIcon} />
                Check
            </Button>
        </>
    );
};

const DevicesFormItems = (
    uploadMode,
    setUploadMode,
    fileList,
    setFileList,
    batchMonitorEnable,
    setBatchMonitorEnable,
    groupList,
    connectType,
    setConnectType,
    formContent,
    setFormContent,
    monitorEnabled,
    setMonitorEnabled
) => {
    const [isHoverd, setIsHoverd] = useState(false);
    const currentUser = useSelector(state => state.user.userInfo);

    const handleTypeChange = value => {
        if (value === "password") {
            setFormContent(
                <>
                    <Form.Item
                        name="deviceUser"
                        label="User"
                        rules={[{required: true, message: "Please input your username!"}]}
                    >
                        <Input placeholder="Username" style={{width: "280px"}} />
                    </Form.Item>
                    <Form.Item
                        name="devicePassword"
                        label="Password"
                        rules={[{required: true, message: "Please input your password!"}]}
                    >
                        <Input.Password
                            iconRender={visible => {
                                return visible ? (
                                    <EyeOutlined style={{color: "#c5c5c5"}} />
                                ) : (
                                    <EyeInvisibleOutlined style={{color: "#c5c5c5"}} />
                                );
                            }}
                            placeholder="Password"
                            style={{width: "280px"}}
                        />
                    </Form.Item>
                </>
            );
        } else if (value === "pkey") {
            setFormContent(
                <>
                    <Form.Item
                        name="deviceUser"
                        label="User"
                        rules={[{required: true, message: "Please input your username!"}]}
                    >
                        <Input placeholder="Username" style={{width: "280px"}} />
                    </Form.Item>
                    <Form.Item
                        name="devicePkey"
                        label="Pkey"
                        rules={[{required: true, message: "Please input your pkey!"}]}
                    >
                        <TextArea placeholder="Pkey" style={{width: "280px"}} />
                    </Form.Item>
                    {monitorEnabled ? (
                        <Form.Item
                            name="deviceSudoPass"
                            label="SudoPassword"
                            rules={[{required: true, message: "Please input your sudo password!"}]}
                        >
                            <Input.Password
                                iconRender={visible => {
                                    return visible ? (
                                        <EyeOutlined style={{color: "#c5c5c5"}} />
                                    ) : (
                                        <EyeInvisibleOutlined style={{color: "#c5c5c5"}} />
                                    );
                                }}
                                placeholder="SudoPassword"
                                style={{width: "280px"}}
                            />
                        </Form.Item>
                    ) : null}
                </>
            );
        } else {
            setFormContent(null);
        }

        // return formContent;
    };

    useEffect(() => {
        if (monitorEnabled && formContent) {
            handleTypeChange(connectType);
        }
        if (connectType) {
            handleTypeChange(connectType);
        }
    }, [monitorEnabled, connectType]);

    const items = [
        {key: "manually", label: "Add Manually"},
        {key: "bulk", label: "Add in Bulk"}
    ];
    const props = {
        name: "file",
        multiple: true,
        accept: ".xlsx, .xls, .csv",
        fileList,
        maxCount: 10,
        beforeUpload(file) {
            const isExcel =
                file.type === "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" ||
                file.type === "application/vnd.ms-excel" ||
                file.type === "text/csv";
            if (!isExcel) {
                message.error("Unsupported file type. Please upload an Excel or CSV file.");
                return false;
            }
            if (fileList.some(f => f.name === file.name && f.type === file.type)) {
                message.error("This file has already been added.");
                return false;
            }
            setFileList(prev => [...prev, file]);
            return false; // Prevent automatic upload
        },
        onDrop(e) {
            if (e.dataTransfer.files.length > 1) {
                const files = Array.from(e.dataTransfer.files);
                const validFiles = files.filter(file => {
                    const isExcel =
                        file.type === "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" ||
                        file.type === "application/vnd.ms-excel" ||
                        file.type === "text/csv";
                    return isExcel && !fileList.some(f => f.name === file.name && f.type === file.type);
                });
                if (validFiles.length === 0) {
                    message.error("Unsupported file type or duplicate files. Please upload valid Excel or CSV files.");
                    return false;
                }
                setFileList([...fileList, ...validFiles]);
                return false; // Prevent automatic upload
            }
            const file = e.dataTransfer.files[0];
            const isExcel =
                file.type === "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" ||
                file.type === "application/vnd.ms-excel" ||
                file.type === "text/csv";
            if (!isExcel) {
                message.error("Unsupported file type. Please upload an Excel or CSV file.");
                return false;
            }
            if (fileList.some(f => f.name === file.name && f.type === file.type)) {
                if (
                    setTimeout(() => {
                        document.querySelector(".ant-message-notice-content");
                    }, 50)
                ) {
                    return false;
                }
                message.error("This file has already been added.");
                return false;
            }
            setFileList([...fileList, file]);
            return false; // Prevent automatic upload
        },
        onRemove(file) {
            setFileList(prev => prev.filter(item => item.uid !== file.uid));
        }
    };
    const downloadTemplate = () => {
        // 生成一个二维数组，将表头作为第一行数据，插入一行示例数据
        const wsData = [template_header, exampleRow];
        // 将二维数组转换为工作表对象
        const worksheet = XLSX.utils.aoa_to_sheet(wsData);
        // 设置列宽自适应
        const colWidths = template_header.map((header, i) => {
            // 取表头和示例数据的最大宽度
            const headerLen = header ? header.toString().length : 10;
            const exampleLen = exampleRow[i] ? exampleRow[i].toString().length : 10;
            // 适当加宽一点
            return {wch: Math.max(headerLen, exampleLen) + 5};
        });
        worksheet["!cols"] = colWidths;
        // 新建一个工作簿对象
        const workbook = XLSX.utils.book_new();
        // 将工作表添加到工作簿中，工作表名命名为 "bulk_add_device_template"
        XLSX.utils.book_append_sheet(workbook, worksheet, "bulk_add_device_template");
        // 直接调用 XLSX.writeFile 方法生成并下载 Excel 文件
        XLSX.writeFile(workbook, "batch_import_template.xlsx");
    };

    const labelStyle = {
        fontSize: "14px",
        color: "#212519",
        lineHeight: "17px",
        textAlign: "left",
        fontStyle: "normal"
    };

    return (
        <>
            <Tabs
                items={items}
                activeKey={uploadMode}
                onChange={val => {
                    setUploadMode(val);
                }}
                className="radioGroupTabs extra_radioGroupTabs"
                style={{marginBottom: uploadMode === "manually" ? "8px" : "16px"}}
            />
            {uploadMode === "manually" ? (
                <>
                    <Form.Item
                        name="deviceName"
                        label="Name"
                        rules={[{required: true, message: "Please input your device name!"}]}
                    >
                        <Input placeholder="Device Name" style={{width: "280px"}} />
                    </Form.Item>
                    <Form.Item
                        name="deviceIp"
                        label="IP"
                        rules={[{required: true, message: "Please input your ip!"}, formValidateRules.ipv4()]}
                    >
                        <Input placeholder="Device IP" style={{width: "280px"}} />
                    </Form.Item>
                    <Form.Item
                        name="devicePort"
                        label="Port"
                        initialValue={22}
                        rules={[
                            {
                                required: true,
                                type: "integer",
                                min: 1,
                                max: 65535,
                                message: "Please input a valid port number (1-65535)!"
                            }
                        ]}
                    >
                        <InputNumber placeholder="Device Port" style={{width: "280px"}} />
                    </Form.Item>
                    <Form.Item
                        label="Monitor"
                        name="enableMonitor"
                        valuePropName="checked"
                        initialValue={false}
                        rules={[{required: true, message: "Please choice whether enable monitor!"}]}
                    >
                        <Radio.Group
                            onChange={e => setMonitorEnabled(Boolean(e.target.value))}
                            value={monitorEnabled ? 1 : 0}
                        >
                            <Radio value={1}>Enabled</Radio>
                            <Radio value={0}>Disabled</Radio>
                        </Radio.Group>
                    </Form.Item>
                    <Form.Item
                        name="connectType"
                        label="Type"
                        rules={[{required: true, message: "Please input your connect type !"}]}
                    >
                        <Select placeholder="Select a connect type" onChange={setConnectType} style={{width: "280px"}}>
                            <Select.Option value="password" key="password">
                                Password
                            </Select.Option>
                            <Select.Option value="pkey" key="pkey">
                                Pkey
                            </Select.Option>
                        </Select>
                    </Form.Item>
                    {formContent}
                    {currentUser.userType === "group" && (
                        <Form.Item
                            name="groups"
                            label="Select Group"
                            rules={[{required: true, message: "Please select a group!"}]}
                        >
                            <Select placeholder="Select a group" style={{width: "280px"}} mode="multiple" allowClear>
                                {groupList.map(group => (
                                    <Select.Option key={group.id} value={group.name}>
                                        {group.name}
                                    </Select.Option>
                                ))}
                            </Select>
                        </Form.Item>
                    )}
                </>
            ) : (
                <div style={{minHeight: "268px"}}>
                    <div style={{...labelStyle, marginBottom: "24px"}}>
                        Download Template{" "}
                        <span
                            style={{color: "#14C9BB", cursor: "pointer", marginLeft: "32px"}}
                            onClick={downloadTemplate}
                        >
                            {" "}
                            <Icon component={DownloadSvg} style={{marginRight: "2px"}} />
                            Bulk Import Template
                        </span>
                    </div>
                    <Flex style={{marginBottom: "24px"}}>
                        <div style={{...labelStyle, marginRight: "85px"}}> Upload File </div>
                        <Dragger
                            onMouseEnter={() => {
                                setIsHoverd(true);
                            }}
                            onMouseLeave={() => {
                                setIsHoverd(false);
                            }}
                            {...props}
                            style={{
                                width: "280px",
                                minHeight: "160px",
                                maxHeight: "160px",
                                background: isHoverd ? "#f3fcfc" : ""
                            }}
                        >
                            <p className="ant-upload-drag-icon">
                                <UploadTemplateSvg style={{marginBottom: "12px", marginTop: "30px"}} />
                            </p>
                            <p style={{marginTop: "12px", marginBottom: "0px"}} className="ant-upload-hint">
                                Drag and Drop Files Here or Choose Files
                            </p>
                            <p style={{marginBottom: "30px", marginTop: "0px"}} className="ant-upload-hint">
                                (.xls .xlsx .csv)
                            </p>
                        </Dragger>
                    </Flex>
                    <Flex>
                        <div style={{...labelStyle, marginRight: "106px", marginBottom: "24px"}}> Monitor </div>
                        <Radio.Group
                            onChange={e => {
                                setBatchMonitorEnable(Boolean(e.target.value));
                            }}
                            value={batchMonitorEnable}
                        >
                            <Radio value>Enabled</Radio>
                            <Radio value={false}>Disabled</Radio>
                        </Radio.Group>
                    </Flex>
                    {currentUser.userType === "group" && (
                        <Form.Item
                            name="bulkGroups"
                            label="Select Group"
                            rules={[{required: true, message: "Please select a group!"}]}
                            labelCol={{span: 6}}
                        >
                            <Select placeholder="Select a group" style={{width: "280px"}} mode="multiple" allowClear>
                                {groupList.map(group => (
                                    <Select.Option key={group.id} value={group.name}>
                                        {group.name}
                                    </Select.Option>
                                ))}
                            </Select>
                        </Form.Item>
                    )}
                </div>
            )}
        </>
    );
};

const CheckConfigModal = (checkConfigForm, isCheckConfigModalOpen) => {
    useEffect(() => {
        if (isCheckConfigModalOpen) {
            handleReset();
        }
    }, [isCheckConfigModalOpen]);

    const [searchValue, setSearchValue] = useState("");
    const [selectedValues, setSelectedValues] = useState([]);
    const [dropdownVisible, setDropdownVisible] = useState(false); // 控制下拉框的显示状态

    const handleChange = value => {
        // console.log(`selected ${value}`);
        setSelectedValues(value);
        setSearchValue("");
    };

    // 处理确认按钮点击
    const handleConfirm = () => {
        // console.log("Confirmed values:", selectedValues);
        // console.log(checkConfigForm.getFieldValue("check_config"));
        setDropdownVisible(false); // 关闭下拉框
    };

    const handleReset = () => {
        checkConfigForm.resetFields();
        setSelectedValues([]);
        setSearchValue("");
    };

    const handleSearchChange = e => {
        setSearchValue(e.target.value);
    };

    const selectBox = menu => {
        return (
            <div>
                <Input
                    value={searchValue}
                    onChange={handleSearchChange}
                    placeholder="Search..."
                    prefix={<Icon component={searchSvg} />}
                    style={{width: "100%", marginBottom: "8px"}}
                />
                <Divider style={{marginTop: "0px", marginBottom: "10px"}} />
                {menu}
                <Divider style={{marginTop: "10px", marginBottom: "5px"}} />
                <div style={{display: "flex", justifyContent: "space-between", width: "100%"}}>
                    <Button type="text" onClick={handleReset}>
                        Reset
                    </Button>
                    <Button type="text" onClick={handleConfirm}>
                        OK
                    </Button>
                </div>
            </div>
        );
    };

    const treeData = [
        {
            value: "all",
            title: "All",
            children: [
                {
                    value: "roce",
                    title: "RoCE"
                }
                // 在此处新增巡检选项
                // {
                //     value: "wifi",
                //     title: "Wifi"
                // }
            ]
        }
    ];

    const filteredTreeData = treeData.map(node => ({
        ...node,
        children: node.children
            ? node.children.filter(child => child.title.toLowerCase().includes(searchValue.toLowerCase()))
            : null
    }));

    const [hoveredIcons, setHoveredIcons] = useState({});
    const handleMouseEnter = id => {
        setHoveredIcons(prev => ({...prev, [id]: true}));
    };

    const handleMouseLeave = id => {
        setHoveredIcons(prev => ({...prev, [id]: false}));
    };

    const getIconComponent = (expanded, isHovered) => {
        if (expanded) {
            return isHovered ? shrinkHoverSvg : shrinkSvg;
        }
        return isHovered ? unfoldHoverSvg : unfoldSvg;
    };
    const switcherIcon = ({expanded, id}) => {
        const IconComponent = getIconComponent(expanded, hoveredIcons[id]);

        return (
            <IconComponent
                style={{width: "16px", height: "16px", marginTop: "4px"}}
                alt={expanded ? "shrink" : "unfold"}
                onMouseEnter={() => handleMouseEnter(id)}
                onMouseLeave={() => handleMouseLeave(id)}
            />
        );
    };

    return (
        <Form.Item
            name="check_config"
            label="Check Configuration"
            rules={[{required: true, message: "Please select configuration!"}]}
            labelCol={{span: 7}}
        >
            <TreeSelect
                showSearch
                value={selectedValues}
                style={{width: "280px"}}
                dropdownStyle={{
                    maxHeight: 400,
                    overflow: "auto"
                }}
                open={dropdownVisible} // 控制下拉框的显示
                onDropdownVisibleChange={visible => setDropdownVisible(visible)} // 监听下拉框显示状态变化
                placeholder="select configuration"
                allowClear
                multiple
                treeDefaultExpandAll
                onChange={handleChange}
                treeData={filteredTreeData}
                dropdownRender={menu => selectBox(menu)}
                treeCheckable
                suffixIcon={<Icon component={filterSvg} />}
                switcherIcon={({expanded, value}) => switcherIcon({expanded, id: value})}
            />
        </Form.Item>
    );
};

const SSHModal = ({showSSHModal, setShowSSHModal, record}) => {
    const [sshForm] = useForm();

    const sshSubmit = values => {
        const url = `/ssh/?${btoa(`${record.ip};22;${values.username};${values.password}`)}`;
        window.open(url, "_blank", "noopener,noreferrer");
        sshForm.resetFields();
        setShowSSHModal(false);
    };

    const sshFormRender = () => {
        return (
            <>
                <Form.Item
                    name="username"
                    label="Username"
                    rules={[{required: true, message: "Please input username."}]}
                >
                    <Input style={{width: "280px"}} />
                </Form.Item>
                <Form.Item
                    name="password"
                    label="Password"
                    rules={[{required: true, message: "Please input password."}]}
                >
                    <Input.Password
                        iconRender={visible => {
                            return visible ? (
                                <EyeOutlined style={{color: "#c5c5c5"}} />
                            ) : (
                                <EyeInvisibleOutlined style={{color: "#c5c5c5"}} />
                            );
                        }}
                        style={{width: "280px"}}
                    />
                </Form.Item>
            </>
        );
    };

    return (
        <AmpConCustomModalForm
            title="SSH"
            isModalOpen={showSSHModal}
            formInstance={sshForm}
            layoutProps={{
                labelCol: {
                    span: 6
                }
            }}
            CustomFormItems={sshFormRender}
            onCancel={() => {
                sshForm.resetFields();
                setShowSSHModal(false);
            }}
            onSubmit={sshSubmit}
            modalClass="ampcon-middle-modal"
        />
    );
};
const NoteModal = ({showNoteModal, setShowNoteModal, addResult, errmsg, successCallback, errorCallback, setErrmsg}) => {
    const textStyle = {
        fontSize: "18px",
        color: "#323333",
        lineHeight: "25px",
        textAlign: "center",
        fontStyle: "normal"
    };
    const subtextStyle = {
        fontSize: "14px",
        color: "#929A9E",
        lineHeight: "20px",
        textAlign: "center",
        fontStyle: "normal",
        textTransform: "none",
        margin: "10px 32px 24px 32px"
    };
    // const errormsg = ["You entered the wrong IP", "You entered the wrong IP2", "You entered the wrong IP3"];
    const successItem = (
        <div style={{textAlign: "center", marginTop: "26px"}}>
            <Icon component={SuccessSvg} style={{marginBottom: "16px"}} />
            <div style={textStyle}>Devices are added successfully</div>
            <Button
                type="primary"
                style={{margin: "32px 0px", width: "100px", height: "36px"}}
                onClick={successCallback}
            >
                Back
            </Button>
        </div>
    );
    const errormsgstyle = {
        fontSize: "14px",
        color: "#F53F3F",
        lineHeight: "17px",
        textAlign: "left",
        fontStyle: "normal",
        textTransform: "none",
        marginBottom: "10px",
        marginLeft: "48px",
        marginRight: "48px"
    };
    const errorItem = (
        <div
            style={{
                textAlign: "center",
                marginTop: "26px",
                minHeight: "300px",
                maxHeight: `calc(100vh - 300px)`,
                overflowY: "auto"
            }}
        >
            <Icon component={ErrorSvg} style={{marginBottom: "16px"}} />
            <div style={textStyle}>Devices fail to be added</div>
            <div style={subtextStyle}>
                Please review the following information and modify your bulk import file before submitting again.
            </div>

            <div style={{textAlign: "left"}}>
                {errmsg.map((item, index) => (
                    <div key={index} style={errormsgstyle}>
                        <span
                            style={{
                                width: "4px",
                                height: "4px",
                                background: "background: #F53F3F",
                                fontWeight: 900,
                                fontSize: "20px"
                            }}
                        >
                            ·&nbsp;
                        </span>
                        {item}
                    </div>
                ))}
            </div>

            <Button
                type="primary"
                style={{margin: "22px 0px 32px 0px", width: "100px", height: "36px"}}
                onClick={errorCallback}
            >
                Back
            </Button>
        </div>
    );
    return (
        <AmpConCustomModal
            title="Note"
            isModalOpen={showNoteModal}
            onCancel={() => {
                setErrmsg([]);
                setShowNoteModal(false);
            }}
            modalClass="ampcon-mini-modal"
            childItems={addResult === "success" ? successItem : errorItem}
            footer={null}
        />
    );
};

const PowerUpModal = ({showPowerUpModal, setShowPowerUpModal}) => {
    const [bmcForm] = useForm();
    const bmcSubmit = async values => {
        const ret = await devicePowerUpAPI(values);
        if (ret.status === 200) {
            message.success("BMC Power On Success");
            bmcForm.resetFields();
            setShowPowerUpModal(false);
        } else if (ret.info.includes("Unable to establish")) {
            message.error("Unable to establish connection to the BMC IP, please check BMC information");
        } else {
            message.error("BMC Power On Failed");
        }
    };
    const bmcFormRender = () => {
        return (
            <>
                <Form.Item
                    name="bmcIp"
                    label="BMC IP"
                    rules={[{required: true, message: "Please input your bmc ip!"}, formValidateRules.ipv4()]}
                >
                    <Input placeholder="BMC IP" style={{width: "280px"}} />
                </Form.Item>
                <Form.Item
                    name="bmcUsername"
                    label="BMC Username"
                    rules={[{required: true, message: "Please input your bmc username!"}]}
                >
                    <Input placeholder="BMC Username" style={{width: "280px"}} />
                </Form.Item>
                <Form.Item
                    name="bmcPassword"
                    label="BMC Password"
                    rules={[{required: true, message: "Please input your bmc password!"}]}
                >
                    <Input.Password
                        iconRender={visible => {
                            return visible ? (
                                <EyeOutlined style={{color: "#c5c5c5"}} />
                            ) : (
                                <EyeInvisibleOutlined style={{color: "#c5c5c5"}} />
                            );
                        }}
                        placeholder="BMC Password"
                        style={{width: "280px"}}
                    />
                </Form.Item>
            </>
        );
    };

    return (
        <AmpConCustomModalForm
            title="BMC"
            isModalOpen={showPowerUpModal}
            formInstance={bmcForm}
            layoutProps={{
                labelCol: {
                    span: 6
                }
            }}
            CustomFormItems={bmcFormRender}
            onCancel={() => {
                bmcForm.resetFields();
                setShowPowerUpModal(false);
            }}
            onSubmit={bmcSubmit}
            modalClass="ampcon-middle-modal"
        />
    );
};

const BMCModal = ({showBMCModal, setShowBMCModal}) => {
    const [bmcForm] = useForm();
    const bmcSubmit = values => {
        const url = `http://${values.bmcIp}`;
        window.open(url, "_blank", "noopener,noreferrer");
        setShowBMCModal(false);
        bmcForm.resetFields();
    };
    const bmcFormRender = () => {
        return (
            <Form.Item
                name="bmcIp"
                label="IP"
                rules={[{required: true, message: "Please input your bmc ip!"}, formValidateRules.ipv4()]}
            >
                <Input placeholder="Example: ***********" style={{width: "280px"}} />
            </Form.Item>
        );
    };
    return (
        <AmpConCustomModalForm
            title={
                <>
                    <div style={{display: "flex", alignItems: "center"}}>
                        Log in to BMC
                        <Tooltip
                            placement="right"
                            title="Baseboard Management Controller (BMC) is a dedicated controller used to monitor and manage servers."
                        >
                            <Icon
                                className="deleteSvgStyle"
                                component={QuestionSvg}
                                style={{
                                    width: "16px",
                                    height: "16px",
                                    marginLeft: "6px"
                                }}
                            />
                        </Tooltip>
                    </div>
                    <Divider style={{marginTop: "8px", marginBottom: "0px"}} />
                </>
            }
            isModalOpen={showBMCModal}
            formInstance={bmcForm}
            layoutProps={{
                labelCol: {
                    span: 6
                }
            }}
            CustomFormItems={bmcFormRender}
            onCancel={() => {
                bmcForm.resetFields();
                setShowBMCModal(false);
            }}
            onSubmit={bmcSubmit}
            modalClass="ampcon-middle-modal"
        />
    );
};

const HostDevices = () => {
    const [form] = useForm();
    const [isModalOpen, setIsModalOpen] = useState();
    const tableRef = useRef(null);

    const [pingData, setPingData] = useState([]);
    const [loading, setLoading] = useState(false);

    const [mointorform] = useForm();
    const [isSudoPassModalOpen, setIsSudoPassModalOpen] = useState(false);
    const [selectedRecord, setSelectRecord] = useState({});

    const [selectedRowKeys, setSelectedRowKeys] = useState([]);
    const [selectedRows, setSelectedRows] = useState([]);

    const [checkConfigForm] = useForm();
    const [isCheckConfigModalOpen, setIsCheckConfigModalOpen] = useState();

    const [checkResultForm] = useForm();
    const [checkResult, setCheckResult] = useState(null);
    const [isCheckResultModalOpen, setIsCheckResultModalOpen] = useState();

    const [uploadMode, setUploadMode] = useState("manually");
    const [fileList, setFileList] = useState([]);
    const [showSSHModal, setShowSSHModal] = useState(false);
    const [showNoteModal, setShowNoteModal] = useState(false);
    const [batchMonitorEnable, setBatchMonitorEnable] = useState(false);
    const [addResult, setAddResult] = useState("");
    const [errmsg, setErrmsg] = useState([]);
    const [showPowerUpModal, setShowPowerUpModal] = useState(false);
    const [showBMCModal, setShowBMCModal] = useState(false);
    const [groupList, setGroupList] = useState([]);
    const [connectType, setConnectType] = useState("");
    const [formContent, setFormContent] = useState(null);
    const [monitorEnabled, setMonitorEnabled] = useState(false);
    const [spinning, setSpinning] = useState(false);

    const currentUser = useSelector(state => state.user.userInfo);

    useEffect(() => {
        if (isGroupUser()) {
            getUserGroup().then(response => {
                setGroupList(response.data.filter(group => group.group_type === "host"));
            });
        }
    }, [currentUser]);

    const isGroupUser = () => {
        return currentUser.userType === "group";
    };

    const handlePingData = async () => {
        const pingRes = tableRef.current.getTableData();
        setLoading(true);
        try {
            const pingData = await checkPingDevicesAPI(pingRes);
            setPingData(pingData.data);
        } catch (error) {
            // console.log("Error fetching data:", error);
            setPingData([]);
        } finally {
            setLoading(false);
        }
    };

    const bulkAddDevice = async values => {
        if (fileList.length === 0) {
            message.error("Please upload at least one file.");
            return;
        }

        const allData = [];
        const errormsg = [];

        for (const file of fileList) {
            try {
                // 读取文件内容
                const fileContent = await new Promise((resolve, reject) => {
                    const reader = new FileReader();
                    reader.onload = e => resolve(e.target.result);
                    reader.onerror = reject;
                    reader.readAsBinaryString(file);
                });

                let jsonData = [];
                if (file.name.endsWith(".csv")) {
                    // 解析CSV
                    const parsed = Papa.parse(fileContent, {header: true, skipEmptyLines: true, dynamicTyping: false});

                    jsonData = parsed.data;
                } else {
                    // 解析Excel
                    const workbook = XLSX.read(fileContent, {type: "binary"});
                    // 兼容多张表，合并所有表的数据
                    jsonData = [];
                    workbook.SheetNames.forEach(sheetName => {
                        const worksheet = workbook.Sheets[sheetName];
                        const sheetData = XLSX.utils.sheet_to_json(worksheet, {defval: ""});
                        jsonData = jsonData.concat(sheetData);
                    });
                }

                // 检查表头是否合法
                const headers = Object.keys(jsonData[0] || {});
                const isHeaderValid = template_header.every(h => headers.includes(h));
                if (!isHeaderValid) {
                    errormsg.push(`File "${file.name}" has invalid header.`);
                    setErrmsg(prev => [...prev, `File "${file.name}" has invalid header.`]);
                    setAddResult("fail");
                    setShowNoteModal(true);
                    return;
                }

                allData.push({
                    [file.name]: jsonData
                });
            } catch (e) {
                errormsg.push(`File "${file.name}" parse error: ${e.message}`);
            }
        }
        if (errormsg.length > 0) {
            setErrmsg(errormsg);
            setAddResult("fail");
            setShowNoteModal(true);
            return;
        }

        const data = {
            deviceList: allData,
            enableMonitor: batchMonitorEnable,
            groups: values.bulkGroups
        };

        try {
            const ret = await batchAddDevice(data);
            if (ret.status === 200) {
                form.resetFields();
                setAddResult("success");
                setShowNoteModal(true);
                // handleCancel();
                message.success("Devices are added successfully");
            } else {
                setAddResult("fail");
                setShowNoteModal(true);
                setErrmsg(ret.info);
                // message.error(ret.info);
            }
        } catch (e) {
            errormsg.push(e.message || "Unknown error");
        }
        tableRef.current.refreshTable();
    };

    const handleCancel = () => {
        form.resetFields();
        setIsModalOpen(false);
        setConnectType("");
        setIsCheckConfigModalOpen(false);
        setUploadMode("manually");
        setFileList([]);
        setFormContent(null);
        setMonitorEnabled(false);
        setBatchMonitorEnable(false);
    };

    const onSubmit = async values => {
        if (uploadMode === "bulk") {
            bulkAddDevice(values);
            return;
        }
        const ret = await addDeviceInfo(values);
        if (ret.status === 200) {
            form.resetFields();
            setIsModalOpen(false);
            handleCancel();
            message.success(ret.info);
        } else {
            message.error(ret.info);
        }
        tableRef.current.refreshTable();
    };

    const onEnableMonitor = async values => {
        enableMonitor(selectedRecord, values.deviceSudoPassword);
        mointorform.resetFields();
        setIsSudoPassModalOpen(false);
    };

    const matchFieldsList = [
        {name: "device_name", matchMode: "fuzzy"},
        {name: "ip", matchMode: "fuzzy"},
        {name: "device_user", matchMode: "fuzzy"},
        {name: "create_time", matchMode: "fuzzy"},
        {name: "modified_time", matchMode: "fuzzy"}
    ];

    const searchFieldsList = ["device_name", "ip", "device_user"];

    const delDevice = async device_name => {
        const ret = await delDeviceInfo(device_name);
        if (ret.status === 200) {
            message.success(ret.info);
        } else {
            message.error(ret.info);
        }
        tableRef.current.refreshTable();
    };

    const getCheckResult = async device => {
        setIsCheckResultModalOpen(true);
        const ret = await getRoceCheckResultAPI(device.id);
        if (ret.status === 200) {
            setCheckResult(ret.data);
            // message.success(ret.info);
        } else {
            setCheckResult("No Check Result!");
            // message.error(ret.info);
        }
        tableRef.current.refreshTable();
    };

    const handleDeviceReload = async device => {
        setSpinning(true);
        const ret = await deviceReloadAPI(device.device_name);
        if (ret.status === 200) {
            message.success(`Reload device ${device.device_name} success`);
        } else {
            message.error(`Reload device ${device.device_name} failed: ${ret.info}`);
        }
        setSpinning(false);
    };

    const handleDevicePowerDown = async device => {
        setSpinning(true);
        const ret = await devicePowerDownAPI(device.device_name);
        if (ret.status === 200) {
            message.success(`Power down device ${device.device_name} success`);
        } else {
            message.error(`Power down device ${device.device_name} failed: ${ret.info}`);
        }
        setSpinning(false);
    };

    const enableMonitor = async (device, deviceSudoPass) => {
        const ret = await enableDeviceMonitor(device.device_name, deviceSudoPass);
        if (ret.status === 200) {
            message.success(ret.info);
        } else {
            message.error(ret.info);
        }
        tableRef.current.refreshTable();
    };

    const columns = [
        createColumnConfig("Sysname", "device_name", TableFilterDropdown),
        createColumnConfig("Device IP", "ip", TableFilterDropdown),
        createColumnConfig("Device User", "device_user", TableFilterDropdown),
        createColumnConfig("Create Time", "create_time", TableFilterDropdown),
        // createColumnConfig("Update Time", "modified_time", TableFilterDropdown, "", "", "descend"),
        {
            title: "Status",
            render: (_, record) => {
                if (loading) {
                    return (
                        <div className="custom-spin">
                            <Spin indicator={<LoadingOutlined spin />} size="small" />
                        </div>
                    );
                }
                const pingEntry = pingData.find(p => p.id === record.id);
                if (pingEntry) {
                    return pingEntry.status === 200 ? (
                        <CheckCircleOutlined style={{color: "#14c9bb"}} />
                    ) : (
                        <CloseCircleOutlined style={{color: "red"}} />
                    );
                }
                return <span>---</span>;
            }
        },
        {
            title: "Operation",
            render: (_, record) => {
                return (
                    <div>
                        <Space size="large" className="actionLink">
                            <a
                                onClick={() =>
                                    confirmModalAction("Are you sure want to delete?", () =>
                                        delDevice(record.device_name)
                                    )
                                }
                            >
                                Delete
                            </a>
                            <a
                                onClick={() =>
                                    confirmModalAction("Are you sure want to enable monitor?", () => {
                                        if (record.type === "pwd") {
                                            enableMonitor(record, "");
                                        } else {
                                            setIsSudoPassModalOpen(true);
                                            setSelectRecord(record);
                                        }
                                    })
                                }
                            >
                                Enable Monitor
                            </a>
                            <a
                                onClick={() => {
                                    setShowSSHModal(true);
                                    setSelectRecord(record);
                                }}
                            >
                                Web SSH
                            </a>
                            <a onClick={() => setShowBMCModal(true)}>BMC</a>
                            {/* <a onClick={() => setShowPowerUpModal(true)}>Power Up</a> */}
                            <a
                                onClick={() => {
                                    confirmModalAction("Are you sure want to power down?", () =>
                                        handleDevicePowerDown(record)
                                    );
                                }}
                            >
                                Power Down
                            </a>
                            <a
                                onClick={() => {
                                    confirmModalAction("Do you want to reboot?", () => handleDeviceReload(record));
                                }}
                            >
                                Reboot
                            </a>
                            <a onClick={() => getCheckResult(record)}>Result</a>
                        </Space>
                    </div>
                );
            }
        }
    ];

    const rowSelection = {
        selectedRowKeys,
        selectedRows,
        onChange: (selectedRowKeys, selectedRows) => {
            setSelectedRowKeys(selectedRowKeys);
            setSelectedRows(selectedRows);
        }
    };

    const checkConfigOnSubmit = async values => {
        const data = {
            id_list: selectedRowKeys,
            check_config: values.check_config
        };
        const ret = await roceBatchCheckAPI(data);
        if (ret.code === 200) {
            message.success("Start checking");
        } else {
            message.error(ret.msg);
        }
        tableRef.current.refreshTable();
        setIsCheckConfigModalOpen(false);
        checkConfigForm.resetFields();
        // window.location.reload();
    };

    return (
        <div style={{display: "flex", flex: 1}}>
            <div style={{width: "100%"}}>
                {/* <h2 style={{margin: "8px 0 20px"}}>Device Discovery</h2> */}
                <AmpConCustomModalForm
                    modalClass="ampcon-middle-modal"
                    title="Add Device"
                    isModalOpen={isModalOpen}
                    formInstance={form}
                    layoutProps={{
                        labelCol: {
                            span: 5
                        }
                    }}
                    CustomFormItems={DevicesFormItems(
                        uploadMode,
                        setUploadMode,
                        fileList,
                        setFileList,
                        batchMonitorEnable,
                        setBatchMonitorEnable,
                        groupList,
                        connectType,
                        setConnectType,
                        formContent,
                        setFormContent,
                        monitorEnabled,
                        setMonitorEnabled
                    )}
                    onCancel={handleCancel}
                    onSubmit={onSubmit}
                />
                <AmpConCustomTable
                    columns={columns}
                    searchFieldsList={searchFieldsList}
                    matchFieldsList={matchFieldsList}
                    extraButton={
                        <OtherDeviceButton
                            setIsModalOpen={setIsModalOpen}
                            onPing={handlePingData}
                            setIsCheckConfigModalOpen={setIsCheckConfigModalOpen}
                            selectedRowKeys={selectedRowKeys}
                        />
                    }
                    fetchAPIInfo={fetchDeviceInfo}
                    ref={tableRef}
                    rowSelection={{
                        ...rowSelection,
                        checkStrictly: false
                    }}
                />
                <AmpConCustomModalForm
                    modalClass="ampcon-min-modal"
                    title="Enable Monitor"
                    isModalOpen={isSudoPassModalOpen}
                    formInstance={mointorform}
                    layoutProps={{
                        labelCol: {
                            span: 6
                        }
                    }}
                    CustomFormItems={
                        <Form.Item
                            name="deviceSudoPassword"
                            label="SudoPassword"
                            rules={[{required: true, message: "Please input your sudo password!"}]}
                        >
                            <Input.Password
                                iconRender={visible => {
                                    return visible ? (
                                        <EyeOutlined style={{color: "#c5c5c5"}} />
                                    ) : (
                                        <EyeInvisibleOutlined style={{color: "#c5c5c5"}} />
                                    );
                                }}
                                placeholder="Sudo Password"
                                style={{width: "280px"}}
                            />
                        </Form.Item>
                    }
                    onCancel={() => {
                        mointorform.resetFields();
                        setIsSudoPassModalOpen(false);
                    }}
                    onSubmit={onEnableMonitor}
                />
                <AmpConCustomModalForm
                    modalClass="ampcon-middle-modal"
                    title="Check"
                    isModalOpen={isCheckConfigModalOpen}
                    formInstance={checkConfigForm}
                    layoutProps={{
                        labelCol: {
                            span: 5
                        }
                    }}
                    CustomFormItems={CheckConfigModal(checkConfigForm, isCheckConfigModalOpen)}
                    onCancel={() => {
                        checkConfigForm.resetFields();
                        setIsCheckConfigModalOpen(false);
                        // window.location.reload();
                    }}
                    onSubmit={checkConfigOnSubmit}
                />
                <AmpConCustomModalForm
                    modalClass="CheckResult-custom-wider-modal"
                    title="Check Result"
                    isModalOpen={isCheckResultModalOpen}
                    formInstance={checkResultForm}
                    layoutProps={{
                        labelCol: {
                            span: 5
                        }
                    }}
                    CustomFormItems={
                        <Flex flex={1} layout="horizontal" style={{minHeight: "260.23px"}}>
                            <Input.TextArea
                                style={{
                                    height: `${window.innerHeight / 2}px`,
                                    border: "none",
                                    backgroundColor: "#F8FAFB",
                                    fontSize: "16px",
                                    borderRadius: "4px",
                                    boxShadow: "none",
                                    resize: "none",
                                    padding: "16px"
                                }}
                                value={checkResult}
                                readonly
                            />
                        </Flex>
                    }
                    onCancel={() => {
                        setIsCheckResultModalOpen(false);
                    }}
                    onSubmit={() => {
                        setIsCheckResultModalOpen(false);
                    }}
                />
                <SSHModal showSSHModal={showSSHModal} setShowSSHModal={setShowSSHModal} record={selectedRecord} />
                <NoteModal
                    showNoteModal={showNoteModal}
                    setShowNoteModal={setShowNoteModal}
                    addResult={addResult}
                    errmsg={errmsg}
                    setErrmsg={setErrmsg}
                    successCallback={() => {
                        setShowNoteModal(false);
                        // setIsModalOpen(false);
                        // setFileList([]);
                        setErrmsg([]);
                        // setUploadMode("manually");
                    }}
                    errorCallback={() => {
                        setShowNoteModal(false);
                        setErrmsg([]);
                    }}
                />

                <PowerUpModal showPowerUpModal={showPowerUpModal} setShowPowerUpModal={setShowPowerUpModal} />
                <BMCModal showBMCModal={showBMCModal} setShowBMCModal={setShowBMCModal} />
                <Spin spinning={spinning} tip="Loading..." size="large" fullscreen />
            </div>
        </div>
    );
};

export default HostDevices;
