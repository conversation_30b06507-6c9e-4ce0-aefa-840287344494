import {useState, useEffect} from "react";
import {createColumnConfig, AmpConCustomTable, TableFilterDropdown} from "@/modules-ampcon/components/custom_table";
import {Card, Modal, Checkbox, Button} from "antd";
import {fetchHostInfo} from "@/modules-ampcon/apis/monitor_api";
import tipImage from "@/assets/prompt-image.png";
import styles from "@/modules-ampcon/pages/Service/Hosts/Inventory/inventory.module.scss";

const HostInventory = () => {
    const [isModalVisible, setIsModalVisible] = useState(false);
    const [doNotShowAgain, setDoNotShowAgain] = useState(false);
    const [isApplyDisabled, setIsApplyDisabled] = useState(true);
    useEffect(() => {
        const hideTip = localStorage.getItem("hideHostInventoryTip");
        if (!hideTip) {
            setIsModalVisible(true);
        }
    }, []);
    const handleCheckboxChange = e => {
        setDoNotShowAgain(e.target.checked);
        setIsApplyDisabled(!e.target.checked);
    };
    const handleApply = () => {
        if (doNotShowAgain) {
            localStorage.setItem("hideHostInventoryTip", "true");
        }
        setIsModalVisible(false);
    };
    const tableSearchFieldsList = ["hostname", "last_seen", "os_version", "cpu", "memory", "storage"];
    const matchFieldsList = [
        {name: "hostname", matchMode: "fuzzy"},
        {name: "last_seen", matchMode: "fuzzy"},
        {name: "os_version", matchMode: "fuzzy"},
        {name: "cpu", matchMode: "fuzzy"},
        {name: "memory", matchMode: "fuzzy"},
        {name: "storage", matchMode: "fuzzy"}
    ];
    const columns = [
        createColumnConfig("Sysname", "hostname", TableFilterDropdown),
        createColumnConfig("Last Seen", "last_seen", TableFilterDropdown, "", "", "descend"),
        createColumnConfig("OS Vendor", "os_version", TableFilterDropdown),
        createColumnConfig("CPU", "cpu", TableFilterDropdown),
        createColumnConfig("Memory", "memory", TableFilterDropdown),
        createColumnConfig("Storage", "storage", TableFilterDropdown)
    ];
    return (
        <>
            <Modal
                open={isModalVisible}
                onCancel={() => setIsModalVisible(false)}
                className={styles.modalContainer}
                width={680}
                footer={
                    <div className={styles.modalFooter}>
                        <Button key="cancel" onClick={() => setIsModalVisible(false)} style={{marginRight: 10}}>
                            Cancel
                        </Button>
                        <Button key="apply" type="primary" onClick={handleApply} disabled={isApplyDisabled}>
                            Confirm
                        </Button>
                    </div>
                }
            >
                <div className={styles.modalBody}>
                    <img src={tipImage} alt="Tip" className={styles.tipImage} />
                    <h3 className={styles.modalTitle}>Host Inventory Information Tip</h3>
                    <p className={styles.tipText}>
                        Please enable monitor on the Device Discovery page before you can view the host inventory
                        information.
                    </p>
                    <Checkbox onChange={handleCheckboxChange} className={styles.checkboxStyle}>
                        This reminder will not appear again in the future.
                    </Checkbox>
                </div>
            </Modal>
            <div className={styles.inventoryCard}>
                <div style={{width: "100%"}}>
                    {/* <h2 className={styles.inventoryTitle}>Inventory</h2> */}
                    <AmpConCustomTable
                        columns={columns}
                        searchFieldsList={tableSearchFieldsList}
                        matchFieldsList={matchFieldsList}
                        fetchAPIInfo={fetchHostInfo}
                    />
                </div>
            </div>
        </>
    );
};

export default HostInventory;
