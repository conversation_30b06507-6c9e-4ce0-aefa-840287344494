import {
    message,
    But<PERSON>,
    Card,
    Divider,
    List,
    Row,
    Col,
    Tabs,
    DatePicker,
    Tag,
    Flex,
    Select,
    Form,
    Radio,
    Transfer,
    InputNumber,
    Tooltip,
    Input
} from "antd";
import {useState, useEffect, useRef, useMemo} from "react";
import {useLocation, useNavigate} from "react-router-dom";
import {useForm} from "antd/es/form/Form";
import Icon from "@ant-design/icons/lib/components/Icon";
import {searchSvg} from "@/utils/common/iconSvg";
import {QuestionCircleOutlined} from "@ant-design/icons";
import {TelemetrySetting} from "@/modules-ampcon/pages/Dashboard/Telemetry/telemetry_view";
import {
    fetchInterfaceInfo,
    fetchDeviceInfo,
    fetchModulesInfo,
    fetchAIInfo,
    fetchMacTable,
    fetchArpTable,
    fetchInterfaceTopK,
    fetchModulesTopK,
    fetchModulesPortStatus,
    fetchModulesLinkStatus,
    fetchLinkInfo,
    updateLinksThreshold,
    fetchUsageTopK,
    fetchFanData,
    fetchBGPTable,
    fetchOSPFTable,
    fetchRouteTable,
    fetchMlagTable,
    fetchPoeTable
} from "@/modules-ampcon/apis/monitor_api";
import {fetchAllSwitch} from "@/modules-ampcon/apis/dashboard_api";
import {AmpConCustomModalForm, AmpConCustomTelemteryTable} from "@/modules-ampcon/components/custom_table";

import {
    CustomTelemetryCard,
    CustomTelemetryLimitCard,
    CustomTelemetryLinkCard
} from "@/modules-ampcon/components/echarts_common";
import settingGreySvg from "../../Topo/Topology/resource/site_grey.svg?react";
import settingGreenSvg from "../../Topo/Topology/resource/site_green.svg?react";

import styles from "./switch_telemetry.module.scss";
import HealthStatus from "./health_status";

const {RangePicker} = DatePicker;
const {Option} = Select;

const importAllImages = () => {
    const images = {};
    const modules = import.meta.glob("/src/assets/switchs/*.png", {eager: true});

    // eslint-disable-next-line guard-for-in
    for (const path in modules) {
        const key = path.split("/").pop().replace(".png", "");
        images[key] = modules[path];
    }

    return images;
};

const switchImages = importAllImages();

const usageOptions = {
    "Usage (%)": "both",
    "Fan (%)": "fan"
};

const interfaceCheckboxOptions = {
    "In Octets": "in-octets",
    "In Pkts": "in-pkts",
    "In Discards": "in-discards",
    "In Errors": "in-errors",
    "In Fcs Errors": "in-fcs-errors",
    "Out Octets": "out-octets",
    "Out Pkts": "out-pkts",
    "Out Discards": "out-discards",
    "Out Errors": "out-errors",
    "Out Bits Rate (bits/s)": "out-bits-rate",
    "In Bits Rate (bits/s)": "in-bits-rate",
    "Out Pkts Rate (packets/s)": "out-pkts-rate",
    "In Pkts Rate (packets/s)": "in-pkts-rate"
};

const modulesCheckboxOptions = {
    "Tx Power (dBm)": "output-power",
    "Rx Power (dBm)": "input-power",
    "Temperature (℃)": "laser-temperature"
    // "Output Power - Input Power (dB)": "attenuation"
};

const modulesStatusCheckboxOptions = {
    "Temperature (℃)": "transceiver_state_fsconfig_platform_transceiver_extensions:laser_temperature",
    "Voltage (V)": "transceiver_state_supply_voltage",
    "Bias (mA)": "transceiver_physical_channels_channel_state_laser_bias_current",
    "Tx Power (dBm)": "transceiver_state_output_power",
    "Rx Power (dBm)": "transceiver_state_input_power"
};

const modulesLinkCheckboxOptions = {
    "Analysis Link": "analysis"
};

const aiCheckboxOptions = {
    "ECN-Marked-Packets": "ecn-marked-packets",
    "Send-PFC-Pause-Frames": "send-pfc-pause-frames",
    "Receive-PFC-Pause-Frames": "receive-pfc-pause-frames",
    "PFC-Deadlock-Monitor-Count": "pfc-deadlock-monitor-count",
    "PFC-Deadlock-Recovery-Count": "pfc-deadlock-recovery-count"
};

const extractParts = str => {
    const match = str.match(/^([A-Za-z-]+)-(\d+)\/(\d+)\/(\d+)$/);
    if (!match) {
        return {prefix: str, numbers: []};
    }
    const prefix = match[1];
    const numbers = match.slice(2).map(num => parseInt(num, 10));
    return {prefix, numbers};
};

const compareNumbers = (a, b) => {
    const aParts = extractParts(a);
    const bParts = extractParts(b);
    if (aParts.prefix !== bParts.prefix) {
        return aParts.prefix.localeCompare(bParts.prefix);
    }
    for (let i = 0; i < Math.min(aParts.numbers.length, bParts.numbers.length); i++) {
        if (aParts.numbers[i] !== bParts.numbers[i]) {
            return aParts.numbers[i] - bParts.numbers[i];
        }
    }
    return aParts.numbers.length - bParts.numbers.length;
};

const formatNumber = value => (value > 1e8 ? value.toExponential(3) : value || 0);

const SwitchTelemetry = () => {
    const location = useLocation();
    const navigate = useNavigate();
    const [switchInfo, setSwichInfo] = useState([]);
    const [record, setRecord] = useState({});
    const [activeKey, setActiveKey] = useState("switchOverview");

    const tabRef = useRef(null);
    const [tableWidth, setTableWidth] = useState(0);

    const handleResize = () => {
        if (tabRef.current) {
            setTableWidth(tabRef.current.offsetWidth - 50 - 65);
        }
    };

    const makeSwitchInfo = async sn => {
        const response = await fetchAllSwitch(
            1,
            10,
            [{field: "sn", filters: [{value: sn, matchMode: "exact"}]}],
            [],
            {}
        );
        if (response.status === 200) {
            const record = response.data[0];
            const data = [
                {
                    title: "SN",
                    value: record.sn
                },
                {
                    title: "Hardware-ID",
                    value: record.hwid
                },
                {
                    title: "MAC Address",
                    value: record.mac_addr
                },
                {
                    title: "Sysname",
                    value: record.host_name
                },
                {
                    title: "Mgmt IP",
                    value: record.link_ip_addr
                },
                {
                    title: "Version",
                    value: record.version
                }
            ];
            setSwichInfo(data);
            setRecord(record);
        }
    };

    const items = [
        {
            key: "switchOverview",
            label: "Switch Overview",
            children: (
                <SwitchOverview
                    active={activeKey === "switchOverview"}
                    sn={record.sn}
                    showAI={record.gnmi_ai && import.meta.env.VITE_APP_EXPORT_MODULE === "AmpCon-DC"}
                />
            )
        },
        {
            key: "deviceOverview",
            label: "Device Overview",
            children: <DeviceOverview active={activeKey === "deviceOverview"} sn={record.sn} tableWidth={tableWidth} />
        },
        {
            key: "portOverview",
            label: "Port Overview",
            children: <PortOverview active={activeKey === "portOverview"} sn={record.sn} tableWidth={tableWidth} />
        },
        {
            key: "modulesOverview",
            label: "Modules Overview",
            children: (
                <ModulesOverview
                    active={activeKey === "modulesOverview"}
                    sn={record.sn}
                    model={record.platform_model}
                    tableWidth={tableWidth}
                    sysname={record.host_name}
                    ip_address={record.mgt_ip}
                    showHealthStatus={!!(location.state && location.state.showModuleStatus)}
                />
            )
        },
        {
            key: "bgp",
            label: "BGP",
            children: <BGPTableOverview active={activeKey === "bgp"} sn={record.sn} tableWidth={tableWidth} />
        },
        {
            key: "ospf",
            label: "OSPF",
            children: <OSPFTableOverview active={activeKey === "ospf"} sn={record.sn} tableWidth={tableWidth} />
        },
        {
            key: "mac",
            label: "MAC",
            children: <MacTableOverview active={activeKey === "mac"} sn={record.sn} tableWidth={tableWidth} />
        },
        {
            key: "arp",
            label: "ARP",
            children: <ArpTableOverview active={activeKey === "arp"} sn={record.sn} tableWidth={tableWidth} />
        },
        record.gnmi_ai === true && import.meta.env.VITE_APP_EXPORT_MODULE !== "AmpCon-CAMPUS"
            ? {
                  key: "ai",
                  label: "AI",
                  children: <AIOverview active={activeKey === "ai"} sn={record.sn} tableWidth={tableWidth} />
              }
            : null,
        {
            key: "route",
            label: "IP Route",
            children: <RouteTableOverview active={activeKey === "route"} sn={record.sn} tableWidth={tableWidth} />
        },
        {
            key: "mlag",
            label: "MLAG",
            children: <MLAGTableOverview active={activeKey === "mlag"} sn={record.sn} tableWidth={tableWidth} />
        },
        import.meta.env.VITE_APP_EXPORT_MODULE === "AmpCon-CAMPUS"
            ? {
                  key: "poe",
                  label: "PoE",
                  children: <POETableOverview active={activeKey === "poe"} sn={record.sn} tableWidth={tableWidth} />
              }
            : null
    ];

    useEffect(() => {
        const pathParts = location.pathname.split("/");
        const sn = pathParts[pathParts.length - 1];
        makeSwitchInfo(sn);
        if (location.state && location.state.showModuleStatus) {
            setActiveKey("modulesOverview");
        }
    }, [location.pathname]);

    useEffect(() => {
        handleResize();
        window.addEventListener("resize", handleResize);

        return () => {
            window.removeEventListener("resize", handleResize);
        };
    }, []);

    return (
        <div style={{display: "flex", flexDirection: "column", height: "100%"}}>
            {/* <a
                onClick={() => {
                    if (import.meta.env.VITE_APP_EXPORT_MODULE === "AmpCon-SUPER") {
                        navigate("/device/switches/switch");
                    } else {
                        navigate("/device/switches");
                    }
                }}
            >
                <Button
                    type="link"
                    icon={<LeftOutlined />}
                    style={{
                        border: "none",
                        fontSize: "14px",
                        color: "#14C9BB",
                        marginBottom: "20px"
                    }}
                >
                    Back
                </Button>
            </a> */}
            <Card title="Device Information">
                <div
                    style={{
                        display: "flex",
                        flexDirection: "row",
                        justifyContent: "flex-start",
                        alignItems: "center",
                        marginTop: "12px"
                    }}
                >
                    <div style={{marginLeft: "36px"}}>
                        <img
                            src={switchImages[record.platform_model]?.default || switchImages.default_switch.default}
                            alt="Model"
                        />
                        <div style={{display: "flex"}}>
                            <div style={{color: "#929A9E", marginLeft: "8px", marginRight: "8px"}}>Model:</div>
                            <div style={{whiteSpace: "nowrap"}}>{record.platform_model}</div>
                        </div>
                    </div>
                    <Divider type="vertical" style={{height: "100px", marginLeft: "60px", marginRight: "60px"}} />
                    <List
                        dataSource={switchInfo}
                        split={false}
                        grid={{gutter: 32, column: 3}}
                        renderItem={item => (
                            <List.Item key={item.title}>
                                <div
                                    style={{
                                        display: "flex",
                                        alignItems: "center"
                                    }}
                                >
                                    <span
                                        style={{
                                            color: "#929A9E",
                                            width: "100px",
                                            display: "inline-block"
                                        }}
                                    >
                                        {item.title}:
                                    </span>
                                    <span style={{whiteSpace: "nowrap"}}>{item.value}</span>
                                </div>
                            </List.Item>
                        )}
                    />
                </div>
            </Card>
            <Flex ref={tabRef} style={{flex: "1"}}>
                <Tabs onChange={setActiveKey} activeKey={activeKey} items={items} style={{marginTop: "24px"}} />
            </Flex>
        </div>
    );
};

const getSorter = (dataIndex, isString) => {
    return isString ? (a, b) => a[dataIndex].localeCompare(b[dataIndex]) : (a, b) => a[dataIndex] - b[dataIndex];
};

const SwitchOverview = ({active, sn, showAI}) => {
    const [form] = useForm();
    const [timeRange, setTimeRange] = useState(["", ""]);
    const [counters, setCounters] = useState({
        usage: Object.values(usageOptions),
        interface: Object.values(interfaceCheckboxOptions),
        modules: Object.values(modulesCheckboxOptions),
        ai: []
    });
    const [isSelectCountersModalOpen, setSelectCountersModalOpen] = useState(false);
    const cardRefs = useRef({});
    const [isHovered, setIsHovered] = useState(false);

    const addToRefs = (el, name) => {
        if (el) {
            cardRefs.current[name] = el;
        }
    };

    const onChangeCounters = () => {
        if (form.getFieldsValue().usage !== undefined) {
            setCounters(prevCounters => ({
                ...prevCounters,
                usage: form.getFieldsValue().usage
            }));
        }
        if (form.getFieldsValue().interface !== undefined) {
            setCounters(prevCounters => ({
                ...prevCounters,
                interface: form.getFieldsValue().interface
            }));
        }
        if (form.getFieldsValue().modules !== undefined) {
            setCounters(prevCounters => ({
                ...prevCounters,
                modules: form.getFieldsValue().modules
            }));
        }
        if (showAI && form.getFieldsValue().ai !== undefined) {
            setCounters(prevCounters => ({
                ...prevCounters,
                ai: form.getFieldsValue().ai
            }));
        }
        setSelectCountersModalOpen(false);
    };

    useEffect(() => {
        if (active) {
            Object.keys(cardRefs.current).forEach(key => {
                const ref = cardRefs.current[key];
                if (ref && ref.refreshTelemetry) {
                    ref.refreshTelemetry();
                }
            });
        }
    }, [active]);

    useEffect(() => {
        if (showAI) {
            const newCounters = {
                ...counters,
                ai: Object.values(aiCheckboxOptions)
            };
            setCounters(newCounters);
            form.setFieldsValue(newCounters);
        } else {
            form.setFieldsValue(counters);
        }
    }, [showAI]);

    const fetchData = async (label, finalTopK, target, timeRange, _, type) => {
        let chartData = [];
        let xAxisData = [];
        let response;
        if (type === "usage") {
            const name = usageOptions[label];
            response = await fetchUsageTopK(name, finalTopK, target, timeRange[0], timeRange[1]);
        } else if (type === "fan") {
            response = await fetchFanData(target, timeRange[0], timeRange[1]);
        } else if (type === "interface") {
            const name = interfaceCheckboxOptions[label];
            response = await fetchInterfaceTopK(name, finalTopK, target, timeRange[0], timeRange[1]);
        } else if (type === "modules") {
            const name = modulesCheckboxOptions[label];
            response = await fetchModulesTopK(name, finalTopK, target, timeRange[0], timeRange[1]);
        } else {
            const name = aiCheckboxOptions[label];
            response = await fetchInterfaceTopK(name, finalTopK, target, timeRange[0], timeRange[1]);
        }

        if (response.status !== 200) {
            message.error(response.info);
        } else if (response.data.length > 0) {
            chartData = response.data.map(item => {
                const channelIndex = item.channel_index;
                const name = channelIndex ? `${item.interface_name}_${item.channel_index}` : item.interface_name;
                return {
                    name,
                    data: item.values.map(([x, y]) => [x, y])
                };
            });

            xAxisData = Array.from(new Set(response.data.flatMap(item => item.values.map(([x]) => x)))).sort();
        }

        return {
            chartData,
            xAxisData
        };
    };

    const allCards = useMemo(
        () => [
            counters.usage.length > 0 && [
                <CustomTelemetryCard
                    label={Object.keys(usageOptions).find(key => usageOptions[key] === counters.usage[0])}
                    type="usage"
                    timeRange={timeRange}
                    fetchApi={fetchData}
                    ref={el => addToRefs(el, counters.usage[0])}
                    target={sn}
                    cardstyle={{
                        borderColor: "#E7E7E7",
                        borderWidth: "1px",
                        borderStyle: "solid",
                        boxShadow: "none"
                    }}
                    showFilter={false}
                    showTopK={false}
                />,
                <CustomTelemetryCard
                    label={Object.keys(usageOptions).find(key => usageOptions[key] === counters.usage[1])}
                    type="fan"
                    timeRange={timeRange}
                    fetchApi={fetchData}
                    ref={el => addToRefs(el, counters.usage[1])}
                    target={sn}
                    cardstyle={{
                        borderColor: "#E7E7E7",
                        borderWidth: "1px",
                        borderStyle: "solid",
                        boxShadow: "none"
                    }}
                    showFilter={false}
                    showTopK={false}
                />
            ],
            counters.interface.length > 0 &&
                counters.interface.map(interfaceItem => (
                    <CustomTelemetryCard
                        label={Object.keys(interfaceCheckboxOptions).find(
                            key => interfaceCheckboxOptions[key] === interfaceItem
                        )}
                        type="interface"
                        timeRange={timeRange}
                        fetchApi={fetchData}
                        ref={el => addToRefs(el, interfaceItem)}
                        target={sn}
                        cardstyle={{
                            borderColor: "#E7E7E7",
                            borderWidth: "1px",
                            borderStyle: "solid",
                            boxShadow: "none"
                        }}
                        showFilter={false}
                    />
                )),

            counters.modules.length > 0 &&
                counters.modules.map(modulesItem => (
                    <CustomTelemetryCard
                        label={Object.keys(modulesCheckboxOptions).find(
                            key => modulesCheckboxOptions[key] === modulesItem
                        )}
                        type="modules"
                        timeRange={timeRange}
                        fetchApi={fetchData}
                        ref={el => addToRefs(el, modulesItem)}
                        target={sn}
                        cardstyle={{
                            borderColor: "#E7E7E7",
                            borderWidth: "1px",
                            borderStyle: "solid",
                            boxShadow: "none"
                        }}
                        showFilter={false}
                    />
                )),

            counters.ai.length > 0 &&
                counters.ai.map(aiItem => (
                    <CustomTelemetryCard
                        label={Object.keys(aiCheckboxOptions).find(key => aiCheckboxOptions[key] === aiItem)}
                        type="ai"
                        timeRange={timeRange}
                        fetchApi={fetchData}
                        ref={el => addToRefs(el, aiItem)}
                        target={sn}
                        cardstyle={{
                            borderColor: "#E7E7E7",
                            borderWidth: "1px",
                            borderStyle: "solid",
                            boxShadow: "none"
                        }}
                        showFilter={false}
                    />
                ))
        ],
        [counters, timeRange, sn]
    );

    return (
        <>
            <div style={{display: "flex", justifyContent: "flex-end"}}>
                <div style={{display: "flex", alignItems: "center", justifyContent: "center"}}>Time</div>
                <RangePicker
                    showTime={{format: "HH:mm"}}
                    format="YYYY-MM-DD HH:mm"
                    style={{height: "32px", marginLeft: "32px"}}
                    onChange={(_, dateString) => {
                        setTimeRange(dateString);
                    }}
                    disabledDate={current => {
                        const now = new Date();
                        now.setHours(23);
                        now.setMinutes(59);
                        const oneMonthAgo = new Date();
                        oneMonthAgo.setMonth(now.getMonth() - 1);
                        oneMonthAgo.setHours(23);
                        return current && (current > now || current < oneMonthAgo);
                    }}
                    disabledTime={current => {
                        const now = new Date();
                        const currentDay = now.getDate();
                        const currentHours = now.getHours();
                        const currentMinutes = now.getMinutes();

                        const targetDate = new Date(current);
                        const targetDay = targetDate.getDate();
                        const targetHours = targetDate.getHours();
                        const disabledTimeLists = {};
                        if (!current || targetDay === currentDay) {
                            disabledTimeLists.disabledHours = () =>
                                Array.from({length: 24 - currentHours + 1}, (_, hour) => hour + currentHours + 1);
                            disabledTimeLists.disabledMinutes = () =>
                                !current || targetHours === currentHours
                                    ? Array.from(
                                          {length: 60 - currentMinutes + 1},
                                          (_, minute) => minute + currentMinutes + 1
                                      )
                                    : [];
                        }
                        return disabledTimeLists;
                    }}
                />
                <Divider type="vertical" style={{height: "30px", marginLeft: "16px", marginRight: "16px"}} />
                <Button
                    style={{borderColor: isHovered ? "#34DCCF" : "#d9d9d9"}}
                    icon={<Icon component={isHovered ? settingGreenSvg : settingGreySvg} />}
                    onClick={() => setSelectCountersModalOpen(true)}
                    onMouseEnter={() => {
                        setIsHovered(true);
                    }}
                    onMouseLeave={() => {
                        setIsHovered(false);
                    }}
                />
                {/* <Button icon={<Icon component={settingGreenSvg} />} onClick={() => setSelectCountersModalOpen(true)} /> */}
            </div>
            {sn ? (
                <div style={{height: "100%", width: "100%", marginTop: "18px", marginBottom: "18px"}}>
                    <Row gutter={[24, 24]}>
                        {allCards.flat().map((card, index) => (
                            <Col key={index} span={24} xxl={12} style={{display: "flex", justifyContent: "center"}}>
                                {card}
                            </Col>
                        ))}
                    </Row>
                </div>
            ) : null}
            <TelemetrySetting
                form={form}
                isModalOpen={isSelectCountersModalOpen}
                onCancel={() => {
                    form.setFieldsValue(counters);
                    setSelectCountersModalOpen(false);
                }}
                onChange={onChangeCounters}
                showAI={showAI}
            />
        </>
    );
};

const DeviceOverview = ({active, sn, tableWidth}) => {
    const [fanFormattedData, setFanFormattedData] = useState([]);
    const [rpsuFormattedData, setRpsuFormattedData] = useState([]);

    const fanColumnsConfig = [
        {title: "Position", dataIndex: "position"},
        {title: "Direction", dataIndex: "direction"},
        {title: "PWM", dataIndex: "pwm"},
        {title: "Speed", dataIndex: "speed"}
    ];

    const fanColumns = fanColumnsConfig.map(obj => ({
        ...obj,
        sorter: obj.sorter === false ? obj.sorter : getSorter(obj.dataIndex, obj.isString),
        width: obj.width || 120
    }));

    const rpsuColumnsConfig = [
        {title: "PSU Index", dataIndex: "psu_index"},
        {title: "Power On", dataIndex: "power_on"},
        {title: "Enabled", dataIndex: "enabled"},
        {title: "Present", dataIndex: "present"}
    ];

    const rpsuColumns = rpsuColumnsConfig.map(obj => ({
        ...obj,
        sorter: obj.sorter === false ? obj.sorter : getSorter(obj.dataIndex, obj.isString),
        width: obj.width || 120
    }));

    const fetchData = async () => {
        const response = await fetchDeviceInfo(sn);
        if (response.status !== 200) {
            message.error(response.info);
        } else {
            const fanFormattedData = response.data.fan_state.map(fan => ({
                direction: fan.direction,
                position: fan.position,
                pwm: fan.pwm,
                speed: fan.speed
            }));
            const rpsuFormattedData = response.data.rpsu_state.map(rpsu => ({
                index: rpsu.index,
                power_on: rpsu.power_on,
                enabled: rpsu.enabled,
                present: rpsu.present,
                psu_index: rpsu.psu_index
            }));
            setFanFormattedData(fanFormattedData);
            setRpsuFormattedData(rpsuFormattedData);
        }
    };

    useEffect(() => {
        if (active) {
            fetchData();
        }
    }, [active, sn]);

    return (
        <div>
            <h2>RPSUs</h2>
            <AmpConCustomTelemteryTable
                columnsConfig={rpsuColumns}
                data={rpsuFormattedData}
                tableWidth={tableWidth}
                showSetting={false}
            />

            <h2>Fans</h2>
            <AmpConCustomTelemteryTable
                columnsConfig={fanColumns}
                data={fanFormattedData}
                tableWidth={tableWidth}
                showSetting={false}
            />
        </div>
    );
};

const PortOverview = ({active, sn, tableWidth}) => {
    const [formattedData, setFormattedData] = useState([]);

    const columnsConfig = [
        {title: "Port Name", dataIndex: "name", fixed: "left", isString: true, sorter: false},
        {
            title: "Port State",
            dataIndex: "oper_status",
            isString: true,
            render: (_, record) => (
                <Tag className={record.oper_status === "UP" ? "up-tag" : "down-tag"}>{record.oper_status}</Tag>
            )
        },
        {title: "MTU", dataIndex: "mtu", width: 100},
        {title: "Loopback Mode", dataIndex: "loopback_mode", isString: true, width: 150},
        {title: "Port Speed", dataIndex: "port_speed", isString: true},
        {title: "In Bandwidth Utilization", dataIndex: "in_bits_port_speed_usage", isString: true, width: 200},
        {title: "Out Bandwidth Utilization", dataIndex: "out_bits_port_speed_usage", isString: true, width: 220},
        {title: "Auto Negotiate", dataIndex: "auto_negotiate", isString: true, width: 150},
        {title: "Mac Addr", dataIndex: "hw_mac_address", isString: true, width: 140},
        {title: "Duplex Mode", dataIndex: "duplex_mode", isString: true, width: 140},
        {title: "In Broadcast Pkts", dataIndex: "in_broadcast_pkts", width: 160},
        {title: "In Discards", dataIndex: "in_discards"},
        {title: "In Errors", dataIndex: "in_errors"},
        {title: "In Fcs Errors", dataIndex: "in_fcs_errors", width: 140},
        {title: "In Multicast Pkts", dataIndex: "in_multicast_pkts", width: 160},
        {title: "In Octets", dataIndex: "in_octets"},
        {title: "In Pkts", dataIndex: "in_pkts"},
        {title: "In Unicast Pkts", dataIndex: "in_unicast_pkts", width: 140},
        {title: "Out Broadcast Pkts", dataIndex: "out_broadcast_pkts", width: 170},
        {title: "Out Discards", dataIndex: "out_discards", width: 140},
        {title: "Out Errors", dataIndex: "out_errors"},
        {title: "Out Multicast Pkts", dataIndex: "out_multicast_pkts", width: 170},
        {title: "Out Octets", dataIndex: "out_octets"},
        {title: "Out Pkts", dataIndex: "out_pkts"},
        {title: "Out Unicast Pkts", dataIndex: "out_unicast_pkts", width: 160},
        {title: "In Oversize Frames", dataIndex: "in_oversize_frames", width: 170},
        {title: "In Undersize Frames", dataIndex: "in_undersize_frames", width: 180},
        {title: "Out Bits Rate (bits/s)", dataIndex: "out_bits_rate", width: 180},
        {title: "In Bits Rate (bits/s)", dataIndex: "in_bits_rate", width: 170},
        {title: "Out Pkts Rate (packets/s)", dataIndex: "out_pkts_rate", width: 210},
        {title: "In Pkts Rate (packets/s)", dataIndex: "in_pkts_rate", width: 200},
        {title: "In Frames 64 Octets", dataIndex: "in_frames_64_octets", width: 180},
        {title: "In Frames 65-127 Octets", dataIndex: "in_frames_65_127_octets", width: 210},
        {title: "In Frames 128-255 Octets", dataIndex: "in_frames_128_255_octets", width: 210},
        {title: "In Frames 256-511 Octets", dataIndex: "in_frames_256_511_octets", width: 210},
        {title: "In Frames 512-1023 Octets", dataIndex: "in_frames_512_1023_octets", width: 220},
        {title: "In Frames 1024-1518 Octets", dataIndex: "in_frames_1024_1518_octets", width: 230}
    ];

    const columns = columnsConfig.map(obj => ({
        ...obj,
        sorter: obj.sorter === false ? obj.sorter : getSorter(obj.dataIndex, obj.isString),
        width: obj.width || 120
    }));

    const fetchData = async () => {
        const response = await fetchInterfaceInfo(sn);
        if (response.status !== 200) {
            message.error(response.info);
        } else {
            const formattedData = Object.keys(response.data).map(key => {
                const port = response.data[key];
                const port_speed =
                    port?.ethernet_state?.negotiated_port_speed?.match(/SPEED_(.*)/)?.[1] ||
                    port?.ethernet_state?.port_speed?.match(/SPEED_(.*)/)?.[1] ||
                    "-";
                return {
                    name: port?.config?.name || "-",
                    oper_status: port?.state?.oper_status || "-",
                    mtu: port?.state?.mtu || "-",
                    loopback_mode: port?.state?.loopback_mode || "-",
                    port_speed: port_speed === "2500MB" ? "2.5GB" : port_speed,
                    in_bits_port_speed_usage: port?.ethernet_state?.in_bits_port_speed_usage || "-",
                    out_bits_port_speed_usage: port?.ethernet_state?.out_bits_port_speed_usage || "-",
                    duplex_mode:
                        port?.ethernet_state?.negotiated_duplex_mode || port?.ethernet_state?.duplex_mode || "-",
                    auto_negotiate: port?.ethernet_state?.auto_negotiate || "-",
                    hw_mac_address: port?.ethernet_state?.hw_mac_address || "-",
                    in_broadcast_pkts: formatNumber(port?.state?.in_broadcast_pkts),
                    in_discards: formatNumber(port?.state?.counters?.in_discards),
                    in_errors: formatNumber(port?.state?.counters?.in_errors),
                    in_fcs_errors: formatNumber(port?.state?.counters?.in_fcs_errors),
                    in_multicast_pkts: formatNumber(port?.state?.counters?.in_multicast_pkts),
                    in_octets: formatNumber(port?.state?.counters?.in_octets),
                    in_pkts: formatNumber(port?.state?.counters?.in_pkts),
                    in_unicast_pkts: formatNumber(port?.state?.counters?.in_unicast_pkts),
                    out_broadcast_pkts: formatNumber(port?.state?.counters?.out_broadcast_pkts),
                    out_discards: formatNumber(port?.state?.counters?.out_discards),
                    out_errors: formatNumber(port?.state?.counters?.out_errors),
                    out_multicast_pkts: formatNumber(port?.state?.counters?.out_multicast_pkts),
                    out_octets: formatNumber(port?.state?.counters?.out_octets),
                    out_pkts: formatNumber(port?.state?.counters?.out_pkts),
                    out_unicast_pkts: formatNumber(port?.state?.counters?.out_unicast_pkts),
                    in_oversize_frames: formatNumber(port?.state?.counters?.in_oversize_frames),
                    in_undersize_frames: formatNumber(port?.state?.counters?.in_undersize_frames),
                    out_bits_rate: formatNumber(port?.ethernet_state?.out_bits_rate),
                    in_bits_rate: formatNumber(port?.ethernet_state?.in_bits_rate),
                    out_pkts_rate: formatNumber(port?.ethernet_state?.out_pkts_rate),
                    in_pkts_rate: formatNumber(port?.ethernet_state?.in_pkts_rate),
                    in_frames_1024_1518_octets: formatNumber(
                        port?.ethernet_state?.counters?.[
                            "openconfig_if_ethernet_ext:in_distribution_in_frames_1024_1518_octets"
                        ]
                    ),
                    in_frames_128_255_octets: formatNumber(
                        port?.ethernet_state?.counters?.[
                            "openconfig_if_ethernet_ext:in_distribution_in_frames_128_255_octets"
                        ]
                    ),
                    in_frames_256_511_octets: formatNumber(
                        port?.ethernet_state?.counters?.[
                            "openconfig_if_ethernet_ext:in_distribution_in_frames_256_511_octets"
                        ]
                    ),
                    in_frames_512_1023_octets: formatNumber(
                        port?.ethernet_state?.counters?.[
                            "openconfig_if_ethernet_ext:in_distribution_in_frames_512_1023_octets"
                        ]
                    ),
                    in_frames_64_octets: formatNumber(
                        port?.ethernet_state?.counters?.[
                            "openconfig_if_ethernet_ext:in_distribution_in_frames_64_octets"
                        ]
                    ),
                    in_frames_65_127_octets: formatNumber(
                        port?.ethernet_state?.counters?.[
                            "openconfig_if_ethernet_ext:in_distribution_in_frames_65_127_octets"
                        ]
                    )
                };
            });
            formattedData.sort((a, b) => compareNumbers(a.name, b.name));
            setFormattedData(formattedData);
        }
    };

    useEffect(() => {
        if (active) {
            fetchData();
        }
    }, [active, sn]);

    return (
        <AmpConCustomTelemteryTable
            showSetting={false}
            showSettingColumn
            // style={{height: "100vh"}}
            columnsConfig={columns}
            data={formattedData}
            tableWidth={tableWidth}
        />
    );
};

const AIOverview = ({active, sn, tableWidth}) => {
    const [formattedData, setFormattedData] = useState([]);

    const columnsConfig = [
        {
            title: "Interface Name",
            dataIndex: "name",
            fixed: "left",
            isString: true,
            sorter: false,
            render: (text, record) => {
                return {
                    children: text,
                    props: {
                        rowSpan: record.rowSpan
                    }
                };
            }
        },
        {
            title: "ECN-Marked-Packets",
            dataIndex: "ecn_marked_packets",
            render: (text, record) => {
                return {
                    children: text,
                    props: {
                        rowSpan: record.rowSpan
                    }
                };
            }
        },
        {
            title: "ECN-Marked-Packets-Rate",
            dataIndex: "ecn_marked_packets_rate",
            render: (text, record) => {
                return {
                    children: text,
                    props: {
                        rowSpan: record.rowSpan
                    }
                };
            }
        },
        {title: "Queue Name", dataIndex: "queue_name", isString: true},
        {title: "PFC-Deadlock-Monitor-Count", dataIndex: "pfc_deadlock_monitor_count"},
        {title: "PFC-Deadlock-Recovery-Count", dataIndex: "pfc_deadlock_recovery_count"},
        {title: "Receive-PFC-Pause-Frames", dataIndex: "receive_pfc_pause_frames"},
        {title: "Receive-PFC-Pause-Frames-Rate", dataIndex: "receive_pfc_pause_frames_rate"},
        {title: "Send-PFC-Pause-Frames", dataIndex: "send_pfc_pause_frames"},
        {title: "Send-PFC-Pause-Frames-Rate", dataIndex: "send_pfc_pause_frames_rate"}
    ];

    const columns = columnsConfig.map(obj => ({
        ...obj,
        sorter: obj.sorter === false ? obj.sorter : getSorter(obj.dataIndex, obj.isString),
        width: obj.width || 120
    }));

    const reformatData = (data, key) => {
        return data
            .reduce((result, item) => {
                if (result.indexOf(item[key]) < 0) {
                    result.push(item[key]);
                }
                return result;
            }, [])
            .reduce((result, value, rownum) => {
                const children = data.filter(item => item[key] === value);
                result = result.concat(
                    children.map((item, index) => ({
                        ...item,
                        rowSpan: index === 0 ? children.length : 0,
                        rownum: rownum + 1
                    }))
                );
                return result;
            }, []);
    };

    const fetchData = async () => {
        const response = await fetchAIInfo(sn);
        if (response.status !== 200) {
            message.error(response.info);
        } else {
            const rawData = response.data.map(item => {
                return {
                    name: item.interface_name || "-",
                    ecn_marked_packets: item?.ecn_marked_packets || "-",
                    ecn_marked_packets_rate: item?.ecn_marked_packets_rate || "-",
                    queue_name: item?.queue_name || "-",
                    pfc_deadlock_monitor_count: item?.pfc_deadlock_monitor_count.toString() || "-",
                    pfc_deadlock_recovery_count: item?.pfc_deadlock_recovery_count.toString() || "-",
                    receive_pfc_pause_frames: item?.receive_pfc_pause_frames.toString() || "-",
                    receive_pfc_pause_frames_rate: item?.receive_pfc_pause_frames_rate.toString() || "-",
                    send_pfc_pause_frames: item?.send_pfc_pause_frames.toString() || "-",
                    send_pfc_pause_frames_rate: item?.send_pfc_pause_frames_rate.toString() || "-"
                };
            });
            const formattedData = reformatData(rawData, "name");
            formattedData.sort((a, b) => compareNumbers(a.name, b.name));
            setFormattedData(formattedData);
        }
    };

    useEffect(() => {
        if (active) {
            fetchData();
        }
    }, [active, sn]);

    return <AmpConCustomTelemteryTable columnsConfig={columns} data={formattedData} tableWidth={tableWidth} />;
};

const ThresholdSetting = ({form, isModalOpen, onCancel, onChange, linkInfo, onDataUpdated}) => {
    const [threshold, setThreshold] = useState(5);
    const [type, setType] = useState("all");
    const [opticalLinksData, setOpticalLinksData] = useState([]);
    const [opticalLinksTargetKeys, setOpticalLinksTargetKeys] = useState([]);

    const [updateLinksRuleSettingForm] = Form.useForm();
    const handleOpticalLinksChange = newTargetKeys => {
        setOpticalLinksTargetKeys(newTargetKeys);
        const selectedValues = newTargetKeys.map(key => key);
        updateLinksRuleSettingForm.setFieldsValue({linksOpticalLinks: selectedValues});
    };
    const linksAlertAllThreshold = linkInfo || [];
    const abbreviateLinkName = threshold => {
        const maxLength = 12; // 最大显示长度
        const name = `${threshold.source_sn}_${threshold.source_port}-${threshold.target_sn}_${threshold.target_port}`;
        // 如果名称长度小于等于最大长度，直接返回
        if (name.length <= maxLength) return name;
        // 截断并添加省略号
        return `${name.substring(0, maxLength - 3)}`;
    };
    const getAlertThresholdData = () => {
        // const tempAlertThresholdTargetKeys = [];
        const tempAlertThresholdData = linksAlertAllThreshold.map(threshold => ({
            key: threshold.link_id,
            fullName: `${threshold.source_sn}_${threshold.source_port}-${threshold.target_sn}_${threshold.target_port}`,
            thresholdValue: threshold.threshold,
            title:
                threshold.threshold !== null
                    ? `${abbreviateLinkName(threshold)}...  ${threshold.threshold}dB`
                    : abbreviateLinkName(threshold)
        }));
        const targetKeys = tempAlertThresholdData
            .filter(item => item.thresholdValue === threshold)
            .map(item => item.key);
        setOpticalLinksData(tempAlertThresholdData);
        setOpticalLinksTargetKeys(targetKeys);
        // setOpticalLinksTargetKeys(tempAlertThresholdTargetKeys);
    };
    const updateSelectedKeys = () => {
        const prevSelectedKeys = [...opticalLinksTargetKeys];
        const newSelectedKeys = prevSelectedKeys.filter(key => opticalLinksData.some(item => item.key === key));
        setOpticalLinksTargetKeys(newSelectedKeys);
    };
    useEffect(() => {
        if (isModalOpen) {
            getAlertThresholdData();

            // 如果父组件提供了数据更新回调，调用它
            if (onDataUpdated) {
                onDataUpdated(updateSelectedKeys);
            }
        }
    }, [isModalOpen, threshold, opticalLinksTargetKeys]);
    const formItems = () => {
        return (
            <>
                <Form.Item
                    name="threshold"
                    label={
                        <Tooltip title="If the optical attenuation exceeds the set threshold, it means that the link is abnormal.">
                            <span style={{marginRight: "10px"}}>Light Attenuation Threshold (dB)</span>
                            <QuestionCircleOutlined />
                        </Tooltip>
                    }
                    initialValue={5}
                    className={styles.formItem}
                    rules={[
                        {
                            required: true,
                            message: "Please enter a valid threshold."
                        }
                    ]}
                >
                    <InputNumber
                        value={threshold}
                        onChange={setThreshold}
                        placeholder="threshold"
                        disabled={!linkInfo}
                        allowClear
                        style={{width: 280, height: "32px", marginBottom: "10px"}}
                    />
                </Form.Item>
                <Form.Item name="apply" label="Apply">
                    <Radio.Group
                        value={type}
                        onChange={e => setType(e.target.value)}
                        style={{display: "inline-flex", alignItems: "center", height: 32}}
                    >
                        <Radio value="all">All</Radio>
                        <Radio value="specific">Specific</Radio>
                    </Radio.Group>
                    <Transfer
                        showSearch
                        dataSource={opticalLinksData}
                        targetKeys={opticalLinksTargetKeys}
                        onChange={handleOpticalLinksChange}
                        style={{display: type === "all" ? "none" : "", marginTop: 24}}
                        render={item => (
                            <Tooltip
                                title={
                                    item.thresholdValue !== null
                                        ? `${item.fullName} ${item.thresholdValue}dB`
                                        : item.fullName
                                }
                            >
                                <span style={{whiteSpace: "pre"}}>{item.title}</span>
                            </Tooltip>
                        )}
                        listStyle={{
                            height: 246
                        }}
                    />
                </Form.Item>
            </>
        );
    };
    return (
        <AmpConCustomModalForm
            title="Threshold"
            isModalOpen={isModalOpen}
            formInstance={form}
            layoutProps={{
                labelCol: {
                    span: 6
                }
            }}
            CustomFormItems={formItems}
            onCancel={onCancel}
            onSubmit={() => onChange(threshold, type, opticalLinksTargetKeys)}
            modalClass="ampcon-middle-modal"
            footer={
                <Flex vertical>
                    <Divider style={{marginTop: 0, marginBottom: 20}} />
                    <Flex justify="flex-end">
                        <Button onClick={onCancel}>Cancel</Button>
                        <Button type="primary" onClick={() => onChange(threshold, type, opticalLinksTargetKeys)}>
                            Apply
                        </Button>
                    </Flex>
                </Flex>
            }
        />
    );
};
const ModulesOverview = ({active, sn, tableWidth, model, sysname, ip_address, showHealthStatus}) => {
    const [form] = useForm();

    const [formattedData, setFormattedData] = useState([]);
    const [activeTab, setActiveTab] = useState(showHealthStatus ? "health_status" : "module_statistics");
    const [activeKey, setActiveKey] = useState("Statistics");
    const [timeRange, setTimeRange] = useState(["", ""]);
    const [updateSelectedCallback, setUpdateSelectedCallback] = useState(null);
    const [counters, setCounters] = useState({
        links: Object.values(modulesLinkCheckboxOptions),
        modules: Object.values(modulesStatusCheckboxOptions)
    });

    const cardRefs = useRef({});
    const addToRefs = (el, name) => {
        if (el) {
            cardRefs.current[name] = el;
        }
    };

    const fetchPorts = async () => {
        const response = await fetchInterfaceInfo(sn);
        if (response.status !== 200) {
            message.error(response.info);
        } else {
            const interfaces = Object.keys(response.data);
            interfaces.sort((a, b) => compareNumbers(a, b));
            setPortLists(interfaces);
        }
    };

    const fetchDataPort = async (label, target, timeRange, selectedChannel, type, port) => {
        let highMajor, highWarning, lowMajor, lowWarning;
        let chartData = [];
        let xAxisData = [];
        let channelIndex;
        let response;
        if (type === "modules") {
            const name = modulesStatusCheckboxOptions[label];
            if (label === "Temperature (℃)" || label === "Voltage (V)") {
                response = await fetchModulesPortStatus(name, target, port, timeRange[0], timeRange[1]);
            } else {
                response = await fetchModulesPortStatus(
                    name,
                    target,
                    port,
                    timeRange[0],
                    timeRange[1],
                    selectedChannel
                );
            }

            const {channel_index, interface_name, value, threshold} = response.data;
            channelIndex = channel_index;
            xAxisData = value.map(([date]) => date);
            chartData = [
                {
                    name: interface_name,
                    data: value.map(([, val]) => val)
                }
            ];
            const {high_major, high_warning, low_major, low_warning} = threshold;
            highMajor = high_major;
            highWarning = high_warning;
            lowMajor = low_major;
            lowWarning = low_warning;
        }

        return {
            channelIndex,
            highMajor,
            lowMajor,
            highWarning,
            lowWarning,
            chartData,
            xAxisData
        };
    };

    const fetchLinks = async () => {
        const response = await fetchLinkInfo(sn);
        if (response.status !== 200) {
            message.error(response.info);
        } else {
            const links = [];
            setLinkInfo(Object.values(response.data).flat());
            for (const key in response.data) {
                if (Object.prototype.hasOwnProperty.call(response.data, key)) {
                    response.data[key].map(link => {
                        links.push(`${link.source_sn}_${link.source_port}-${link.target_sn}_${link.target_port}`);
                    });
                }
            }
            links.sort((a, b) => compareNumbers(a, b));
            setLinkLists(links);
        }
    };

    const fetchDataLink = async (label, finalTopK, target, portInfo, timeRange, _, type) => {
        let channelIndex = [];
        let chartData = [];
        let xAxisData = [];
        let response;
        if (type === "links" && portInfo) {
            response = await fetchModulesLinkStatus(timeRange[0], timeRange[1], portInfo);
            if (response.status === 200) {
                channelIndex = response.data.map(item => item.channel_index);

                chartData = response.data.map(item => {
                    const name = `${item.interface_name}`;
                    return {
                        name,
                        data: item.values.map(([x, y]) => [x, y])
                    };
                });
                xAxisData = Array.from(new Set(response.data.flatMap(item => item.values.map(([x]) => x)))).sort();
            } else {
                message.error(response.info);
            }
        }

        return {
            channelIndex,
            chartData,
            xAxisData
        };
    };

    const [portLists, setPortLists] = useState([]);
    const [selectedPort, setSelectedPort] = useState();
    const [linkLists, setLinkLists] = useState([]);
    const [selectedLink, setSelectedLink] = useState();
    const [linkInfo, setLinkInfo] = useState();
    const [portInfo, setPortInfo] = useState();
    const [isThresholdModalOpen, setIsThresholdModalOpen] = useState(false);
    const [isHovered, setIsHovered] = useState(false);
    const channelLists = ["channel 1", "channel 2", "channel 3", "channel 4"];

    const handleSelectLink = value => {
        const foundLink = linkInfo.find(
            link => `${link.source_sn}_${link.source_port}-${link.target_sn}_${link.target_port}` === value
        );
        if (foundLink !== undefined) {
            const portInfo = [foundLink];
            setPortInfo(portInfo);
        } else setPortInfo();
    };

    const allCards = useMemo(
        () => [
            counters.modules.length > 0 &&
                counters.modules.map(modulesItem => (
                    <CustomTelemetryLimitCard
                        label={Object.keys(modulesStatusCheckboxOptions).find(
                            key => modulesStatusCheckboxOptions[key] === modulesItem
                        )}
                        type="modules"
                        timeRange={timeRange}
                        fetchApi={fetchDataPort}
                        ref={el => addToRefs(el, modulesItem)}
                        target={sn}
                        port={selectedPort}
                        cardstyle={{
                            borderColor: "#E7E7E7",
                            borderWidth: "1px",
                            borderStyle: "solid",
                            boxShadow: "none"
                        }}
                        showFilter={
                            !["Temperature (℃)", "Voltage (V)"].includes(
                                Object.keys(modulesStatusCheckboxOptions).find(
                                    key => modulesStatusCheckboxOptions[key] === modulesItem
                                )
                            )
                        }
                        filterList={channelLists}
                    />
                ))
        ],
        [counters, timeRange, sn, selectedPort]
    );

    const linkCards = useMemo(
        () => [
            counters.links.length > 0 &&
                counters.links.map(linksItem => (
                    <CustomTelemetryLinkCard
                        title="Light Attenuation"
                        label={selectedLink || "Link"}
                        type="links"
                        timeRange={timeRange}
                        fetchApi={fetchDataLink}
                        ref={el => addToRefs(el, linksItem)}
                        target={sn}
                        portInfo={portInfo}
                        cardstyle={{
                            borderColor: "#E7E7E7",
                            borderWidth: "1px",
                            borderStyle: "solid",
                            boxShadow: "none"
                        }}
                        showFilter={false}
                        showTopK={false}
                    />
                ))
        ],
        [counters, timeRange, sn, portInfo]
    );

    const columnsConfig = [
        {title: "Port Name", dataIndex: "name", fixed: "left", isString: true, sorter: false},
        {title: "Connector Type", dataIndex: "connector_type", isString: true, width: 270},
        {title: "Identifier", dataIndex: "form_factor", isString: true},
        {title: "Vendor Name", dataIndex: "vendor", isString: true, width: 140},
        {title: "Vendor Part", dataIndex: "vendor_part", isString: true, width: 190},

        {
            title: "Transmission Distance",
            dataIndex: "transmission_distance",
            isString: true,
            width: 270,
            render: text => <div style={{whiteSpace: "pre-line"}}>{text}</div>
        },
        {title: "Transmission Rate", dataIndex: "transmission_rate", isString: true, width: 170},
        {title: "WaveLength (nm)", dataIndex: "wavelength", width: 160},
        {
            title: "Rx Power (dBm)",
            dataIndex: "input_power",
            width: 150,
            render: (text, record) => {
                const values = text.toString().split(",");
                return (
                    <div style={{whiteSpace: "pre-line"}}>
                        {values.map((value, index) => (
                            <span key={index}>
                                {value}
                                {index < values.length - 1 && <br />}
                            </span>
                        ))}
                    </div>
                );
            }
        },
        {
            title: "Tx Power (dBm)",
            dataIndex: "output_power",
            width: 150,
            render: (text, record) => {
                const values = text.toString().split(",");
                return (
                    <div style={{whiteSpace: "pre-line"}}>
                        {values.map((value, index) => (
                            <span key={index}>
                                {value}
                                {index < values.length - 1 && <br />}
                            </span>
                        ))}
                    </div>
                );
            }
        },
        // {title: "Power Budget (dB)", dataIndex: "attenuation", width: 170},
        {title: "Temperature (℃)", dataIndex: "temperature", width: 160}
    ];

    const columns = columnsConfig.map(obj => ({
        ...obj,
        sorter: obj.sorter === false ? obj.sorter : getSorter(obj.dataIndex, obj.isString),
        width: obj.width || 120
    }));

    const openConfig_SFP_typeMap = {
        SFP: "SFP",
        SFP_PLUS: "SFP+",
        SFP28: "SFP28",
        SFP56: "SFP56",
        QSFP: "QSFP",
        QSFP_PLUS: "QSFP+",
        QSFP28: "QSFP28",
        QSFP56: "QSFP56",
        QSFP56_DD: "QSFP-DD",
        QSFP56_DD_TYPE2: "QSFP112"
    };

    const fetchData = async () => {
        const response = await fetchModulesInfo(sn);
        if (response.status !== 200) {
            message.error(response.info);
        } else {
            const formattedData = Object.keys(response.data).map(key => {
                const port = response.data[key];
                const formFactor = port?.transceiver_state?.form_factor?.split(":")[1] || "-";
                return {
                    name: key,
                    connector_type: port?.transceiver_state?.connector_type?.split(":")[1] || "-",
                    form_factor: openConfig_SFP_typeMap[formFactor] || formFactor,
                    vendor: port?.transceiver_state?.vendor || "-",
                    vendor_part: port?.transceiver_state?.vendor_part || "-",
                    transmission_distance:
                        port?.transceiver_state?.fsconfig_platform_transceiver_extensions_transmission_distance
                            ?.split(",")
                            .join("\n") || "-",
                    transmission_rate:
                        port?.transceiver_state?.fsconfig_platform_transceiver_extensions_transmission_rate || "-",
                    wavelength: port?.transceiver_state?.fsconfig_platform_transceiver_extensions_wavelength || 0,
                    input_power: port?.transceiver_state?.input_power || 0,
                    output_power: port?.transceiver_state?.output_power || 0,
                    temperature: port?.transceiver_state?.temperature || 0,
                    attenuation: port?.transceiver_state?.attenuation || 0
                };
            });
            formattedData.sort((a, b) => compareNumbers(a.name, b.name));
            setFormattedData(formattedData);
        }
    };

    useEffect(() => {
        if (selectedLink) {
            handleSelectLink(selectedLink);
        }
    }, [selectedLink, linkInfo]);

    useEffect(() => {
        if (!sn) {
            return;
        }
        if (active) {
            fetchData();
            fetchPorts();
            fetchLinks();
        }
    }, [active, sn]);

    const items = [
        {
            key: "Statistics",
            label: "Statistics",
            children: (
                <HealthStatus
                    model={model}
                    sn={sn}
                    tableWidth={tableWidth}
                    ip_address={ip_address}
                    sysname={sysname}
                    showHealthStatus={showHealthStatus}
                    setActiveKey={setActiveKey}
                    setSelectedPort={setSelectedPort}
                    active={active}
                    activeTab={activeTab}
                    activeKey={activeKey}
                />
            )
        },
        {
            key: "Visualization",
            label: "Visualization",
            children: (
                <>
                    <div style={{display: "flex", justifyContent: "flex-end"}}>
                        <div style={{display: "flex", alignItems: "center", justifyContent: "center"}}>Port</div>
                        <Select
                            style={{width: 280, margin: "0 32px"}}
                            placeholder="Select Port"
                            value={selectedPort}
                            onChange={value => {
                                setSelectedPort(value);
                            }}
                            allowClear
                            showSearch={false}
                        >
                            {portLists.map(port => (
                                <Option key={port} value={port}>
                                    {port}
                                </Option>
                            ))}
                        </Select>
                        <div style={{display: "flex", alignItems: "center", justifyContent: "center"}}>Time</div>
                        <RangePicker
                            showTime={{format: "HH:mm"}}
                            format="YYYY-MM-DD HH:mm"
                            style={{height: "32px", marginLeft: "32px", marginBottom: "0px"}}
                            onChange={(_, dateString) => {
                                setTimeRange(dateString);
                            }}
                            disabledDate={current => {
                                const now = new Date();
                                now.setHours(23);
                                now.setMinutes(59);
                                const oneMonthAgo = new Date();
                                oneMonthAgo.setDate(now.getDate() - 30);
                                oneMonthAgo.setHours(23);
                                return current && (current > now || current < oneMonthAgo);
                            }}
                            disabledTime={current => {
                                const now = new Date();
                                const currentMonth = now.getMonth();
                                const currentDay = now.getDate();
                                const currentHours = now.getHours();
                                const currentMinutes = now.getMinutes();

                                const targetDate = new Date(current);
                                const targetMonth = targetDate.getMonth();
                                const targetDay = targetDate.getDate();
                                const targetHours = targetDate.getHours();
                                const disabledTimeLists = {};
                                if (!current || (targetMonth === currentMonth && targetDay === currentDay)) {
                                    disabledTimeLists.disabledHours = () =>
                                        Array.from(
                                            {length: 24 - currentHours + 1},
                                            (_, hour) => hour + currentHours + 1
                                        );
                                    disabledTimeLists.disabledMinutes = () =>
                                        !current || targetHours >= currentHours
                                            ? Array.from(
                                                  {length: 60 - currentMinutes + 1},
                                                  (_, minute) => minute + currentMinutes + 1
                                              )
                                            : [];
                                }
                                return disabledTimeLists;
                            }}
                        />
                    </div>
                    {sn ? (
                        <div style={{height: "100%", width: "100%", marginTop: "18px", marginBottom: "18px"}}>
                            <Row gutter={[24, 24]}>
                                {allCards.flat().map((card, index) => (
                                    <Col
                                        key={index}
                                        span={24}
                                        xxl={12}
                                        style={{display: "flex", justifyContent: "center"}}
                                    >
                                        {card}
                                    </Col>
                                ))}
                            </Row>
                        </div>
                    ) : null}
                </>
            )
        }
    ];
    const [searchValue, setSearchValue] = useState("");
    const [filteredLinkLists, setFilteredLinkLists] = useState(linkLists);

    useEffect(() => {
        setFilteredLinkLists(linkLists);
    }, [linkLists]);

    const handleSearch = value => {
        setSearchValue(value);
        const filtered = linkLists.filter(option => option.toLowerCase().includes(value.toLowerCase()));
        setFilteredLinkLists(filtered);
    };
    const dropDownRender = menu => {
        return (
            <div>
                <div>
                    <Input
                        value={searchValue}
                        onChange={e => handleSearch(e.target.value)}
                        placeholder="Search"
                        prefix={<Icon component={searchSvg} />}
                        allowClear
                        style={{width: "100%", height: "32px", marginBottom: "3px"}}
                    />
                </div>
                {menu}
            </div>
        );
    };
    const tabItems = [
        {
            key: "module_statistics",
            label: "Module Statistics",
            children: (
                <AmpConCustomTelemteryTable
                    showSettingColumn
                    showSetting={false}
                    columnsConfig={columns}
                    data={formattedData}
                    tableWidth={tableWidth}
                />
            )
        },
        {
            key: "health_status",
            label: <span>Health Status</span>,
            children: (
                <div className="customLineTabs">
                    <Tabs activeKey={activeKey} onChange={setActiveKey} items={items} />
                </div>
            )
        },
        {
            key: "fault_analysis",
            label: <span>Fault Analysis</span>,
            children: (
                <>
                    <div style={{display: "flex", justifyContent: "flex-end"}}>
                        <div style={{display: "flex", alignItems: "center", justifyContent: "center"}}>
                            Optical Filter Link
                        </div>
                        <Select
                            style={{width: 280, margin: "0 32px"}}
                            placeholder="Select Link"
                            dropdownStyle={{minWidth: "fit-content"}}
                            value={selectedLink}
                            onChange={value => setSelectedLink(value)}
                            allowClear
                            showSearch={false}
                            dropdownRender={menu => dropDownRender(menu)}
                        >
                            {filteredLinkLists.map(link => (
                                <Option key={link} value={link}>
                                    {link}
                                </Option>
                            ))}
                        </Select>
                        <div style={{display: "flex", alignItems: "center", justifyContent: "center"}}>Time</div>
                        <RangePicker
                            showTime={{format: "HH:mm"}}
                            format="YYYY-MM-DD HH:mm"
                            style={{height: "32px", marginLeft: "32px", marginBottom: "0px"}}
                            onChange={(_, dateString) => {
                                setTimeRange(dateString);
                            }}
                            disabledDate={current => {
                                const now = new Date();
                                now.setHours(23);
                                now.setMinutes(59);
                                const oneMonthAgo = new Date();
                                oneMonthAgo.setDate(now.getDate() - 30);
                                oneMonthAgo.setHours(23);
                                return current && (current > now || current < oneMonthAgo);
                            }}
                            disabledTime={current => {
                                const now = new Date();
                                const currentMonth = now.getMonth();
                                const currentDay = now.getDate();
                                const currentHours = now.getHours();
                                const currentMinutes = now.getMinutes();

                                const targetDate = new Date(current);
                                const targetMonth = targetDate.getMonth();
                                const targetDay = targetDate.getDate();
                                const targetHours = targetDate.getHours();
                                const disabledTimeLists = {};
                                if (!current || (targetMonth === currentMonth && targetDay === currentDay)) {
                                    disabledTimeLists.disabledHours = () =>
                                        Array.from(
                                            {length: 24 - currentHours + 1},
                                            (_, hour) => hour + currentHours + 1
                                        );
                                    disabledTimeLists.disabledMinutes = () =>
                                        !current || targetHours >= currentHours
                                            ? Array.from(
                                                  {length: 60 - currentMinutes + 1},
                                                  (_, minute) => minute + currentMinutes + 1
                                              )
                                            : [];
                                }
                                return disabledTimeLists;
                            }}
                        />
                        <Divider type="vertical" style={{height: "30px", marginLeft: "16px", marginRight: "16px"}} />
                        <Button
                            style={{borderColor: isHovered ? "#34DCCF" : "#d9d9d9"}}
                            icon={<Icon component={isHovered ? settingGreenSvg : settingGreySvg} />}
                            onClick={() => setIsThresholdModalOpen(true)}
                            onMouseEnter={() => {
                                setIsHovered(true);
                            }}
                            onMouseLeave={() => {
                                setIsHovered(false);
                            }}
                        />
                    </div>
                    <ThresholdSetting
                        form={form}
                        linkInfo={linkInfo}
                        isModalOpen={isThresholdModalOpen}
                        onCancel={() => {
                            setIsThresholdModalOpen(false);
                        }}
                        onDataUpdated={callback => setUpdateSelectedCallback(callback)}
                        onChange={async (threshold, apply_type, links_selected) => {
                            setIsThresholdModalOpen(false);
                            const response = await updateLinksThreshold(apply_type, sn, threshold, links_selected);
                            await fetchLinks();
                            if (response.status !== 200) {
                                message.error(response.info);
                            } else {
                                message.success("Threshold updated successfully!");
                                await fetchLinks();
                                if (updateSelectedCallback) {
                                    updateSelectedCallback();
                                }
                            }
                        }}
                    />
                    {sn ? (
                        <div style={{height: "100%", width: "100%", marginTop: "18px", marginBottom: "18px"}}>
                            <Row gutter={[24, 24]}>
                                {linkCards.flat().map((card, index) => (
                                    <Col
                                        key={index}
                                        span={24}
                                        xxl={24}
                                        style={{display: "flex", justifyContent: "center"}}
                                    >
                                        {card}
                                    </Col>
                                ))}
                            </Row>
                        </div>
                    ) : null}
                </>
            )
        }
    ];

    return (
        <Tabs
            className="radioGroupTabs customTab"
            tabBarStyle={{marginBottom: "24px"}}
            defaultActiveKey="module_statistics"
            activeKey={activeTab}
            onChange={setActiveTab}
            items={tabItems}
        />
    );
};

const MacTableOverview = ({active, sn, tableWidth}) => {
    const [formattedData, setFormattedData] = useState([]);

    const columnsConfig = [
        {title: "MAC Address", dataIndex: "mac", fixed: "left", isString: true},
        {title: "Interface", dataIndex: "interface", isString: true},
        {title: "Vlan", dataIndex: "vlan", isString: true},
        {title: "Age (s)", dataIndex: "age", isString: true},
        {title: "Type", dataIndex: "type", isString: true}
    ];

    const columns = columnsConfig.map(obj => ({
        ...obj,
        sorter: obj.sorter === false ? obj.sorter : getSorter(obj.dataIndex, obj.isString),
        width: obj.width || 120
    }));

    const fetchData = async () => {
        const response = await fetchMacTable(sn);
        if (response.status !== 200) {
            message.error(response.info);
        } else {
            const formattedData = response.data.map(item => {
                return {
                    name: item.mac_address,
                    mac: item.mac_address,
                    interface: item.interface,
                    vlan: item.entry_vlan,
                    age: item.age,
                    type: item.entry_type
                };
            });
            formattedData.sort((a, b) => compareNumbers(a.name, b.name));
            setFormattedData(formattedData);
        }
    };

    useEffect(() => {
        if (active) {
            fetchData();
        }
    }, [active, sn]);

    return (
        <AmpConCustomTelemteryTable
            columnsConfig={columns}
            data={formattedData}
            tableWidth={tableWidth}
            showSetting={false}
        />
    );
};

const ArpTableOverview = ({active, sn, tableWidth}) => {
    const [formattedData, setFormattedData] = useState([]);

    const columnsConfig = [
        {title: "IP Address", dataIndex: "ip", fixed: "left", isString: true},
        {title: "HW Address", dataIndex: "hw_address", isString: true},
        {title: "Type", dataIndex: "type", isString: true},
        {title: "Interface", dataIndex: "interface", isString: true}
    ];

    const columns = columnsConfig.map(obj => ({
        ...obj,
        sorter: obj.sorter === false ? obj.sorter : getSorter(obj.dataIndex, obj.isString),
        width: obj.width || 120
    }));

    const fetchData = async () => {
        const response = await fetchArpTable(sn);
        if (response.status !== 200) {
            message.error(response.info);
        } else {
            const formattedData = response.data.map(item => {
                return {
                    name: item.ip,
                    ip: item.ip,
                    interface: item.interface_name,
                    hw_address: item.link_layer_address,
                    type: item.origin
                };
            });
            formattedData.sort((a, b) => compareNumbers(a.name, b.name));
            setFormattedData(formattedData);
        }
    };

    useEffect(() => {
        if (active) {
            fetchData();
        }
    }, [active, sn]);

    return (
        <AmpConCustomTelemteryTable
            columnsConfig={columns}
            data={formattedData}
            tableWidth={tableWidth}
            showSetting={false}
        />
    );
};

const BGPTableOverview = ({active, sn, tableWidth}) => {
    const [formattedData, setFormattedData] = useState([]);

    const columnsConfig = [
        {title: "VRF Name", dataIndex: "name", fixed: "left", isString: true},
        {title: "BGP Version", dataIndex: "version", width: 110},
        {title: "Local AS", dataIndex: "loacl_as", width: 90},
        {title: "Local Router ID", dataIndex: "local_router_id", isString: true, width: 130},
        {title: "Remote AS", dataIndex: "remote_as", width: 100},
        {title: "Remote Router ID", dataIndex: "remote_router_id", isString: true, width: 140},
        {title: "BGP State", dataIndex: "bgp_state", isString: true},
        {title: "Hold Time (s)", dataIndex: "hold_time", width: 120},
        {title: "Keepalive Interval (s)", dataIndex: "keepalive_interval", width: 150}
    ];

    const columns = columnsConfig.map(obj => ({
        ...obj,
        sorter: obj.sorter === false ? obj.sorter : getSorter(obj.dataIndex, obj.isString),
        width: obj.width || 100
    }));

    const fetchData = async () => {
        const response = await fetchBGPTable(sn);
        if (response.status !== 200) {
            message.error(response.info);
        } else {
            const formattedData = response.data.map(item => {
                return {
                    name: item.protocol_name,
                    version: parseInt(item.description, 10),
                    loacl_as: parseInt(item.local_as, 10),
                    local_router_id: item.local_address,
                    remote_as: parseInt(item.peer_as, 10),
                    remote_router_id: item.remote_address,
                    bgp_state: item.session_state,
                    hold_time: parseInt(item.hold_time, 10),
                    keepalive_interval: parseInt(item.keepalive_interval, 10)
                };
            });
            formattedData.sort((a, b) => compareNumbers(a.name, b.name));
            setFormattedData(formattedData);
        }
    };

    useEffect(() => {
        if (active) {
            fetchData();
        }
    }, [active, sn]);

    return (
        <AmpConCustomTelemteryTable
            columnsConfig={columns}
            data={formattedData}
            tableWidth={tableWidth}
            showSetting={false}
        />
    );
};

const OSPFTableOverview = ({active, sn, tableWidth}) => {
    const [formattedData, setFormattedData] = useState([]);

    const columnsConfig = [
        {title: "VRF Name", dataIndex: "name", fixed: "left", isString: true},
        {title: "Neighbor ID", dataIndex: "neighbor_id", isString: true, width: 130},
        {title: "Pri", dataIndex: "pri", isString: true, width: 80},
        {title: "State", dataIndex: "state", isString: true, width: 80},
        {title: "Dead Time", dataIndex: "dead_time", isString: true, width: 160},
        {title: "Address", dataIndex: "address", isString: true},
        {title: "Interface", dataIndex: "interface", isString: true, width: 140},
        {title: "RXmtl", dataIndex: "rxmtl", width: 90},
        {title: "Rqstl", dataIndex: "rqstl", width: 90},
        {title: "DBsml", dataIndex: "dbsml", width: 90},
        {title: "DR", dataIndex: "dr", isString: true},
        {title: "BDR", dataIndex: "bdr", isString: true}
    ];

    const columns = columnsConfig.map(obj => ({
        ...obj,
        sorter: obj.sorter === false ? obj.sorter : getSorter(obj.dataIndex, obj.isString),
        width: obj.width || 120
    }));

    const formatDateTime = timestampString => {
        const timestamp = parseInt(timestampString, 10);
        const milliseconds = Math.floor(timestamp / 1000000); // 将纳秒转换为毫秒
        const date = new Date(milliseconds);

        const formattedDateTime = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, "0")}-${String(date.getDate()).padStart(2, "0")} ${String(date.getHours()).padStart(2, "0")}:${String(date.getMinutes()).padStart(2, "0")}:${String(date.getSeconds()).padStart(2, "0")}`;
        return formattedDateTime;
    };

    const fetchData = async () => {
        const response = await fetchOSPFTable(sn);
        if (response.status !== 200) {
            message.error(response.info);
        } else {
            const formattedData = response.data.map(item => {
                return {
                    name: item.protocol_name,
                    neighbor_id: item.neighbor_router_id,
                    pri: parseInt(item.priority, 10),
                    state: item.adjacency_state.split(":")[1].trim(),
                    dead_time: formatDateTime(item.dead_time),
                    address: item.fsconfig_ospfv2_extensions_neighbor_address,
                    interface: `${item.interface_id}:${item.backup_designated_router}`,
                    rxmtl: parseInt(item.fsconfig_ospfv2_extensions_link_state_retransmission_list, 10),
                    rqstl: parseInt(item.fsconfig_ospfv2_extensions_link_state_request_list, 10),
                    dbsml: parseInt(item.fsconfig_ospfv2_extensions_database_summary_list, 10),
                    dr: item.designated_router,
                    bdr: item.backup_designated_router
                };
            });
            formattedData.sort((a, b) => compareNumbers(a.name, b.name));
            setFormattedData(formattedData);
        }
    };

    useEffect(() => {
        if (active) {
            fetchData();
        }
    }, [active, sn]);

    return (
        <AmpConCustomTelemteryTable
            columnsConfig={columns}
            data={formattedData}
            tableWidth={tableWidth}
            showSetting={false}
        />
    );
};

const RouteTableOverview = ({active, sn, tableWidth}) => {
    const [formattedData, setFormattedData] = useState([]);

    const columnsConfig = [
        {title: "Type", dataIndex: "protocol", isString: true},
        {title: "Destination Network", dataIndex: "destination", fixed: "left", isString: true},
        {
            title: "Route Metrics",
            dataIndex: "metric",
            width: 120,
            render: (_, record) => `[${record.route_preference}/${record.metric}]`
        },
        {
            title: "Next Hop",
            dataIndex: "next_hops",
            isString: true,
            render: next_hops => (
                <div>
                    {next_hops.map((hop, index) => (
                        <div key={index}>{hop.ip_address}</div>
                    ))}
                </div>
            )
        },
        {
            title: "Outgoing Interface",
            dataIndex: "next_hops",
            isString: true,
            render: next_hops => (
                <div>
                    {next_hops.map((hop, index) => (
                        <div key={index}>{hop.outgoing_interface}</div>
                    ))}
                </div>
            )
        },
        {title: "Route Age", dataIndex: "uptime", isString: true, width: 100}
    ];

    const columns = columnsConfig.map(obj => ({
        ...obj,
        sorter: obj.sorter === false ? obj.sorter : getSorter(obj.dataIndex, obj.isString),
        width: obj.width || 120
    }));

    const fetchData = async () => {
        const response = await fetchRouteTable(sn);
        if (response.status !== 200) {
            message.error(response.info);
        } else {
            setFormattedData(response.data);
        }
    };

    useEffect(() => {
        if (active) {
            fetchData();
        }
    }, [active, sn]);

    return (
        <AmpConCustomTelemteryTable
            columnsConfig={columns}
            data={formattedData}
            tableWidth={tableWidth}
            showSetting={false}
        />
    );
};
const MLAGTableOverview = ({active, sn, tableWidth}) => {
    const [formattedData, setFormattedData] = useState([]);

    const columnsConfig = [
        {title: "Domain ID", dataIndex: "domain_id", width: 90},
        {title: "Domain MAC", dataIndex: "domain_mac"},
        {title: "Node ID", dataIndex: "node_id", width: 90},
        {title: "Peer Link", dataIndex: "peer_link", width: 90},
        {title: "Peer IP", dataIndex: "peer_ip", width: 120},
        {title: "Peer VLAN", dataIndex: "peer_vlan", width: 90},
        {title: "Neighbor Status", dataIndex: "neighbor_status", width: 150},
        {title: "Config Matched", dataIndex: "config_matched", width: 150},
        {title: "MAC Synced", dataIndex: "mac_synced", width: 120},
        {title: "MLAG Link Count", dataIndex: "mlag_link_count", width: 150}
    ];

    const columns = columnsConfig.map(obj => ({
        ...obj,
        sorter: obj.sorter === false ? obj.sorter : getSorter(obj.dataIndex, obj.isString),
        width: obj.width || 120
    }));

    const fetchData = async () => {
        try {
            const response = await fetchMlagTable(sn);
            if (response.status !== 200) {
                message.error("Failed to fetch MLAG data");
            } else {
                const formattedData = response.data.map(item => ({
                    domain_id: item.domain_id,
                    domain_mac: item.domain_mac,
                    node_id: item.node_id,
                    peer_link: item.peer_link,
                    peer_ip: item.peer_ip,
                    peer_vlan: item.peer_vlan,
                    neighbor_status: item.neighbor_status,
                    config_matched: item.config_matched,
                    mac_synced: item.mac_synced,
                    mlag_link_count: item.num_of_links
                }));
                setFormattedData(formattedData);
            }
        } catch (error) {
            message.error("Error fetching MLAG data");
            console.error("Fetch error:", error);
        }
    };
    useEffect(() => {
        if (active) {
            fetchData();
            const interval = setInterval(fetchData, 30000);
            return () => clearInterval(interval);
        }
    }, [active, sn]);

    return (
        <AmpConCustomTelemteryTable
            columnsConfig={columns}
            data={formattedData}
            tableWidth={tableWidth}
            showSetting={false}
        />
    );
};
const POETableOverview = ({active, sn, tableWidth}) => {
    const [formattedData, setFormattedData] = useState([]);
    const columnsConfig = [
        {title: "Port", dataIndex: "interface_name", width: 150},
        {title: "Status", dataIndex: "enabled", width: 100},
        {title: "Consume", dataIndex: "power_used", width: 120},
        {title: "PD-Class", dataIndex: "power_class", width: 120}
    ];

    const columns = columnsConfig.map(obj => ({
        ...obj,
        sorter: obj.sorter === false ? obj.sorter : getSorter(obj.dataIndex),
        width: obj.width || 120
    }));

    const fetchData = async () => {
        try {
            const response = await fetchPoeTable(sn);
            if (response.status !== 200) {
                message.error("Failed to fetch PoE data");
            } else {
                const formattedData = response.data.map(item => ({
                    interface_name: item.interface_name,
                    enabled: item.enabled ? "True" : "False",
                    power_used: `${item.power_used} W`,
                    power_class: item.power_class
                }));
                setFormattedData(formattedData);
            }
        } catch (error) {
            message.error("Error fetching PoE data");
            console.error("Fetch error:", error);
        }
    };
    useEffect(() => {
        if (active) {
            fetchData();
            const interval = setInterval(fetchData, 30000);
            return () => clearInterval(interval);
        }
    }, [active, sn]);

    return (
        <AmpConCustomTelemteryTable
            columnsConfig={columns}
            data={formattedData}
            tableWidth={tableWidth}
            showSetting={false}
        />
    );
};
export default SwitchTelemetry;
