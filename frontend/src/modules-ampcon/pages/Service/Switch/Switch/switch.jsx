import {Card} from "antd";
import {useRef, useState, useEffect} from "react";
import {useSelector} from "react-redux";
import {useNavigate} from "react-router-dom";

import GlobalView from "@/modules-ampcon/pages/Service/Switch/Switch/GlobalView/global_view";
import VpnView from "@/modules-ampcon/pages/Service/Switch/Switch/VpnView/vpn_view";
import SnmpView from "@/modules-ampcon/pages/Service/Switch/Switch/SnmpView/snmp_view";

const Switch = () => {
    const switchRef = useRef(null);
    const fetchIntervalRef = useRef();
    const currentUser = useSelector(state => state.user.userInfo);
    const [isForbidden, setIsForbidden] = useState(false);
    const [currentView, setCurrentView] = useState("global");
    const navigate = useNavigate();

    useEffect(() => {
        const userType = currentUser?.type;
        if (userType === "readonly") {
            setIsForbidden(true);
        } else {
            setIsForbidden(false);
        }
    }, [currentUser]);

    useEffect(() => {
        fetchIntervalRef.current = setInterval(() => {
            if (switchRef.current) {
                switchRef.current.refreshTable();
            }
        }, 60000);
        return () => {
            if (fetchIntervalRef.current) {
                clearInterval(fetchIntervalRef.current);
            }
        };
    }, []);

    // 视图切换
    const handleViewChange = viewKey => {
        setCurrentView(viewKey);
    };

    const handleNavigate = (path, state) => {
        navigate(path, {state});
    };

    return (
        <div style={{minHeight: "100%"}}>
            <Card style={{display: "flex", flex: 1, minHeight: "100%"}}>
                <h2 style={{marginTop: "8px", marginBottom: "20px"}}>Switch</h2>

                {currentView === "global" && (
                    <GlobalView
                        isForbidden={isForbidden}
                        tableRef={switchRef}
                        navigate={handleNavigate}
                        currentView={currentView}
                        onViewChange={handleViewChange}
                    />
                )}
                {currentView === "vpn" && (
                    <VpnView
                        isForbidden={isForbidden}
                        tableRef={switchRef}
                        navigate={handleNavigate}
                        currentView={currentView}
                        onViewChange={handleViewChange}
                    />
                )}
                {currentView === "snmp" && (
                    <SnmpView tableRef={switchRef} currentView={currentView} onViewChange={handleViewChange} />
                )}
            </Card>
        </div>
    );
};

export default Switch;
