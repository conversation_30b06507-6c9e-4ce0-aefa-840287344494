import {
    AmpConCustomTable,
    createColumnConfig,
    TableFilterDropdown,
    AmpConCustomModalTable
} from "@/modules-ampcon/components/custom_table";
import {Space, message, But<PERSON>, Card} from "antd";
import {fetchSnmpInfo, removeSnmpDevice} from "@/modules-ampcon/apis/dashboard_api";
import {useRef, useState, useEffect} from "react";
import {useSelector} from "react-redux";
import {useNavigate} from "react-router-dom";
import SSHAction from "@/modules-ampcon/pages/Service/Switch/ssh_action";
import {onlineSvg, offlineSvg, exclamationSvg, exportSvg} from "@/utils/common/iconSvg";
import Icon from "@ant-design/icons/lib/components/Icon";
import LogAction from "@/modules-ampcon/pages/Service/Switch/log_action";
import ViewChangeDropdown from "@/modules-ampcon/pages/Service/Switch/Switch/view_change_dropdown";
import {confirmModalAction} from "@/modules-ampcon/components/custom_modal";
import ImportDropdown from "@/modules-ampcon/pages/Service/Switch/Switch/import_dropdown";

const SnmpView = ({tableRef, currentView, onViewChange}) => {
    // const currentUser = useSelector(state => state.user.userInfo);
    // const [isForbidden, setIsForbidden] = useState(false);
    const navigate = useNavigate();

    const snmpColumns = [
        {...createColumnConfig("Sysname", "sysname", TableFilterDropdown, "", "16.6%")},
        {
            ...createColumnConfig("SN/Service Tag", "sn", TableFilterDropdown, "", "16.7%"),
            render: (_, record) => {
                return (
                    <Space size="large" className="actionLink">
                        <a
                            onClick={() => {
                                navigate(`/service/switch/${record.sn}`);
                            }}
                        >
                            {record.sn}
                        </a>
                    </Space>
                );
            }
        },
        createColumnConfig("Model", "model", TableFilterDropdown, "", "16.7%"),
        createColumnConfig("Version", "version", TableFilterDropdown, "", "16.7%"),
        {
            ...createColumnConfig("Mgmt IP", "mgt_ip", TableFilterDropdown, "", "16.7%"),
            render: (_, record) => {
                if (!record.mgt_ip) {
                    return null;
                }

                let iconComponent;
                if (record.reachable_status === 0) {
                    iconComponent = onlineSvg;
                } else if (record.reachable_status === 1) {
                    iconComponent = offlineSvg;
                } else {
                    iconComponent = exclamationSvg;
                }

                return (
                    <Space>
                        <Icon component={iconComponent} />
                        {/* {record.link_ip_addr ? `${record.mgt_ip}/${record.link_ip_addr}` : record.mgt_ip} */}
                        {record.mgt_ip}
                    </Space>
                );
            }
        },
        {
            title: "Operation",
            render: (_, record) => {
                return (
                    <div>
                        <Space size="large" className="actionLink">
                            <SSHAction record={record} tableRef={tableRef} />
                            <LogAction record={record} tableRef={tableRef} />
                            <a
                                onClick={() => {
                                    clickRemoveSnmpDeviceCallback(record);
                                }}
                            >
                                Remove
                            </a>
                        </Space>
                    </div>
                );
            }
        }
    ].filter(Boolean);

    const snmpSearchFieldsList = ["sn", "sysname", "mgt_ip", "model"];
    const snmpMatchFieldsList = [
        {name: "sn", matchMode: "fuzzy"},
        {name: "sysname", matchMode: "fuzzy"},
        {name: "mgt_ip", matchMode: "fuzzy"},
        {name: "model", matchMode: "fuzzy"}
    ];

    const clickRemoveSnmpDeviceCallback = record => {
        confirmModalAction("Are you sure want to remove the SNMP device ?", () => {
            removeSnmpDevice({snmpId: record.id}).then(response => {
                if (response.status !== 200) {
                    message.error(response.info);
                } else {
                    message.success(response.info);
                    tableRef.current.refreshTable();
                }
            });
        });
    };

    return (
        <div>
            <AmpConCustomTable
                columns={snmpColumns}
                searchFieldsList={snmpSearchFieldsList}
                matchFieldsList={snmpMatchFieldsList}
                extraButton={<ExtraButton tableRef={tableRef} currentView={currentView} onViewChange={onViewChange} />}
                fetchAPIInfo={fetchSnmpInfo}
                ref={tableRef}
            />
        </div>
    );
};

const ExtraButton = ({tableRef, currentView, onViewChange}) => {
    return (
        <Space size="middle">
            <ImportDropdown tableRef={tableRef} isSnmp />
            <ViewChangeDropdown currentView={currentView} onViewChange={onViewChange} />
        </Space>
    );
};

export default SnmpView;
