import {Button, Dropdown, message, Spin} from "antd";
import React, {useState} from "react";
import {DownOutlined, UpOutlined} from "@ant-design/icons";

const ViewChangeDropdown = ({currentView, onViewChange}) => {
    const [hoverStatus, setHoverStatus] = useState(false);
    const viewNameMap = {
        global: "Global View",
        vpn: "VPN View",
        snmp: "SNMP View"
    };
    const viewMenu = [
        {
            key: "global",
            label: "Global View",
            onClick: () => onViewChange("global")
        },
        {
            key: "vpn",
            label: "VPN View",
            onClick: () => onViewChange("vpn")
        },
        {
            key: "snmp",
            label: "SNMP View",
            onClick: () => onViewChange("snmp")
        }
    ];

    return (
        <Dropdown menu={{items: viewMenu}} trigger={["hover"]} onOpenChange={val => setHoverStatus(val)}>
            <Button style={{width: "131px", height: "36px"}}>
                {viewNameMap[currentView]}
                {hoverStatus ? (
                    <UpOutlined style={{fontSize: "12px", marginLeft: "8px"}} />
                ) : (
                    <DownOutlined style={{fontSize: "12px", marginLeft: "8px"}} />
                )}
            </Button>
        </Dropdown>
    );
};

export default ViewChangeDropdown;
