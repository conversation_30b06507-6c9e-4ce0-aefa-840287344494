import {Button, Dropdown, Spin, Form, Input, Select, message, TreeSelect, Divider, Radio, Upload} from "antd";
// import <PERSON>agger from "antd/es/upload/Dragger";
import Icon, {InboxOutlined, DownOutlined, UpOutlined, PlusOutlined, MinusOutlined} from "@ant-design/icons";
import React, {useState, useEffect} from "react";
import {formValidateRules} from "@/modules-ampcon/utils/util";
import {AmpConCustomModalForm} from "@/modules-ampcon/components/custom_table";
import {getSystemConfigInfo} from "@/modules-ampcon/apis/config_api";
import {importVPNSwitch} from "@/modules-ampcon/apis/rma_api";
import {useSelector} from "react-redux";
import {getUserGroup} from "@/modules-ampcon/apis/inventory_api";
import {getFabric, getSite} from "@/modules-ampcon/apis/lifecycle_api";
import {batchUpdateSnmpDevice, updateSnmpDevice} from "@/modules-ampcon/apis/dashboard_api";
import uploadFileSvg from "@/modules-ampcon/pages/Service/Switch/Switch/resource/upload_file.svg?react";
import downloadSVG from "@/modules-ampcon/pages/Service/Switch/Switch/resource/download_template.svg?react";
import {tipModalAction} from "@/modules-ampcon/components/custom_modal";

const {Option} = Select;
const {Dragger} = Upload;

const ImportDropdown = ({tableRef, isVpn, isSnmp}) => {
    const [isVpnModalOpen, setisVpnModalOpen] = useState(false);
    const [isSnmpModalOpen, setisSnmpModalOpen] = useState(false);
    const [hoverStatus, setHoverStatus] = useState(false);
    const currentUser = useSelector(state => state.user.userInfo);
    const [groupList, setGroupList] = useState([]);

    const dropdownMenu = [
        isVpn && {
            label: "VPN",
            onClick: () => {
                setisVpnModalOpen(true);
            }
        },
        isSnmp && {
            label: "SNMP",
            onClick: () => {
                setisSnmpModalOpen(true);
            }
        }
    ];

    useEffect(() => {
        if (isGroupUser()) {
            getUserGroup().then(response => {
                setGroupList(response.data);
            });
        }
    }, [currentUser]);

    const isGroupUser = () => {
        return currentUser.userType === "group";
    };

    return (
        <div>
            <Dropdown menu={{items: dropdownMenu}} trigger={["hover"]} onOpenChange={val => setHoverStatus(val)}>
                <Button type="primary" style={{width: "98px", height: "36px"}}>
                    <>
                        Import
                        {hoverStatus ? (
                            <UpOutlined style={{fontSize: "12px", marginLeft: "8px"}} />
                        ) : (
                            <DownOutlined style={{fontSize: "12px", marginLeft: "8px"}} />
                        )}
                    </>
                </Button>
            </Dropdown>
            {isVpn && (
                <VpnModal
                    isModalOpen={isVpnModalOpen}
                    onCancel={() => setisVpnModalOpen(false)}
                    onSubmitSuccess={() => tableRef.current.refreshTable()}
                    groupList={groupList}
                />
            )}
            {isSnmp && (
                <SnmpModal
                    isModalOpen={isSnmpModalOpen}
                    onCancel={() => setisSnmpModalOpen(false)}
                    onSubmitSuccess={() => tableRef.current.refreshTable()}
                />
            )}
        </div>
    );
};

const VpnModal = ({isModalOpen, onCancel, onSubmitSuccess, groupList}) => {
    const fabricSelectTooltip =
        "Select the fabric configuration for the switch. This determines the network topology and connectivity.";
    const siteSelectTooltip =
        "Select the site where the switch will be deployed. This helps in organizing and managing the network devices.";

    const [form] = Form.useForm();
    const [configOptions, setConfigOption] = useState([]);
    const [spinning, setSpinning] = useState(false);
    const currentUser = useSelector(state => state.user.userInfo);

    const [additionalGroupObject, setAdditionalGroupObject] = useState({});
    const [groupListIndex, setGroupListIndex] = useState(0);
    const [rootGroupObject, setRootGroupObject] = useState({
        valid: true
    });

    const [fabricList, setFabricList] = useState([]);
    const [siteList, setSiteList] = useState([]);
    const [selectedFabric, setSelectedFabric] = useState("default");
    const [selectedSite, setSelectedSite] = useState("default");

    const handleGroupChangeCallback = (selectedItem, siteGroupKey) => {
        const formValues = form.getFieldsValue();
        formValues[siteGroupKey] = selectedItem.children;
        form.setFieldsValue(formValues);
        const newAdditionalGroupObject = JSON.parse(JSON.stringify(additionalGroupObject));
        groupValueCheck(formValues, newAdditionalGroupObject);
    };

    const handleRemoveGroupCallback = (selectedItem, groupKey) => {
        const newAdditionalGroupObject = JSON.parse(JSON.stringify(additionalGroupObject));
        delete newAdditionalGroupObject[groupKey];
        const formValues = form.getFieldsValue();
        delete formValues[groupKey];
        form.setFieldsValue(formValues);
        groupValueCheck(formValues, newAdditionalGroupObject);
    };

    const groupValueCheck = (formValues, newAdditionalGroupObject) => {
        const keysWithValuesMoreThanTwice = getGroupKeysWithValuesMoreThanTwice(formValues);
        // set all keys to valid
        setRootGroupObject({valid: true});
        Object.keys(newAdditionalGroupObject).forEach(key => {
            newAdditionalGroupObject[key].valid = true;
        });
        keysWithValuesMoreThanTwice.forEach(key => {
            if (!key.startsWith("group")) {
                return;
            }
            if (key === "group0") {
                setRootGroupObject({valid: false});
            } else {
                newAdditionalGroupObject[key].valid = false;
            }
        });
        setAdditionalGroupObject(newAdditionalGroupObject);
    };

    const getGroupKeysWithValuesMoreThanTwice = formValues => {
        const filteredFormValues = Object.keys(formValues)
            .filter(key => key.startsWith("group"))
            .reduce((result, key) => {
                result[key] = formValues[key];
                return result;
            }, {});
        const valueCounts = Object.values(filteredFormValues).reduce((counts, value) => {
            counts[value] = (counts[value] || 0) + 1;
            return counts;
        }, {});
        const valuesMoreThanTwice = Object.keys(valueCounts).filter(value => valueCounts[value] >= 2);
        return Object.keys(formValues).filter(key => valuesMoreThanTwice.includes(formValues[key]));
    };

    const formItems = () => {
        return (
            <>
                <Form.Item
                    name="ip"
                    label="IP"
                    rules={[{required: true, message: "Please input your IP!"}, formValidateRules.ipv4()]}
                >
                    <Input placeholder="IP" style={{width: "280px"}} />
                </Form.Item>
                <Form.Item
                    name="systemConfig"
                    label="System Config"
                    rules={[{required: true, message: "Please select your config!"}]}
                >
                    <Select placeholder="Select a config" style={{width: "280px"}}>
                        {configOptions.map(item => (
                            <Option key={item} value={item}>
                                {item}
                            </Option>
                        ))}
                    </Select>
                </Form.Item>

                {import.meta.env.VITE_APP_EXPORT_MODULE === "AmpCon-DC" && (
                    <Form.Item
                        name="fabric"
                        label="Fabric"
                        rules={[
                            {
                                validator: () => {
                                    return Promise.resolve(); // 始终通过
                                }
                            }
                        ]}
                        required
                    >
                        <Select
                            style={{width: "280px"}}
                            placeholder="Please select a fabirc"
                            defaultValue={selectedFabric}
                            onChange={value => {
                                setSelectedFabric(value);
                            }}
                        >
                            {fabricList.map(fabric => {
                                return (
                                    <Option key={fabric} value={fabric}>
                                        {fabric}
                                    </Option>
                                );
                            })}
                        </Select>
                    </Form.Item>
                )}
                {import.meta.env.VITE_APP_EXPORT_MODULE === "AmpCon-CAMPUS" && (
                    <Form.Item
                        name="site"
                        label="Site"
                        rules={[
                            {
                                validator: () => {
                                    return Promise.resolve(); // 始终通过
                                }
                            }
                        ]}
                        required
                    >
                        <Select
                            style={{width: "280px"}}
                            placeholder="Please select a site"
                            defaultValue={selectedSite}
                            onChange={value => {
                                setSelectedSite(value);
                            }}
                        >
                            {siteList.map(site => {
                                return (
                                    <Option key={site} value={site}>
                                        {site}
                                    </Option>
                                );
                            })}
                        </Select>
                    </Form.Item>
                )}

                {groupList.length !== 0 ? (
                    <>
                        <Form.Item
                            name="group0"
                            label="Select Group"
                            rules={[{required: true, message: "Please select a group!"}]}
                            validateStatus={rootGroupObject.valid ? "success" : "error"}
                            help={rootGroupObject.valid ? "" : "The selected group cannot be selected."}
                            validateTrigger={["onChange", "onBlur"]}
                        >
                            <Select
                                style={{width: "280px"}}
                                placeholder="Please select group"
                                onChange={(value, selectedItem) => {
                                    handleGroupChangeCallback(selectedItem, "group0");
                                }}
                            >
                                {groupList.map(group => {
                                    return (
                                        <Option key={group.id} value={group.name}>
                                            {group.name}
                                        </Option>
                                    );
                                })}
                            </Select>
                            <Button
                                style={{
                                    backgroundColor: "transparent",
                                    color: "#BFBFBF"
                                }}
                                type="link"
                                onClick={() => {
                                    const newAdditionalGroupObject = JSON.parse(JSON.stringify(additionalGroupObject));
                                    newAdditionalGroupObject[`group${groupListIndex + 1}`] = {
                                        valid: true
                                    };
                                    setGroupListIndex(groupListIndex + 1);
                                    setAdditionalGroupObject(newAdditionalGroupObject);
                                }}
                                icon={<PlusOutlined />}
                            />
                        </Form.Item>
                        {Object.entries(additionalGroupObject).map(([groupKey, groupValue]) => {
                            return (
                                <Form.Item
                                    name={groupKey}
                                    label="Select Group"
                                    validateStatus={groupValue.valid ? "success" : "error"}
                                    help={groupValue.valid ? "" : "The selected group cannot be selected."}
                                    validateTrigger={["onChange", "onBlur"]}
                                >
                                    <Select
                                        style={{width: "280px"}}
                                        placeholder="Please select a group"
                                        onChange={(value, selectedItem) => {
                                            handleGroupChangeCallback(selectedItem, groupKey);
                                        }}
                                    >
                                        {groupList.map(group => {
                                            return (
                                                <Option key={group.id} value={group.id}>
                                                    {group.name}
                                                </Option>
                                            );
                                        })}
                                    </Select>
                                    <Button
                                        style={{
                                            backgroundColor: "transparent",
                                            color: "black"
                                        }}
                                        type="minus"
                                        onClick={(value, selectedItem) => {
                                            handleRemoveGroupCallback(selectedItem, groupKey);
                                        }}
                                        icon={<MinusOutlined />}
                                    />
                                </Form.Item>
                            );
                        })}
                    </>
                ) : null}
            </>
        );
    };

    const onSubmit = async () => {
        const values = form.getFieldValue();
        const groupList = Object.values(values).filter((_, index) => {
            const key = Object.keys(values)[index];
            return key.startsWith("group");
        });
        setSpinning(true);
        onCancel();
        form.resetFields();
        setSelectedSite("default");
        setSelectedFabric("default");
        importVPNSwitch(values.ip, values.systemConfig, groupList, selectedFabric, selectedSite)
            .then(response => {
                setSpinning(false);
                if (response.status !== 200) {
                    message.error(response.info);
                } else {
                    message.success(response.info);
                    onSubmitSuccess();
                }
            })
            .catch(() => {
                setSpinning(false);
                message.error("Import Switch failed");
            });
    };

    useEffect(() => {
        fetchFabricOrSiteSelectData().then(() => {});

        if (currentUser.type !== "readonly") {
            getSystemConfigInfo("Global").then(response => {
                if (response.status === 200) {
                    setConfigOption(response.data.allSystemConfigName);
                }
            });
        }
        setAdditionalGroupObject({});
        setGroupListIndex(0);
    }, []);

    const onCancelFunc = () => {
        setAdditionalGroupObject({});
        setGroupListIndex(0);
        onCancel();
        form.resetFields();
        setSelectedSite("default");
        setSelectedFabric("default");
    };

    const fetchFabricOrSiteSelectData = async () => {
        if (import.meta.env.VITE_APP_EXPORT_MODULE === "AmpCon-CAMPUS") {
            await getSite().then(response => {
                if (response.status !== 200) {
                    message.error(response.info);
                } else {
                    setFabricList(["default"]);
                    setSiteList(response.data);
                }
            });
        } else if (import.meta.env.VITE_APP_EXPORT_MODULE === "AmpCon-DC") {
            await getFabric().then(response => {
                if (response.status !== 200) {
                    message.error(response.info);
                } else {
                    setFabricList(response.data);
                    setSiteList(["default"]);
                }
            });
        }
    };

    return (
        <div>
            <AmpConCustomModalForm
                title="Import switch with VPN"
                isModalOpen={isModalOpen}
                formInstance={form}
                layoutProps={{
                    labelCol: {
                        span: 6
                    }
                }}
                CustomFormItems={formItems}
                onCancel={onCancelFunc}
                onSubmit={onSubmit}
                modalClass="ampcon-middle-modal"
            />
            <Spin spinning={spinning} tip="Loading..." size="large" fullscreen />
        </div>
    );
};

const SnmpModal = ({isModalOpen, onCancel, onSubmitSuccess}) => {
    const [form] = Form.useForm();
    const currentUser = useSelector(state => state.user.userInfo);

    const [snmpVersion, setSnmpVersion] = useState("v3");
    const [securityLevel, setSecurityLevel] = useState("authPriv");
    const [spinning, setSpinning] = useState(false);

    const [importMode, setImportMode] = useState("single");
    const [fileList, setFileList] = useState([]); // 上传文件列表
    // const [uploadProgress, setUploadProgress] = useState(0); // 上传进度

    const snmpVersionOptions = [
        {value: "v1", label: "SNMP v1"},
        {value: "v2c", label: "SNMP v2c"},
        {value: "v3", label: "SNMP v3"}
    ];

    const securityLevelOptions = [
        {value: "noAuthNoPriv", label: "noAuthNoPriv"},
        {value: "authNoPriv", label: "authNoPriv"},
        {value: "authPriv", label: "authPriv"}
    ];

    const authProtocolOptions = [
        {value: "MD5", label: "MD5"},
        {value: "SHA", label: "SHA"},
        {value: "SHA-256", label: "SHA-256"}
    ];

    const privProtocolOptions = [
        {value: "DES", label: "DES"},
        {value: "AES-128", label: "AES-128"},
        {value: "AES-256", label: "AES-256"}
    ];

    const handleDownload = () => {
        const templateExamples = [
            {
                description: "Example for SNMP v1 - all fields are required",
                ip: "***********",
                snmpVersion: "v1",
                community: "Public"
            },
            {
                description: "Example for SNMP v2c - all fields are required",
                ip: "***********",
                snmpVersion: "v2c",
                community: "Public"
            },
            {
                description:
                    "Example for SNMP v3 with securityLevel 'noAuthNoPriv' - contextName is optional. If the device does not require a specific SNMP context, use an empty string (do not use spaces).",
                ip: "***********",
                snmpVersion: "v3",
                contextName: "",
                securityUser: "user1",
                securityLevel: "noAuthNoPriv"
            },
            {
                description:
                    "Example for SNMP v3 with securityLevel 'authNoPriv' - contextName is optional. If the device does not require a specific SNMP context, use an empty string (do not use spaces).",
                ip: "***********",
                snmpVersion: "v3",
                contextName: "",
                securityUser: "user2",
                securityLevel: "authNoPriv",
                authProtocol: "MD5",
                authPassword: "AuthPass123"
            },
            {
                description:
                    "Example for SNMP v3 with securityLevel 'authPriv' - contextName is optional. If the device does not require a specific SNMP context, use an empty string (do not use spaces).",
                ip: "***********",
                snmpVersion: "v3",
                contextName: "",
                securityUser: "user3",
                securityLevel: "authPriv",
                authProtocol: "SHA",
                authPassword: "AuthPass456",
                privProtocol: "AES-128",
                privPassword: "PrivPass789"
            }
        ];

        // 转换为标准 JSON 字符串
        const jsonString = JSON.stringify(templateExamples, null, 2);
        const blob = new Blob([jsonString], {type: "application/json"});
        const url = URL.createObjectURL(blob);

        const link = document.createElement("a");
        link.href = url;
        link.download = "snmp_templates.json";
        link.click();

        // 清理资源
        URL.revokeObjectURL(url);
    };

    const handleImportModeChange = mode => {
        setImportMode(mode);
        // setFileList([]);
        // form.resetFields();
    };

    const handleRemoveFile = file => {
        // console.log(fileList);
        setFileList(prevFiles => prevFiles.filter(f => f.uid !== file.uid));
    };

    const handleBatchImport = async () => {
        try {
            setSpinning(true);

            if (fileList.length === 0) {
                throw new Error("No files selected");
            }

            const formData = new FormData();
            formData.append("file", fileList[0]);

            const response = await batchUpdateSnmpDevice(formData);
            if (response.status !== 200) {
                message.error(`${fileList[0].name} upload failed: ${response.info}`);
            } else {
                message.success(`${fileList[0].name} upload success`);
                onSubmitSuccess();
            }
        } catch (error) {
            message.error(error.message || "Batch import failed, please check input");
        } finally {
            onCancel();
            setSpinning(false);
            setFileList([]);
        }
    };

    // 动态表单
    const getFormItems = () => {
        return (
            <>
                <Form.Item name="importMode" label="Import Mode" initialValue={importMode}>
                    <Radio.Group
                        options={[
                            {value: "single", label: "Single Import"},
                            {value: "batch", label: "Batch Import"}
                        ]}
                        onChange={e => handleImportModeChange(e.target.value)}
                    />
                </Form.Item>

                {importMode === "batch" && (
                    <>
                        <Form.Item name="downloadTemplate" label="Download Template" className="actionLink">
                            <a onClick={handleDownload}>
                                <Icon component={downloadSVG} style={{marginRight: 4}} />
                                JSON Template
                            </a>
                        </Form.Item>
                        <Form.Item name="uploadFile" label="Upload File" wrapperCol={{span: 12}}>
                            <Dragger
                                name="file"
                                style={{marginTop: "10px"}}
                                beforeUpload={file => {
                                    // 处理文件格式验证
                                    const isJson = file.name.endsWith(".json");
                                    if (isJson) {
                                        if (fileList.length < 1) {
                                            setFileList(prev => [...prev, file]);
                                        } else {
                                            message.error("Only one file can be uploaded at a time");
                                        }
                                    } else {
                                        message.error(`${file.name} is not a json file`);
                                    }
                                    return false;
                                }}
                                accept=".json"
                                fileList={fileList}
                                onRemove={file => handleRemoveFile(file)}
                            >
                                <p className="ant-upload-drag-icon">
                                    <Icon component={uploadFileSvg} />
                                </p>
                                <p className="ant-upload-hint">
                                    Drag and Drop file here or <span style={{color: "#14C9BB"}}>Choose File</span>
                                    <br />
                                    (.json)
                                </p>
                            </Dragger>
                        </Form.Item>
                    </>
                )}

                {importMode === "single" && (
                    <>
                        <Form.Item
                            name="ip"
                            label="IP Address"
                            rules={[{required: true, message: "Please input IP address!"}, formValidateRules.ipv4()]}
                        >
                            <Input style={{width: "280px"}} placeholder="Example：***********" />
                        </Form.Item>

                        <Form.Item
                            name="snmpVersion"
                            label="SNMP Version"
                            initialValue="v3"
                            rules={[{required: true, message: "Please select SNMP version!"}]}
                        >
                            <Select
                                style={{width: "280px"}}
                                value={snmpVersion}
                                onChange={value => setSnmpVersion(value)}
                            >
                                {snmpVersionOptions.map(option => (
                                    <Option key={option.value} value={option.value}>
                                        {option.label}
                                    </Option>
                                ))}
                            </Select>
                        </Form.Item>

                        {(snmpVersion === "v1" || snmpVersion === "v2c") && (
                            <Form.Item
                                name="community"
                                label="SNMP Community"
                                rules={[{required: true, message: "Please input SNMP community!"}]}
                            >
                                <Input style={{width: "280px"}} />
                            </Form.Item>
                        )}

                        {snmpVersion === "v3" && (
                            <>
                                <Form.Item name="contextName" label="Context Name">
                                    <Input style={{width: "280px"}} />
                                </Form.Item>

                                <Form.Item
                                    name="securityUser"
                                    label="Security User"
                                    rules={[{required: true, message: "Please input security user!"}]}
                                >
                                    <Input style={{width: "280px"}} />
                                </Form.Item>

                                <Form.Item
                                    name="securityLevel"
                                    label="Security Level"
                                    initialValue="authPriv"
                                    rules={[{required: true, message: "Please select security level!"}]}
                                >
                                    <Select
                                        style={{width: "280px"}}
                                        value={securityLevel}
                                        onChange={value => setSecurityLevel(value)}
                                    >
                                        {securityLevelOptions.map(option => (
                                            <Option key={option.value} value={option.value}>
                                                {option.label}
                                            </Option>
                                        ))}
                                    </Select>
                                </Form.Item>

                                {securityLevel !== "noAuthNoPriv" && (
                                    <>
                                        <Form.Item
                                            name="authProtocol"
                                            label="Authentication Protocol"
                                            initialValue="MD5"
                                            rules={[
                                                {required: true, message: "Please select authentication protocol!"}
                                            ]}
                                        >
                                            <Select
                                                placeholder="Select authentication protocol"
                                                style={{width: "280px"}}
                                            >
                                                {authProtocolOptions.map(option => (
                                                    <Option key={option.value} value={option.value}>
                                                        {option.label}
                                                    </Option>
                                                ))}
                                            </Select>
                                        </Form.Item>

                                        <Form.Item
                                            name="authPassword"
                                            label="Authentication Password"
                                            rules={[{required: true, message: "Please input authentication password!"}]}
                                        >
                                            <Input.Password style={{width: "280px"}} />
                                        </Form.Item>
                                    </>
                                )}

                                {securityLevel === "authPriv" && (
                                    <>
                                        <Form.Item
                                            name="privProtocol"
                                            label="Privacy Protocol"
                                            initialValue="DES"
                                            rules={[{required: true, message: "Please select privacy protocol!"}]}
                                        >
                                            <Select placeholder="Select privacy protocol" style={{width: "280px"}}>
                                                {privProtocolOptions.map(option => (
                                                    <Option key={option.value} value={option.value}>
                                                        {option.label}
                                                    </Option>
                                                ))}
                                            </Select>
                                        </Form.Item>

                                        <Form.Item
                                            name="privPassword"
                                            label="Privacy Password"
                                            rules={[{required: true, message: "Please input privacy password!"}]}
                                        >
                                            <Input.Password style={{width: "280px"}} />
                                        </Form.Item>
                                    </>
                                )}
                            </>
                        )}
                    </>
                )}
            </>
        );
    };

    const onSubmit = async () => {
        try {
            setSpinning(true);
            const values = await form.getFieldValue();

            const response = await updateSnmpDevice(values);

            if (response.status !== 200) {
                tipModalAction(response.info, () => {});
                // message.error(response.info);
                // form.resetFields();
            } else {
                message.success(response.info);
                onSubmitSuccess();
                form.resetFields();
            }
        } catch (error) {
            // console.error("Form validation error:", error);
            message.error("Please check form inputs");
        } finally {
            onCancel();
            setSpinning(false);
        }
    };

    const onCancelFunc = () => {
        setSnmpVersion("v3");
        setSecurityLevel("authPriv");
        form.resetFields();
        onCancel();
    };

    return (
        <div>
            <AmpConCustomModalForm
                title={<h3>Import Switch with SNMP</h3>}
                isModalOpen={isModalOpen}
                formInstance={form}
                layoutProps={{
                    labelCol: {
                        span: 8
                    }
                }}
                CustomFormItems={getFormItems}
                onCancel={onCancelFunc}
                onSubmit={onSubmit}
                modalClass="ampcon-middle-modal"
                footer={[
                    <Divider style={{marginTop: 0, marginBottom: 20}} />,
                    <Button key="cancel" onClick={onCancelFunc}>
                        Cancel
                    </Button>,
                    <Button
                        key="apply"
                        type="primary"
                        onClick={() => (importMode === "single" ? form.submit() : handleBatchImport())}
                    >
                        Apply
                    </Button>
                ]}
            />
            <Spin spinning={spinning} tip="Importing..." size="large" fullscreen />
        </div>
    );
};

export default ImportDropdown;
