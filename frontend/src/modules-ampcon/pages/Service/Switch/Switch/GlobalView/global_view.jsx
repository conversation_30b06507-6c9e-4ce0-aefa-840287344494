import {<PERSON>, Button, Dropdown, Modal} from "antd";
import {useState} from "react";
import {onlineSvg, offlineSvg, exclamationSvg} from "@/utils/common/iconSvg";
import Icon from "@ant-design/icons/lib/components/Icon";
import {AmpConCustomTable, createColumnConfig, TableFilterDropdown} from "@/modules-ampcon/components/custom_table";
import {fetchAllSwitch, fetchAllSwitchWithFabric} from "@/modules-ampcon/apis/dashboard_api";
import LifecycleAction from "@/modules-ampcon/pages/Service/Switch/lifecycle_action";
import SSHAction from "@/modules-ampcon/pages/Service/Switch/ssh_action";
import LogAction from "@/modules-ampcon/pages/Service/Switch/log_action";
import ConfigAction from "@/modules-ampcon/pages/Service/Switch/config_action";
import ConfigurationAction from "@/modules-ampcon/pages/Service/Switch/configuration_action";
import StageAction from "@/modules-ampcon/pages/Service/Switch/stage_action";
import ImportDropdown from "@/modules-ampcon/pages/Service/Switch/Switch/import_dropdown";
import ViewChangeDropdown from "@/modules-ampcon/pages/Service/Switch/Switch/view_change_dropdown";

const GlobalView = ({isForbidden, tableRef, navigate, currentView, onViewChange}) => {
    const columns = [
        {...createColumnConfig("Sysname", "host_name", TableFilterDropdown)},
        {
            ...createColumnConfig("SN/Service Tag", "sn", TableFilterDropdown),
            render: (_, record) => (
                <Space size="large" className="actionLink">
                    <a onClick={() => navigate(`/service/switch/${record.sn}`)}>{record.sn}</a>
                </Space>
            )
        },
        createColumnConfig("Model", "platform_model", TableFilterDropdown),
        createColumnConfig("Version", "version", TableFilterDropdown),
        import.meta.env.VITE_APP_EXPORT_MODULE === "AmpCon-DC"
            ? createColumnConfig("Fabric", "fabric", TableFilterDropdown)
            : null,
        createColumnConfig("Status", "status", TableFilterDropdown),
        {
            ...createColumnConfig("Mgmt IP", "mgt_ip", TableFilterDropdown),
            render: (_, record) => {
                if (!record.mgt_ip) return null;

                let iconComponent;
                if (record.reachable_status === 0) {
                    iconComponent = onlineSvg;
                } else if (record.reachable_status === 1) {
                    iconComponent = offlineSvg;
                } else {
                    iconComponent = exclamationSvg;
                }

                return (
                    <Space>
                        <Icon component={iconComponent} />
                        {record.link_ip_addr ? `${record.mgt_ip}/${record.link_ip_addr}` : record.mgt_ip}
                    </Space>
                );
            }
        },
        {
            title: "Operation",
            render: (_, record) => (
                <div>
                    <Space size="large" className="actionLink">
                        {isForbidden ? null : <StageAction record={record} tableRef={tableRef} />}
                        <SSHAction record={record} tableRef={tableRef} />
                        <LogAction record={record} tableRef={tableRef} />
                        {isForbidden ? null : <ConfigurationAction record={record} tableRef={tableRef} />}
                        <ConfigAction record={record} isForbidden={isForbidden} tableRef={tableRef} />
                        {isForbidden ? null : <LifecycleAction record={record} tableRef={tableRef} />}
                    </Space>
                </div>
            )
        }
    ].filter(Boolean);

    const switchSearchFieldsList = ["sn", "host_name", "mgt_ip", "platform_model"];
    const switchMatchFieldsList = [
        {name: "sn", matchMode: "fuzzy"},
        {name: "host_name", matchMode: "fuzzy"},
        {name: "mgt_ip", matchMode: "fuzzy"},
        {name: "platform_model", matchMode: "fuzzy"},
        {name: "create_time", matchMode: "fuzzy"}
    ];

    return (
        <div>
            <AmpConCustomTable
                columns={columns}
                searchFieldsList={switchSearchFieldsList}
                matchFieldsList={switchMatchFieldsList}
                extraButton={<ExtraButton tableRef={tableRef} currentView={currentView} onViewChange={onViewChange} />}
                fetchAPIInfo={
                    import.meta.env.VITE_APP_EXPORT_MODULE === "AmpCon-DC" ? fetchAllSwitchWithFabric : fetchAllSwitch
                }
                ref={tableRef}
            />
        </div>
    );
};

const ExtraButton = ({tableRef, currentView, onViewChange}) => {
    return (
        <Space size="middle">
            <ImportDropdown tableRef={tableRef} isVpn isSnmp />
            <ViewChangeDropdown currentView={currentView} onViewChange={onViewChange} />
        </Space>
    );
};

export default GlobalView;
