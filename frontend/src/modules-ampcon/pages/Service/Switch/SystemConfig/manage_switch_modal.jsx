import {<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, message} from "antd";
import {forwardRef, useEffect, useImperativeHandle, useRef, useState} from "react";
import {querySwitchPinBySystemConfigName, updateSystemConfigAndSwitchMapping} from "@/modules-ampcon/apis/config_api";
import {
    AmpConCustomModalTable,
    createColumnConfigMultipleParams,
    TableFilterDropdown
} from "@/modules-ampcon/components/custom_table";

const ManageSwitchModal = forwardRef((props, ref) => {
    useImperativeHandle(ref, () => ({
        showManageSwitchModal: configName => {
            setSelectedSystemConfig(configName);
            setIsShowModal(true);
        },
        hideManageSwitchModal: () => {
            setIsShowModal(false);
        }
    }));

    useEffect(() => {
        refreshTableData();
    }, []);

    const title = "Manage Switch";

    const matchFieldsList = [
        {name: "host_name", matchMode: "fuzzy"},
        {name: "sn", matchMode: "fuzzy"},
        {name: "platform_model", matchMode: "fuzzy"},
        {name: "status", matchMode: "fuzzy"},
        {name: "mgt_ip", matchMode: "fuzzy"}
    ];
    const searchFieldsList = ["host_name", "sn", "platform_model", "status", "mgt_ip"];

    const tableModalRef = useRef(null);

    const [isShowModal, setIsShowModal] = useState(false);
    const [selectedSystemConfig, setSelectedSystemConfig] = useState(null);

    const refreshTableData = () => {};

    return (
        <AmpConCustomModalTable
            modalClass="ampcon-max-modal"
            ref={tableModalRef}
            title={title}
            rowSelection={{
                selectedRowKeys: [],
                selectedRows: [],
                onChange: () => {}
            }}
            selectModalOpen={isShowModal}
            onCancel={() => {
                setIsShowModal(false);
            }}
            columns={[
                createColumnConfigMultipleParams({
                    title: "Host Name",
                    dataIndex: "host_name",
                    filterDropdownComponent: TableFilterDropdown,
                    width: "15%"
                }),
                createColumnConfigMultipleParams({
                    title: "SN / Service Tag",
                    dataIndex: "sn",
                    filterDropdownComponent: TableFilterDropdown,
                    width: "15%"
                }),
                createColumnConfigMultipleParams({
                    title: "Model",
                    dataIndex: "platform_model",
                    filterDropdownComponent: TableFilterDropdown,
                    width: "15%"
                }),
                createColumnConfigMultipleParams({
                    title: "Version",
                    enableSorter: false,
                    enableFilter: false,
                    render: (_, record) => {
                        if (record.revision === null) {
                            return "";
                        }
                        if (record.version === null) {
                            return `${record.version}`;
                        }
                        return `${record.version}/${record.revision}`;
                    }
                }),
                createColumnConfigMultipleParams({
                    title: "Status",
                    dataIndex: "status",
                    filterDropdownComponent: TableFilterDropdown,
                    width: "15%"
                }),
                createColumnConfigMultipleParams({
                    title: "Mgmt",
                    dataIndex: "mgt_ip",
                    filterDropdownComponent: TableFilterDropdown,
                    width: "15%"
                })
            ]}
            matchFieldsList={matchFieldsList}
            searchFieldsList={searchFieldsList}
            buttonProps={[]}
            fetchAPIInfo={querySwitchPinBySystemConfigName}
            fetchAPIParams={[selectedSystemConfig]}
            footer={
                <Flex vertical>
                    <Divider />
                    <Flex justify="flex-end">
                        <Button
                            htmlType="button"
                            onClick={() => {
                                setIsShowModal(false);
                            }}
                        >
                            Cancel
                        </Button>
                        <Button
                            type="primary"
                            onClick={() => {
                                updateSystemConfigAndSwitchMapping(
                                    selectedSystemConfig,
                                    tableModalRef.current.getTableRef().current.getOperations()
                                ).then(response => {
                                    if (response.status === 200) {
                                        message.success(response.info);
                                        tableModalRef.current.getTableRef().current.refreshTable();
                                    } else {
                                        message.error(response.info);
                                    }
                                });
                            }}
                        >
                            Save
                        </Button>
                    </Flex>
                </Flex>
            }
        />
    );
});

export default ManageSwitchModal;
