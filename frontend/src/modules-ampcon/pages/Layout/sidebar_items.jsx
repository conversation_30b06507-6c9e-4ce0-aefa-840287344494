import React from "react";
import {Tooltip} from "antd";
import Icon from "@ant-design/icons";
import {
    dashBoardSvg,
    maintainSvg,
    monitorSvg,
    resourceSvg,
    serviceSvg,
    settingSvg,
    topologySvg,
    provisionSvg,
    roceSvg,
    telemetrySvg
} from "@/utils/common/iconSvg";

const dcItems = [
    {
        label: (
            <Tooltip title={<div className="fixed-tooltip">Dashboard</div>} placement="right">
                Dashboard
            </Tooltip>
        ),
        key: "/dashboard",
        icon: <Icon component={dashBoardSvg} />,
        children: [
            {
                key: "/dashboard/system_overview",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">System Overview</div>} placement="right">
                        System Overview
                    </Tooltip>
                )
            },
            {
                key: "/dashboard/switch_overview",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">Switch Overview</div>} placement="right">
                        Switch Overview
                    </Tooltip>
                )
            }
            // {
            //     key: "/dashboard/telemetry_dashboard",
            //     label: (
            //         <Tooltip title={<div className="fixed-tooltip">Telemetry Dashboard</div>} placement="right">
            //             Telemetry Dashboard
            //         </Tooltip>
            //     )
            // }
        ]
    },
    {
        label: (
            <Tooltip title={<div className="fixed-tooltip">Physical Network</div>} placement="right">
                Physical Network
            </Tooltip>
        ),
        key: "/physical_network",
        icon: <Icon component={topologySvg} />,
        children: [
            {
                key: "/physical_network/topologies",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">Topologies</div>} placement="right">
                        Topologies
                    </Tooltip>
                )
            },
            {
                key: "/physical_network/design",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">Design</div>} placement="right">
                        Design
                    </Tooltip>
                ),
                children: [
                    {
                        key: "/physical_network/design/units",
                        label: (
                            <Tooltip
                                className="menu-dot"
                                title={<div className="fixed-tooltip">Units</div>}
                                placement="right"
                            >
                                Units
                            </Tooltip>
                        )
                    },
                    {
                        key: "/physical_network/design/dc_templates",
                        label: (
                            <Tooltip
                                className="menu-dot"
                                title={<div className="fixed-tooltip">DC Templates</div>}
                                placement="right"
                            >
                                DC Templates
                            </Tooltip>
                        )
                    }
                ]
            },
            {
                key: "/physical_network/fabrics",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">Fabrics</div>} placement="right">
                        Fabrics
                    </Tooltip>
                )
            },
            {
                key: "/physical_network/RoCE",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">RoCE</div>} placement="right">
                        RoCE
                    </Tooltip>
                ),
                children: [
                    {
                        key: "/physical_network/RoCE/RoCE_easydeploy",
                        label: (
                            <Tooltip
                                className="menu-dot"
                                title={<div className="fixed-tooltip">RoCE EasyDeploy</div>}
                                placement="right"
                            >
                                RoCE EasyDeploy
                            </Tooltip>
                        )
                    },
                    {
                        key: "/physical_network/RoCE/RoCE_policies",
                        label: (
                            <Tooltip
                                className="menu-dot"
                                title={<div className="fixed-tooltip">RoCE Policies</div>}
                                placement="right"
                            >
                                RoCE Policies
                            </Tooltip>
                        )
                    },
                    {
                        key: "/physical_network/RoCE/load_balancing",
                        label: (
                            <Tooltip
                                className="menu-dot"
                                title={<div className="fixed-tooltip">Load Balancing</div>}
                                placement="right"
                            >
                                Load Balancing
                            </Tooltip>
                        )
                    },
                    {
                        key: "/physical_network/RoCE/NIC_configurations",
                        label: (
                            <Tooltip
                                className="menu-dot"
                                title={<div className="fixed-tooltip">NIC Configurations</div>}
                                placement="right"
                            >
                                NIC Configurations
                            </Tooltip>
                        )
                    }
                ]
            }
        ]
    },
    {
        label: (
            <Tooltip title={<div className="fixed-tooltip">Resource</div>} placement="right">
                Resource
            </Tooltip>
        ),
        key: "/resource",
        icon: <Icon component={resourceSvg} />,
        children: [
            {
                key: "/resource/resource_interconnection",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">Resource Interconnection</div>} placement="right">
                        Resource Interconnection
                    </Tooltip>
                )
            },
            {
                key: "/resource/os_images",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">OS Images</div>} placement="right">
                        OS Images
                    </Tooltip>
                )
            },
            // {
            //     key: "/resource/auth_management",
            //     label: (
            //         <Tooltip title={<div className="fixed-tooltip">Authority Management</div>} placement="right">
            //             Authority Management
            //         </Tooltip>
            //     ),
            //     children: [
            //         {
            //             key: "/resource/auth_management/device_license_management",
            //             label: (
            //                 <Tooltip
            //                     className="menu-dot"
            //                     title={<div className="fixed-tooltip">Device License Management</div>}
            //                     placement="right"
            //                 >
            //                     Device License Management
            //                 </Tooltip>
            //             )
            //         },
            //         {
            //             key: "/resource/auth_management/group_management",
            //             label: (
            //                 <Tooltip
            //                     className="menu-dot"
            //                     title={<div className="fixed-tooltip">Group Management</div>}
            //                     placement="right"
            //                 >
            //                     Group Management
            //                 </Tooltip>
            //             )
            //         }
            //     ]
            // },
            {
                key: "/resource/device_licenses",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">Device Licenses</div>} placement="right">
                        Device Licenses
                    </Tooltip>
                )
            },
            {
                key: "/resource/group_management",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">Group Management</div>} placement="right">
                        Group Management
                    </Tooltip>
                )
            },
            {
                key: "/resource/pools",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">Pools</div>} placement="right">
                        Pools
                    </Tooltip>
                ),
                children: [
                    {
                        key: "/resource/pools/ip_pools",
                        label: (
                            <Tooltip
                                className="menu-dot"
                                title={<div className="fixed-tooltip">IP Pools</div>}
                                placement="right"
                            >
                                IP Pools
                            </Tooltip>
                        )
                    },
                    {
                        key: "/resource/pools/asn_pools",
                        label: (
                            <Tooltip
                                className="menu-dot"
                                title={<div className="fixed-tooltip">ASN Pools</div>}
                                placement="right"
                            >
                                ASN Pools
                            </Tooltip>
                        )
                    },
                    {
                        key: "/resource/pools/vni_pools",
                        label: (
                            <Tooltip
                                className="menu-dot"
                                title={<div className="fixed-tooltip">VNI Pools</div>}
                                placement="right"
                            >
                                VNI Pools
                            </Tooltip>
                        )
                    }
                ]
            }
        ]
    },
    // {
    //     label: (
    //         <Tooltip title={<div className="fixed-tooltip">Telemetry</div>} placement="right">
    //             Telemetry
    //         </Tooltip>
    //     ),
    //     key: "/telemetry",
    //     icon: <Icon component={telemetrySvg} />,
    //     children: [
    //         {
    //             key: "/telemetry/switch",
    //             label: (
    //                 <Tooltip title={<div className="fixed-tooltip">Global View</div>} placement="right">
    //                     Switch
    //                 </Tooltip>
    //             ),
    //             children: [
    //                 {
    //                     key: "/telemetry/switch/optical_modules_pre",
    //                     label: (
    //                         <Tooltip
    //                             className="menu-dot"
    //                             title={<div className="fixed-tooltip">Optical Modules</div>}
    //                             placement="right"
    //                         >
    //                             Optical Modules
    //                         </Tooltip>
    //                     )
    //                 },
    //                 {
    //                     key: "/telemetry/switch/roce",
    //                     label: (
    //                         <Tooltip
    //                             className="menu-dot"
    //                             title={<div className="fixed-tooltip">RoCE</div>}
    //                             placement="right"
    //                         >
    //                             RoCE
    //                         </Tooltip>
    //                     )
    //                 }
    //             ]
    //         }
    //     ]
    // },
    // {
    //     label: (
    //         <Tooltip title={<div className="fixed-tooltip">Service</div>} placement="right">
    //             Service
    //         </Tooltip>
    //     ),
    //     key: "/service",
    //     icon: <Icon component={serviceSvg} />,
    //     children: [
    //         {
    //             key: "/service/switch",
    //             label: (
    //                 <Tooltip title={<div className="fixed-tooltip">Switch</div>} placement="right">
    //                     Switch
    //                 </Tooltip>
    //             )
    //         },
    //         {
    //             key: "/service/hosts",
    //             label: (
    //                 <Tooltip title={<div className="fixed-tooltip">Hosts</div>} placement="right">
    //                     Hosts
    //                 </Tooltip>
    //             ),
    //             children: [
    //                 {
    //                     key: "/service/hosts/device_discovery",
    //                     label: (
    //                         <Tooltip
    //                             className="menu-dot"
    //                             title={<div className="fixed-tooltip">Device Discovery</div>}
    //                             placement="right"
    //                         >
    //                             Device Discovery
    //                         </Tooltip>
    //                     )
    //                 },
    //                 {
    //                     key: "/service/hosts/inventory",
    //                     label: (
    //                         <Tooltip
    //                             className="menu-dot"
    //                             title={<div className="fixed-tooltip">Inventory</div>}
    //                             placement="right"
    //                         >
    //                             Inventory
    //                         </Tooltip>
    //                     )
    //                 }
    //             ]
    //         },
    //         {
    //             key: "/service/NICs",
    //             label: (
    //                 <Tooltip title={<div className="fixed-tooltip">NICs</div>} placement="right">
    //                     NICs
    //                 </Tooltip>
    //             ),
    //             children: [
    //                 {
    //                     key: "/service/NICs/inventory",
    //                     label: (
    //                         <Tooltip
    //                             className="menu-dot"
    //                             title={<div className="fixed-tooltip">Inventory</div>}
    //                             placement="right"
    //                         >
    //                             Inventory
    //                         </Tooltip>
    //                     )
    //                 },
    //                 {
    //                     key: "/service/NICs/modules_overview",
    //                     label: (
    //                         <Tooltip
    //                             className="menu-dot"
    //                             title={<div className="fixed-tooltip">Modules Overview</div>}
    //                             placement="right"
    //                         >
    //                             Modules Overview
    //                         </Tooltip>
    //                     )
    //                 },
    //                 {
    //                     key: "/service/NICs/monitoring",
    //                     label: (
    //                         <Tooltip
    //                             className="menu-dot"
    //                             title={<div className="fixed-tooltip">Monitoring</div>}
    //                             placement="right"
    //                         >
    //                             Monitoring
    //                         </Tooltip>
    //                     )
    //                 },
    //                 {
    //                     key: "/service/NICs/RoCE_configuration",
    //                     label: (
    //                         <Tooltip
    //                             className="menu-dot"
    //                             title={<div className="fixed-tooltip">RoCE Configuration</div>}
    //                             placement="right"
    //                         >
    //                             RoCE Configuration
    //                         </Tooltip>
    //                     )
    //                 }
    //             ]
    //         },
    //         {
    //             key: "/service/global_configuration",
    //             label: (
    //                 <Tooltip title={<div className="fixed-tooltip">Global Configuration</div>} placement="right">
    //                     Global Configuration
    //                 </Tooltip>
    //             )
    //         },
    //         {
    //             key: "/service/switch_configuration",
    //             label: (
    //                 <Tooltip title={<div className="fixed-tooltip">Switch Configuration</div>} placement="right">
    //                     Switch Configuration
    //                 </Tooltip>
    //             )
    //         },
    //         {
    //             key: "/service/config_files_view",
    //             label: (
    //                 <Tooltip title={<div className="fixed-tooltip">Config Files View</div>} placement="right">
    //                     Config Files View
    //                 </Tooltip>
    //             )
    //         },
    //         {
    //             key: "/service/switch_model",
    //             label: (
    //                 <Tooltip title={<div className="fixed-tooltip">Switch Model</div>} placement="right">
    //                     Switch Model
    //                 </Tooltip>
    //             )
    //         },
    //         {
    //             key: "/service/system_configuration",
    //             label: (
    //                 <Tooltip title={<div className="fixed-tooltip">System Configuration</div>} placement="right">
    //                     System Configuration
    //                 </Tooltip>
    //             )
    //         },
    //         {
    //             key: "/service/config_template",
    //             label: (
    //                 <Tooltip title={<div className="fixed-tooltip">Config Template</div>} placement="right">
    //                     Config Template
    //                 </Tooltip>
    //             )
    //         }
    //     ]
    // },
    {
        label: (
            <Tooltip title={<div className="fixed-tooltip">Device</div>} placement="right">
                Device
            </Tooltip>
        ),
        key: "/device",
        icon: <Icon component={serviceSvg} />,
        children: [
            {
                key: "/device/switches",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">Switches</div>} placement="right">
                        Switches
                    </Tooltip>
                )
            },
            {
                key: "/device/hosts",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">Hosts</div>} placement="right">
                        Hosts
                    </Tooltip>
                )
            },
            {
                key: "/device/NICs",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">NICs</div>} placement="right">
                        NICs
                    </Tooltip>
                )
            },
            {
                key: "/device/device_profiles",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">Device Profiles</div>} placement="right">
                        Device Profiles
                    </Tooltip>
                )
            },
            {
                key: "/device/config_templates",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">Config Templates</div>} placement="right">
                        Config Templates
                    </Tooltip>
                )
            }
        ]
    },
    {
        label: (
            <Tooltip title={<div className="fixed-tooltip">Service Provision</div>} placement="right">
                Service Provision
            </Tooltip>
        ),
        key: "/service_provision",
        icon: <Icon component={provisionSvg} />,
        children: [
            {
                key: "/service_provision/logical_networks",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">Logical Networks</div>} placement="right">
                        Logical Networks
                    </Tooltip>
                )
            },
            {
                key: "/service_provision/logical_routers",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">Logical Routers</div>} placement="right">
                        Logical Routers
                    </Tooltip>
                )
            },
            {
                key: "/service_provision/logical_switches",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">Logical Switches</div>} placement="right">
                        Logical Switches
                    </Tooltip>
                )
            },
            {
                key: "/service_provision/network_access",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">Network Access</div>} placement="right">
                        Network Access
                    </Tooltip>
                )
            }
        ]
    },
    {
        label: (
            <Tooltip title={<div className="fixed-tooltip">Monitor</div>} placement="right">
                Monitor
            </Tooltip>
        ),
        key: "/monitor",
        icon: <Icon component={monitorSvg} />,
        children: [
            {
                key: "/monitor/alerts",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">Alerts</div>} placement="right">
                        Alerts
                    </Tooltip>
                ),
                children: [
                    {
                        key: "/monitor/alerts/alert_list",
                        label: (
                            <Tooltip
                                className="menu-dot"
                                title={<div className="fixed-tooltip">Alert List</div>}
                                placement="right"
                            >
                                Alert List
                            </Tooltip>
                        )
                    },
                    {
                        key: "/monitor/alerts/notification_rules",
                        label: (
                            <Tooltip
                                className="menu-dot"
                                title={<div className="fixed-tooltip">Notification Rules</div>}
                                placement="right"
                            >
                                Notification Rules
                            </Tooltip>
                        )
                    },
                    {
                        key: "/monitor/alerts/notification_history",
                        label: (
                            <Tooltip
                                className="menu-dot"
                                title={<div className="fixed-tooltip">Notification History</div>}
                                placement="right"
                            >
                                Notification History
                            </Tooltip>
                        )
                    }
                ]
            },
            // {
            //     // 侧边栏删
            //     key: "/monitor/network",
            //     label: (
            //         <Tooltip title={<div className="fixed-tooltip">Network</div>} placement="right">
            //             Network
            //         </Tooltip>
            //     ),
            //     children: [
            //         {
            //             key: "/monitor/network/DLB",
            //             label: (
            //                 <Tooltip
            //                     className="menu-dot"
            //                     title={<div className="fixed-tooltip">DLB</div>}
            //                     placement="right"
            //                 >
            //                     DLB
            //                 </Tooltip>
            //             )
            //         }
            //     ]
            // },
            // {
            //     key: "/monitor/RoCE_counters",
            //     label: (
            //         <Tooltip title={<div className="fixed-tooltip">RoCE Counters</div>} placement="right">
            //             RoCE Counters
            //         </Tooltip>
            //     )
            //     // children: [
            //     //     {
            //     //         key: "/monitor/RoCE_counters/switch",
            //     //         label: (
            //     //             <Tooltip
            //     //                 className="menu-dot"
            //     //                 title={<div className="fixed-tooltip">Switch</div>}
            //     //                 placement="right"
            //     //             >
            //     //                 Switch
            //     //             </Tooltip>
            //     //         )
            //     //     },
            //     //     {
            //     //         key: "/monitor/RoCE_counters/NICs",
            //     //         label: (
            //     //             <Tooltip
            //     //                 className="menu-dot"
            //     //                 title={<div className="fixed-tooltip">NICs</div>}
            //     //                 placement="right"
            //     //             >
            //     //                 NICs
            //     //             </Tooltip>
            //     //         )
            //     //     }
            //     // ]
            // },
            {
                key: "/monitor/telemetry",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">Telemetry</div>} placement="right">
                        Telemetry
                    </Tooltip>
                ),
                children: [
                    {
                        key: "/monitor/telemetry/performance_statistics",
                        label: (
                            <Tooltip
                                className="menu-dot"
                                title={<div className="fixed-tooltip">Performance Statistics</div>}
                                placement="right"
                            >
                                Performance Statistics
                            </Tooltip>
                        )
                    },
                    {
                        key: "/monitor/telemetry/optical_modules",
                        label: (
                            <Tooltip
                                className="menu-dot"
                                title={<div className="fixed-tooltip">Optical Modules</div>}
                                placement="right"
                            >
                                Optical Modules
                            </Tooltip>
                        )
                    },
                    {
                        key: "/monitor/telemetry/RoCE_counters",
                        label: (
                            <Tooltip
                                className="menu-dot"
                                title={<div className="fixed-tooltip">RoCE Counters</div>}
                                placement="right"
                            >
                                RoCE Counters
                            </Tooltip>
                        )
                    }
                ]
            },
            {
                key: "/monitor/event_log",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">Event Log</div>} placement="right">
                        Event Log
                    </Tooltip>
                )
            }
        ]
    },
    {
        label: (
            <Tooltip title={<div className="fixed-tooltip">Maintain</div>} placement="right">
                Maintain
            </Tooltip>
        ),
        key: "/maintain",
        icon: <Icon component={maintainSvg} />,
        children: [
            {
                key: "/maintain/automation",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">Automation</div>} placement="right">
                        Automation
                    </Tooltip>
                ),
                children: [
                    {
                        key: "/maintain/automation/playbooks",
                        label: (
                            <Tooltip
                                className="menu-dot"
                                title={<div className="fixed-tooltip">Playbooks</div>}
                                placement="right"
                            >
                                Playbooks
                            </Tooltip>
                        )
                    },
                    {
                        key: "/maintain/automation/ansible_jobs",
                        label: (
                            <Tooltip
                                className="menu-dot"
                                title={<div className="fixed-tooltip">Ansible Jobs</div>}
                                placement="right"
                            >
                                Ansible Jobs
                            </Tooltip>
                        )
                    },
                    {
                        key: "/maintain/automation/scheduler",
                        label: (
                            <Tooltip
                                className="menu-dot"
                                title={<div className="fixed-tooltip">Scheduler</div>}
                                placement="right"
                            >
                                Scheduler
                            </Tooltip>
                        )
                    }
                ]
            },
            {
                key: "/maintain/system_backup",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">System Backup</div>} placement="right">
                        System Backup
                    </Tooltip>
                )
            },
            {
                key: "/maintain/web_access",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">Web Access</div>} placement="right">
                        Web Access
                    </Tooltip>
                )
            }
        ]
    },
    {
        label: (
            <Tooltip title={<div className="fixed-tooltip">System</div>} placement="right">
                System
            </Tooltip>
        ),
        key: "/system",
        icon: <Icon component={settingSvg} />,
        children: [
            {
                key: "/system/user_management",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">User Management</div>} placement="right">
                        User Management
                    </Tooltip>
                )
            },

            {
                key: "/system/email_settings",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">Email Settings</div>} placement="right">
                        Email Settings
                    </Tooltip>
                )
            },
            {
                key: "/system/software_licenses",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">Software Licenses</div>} placement="right">
                        Software Licenses
                    </Tooltip>
                ),
                children: [
                    {
                        key: "/system/software_licenses/licenses_view",
                        label: (
                            <Tooltip
                                className="menu-dot"
                                title={<div className="fixed-tooltip">Licenses View</div>}
                                placement="right"
                            >
                                Licenses View
                            </Tooltip>
                        )
                    },
                    {
                        key: "/system/software_licenses/licenses_management",
                        label: (
                            <Tooltip
                                className="menu-dot"
                                title={<div className="fixed-tooltip">Licenses Management</div>}
                                placement="right"
                            >
                                Licenses Management
                            </Tooltip>
                        )
                    },
                    {
                        key: "/system/software_licenses/licenses_log",
                        label: (
                            <Tooltip
                                className="menu-dot"
                                title={<div className="fixed-tooltip">Licenses Log</div>}
                                placement="right"
                            >
                                Licenses Log
                            </Tooltip>
                        )
                    }
                ]
            }
        ]
    }
];

const campusItems = [
    {
        label: <>Dashboard</>,
        key: "/dashboard",
        icon: <Icon component={dashBoardSvg} />,
        children: [
            {
                key: "/dashboard/global_view",
                label: <>Global View</>
            },
            {
                key: "/dashboard/switch_view",
                label: <>Switch View</>
            },
            {
                key: "/dashboard/telemetry_dashboard",
                label: <>Telemetry Dashboard</>
            }
        ]
    },
    {
        label: <>Resource</>,
        key: "/resource",
        icon: <Icon component={resourceSvg} />,
        children: [
            {
                key: "/resource/upgrade_management",
                label: <>Upgrade Management</>
            },
            {
                key: "/resource/auth_management",
                label: <>Authority Management</>,
                children: [
                    {
                        key: "/resource/auth_management/device_license_management",
                        label: <div className="menu-dot">Device License Management</div>
                    },
                    {
                        key: "/resource/auth_management/group_management",
                        label: <div className="menu-dot">Group Management</div>
                    },
                    {
                        key: "/resource/auth_management/site_management",
                        label: <div className="menu-dot">Site Management</div>
                    }
                ]
            }
        ]
    },
    {
        label: <>Service</>,
        key: "/service",
        icon: <Icon component={serviceSvg} />,
        children: [
            {
                key: "/service/switch",
                label: <>Switch</>
            },
            {
                key: "/service/global_configuration",
                label: <>Global Configuration</>
            },
            {
                key: "/service/switch_configuration",
                label: <>Switch Configuration</>
            },
            {
                key: "/service/config_file_view",
                label: <>Config File View</>
            },
            {
                key: "/service/switch_model",
                label: <>Switch Model</>
            },
            {
                key: "/service/system_configuration",
                label: <>System Config</>
            },
            {
                key: "/service/config_templates",
                label: <>Config Templates</>
            }
        ]
    },
    {
        label: <>Monitor</>,
        key: "/monitor",
        icon: <Icon component={monitorSvg} />,
        children: [
            {
                key: "/monitor/alerts",
                label: <>Alerts</>,
                children: [
                    {
                        key: "/monitor/alerts/alert_list",
                        label: <div className="menu-dot">Alert List</div>
                    },
                    {
                        key: "/monitor/alerts/notification_rules",
                        label: <div className="menu-dot">Notification Rules</div>
                    },
                    {
                        key: "/monitor/alerts/notification_history",
                        label: <div className="menu-dot">Notification History</div>
                    }
                ]
            },
            {
                key: "/monitor/wired_clients",
                label: <>Wired Clients</>
            }
        ]
    },
    {
        label: <>Maintain</>,
        key: "/maintain",
        icon: <Icon component={maintainSvg} />,
        children: [
            {
                key: "/maintain/automation",
                label: <>Automation</>,
                children: [
                    {
                        key: "/maintain/automation/playbooks",
                        label: <div className="menu-dot">Playbooks</div>
                    },
                    {
                        key: "/maintain/automation/other_devices",
                        label: <div className="menu-dot">Other Devices</div>
                    },
                    {
                        key: "/maintain/automation/ansible_jobs",
                        label: <div className="menu-dot">Ansible Jobs</div>
                    },
                    {
                        key: "/maintain/automation/schedule",
                        label: <div className="menu-dot">Schedule</div>
                    }
                ]
            },
            {
                key: "/maintain/system_backup",
                label: <>System Backup</>
            },
            {
                key: "/maintain/web_access",
                label: <>Web Access</>
            }
        ]
    },
    {
        label: <>System</>,
        key: "/system",
        icon: <Icon component={settingSvg} />,
        children: [
            {
                key: "/system/user_management",
                label: <>User Management</>
            },
            {
                key: "/system/email_settings",
                label: <>Email Settings</>
            },
            {
                key: "/system/software_licenses",
                label: <>Software Licenses</>,
                children: [
                    {
                        key: "/system/software_licenses/license_view",
                        label: <div className="menu-dot">Licenses View</div>
                    },
                    {
                        key: "/system/software_licenses/license_management",
                        label: <div className="menu-dot">Licenses Management</div>
                    },
                    {
                        key: "/system/software_licenses/license_log",
                        label: <div className="menu-dot">Licenses Log</div>
                    }
                ]
            }
        ]
    },
    {
        label: <>Topo</>,
        key: "/topo",
        icon: <Icon component={topologySvg} />,
        children: [
            {
                key: "/topo/topology",
                label: <>Topology</>
            },
            {
                key: "/topo/campus_fabric",
                label: <>Campus Fabric</>
            },
            {
                key: "/topo/switch_templates",
                label: <>Switch Templates</>
            }
        ]
    }
];

const smbItems = [
    {
        label: <>Dashboard</>,
        key: "/dashboard",
        icon: <Icon component={dashBoardSvg} />,
        children: [
            {
                key: "/dashboard/global_view",
                label: <>Global View</>
            },
            {
                key: "/dashboard/switch_view",
                label: <>Switch View</>
            },
            {
                key: "/dashboard/telemetry_dashboard",
                label: <>Telemetry Dashboard</>
            },
            {
                key: "/dashboard/wireless_view",
                label: <>Wireless View</>
            }
        ]
    },
    {
        label: <>Resource</>,
        key: "/resource",
        icon: <Icon component={resourceSvg} />,
        children: [
            {
                key: "/resource/upgrade_management/ap",
                label: <>Upgrade Management</>
            },
            {
                key: "/resource/auth_management",
                label: <>Authority Management</>,
                children: [
                    {
                        key: "/resource/auth_management/device_license_management",
                        label: <div className="menu-dot">Device License Management</div>
                    },
                    {
                        key: "/resource/auth_management/group_management",
                        label: <div className="menu-dot">Group Management</div>
                    },
                    {
                        key: "/resource/auth_management/site_management",
                        label: <div className="menu-dot">Site Management</div>
                    }
                ]
            }
        ]
    },
    {
        label: (
            <div style={{position: "relative", display: "inline-block"}}>
                <span style={{fontWeight: "bold"}}>Wireless</span>
                <span
                    style={{
                        position: "absolute",
                        top: "-8px",
                        left: "60px",
                        right: "-25px",
                        fontSize: "13px",
                        color: "red",
                        padding: "1px 4px",
                        borderRadius: "3px",
                        fontWeight: "bold"
                    }}
                >
                    v1.0
                </span>
            </div>
        ),
        key: "/wireless",
        icon: <Icon component={serviceSvg} />,
        children: [
            // {
            //     key: "/wireless/login",
            //     label: <>Login</>
            // },
            {
                key: "/wireless/entities",
                label: <>Manage</>
            },
            {
                key: "/wireless/devices",
                label: <>Devices</>
                // children: [
                //     {
                //         key: "/wireless/devices",
                //         label: <div className="menu-dot">All</div>
                //     },
                //     // {
                //     //     key: "/wireless/devices/dashboard",
                //     //     label: <div className="menu-dot">Dashboard</div>
                //     // },
                //     {
                //         key: "/wireless/devices/blacklist",
                //         label: <div className="menu-dot">Blacklist</div>
                //     }
                // ]
            },
            // {
            //     key: "/wireless/firmware",
            //     label: <>Firmware</>,
            //     children: [
            //         {
            //             key: "/wireless/firmware/list",
            //             label: <div className="menu-dot">All</div>
            //         },
            //         {
            //             key: "/wireless/firmware/dashboard",
            //             label: <div className="menu-dot">Dashboard</div>
            //         }
            //     ]
            // },
            // {
            //     key: "/wireless/scripts",
            //     label: <>Scripts</>
            // },
            // {
            //     key: "/wireless/defaults",
            //     label: <>Defaults</>,
            //     children: [
            //         {
            //             key: "/wireless/defaults/configuration",
            //             label: <div className="menu-dot">Configuration</div>
            //         },
            //         {
            //             key: "/wireless/defaults/firmware",
            //             label: <div className="menu-dot">Firmware</div>
            //         }
            //     ]
            // },
            // {
            //     key: "/wireless/maps",
            //     label: <>Maps</>
            // },
            {
                key: "/wireless/inventory",
                label: <>Inventory</>
            },
            // {
            //     key: "/wireless/operators",
            //     label: <>Operators</>
            // },
            // {
            //     key: "/wireless/logs",
            //     label: <>Logs</>,
            //     children: [
            //         {
            //             key: "/wireless/logs/devices",
            //             label: <div className="menu-dot">Devices</div>
            //         },
            //         {
            //             key: "/wireless/logs/controller",
            //             label: <div className="menu-dot">Controller</div>
            //         },
            //         {
            //             key: "/wireless/logs/venues",
            //             label: <div className="menu-dot">Venues</div>
            //         },
            //         {
            //             key: "/wireless/logs/provisioning",
            //             label: <div className="menu-dot">Provisioning</div>
            //         },
            //         {
            //             key: "/wireless/logs/security",
            //             label: <div className="menu-dot">Security</div>
            //         }
            //         // {
            //         //     key: "/wireless/logs/firmware",
            //         //     label: <div className="menu-dot">Firmware</div>
            //         // }
            //     ]
            // },
            // // {
            // //     key: "/wireless/users",
            // //     label: <>Users</>
            // // }
            // {
            //     key: "/wireless/system",
            //     label: <>System</>,
            //     children: [
            //         {
            //             key: "/wireless/system/advanced",
            //             label: <div className="menu-dot">Advanced</div>
            //         },
            //         {
            //             key: "/wireless/system/configuration",
            //             label: <div className="menu-dot">Configuration</div>
            //         },
            //         {
            //             key: "/wireless/system/openroaming",
            //             label: <div className="menu-dot">Openroaming</div>
            //         }
            //         // ,
            //         // {
            //         //     key: "/wireless/system/monitoring",
            //         //     label: <div className="menu-dot">Monitoring</div>
            //         // },
            //         // {
            //         //     key: "/wireless/system/services",
            //         //     label: <div className="menu-dot">Services</div>
            //         // }
            //     ]
            // }
            // {
            //     key: "/wireless/layout",
            //     label: <>Layout</>
            // },
            // {
            //     key: "/wireless/logout",
            //     label: <>Logout</>
            // }
            {
                key: "/wireless/profile",
                label: <>Wireless Profile</>
            }
        ]
    },
    {
        label: <>Service</>,
        key: "/service",
        icon: <Icon component={serviceSvg} />,
        children: [
            {
                key: "/service/switches",
                label: <>Switches</>
            },
            {
                key: "/service/global_configuration",
                label: <>Global Configuration</>
            },
            {
                key: "/service/switch_configuration",
                label: <>Switch Configuration</>
            },
            {
                key: "/service/config_file_view",
                label: <>Config File View</>
            },
            {
                key: "/service/switch_model",
                label: <>Switch Model</>
            },
            {
                key: "/service/system_configuration",
                label: <>System Config</>
            },
            {
                key: "/service/config_templates",
                label: <>Config Templates</>
            }
        ]
    },
    {
        label: (
            <Tooltip title={<div className="fixed-tooltip">Device</div>} placement="right">
                Device
            </Tooltip>
        ),
        key: "/device",
        icon: <Icon component={serviceSvg} />,
        children: [
            {
                key: "/device/switches",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">Switches</div>} placement="right">
                        Switches
                    </Tooltip>
                )
            },
            {
                key: "/device/device_profiles",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">Device Profiles</div>} placement="right">
                        Device Profiles
                    </Tooltip>
                )
            },
            {
                key: "/device/config_templates",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">Config Templates</div>} placement="right">
                        Config Templates
                    </Tooltip>
                )
            }
        ]
    },
    {
        label: <>Monitor</>,
        key: "/monitor",
        icon: <Icon component={monitorSvg} />,
        children: [
            {
                key: "/monitor/alerts",
                label: <>Alert</>,
                children: [
                    {
                        key: "/monitor/alerts/alert_list",
                        label: <div className="menu-dot">Alert List</div>
                    },
                    {
                        key: "/monitor/alerts/notification_rules",
                        label: <div className="menu-dot">Notification Rules</div>
                    },
                    {
                        key: "/monitor/alerts/notification_history",
                        label: <div className="menu-dot">Notification History</div>
                    }
                ]
            },
            {
                key: "/monitor/wired_clients",
                label: <>Wired Clients</>
            },
            {
                key: "/monitor/wireless_clients",
                label: <>Wireless Clients</>
            }
        ]
    },
    {
        label: <>Maintain</>,
        key: "/maintain",
        icon: <Icon component={maintainSvg} />,
        children: [
            {
                key: "/maintain/automation",
                label: <>Automation</>,
                children: [
                    {
                        key: "/maintain/automation/playbooks",
                        label: <div className="menu-dot">Playbooks</div>
                    },
                    {
                        key: "/maintain/automation/other_devices",
                        label: <div className="menu-dot">Other Devices</div>
                    },
                    {
                        key: "/maintain/automation/ansible_jobs",
                        label: <div className="menu-dot">Ansible Jobs</div>
                    },
                    {
                        key: "/maintain/automation/schedule",
                        label: <div className="menu-dot">Schedule</div>
                    }
                ]
            },
            {
                key: "/maintain/system_backup",
                label: <>System Backup</>
            },
            {
                key: "/maintain/web_access",
                label: <>Web Access</>
            }
        ]
    },
    {
        label: <>System</>,
        key: "/system",
        icon: <Icon component={settingSvg} />,
        children: [
            {
                key: "/system/user_management",
                label: <>User Management</>
            },
            {
                key: "/system/email_settings",
                label: <>Email Settings</>
            },
            {
                key: "/system/software_licenses",
                label: <>Software Licenses</>,
                children: [
                    {
                        key: "/system/software_licenses/license_view",
                        label: <div className="menu-dot">Licenses View</div>
                    },
                    {
                        key: "/system/software_licenses/license_management",
                        label: <div className="menu-dot">Licenses Management</div>
                    },
                    {
                        key: "/system/software_licenses/license_log",
                        label: <div className="menu-dot">Licenses Log</div>
                    }
                ]
            }
        ]
    },
    {
        label: <>Topo</>,
        key: "/topo",
        icon: <Icon component={topologySvg} />,
        children: [
            {
                key: "/topo/topology",
                label: <>Topology</>
            },
            {
                key: "/topo/campus_fabric",
                label: <>Campus Fabric</>
            },
            {
                key: "/topo/switch_templates",
                label: <>Switch Templates</>
            }
        ]
    }
];

const tItems = [
    {
        label: (
            <Tooltip title={<div className="fixed-tooltip">Dashboard</div>} placement="right">
                Dashboard
            </Tooltip>
        ),
        key: "/dashboard",
        icon: <Icon component={dashBoardSvg} />,
        children: [
            {
                key: "/dashboard/global_view",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">Global View</div>} placement="right">
                        Global View
                    </Tooltip>
                )
            },
            {
                key: "/dashboard/switch_view",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">Switch View</div>} placement="right">
                        Switch View
                    </Tooltip>
                )
            },
            {
                key: "/dashboard/telemetry_dashboard",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">Telemetry Dashboard</div>} placement="right">
                        Telemetry Dashboard
                    </Tooltip>
                )
            }
        ]
    },
    {
        label: (
            <Tooltip title={<div className="fixed-tooltip">Resource</div>} placement="right">
                Resource
            </Tooltip>
        ),
        key: "/resource",
        icon: <Icon component={resourceSvg} />,
        children: [
            {
                key: "/resource/upgrade_management",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">Upgrade Management</div>} placement="right">
                        Upgrade Management
                    </Tooltip>
                )
            },
            {
                key: "/resource/auth_management",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">Authority Management</div>} placement="right">
                        Authority Management
                    </Tooltip>
                ),
                children: [
                    {
                        key: "/resource/auth_management/device_license_management",
                        label: (
                            <Tooltip
                                className="menu-dot"
                                title={<div className="fixed-tooltip">Device License Management</div>}
                                placement="right"
                            >
                                Device License Management
                            </Tooltip>
                        )
                    },
                    {
                        key: "/resource/auth_management/group_management",
                        label: (
                            <Tooltip
                                className="menu-dot"
                                title={<div className="fixed-tooltip">Group Management</div>}
                                placement="right"
                            >
                                Group Management
                            </Tooltip>
                        )
                    },
                    {
                        key: "/resource/auth_management/site_management",
                        label: (
                            <Tooltip
                                className="menu-dot"
                                title={<div className="fixed-tooltip">Site Management</div>}
                                placement="right"
                            >
                                Site Management
                            </Tooltip>
                        )
                    }
                ]
            }
        ]
    },
    {
        label: (
            <Tooltip title={<div className="fixed-tooltip">Service</div>} placement="right">
                Service
            </Tooltip>
        ),
        key: "/service",
        icon: <Icon component={serviceSvg} />,
        children: [
            {
                key: "/service/switch",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">Switch</div>} placement="right">
                        Switch
                    </Tooltip>
                )
            },
            {
                key: "/service/global_configuration",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">Global Configuration</div>} placement="right">
                        Global Configuration
                    </Tooltip>
                )
            },
            {
                key: "/service/switch_configuration",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">Switch Configuration</div>} placement="right">
                        Switch Configuration
                    </Tooltip>
                )
            },
            {
                key: "/service/config_files_view",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">Config Files View</div>} placement="right">
                        Config Files View
                    </Tooltip>
                )
            },
            {
                key: "/service/switch_model",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">Switch Model</div>} placement="right">
                        Switch Model
                    </Tooltip>
                )
            },
            {
                key: "/service/system_configuration",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">System Configuration</div>} placement="right">
                        System Configuration
                    </Tooltip>
                )
            },
            {
                key: "/service/config_template",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">Config Template</div>} placement="right">
                        Config Template
                    </Tooltip>
                )
            }
        ]
    },
    {
        label: (
            <Tooltip title={<div className="fixed-tooltip">Monitor</div>} placement="right">
                Monitor
            </Tooltip>
        ),
        key: "/monitor",
        icon: <Icon component={monitorSvg} />,
        children: [
            {
                key: "/monitor/alarm",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">Alarm</div>} placement="right">
                        Alarm
                    </Tooltip>
                )
            },
            {
                key: "/monitor/network",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">Network</div>} placement="right">
                        Network
                    </Tooltip>
                ),
                children: [
                    {
                        key: "/monitor/network/dlb",
                        label: (
                            <Tooltip
                                className="menu-dot"
                                title={<div className="fixed-tooltip">DLB</div>}
                                placement="right"
                            >
                                DLB
                            </Tooltip>
                        )
                    }
                ]
            }
        ]
    },
    {
        label: (
            <Tooltip title={<div className="fixed-tooltip">Maintain</div>} placement="right">
                Maintain
            </Tooltip>
        ),
        key: "/maintain",
        icon: <Icon component={maintainSvg} />,
        children: [
            {
                key: "/maintain/automation",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">Automation</div>} placement="right">
                        Automation
                    </Tooltip>
                ),
                children: [
                    {
                        key: "/maintain/automation/playbooks",
                        label: (
                            <Tooltip
                                className="menu-dot"
                                title={<div className="fixed-tooltip">Playbooks</div>}
                                placement="right"
                            >
                                Playbooks
                            </Tooltip>
                        )
                    },
                    {
                        key: "/maintain/automation/other_devices",
                        label: (
                            <Tooltip
                                className="menu-dot"
                                title={<div className="fixed-tooltip">Other Devices</div>}
                                placement="right"
                            >
                                Other Devices
                            </Tooltip>
                        )
                    },
                    {
                        key: "/maintain/automation/ansible_jobs_list",
                        label: (
                            <Tooltip
                                className="menu-dot"
                                title={<div className="fixed-tooltip">Ansible Jobs List</div>}
                                placement="right"
                            >
                                Ansible Jobs List
                            </Tooltip>
                        )
                    },
                    {
                        key: "/maintain/automation/schedule",
                        label: (
                            <Tooltip
                                className="menu-dot"
                                title={<div className="fixed-tooltip">Schedule</div>}
                                placement="right"
                            >
                                Schedule
                            </Tooltip>
                        )
                    }
                ]
            },
            {
                key: "/maintain/system_backup",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">System Backup</div>} placement="right">
                        System Backup
                    </Tooltip>
                )
            },
            {
                key: "/maintain/cli_configuration",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">CLI Configuration</div>} placement="right">
                        CLI Configuration
                    </Tooltip>
                )
            }
        ]
    },
    {
        label: (
            <Tooltip title={<div className="fixed-tooltip">System</div>} placement="right">
                System
            </Tooltip>
        ),
        key: "/system",
        icon: <Icon component={settingSvg} />,
        children: [
            {
                key: "/system/user_management",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">User Management</div>} placement="right">
                        User Management
                    </Tooltip>
                )
            },
            {
                key: "/system/software_license",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">Software License</div>} placement="right">
                        Software License
                    </Tooltip>
                ),
                children: [
                    {
                        key: "/system/software_license/license_view",
                        label: (
                            <Tooltip
                                className="menu-dot"
                                title={<div className="fixed-tooltip">License View</div>}
                                placement="right"
                            >
                                License View
                            </Tooltip>
                        )
                    },
                    {
                        key: "/system/software_license/license_management",
                        label: (
                            <Tooltip
                                className="menu-dot"
                                title={<div className="fixed-tooltip">License Management</div>}
                                placement="right"
                            >
                                License Management
                            </Tooltip>
                        )
                    },
                    {
                        key: "/system/software_license/license_log",
                        label: (
                            <Tooltip
                                className="menu-dot"
                                title={<div className="fixed-tooltip">License Log</div>}
                                placement="right"
                            >
                                License Log
                            </Tooltip>
                        )
                    }
                ]
            }
        ]
    },
    {
        label: (
            <Tooltip title={<div className="fixed-tooltip">System</div>} placement="right">
                Topo
            </Tooltip>
        ),
        key: "/topo",
        icon: <Icon component={topologySvg} />,
        children: [
            {
                key: "/topo/topology",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">Topology</div>} placement="right">
                        Topology
                    </Tooltip>
                )
            }
        ]
    }
];

const superItems = [
    {
        label: (
            <Tooltip title={<div className="fixed-tooltip">Dashboard</div>} placement="right">
                Dashboard
            </Tooltip>
        ),
        key: "/dashboard",
        icon: <Icon component={dashBoardSvg} />,
        children: [
            {
                key: "/dashboard/global_view",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">Global View</div>} placement="right">
                        Global View
                    </Tooltip>
                )
            },
            {
                key: "/dashboard/switch_view",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">Switch View</div>} placement="right">
                        Switch View
                    </Tooltip>
                )
            },
            {
                key: "/dashboard/telemetry_dashboard",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">Telemetry Dashboard</div>} placement="right">
                        Telemetry Dashboard
                    </Tooltip>
                )
            }
        ]
    },
    {
        label: (
            <Tooltip title={<div className="fixed-tooltip">Resource</div>} placement="right">
                Resource
            </Tooltip>
        ),
        key: "/resource",
        icon: <Icon component={resourceSvg} />,
        children: [
            {
                key: "/resource/upgrade_management",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">Upgrade Management</div>} placement="right">
                        Upgrade Management
                    </Tooltip>
                )
            },
            {
                key: "/resource/auth_management",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">Authority Management</div>} placement="right">
                        Authority Management
                    </Tooltip>
                ),
                children: [
                    {
                        key: "/resource/auth_management/device_license_management",
                        label: (
                            <Tooltip
                                className="menu-dot"
                                title={<div className="fixed-tooltip">Device License Management</div>}
                                placement="right"
                            >
                                Device License Management
                            </Tooltip>
                        )
                    },
                    {
                        key: "/resource/auth_management/group_management",
                        label: (
                            <Tooltip
                                className="menu-dot"
                                title={<div className="fixed-tooltip">Group Management</div>}
                                placement="right"
                            >
                                Group Management
                            </Tooltip>
                        )
                    },
                    {
                        key: "/resource/auth_management/site_management",
                        label: (
                            <Tooltip
                                className="menu-dot"
                                title={<div className="fixed-tooltip">Site Management</div>}
                                placement="right"
                            >
                                Site Management
                            </Tooltip>
                        )
                    }
                ]
            }
        ]
    },
    {
        label: (
            <Tooltip title={<div className="fixed-tooltip">Service</div>} placement="right">
                Service
            </Tooltip>
        ),
        key: "/service",
        icon: <Icon component={serviceSvg} />,
        children: [
            {
                key: "/service/switch",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">Switch</div>} placement="right">
                        Switch
                    </Tooltip>
                )
            },
            {
                key: "/service/global_configuration",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">Global Configuration</div>} placement="right">
                        Global Configuration
                    </Tooltip>
                )
            },
            {
                key: "/service/switch_configuration",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">Switch Configuration</div>} placement="right">
                        Switch Configuration
                    </Tooltip>
                )
            },
            {
                key: "/service/config_files_view",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">Config Files View</div>} placement="right">
                        Config Files View
                    </Tooltip>
                )
            },
            {
                key: "/service/switch_model",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">Switch Model</div>} placement="right">
                        Switch Model
                    </Tooltip>
                )
            },
            {
                key: "/service/system_configuration",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">System Configuration</div>} placement="right">
                        System Configuration
                    </Tooltip>
                )
            },
            {
                key: "/service/config_template",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">Config Template</div>} placement="right">
                        Config Template
                    </Tooltip>
                )
            }
        ]
    },
    {
        label: (
            <Tooltip title={<div className="fixed-tooltip">Monitor</div>} placement="right">
                Monitor
            </Tooltip>
        ),
        key: "/monitor",
        icon: <Icon component={monitorSvg} />,
        children: [
            {
                key: "/monitor/alarm",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">Alarm</div>} placement="right">
                        Alarm
                    </Tooltip>
                )
            }
        ]
    },
    {
        label: (
            <Tooltip title={<div className="fixed-tooltip">Maintain</div>} placement="right">
                Maintain
            </Tooltip>
        ),
        key: "/maintain",
        icon: <Icon component={maintainSvg} />,
        children: [
            {
                key: "/maintain/automation",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">Automation</div>} placement="right">
                        Automation
                    </Tooltip>
                ),
                children: [
                    {
                        key: "/maintain/automation/playbooks",
                        label: (
                            <Tooltip
                                className="menu-dot"
                                title={<div className="fixed-tooltip">Playbooks</div>}
                                placement="right"
                            >
                                Playbooks
                            </Tooltip>
                        )
                    },
                    {
                        key: "/maintain/automation/other_devices",
                        label: (
                            <Tooltip
                                className="menu-dot"
                                title={<div className="fixed-tooltip">Other Devices</div>}
                                placement="right"
                            >
                                Other Devices
                            </Tooltip>
                        )
                    },
                    {
                        key: "/maintain/automation/ansible_jobs_list",
                        label: (
                            <Tooltip
                                className="menu-dot"
                                title={<div className="fixed-tooltip">Ansible Jobs List</div>}
                                placement="right"
                            >
                                Ansible Jobs List
                            </Tooltip>
                        )
                    },
                    {
                        key: "/maintain/automation/schedule",
                        label: (
                            <Tooltip
                                className="menu-dot"
                                title={<div className="fixed-tooltip">Schedule</div>}
                                placement="right"
                            >
                                Schedule
                            </Tooltip>
                        )
                    }
                ]
            },
            {
                key: "/maintain/system_backup",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">System Backup</div>} placement="right">
                        System Backup
                    </Tooltip>
                )
            },
            {
                key: "/maintain/cli_configuration",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">CLI Configuration</div>} placement="right">
                        CLI Configuration
                    </Tooltip>
                )
            }
        ]
    },
    {
        label: (
            <Tooltip title={<div className="fixed-tooltip">System</div>} placement="right">
                System
            </Tooltip>
        ),
        key: "/system",
        icon: <Icon component={settingSvg} />,
        children: [
            {
                key: "/system/user_management",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">User Management</div>} placement="right">
                        User Management
                    </Tooltip>
                )
            },
            {
                key: "/system/software_licenses",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">Software License</div>} placement="right">
                        Software License
                    </Tooltip>
                ),
                children: [
                    {
                        key: "/system/software_licenses/license_view",
                        label: (
                            <Tooltip
                                className="menu-dot"
                                title={<div className="fixed-tooltip">License View</div>}
                                placement="right"
                            >
                                License View
                            </Tooltip>
                        )
                    },
                    {
                        key: "/system/software_licenses/license_management",
                        label: (
                            <Tooltip
                                className="menu-dot"
                                title={<div className="fixed-tooltip">License Management</div>}
                                placement="right"
                            >
                                License Management
                            </Tooltip>
                        )
                    },
                    {
                        key: "/system/software_licenses/license_log",
                        label: (
                            <Tooltip
                                className="menu-dot"
                                title={<div className="fixed-tooltip">License Log</div>}
                                placement="right"
                            >
                                License Log
                            </Tooltip>
                        )
                    }
                ]
            }
        ]
    },
    {
        label: (
            <Tooltip title={<div className="fixed-tooltip">System</div>} placement="right">
                Topo
            </Tooltip>
        ),
        key: "/topo",
        icon: <Icon component={topologySvg} />,
        children: [
            {
                key: "/topo/topology",
                label: (
                    <Tooltip title={<div className="fixed-tooltip">Topology</div>} placement="right">
                        Topology
                    </Tooltip>
                )
            }
        ]
    }
];

const getSidebarItems = () => {
    switch (import.meta.env.VITE_APP_EXPORT_MODULE) {
        case "AmpCon-DC":
            return dcItems;
        case "AmpCon-CAMPUS":
            return campusItems;
        case "AmpCon-T":
            return tItems;
        case "AmpCon-SUPER":
            return superItems;
        case "AmpCon-SMB":
            return smbItems;
        default:
            return getSidebarItems();
    }
};

const all_items = getSidebarItems();

const excludeItemsByKey = (items, excludeKeys) => {
    const excludeRecursive = list => {
        return list.reduce((acc, item) => {
            if (item && !excludeKeys.includes(item.key)) {
                if (item.children) {
                    const filteredChildren = excludeRecursive(item.children);
                    acc.push({...item, children: filteredChildren});
                } else {
                    acc.push(item);
                }
            }
            return acc;
        }, []);
    };

    return excludeRecursive(items);
};

const adminExcludeKeys = [
    "/resource/auth_management/group_management",
    "/resource/group_management",
    "/system/user_management",
    "/service/switch_model",
    "/wireless",
    "/dashboard/wireless_view",
    "/service/system_configuration"
];
const operatorExcludeKeys = [
    "/resource/auth_management/group_management",
    "/resource/group_management",
    "/system/user_management",
    "/system/software_licenses",
    "/wireless",
    "/dashboard/wireless_view",
    "/service/switch_model",
    "/service/system_configuration"
];
const readonlyExcludeKeys = [
    "/system",
    "/resource",
    "/maintain/automation",
    "/maintain/system_backup",
    "/service/system_configuration",
    "/service/global_configuration",
    "/service/switch_configuration",
    "/service/switch_model",
    "/topo/campus_fabric",
    "/system/user_management",
    "/system/software_licenses",
    "/system/email_settings",
    "/system",
    "/physical_network/design",
    "/physical_network/fabrics",
    "/physical_network/RoCE",
    "/device/hosts",
    "/device/NICs",
    "/monitor/alerts/notification_rules",
    "/resource/pools",
    "/monitor/alarm/alarm_notification_rules",
    "/wireless",
    "/dashboard/wireless_view",
    "/service_provision",
    "/monitor/alarm/alarm_notification_rules",
    "/service/hosts/device_discovery",
    "/service/NICs/RoCE_configuration"
];

const sidebar_items = {
    superuser: all_items,
    superadmin: excludeItemsByKey(all_items, adminExcludeKeys),
    admin: excludeItemsByKey(all_items, operatorExcludeKeys),
    readonly: excludeItemsByKey(all_items, readonlyExcludeKeys)
};

export default sidebar_items;
