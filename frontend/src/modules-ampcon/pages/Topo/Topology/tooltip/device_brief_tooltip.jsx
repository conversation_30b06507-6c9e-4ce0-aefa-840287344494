import {forwardRef, useEffect, useImperativeHandle, useState} from "react";
import {Flex} from "antd";

const DeviceInfoBody = ({deviceInfo, statusIcon}) => (
    <Flex
        style={{
            margin: "16px",
            textAlign: "Left",
            fontFamily: "La<PERSON>, Lato",
            fontSize: "14px",
            fontStyle: "normal",
            textTransform: "none"
        }}
    >
        <Flex vertical>
            {Object.keys(deviceInfo).map((key, index) => (
                <div
                    style={{
                        height: "17px",
                        color: "#929A9E",
                        fontWeight: 400,
                        marginBottom: index === Object.keys(deviceInfo).length - 1 ? "4px" : "12px"
                    }}
                >
                    {key}
                </div>
            ))}
        </Flex>
        <div style={{width: "16px"}} />
        <Flex vertical>
            {Object.keys(deviceInfo).map((key, index) => (
                <div
                    style={{
                        height: "17px",
                        color: "#212519",
                        fontWeight: "bold",
                        marginBottom: index === Object.keys(deviceInfo).length - 1 ? "4px" : "12px"
                    }}
                >
                    {key === "Mgmt IP" && statusIcon}
                    {deviceInfo[key]}
                </div>
            ))}
        </Flex>
        <div style={{width: "2px"}} />
    </Flex>
);

const DeviceBriefTooltip = forwardRef((props, ref) => {
    const baseStyle = {
        backgroundColor: "#FFFFFF",
        boxShadow: "0px 1px 12px 1px #E6E8EA",
        borderRadius: "4px",
        position: "absolute",
        display: "block",
        color: "white",
        pointerEvents: "none",
        zIndex: 1000,
        transform: "none",
        whiteSpace: "pre"
    };

    const [isTooltipVisible, setTooltipVisible] = useState(false);
    const [deviceInfo, setDeviceInfo] = useState({});
    const [statusIcon, setStatusIcon] = useState(null);
    const [deviceStyle, setDeviceStyle] = useState(baseStyle);

    useEffect(() => {
        const handleMouseMove = event => {
            calculateDeviceBriefTooltipStyle(event.clientX, event.clientY);
        };

        window.addEventListener("mousemove", handleMouseMove);

        return () => {
            window.removeEventListener("mousemove", handleMouseMove);
        };
    }, []);

    useImperativeHandle(ref, () => ({
        showDeviceBriefTooltip: deviceInfo => {
            setStatusIcon(getStatusIcon(deviceInfo["Reachable Status"]));
            const {"Reachable Status": _, ...cleanedInfo} = deviceInfo;
            setDeviceInfo(cleanedInfo);
            setTooltipVisible(true);
        },
        hideDeviceBriefTooltip: () => {
            setTooltipVisible(false);
        }
    }));

    const calculateDeviceBriefTooltipStyle = (x, y) => {
        const baseStyleTemp = {...baseStyle};
        const deviceBriefTooltipHidden = document.getElementsByClassName("device_brief_tooltip_hidden")[0];
        if (deviceBriefTooltipHidden) {
            const rectHidden = deviceBriefTooltipHidden.getBoundingClientRect();
            if (x + 30 + rectHidden.width > window.innerWidth) {
                baseStyleTemp.right = "30px";
                delete baseStyleTemp.left;
            } else {
                baseStyleTemp.left = `${x + 10}px`;
                delete baseStyleTemp.right;
            }
            if (y + 30 + rectHidden.height > window.innerHeight) {
                baseStyleTemp.bottom = "30px";
                delete baseStyleTemp.top;
            } else {
                baseStyleTemp.top = `${y + 10}px`;
                delete baseStyleTemp.bottom;
            }
        }
        setDeviceStyle(baseStyleTemp);
    };

    const getStatusIcon = value => {
        value = value?.toString();
        return (
            <svg
                style={{
                    width: "8px",
                    height: "8px",
                    borderRadius: "50%",
                    backgroundColor: value?.toLowerCase() === "online" ? "#14C9BB" : "#F53F3F",
                    marginRight: "4px"
                }}
            />
        );
    };

    return (
        <>
            {isTooltipVisible ? (
                <div ref={ref} className="device_brief_tooltip" style={deviceStyle}>
                    <DeviceInfoBody deviceInfo={deviceInfo} statusIcon={statusIcon} />
                </div>
            ) : null}
            <div
                ref={ref}
                className="device_brief_tooltip_hidden"
                style={{
                    backgroundColor: "#FFFFFF",
                    boxShadow: "0px 1px 12px 1px #E6E8EA",
                    borderRadius: "4px",
                    position: "absolute",
                    right: window.innerWidth / 2,
                    bottom: window.innerHeight / 2,
                    display: "block",
                    color: "white",
                    pointerEvents: "none",
                    zIndex: -1,
                    transform: "none",
                    whiteSpace: "pre"
                }}
            >
                <DeviceInfoBody deviceInfo={deviceInfo} statusIcon={statusIcon} />
            </div>
        </>
    );
});

export default DeviceBriefTooltip;
