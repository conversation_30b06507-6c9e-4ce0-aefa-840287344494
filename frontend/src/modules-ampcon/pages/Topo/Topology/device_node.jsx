import {Flex} from "antd";
import {useEffect, useState} from "react";
import DownCircleTwoTone from "./resource/DownCircleTwoTone.svg?react";
import TopCircleTwoTone from "./resource/TopCircleTwoTone.svg?react";
import DownCircleTwoToneHover from "./resource/DownCircleTwoToneHover.svg?react";
import TopCircleTwoToneHover from "./resource/TopCircleTwoToneHover.svg?react";
import TopoSwitchOfflinePng from "./resource/topo_switch_offline.png?react";
import TopoSwitchOnlinePng from "./resource/topo_switch_online.png?react";
import TopoOTNOnlinePng from "./resource/topo_otn_online.png?react";
import TopoOTNOfflinePng from "./resource/topo_otn_offline.png?react";

const HoverableIcon = ({isHovering, onHoverChange, Icon, HoverIcon, onClick}) => {
    return (
        <div
            onMouseEnter={() => onHoverChange(true)}
            onMouseLeave={() => onHoverChange(false)}
            onClick={onClick}
            style={{position: "absolute", bottom: -13.5, right: 11, color: "#14C9BB", zIndex: 500}}
        >
            {isHovering ? <HoverIcon /> : <Icon />}
        </div>
    );
};

const DeviceNode = ({node, graph, isEditMode, isInTreeMode}) => {
    const deviceData = node.store.data;
    const [isShowLeaf, setIsShowLeaf] = useState(true);
    const [isHasLeaf, setIsHasLeaf] = useState(false);
    const [nodeStatus, setNodeStatus] = useState(deviceData.reachable_status);
    const [isHovering, setIsHovering] = useState(false);
    const isSelected = node.getAttrs()["isSelected"];
    const showHoverableIcon = !isEditMode && isHasLeaf && isInTreeMode;

    useEffect(() => {
        if (isInTreeMode) recursionHideLeafNodes(node);
        updateIsHasLeafStatus(node);
        setNodeStatus(deviceData.reachable_status);
    }, [isShowLeaf, node]);

    useEffect(() => {
        setNodeStatus(deviceData.reachable_status);
    }, [deviceData.reachable_status]);

    const hasOtherLinksOnTargetNodeInput = (targetNode, graph) => {
        const connectedEdges = graph.getConnectedEdges(targetNode);
        const targetNodeLayer = getNodeLayer(targetNode.id, graph);
        const inputLinks = connectedEdges.filter(edge => {
            const currentTargetNodeLayer = getNodeLayer(edge.target.cell, graph);
            const currentSourceNodeLayer = getNodeLayer(edge.source.cell, graph);
            return (
                (edge.target.cell === targetNode.id && currentTargetNodeLayer < targetNodeLayer) ||
                (edge.source.cell === targetNode.id && currentSourceNodeLayer < targetNodeLayer)
            );
        });
        return inputLinks.length > 1;
    };

    const recursionHideLeafNodes = currentNode => {
        const connectedEdges = graph.getConnectedEdges(currentNode);
        connectedEdges.forEach(edge => {
            if (edge.source.cell !== currentNode.id) {
                return;
            }
            const targetNode = graph.getCellById(edge.target.cell);
            // only if the target node has no other links on its input, then hide it
            if (
                targetNode &&
                targetNode.store.data.layer > currentNode.store.data.layer &&
                !hasOtherLinksOnTargetNodeInput(targetNode, graph)
            ) {
                recursionHideLeafNodes(targetNode);
                targetNode.setVisible(isShowLeaf);
                edge.setVisible(isShowLeaf);
            }
        });
    };

    const getNodeLayer = (nodeId, graph) => {
        const node = graph.getCellById(nodeId);
        return node ? node.prop("layer") : -1;
    };

    const updateIsHasLeafStatus = node => {
        const connectedEdges = graph.getConnectedEdges(node);
        const currentNodeLayer = getNodeLayer(node.id, graph);

        for (let i = 0; i < connectedEdges.length; i++) {
            const edge = connectedEdges[i];
            const edgeSourceId = edge.source.cell === node.id ? edge.target.cell : edge.source.cell;
            const sourceNodeLayer = getNodeLayer(edgeSourceId, graph);

            if (edge.source.cell === node.id && sourceNodeLayer > currentNodeLayer) {
                setIsHasLeaf(true);
                return;
            }
        }
        setIsHasLeaf(false);
    };

    return (
        <Flex vertical>
            <Flex vertical style={{justifyContent: "center", alignItems: "center", height: "100%"}}>
                <div
                    style={{
                        position: "relative",
                        display: "inline-block",
                        lineHeight: 0
                    }}
                >
                    {nodeStatus === "offline" && !isEditMode ? (
                        <img
                            src={deviceData.device_type === 2 ? TopoOTNOfflinePng : TopoSwitchOfflinePng}
                            width={34}
                            height={36}
                            alt="Topo Switch Offline"
                        />
                    ) : (
                        <img
                            src={deviceData.device_type === 2 ? TopoOTNOnlinePng : TopoSwitchOnlinePng}
                            width={34}
                            height={36}
                            alt="Topo Switch Online"
                        />
                    )}
                    {isSelected && (
                        <div
                            style={{
                                position: "absolute",
                                top: "-2px",
                                left: "-2px",
                                right: "-1px",
                                bottom: "0px",
                                border: "1px solid #15C5B8",
                                borderRadius: "4px",
                                pointerEvents: "none",
                                boxSizing: "border-box"
                            }}
                        />
                    )}
                </div>
                {showHoverableIcon && (
                    <div
                        style={{
                            position: "relative",
                            left: "17px",
                            top: "6px"
                        }}
                    >
                        <HoverableIcon
                            isHovering={isHovering}
                            onHoverChange={setIsHovering}
                            Icon={isShowLeaf ? DownCircleTwoTone : TopCircleTwoTone}
                            HoverIcon={isShowLeaf ? DownCircleTwoToneHover : TopCircleTwoToneHover}
                            onClick={() => setIsShowLeaf(!isShowLeaf)}
                        />
                    </div>
                )}
            </Flex>
            <Flex
                style={{
                    marginTop: showHoverableIcon || isEditMode ? "10px" : null,
                    justifyContent: "center",
                    alignItems: "center",
                    height: "100%",
                    fontFamily: "Lato",
                    whiteSpace: "nowrap"
                }}
            >
                {deviceData.label}
            </Flex>
        </Flex>
    );
};

export default DeviceNode;
