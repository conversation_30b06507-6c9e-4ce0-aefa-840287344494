import {Button, message, Form, Input, Modal, Flex, Divider, Alert} from "antd";
import {UploadSvg, InfoSvg} from "@/utils/common/iconSvg";
import {useState, useImperativeHandle, forwardRef, useRef} from "react";
import Dragger from "antd/es/upload/Dragger";
import {importTemplate} from "@/modules-ampcon/apis/campus_blueprint_api";

const ImporModal = forwardRef((props, ref) => {
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [nameList, setNameList] = useState([]);
    const [form] = Form.useForm();
    const [fileList, setFileList] = useState([]);
    const [error, setError] = useState(null);
    const ImporModalSuccessCallback = useRef(null);
    useImperativeHandle(ref, () => ({
        showCreateImportModal: (nameList, onSuccess) => {
            setNameList(nameList);
            form.resetFields();
            setIsModalOpen(true);
            ImporModalSuccessCallback.current = onSuccess;
        }
    }));
    const beforeUpload = file => {
        const isJson = file.name.endsWith(".json") || file.type === "application/json";
        setFileList([file]);
        if (!isJson) {
            setError("Wrong File format. Only .json files are supported.");
        } else {
            setError(null);
        }
        return false;
    };
    const onRemove = () => {
        setFileList([]);
        setError(null);
    };
    const validateName = async (_, value) => {
        if (nameList.includes(value)) {
            return Promise.reject(new Error("Configuration name is already existed"));
        }
        return Promise.resolve();
    };
    return isModalOpen ? (
        <Modal
            title="Import Config"
            open={isModalOpen}
            className="ampcon-middle-modal"
            onOk={() => {}}
            onCancel={() => {
                form.resetFields();
                setIsModalOpen(false);
                setFileList([]);
                setError(null);
            }}
            footer={
                <Flex vertical>
                    <Divider style={{marginTop: 0, marginBottom: 20}} />
                    <Flex justify="flex-end">
                        <Button
                            onClick={() => {
                                form.resetFields();
                                setFileList([]);
                                setError(null);
                                setIsModalOpen(false);
                            }}
                        >
                            Cancel
                        </Button>
                        <Button
                            type="primary"
                            onClick={async () => {
                                try {
                                    const values = await form.validateFields();
                                    if (fileList.length === 0) {
                                        message.error("Please upload a configuration file");
                                        return;
                                    }
                                    const formData = new FormData();
                                    formData.append("template_name", values.name);
                                    formData.append("json_file", fileList[0]);
                                    const res = await importTemplate(formData);
                                    if (res?.status === 200) {
                                        message.success("Import successful");
                                        setIsModalOpen(false);
                                        setFileList([]);
                                        setError(null);
                                        form.resetFields();
                                        if (ImporModalSuccessCallback.current) {
                                            ImporModalSuccessCallback.current(res?.id, values.name);
                                        }
                                    } else {
                                        message.error("Import failed");
                                    }
                                } catch (err) {
                                    console.error(err);
                                    message.error("Import failed. Please check input fields.");
                                }
                            }}
                        >
                            Import
                        </Button>
                    </Flex>
                </Flex>
            }
        >
            <Divider />
            <Alert
                message={
                    <div style={{paddingLeft: "8px"}}>
                        <span style={{fontWeight: 700}}>Note: </span>
                        Only the functions supported by the current template are delivered.
                    </div>
                }
                type="info"
                showIcon
                icon={<InfoSvg />}
                closable
                style={{
                    marginBottom: 16,
                    backgroundColor: "#F3F8FF",
                    border: "1px solid #F3F8FF",
                    color: "#367EFF",
                    width: 620,
                    height: 40,
                    borderRadius: 2
                }}
            />
            <Form labelAlign="left" labelCol={{flex: "120px"}} wrapperCol={{flex: "320px"}} form={form}>
                <Form.Item label="Config File" required>
                    <Dragger
                        name="file"
                        beforeUpload={beforeUpload}
                        onRemove={onRemove}
                        multiple={false}
                        fileList={fileList}
                        maxCount={1}
                    >
                        <p style={{marginBottom: 2}}>
                            <UploadSvg />
                        </p>
                        <p className="ant-upload-text">
                            Drag and drop config files to this area or
                            <br />
                            click <span style={{color: "#14C9BB"}}>Choose File</span> to upload it
                        </p>
                        <p style={{color: "#B8BFBF"}}>Support uploading only JSON files</p>
                    </Dragger>
                    {error && (
                        <div
                            style={{
                                color: "red",
                                marginTop: 8,
                                fontSize: 14
                            }}
                        >
                            {error}
                        </div>
                    )}
                </Form.Item>
                <Form.Item
                    name="name"
                    label="Template Name"
                    rules={[
                        {required: true, message: "Template Name is required."},
                        {validator: validateName},
                        {max: 32, message: "Template name cannot exceed 32 characters!"}
                    ]}
                >
                    <Input />
                </Form.Item>
            </Form>
        </Modal>
    ) : null;
});
export default ImporModal;
