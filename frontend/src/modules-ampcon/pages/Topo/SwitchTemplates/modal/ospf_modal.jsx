import {Button, Form, message, Divider, Input, Modal, Select, Space} from "antd";
import {useState, forwardRef, useImperativeHandle, useEffect} from "react";
import {formValidateRules} from "@/modules-ampcon/utils/util";
import {addGreenSvg, deleteSvg} from "@/utils/common/iconSvg";
import Icon from "@ant-design/icons";

const OspfModal = forwardRef(({handleAddServer, handleEditServer, ospData}, ref) => {
    const [isShowModal, setIsShowModal] = useState(false);
    const [form] = Form.useForm();
    const [mode, setMode] = useState("");
    const [type, setType] = useState([
        {label: "Default", value: "Default"},
        {label: "Stub", value: "Stub"},
        {label: "NSSA", value: "NSSA"}
    ]);
    const [existingConfigs, setExistingConfigs] = useState([]);
    const [currentRecord, setCurrentRecord] = useState(null);
    const validateAreaId = () => ({
        validator(_, value) {
            if (!value) return Promise.reject(new Error("Please input Area ID"));
            const ipv4Pattern = /^((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)\.){3}(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)$/;
            const isValidIp = ipv4Pattern.test(value);
            const isValidNumber = /^\d+$/.test(value) && +value >= 0 && +value <= 4294967295;

            return isValidIp || isValidNumber
                ? Promise.resolve()
                : Promise.reject(new Error("The Area ID must be 0-4294967295/XXX.XXX.XXX.XXX"));
        }
    });
    const validateAreaType = (_, value) => {
        const areaId = form.getFieldValue("id");
        if (areaId === "0.0.0.0" && (value === "Stub" || value === "NSSA")) {
            return Promise.reject(new Error("Backbone area (0.0.0.0) cannot be Stub or NSSA"));
        }
        return Promise.resolve();
    };
    useImperativeHandle(ref, () => ({
        showOspfModal: ({mode}, record) => {
            setMode(mode);
            if (mode === "edit") {
                let ipv4List = [];
                const typeMap = {
                    default: "Default",
                    stub: "Stub",
                    nssa: "NSSA"
                };
                const displayType = typeMap[record.type.toLowerCase()] || record.type;
                if (record.ipv4) {
                    ipv4List = Array.isArray(record.ipv4) ? record.ipv4 : record.ipv4.split(",");
                }
                form.setFieldsValue({
                    ...record,
                    ipv4List,
                    type: displayType
                });
                setCurrentRecord(record);
            } else {
                form.setFieldsValue({
                    ipv4List: [""]
                });
                setCurrentRecord(null);
            }
            setIsShowModal(true);
        }
    }));
    useEffect(() => {
        if (isShowModal && ospData) {
            setExistingConfigs(
                ospData.areas.flatMap(item => {
                    if (Array.isArray(item.ipv4)) {
                        return item.ipv4;
                    }
                    if (typeof item.ipv4 === "string") {
                        return item.ipv4
                            .split(",")
                            .map(ip => ip.trim())
                            .filter(Boolean);
                    }
                    return [];
                })
            );
        }
    }, [isShowModal, ospData]);
    return isShowModal ? (
        <Modal
            className="ampcon-middle-modal"
            title={
                <div>
                    {mode === "create" ? "Create OSPF" : "Edit"}
                    <Divider style={{marginTop: 8, marginBottom: 0}} />
                </div>
            }
            open={isShowModal}
            onOk={() => {}}
            onCancel={() => {
                setIsShowModal(false);
                form.resetFields();
            }}
            footer={
                <div>
                    <Divider style={{marginTop: 0, marginBottom: 20}} />
                    <Button
                        onClick={() => {
                            setIsShowModal(false);
                            form.resetFields();
                        }}
                    >
                        Cancel
                    </Button>
                    <Button
                        type="primary"
                        onClick={() => {
                            form.submit();
                        }}
                    >
                        Apply
                    </Button>
                </div>
            }
        >
            <Form
                layout="horizontal"
                labelAlign="left"
                labelCol={{span: 6}}
                wrapperCol={{span: 17}}
                labelWrap
                className="label-wrap"
                form={form}
                style={{minHeight: "267.23px"}}
                ref={form}
                validateTrigger={["onBlur", "onSubmit"]}
                onFinish={() => {
                    const values = form.getFieldsValue();
                    const ipv4List = (values.ipv4List || []).filter(ip => ip?.trim());
                    const {ipv4List: _, ...rest} = values;
                    const finalData = {
                        ...rest,
                        ipv4: ipv4List
                    };
                    try {
                        if (mode === "create") {
                            handleAddServer(finalData);
                            message.success("Ospf created successfully");
                        }
                        if (mode === "edit") {
                            handleEditServer(finalData, currentRecord);
                            message.success("Ospf edited successfully");
                        }
                    } catch (error) {
                        message.error("An error occurred while processing the ospf");
                        console.error(error);
                    } finally {
                        setIsShowModal(false);
                        form.resetFields();
                    }
                }}
                onValuesChange={changedValues => {
                    if ("id" in changedValues && form.getFieldValue("type")) {
                        form.validateFields(["type"]);
                    }
                    if ("type" in changedValues && form.getFieldValue("id") === "0.0.0.0") {
                        form.validateFields(["type"]);
                    }
                }}
            >
                <Form.List name="ipv4List">
                    {(fields, {add, remove}) => (
                        <>
                            {fields.map((field, index) => (
                                <Form.Item
                                    required={index === 0}
                                    key={field.key}
                                    label="IPv4/Prefixlen"
                                    labelCol={{span: 6}}
                                    wrapperCol={{span: 17}}
                                    style={{marginBottom: 24}}
                                >
                                    <Space align="baseline">
                                        <Form.Item
                                            {...field}
                                            name={[field.name]}
                                            validateTrigger={["onBlur", "onChange"]}
                                            rules={[
                                                ...(index === 0
                                                    ? [{required: true, message: "Please input IPv4/Prefixlen"}]
                                                    : []),
                                                formValidateRules.ipv4net(),
                                                {
                                                    validator(_, value) {
                                                        if (!value) return Promise.resolve();
                                                        const trimmed = value.trim();
                                                        const existsInExisting = existingConfigs.some(existingIp => {
                                                            if (mode === "edit" && currentRecord?.ipv4) {
                                                                const currentList = Array.isArray(currentRecord.ipv4)
                                                                    ? currentRecord.ipv4
                                                                    : currentRecord.ipv4
                                                                          .split(",")
                                                                          .map(ip => ip.trim());

                                                                if (currentList.includes(trimmed)) {
                                                                    return false;
                                                                }
                                                            }
                                                            return existingIp === trimmed;
                                                        });

                                                        if (existsInExisting) {
                                                            return Promise.reject(
                                                                new Error("IPv4/Prefixlen already exists")
                                                            );
                                                        }
                                                        const currentList = form.getFieldValue("ipv4List") || [];
                                                        const count = currentList.filter(
                                                            ip => ip?.trim() === trimmed
                                                        ).length;
                                                        if (count > 1) {
                                                            return Promise.reject(
                                                                new Error("Duplicate IPv4/Prefixlen in current form")
                                                            );
                                                        }

                                                        return Promise.resolve();
                                                    }
                                                }
                                            ]}
                                            noStyle
                                        >
                                            <Input style={{width: 280}} placeholder="XXX.XXX.XXX.XXX/XX" />
                                        </Form.Item>
                                        {index === 0 ? (
                                            <Button
                                                type="text"
                                                icon={<Icon component={addGreenSvg} />}
                                                onClick={() => add()}
                                                style={{color: "#14c9bb"}}
                                            />
                                        ) : (
                                            <Button
                                                type="text"
                                                danger
                                                icon={<Icon component={deleteSvg} />}
                                                onClick={() => remove(field.name)}
                                                style={{backgroundColor: "transparent", color: "#BFBFBF"}}
                                            />
                                        )}
                                    </Space>
                                </Form.Item>
                            ))}
                        </>
                    )}
                </Form.List>

                <Form.Item
                    name="id"
                    validateFirst
                    rules={[
                        {
                            required: true,
                            message: "Please input id"
                        },
                        validateAreaId()
                        // validateCombinationUnique()
                    ]}
                    label="Area ID"
                    initialValue=""
                >
                    <Input style={{width: "280px"}} placeholder="0-4294967295/XXX.XXX.XXX.XXX" />
                </Form.Item>
                <Form.Item
                    name="type"
                    rules={[{required: true, message: "Please Select type"}, {validator: validateAreaType}]}
                    label="Area Type"
                >
                    <Select style={{width: "280px"}} options={type} />
                </Form.Item>
            </Form>
        </Modal>
    ) : null;
});
export default OspfModal;
