import {Space, Form, Flex, Input, Divider, Modal, Button, message} from "antd";
import {addSvg} from "@/utils/common/iconSvg";
import {AmpConCustomTable} from "@/modules-ampcon/components/custom_table";
import AssignTemplateAddModal from "@/modules-ampcon/pages/Topo/SwitchTemplates/modal/assign_template_add_modal";
import Icon from "@ant-design/icons";
import {useState, useRef, forwardRef, useImperativeHandle} from "react";
import {confirmModalAction} from "@/modules-ampcon/components/custom_modal";

const AssignTemplateModal = forwardRef(({data, tableStyle, onApply}, ref) => {
    const [templateName, setTemplateName] = useState("");
    const [form] = Form.useForm();
    const AssignTemplateAddModalRef = useRef(null);
    const [isShowModal, setIsShowModal] = useState(false);
    const [switchList, setSwitchList] = useState([]);
    useImperativeHandle(ref, () => ({
        showAssignTemplateModal: (switchList, name) => {
            setSwitchList(switchList || []);
            setTemplateName(name || "");
            setIsShowModal(true);
        }
    }));

    const handleDeleteServer = record => {
        const updatedData = switchList.filter(item => item.id !== record.id);
        setSwitchList(updatedData || []);
    };
    const handleAddSwitch = (selectedSwitches = []) => {
        const normalized = selectedSwitches.map(item => ({
            host_name: item.host_name,
            sn: item.sn,
            mgt_ip: item.mgt_ip,
            site: item.site,
            platform_model: item.platform_model,
            id: item.id
        }));
        setSwitchList(normalized);
    };

    const handleApply = () => {
        const snList = switchList.map(item => item.sn);
        if (onApply) {
            onApply(snList);
        }
        setIsShowModal(false);
        form.resetFields();
    };
    const columns = [
        // createColumnConfig("Sysname", "host_name"),
        // createColumnConfig("SN", "sn"),
        // createColumnConfig("Mgmt IP", "mgt_ip"),
        // createColumnConfig("Model", "platform_model"),
        // createColumnConfig("Site", "site"),
        {
            title: "Sysname",
            dataIndex: "host_name",
            width: "19%",
            render: (_, record) => <span>{record.host_name}</span>,
            sorter: (a, b) => a.host_name.localeCompare(b.host_name)
        },
        {
            title: "SN",
            dataIndex: "sn",
            width: "13%",
            render: (_, record) => <span>{record.sn}</span>,
            sorter: (a, b) => a.sn.localeCompare(b.sn)
        },
        {
            title: "Mgmt IP",
            dataIndex: "mgt_ip",
            width: "19%",
            render: (_, record) => <span>{record.mgt_ip}</span>,
            sorter: (a, b) => a.mgt_ip.localeCompare(b.mgt_ip)
        },
        {
            title: "Model",
            dataIndex: "platform_model",
            width: "17%",
            render: (_, record) => <span>{record.platform_model}</span>,
            sorter: (a, b) => a.platform_model.localeCompare(b.platform_model)
        },
        {
            title: "Site",
            dataIndex: "site",
            width: "13%",
            render: (_, record) => <span>{record.site}</span>,
            sorter: (a, b) => a.site.localeCompare(b.site)
        },
        {
            title: "Operation",
            render: (_, record) => {
                return (
                    <Space size="large" className="actionLink">
                        <a
                            onClick={() => {
                                confirmModalAction("Are you sure you want to delete this switch?", () => {
                                    try {
                                        handleDeleteServer(record);
                                        message.success("Delete Successful");
                                    } catch (error) {
                                        message.error("Delete Failed");
                                    }
                                });
                            }}
                        >
                            Delete
                        </a>
                    </Space>
                );
            }
        }
    ];

    return isShowModal ? (
        <Modal
            className="ampcon-max-modal"
            title={
                <div>
                    Assign Template
                    <Divider style={{marginTop: 8, marginBottom: 0}} />
                </div>
            }
            open={isShowModal}
            onOk={() => {}}
            onCancel={() => {
                setIsShowModal(false);
                form.resetFields();
            }}
            footer={
                <div>
                    <Divider style={{marginTop: 0, marginBottom: 20}} />
                    <Button
                        onClick={() => {
                            setIsShowModal(false);
                            form.resetFields();
                        }}
                    >
                        Cancel
                    </Button>
                    <Button type="primary" onClick={handleApply}>
                        Apply
                    </Button>
                </div>
            }
        >
            <Flex vertical flex={1} style={{height: "100%", paddingRight: "5px"}}>
                <div style={{width: "455px", height: "150px", backgroundColor: "#F1F3F6", paddingLeft: "18px"}}>
                    <h2>{templateName}</h2>
                    <p>Switches Assosicated with the Template</p>
                    <Flex direction="column" gap="8px">
                        <Input
                            style={{width: 206, background: "#F8FAFB"}}
                            disabled
                            suffix="Switches"
                            value={switchList?.length || 0}
                        />
                    </Flex>
                </div>
                <h2>Switches</h2>
                <AssignTemplateAddModal
                    ref={AssignTemplateAddModalRef}
                    switchData={switchList}
                    handleAddSwitch={handleAddSwitch}
                    templateName={templateName}
                />
                <Button
                    style={{width: "100px"}}
                    type="primary"
                    onClick={() => {
                        AssignTemplateAddModalRef.current.showAddModal();
                    }}
                >
                    <Icon component={addSvg} />
                    Switch
                </Button>
                <div>
                    <AmpConCustomTable
                        columns={columns}
                        style={tableStyle}
                        dataSource={switchList}
                        // searchFieldsList={searchFieldsList}
                        // matchFieldsList={matchFieldsList}
                        pagination={{
                            defaultPageSize: 10,
                            showSizeChanger: true,
                            hideOnSinglePage: switchList.length <= 10
                        }}
                    />
                </div>
                <Divider style={{marginTop: "32px", marginBottom: "32px"}} />
            </Flex>
        </Modal>
    ) : null;
});
export default AssignTemplateModal;
