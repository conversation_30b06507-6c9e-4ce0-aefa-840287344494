import {Button, Form, message, Divider, Input, Modal, Checkbox} from "antd";
import {useState, forwardRef, useImperativeHandle, useEffect} from "react";

const VrfModal = forwardRef(({handleAddServer, handleEditServer, networkData, vrfData}, ref) => {
    const [isShowModal, setIsShowModal] = useState(false);
    const [form] = Form.useForm();
    const [mode, setMode] = useState("");
    const [existingData, setExistingData] = useState({networks: []});
    const [currentRecord, setCurrentRecord] = useState(null);
    const [title, setTitle] = useState("Create Network");
    useImperativeHandle(ref, () => ({
        showVrfModal: ({mode}, record) => {
            setMode(mode);
            if (mode === "edit") {
                setTitle("Edit");
                form.setFieldsValue(record);
                setCurrentRecord(record);
            } else {
                setCurrentRecord(null);
                setTitle("Create VRF");
            }
            setIsShowModal(true);
        }
    }));
    const validateNameUnique = (_, value) => {
        if (!value) return Promise.resolve();
        const isDuplicate = existingData.networks.some(item => {
            if (mode === "edit" && item.id === currentRecord?.id) return false;
            return item.name === value;
        });

        return isDuplicate ? Promise.reject(new Error("Name already exists")) : Promise.resolve();
    };
    const EmptyStringValidator = msg => {
        return {
            validator: (_, value) => {
                if (value && /\s/.test(value)) {
                    return Promise.reject(new Error(msg));
                }
                return Promise.resolve();
            }
        };
    };
    useEffect(() => {
        if (isShowModal && vrfData) {
            setExistingData(vrfData);
        }
    }, [isShowModal, networkData, vrfData]);
    const isNetworkUsed = network => {
        if (mode === "edit" && form.getFieldValue("id")?.includes(network)) {
            return false;
        }
        const isUsed = existingData.networks.some(networkObj => {
            const ids = networkObj.id || [];
            return ids.includes(network);
        });
        return isUsed;
    };
    return isShowModal ? (
        <Modal
            className="ampcon-middle-modal"
            title={
                <div>
                    {title}
                    <Divider style={{marginTop: 8, marginBottom: 0}} />
                </div>
            }
            open={isShowModal}
            onOk={() => {}}
            onCancel={() => {
                setIsShowModal(false);
                form.resetFields();
            }}
            footer={
                mode === "view" ? null : (
                    <div>
                        <Divider style={{marginTop: 0, marginBottom: 20}} />
                        <Button
                            onClick={() => {
                                setIsShowModal(false);
                                form.resetFields();
                            }}
                        >
                            Cancel
                        </Button>
                        <Button
                            type="primary"
                            onClick={() => {
                                form.submit();
                            }}
                        >
                            Apply
                        </Button>
                    </div>
                )
            }
        >
            <Form
                layout="horizontal"
                labelAlign="left"
                labelCol={{span: 6}}
                wrapperCol={{span: 17}}
                labelWrap
                className="label-wrap"
                form={form}
                style={{minHeight: "267.23px"}}
                ref={form}
                validateTrigger={["onBlur", "onSubmit"]}
                onFinish={() => {
                    try {
                        if (mode === "create") {
                            handleAddServer(form.getFieldsValue());
                            message.success("Vrf created successfully");
                        }
                        if (mode === "edit") {
                            handleEditServer(form.getFieldsValue(), currentRecord);
                            message.success("Vrf edited successfully");
                        }
                    } catch (error) {
                        message.error("An error occurred while processing the Vrf");
                        console.error(error);
                    } finally {
                        setIsShowModal(false);
                        form.resetFields();
                    }
                }}
            >
                <Form.Item
                    name="name"
                    validateFirst
                    rules={[
                        {
                            required: true,
                            message: "Please input name"
                        },
                        {max: 32, message: "VRF name cannot exceed 32 characters!"},
                        {validator: validateNameUnique},
                        EmptyStringValidator("Name cannot contain spaces"),
                        {
                            validator: (_, value) => {
                                if (!value) return Promise.resolve();
                                const reservedNames = [
                                    "gretap0",
                                    "erspan0",
                                    "sit0",
                                    "bridge0",
                                    "bridge",
                                    "pimreg",
                                    "ipmr-lo",
                                    "MGMT_VRF"
                                ];
                                if (reservedNames.includes(value)) {
                                    return Promise.reject(new Error(`The name "${value}" is reserved for others`));
                                }
                                const regex = /^(te\d+|ge\d+|eth\d+|ae\d+|xe\d+)(?:\.\d+)?$/;
                                if (regex.test(value)) {
                                    return Promise.reject(new Error(`The name "${value}" is reserved for others`));
                                }
                                return Promise.resolve();
                            }
                        }
                    ]}
                    label="VRF Name"
                    initialValue=""
                >
                    <Input style={{width: "280px"}} disabled={mode === "edit"} />
                </Form.Item>
                <Form.Item
                    name="id"
                    label="Network"
                    initialValue={[]}
                    rules={[{required: true, message: "Please select networks"}]}
                >
                    <Checkbox.Group style={{width: "100%"}}>
                        {networkData?.map(item => (
                            <Checkbox
                                key={`${item.name}(${item.id})`}
                                value={`${item.name}(${item.id})`}
                                disabled={isNetworkUsed(`${item.name}(${item.id})`)}
                            >
                                {`${item.name}(${item.id})`}
                            </Checkbox>
                        ))}
                    </Checkbox.Group>
                </Form.Item>
            </Form>
        </Modal>
    ) : null;
});
export default VrfModal;
