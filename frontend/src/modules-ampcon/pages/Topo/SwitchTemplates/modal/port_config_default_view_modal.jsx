import {Select, Form, Radio, Divider, Input, Modal, Checkbox, Button, message} from "antd";
import {useState, forwardRef, useImperativeHandle, useEffect} from "react";

const PortConfigDefaultViewModal = forwardRef(
    ({handleAddServer, handleEditServer, networkData, portConfigData}, ref) => {
        const [isShowModal, setIsShowModal] = useState(false);
        const {TextArea} = Input;
        const {Option} = Select;
        const [form] = Form.useForm();
        const [portMode, setPortMode] = useState();
        const [stormControl, setStormControl] = useState();
        const [mode, setMode] = useState("");
        const [selectAll, setSelectAll] = useState(false);
        const [selectedNetworks, setSelectedNetworks] = useState([]);
        const speed = {
            Auto: "auto",
            "10M": "10",
            "100M": "100",
            "1G": "1000",
            "2.5G": "2500",
            "5G": "5000",
            "10G": "10000",
            "25G": "25000",
            "40G": "40000",
            "100G": "100000"
        };
        const [modes, setModes] = useState(["broadcast", "multicast", "unicast"]);
        const [modalName, setModalName] = useState("Create Port Profile");
        const [existingData, setExistingData] = useState([]);
        const [currentRecord, setCurrentRecord] = useState(null);
        const [showZeroHint, setShowZeroHint] = useState(false);
        const handleModeChange = e => {
            setPortMode(e.target.value);
            if (e.target.value !== "trunk") {
                form.setFieldsValue({trunkNetwork: []});
                setSelectedNetworks([]);
                setSelectAll(false);
            }
        };
        const handleStormControlChange = e => {
            setStormControl(e.target.value);
            setShowZeroHint(false);
            if (e.target.value !== "disable") {
                form.setFieldsValue({percentage: "", pcps: "", kps: "", controlMethod: "ratio"});
            }
        };
        const handleSelectAllChange = e => {
            const {checked} = e.target;
            setSelectAll(checked);
            setSelectedNetworks([]);
            if (checked) {
                form.setFieldsValue({trunkNetwork: ["all networks"]});
            } else {
                form.setFieldsValue({trunkNetwork: []});
            }
        };
        const handleSelectChange = value => {
            setSelectedNetworks(value);
            form.setFieldsValue({trunkNetwork: value});
            setSelectAll(false);
        };
        const validateNameUnique = (_, value) => {
            if (!value) return Promise.resolve();
            if (existingData.length === 0) return Promise.resolve();
            const isDuplicate = existingData.some(item => {
                if (mode === "edit" && item.id === currentRecord?.id) return false;
                return item.name === value;
            });

            return isDuplicate ? Promise.reject(new Error("VLAN name already exists")) : Promise.resolve();
        };
        useEffect(() => {
            if (isShowModal) {
                const trunk = form.getFieldValue("trunkNetwork") || [];
                setSelectedNetworks(trunk);
            }
            if (portConfigData) {
                setExistingData(portConfigData);
            }
        }, [isShowModal, portConfigData]);
        const reverseSpeedMapping = Object.fromEntries(Object.entries(speed).map(([key, value]) => [value, key]));
        useImperativeHandle(ref, () => ({
            showPortConfigDefaultViewModal: ({mode}, record) => {
                const isAllNetworks = record?.trunkNetwork?.includes("all networks");
                const trunkNetworkValue = isAllNetworks ? [] : record?.trunkNetwork || [];
                setSelectAll(prev => {
                    return isAllNetworks;
                });
                setSelectedNetworks(trunkNetworkValue);
                if (mode === "create") {
                    setModalName("Create Port Profile");
                    setPortMode("access");
                    setStormControl("disable");
                    form.setFieldsValue({
                        name: "",
                        portEnable: "enable",
                        description: "",
                        portMode: "access",
                        portNetwork: "Default(1)",
                        trunkNetwork: [],
                        speed: "Auto",
                        poe: "disable",
                        stormControl: "disable",
                        modes: ["broadcast", "multicast", "unicast"],
                        controlMethod: "ratio",
                        percentage: "",
                        pcps: "",
                        kps: ""
                    });
                } else if (mode === "edit") {
                    setModalName(record.name);
                    setPortMode(record.portMode || "access");
                    setStormControl(record.stormControl);
                    const displaySpeed = reverseSpeedMapping[record.speed] || "Auto";
                    form.setFieldsValue({
                        name: record.name,
                        portEnable: record.portEnable || "enable",
                        description: record.description || "",
                        portMode: record.portMode || "access",
                        portNetwork: record.portNetwork || "Default(1)",
                        trunkNetwork: trunkNetworkValue,
                        speed: displaySpeed,
                        poe: record.poe || "disable",
                        stormControl: record.stormControl || "disable",
                        modes: record.modes || ["broadcast", "multicast", "unicast"],
                        controlMethod: record.controlMethod || "ratio",
                        percentage: record.percentage || "",
                        pcps: record.pcps || "",
                        kps: record.kps || ""
                    });
                    setCurrentRecord(record);
                } else {
                    setModalName(record.name);
                    setPortMode(record.portMode || "access");
                    setStormControl(record.stormControl);
                    form.setFieldsValue({
                        name: record.name,
                        poe: "disable",
                        portEnable: "disable",
                        portMode: "access",
                        stormControl: "disable",
                        portNetwork: record.portNetwork || "Default(1)",
                        description: record.description || "",
                        speed: reverseSpeedMapping[record.speed] || "Auto"
                    });
                }
                setMode(mode);
                setIsShowModal(true);
            }
        }));

        return isShowModal ? (
            <Modal
                className="ampcon-middle-modal"
                title={
                    <div>
                        {modalName}
                        <Divider style={{marginTop: 8, marginBottom: 0}} />
                    </div>
                }
                open={isShowModal}
                onOk={() => {}}
                onCancel={() => {
                    setIsShowModal(false);
                    form.resetFields();
                    setPortMode("trunk");
                }}
                footer={
                    mode === "view" ? null : (
                        <div>
                            <Divider style={{marginTop: 0, marginBottom: 20}} />
                            <Button
                                onClick={() => {
                                    setIsShowModal(false);
                                    form.resetFields();
                                }}
                            >
                                Cancel
                            </Button>
                            <Button
                                type="primary"
                                onClick={() => {
                                    form.submit();
                                }}
                            >
                                Apply
                            </Button>
                        </div>
                    )
                }
            >
                <Form
                    layout="horizontal"
                    labelAlign="left"
                    labelCol={{span: 8}}
                    wrapperCol={{span: 17}}
                    labelWrap
                    className="label-wrap"
                    form={form}
                    style={{minHeight: "267.23px"}}
                    ref={form}
                    validateTrigger={["onBlur", "onSubmit"]}
                    onFinish={values => {
                        const convertedSpeed = speed[values.speed] || values.speed;
                        const finalValues = {
                            ...values,
                            speed: convertedSpeed,
                            trunkNetwork: selectAll ? ["all networks"] : values.trunkNetwork || []
                        };
                        try {
                            if (mode === "create") {
                                handleAddServer(finalValues);
                                message.success("Port Profile created successfully");
                            }
                            if (mode === "edit") {
                                handleEditServer(finalValues, currentRecord);
                                message.success("Port Profile edited successfully");
                            }
                        } catch (error) {
                            message.error("An error occurred while processing the port Profile");
                            console.error(error);
                        } finally {
                            setIsShowModal(false);
                            form.resetFields();
                        }
                    }}
                >
                    <Form.Item
                        name="name"
                        validateFirst
                        label="Profile Name"
                        rules={[
                            {
                                required: true,
                                message: "Please input name"
                            },
                            {validator: validateNameUnique},
                            {max: 32, message: "Profile Name cannot exceed 32 characters!"}
                        ]}
                    >
                        <Input style={{width: "280px"}} disabled={mode === "view" || mode === "edit"} />
                    </Form.Item>
                    <Form.Item name="portEnable" validateFirst label="Port Enable">
                        <Radio.Group onChange={e => {}} disabled={mode === "view"}>
                            <Radio value="enable">Enable</Radio>
                            <Radio value="disable">Disable</Radio>
                        </Radio.Group>
                    </Form.Item>
                    <Form.Item name="description" validateFirst label="Description">
                        <TextArea style={{width: "280px"}} disabled={mode === "view"} />
                    </Form.Item>
                    <Form.Item name="portMode" validateFirst label="Port Mode">
                        <Radio.Group onChange={handleModeChange} value={portMode} disabled={mode === "view"}>
                            <Radio value="trunk">Trunk</Radio>
                            <Radio value="access">Access</Radio>
                        </Radio.Group>
                    </Form.Item>
                    <Form.Item name="portNetwork" validateFirst label="Port Network">
                        <Select style={{width: "280px"}} disabled={mode === "view"}>
                            {networkData?.map(network => (
                                <Option key={network.id} value={`${network.name}(${network.id})`}>
                                    {`${network.name}(${network.id})`}
                                </Option>
                            ))}
                        </Select>
                    </Form.Item>
                    {portMode === "trunk" && (
                        <Form.Item label="Trunk Network">
                            <div style={{display: "flex", flexDirection: "column", gap: 8}}>
                                <Checkbox checked={selectAll} onChange={handleSelectAllChange} style={{marginRight: 8}}>
                                    All Networks
                                </Checkbox>
                                {!selectAll && (
                                    <Form.Item name="trunkNetwork" noStyle>
                                        <Select
                                            mode="multiple"
                                            style={{width: 280}}
                                            value={selectedNetworks}
                                            onChange={handleSelectChange}
                                        >
                                            {networkData?.map(network => (
                                                <Option key={network.id} value={`${network.name}(${network.id})`}>
                                                    {`${network.name}(${network.id})`}
                                                </Option>
                                            ))}
                                        </Select>
                                    </Form.Item>
                                )}
                            </div>
                        </Form.Item>
                    )}
                    <Form.Item name="speed" label="Speed">
                        <Select
                            style={{width: 280}}
                            onChange={value => form.setFieldsValue({speed: value})}
                            disabled={mode === "view"}
                        >
                            {Object.keys(speed).map(sd => (
                                <Option key={sd} value={sd}>
                                    {sd}
                                </Option>
                            ))}
                        </Select>
                    </Form.Item>
                    <Form.Item name="poe" validateFirst label="PoE" initialValue="enable">
                        <Radio.Group onChange={() => {}} disabled={mode === "view"}>
                            <Radio value="enable">Enable</Radio>
                            <Radio value="disable">Disable</Radio>
                        </Radio.Group>
                    </Form.Item>
                    <Form.Item name="stormControl" validateFirst label="Storm Control" initialValue="enable">
                        <Radio.Group onChange={handleStormControlChange} disabled={mode === "view"}>
                            <Radio value="enable">Enable</Radio>
                            <Radio value="disable">Disable</Radio>
                        </Radio.Group>
                    </Form.Item>
                    {stormControl === "enable" && (
                        <>
                            <Form.Item name="modes" validateFirst label="Type" initialValue={modes}>
                                <Checkbox.Group checked={modes} onChange="">
                                    <Checkbox value="broadcast">Broadcast</Checkbox>
                                    <Checkbox value="multicast">Multicast</Checkbox>
                                    <Checkbox value="unicast">Unicast</Checkbox>
                                </Checkbox.Group>
                            </Form.Item>
                            <Form.Item name="controlMethod" validateFirst label="Control Method" initialValue="ratio">
                                <Radio.Group
                                    onChange={e => {
                                        form.setFieldValue("controlMethod", e.target.value);
                                        const method = e.target.value;
                                        if (method === "ratio") {
                                            form.setFieldsValue({pcps: undefined, kps: undefined});
                                        } else if (method === "pps") {
                                            form.setFieldsValue({percentage: undefined, kps: undefined});
                                        } else if (method === "kbps") {
                                            form.setFieldsValue({percentage: undefined, pcps: undefined});
                                        }
                                        setShowZeroHint(false);
                                    }}
                                >
                                    <Radio value="pps">PPS</Radio>
                                    <Radio value="ratio">Ratio</Radio>
                                    <Radio value="kbps">Kbps</Radio>
                                </Radio.Group>
                            </Form.Item>
                            <Form.Item noStyle shouldUpdate={(prev, curr) => prev.controlMethod !== curr.controlMethod}>
                                {({getFieldValue}) => {
                                    const method = getFieldValue("controlMethod");
                                    if (method === "ratio") {
                                        return (
                                            <Form.Item
                                                name="percentage"
                                                label="Percentage of Physical Link"
                                                required
                                                rules={[
                                                    {
                                                        validator: (_, value) => {
                                                            if (value === undefined || value === "") {
                                                                return Promise.reject(
                                                                    new Error(
                                                                        "Please input Percentage of Physical Link"
                                                                    )
                                                                );
                                                            }
                                                            const numberValue = Number(value);
                                                            if (
                                                                !/^(0|[1-9]\d*)$/.test(value) ||
                                                                isNaN(numberValue) ||
                                                                numberValue < 0 ||
                                                                numberValue > 100
                                                            ) {
                                                                return Promise.reject(
                                                                    new Error("Value must be between 0 and 100")
                                                                );
                                                            }
                                                            return Promise.resolve();
                                                        }
                                                    }
                                                ]}
                                                extra={
                                                    showZeroHint ? (
                                                        <span style={{color: "#ff4d4f"}}>
                                                            0: Do not allow packets to pass
                                                        </span>
                                                    ) : null
                                                }
                                            >
                                                <Input
                                                    style={{width: "280px"}}
                                                    placeholder="Range: 0-100"
                                                    onBlur={async e => {
                                                        const {value} = e.target;
                                                        try {
                                                            await form.validateFields(["percentage"]);
                                                            setTimeout(() => {
                                                                setShowZeroHint(value === "0");
                                                            }, 400);
                                                        } catch {
                                                            setShowZeroHint(false);
                                                        }
                                                    }}
                                                />
                                            </Form.Item>
                                        );
                                    }
                                    if (method === "pps") {
                                        return (
                                            <Form.Item
                                                name="pcps"
                                                label="Packet Count Per Second"
                                                required
                                                rules={[
                                                    {
                                                        validator: (_, value) => {
                                                            const numberValue = Number(value);
                                                            if (value === undefined || value === "") {
                                                                return Promise.reject(
                                                                    new Error("Please input Packet Count Per Second")
                                                                );
                                                            }

                                                            if (
                                                                !/^(0|[1-9]\d*)$/.test(value) ||
                                                                isNaN(numberValue) ||
                                                                numberValue < 0 ||
                                                                numberValue > 30000000
                                                            ) {
                                                                return Promise.reject(
                                                                    new Error("Value must be between 0 and 30000000")
                                                                );
                                                            }
                                                            return Promise.resolve();
                                                        }
                                                    }
                                                ]}
                                                extra={
                                                    showZeroHint ? (
                                                        <span style={{color: "#ff4d4f"}}>
                                                            0: Do not allow packets to pass
                                                        </span>
                                                    ) : null
                                                }
                                            >
                                                <Input
                                                    style={{width: "280px"}}
                                                    placeholder="Range: 0-30000000"
                                                    onBlur={async e => {
                                                        const {value} = e.target;
                                                        try {
                                                            await form.validateFields(["pcps"]);
                                                            setTimeout(() => {
                                                                setShowZeroHint(value === "0");
                                                            }, 400);
                                                        } catch {
                                                            setShowZeroHint(false);
                                                        }
                                                    }}
                                                />
                                            </Form.Item>
                                        );
                                    }
                                    if (method === "kbps") {
                                        return (
                                            <Form.Item
                                                name="kps"
                                                label="Kilobits per Second"
                                                required
                                                rules={[
                                                    {
                                                        validator: (_, value) => {
                                                            const numberValue = Number(value);
                                                            if (value === undefined || value === "") {
                                                                return Promise.reject(
                                                                    new Error("Please input Kilobits per Second")
                                                                );
                                                            }
                                                            if (
                                                                !/^(0|[1-9]\d*)$/.test(value) ||
                                                                isNaN(numberValue) ||
                                                                numberValue < 0 ||
                                                                numberValue > 10000000
                                                            ) {
                                                                return Promise.reject(
                                                                    new Error("Value must be between 0 and 10000000")
                                                                );
                                                            }
                                                            return Promise.resolve();
                                                        }
                                                    }
                                                ]}
                                                extra={
                                                    showZeroHint ? (
                                                        <span style={{color: "#ff4d4f"}}>
                                                            0: Do not allow packets to pass
                                                        </span>
                                                    ) : null
                                                }
                                            >
                                                <Input
                                                    style={{width: "280px"}}
                                                    placeholder="Range: 0-10000000"
                                                    onBlur={async e => {
                                                        const {value} = e.target;
                                                        try {
                                                            await form.validateFields(["kps"]);
                                                            setTimeout(() => {
                                                                setShowZeroHint(value === "0");
                                                            }, 400);
                                                        } catch {
                                                            setShowZeroHint(false);
                                                        }
                                                    }}
                                                />
                                            </Form.Item>
                                        );
                                    }
                                    return null;
                                }}
                            </Form.Item>
                        </>
                    )}
                </Form>
            </Modal>
        ) : null;
    }
);
export default PortConfigDefaultViewModal;
