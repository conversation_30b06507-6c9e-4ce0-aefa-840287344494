import {Button, Form, Input, Modal, Flex, Divider, message} from "antd";
import {useState, useImperativeHandle, forwardRef, useRef} from "react";
import {copyTemplate} from "@/modules-ampcon/apis/campus_blueprint_api";

const CopyModal = forwardRef((props, ref) => {
    const [nameList, setNameList] = useState([]);
    const [templateId, setTemplateId] = useState();
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [createCopyForm] = Form.useForm();
    const copyModalSuccessCallback = useRef(null);
    useImperativeHandle(ref, () => ({
        showCreateCopyModal: (nameList, id, copyName, onSuccess) => {
            setNameList(nameList);
            setTemplateId(id);
            createCopyForm.resetFields();
            setIsModalOpen(true);
            createCopyForm.setFieldsValue({name: `${copyName}:::${getDate()}`, src_name: copyName});
            copyModalSuccessCallback.current = onSuccess;
        }
    }));
    const validateName = async (_, value) => {
        if (nameList.includes(value)) {
            return Promise.reject(new Error("The configuration name already exists"));
        }
        return Promise.resolve();
    };
    const handleCreateCopy = async () => {
        const res = await copyTemplate(templateId, createCopyForm.getFieldValue("name"));
        if (res.status === 200) {
            setIsModalOpen(false);
            createCopyForm.resetFields();
            message.success(res.msg);
        } else {
            message.error(res.msg);
        }
        if (copyModalSuccessCallback.current) {
            copyModalSuccessCallback.current();
        }
    };
    return isModalOpen ? (
        <Modal
            title={
                <div>
                    Copy
                    <Divider style={{marginTop: 8, marginBottom: 0}} />
                </div>
            }
            open={isModalOpen}
            onOk={() => {}}
            onCancel={() => {
                createCopyForm.resetFields();
                setIsModalOpen(false);
            }}
            className="ampcon-middle-modal"
            footer={
                <Flex vertical>
                    <Divider style={{marginTop: 0, marginBottom: 20}} />
                    <Flex justify="flex-end">
                        <Button
                            onClick={() => {
                                setIsModalOpen(false);
                                createCopyForm.resetFields();
                            }}
                        >
                            Cancel
                        </Button>
                        <Button type="primary" onClick={handleCreateCopy}>
                            Apply
                        </Button>
                    </Flex>
                </Flex>
            }
        >
            <Form labelAlign="left" labelCol={{flex: "140px"}} wrapperCol={{flex: "280px"}} form={createCopyForm}>
                <Form.Item
                    name="src_name"
                    label="Src Template Name"
                    rules={[{required: true, message: "Template Name is required."}]}
                >
                    <Input />
                </Form.Item>
                <Form.Item
                    name="name"
                    label="Template Name"
                    rules={[
                        {required: true, message: "Template Name is required."},
                        {validator: validateName},
                        {max: 32, message: "Template name cannot exceed 32 characters!"}
                    ]}
                >
                    <Input />
                </Form.Item>
            </Form>
        </Modal>
    ) : null;
});
export default CopyModal;
const getDate = () => {
    const timestamp = Date.now();
    const date = new Date(timestamp);
    const year = date.getFullYear();
    const month = date.getMonth() + 1;
    const day = date.getDate();
    const hours = date.getHours();
    const minutes = date.getMinutes();
    const seconds = date.getSeconds();

    return `${year}-${month}-${day}_${hours}:${minutes}:${seconds}`;
};
