import {Button, Form, message, Divider, Input, Modal} from "antd";
import {useState, forwardRef, useImperativeHandle, useEffect} from "react";

const NetworkModal = forwardRef(({handleAddNetwork, handleEditNetwork, serverData}, ref) => {
    const [isShowModal, setIsShowModal] = useState(false);
    const [form] = Form.useForm();
    const [mode, setMode] = useState("");
    const [existingData, setExistingData] = useState([]);
    const [currentRecord, setCurrentRecord] = useState(null);
    const [title, setTitle] = useState("Create Network");
    useImperativeHandle(ref, () => ({
        showNetworkModal: ({mode}, record) => {
            setMode(mode);
            if (mode === "edit") {
                setTitle("Edit");
            }
            if (mode === "view") {
                setTitle("View");
            }
            if (mode === "create") {
                setTitle("Create Network");
            }
            if (mode === "edit" || mode === "view") {
                form.setFieldsValue(record);
                setCurrentRecord(record);
            } else {
                setCurrentRecord(null);
            }
            setIsShowModal(true);
        }
    }));
    const validateNameUnique = (_, value) => {
        if (!value) return Promise.resolve();
        if (existingData.length === 0) return Promise.resolve();
        const isDuplicate = existingData.some(item => {
            if (mode === "edit" && item.id === currentRecord?.id) return false;
            return item.name === value;
        });

        return isDuplicate ? Promise.reject(new Error("VLAN name already exists")) : Promise.resolve();
    };

    const validateIdUnique = (_, value) => {
        if (!value) return Promise.resolve();
        if (existingData.length === 0) return Promise.resolve();
        const isDuplicate = existingData.some(item => {
            if (mode === "edit" && item.id === currentRecord?.id) return false;
            return item.id === value;
        });
        return isDuplicate ? Promise.reject(new Error("VLAN ID already exists")) : Promise.resolve();
    };

    useEffect(() => {
        if (isShowModal && serverData) {
            setExistingData(serverData);
        }
    }, [isShowModal, serverData]);

    return isShowModal ? (
        <Modal
            className="ampcon-middle-modal"
            title={
                <div>
                    {title}
                    <Divider style={{marginTop: 8, marginBottom: 0}} />
                </div>
            }
            open={isShowModal}
            onOk={() => {}}
            onCancel={() => {
                setIsShowModal(false);
                form.resetFields();
            }}
            footer={
                mode === "view" ? null : (
                    <div>
                        <Divider style={{marginTop: 0, marginBottom: 20}} />
                        <Button
                            onClick={() => {
                                setIsShowModal(false);
                                form.resetFields();
                            }}
                        >
                            Cancel
                        </Button>
                        <Button
                            type="primary"
                            onClick={() => {
                                form.submit();
                            }}
                        >
                            Apply
                        </Button>
                    </div>
                )
            }
        >
            <Form
                layout="horizontal"
                labelAlign="left"
                labelCol={{span: 6}}
                wrapperCol={{span: 17}}
                labelWrap
                className="label-wrap"
                form={form}
                style={{minHeight: "267.23px"}}
                ref={form}
                validateTrigger={["onBlur", "onSubmit"]}
                onFinish={() => {
                    try {
                        if (mode === "create") {
                            handleAddNetwork(form.getFieldsValue());
                            message.success("Network created successfully");
                        }
                        if (mode === "edit") {
                            handleEditNetwork(form.getFieldsValue());
                            message.success("Network edited successfully");
                        }
                    } catch (error) {
                        message.error("An error occurred while processing the network");
                        console.error(error);
                    } finally {
                        setIsShowModal(false);
                        form.resetFields();
                    }
                }}
            >
                <Form.Item
                    name="name"
                    validateFirst
                    rules={[
                        {
                            required: true,
                            message: "Please input name"
                        },
                        {max: 32, message: "Network name cannot exceed 32 characters!"},
                        {validator: validateNameUnique}
                    ]}
                    label="Name"
                >
                    <Input style={{width: "280px"}} disabled={mode === "view" || mode === "edit"} />
                </Form.Item>
                <Form.Item
                    name="id"
                    validateFirst
                    rules={[
                        {
                            required: true,
                            message: "Please input id"
                        },
                        {
                            validator: (_, value) => {
                                if (
                                    Number.isInteger(Number(value)) &&
                                    value > 1 &&
                                    value <= 4094 &&
                                    /^(0|[1-9]\d*)$/.test(value)
                                ) {
                                    return Promise.resolve();
                                }
                                return Promise.reject(new Error(""));
                            },
                            message: "VlAN ID must be an integer between 2 and 4094"
                        },
                        {validator: validateIdUnique}
                    ]}
                    label="VLAN ID"
                >
                    <Input style={{width: "280px"}} disabled={mode === "view"} />
                </Form.Item>
            </Form>
        </Modal>
    ) : null;
});
export default NetworkModal;
