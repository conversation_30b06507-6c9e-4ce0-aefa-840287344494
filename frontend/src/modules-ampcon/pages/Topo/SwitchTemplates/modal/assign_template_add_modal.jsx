import {<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>lex, message} from "antd";
import {useState, forwardRef, useImperativeHandle, useRef, useEffect} from "react";
import {AmpConCustomTable, createColumnConfig, TableFilterDropdown} from "@/modules-ampcon/components/custom_table";
import {confirmModalAction} from "@/modules-ampcon/components/custom_modal";
import {getSwitchList} from "@/modules-ampcon/apis/campus_blueprint_api";

const AssignTemplateAddModal = forwardRef(({handleAddSwitch, switchData, templateName}, ref) => {
    const [isShowModal, setIsShowModal] = useState(false);
    const tableModalRef = useRef();
    const [selectedRowKeys, setSelectedRowKeys] = useState([]);
    const [selectedRows, setSelectedRows] = useState([]);
    const matchFieldsList = [
        {name: "host_name", matchMode: "fuzzy"},
        {name: "sn", matchMode: "fuzzy"},
        {name: "mgt_ip", matchMode: "fuzzy"},
        {name: "platform_model", matchMode: "fuzzy"},
        {name: "site", matchMode: "fuzzy"},
        {name: "template_name", matchMode: "fuzzy"}
    ];
    const searchFieldsList = ["host_name", "sn", "mgt_ip", "platform_model", "site", "template_name"];
    const [snList, setSnList] = useState([]);
    const columns = [
        createColumnConfig("Sysname", "host_name", TableFilterDropdown),
        createColumnConfig("SN", "sn", TableFilterDropdown),
        createColumnConfig("Mgmt IP", "mgt_ip", TableFilterDropdown),
        createColumnConfig("Model", "platform_model", TableFilterDropdown),
        createColumnConfig("Site", "site", TableFilterDropdown),
        {
            ...createColumnConfig("Configuration Template", "template_name", TableFilterDropdown),
            render: text => text?.split?.("-")[0] || ""
        }
    ];
    useImperativeHandle(ref, () => ({
        showAddModal: () => {
            if (switchData) {
                setSnList(switchData.map(item => item.sn));
                const keys = switchData.map(item => item.id);
                setSelectedRowKeys(keys);
                setSelectedRows(switchData);
            }
            setIsShowModal(true);
        }
    }));
    const handleSave = async () => {
        try {
            const response = await getSwitchList([], 1, 1000, [], [], []);
            const fullData = response?.data || [];
            const completedRows = selectedRows.map(row => {
                const full = fullData.find(item => item.id === row.id);
                const merged = full ? {...row, ...full} : row;
                return {
                    ...merged,
                    template_name: merged.template_name?.split?.("-")[0] || ""
                };
            });
            const hasDifferentTemplate = completedRows.some(
                row => !!row.template_name && row.template_name !== templateName
            );
            if (hasDifferentTemplate) {
                const affectedSwitches = completedRows
                    .filter(row => !!row.template_name && row.template_name !== templateName)
                    .map(row => row.host_name)
                    .join(", ");
                confirmModalAction(
                    `This template will replace the template previously applied to the selected switches: ${affectedSwitches}.`,
                    () => {
                        handleAddSwitch(completedRows);
                        setIsShowModal(false);
                        setSelectedRowKeys([]);
                        setSelectedRows([]);
                        message.success("Template applied successfully");
                    }
                );
            } else {
                handleAddSwitch(completedRows);
                setIsShowModal(false);
                setSelectedRowKeys([]);
                setSelectedRows([]);
            }
        } catch (error) {
            message.error("An error occurred while applying the template");
        }
    };

    return isShowModal ? (
        <Modal
            className="ampcon-max-modal"
            title="Add Switch"
            open={isShowModal}
            onCancel={() => {
                setIsShowModal(false);
            }}
            footer={
                <Flex vertical>
                    <Divider />
                    <Flex justify="flex-end">
                        <Button
                            htmlType="button"
                            onClick={() => {
                                setIsShowModal(false);
                                setSelectedRowKeys([]);
                                setSelectedRows([]);
                            }}
                        >
                            Cancel
                        </Button>
                        <Button type="primary" onClick={handleSave}>
                            Save
                        </Button>
                    </Flex>
                </Flex>
            }
        >
            <Divider />
            <AmpConCustomTable
                rowSelection={{
                    selectedRowKeys,
                    selectedRows,
                    onChange: (keys, rows) => {
                        setSelectedRowKeys(keys);
                        setSelectedRows(rows);
                    }
                }}
                columns={columns}
                fetchAPIInfo={getSwitchList}
                fetchAPIParams={[snList]}
                searchFieldsList={searchFieldsList}
                matchFieldsList={matchFieldsList}
                ref={tableModalRef}
                onDataSourceLoaded={data => {
                    setSelectedRows(prev =>
                        prev.map(item => {
                            const fullItem = data.find(d => d.id === item.id);
                            return fullItem ? {...item, ...fullItem} : item;
                        })
                    );
                }}
            />
        </Modal>
    ) : null;
});
export default AssignTemplateAddModal;
