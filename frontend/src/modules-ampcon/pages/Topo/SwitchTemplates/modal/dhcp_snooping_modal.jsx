import {Button, Form, message, Divider, Select, Modal, Input} from "antd";
import {useState, forwardRef, useImperativeHandle} from "react";

const DhcpSnoopingModal = forwardRef(({handleAddServer, handleEditServer, serverData, networkData}, ref) => {
    const [isShowModal, setIsShowModal] = useState(false);
    const [form] = Form.useForm();
    const [mode, setMode] = useState("");
    const [currentRecord, setCurrentRecord] = useState(null);
    const getUsedNetworks = () => {
        return serverData.networks?.map(item => item.network) || [];
    };
    const networkOptions =
        networkData
            ?.filter(item => !getUsedNetworks().includes(`${item.name}(${item.id})`))
            .map(item => ({
                label: `${item.name} (${item.id})`,
                value: item.name,
                id: item.id
            })) || [];
    const handleNetworkChange = (value, option) => {
        form.setFieldsValue({
            id: option.id
        });
    };
    useImperativeHandle(ref, () => ({
        showDhcpSnoopingModal: ({mode}, record) => {
            setMode(mode);
            if (mode === "edit") {
                form.setFieldsValue(record);
                setCurrentRecord(record);
            }
            setIsShowModal(true);
        }
    }));

    return isShowModal ? (
        <Modal
            className="ampcon-middle-modal"
            title={
                <div>
                    {mode === "create" ? "Create DHCP Snooping" : "Edit"}
                    <Divider style={{marginTop: 8, marginBottom: 0}} />
                </div>
            }
            open={isShowModal}
            onOk={() => {}}
            onCancel={() => {
                setIsShowModal(false);
                form.resetFields();
            }}
            footer={
                <div>
                    <Divider style={{marginTop: 0, marginBottom: 20}} />
                    <Button
                        onClick={() => {
                            setIsShowModal(false);
                            form.resetFields();
                        }}
                    >
                        Cancel
                    </Button>
                    <Button
                        type="primary"
                        onClick={() => {
                            form.submit();
                        }}
                    >
                        Apply
                    </Button>
                </div>
            }
        >
            <Form
                layout="horizontal"
                labelAlign="left"
                labelCol={{span: 6}}
                wrapperCol={{span: 17}}
                labelWrap
                className="label-wrap"
                form={form}
                style={{minHeight: "267.23px"}}
                ref={form}
                validateTrigger={["onBlur", "onSubmit"]}
                onFinish={() => {
                    try {
                        if (mode === "create") {
                            handleAddServer(form.getFieldsValue());
                            message.success("DHCP Snooping created successfully");
                        }
                        if (mode === "edit") {
                            handleEditServer(form.getFieldsValue(), currentRecord);
                            message.success("DHCP Snooping edited successfully");
                        }
                    } catch (error) {
                        message.error("An error occurred while processing the DHCP Snooping");
                        console.error(error);
                    } finally {
                        setIsShowModal(false);
                        form.resetFields();
                    }
                }}
            >
                <Form.Item
                    name="network"
                    rules={[{required: true, message: "Please input server address"}]}
                    label="Network"
                >
                    <Select style={{width: "280px"}} options={networkOptions} onChange={handleNetworkChange} />
                </Form.Item>
                <Form.Item name="id" validateFirst rules={[{required: true}]} label="VLAN ID" initialValue="">
                    <Input style={{width: "280px"}} disabled />
                </Form.Item>
            </Form>
        </Modal>
    ) : null;
});
export default DhcpSnoopingModal;
