import {Button, Form, Input, Modal, Flex, Divider} from "antd";
import {useState, useImperativeHandle, forwardRef} from "react";

const CreateNameModal = forwardRef((props, ref) => {
    const {onApply} = props;
    const [nameList, setNameList] = useState([]);
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [form] = Form.useForm();
    useImperativeHandle(ref, () => ({
        showCreateNameModal: (nameList = []) => {
            setNameList(nameList);
            form.resetFields();
            setIsModalOpen(true);
        }
    }));
    const validateName = async (_, value) => {
        if (nameList.includes(value)) {
            return Promise.reject(new Error("The configuration name already exists"));
        }
        return Promise.resolve();
    };
    const handleApply = async () => {
        try {
            const values = await form.validateFields();
            onApply(values.name);
            setIsModalOpen(false);
            form.resetFields();
        } catch (error) {
            console.error("Validation failed:", error);
        }
    };
    return isModalOpen ? (
        <Modal
            title={
                <div>
                    New Template
                    <Divider style={{marginTop: 8, marginBottom: 0}} />
                </div>
            }
            open={isModalOpen}
            onOk={() => {}}
            onCancel={() => {
                form.resetFields();
                setIsModalOpen(false);
            }}
            className="ampcon-middle-modal"
            footer={
                <Flex vertical>
                    <Divider style={{marginTop: 0, marginBottom: 20}} />
                    <Flex justify="flex-end">
                        <Button
                            onClick={() => {
                                setIsModalOpen(false);
                                form.resetFields();
                            }}
                        >
                            Cancel
                        </Button>
                        <Button type="primary" onClick={handleApply}>
                            Apply
                        </Button>
                    </Flex>
                </Flex>
            }
        >
            <Form labelAlign="left" labelCol={{flex: "120px"}} wrapperCol={{flex: "280px"}} form={form}>
                <Form.Item
                    name="name"
                    label="Template Name"
                    rules={[
                        {required: true, message: "Template Name is required."},
                        {validator: validateName},
                        {max: 32, message: "Template name cannot exceed 32 characters!"}
                    ]}
                >
                    <Input />
                </Form.Item>
            </Form>
        </Modal>
    ) : null;
});
export default CreateNameModal;
