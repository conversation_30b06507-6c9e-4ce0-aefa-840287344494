import {useState, useRef} from "react";
import SwitchTemplatesTbleView from "@/modules-ampcon/pages/Topo/SwitchTemplates/switch_templates_table_view";
import SwitchTemplatesCreate from "@/modules-ampcon/pages/Topo/SwitchTemplates/switch_templates_create";
import SwitchTemplatesView from "@/modules-ampcon/pages/Topo/SwitchTemplates/switch_templates_view";
import TemplateDeploymentStatus from "@/modules-ampcon/pages/Topo/SwitchTemplates/template_deployment_status";
import {viewTemplate} from "@/modules-ampcon/apis/campus_blueprint_api";
import {message} from "antd";

const SwitchTemplates = () => {
    const [isInTableView, setIsInTableView] = useState(true);
    const [isInCreateView, setIsInCreateView] = useState(false);
    const [isInEditView, setIsInEditView] = useState(false);
    const [isInDeploymentStatusView, setIsInDeploymentStatusView] = useState(false);
    const [templateName, setTemplateName] = useState("");
    const [hasRunningDeployment, setHasRunningDeployment] = useState(false);
    const tableRef = useRef(null);
    const [currentTemplateId, setCurrentTemplateId] = useState(null);
    const [switchTemplateData, setSwitchTemplateData] = useState(null);
    const [allTableNames, setAllTableNames] = useState([]);
    const createSwitchTemplatesCallback = (templateName, allNames) => {
        setTemplateName(templateName);
        setAllTableNames(allNames);
        setIsInTableView(false);
        setIsInEditView(false);
        setIsInCreateView(true);
    };
    const viewSwitchTemplatesCallback = async (id, name, allNames) => {
        try {
            setAllTableNames(allNames);
            const res = await viewTemplate(id);
            if (res?.status === 200) {
                setTemplateName(name);
                setSwitchTemplateData(res.data);
                setCurrentTemplateId(id);
                setIsInCreateView(false);
                setIsInTableView(false);
                setIsInEditView(true);
            } else {
                message.error("Failed to get template details");
            }
        } catch (err) {
            console.error(err);
            message.error("View template failed");
        }
    };
    const deploymentStatusCallback = record => {
        setCurrentTemplateId(record.id);
        setIsInTableView(false);
        setIsInCreateView(false);
        setIsInEditView(false);
        setIsInDeploymentStatusView(true);
    };
    const backToTableViewCallback = async () => {
        setIsInEditView(false);
        setIsInCreateView(false);
        setIsInTableView(true);
        tableRef.current?.refreshTable?.();
    };
    const updateDeploymentStatus = data => {
        setHasRunningDeployment(data.some(item => item.status === 1));
    };
    const getSwitchTemplatesComponent = () => {
        if (isInTableView) {
            return (
                <SwitchTemplatesTbleView
                    ref={tableRef}
                    createSwitchTemplatesCallback={createSwitchTemplatesCallback}
                    viewSwitchTemplatesCallback={viewSwitchTemplatesCallback}
                    deploymentStatusCallback={deploymentStatusCallback}
                    hasRunningDeployment={hasRunningDeployment}
                    updateDeploymentStatus={updateDeploymentStatus}
                />
            );
        }
        if (isInCreateView) {
            return (
                <SwitchTemplatesCreate
                    backToTableViewCallback={backToTableViewCallback}
                    templateName={templateName}
                    allTableNames={allTableNames}
                    onSaveSuccess={() => {
                        backToTableViewCallback();
                    }}
                />
            );
        }
        if (isInEditView) {
            return (
                <SwitchTemplatesView
                    backToTableViewCallback={backToTableViewCallback}
                    templateName={templateName}
                    allData={switchTemplateData}
                    allTableNames={allTableNames}
                    currentTemplateId={currentTemplateId}
                    onSaveSuccess={() => {
                        backToTableViewCallback();
                    }}
                />
            );
        }
        if (isInDeploymentStatusView) {
            return (
                <TemplateDeploymentStatus
                    backToTableViewCallback={backToTableViewCallback}
                    updateDeploymentStatus={updateDeploymentStatus}
                    currentTemplateId={currentTemplateId}
                />
            );
        }
    };

    return getSwitchTemplatesComponent();
};

export default SwitchTemplates;
