import {<PERSON><PERSON>, <PERSON>, Space, message} from "antd";
import {AmpConCustomTable, createColumnConfig} from "@/modules-ampcon/components/custom_table";
import Icon from "@ant-design/icons";
import {addSvg, importSvg} from "@/utils/common/iconSvg";
import {useEffect, useRef} from "react";
import {getSwitchTemplateList, deleteTemplate, downloadTemplate} from "@/modules-ampcon/apis/campus_blueprint_api";
import styles from "@/modules-ampcon/pages/Topo/Topology/topo.module.scss";
import {confirmModalAction} from "@/modules-ampcon/components/custom_modal";
import CopyModal from "@/modules-ampcon/pages/Topo/SwitchTemplates/modal/copy_modal";
import ImporModal from "@/modules-ampcon/pages/Topo/SwitchTemplates/modal/import_modal";
import CreateNameModal from "@/modules-ampcon/pages/Topo/SwitchTemplates/modal/create_name_modal";

const SwitchTemplatesTbleView = ({
    createSwitchTemplatesCallback,
    viewSwitchTemplatesCallback,
    deploymentStatusCallback,
    hasRunningDeployment,
    updateDeploymentStatus
}) => {
    const tableRef = useRef();
    const matchFieldsList = [
        {name: "create_time", matchMode: "fuzzy"},
        {name: "topology_name", matchMode: "fuzzy"},
        {name: "site_id", matchMode: "fuzzy"},
        {name: "id", matchMode: "fuzzy"},
        {name: "modified_time", matchMode: "fuzzy"},
        {name: "site_name", matchMode: "fuzzy"},
        {name: "type", matchMode: "fuzzy"}
    ];

    const searchFieldsList = ["topology_name", "site_id", "create_time", "id", "modified_time", "site_name", "type"];
    const copyModalRef = useRef();
    const importModalRef = useRef();
    const CreateNameModalRef = useRef();
    const handleImportSuccess = async (templateId, templateName) => {
        try {
            if (templateId) {
                const names = getAllTemplateNames();
                viewSwitchTemplatesCallback(templateId, templateName, names);
            } else {
                message.error(`Template "${templateName}" not found after import`);
            }
        } catch (error) {
            console.error("Error handling import success:", error);
            message.error("Failed to navigate to imported template");
        }
    };
    const delete_fabric = record => {
        confirmModalAction("Are you sure want to delete this template?", () => {
            deleteTemplate(record.id).then(res => {
                if (res.status === 200) {
                    message.success(res.msg);
                    tableRef.current.refreshTable();
                } else {
                    message.error(res.msg);
                }
            });
        });
    };
    const getAllTemplateNames = () => {
        const tableData = tableRef.current?.getTableData() || [];
        return tableData.map(item => item.name);
    };
    const columns = [
        createColumnConfig("Template", "name", null, "", 250),
        createColumnConfig("Switches", "switch_num", null, "", 250),
        {
            title: "Operation",
            width: 500,
            render: (_, record) => (
                <div>
                    <Space size="middle" className={styles.actionLink}>
                        <a
                            onClick={() => {
                                const names = getAllTemplateNames();
                                viewSwitchTemplatesCallback(record.id, record.name, names);
                            }}
                        >
                            View
                        </a>
                        <a
                            onClick={() => {
                                const names = getAllTemplateNames();
                                copyModalRef.current.showCreateCopyModal(names, record.id, record.name, () => {
                                    tableRef.current.refreshTable();
                                });
                            }}
                        >
                            Copy
                        </a>
                        <a
                            onClick={async () => {
                                try {
                                    const res = await downloadTemplate(record.id);
                                    const blob = new Blob([res], {type: "application/json"});
                                    const url = window.URL.createObjectURL(blob);
                                    const link = document.createElement("a");
                                    link.href = url;
                                    link.download = `${record.name}.json`;
                                    link.click();
                                    window.URL.revokeObjectURL(url);
                                } catch (err) {
                                    message.error("Download failed");
                                    console.error(err);
                                }
                            }}
                        >
                            {" "}
                            Download
                        </a>
                        <a onClick={() => deploymentStatusCallback(record)}> Template Deployment Status</a>
                        <a onClick={() => delete_fabric(record)} disabled={hasRunningDeployment}>
                            Delete
                        </a>
                    </Space>
                </div>
            )
        }
    ];
    const handleApply = templateName => {
        const names = getAllTemplateNames();
        createSwitchTemplatesCallback(templateName, names); // 触发父组件的跳转逻辑
    };
    useEffect(() => {}, []);
    return (
        <Card style={{display: "flex", flex: 1}}>
            <h2 style={{margin: "8px 0 20px"}}>Switch Templates</h2>
            <AmpConCustomTable
                ref={tableRef}
                columns={columns}
                fetchAPIInfo={getSwitchTemplateList}
                searchFieldsList={searchFieldsList}
                matchFieldsList={matchFieldsList}
                extraButton={
                    <>
                        <Button
                            type="primary"
                            onClick={() => {
                                const names = getAllTemplateNames();
                                CreateNameModalRef.current.showCreateNameModal(names);
                            }}
                        >
                            <Icon component={addSvg} />
                            Template
                        </Button>
                        <Button
                            onClick={() => {
                                const names = getAllTemplateNames();
                                importModalRef.current.showCreateImportModal(names, handleImportSuccess);
                            }}
                        >
                            <Icon component={importSvg} />
                            Import
                        </Button>
                    </>
                }
            />
            <CopyModal ref={copyModalRef} />
            <ImporModal ref={importModalRef} />
            <CreateNameModal ref={CreateNameModalRef} onApply={handleApply} />
        </Card>
    );
};

export default SwitchTemplatesTbleView;
