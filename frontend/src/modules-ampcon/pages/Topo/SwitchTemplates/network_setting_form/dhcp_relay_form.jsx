import {Space, Form, message, Divider, Radio} from "antd";
import {addGreenSvg} from "@/utils/common/iconSvg";
import {AmpConCustomTable} from "@/modules-ampcon/components/custom_table";
import DhcpRelayModal from "@/modules-ampcon/pages/Topo/SwitchTemplates/modal/dhcp_relay_modal";
import Icon from "@ant-design/icons";
import {useState, useRef, useEffect, forwardRef, useImperativeHandle} from "react";
import {confirmModalAction} from "@/modules-ampcon/components/custom_modal";

const DhcpRelayForm = forwardRef(({data, tableStyle, networkData, setSwitchTemplateData}, ref) => {
    const [isEnable, setIsEnable] = useState(false);
    const [form] = Form.useForm();
    const dhcpRelayModalRef = useRef(null);
    const [dataSource, setDataSource] = useState([]);
    useEffect(() => {
        if (data) {
            setIsEnable(data.configuration);
            form.setFieldsValue({
                enable: data.configuration ? "enable" : "disable"
            });
            setDataSource(data.networks || []);
        }
    }, [data]);
    const updateDataRelay = newNetworks => {
        setSwitchTemplateData(prev => ({
            ...prev,
            switch: {
                ...prev.switch,
                dhcpSnooping: {
                    configuration: isEnable,
                    networks: newNetworks
                }
            }
        }));
    };
    const handleAddServer = newData => {
        const updatedData = [...dataSource, newData];
        setDataSource(updatedData);
        updateDataRelay(updatedData);
    };
    const handleEditServer = (newData, currentRecord) => {
        const updatedData = dataSource.map(item =>
            item.network === currentRecord.network && item.ip === currentRecord.ip ? newData : item
        );
        setDataSource(updatedData);
        updateDataRelay(updatedData);
    };

    const handleDeleteServer = record => {
        const updatedData = dataSource.filter(item => item.id !== record.id);
        setDataSource(updatedData);
        updateDataRelay(updatedData);
    };
    const handleConfigChange = e => {
        const enabled = e.target.value === "enable";
        setIsEnable(enabled);

        setSwitchTemplateData(prev => ({
            ...prev,
            switch: {
                ...prev.switch,
                dhcpRelay: {
                    configuration: enabled,
                    networks: enabled ? dataSource : []
                }
            }
        }));
    };
    // useImperativeHandle(ref, () => ({
    //     validate: () => {
    //         if (isEnable && serverData.length === 0) {
    //             Promise.reject(new Error("Please add NAC Server"));
    //             message.error("Please add NAC Server");
    //             return false;
    //         }
    //         return true;
    //     }
    // }));

    const columns = [
        {
            title: "Network",
            dataIndex: "network",
            width: "25%",
            render: (_, record) => {
                return record.network;
            },
            sorter: (a, b) => a.network.localeCompare(b.network)
        },
        {
            title: "VLAN ID",
            dataIndex: "id",
            width: "25%",
            render: (_, record) => {
                return record.id;
            },
            sorter: (a, b) => a.id.localeCompare(b.id)
        },
        {
            title: "DHCP Server",
            dataIndex: "ip",
            width: "25%",
            render: (_, record) => {
                return record.ip;
            },
            sorter: (a, b) => a.ip.localeCompare(b.ip)
        },
        {
            title: "Operation",
            render: (_, record) => {
                return (
                    <Space size="large" className="actionLink">
                        <a
                            onClick={() => {
                                dhcpRelayModalRef.current.showNetworkModal({mode: "edit"}, record);
                            }}
                        >
                            Edit
                        </a>
                        <a
                            onClick={() => {
                                confirmModalAction("Are you sure want to delete?", () => {
                                    try {
                                        handleDeleteServer(record);
                                        message.success("Delete Successful");
                                    } catch (error) {
                                        message.error("Delete Failed");
                                    }
                                });
                            }}
                        >
                            Delete
                        </a>
                    </Space>
                );
            }
        }
    ];

    return (
        <>
            <h2>DHCP Relay</h2>
            <DhcpRelayModal
                ref={dhcpRelayModalRef}
                handleEditServer={handleEditServer}
                handleAddServer={handleAddServer}
                dhcpData={data}
                networkData={networkData}
            />
            <Form ref={ref} form={form} validateTrigger="onBlur" labelAlign="left" style={{width: 505}}>
                <Form.Item
                    name="enable"
                    label="Configuration"
                    labelCol={{style: {width: 175}}}
                    initialValue={isEnable ? "enable" : "disable"}
                >
                    <Radio.Group onChange={handleConfigChange}>
                        <Radio value="enable">Enable</Radio>
                        <Radio value="disable">Disable</Radio>
                    </Radio.Group>
                </Form.Item>
                {isEnable && (
                    <Form.Item style={{marginBottom: "0px"}} label="DHCP Network" labelCol={{style: {width: 175}}}>
                        <a
                            style={{
                                border: "none",
                                borderRadius: "4px",
                                color: "#14c9bb"
                            }}
                            onClick={() => {
                                dhcpRelayModalRef.current.showNetworkModal({mode: "create"});
                            }}
                        >
                            <Icon component={addGreenSvg} style={{marginRight: "8px"}} />
                            Add
                        </a>
                    </Form.Item>
                )}
            </Form>
            {isEnable && (
                <div>
                    <AmpConCustomTable
                        dataSource={dataSource}
                        columns={columns}
                        style={tableStyle}
                        // pagination={{
                        //     defaultPageSize: 10,
                        //     showSizeChanger: true,
                        //     hideOnSinglePage: serverData.length <= 10
                        // }}
                    />
                </div>
            )}

            <Divider style={{marginTop: "32px", marginBottom: "32px"}} />
        </>
    );
});
export default DhcpRelayForm;
