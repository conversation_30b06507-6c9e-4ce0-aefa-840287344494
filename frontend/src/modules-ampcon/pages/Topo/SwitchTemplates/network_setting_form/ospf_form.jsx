import {Space, Form, Divider, Radio, message} from "antd";
import {AmpConCustomTable} from "@/modules-ampcon/components/custom_table";
import {useState, useEffect, forwardRef, useImperativeHandle, useRef} from "react";
import Icon from "@ant-design/icons";
import {addGreenSvg} from "@/utils/common/iconSvg";
import {confirmModalAction} from "@/modules-ampcon/components/custom_modal";
import OspfModal from "@/modules-ampcon/pages/Topo/SwitchTemplates/modal/ospf_modal";

const OspfForm = forwardRef(({ospData, tableStyle, setSwitchTemplateData}, ref) => {
    const [isEnable, setIsEnable] = useState(false);
    const [form] = Form.useForm();
    const ospfModalRef = useRef(null);
    const [dataSource, setDataSource] = useState([]);
    const displayTypes = {
        default: "Default",
        stub: "Stub",
        nssa: "NSSA"
    };
    useEffect(() => {
        if (ospData) {
            form.setFieldsValue({
                enable: ospData.configuration ? "enable" : "disable"
            });
            setIsEnable(ospData.configuration);
            setDataSource(ospData.areas || []);
        }
    }, [ospData, form]);

    useImperativeHandle(ref, () => ({
        // validate: () => {
        //     if (isEnable && serverData.length === 0) {
        //         Promise.reject(new Error("Please add NAC Server"));
        //         message.error("Please add NAC Server");
        //         return false;
        //     }
        //     return true;
        // }
    }));
    const updateOspf = newData => {
        setSwitchTemplateData(prev => ({
            ...prev,
            switch: {
                ...prev.switch,
                ospf: {
                    configuration: isEnable,
                    areas: newData
                }
            }
        }));
    };
    const handleAddServer = newData => {
        const updatedData = [...dataSource, newData];
        setDataSource(updatedData);
        updateOspf(updatedData);
    };
    const handleEditServer = (newData, currentRecord) => {
        const updatedData = dataSource.map(item =>
            item.ipv4 === currentRecord.ipv4 && item.id === currentRecord.id ? newData : item
        );
        setDataSource(updatedData);
        updateOspf(updatedData);
    };

    const handleDeleteServer = record => {
        const updatedData = dataSource.filter(item => !(item.ipv4 === record.ipv4 && item.id === record.id));
        setDataSource(updatedData);
        updateOspf(updatedData);
    };
    const handleConfigChange = e => {
        const enabled = e.target.value === "enable";
        setIsEnable(enabled);
        setSwitchTemplateData(prev => ({
            ...prev,
            switch: {
                ...prev.switch,
                ospf: {
                    configuration: enabled,
                    areas: enabled ? dataSource : []
                }
            }
        }));
    };
    const columns = [
        {
            title: "IPv4/Prefixlen",
            dataIndex: "ipv4",
            width: "25%",
            render: (_, record) => {
                let list = [];
                if (Array.isArray(record.ipv4)) {
                    list = record.ipv4;
                } else if (typeof record.ipv4 === "string") {
                    list = record.ipv4.split(",").filter(Boolean);
                }

                return (
                    <div>
                        {list.map((ip, i) => (
                            <div key={i}>{ip}</div>
                        ))}
                    </div>
                );
            },
            sorter: (a, b) => {
                const aFirst = Array.isArray(a.ipv4) ? a.ipv4[0] : a.ipv4?.split?.(",")[0] || "";
                const bFirst = Array.isArray(b.ipv4) ? b.ipv4[0] : b.ipv4?.split?.(",")[0] || "";
                return aFirst.localeCompare(bFirst);
            }
        },
        {
            title: "Area ID",
            dataIndex: "id",
            width: "25%",
            render: (_, record) => {
                return record.id;
            },
            sorter: (a, b) => a.id.localeCompare(b.id)
        },
        {
            title: "Area Type",
            dataIndex: "type",
            width: "25%",
            render: (_, record) => {
                return displayTypes[record.type.toLowerCase()] || record.type;
            },
            sorter: (a, b) => a.type.localeCompare(b.type)
        },
        {
            title: "Operation",
            render: (_, record) => {
                return (
                    <Space size="large" className="actionLink">
                        <a
                            onClick={() => {
                                ospfModalRef.current.showOspfModal({mode: "edit"}, record);
                            }}
                        >
                            Edit
                        </a>
                        <a
                            onClick={() => {
                                confirmModalAction("Are you sure want to delete?", () => {
                                    try {
                                        handleDeleteServer(record);
                                        message.success("Delete Successful");
                                    } catch (error) {
                                        message.error("Delete Failed");
                                    }
                                });
                            }}
                        >
                            Delete
                        </a>
                    </Space>
                );
            }
        }
    ];

    return (
        <>
            <h2>OSPF</h2>
            <OspfModal
                ref={ospfModalRef}
                ospData={ospData}
                handleAddServer={handleAddServer}
                handleEditServer={handleEditServer}
            />
            <Form ref={ref} form={form} validateTrigger="onBlur" labelAlign="left" style={{width: 505}}>
                <Form.Item
                    name="enable"
                    label="Configuration"
                    labelCol={{style: {width: 175}}}
                    initialValue={isEnable ? "enable" : "disable"}
                >
                    <Radio.Group onChange={handleConfigChange}>
                        <Radio value="enable">Enable</Radio>
                        <Radio value="disable">Disable</Radio>
                    </Radio.Group>
                </Form.Item>
                {isEnable && (
                    <Form.Item style={{marginBottom: "0px"}} label="OSPF" labelCol={{style: {width: 175}}}>
                        <a
                            style={{
                                border: "none",
                                borderRadius: "4px",
                                color: "#14c9bb"
                            }}
                            onClick={() => {
                                ospfModalRef.current.showOspfModal({mode: "create"});
                            }}
                        >
                            <Icon component={addGreenSvg} style={{marginRight: "8px"}} />
                            OSPF
                        </a>
                    </Form.Item>
                )}
            </Form>
            {isEnable && dataSource.length > 0 && (
                <div>
                    <AmpConCustomTable
                        dataSource={dataSource}
                        columns={columns}
                        style={tableStyle}
                        pagination={{
                            defaultPageSize: 10,
                            showSizeChanger: true,
                            hideOnSinglePage: dataSource.length <= 10
                        }}
                    />
                </div>
            )}
            <Divider style={{marginTop: "32px", marginBottom: "32px"}} />
        </>
    );
});
export default OspfForm;
