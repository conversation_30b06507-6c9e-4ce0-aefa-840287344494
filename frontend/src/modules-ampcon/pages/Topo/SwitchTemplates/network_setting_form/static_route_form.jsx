import {Space, Form, Input, Divider, Radio, message} from "antd";
import {AmpConCustomTable} from "@/modules-ampcon/components/custom_table";
import {useState, useEffect, forwardRef, useImperativeHandle, useRef} from "react";
import Icon from "@ant-design/icons";
import {addGreenSvg} from "@/utils/common/iconSvg";
import {confirmModalAction} from "@/modules-ampcon/components/custom_modal";
import StaticRouteModal from "../modal/static_route_modal";

const StaticRouteForm = forwardRef(({staticRouteData, tableStyle, setSwitchTemplateData}, ref) => {
    const [isEnable, setIsEnable] = useState(false);
    const [form] = Form.useForm();
    const staticRouteModalRef = useRef(null);
    const [dataSource, setDataSource] = useState([]);
    useEffect(() => {
        if (staticRouteData) {
            form.setFieldsValue({
                enable: staticRouteData.configuration ? "enable" : "disable"
            });
            setIsEnable(staticRouteData.configuration);
            setDataSource(staticRouteData.routes || []);
        }
    }, [staticRouteData, form]);

    useImperativeHandle(ref, () => ({
        // validate: () => {
        //     if (isEnable && serverData.length === 0) {
        //         Promise.reject(new Error("Please add NAC Server"));
        //         message.error("Please add NAC Server");
        //         return false;
        //     }
        //     return true;
        // }
    }));
    const updateStaticRoute = newData => {
        setSwitchTemplateData(prev => ({
            ...prev,
            switch: {
                ...prev.switch,
                staticRoute: {
                    configuration: isEnable,
                    routes: newData
                }
            }
        }));
    };
    const handleAddServer = newData => {
        const updatedData = [...dataSource, newData];
        setDataSource(updatedData);
        updateStaticRoute(updatedData);
    };
    const handleEditServer = (newData, currentRecord) => {
        const updatedData = dataSource.map(item =>
            item.destination === currentRecord.destination && item.nexthop === currentRecord.nexthop ? newData : item
        );
        setDataSource(updatedData);
        updateStaticRoute(updatedData);
    };

    const handleDeleteServer = record => {
        const updatedData = dataSource.filter(item => item.nexthop !== record.nexthop);
        setDataSource(updatedData);
        updateStaticRoute(updatedData);
    };
    const handleConfigChange = e => {
        const enabled = e.target.value === "enable";
        setIsEnable(enabled);
        setSwitchTemplateData(prev => ({
            ...prev,
            switch: {
                ...prev.switch,
                staticRoute: {
                    configuration: enabled,
                    routes: enabled ? dataSource : []
                }
            }
        }));
    };
    const columns = [
        {
            title: "Destination",
            dataIndex: "destination",
            width: "30%",
            render: (_, record) => {
                return record.destination;
            },
            sorter: (a, b) => a.destination.localeCompare(b.destination)
        },
        {
            title: "Next Hop",
            dataIndex: "nexthop",
            width: "30%",
            render: (_, record) => {
                let list = [];
                if (Array.isArray(record.nexthop)) {
                    list = record.nexthop;
                } else if (typeof record.nexthop === "string") {
                    list = record.nexthop.split(",").filter(Boolean);
                }
                return (
                    <div>
                        {list.map((ip, i) => (
                            <div key={i}>{ip}</div>
                        ))}
                    </div>
                );
            },
            sorter: (a, b) => {
                const aFirst = Array.isArray(a.nexthop) ? a.nexthop[0] : a.nexthop?.split?.(",")[0] || "";
                const bFirst = Array.isArray(b.nexthop) ? b.nexthop[0] : b.nexthop?.split?.(",")[0] || "";
                return aFirst.localeCompare(bFirst);
            }
        },
        {
            title: "Operation",
            render: (_, record) => {
                return (
                    <Space size="large" className="actionLink">
                        <a
                            onClick={() => {
                                staticRouteModalRef.current.showStaticRouteModal({mode: "edit"}, record);
                            }}
                        >
                            Edit
                        </a>
                        <a
                            onClick={() => {
                                confirmModalAction("Are you sure want to delete?", () => {
                                    try {
                                        handleDeleteServer(record);
                                        message.success("Delete Successful");
                                    } catch (error) {
                                        message.error("Delete Failed");
                                        console.error(error);
                                    }
                                });
                            }}
                        >
                            Delete
                        </a>
                    </Space>
                );
            }
        }
    ];

    return (
        <>
            <h2>Static Route</h2>
            <StaticRouteModal
                ref={staticRouteModalRef}
                staticRouteData={staticRouteData}
                handleAddServer={handleAddServer}
                handleEditServer={handleEditServer}
            />
            <Form ref={ref} form={form} validateTrigger="onBlur" labelAlign="left" style={{width: 505}}>
                <Form.Item
                    name="enable"
                    label="Configuration"
                    labelCol={{style: {width: 175}}}
                    initialValue={isEnable ? "enable" : "disable"}
                >
                    <Radio.Group onChange={handleConfigChange}>
                        <Radio value="enable">Enable</Radio>
                        <Radio value="disable">Disable</Radio>
                    </Radio.Group>
                </Form.Item>
                {isEnable && (
                    <Form.Item style={{marginBottom: "0px"}} labelCol={{style: {width: 175}}} label="Static Route">
                        <a
                            style={{
                                border: "none",
                                borderRadius: "4px",
                                color: "#14c9bb"
                            }}
                            onClick={() => {
                                staticRouteModalRef.current.showStaticRouteModal({mode: "create"});
                            }}
                        >
                            <Icon component={addGreenSvg} style={{marginRight: "8px"}} />
                            Static Route
                        </a>
                    </Form.Item>
                )}
            </Form>
            {isEnable && dataSource.length > 0 && (
                <div>
                    <AmpConCustomTable
                        dataSource={dataSource}
                        columns={columns}
                        style={tableStyle}
                        pagination={{
                            defaultPageSize: 10,
                            showSizeChanger: true,
                            hideOnSinglePage: dataSource.length <= 10
                        }}
                    />
                </div>
            )}
            <Divider style={{marginTop: "32px", marginBottom: "32px"}} />
        </>
    );
});
export default StaticRouteForm;
