import React, {forwardRef, useEffect, useImperativeHandle} from "react";
import {Form, Input, Button, Space, Divider, message} from "antd";
import Icon from "@ant-design/icons";
import {addGreenSvg, deleteSvg} from "@/utils/common/iconSvg";
import {formValidateRules} from "@/modules-ampcon/utils/util";

const DnsForm = forwardRef(({data, setSwitchTemplateData}, ref) => {
    const [form] = Form.useForm();
    const updateDnsData = () => {
        const dnsIps = form.getFieldValue("dnsServers") || [];
        setSwitchTemplateData(prev => ({
            ...prev,
            switch: {
                ...prev.switch,
                dns: {
                    ip: dnsIps
                }
            }
        }));
    };
    useEffect(() => {
        const ipList = Array.isArray(data?.ip) ? data.ip : [];
        form.setFieldsValue({dnsServers: ipList.length ? ipList : [""]});
    }, [JSON.stringify(data?.ip)]);

    useImperativeHandle(ref, () => ({}));

    return (
        <>
            <h2>DNS</h2>
            <Form form={form} layout="horizontal" labelAlign="left" style={{width: 505}} onValuesChange={updateDnsData}>
                <Form.List name="dnsServers">
                    {(fields, {add, remove}) => (
                        <>
                            {fields.map((field, index) => (
                                <Form.Item
                                    key={field.key}
                                    label={index === 0 ? "DNS Server IP" : ""}
                                    labelCol={{style: {width: 175}}}
                                    style={{marginBottom: 16, marginLeft: index === 0 ? 0 : 175}}
                                >
                                    <Space>
                                        <Form.Item
                                            {...field}
                                            validateTrigger={["onBlur", "onChange"]}
                                            rules={[
                                                formValidateRules.ipv4(),
                                                {
                                                    validator(_, value) {
                                                        if (!value) return Promise.resolve();
                                                        const currentList = form.getFieldValue("dnsServers") || [];
                                                        const count = currentList.filter(ip => ip === value).length;
                                                        if (count > 1) {
                                                            return Promise.reject(
                                                                new Error("DNS Server IP is duplicated")
                                                            );
                                                        }
                                                        return Promise.resolve();
                                                    }
                                                }
                                            ]}
                                            noStyle
                                        >
                                            <Input style={{width: 280}} />
                                        </Form.Item>

                                        {index === 0 ? (
                                            <Button
                                                type="text"
                                                icon={<Icon component={addGreenSvg} />}
                                                onClick={() => {
                                                    add();
                                                }}
                                                style={{color: "#14c9bb"}}
                                            />
                                        ) : (
                                            <Button
                                                type="text"
                                                danger
                                                icon={<Icon component={deleteSvg} />}
                                                onClick={() => remove(field.name)}
                                                style={{backgroundColor: "transparent", color: "#BFBFBF"}}
                                            />
                                        )}
                                    </Space>
                                </Form.Item>
                            ))}
                        </>
                    )}
                </Form.List>
            </Form>

            <Divider style={{marginTop: "32px", marginBottom: "32px"}} />
        </>
    );
});

export default DnsForm;
