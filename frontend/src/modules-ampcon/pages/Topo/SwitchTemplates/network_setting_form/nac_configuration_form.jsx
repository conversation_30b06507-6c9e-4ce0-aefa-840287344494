import {Space, Form, message, Divider, Radio} from "antd";
import {addGreenSvg} from "@/utils/common/iconSvg";
import {AmpConCustomTable} from "@/modules-ampcon/components/custom_table";
import NACServerModal from "@/modules-ampcon/pages/Topo/SwitchTemplates/modal/nac_server_modal";
import Icon from "@ant-design/icons";
import {useState, useRef, useEffect, forwardRef, useImperativeHandle} from "react";
import {confirmModalAction} from "@/modules-ampcon/components/custom_modal";

const NACConfigurationForm = forwardRef(({data, tableStyle}, ref) => {
    const [isEnable, setIsEnable] = useState(false);
    const [form] = Form.useForm();
    const nacServerModalRef = useRef(null);
    const [dataSource, setDataSource] = useState([]);
    useEffect(() => {
        if (data) {
            form.setFieldsValue({
                enable: data.configuration ? "enable" : "disable"
            });
            setIsEnable(data.configuration);
            setDataSource(data.server || []);
        }
    }, [data]);

    // useImperativeHandle(ref, () => ({
    //     validate: () => {
    //         if (isEnable && serverData.length === 0) {
    //             Promise.reject(new Error("Please add NAC Server"));
    //             message.error("Please add NAC Server");
    //             return false;
    //         }
    //         return true;
    //     }
    // }));

    const columns = [
        {
            title: "Server Name",
            dataIndex: "name",
            width: "25%",
            render: (_, record) => {
                return record.name;
            },
            sorter: (a, b) => a.name.localeCompare(b.name)
        },
        {
            title: "NAC Server IP",
            dataIndex: "ip",
            width: "25%",
            render: (_, record) => {
                return record.ip;
            },
            sorter: (a, b) => a.ip - b.ip
        },
        {
            title: "Port",
            dataIndex: "port",
            width: "25%",
            render: (_, record) => {
                return record.port;
            },
            sorter: (a, b) => a.port - b.port
        },
        {
            title: "Operation",
            render: (_, record) => {
                return (
                    <Space size="large" className="actionLink">
                        <a
                            onClick={() => {
                                nacServerModalRef.current.showNetworkModal({mode: "edit"}, record);
                            }}
                        >
                            Edit
                        </a>
                        <a
                            onClick={() => {
                                confirmModalAction("Are you sure want to delete?", () => {
                                    // try {
                                    //     setServerData(
                                    //         serverData.filter(item => item.server_name !== record.server_name)
                                    //     );
                                    //     message.success("Server deleted successfully");
                                    // } catch (error) {
                                    //     message.error("An error occurred while deleting the server");
                                    // }
                                });
                            }}
                        >
                            Delete
                        </a>
                    </Space>
                );
            }
        }
    ];

    return (
        <>
            <h2>NAC Configuration</h2>
            <NACServerModal
                ref={nacServerModalRef}
                // handleAddServer={data => {
                //     setServerData([...serverData, data]);
                // }}
                // handleEditServer={data => {
                //     setServerData(serverData.map(item => (item.server_name === data.server_name ? data : item)));
                // }}
                serverData={data}
            />
            <Form ref={ref} form={form} validateTrigger="onBlur" labelAlign="left" style={{width: 505}}>
                <Form.Item
                    name="enable"
                    label="Configuration"
                    labelCol={{style: {width: 175}}}
                    initialValue={isEnable ? "enable" : "disable"}
                >
                    <Radio.Group
                        onChange={e => {
                            // if (e.target.value === "disable") {
                            //     setServerData([]);
                            // }
                            setIsEnable(e.target.value === "enable");
                            // setSwitchTemplateData(prev => {
                            //     return {
                            //         ...prev,
                            //         networks: {
                            //             ...prev.networks,
                            //             enable_nac: e.target.value
                            //         }
                            //     };
                            // });
                        }}
                    >
                        <Radio value="enable">Enable</Radio>
                        <Radio value="disable">Disable</Radio>
                    </Radio.Group>
                </Form.Item>
                {isEnable && (
                    <Form.Item style={{marginBottom: "0px"}} label="Server" labelCol={{style: {width: 175}}}>
                        <a
                            style={{
                                border: "none",
                                borderRadius: "4px",
                                color: "#14c9bb"
                            }}
                            onClick={() => {
                                nacServerModalRef.current.showNetworkModal({mode: "create"});
                            }}
                        >
                            <Icon component={addGreenSvg} style={{marginRight: "8px"}} />
                            Add
                        </a>
                    </Form.Item>
                )}
            </Form>
            {isEnable && (
                <div>
                    <AmpConCustomTable
                        dataSource={dataSource}
                        columns={columns}
                        style={tableStyle}
                        // pagination={{
                        //     defaultPageSize: 10,
                        //     showSizeChanger: true,
                        //     hideOnSinglePage: serverData.length <= 10
                        // }}
                    />
                </div>
            )}

            <Divider style={{marginTop: "32px", marginBottom: "32px"}} />
        </>
    );
});
export default NACConfigurationForm;
