import {Form, Divider, Space, message} from "antd";
import {useState, useRef, useEffect, forwardRef, useImperativeHandle} from "react";
import Icon from "@ant-design/icons";
import {addGreenSvg} from "@/utils/common/iconSvg";
import {AmpConCustomTable} from "@/modules-ampcon/components/custom_table";
import {confirmModalAction} from "@/modules-ampcon/components/custom_modal";
import VrfModal from "@/modules-ampcon/pages/Topo/SwitchTemplates/modal/vrf_modal";

const VrfForm = forwardRef(({vrfData, networkData, tableStyle, setSwitchTemplateData}, ref) => {
    const [form] = Form.useForm();
    const [dataSource, setDataSource] = useState([]);
    const vrfModalRef = useRef();
    useEffect(() => {
        if (vrfData) {
            setDataSource(vrfData.networks || []);
        }
    }, [vrfData, form]);
    useImperativeHandle(ref, () => ({}));
    const columns = [
        {
            title: "VLAN Name",
            dataIndex: "name",
            width: "30%",
            render: (_, record) => {
                return record.name;
            },
            sorter: (a, b) => a.name.localeCompare(b.name)
        },
        {
            title: "Networks",
            dataIndex: "id",
            width: "30%",
            render: (_, record) => {
                const ids = Array.isArray(record.id) ? record.id : [record.id];

                return <div style={{whiteSpace: "pre-line"}}>{ids.join("\n")}</div>;
            },
            sorter: (a, b) => {
                const aIds = Array.isArray(a.id) ? a.id.join(",") : a.id;
                const bIds = Array.isArray(b.id) ? b.id.join(",") : b.id;
                return aIds.localeCompare(bIds);
            }
        },
        {
            title: "Operation",
            render: (_, record) => {
                const isDefaultVlan = record.name.toLowerCase() === "default vlan";

                return (
                    <Space size="large" className="actionLink">
                        {isDefaultVlan ? (
                            <a
                                onClick={() => {
                                    vrfModalRef.current.showVrfModal({mode: "view"}, record);
                                }}
                            >
                                View
                            </a>
                        ) : (
                            <>
                                <a
                                    onClick={() => {
                                        vrfModalRef.current.showVrfModal({mode: "edit"}, record);
                                    }}
                                >
                                    Edit
                                </a>
                                <a
                                    onClick={() => {
                                        confirmModalAction("Are you sure want to delete?", () => {
                                            try {
                                                handleDeleteServer(record);
                                                message.success("Delete Successful");
                                            } catch (error) {
                                                message.error("Delete Failed");
                                            }
                                        });
                                    }}
                                >
                                    Delete
                                </a>
                            </>
                        )}
                    </Space>
                );
            }
        }
    ];
    const updateVrf = newData => {
        setSwitchTemplateData(prev => ({
            ...prev,
            switch: {
                ...prev.switch,
                vrf: {
                    networks: newData
                }
            }
        }));
    };
    const handleAddServer = newData => {
        const updatedData = [...dataSource, newData];
        setDataSource(updatedData);
        updateVrf(updatedData);
    };
    const handleEditServer = (newData, currentRecord) => {
        const updatedData = dataSource.map(item => (item.name === currentRecord.name ? newData : item));
        setDataSource(updatedData);
        updateVrf(updatedData);
    };

    const handleDeleteServer = record => {
        const updatedData = dataSource.filter(item => item.name !== record.name);
        setDataSource(updatedData);
        updateVrf(updatedData);
    };
    return (
        <>
            <h2>VRF</h2>
            <VrfModal
                ref={vrfModalRef}
                networkData={networkData}
                vrfData={vrfData}
                handleAddServer={handleAddServer}
                handleEditServer={handleEditServer}
            />
            <Form ref={ref} form={form} validateTrigger="onBlur" labelAlign="left" style={{width: 505}}>
                <Form.Item style={{marginBottom: "0px"}} labelCol={{style: {width: 175}}}>
                    <a
                        style={{
                            border: "none",
                            borderRadius: "4px",
                            color: "#14c9bb"
                        }}
                        onClick={() => {
                            vrfModalRef.current.showVrfModal({mode: "create"});
                        }}
                    >
                        <Icon component={addGreenSvg} style={{marginRight: "8px"}} />
                        VRF
                    </a>
                </Form.Item>
            </Form>
            {dataSource.length > 0 && (
                <div>
                    <AmpConCustomTable
                        dataSource={dataSource}
                        columns={columns}
                        style={tableStyle}
                        pagination={{
                            defaultPageSize: 10,
                            showSizeChanger: true,
                            hideOnSinglePage: dataSource.length <= 10
                        }}
                    />
                </div>
            )}
            <Divider style={{marginTop: "32px", marginBottom: "32px"}} />
        </>
    );
});
export default VrfForm;
