import {useState, useRef, useEffect} from "react";
import {But<PERSON>, Card, Form, Input, Flex, Divider, Collapse, message, Spin} from "antd";
import {ArrowLeftOutlined} from "@ant-design/icons";
import styles from "@/modules-ampcon/pages/Topo/SwitchTemplates/switch_templates.module.scss";
import NtpForm from "@/modules-ampcon/pages/Topo/SwitchTemplates/network_setting_form/ntp_form";
import DnsForm from "@/modules-ampcon/pages/Topo/SwitchTemplates/network_setting_form/dns_form";
import StaticRouteForm from "@/modules-ampcon/pages/Topo/SwitchTemplates/network_setting_form/static_route_form";
import OspfForm from "@/modules-ampcon/pages/Topo/SwitchTemplates/network_setting_form/ospf_form";
import NetworkForm from "@/modules-ampcon/pages/Topo/SwitchTemplates/network_setting_form/network_form";
import VrfForm from "@/modules-ampcon/pages/Topo/SwitchTemplates/network_setting_form/vrf_form";
import PortConfigForm from "@/modules-ampcon/pages/Topo/SwitchTemplates/network_setting_form/port_config_form";
import PortConfigAppForm from "@/modules-ampcon/pages/Topo/SwitchTemplates/network_setting_form/port_config_app_form";
import LocalUserForm from "@/modules-ampcon/pages/Topo/SwitchTemplates/network_setting_form/local_user_form";
import AssignTemplateModal from "@/modules-ampcon/pages/Topo/SwitchTemplates/modal/assign_template_modal";
import ReactDOM from "react-dom";
import {configDeploy, getAddedSwitch} from "@/modules-ampcon/apis/campus_blueprint_api";

const SwitchTemplatesCreate = ({backToTableViewCallback, templateName, allTableNames, onSaveSuccess}) => {
    const ntpFormRef = useRef(null);
    const dnsFormRef = useRef(null);
    const staticRouteFormRef = useRef(null);
    const ospfFormRef = useRef(null);
    const networkFormRef = useRef(null);
    const vrfFormRef = useRef(null);
    const portConfigFormRef = useRef(null);
    const portConfigAppFormRef = useRef(null);
    const localUserFormRef = useRef(null);
    const assignTemplateModalRef = useRef(null);
    const [maxContainerHeight, setMaxContainerHeight] = useState(null);
    const [isFocused, setIsFocused] = useState(false);
    const inputRef = useRef(null);
    const tableStyle = {
        maxWidth: "45%",
        minWidth: "650px"
    };
    const {TextArea} = Input;
    const [form] = Form.useForm();
    const [nameForm] = Form.useForm();
    const [isLoading, setIsLoading] = useState(false);
    const [switchTemplateData, setSwitchTemplateData] = useState({
        name: "",
        scope: {
            switches: 0,
            switchList: []
        },
        switch: {
            network: {
                networks: []
            },
            ntp: {
                ip: []
            },
            dns: {
                ip: []
            },
            staticRoute: {
                configuration: false,
                routes: []
            },
            ospf: {
                configuration: false,
                areas: []
            },
            vrf: {
                networks: []
            },
            portConfiguration: [
                {
                    name: "Default",
                    portEnable: "enable",
                    description: "",
                    portMode: "access",
                    portNetwork: "Default(1)",
                    speed: "auto",
                    poe: "disable",
                    stormControl: "disable"
                }
            ],
            portConfigApplication: []
        },
        management: {
            localUser: {
                users: []
            },
            loginBanner: {},
            timeout: ""
        }
    });
    const handleDeleteNetwork = deleteNetwork => {
        setSwitchTemplateData(prev => {
            const networkIdentifier = `${deleteNetwork.name}(${deleteNetwork.id})`;

            const updatedNetworks = prev.switch.network.networks.filter(network => network.name !== deleteNetwork.name);
            const updatedVrf = {
                networks: (prev.switch.vrf.networks || []).filter(vrf => !vrf.id?.some(id => id === networkIdentifier))
            };

            const deletedPortProfiles = (prev.switch.portConfiguration || [])
                .filter(port => {
                    if (port.portNetwork === networkIdentifier) return true;
                    if (port.trunkNetwork) {
                        return Array.isArray(port.trunkNetwork)
                            ? port.trunkNetwork.includes(networkIdentifier)
                            : port.trunkNetwork === networkIdentifier;
                    }
                    return false;
                })
                .map(port => port.name);

            const updatedPortConfig = (prev.switch.portConfiguration || []).filter(
                port => !deletedPortProfiles.includes(port.name)
            );
            const updatedPortApp = (prev.switch.portConfigApplication || []).filter(
                app => !deletedPortProfiles.includes(app.profile)
            );
            return {
                ...prev,
                switch: {
                    ...prev.switch,
                    network: {networks: updatedNetworks},
                    vrf: updatedVrf,
                    portConfiguration: updatedPortConfig,
                    portConfigApplication: updatedPortApp
                }
            };
        });
    };
    const items = [
        {
            key: "1",
            label: <h2>Switch</h2>,
            children: (
                <>
                    <NetworkForm
                        ref={networkFormRef}
                        tableStyle={tableStyle}
                        setSwitchTemplateData={setSwitchTemplateData}
                        data={switchTemplateData.switch.network.networks}
                        onDeleteNetwork={handleDeleteNetwork}
                    />
                    <VrfForm
                        ref={vrfFormRef}
                        vrfData={switchTemplateData.switch.vrf}
                        tableStyle={tableStyle}
                        networkData={switchTemplateData.switch.network.networks}
                        setSwitchTemplateData={setSwitchTemplateData}
                    />
                    <NtpForm
                        ref={ntpFormRef}
                        data={switchTemplateData.switch.ntp}
                        setSwitchTemplateData={setSwitchTemplateData}
                    />
                    <DnsForm
                        ref={dnsFormRef}
                        data={switchTemplateData.switch.dns}
                        setSwitchTemplateData={setSwitchTemplateData}
                    />
                    <StaticRouteForm
                        ref={staticRouteFormRef}
                        staticRouteData={switchTemplateData.switch.staticRoute}
                        tableStyle={tableStyle}
                        setSwitchTemplateData={setSwitchTemplateData}
                    />
                    <OspfForm
                        ref={ospfFormRef}
                        ospData={switchTemplateData.switch.ospf}
                        tableStyle={tableStyle}
                        setSwitchTemplateData={setSwitchTemplateData}
                    />
                    <PortConfigForm
                        ref={portConfigFormRef}
                        portConfigData={switchTemplateData.switch.portConfiguration}
                        tableStyle={tableStyle}
                        setSwitchTemplateData={setSwitchTemplateData}
                        networkData={switchTemplateData.switch.network.networks}
                    />
                    <PortConfigAppForm
                        ref={portConfigAppFormRef}
                        portConfigAppData={switchTemplateData.switch.portConfigApplication}
                        tableStyle={tableStyle}
                        setSwitchTemplateData={setSwitchTemplateData}
                        portConfigData={switchTemplateData.switch.portConfiguration}
                    />
                </>
            )
        },
        {
            key: "2",
            label: <h2>Management</h2>,
            children: (
                <>
                    <LocalUserForm
                        ref={localUserFormRef}
                        userData={switchTemplateData.management.localUser}
                        tableStyle={tableStyle}
                        setSwitchTemplateData={setSwitchTemplateData}
                    />
                    <h2>Login Banner</h2>
                    <Form
                        layout="horizontal"
                        labelAlign="left"
                        labelCol={{span: 3}}
                        wrapperCol={{span: 10}}
                        labelWrap
                        className="label-wrap"
                        form={form}
                        style={{minHeight: "267.23px"}}
                        ref={form}
                        validateTrigger={["onBlur", "onSubmit"]}
                    >
                        <Form.Item
                            name="before"
                            validateFirst
                            label="Banner before Login"
                            rules={[
                                {
                                    validator: (_, value) => {
                                        if (!value) return Promise.resolve();
                                        const lineCount = value.split(/\r?\n/).length;
                                        if (lineCount > 20) {
                                            return Promise.reject(new Error("Maximum 20 lines allowed"));
                                        }
                                        return Promise.resolve();
                                    }
                                }
                            ]}
                        >
                            <TextArea style={{width: "280px", height: "102px"}} />
                        </Form.Item>
                        <Form.Item
                            name="after"
                            validateFirst
                            label="Banner after Login"
                            rules={[
                                {
                                    validator: (_, value) => {
                                        if (!value) return Promise.resolve();
                                        const lineCount = value.split(/\r?\n/).length;
                                        if (lineCount > 20) {
                                            return Promise.reject(new Error("Maximum 20 lines allowed"));
                                        }
                                        return Promise.resolve();
                                    }
                                }
                            ]}
                        >
                            <TextArea style={{width: "280px", height: "102px"}} />
                        </Form.Item>
                        <Divider />
                        <h2>
                            Idle Timeout <span style={{fontSize: "14px", fontWeight: "300"}}>(in Minutes)</span>
                        </h2>
                        <Form.Item
                            name="timeout"
                            validateFirst
                            label="Idle Timeout"
                            rules={[
                                {
                                    validator: (_, value) => {
                                        if (value === undefined || value === "") {
                                            return Promise.resolve();
                                        }
                                        const valueStr = String(value).trim();
                                        if (!/^(0|[1-9]\d{0,4})$/.test(valueStr)) {
                                            return Promise.reject(
                                                new Error("Ranges from 0 to 20000, and defaults to 0.")
                                            );
                                        }
                                        const numberValue = Number(valueStr);
                                        if (numberValue < 0 || numberValue > 20000) {
                                            return Promise.reject(
                                                new Error("Ranges from 0 to 20000, and defaults to 0.")
                                            );
                                        }
                                        return Promise.resolve();
                                    }
                                }
                            ]}
                        >
                            <Input
                                style={{width: "280px"}}
                                ref={inputRef}
                                onFocus={() => setIsFocused(true)}
                                onBlur={() => setIsFocused(false)}
                            />
                        </Form.Item>
                        <CustomTooltip visible={isFocused} targetRef={inputRef} />
                    </Form>
                </>
            )
        }
    ];
    useEffect(() => {
        if (templateName) {
            nameForm.setFieldsValue({template_name: templateName});
        }
        const handleResize = () => {
            setMaxContainerHeight("calc(100vh - 200px)");
        };
        handleResize();
        window.addEventListener("resize", handleResize);
        return () => {
            window.removeEventListener("resize", handleResize);
        };
    }, [templateName]);

    const handleApplySwitchList = updatedSwitchList => {
        setSwitchTemplateData(prev => ({
            ...prev,
            scope: {
                ...prev.scope,
                switchList: updatedSwitchList,
                switches: updatedSwitchList.length
            }
        }));
    };
    const handleSave = async () => {
        try {
            const [nameValues, bannerValues] = await Promise.all([nameForm.validateFields(), form.validateFields()]);
            setIsLoading(true);
            const cleanedNtpIps = (switchTemplateData.switch.ntp?.ip || []).filter(
                ip => typeof ip === "string" && ip.trim() !== ""
            );
            const cleanedDnsIps = (switchTemplateData.switch.dns?.ip || []).filter(
                ip => typeof ip === "string" && ip.trim() !== ""
            );
            const updatedOspfAreas = switchTemplateData.switch.ospf.areas.map(area => ({
                ...area,
                type: area.type.toLowerCase()
            }));
            const updatedData = {
                ...switchTemplateData,
                name: nameValues.template_name,
                scope: {
                    ...switchTemplateData.scope,
                    switchList: switchTemplateData.scope.switchList
                },
                switch: {
                    ...switchTemplateData.switch,
                    ntp: {
                        ...switchTemplateData.switch.ntp,
                        ip: cleanedNtpIps
                    },
                    dns: {
                        ...switchTemplateData.switch.dns,
                        ip: cleanedDnsIps
                    },
                    ospf: {
                        ...switchTemplateData.switch.ospf,
                        areas: updatedOspfAreas
                    }
                },
                management: {
                    ...switchTemplateData.management,
                    loginBanner: {
                        before: bannerValues.before,
                        after: bannerValues.after
                    },
                    timeout: bannerValues.timeout
                }
            };
            const res = await configDeploy(updatedData);
            if (res.status === 200) {
                setIsLoading(false);
                message.success("Template saved successfully");
                setSwitchTemplateData(updatedData);
                onSaveSuccess?.();
            } else {
                setIsLoading(false);
                message.error(res.info);
            }
        } catch (error) {
            setIsLoading(false);
            console.error("Error saving template:", error);
        }
    };
    return (
        <>
            <Spin spinning={isLoading} tip="Loading..." fullscreen />
            <Card style={{display: "flex", flexDirection: "column", flex: 1}}>
                <Flex
                    vertical
                    flex={1}
                    style={{height: "100%", maxHeight: maxContainerHeight, overflowY: "scroll", paddingRight: "5px"}}
                >
                    <p className={styles.goBack} onClick={backToTableViewCallback} style={{marginBottom: "23px"}}>
                        <ArrowLeftOutlined style={{marginRight: "8px"}} />
                        <span>Back</span>
                    </p>
                    <h2 style={{margin: "8px 0 20px"}}>Template Application</h2>
                    <Form labelAlign="left" labelCol={{flex: "120px"}} wrapperCol={{flex: "280px"}} form={nameForm}>
                        <Form.Item
                            name="template_name"
                            label="Template Name"
                            rules={[
                                {required: true, message: "Template Name is required."},
                                {
                                    validator: (_, value) => {
                                        if (!value) return Promise.resolve();
                                        const duplicate = allTableNames.includes(value.trim());
                                        if (duplicate) {
                                            return Promise.reject(new Error("Template name already exists."));
                                        }
                                        return Promise.resolve();
                                    }
                                },
                                {max: 32, message: "Template name cannot exceed 32 characters!"}
                            ]}
                        >
                            <Input />
                        </Form.Item>
                    </Form>
                    <Divider />
                    <h2 style={{margin: "8px 0 20px"}}>Template Application Scope</h2>
                    <Form labelAlign="left">
                        <Flex direction="column" gap="8px">
                            <Input
                                style={{width: 206, background: "#F8FAFB"}}
                                disabled
                                suffix="Switches"
                                defaultValue={0}
                                value={switchTemplateData.scope.switches}
                            />
                        </Flex>
                    </Form>
                    <Button
                        type="primary"
                        onClick={async () => {
                            const snList = switchTemplateData.scope.switchList;
                            try {
                                const res = await getAddedSwitch({switchList: snList});
                                if (res?.status === 200 && Array.isArray(res.data)) {
                                    assignTemplateModalRef.current.showAssignTemplateModal(res.data, templateName);
                                } else {
                                    message.error("Failed to fetch switches");
                                }
                            } catch (error) {
                                console.error("Error fetching switches:", error);
                                message.error("Error occurred while fetching switches");
                            }
                        }}
                        style={{marginTop: "24px", width: "133px"}}
                    >
                        Assign Template
                    </Button>
                    <Divider />
                    <Collapse
                        items={items}
                        defaultActiveKey={["1", "2"]}
                        expandIconPosition="end"
                        style={{background: "#F8FAFB"}}
                        destroyInactivePanel={false}
                    />
                    <Flex
                        style={{
                            marginTop: 24,
                            position: "absolute",
                            bottom: "16px",
                            right: "10px",
                            flexDirection: "row-reverse",
                            width: "100%",
                            paddingRight: "24px",
                            paddingTop: "16px",
                            gap: "16px"
                        }}
                    >
                        <Button key="ok" type="primary" onClick={handleSave}>
                            Apply
                        </Button>
                        <Button key="cancel" onClick={backToTableViewCallback}>
                            Cancel
                        </Button>
                    </Flex>
                </Flex>
                <AssignTemplateModal
                    ref={assignTemplateModalRef}
                    onApply={handleApplySwitchList}
                    data={switchTemplateData.scope}
                />
            </Card>
        </>
    );
};

export default SwitchTemplatesCreate;
const CustomTooltip = ({visible, targetRef}) => {
    const [position, setPosition] = useState({top: 0, left: 0});

    const updatePosition = () => {
        if (targetRef?.current?.input) {
            const rect = targetRef.current.input.getBoundingClientRect();
            setPosition({
                top: rect.top + window.scrollY - 15,
                left: rect.right + 15
            });
        }
    };

    useEffect(() => {
        if (!visible) return;
        updatePosition();
        window.addEventListener("resize", updatePosition);
        window.addEventListener("scroll", updatePosition);
        return () => {
            window.removeEventListener("resize", updatePosition);
            window.removeEventListener("scroll", updatePosition);
        };
    }, [visible]);

    if (!visible) return null;
    const style = {
        position: "absolute",
        top: position.top,
        left: position.left,
        zIndex: 9999,
        width: "184px",
        background: "#646569",
        color: "#fff",
        padding: "8px 12px",
        borderRadius: 6,
        fontSize: 12,
        boxShadow: "0 3px 6px rgba(0,0,0,0.2)",
        lineHeight: 1.6
    };
    return ReactDOM.createPortal(<div style={style}>Ranges from 0 to 20000, and defaults to 0.</div>, document.body);
};
