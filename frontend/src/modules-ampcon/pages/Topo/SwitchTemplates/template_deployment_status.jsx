import {useState} from "react";
import {<PERSON>, <PERSON>, Tag, Mo<PERSON>, Button, Divider, Input, Flex, message} from "antd";
import {ArrowLeftOutlined, ReloadOutlined} from "@ant-design/icons";
import styles from "@/modules-ampcon/pages/Topo/SwitchTemplates/switch_templates.module.scss";
import {AmpConCustomTable, createColumnConfig, TableFilterDropdown} from "@/modules-ampcon/components/custom_table";
import {getStatusList, getStatusLog} from "@/modules-ampcon/apis/campus_blueprint_api";

const TemplateDeploymentStatus = ({backToTableViewCallback, currentTemplateId}) => {
    const [viewLogModal, setViewLogModal] = useState(false);
    const [logInfo, setLogInfo] = useState(null);
    const [logModel, setLogModel] = useState("");
    const searchFieldsList = ["model", "sn", "mgt_ip", "status"];
    const matchFieldsList = [
        {name: "model", matchMode: "fuzzy"},
        {name: "sn", matchMode: "fuzzy"},
        {name: "mgt_ip", matchMode: "fuzzy"},
        {name: "status", matchMode: "fuzzy"}
    ];
    const [currentLogId, setCurrentLogId] = useState(null);
    const fetchStatusLog = record => {
        setCurrentLogId(record.log_id);
        getStatusLog(record.log_id).then(res => {
            if (res.status === 200) {
                setLogInfo(res.log_info);
                setViewLogModal(true);
                setLogModel(record.model);
            } else {
                message.error(res.info);
            }
        });
    };
    const reloadStatusLog = () => {
        if (!currentLogId) return;
        getStatusLog(currentLogId).then(res => {
            if (res.status === 200) {
                message.success("Log reloaded successfully");
                setLogInfo(res.log_info);
            } else {
                message.error(res.info);
            }
        });
    };
    const columns = [
        createColumnConfig("Model", "model", TableFilterDropdown),
        createColumnConfig("SN", "sn", TableFilterDropdown),
        createColumnConfig("Mgmt IP", "mgt_ip", TableFilterDropdown),
        createColumnConfig("Site", "site", TableFilterDropdown),
        {
            ...createColumnConfig("Deployment Status", "status"),
            render: (_, record) => {
                return (
                    <div>
                        <Space size="small">
                            {record.status === 2 && <Tag className={styles.successTag}>Success</Tag>}
                            {record.status === 3 && <Tag className={styles.failedTag}>Failed</Tag>}
                            {record.status === 1 && <Tag className={styles.runningTag}>Running</Tag>}
                        </Space>
                    </div>
                );
            }
        },
        {
            title: "Operation",
            render: (_, record) => (
                <div>
                    <Space size="middle" className={styles.actionLink}>
                        <a onClick={() => fetchStatusLog(record)}>Log</a>
                    </Space>
                </div>
            )
        }
    ];
    return (
        <>
            <Card style={{display: "flex", flexDirection: "column", flex: 1}}>
                <p className={styles.goBack} onClick={backToTableViewCallback} style={{marginBottom: "23px"}}>
                    <ArrowLeftOutlined style={{marginRight: "8px"}} />
                    <span>Back</span>
                </p>
                <AmpConCustomTable
                    columns={columns}
                    fetchAPIInfo={getStatusList}
                    fetchAPIParams={[currentTemplateId]}
                    searchFieldsList={searchFieldsList}
                    matchFieldsList={matchFieldsList}
                />
            </Card>
            <LogViewModal
                title="Logs"
                logInfo={logInfo}
                logModel={logModel}
                viewLogModal={viewLogModal}
                onCancel={() => setViewLogModal(false)}
                modalClass="ampcon-max-modal"
                onReloadLog={reloadStatusLog}
            />
        </>
    );
};

export default TemplateDeploymentStatus;
const LogViewModal = ({logInfo, logModel, viewLogModal, onCancel, onReloadLog}) => {
    const readonlyStyle = {
        minHeight: "330px",
        height: "58vh",
        resize: "vertical",
        border: "1px solid rgb(217, 217, 217)",
        fontSize: "16px",
        borderRadius: "4px",
        boxShadow: "none",
        maxHeight: "calc(100vh - 500px)"
    };

    return (
        <Modal
            className="ampcon-middle-modal"
            title={
                <>
                    <div style={{display: "flex", justifyContent: "space-between", alignItems: "center"}}>
                        {`${logModel} logs`}
                        <Button
                            type="text"
                            className="ant-modal-close"
                            style={{marginRight: "30px"}}
                            icon={<ReloadOutlined className="anticon anticon-close ant-modal-close-icon" />}
                            onClick={onReloadLog}
                        />
                    </div>
                    <Divider style={{marginTop: 8, marginBottom: 0}} />
                </>
            }
            open={viewLogModal}
            onCancel={onCancel}
            footer={null}
        >
            <Flex vertical style={{flex: 1}}>
                <Input.TextArea style={readonlyStyle} value={logInfo} rows={19} readOnly />
            </Flex>
        </Modal>
    );
};
