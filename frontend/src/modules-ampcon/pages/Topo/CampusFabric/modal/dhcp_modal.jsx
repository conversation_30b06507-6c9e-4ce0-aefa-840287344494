import {Button, Form, Input, message, Divider, Select, Modal} from "antd";
import {useState, forwardRef, useImperativeHandle} from "react";
import {formValidateRules} from "@/modules-ampcon/utils/util";

export const DHCPModal = forwardRef(({type, dhcpData, setDhcpData, networkData, CreateLabel, EditLabel}, ref) => {
    const [isShowModal, setIsShowModal] = useState(false);
    const [preValue, setPreValue] = useState({});

    const IPv4_REGEXP =
        /^(?:0|(?:[1-9]\d{0,1})|1\d{2}|2[0-4]\d|25[0-5])(?:\.(?:0|(?:[1-9]\d{0,1})|1\d{2}|2[0-4]\d|25[0-5])){3}$/;
    const [form] = Form.useForm();
    const [mode, setMode] = useState("");

    useImperativeHandle(ref, () => ({
        showNetworkModal: ({mode}, record) => {
            setPreValue(record);
            setMode(mode);
            if (mode === "edit") {
                form.setFieldsValue({
                    dhcp_network: record.dhcp_network,
                    vlan_id: networkData.find(item => item.name === record.dhcp_network)?.vlan_id,
                    dhcp_server: record.dhcp_server
                });
            }
            setIsShowModal(true);
        }
    }));

    return isShowModal ? (
        <Modal
            className="ampcon-middle-modal"
            title={
                <div>
                    {mode === "create" ? CreateLabel : EditLabel}
                    <Divider style={{marginTop: 8, marginBottom: 0}} />
                </div>
            }
            open={isShowModal}
            onOk={() => {}}
            onCancel={() => {
                setIsShowModal(false);
                form.resetFields();
            }}
            footer={
                <div>
                    <Divider style={{marginTop: 0, marginBottom: 20}} />
                    <Button
                        onClick={() => {
                            setIsShowModal(false);
                            form.resetFields();
                        }}
                    >
                        Cancel
                    </Button>
                    <Button
                        type="primary"
                        onClick={() => {
                            form.submit();
                        }}
                    >
                        Apply
                    </Button>
                </div>
            }
        >
            <Form
                layout="horizontal"
                labelAlign="left"
                labelCol={{span: 5}}
                wrapperCol={{span: 17}}
                labelWrap
                className="label-wrap"
                form={form}
                style={{minHeight: "267.23px"}}
                ref={form}
                validateTrigger={["onBlur", "onSubmit"]}
                onFinish={() => {
                    try {
                        if (mode === "create") {
                            setDhcpData([
                                ...dhcpData,
                                {
                                    dhcp_network: form.getFieldValue("dhcp_network"),
                                    dhcp_server: form.getFieldValue("dhcp_server"),
                                    vlan_id: form.getFieldValue("vlan_id")
                                }
                            ]);

                            message.success("DHCP created successfully");
                        }
                        if (mode === "edit") {
                            const preItem = dhcpData.find(item => item === preValue);
                            const newItem = {
                                dhcp_network: form.getFieldValue("dhcp_network"),
                                dhcp_server: form.getFieldValue("dhcp_server"),
                                vlan_id: form.getFieldValue("vlan_id")
                            };
                            setDhcpData(dhcpData.map(item => (item === preItem ? newItem : item)));
                            message.success("DHCP edited successfully");
                        }
                    } catch (error) {
                        message.error("An error occurred while processing the DHCP");
                        console.error(error);
                    } finally {
                        setIsShowModal(false);
                        form.resetFields();
                    }
                }}
            >
                <Form.Item
                    name="dhcp_network"
                    label="Network"
                    rules={[{required: true, message: "Please select network!"}]}
                    initialValue=""
                >
                    <Select
                        onChange={value => {
                            form.setFieldsValue({vlan_id: networkData.find(item => item.name === value)?.vlan_id});
                        }}
                        style={{width: "280px"}}
                    >
                        {networkData
                            .filter(item => {
                                if (mode === "edit" && item.name === preValue.dhcp_network) {
                                    return true; // Include the current network in edit mode
                                }
                                return !dhcpData.some(dhcp => dhcp.dhcp_network === item.name);
                            })
                            .map(item => (
                                <Select.Option key={item.name} value={item.name}>
                                    {item.name}
                                </Select.Option>
                            ))}
                    </Select>
                </Form.Item>

                <Form.Item
                    name="vlan_id"
                    label="VLAN ID"
                    rules={[{required: true, message: "Please input VLAN ID"}]}
                    initialValue=""
                >
                    <Input style={{width: "280px"}} disabled />
                </Form.Item>

                {type === "relay" && (
                    <Form.Item
                        name="dhcp_server"
                        rules={[
                            {required: true, message: "Please input DHCP server"},
                            {
                                pattern: IPv4_REGEXP,
                                message: "Please input a valid IPv4 address"
                            }
                        ]}
                        label="DHCP Server"
                        initialValue=""
                        tooltip="If a PicOS switch is used as the DHCP server, you need to enter the Layer 3 IPv4 address of the DHCP server."
                    >
                        <Input style={{width: "280px"}} />
                    </Form.Item>
                )}
            </Form>
        </Modal>
    ) : null;
});
