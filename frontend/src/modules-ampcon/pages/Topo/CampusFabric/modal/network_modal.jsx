import {Button, Divider, Form, Input, message, Modal} from "antd";
import {formValidateRules} from "@/modules-ampcon/utils/util";
import {forwardRef, useImperativeHandle, useState} from "react";
import {EmptyStringValidator} from "../network_setting_form/form_validator";
import ip from "ip";

const ipv4NetRegex = /^((25[0-5]|2[0-4]\d|1\d{2}|[1-9]?\d)\.){3}0\/(\d|[12]\d|3[0-2])$/;

export const NetworkModal = forwardRef(
    ({handleAddNetwork, handleEditNetwork, networkData, requiredIPCounts, usedSubnets}, ref) => {
        const [isShowModal, setIsShowModal] = useState(false);

        const [networkForm] = Form.useForm();
        const [mode, setMode] = useState("");

        useImperativeHandle(ref, () => ({
            showNetworkModal: ({mode}, record) => {
                setMode(mode);
                if (mode === "edit") {
                    networkForm.setFieldsValue(record);
                }
                setIsShowModal(true);
            }
        }));

        return isShowModal ? (
            <Modal
                className="ampcon-middle-modal"
                title={
                    <div>
                        {mode === "create" ? "Create Network" : "Edit Network"}
                        <Divider style={{marginTop: 8, marginBottom: 0}} />
                    </div>
                }
                open={isShowModal}
                onOk={() => {}}
                onCancel={() => {
                    setIsShowModal(false);
                    networkForm.resetFields();
                }}
                footer={
                    <div>
                        <Divider style={{marginTop: 0, marginBottom: 20}} />
                        <Button
                            onClick={() => {
                                setIsShowModal(false);
                                networkForm.resetFields();
                            }}
                        >
                            Cancel
                        </Button>
                        <Button
                            type="primary"
                            onClick={() => {
                                networkForm.submit();
                            }}
                        >
                            Apply
                        </Button>
                    </div>
                }
            >
                <Form
                    layout="horizontal"
                    labelAlign="left"
                    labelCol={{span: 5}}
                    wrapperCol={{span: 17}}
                    labelWrap
                    className="label-wrap"
                    form={networkForm}
                    style={{minHeight: "267.23px"}}
                    ref={networkForm}
                    validateTrigger={["onBlur", "onSubmit"]}
                    onFinish={() => {
                        try {
                            if (mode === "create") {
                                handleAddNetwork(networkForm.getFieldsValue());
                                message.success("Network created successfully");
                            }
                            if (mode === "edit") {
                                handleEditNetwork(networkForm.getFieldsValue());
                                message.success("Network edited successfully");
                            }
                        } catch (error) {
                            message.error("An error occurred while processing the network");
                            console.error(error);
                        } finally {
                            setIsShowModal(false);
                            networkForm.resetFields();
                        }
                    }}
                >
                    <Form.Item
                        name="name"
                        label="Name"
                        validateFirst
                        rules={[
                            {required: true, message: "Please input network name!"},
                            {max: 32, message: "Network name cannot exceed 32 characters!"},
                            {
                                validator: (_, value) => {
                                    if (mode === "create" && networkData.some(item => item.name === value)) {
                                        return Promise.reject(new Error("Name already exists"));
                                    }
                                    return Promise.resolve();
                                }
                            },
                            EmptyStringValidator("Name cannot contain spaces")
                        ]}
                        initialValue=""
                    >
                        <Input style={{width: "280px"}} disabled={mode === "edit"} />
                    </Form.Item>
                    <Form.Item
                        name="vlan_id"
                        validateFirst
                        rules={[
                            {required: true, message: "Please input VLAN ID"},
                            formValidateRules.int(),
                            {
                                validator: async (_, value) => {
                                    if (mode === "create" && networkData.some(item => item.vlan_id === value)) {
                                        return Promise.reject(new Error("VLAN ID already exists"));
                                    }
                                    if (
                                        mode === "edit" &&
                                        networkData.some(
                                            item =>
                                                item.vlan_id === value &&
                                                item.name !== networkForm.getFieldValue("name")
                                        )
                                    ) {
                                        return Promise.reject(new Error("VLAN ID already exists"));
                                    }
                                }
                            },
                            {
                                validator: async (_, value) => {
                                    if (value && typeof value === "string" && value.slice(0, 1) === "0") {
                                        return Promise.reject(new Error("Invalid VLAN ID"));
                                    }
                                    if (value < 2 || value > 3837) {
                                        return Promise.reject(new Error("VLAN ID must be between 2 and 3837"));
                                    }
                                }
                            }
                        ]}
                        label="VLAN ID"
                        initialValue=""
                    >
                        <Input style={{width: "280px"}} placeholder="Range(2-3837)" />
                    </Form.Item>
                    <Form.Item
                        name="subnet"
                        validateFirst
                        rules={[
                            {
                                pattern: ipv4NetRegex,
                                message: "The subnet must be XXX.XXX.XXX.0/XX"
                            },
                            {required: true, message: "Please input subnet"},
                            {
                                validator: async (_, value) => {
                                    if (mode === "create" && networkData.some(item => item.subnet === value)) {
                                        return Promise.reject(new Error("Subnet already exists"));
                                    }
                                    if (
                                        mode === "edit" &&
                                        networkData.some(
                                            item =>
                                                item.subnet === value && item.name !== networkForm.getFieldValue("name")
                                        )
                                    ) {
                                        return Promise.reject(new Error("Subnet already exists"));
                                    }
                                    if (usedSubnets.includes(value)) {
                                        return Promise.reject(new Error("Subnet already in use"));
                                    }
                                }
                            },
                            {
                                validator: async (_, value) => {
                                    const subnetInfo = ip.cidrSubnet(value);
                                    const availableIPs = subnetInfo.numHosts;
                                    if (requiredIPCounts && availableIPs < requiredIPCounts) {
                                        return Promise.reject(new Error("Insufficient IPs in the subnet"));
                                    }
                                    return Promise.resolve();
                                }
                            }
                        ]}
                        label="Subnet"
                        initialValue=""
                    >
                        <Input style={{width: "280px"}} placeholder="XXX.XXX.XXX.0/XX" />
                    </Form.Item>
                </Form>
            </Modal>
        ) : null;
    }
);
