import {Button, Form, Input, message, Divider, Checkbox, Modal, Row, Col} from "antd";
import {addGreenSvg} from "@/utils/common/iconSvg";
import {deleteSvg} from "../resource/icon";
import {formValidateRules} from "@/modules-ampcon/utils/util";
import {EmptyStringValidator} from "../network_setting_form/form_validator";
import Icon from "@ant-design/icons";
import {useDynamicList} from "ahooks";

import {useState, useRef, useEffect, forwardRef, useImperativeHandle} from "react";

const ipv4NetRegex = /^((25[0-5]|2[0-4]\d|1\d{2}|[1-9]?\d)\.){3}0\/(\d|[12]\d|3[0-2])$/;
const IPv4_REGEXP =
    /^(?:0|(?:[1-9]\d{0,1})|1\d{2}|2[0-4]\d|25[0-5])(?:\.(?:0|(?:[1-9]\d{0,1})|1\d{2}|2[0-4]\d|25[0-5])){3}$/;

const RouteInputList = forwardRef(({value = [], onChange}, ref) => {
    const {list: routeList, remove, getKey, replace, push} = useDynamicList(value);

    useEffect(() => {
        onChange?.(routeList);
    }, [routeList]);

    useImperativeHandle(ref, () => ({
        addRoute: () => {
            push({route: "", via: ""});
        },
        getRoutes: () => routeList
    }));

    const routeBlock = (index, route) => (
        <div
            key={getKey(index)}
            style={{
                border: "1px dashed #DCDCDC",
                padding: "16px",
                paddingBottom: "2px",
                paddingLeft: "24px",
                position: "relative",
                width: "568px",
                marginBottom: "8px"
            }}
        >
            <Form.Item
                label="Route"
                labelAlign="left"
                labelCol={{span: 5}}
                name={["extra_routes", index, "route"]}
                rules={[
                    {required: true, message: "Please input route"},
                    {
                        pattern: ipv4NetRegex,
                        message: "The subnet must be XXX.XXX.XXX.0/XX"
                    },
                    {
                        validator: async (_, value) => {
                            if (value && routeList.filter(item => item.route === value).length > 1) {
                                return Promise.reject(new Error("Duplicate route"));
                            }
                            return Promise.resolve();
                        }
                    }
                ]}
            >
                <Input
                    style={{width: "280px"}}
                    placeholder="CIDR(XXX.XXX.XXX.0/XX)"
                    onChange={e => replace(index, {...route, route: e.target.value})}
                />
            </Form.Item>
            <Form.Item
                label="Via"
                labelAlign="left"
                labelCol={{span: 5}}
                name={["extra_routes", index, "via"]}
                rules={[
                    {required: true, message: "Please input via"},
                    {
                        pattern: IPv4_REGEXP,
                        message: "Please input a valid IPv4 address"
                    }
                ]}
                style={{marginBottom: "14px"}}
            >
                <Input
                    style={{width: "280px"}}
                    placeholder="IP Address(XXX.XXX.XXX.XXX)"
                    onChange={e => replace(index, {...route, via: e.target.value})}
                />
            </Form.Item>
            <Icon
                className="deleteSvgStyle"
                component={deleteSvg}
                style={{position: "absolute", right: "16px", top: "16px", cursor: "pointer"}}
                onClick={() => {
                    remove(index);
                }}
            />
        </div>
    );

    return (
        <>
            {routeList.map((item, index) => {
                return routeBlock(index, item);
            })}
        </>
    );
});

export const VRFModal = forwardRef(({handleAddVRF, handleEditVRF, networkData, VRF_data}, ref) => {
    const [isShowModal, setIsShowModal] = useState(false);

    const [VRFForm] = Form.useForm();
    const [mode, setMode] = useState("");

    const routeInputListRef = useRef(null);

    useImperativeHandle(ref, () => ({
        showVRFModal: ({mode}, record) => {
            setMode(mode);
            if (mode === "edit") {
                VRFForm.setFieldsValue(record);
            }
            setIsShowModal(true);
        }
    }));

    return isShowModal ? (
        <Modal
            className="ampcon-middle-modal"
            title={
                <div>
                    {mode === "create" ? "Create VRF" : "Edit VRF"}
                    <Divider style={{marginTop: 8, marginBottom: 0}} />
                </div>
            }
            open={isShowModal}
            onOk={() => {}}
            onCancel={() => {
                setIsShowModal(false);
                VRFForm.resetFields();
            }}
            footer={
                <div>
                    <Divider style={{marginTop: 0, marginBottom: 20}} />
                    <Button
                        onClick={() => {
                            setIsShowModal(false);
                            VRFForm.resetFields();
                        }}
                    >
                        Cancel
                    </Button>
                    <Button
                        type="primary"
                        onClick={() => {
                            if (networkData.length === 0) {
                                message.error("Please create network first");
                                return;
                            }

                            VRFForm.validateFields().then(() => {
                                try {
                                    if (mode === "create") {
                                        handleAddVRF(VRFForm.getFieldsValue());
                                        message.success("VRF created successfully");
                                    }
                                    if (mode === "edit") {
                                        handleEditVRF(VRFForm.getFieldsValue());
                                        message.success("VRF edited successfully");
                                    }
                                } catch (error) {
                                    message.error("An error occurred while processing the VRF");
                                } finally {
                                    setIsShowModal(false);
                                    VRFForm.resetFields();
                                }
                            });
                        }}
                    >
                        Apply
                    </Button>
                </div>
            }
        >
            <Form
                layout="horizontal"
                labelAlign="left"
                labelCol={{span: 5}}
                wrapperCol={{span: 17}}
                labelWrap
                className="label-wrap"
                form={VRFForm}
                style={{minHeight: "267.23px"}}
                ref={VRFForm}
                validateTrigger={["onBlur", "onSubmit"]}
            >
                <Form.Item
                    name="name"
                    label="Name"
                    validateFirst
                    rules={[
                        {required: true, message: "Please input VRF name!"},
                        {max: 15, message: "VRF name cannot exceed 15 characters!"},
                        {
                            validator: (_, value) => {
                                if (mode === "create" && VRF_data.some(item => item.name === value)) {
                                    return Promise.reject(new Error("Name already exists"));
                                }
                                return Promise.resolve();
                            }
                        },
                        EmptyStringValidator("Name cannot contain spaces"),
                        {
                            validator: (_, value) => {
                                if (value === "test") {
                                    return Promise.reject(new Error("VRF name cannot be 'test'"));
                                }
                                return Promise.resolve();
                            }
                        }
                    ]}
                    initialValue=""
                >
                    <Input style={{width: "280px"}} disabled={mode === "edit"} />
                </Form.Item>
                <Form.Item
                    name="networks"
                    label="Networks"
                    initialValue={[]}
                    rules={[{required: true, message: "Please select networks"}]}
                >
                    <Checkbox.Group style={{width: "100%"}}>
                        {networkData.map(item => (
                            <Checkbox
                                value={item.name}
                                disabled={
                                    VRF_data.some(vrf => vrf.networks?.includes(item.name)) &&
                                    !VRFForm.getFieldValue("networks")?.includes(item.name)
                                }
                            >
                                {item.name}
                            </Checkbox>
                        ))}
                    </Checkbox.Group>
                </Form.Item>
                {/* <Form.Item label="Extra Routes" style={{marginBottom: "16px"}}>
                    <a
                        style={{
                            border: "none",
                            borderRadius: "4px",
                            color: "#14c9bb"
                        }}
                        onClick={() => {
                            routeInputListRef.current.addRoute();
                        }}
                    >
                        <Icon component={addGreenSvg} style={{marginRight: "8px"}} />
                        Add
                    </a>
                </Form.Item>
                <Form.Item
                    style={{
                        position: "relative"
                    }}
                    name="extra_routes"
                    initialValue={[]}
                >
                    <RouteInputList ref={routeInputListRef} />
                </Form.Item> */}
            </Form>
        </Modal>
    ) : null;
});
