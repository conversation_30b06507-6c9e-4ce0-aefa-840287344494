import {Button, Form, Input, message, Divider, Select, Modal} from "antd";
import {formValidateRules} from "@/modules-ampcon/utils/util";
import {EmptyStringValidator} from "../network_setting_form/form_validator";
import CustomPasswordInput from "@/modules-ampcon/components/custom_input_password";

import {useState, forwardRef, useImperativeHandle} from "react";

export const NACServerModal = forwardRef(({handleAddServer, handleEditServer, serverData}, ref) => {
    const [isShowModal, setIsShowModal] = useState(false);

    const ipv4RegEx =
        /^(?:0|(?:[1-9]\d{0,1})|1\d{2}|2[0-4]\d|25[0-5])(?:\.(?:0|(?:[1-9]\d{0,1})|1\d{2}|2[0-4]\d|25[0-5])){3}$/;

    const [form] = Form.useForm();
    const [mode, setMode] = useState("");

    useImperativeHandle(ref, () => ({
        showNetworkModal: ({mode}, record) => {
            setMode(mode);
            if (mode === "edit") {
                form.setFieldsValue(record);
            }
            setIsShowModal(true);
        }
    }));

    return isShowModal ? (
        <Modal
            className="ampcon-middle-modal"
            title={
                <div>
                    {mode === "create" ? "Create Server" : "Edit Server"}
                    <Divider style={{marginTop: 8, marginBottom: 0}} />
                </div>
            }
            open={isShowModal}
            onOk={() => {}}
            onCancel={() => {
                setIsShowModal(false);
                form.resetFields();
            }}
            footer={
                <div>
                    <Divider style={{marginTop: 0, marginBottom: 20}} />
                    <Button
                        onClick={() => {
                            setIsShowModal(false);
                            form.resetFields();
                        }}
                    >
                        Cancel
                    </Button>
                    <Button
                        type="primary"
                        onClick={() => {
                            form.submit();
                        }}
                    >
                        Apply
                    </Button>
                </div>
            }
        >
            <Form
                layout="horizontal"
                labelAlign="left"
                labelCol={{span: 5}}
                wrapperCol={{span: 17}}
                labelWrap
                className="label-wrap"
                form={form}
                style={{minHeight: "267.23px"}}
                ref={form}
                validateTrigger={["onBlur", "onSubmit"]}
                onFinish={() => {
                    try {
                        if (mode === "create") {
                            handleAddServer(form.getFieldsValue());
                            message.success("Server created successfully");
                        }
                        if (mode === "edit") {
                            handleEditServer(form.getFieldsValue());
                            message.success("Server edited successfully");
                        }
                    } catch (error) {
                        message.error("An error occurred while processing the server");
                        console.error(error);
                    } finally {
                        setIsShowModal(false);
                        form.resetFields();
                    }
                }}
            >
                <Form.Item
                    name="server_name"
                    label="Name"
                    validateFirst
                    rules={[
                        {required: true, message: "Please input server name!"},
                        {max: 32, message: "Server name cannot exceed 32 characters!"},
                        {
                            validator: (_, value) => {
                                if (mode === "create" && serverData.some(item => item.server_name === value)) {
                                    return Promise.reject(new Error("Name already exists"));
                                }
                                return Promise.resolve();
                            }
                        },
                        EmptyStringValidator("Name cannot contain spaces")
                    ]}
                    initialValue=""
                >
                    <Input style={{width: "280px"}} disabled={mode === "edit"} />
                </Form.Item>
                <Form.Item
                    name="server_address"
                    rules={[
                        {required: true, message: "Please input server address"},
                        {
                            pattern: ipv4RegEx,
                            message: "Please input a valid IPv4 address"
                        }
                    ]}
                    label="Server Address"
                    initialValue=""
                >
                    <Input style={{width: "280px"}} />
                </Form.Item>
                <Form.Item
                    name="port"
                    validateFirst
                    rules={[
                        {
                            required: true,
                            message: "Please input port"
                        },
                        formValidateRules.int(),
                        {
                            validator: (_, value) => {
                                if (value && typeof value === "string" && value.slice(0, 1) === "0") {
                                    return Promise.reject(new Error("Invalid port"));
                                }
                                if (Number.isInteger(Number(value)) && value >= 1 && value <= 65535) {
                                    return Promise.resolve();
                                }
                                return Promise.reject(new Error("Invalid port"));
                            },
                            message: "Invalid port"
                        }
                    ]}
                    label="Port"
                    initialValue={1812}
                >
                    <Input style={{width: "280px"}} />
                </Form.Item>

                <Form.Item
                    name="shared_secret"
                    validateFirst
                    rules={[
                        {required: true, message: "Please input key"},
                        {
                            validator: (_, value) => {
                                if (value.trim() === "") {
                                    return Promise.reject(new Error("Invalid shared secret"));
                                }
                                return Promise.resolve();
                            }
                        }
                    ]}
                    label="Shared Secret"
                    initialValue=""
                >
                    <CustomPasswordInput style={{width: "280px"}} />
                </Form.Item>
            </Form>
        </Modal>
    ) : null;
});
