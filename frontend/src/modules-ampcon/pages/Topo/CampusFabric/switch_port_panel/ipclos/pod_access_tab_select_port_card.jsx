import {But<PERSON>, <PERSON>, Flex, Menu, message, Radio, Select} from "antd";
import {forwardRef, useImperativeHandle, useRef, useState} from "react";

let isPortCardSelectDropdownVisible = false;

const PodAccessTabSelectedPortCard = forwardRef(
    (
        {
            podAccessTabUpdateDisPortStatusCallback,
            podAccessTabRemoveDisPortStatusCallback,
            podAccessTabUpdateCorePortStatusCallback,
            podAccessTabRemoveCorePortStatusCallback,
            getDisSwitchSNOptionsForAccess,
            getCoreSwitchSNOptionsForAccess,
            isDisNodeExists,
            isMenuCollapsed
        },
        ref
    ) => {
        const selectedPortCardRef = useRef(null);

        const [isShowPortCard, setIsShowPortCard] = useState(false);
        const [displayPos, setDisplayPos] = useState([0, 0]);
        const [displayPortName, setDisplayPortName] = useState("");
        const [selectedPortType, setSelectedPortType] = useState("ge");
        const [selectedSN, setSelectedSN] = useState("");
        const [isPortSelectedWithDis, setIsPortSelectedWithDis] = useState(false); // dis连接access
        const [isPortSelectedWithCore, setIsPortSelectedWithCore] = useState(false); // dis连接core

        const [isSelectedCoreValid, setIsSelectedCoreValid] = useState(true);
        const [currentCoreSwitchSNOptions, setCurrentCoreSwitchSNOptions] = useState([]);
        const [selectedCoreSwitchSN, setSelectedCoreSwitchSN] = useState("");
        const [isSelectedDisValid, setIsSelectedDisValid] = useState(true);
        const [currentDisSwitchSNOptions, setCurrentDisSwitchSNOptions] = useState([]);
        const [selectedDisSwitchSN, setSelectedDisSwitchSN] = useState("");

        const [showCoreLinkOptions, setShowCoreLinkOptions] = useState(false);
        const [showDisLinkOptions, setShowDisLinkOptions] = useState(false);

        useImperativeHandle(ref, () => ({
            showSelectedPortCard: (record, portName, portPosX, portPosY) => {
                setSelectedSN(record.sn);
                setDisplayPortName(portName);
                setDisplayPos([portPosX, portPosY]);
                setIsShowPortCard(true);
                updateCurrentPortStatus(record, portName);

                setIsSelectedCoreValid(true);
                setIsSelectedDisValid(true);

                setCurrentCoreSwitchSNOptions(getCoreSwitchSNOptionsForAccess(record.sn));
                setCurrentDisSwitchSNOptions(getDisSwitchSNOptionsForAccess(record.sn));

                showDiffSwitchLinkOptions();
            },
            hideSelectedPortCard: () => {
                setIsShowPortCard(false);
            },
            isSelectedPortCardShown: () => {
                return isShowPortCard;
            },
            isMouseClickOnPortCard: e => {
                if (isPortCardSelectDropdownVisible) {
                    return true;
                }
                const selectedPortCardRect = selectedPortCardRef.current.getBoundingClientRect();
                return (
                    selectedPortCardRect.x <= e.clientX &&
                    e.clientX <= selectedPortCardRect.x + selectedPortCardRect.width &&
                    selectedPortCardRect.y <= e.clientY &&
                    e.clientY <= selectedPortCardRect.y + selectedPortCardRect.height
                );
            }
        }));

        const updateCurrentPortStatus = (record, portName) => {
            if (record.currentLinkToDis && Object.keys(record.currentLinkToDis).includes(portName)) {
                setIsPortSelectedWithDis(true);
                setIsPortSelectedWithCore(false);
                setSelectedPortType(record.currentLinkToDis[portName].port_type);
                setSelectedDisSwitchSN(record.currentLinkToDis[portName].dis_sn);
            } else if (record.currentLinkToCore && Object.keys(record.currentLinkToCore).includes(portName)) {
                setIsPortSelectedWithDis(false);
                setIsPortSelectedWithCore(true);
                setSelectedPortType(record.currentLinkToCore[portName].port_type);
                setSelectedCoreSwitchSN(record.currentLinkToCore[portName].core_sn);
            } else {
                setIsPortSelectedWithDis(false);
                setIsPortSelectedWithCore(false);
                const portTypeName = portName.substring(0, 2).toLowerCase();
                setSelectedPortType(["ge", "te", "xe"].includes(portTypeName) ? portTypeName : "ge");

                const enabledCoreSNOptions = getCoreSwitchSNOptionsForAccess(record.sn).filter(item => {
                    return !item.disabled;
                });
                setSelectedCoreSwitchSN(enabledCoreSNOptions.length > 0 ? enabledCoreSNOptions[0].value : "");

                const enabledDisSNOptions = getDisSwitchSNOptionsForAccess(record.sn).filter(item => {
                    return !item.disabled;
                });
                setSelectedDisSwitchSN(enabledDisSNOptions.length > 0 ? enabledDisSNOptions[0].value : "");
            }
        };

        const showDiffSwitchLinkOptions = () => {
            if (isDisNodeExists) {
                setShowDisLinkOptions(true);
                setShowCoreLinkOptions(false);
            } else {
                setShowDisLinkOptions(false);
                setShowCoreLinkOptions(true);
            }
        };

        const calculateCardOffsetTop = () => {
            return displayPos[1] + 90 + 270 > window.innerHeight ? displayPos[1] - 90 - 310 : displayPos[1] - 90;
        };

        const calculateCardOffsetLeft = () => {
            const offsetLeft = isMenuCollapsed ? displayPos[0] - 110 : displayPos[0] - 290;
            if (offsetLeft + 600 > window.innerWidth) {
                return offsetLeft - 290 + 5;
            }
            return offsetLeft;
        };

        const getCoreLinkConnectionComponent = () => {
            if (isPortSelectedWithCore) {
                return (
                    <Button
                        id="removeLinkToCore"
                        onClick={() => {
                            podAccessTabRemoveCorePortStatusCallback(selectedSN, displayPortName);
                        }}
                    >
                        Remove Link to Core
                    </Button>
                );
            }
            return (
                <Button
                    id="linkToCore"
                    onClick={() => {
                        podAccessTabUpdateCorePortStatusCallback(
                            selectedSN,
                            displayPortName,
                            selectedPortType,
                            selectedCoreSwitchSN
                        );
                    }}
                >
                    Link to Core
                </Button>
            );
        };

        const getDisLinkConnectionComponent = () => {
            if (isPortSelectedWithDis) {
                return (
                    <Button
                        id="removeLinkToDis"
                        onClick={() => {
                            podAccessTabRemoveDisPortStatusCallback(selectedSN, displayPortName);
                        }}
                    >
                        Remove Link to Distribution
                    </Button>
                );
            }
            return (
                <Button
                    id="linkToDis"
                    onClick={() => {
                        podAccessTabUpdateDisPortStatusCallback(
                            selectedSN,
                            displayPortName,
                            selectedPortType,
                            selectedDisSwitchSN
                        );
                    }}
                >
                    Link to Distribution
                </Button>
            );
        };

        return isShowPortCard ? (
            <div
                style={{
                    position: "absolute",
                    top: `${calculateCardOffsetTop()}px`,
                    left: `${calculateCardOffsetLeft()}px`,
                    transition: "background-color 0.3s ease",
                    width: "275px",
                    zIndex: 1000
                }}
            >
                <Card ref={selectedPortCardRef} title={displayPortName} bordered={false}>
                    <Flex vertical>
                        <Flex style={{fontSize: "14px", paddingBottom: "12px"}}>Port Type</Flex>
                        <Flex style={{paddingBottom: "12px"}}>
                            <Radio.Group
                                onChange={e => {
                                    setSelectedPortType(e.target.value);
                                }}
                                value={selectedPortType}
                                // disabled={isPortSelectedWithCore || isPortSelectedWithDis}
                                disabled
                                options={[
                                    {value: "ge", label: "ge"},
                                    {value: "te", label: "te"},
                                    {value: "xe", label: "xe"}
                                ]}
                            />
                        </Flex>
                        {!isDisNodeExists ? (
                            <>
                                <Flex style={{fontSize: "14px", paddingBottom: "12px"}}>Core Switch SN</Flex>
                                <Flex style={{paddingBottom: "12px"}}>
                                    <Select
                                        style={{width: "100%"}}
                                        value={selectedCoreSwitchSN}
                                        onChange={key => {
                                            setSelectedCoreSwitchSN(key);
                                        }}
                                        options={currentCoreSwitchSNOptions}
                                        disabled={isPortSelectedWithCore}
                                        status={isSelectedCoreValid ? "" : "error"}
                                    />
                                </Flex>
                            </>
                        ) : null}

                        {isDisNodeExists ? (
                            <>
                                <Flex style={{fontSize: "14px", paddingBottom: "12px"}}>Distribution Switch SN</Flex>
                                <Flex style={{paddingBottom: "12px"}}>
                                    <Select
                                        style={{width: "100%"}}
                                        value={selectedDisSwitchSN}
                                        onChange={key => {
                                            setSelectedDisSwitchSN(key);
                                        }}
                                        options={currentDisSwitchSNOptions}
                                        disabled={isPortSelectedWithDis}
                                        status={isSelectedDisValid ? "" : "error"}
                                        onDropdownVisibleChange={isOpen => {
                                            if (isOpen) {
                                                isPortCardSelectDropdownVisible = true;
                                            } else {
                                                setTimeout(() => {
                                                    isPortCardSelectDropdownVisible = false;
                                                }, 100);
                                            }
                                        }}
                                    />
                                </Flex>
                            </>
                        ) : null}

                        <>
                            <Flex style={{fontSize: "14px", paddingBottom: "12px"}}>Port Connection</Flex>

                            {/* 如果 Dis 连接不存在，连接 Core */}
                            {showCoreLinkOptions ? (
                                <Flex style={{paddingBottom: "12px"}}>{getCoreLinkConnectionComponent()}</Flex>
                            ) : null}

                            {/* 如果 Dis 连接存在，显示 Dis */}
                            {showDisLinkOptions ? (
                                <Flex style={{paddingBottom: "12px"}}>{getDisLinkConnectionComponent()}</Flex>
                            ) : null}
                        </>
                    </Flex>
                </Card>
            </div>
        ) : null;
    }
);

export default PodAccessTabSelectedPortCard;
