import {But<PERSON>, <PERSON>lex} from "antd";
import React, {forwardRef, useEffect, useImperativeHandle, useRef, useState} from "react";
import PortNode from "@/modules-ampcon/pages/Topo/CampusFabric/switch_port_panel/port_node";

const SwitchPortInfoLogicSelectPanel = forwardRef(
    (
        {
            physicPortInfo,
            linkToCorePorts,
            linkToAccessPorts,
            linkToWANPorts,
            linkToBorderPorts,
            linkToDisPorts,
            selectedPortCardFunc,
            updateCurrentPortConfigForTheSameModelSwitch,
            record,
            tabType,
            podType,
            currentFabricTopologyType,
            isUseCoreAsBorder,
            isDisNodeExists,
            linkToWANExist = false
        },
        ref
    ) => {
        const [displayPairPhysicPortInfo, setDisplayPairPhysicPortInfo] = useState([]);
        const [hoveredPortIndex, setHoveredPortIndex] = useState(null);
        const portNodeRefs = useRef({});

        useImperativeHandle(ref, () => ({
            refreshPort: (portName, newStatus) => {
                portNodeRefs.current[portName].current.updateNodeStatus(newStatus);
            },
            refreshAllPortStatus: () => {
                refreshDisplayPairPhysicPortInfo(true);
            },
            clearAllPortsSelectedStatus: () => {
                Object.entries(portNodeRefs.current).forEach(([_, ref]) => {
                    ref.current.clearSelectedStatus();
                });
            }
        }));

        useEffect(() => {
            refreshDisplayPairPhysicPortInfo(false);
        }, []);

        const refreshDisplayPairPhysicPortInfo = (isForce = false) => {
            const temp = [];
            const res = [];
            const portNameStatusMappings = {};
            ["ge", "te", "qe", "xe"].map(type =>
                physicPortInfo?.[type]?.map(port => {
                    temp.push([port, type]);
                })
            );
            for (let i = 0; i < temp.length; i += 2) {
                const firstStatus = calculatePortStatus(temp[i][0]);
                portNameStatusMappings[temp[i][0]] = firstStatus;
                if (temp[i + 1] !== undefined) {
                    const secondStatus = calculatePortStatus(temp[i + 1][0]);
                    portNameStatusMappings[temp[i + 1][0]] = secondStatus;
                    res.push([
                        {
                            portName: temp[i][0],
                            portType: temp[i][1],
                            status: firstStatus
                        },
                        {
                            portName: temp[i + 1][0],
                            portType: temp[i + 1][1],
                            status: secondStatus
                        }
                    ]);
                } else {
                    res.push([
                        {
                            portName: temp[i][0],
                            portType: temp[i][1],
                            status: firstStatus
                        }
                    ]);
                }
            }
            if (isForce === true) {
                Object.keys(portNodeRefs.current).forEach(portName => {
                    portNodeRefs.current[portName].current.updateNodeStatus(portNameStatusMappings[portName]);
                });
            } else {
                setDisplayPairPhysicPortInfo(res);
            }
        };

        const handleMouseEnter = index => {
            setHoveredPortIndex(index);
        };

        const handleMouseLeave = () => {
            setHoveredPortIndex(null);
        };

        const calculatePortStatus = status => {
            if (Object.keys(linkToCorePorts).includes(status)) {
                return "selectedToCore";
            }
            if (Object.keys(linkToAccessPorts).includes(status)) {
                return "selectedToAccess";
            }
            if (linkToWANPorts && Object.keys(linkToWANPorts).includes(status)) {
                return "selectedToWAN";
            }
            if (linkToBorderPorts && Object.keys(linkToBorderPorts).includes(status)) {
                return "selectedToBorder";
            }
            if (linkToDisPorts && Object.keys(linkToDisPorts).includes(status)) {
                return "selectedToDis";
            }
            return "common";
        };

        const calculateDisplayPortStatus = (status, index) => {
            if (status === "common") {
                return hoveredPortIndex === index ? "hover" : "common";
            }
            return status;
        };

        return (
            <>
                <Flex
                    gap="16px"
                    style={{
                        position: "absolute",
                        right: "16px",
                        top: "16px"
                    }}
                >
                    <Flex>
                        <Button
                            disabled={(() => {
                                if (currentFabricTopologyType === "mlag") {
                                    if (tabType === "core") {
                                        return (
                                            record.maxLinkToCoreCount !== Object.keys(linkToCorePorts).length ||
                                            record.maxLinkToAccessCount !== Object.keys(linkToAccessPorts).length
                                        );
                                    }
                                    return record.maxLinkToCoreCount !== Object.keys(linkToCorePorts).length;
                                }

                                if (currentFabricTopologyType === "ip-clos") {
                                    if (tabType === "border") {
                                        return record.maxLinkToCoreCount !== Object.keys(linkToCorePorts).length;
                                    }

                                    if (tabType === "core") {
                                        if (linkToWANExist && Object.keys(linkToWANPorts).length !== 1) {
                                            return true;
                                        }
                                        if (!isUseCoreAsBorder) {
                                            if (isDisNodeExists) {
                                                return (
                                                    record.maxLinkToBorderCount !==
                                                        Object.keys(linkToBorderPorts).length ||
                                                    record.maxLinkToDisCount !== Object.keys(linkToDisPorts).length ||
                                                    record.maxLinkToCoreCount !== Object.keys(linkToCorePorts).length
                                                );
                                            }
                                            return (
                                                record.maxLinkToBorderCount !== Object.keys(linkToBorderPorts).length ||
                                                record.maxLinkToAccessCount !== Object.keys(linkToAccessPorts).length ||
                                                record.maxLinkToCoreCount !== Object.keys(linkToCorePorts).length
                                            );
                                        }

                                        if (isDisNodeExists) {
                                            return (
                                                record.maxLinkToDisCount !== Object.keys(linkToDisPorts).length ||
                                                record.maxLinkToCoreCount !== Object.keys(linkToCorePorts).length
                                            );
                                        }
                                        return (
                                            record.maxLinkToAccessCount !== Object.keys(linkToAccessPorts).length ||
                                            record.maxLinkToCoreCount !== Object.keys(linkToCorePorts).length
                                        );
                                    }

                                    if (tabType === "pod") {
                                        if (
                                            record.maxLinkToAccessCount &&
                                            record.maxLinkToCoreCount &&
                                            !record.maxLinkToDisCount
                                        ) {
                                            return (
                                                record.maxLinkToCoreCount !== Object.keys(linkToCorePorts).length ||
                                                record.maxLinkToAccessCount !== Object.keys(linkToAccessPorts).length
                                            );
                                        }
                                        return isDisNodeExists
                                            ? record.maxLinkToDisCount !== Object.keys(linkToDisPorts).length
                                            : record.maxLinkToCoreCount !== Object.keys(linkToCorePorts).length;
                                    }
                                }
                                return false;
                            })()}
                            onClick={() => {
                                updateCurrentPortConfigForTheSameModelSwitch({
                                    currentLinkToCore: linkToCorePorts,
                                    currentLinkToAccess: linkToAccessPorts,
                                    currentLinkToWAN: linkToWANPorts,
                                    currentLinkToDis: linkToDisPorts,
                                    currentLinkToBorder: linkToBorderPorts,
                                    model: record.model,
                                    podName: record.podName,
                                    podType
                                });
                            }}
                        >
                            Apply to All {record.model} Models
                        </Button>
                    </Flex>
                </Flex>
                <Flex
                    wrap
                    gap="small"
                    style={{paddingLeft: "50px", paddingTop: "32px", paddingBottom: "32px", width: "100%"}}
                >
                    <Flex horizontal style={{marginTop: "25px"}}>
                        {displayPairPhysicPortInfo.map((ports, pairIndex) => {
                            return (
                                <Flex vertical gap="8px">
                                    {ports.map((port, index) => {
                                        portNodeRefs.current[port.portName] = React.createRef();
                                        return (
                                            <Flex gap={(pairIndex + 1) % 6 === 0 ? "32px" : "0px"}>
                                                <PortNode
                                                    ref={portNodeRefs.current[port.portName]}
                                                    index={pairIndex * 2 + index + 1}
                                                    portName={port.portName}
                                                    portType={port.portType}
                                                    status={calculateDisplayPortStatus(
                                                        port.status,
                                                        pairIndex * 2 + index + 1
                                                    )}
                                                    isSvgReverse={index % 2 === 1}
                                                    handleMouseEnter={() => {
                                                        handleMouseEnter(pairIndex * 2 + index + 1);
                                                    }}
                                                    handleMouseLeave={handleMouseLeave}
                                                    selectedPortCardFunc={selectedPortCardFunc}
                                                    record={record}
                                                />
                                                <div style={{width: "8px"}} />
                                            </Flex>
                                        );
                                    })}
                                </Flex>
                            );
                        })}
                    </Flex>
                </Flex>
            </>
        );
    }
);

export default SwitchPortInfoLogicSelectPanel;
