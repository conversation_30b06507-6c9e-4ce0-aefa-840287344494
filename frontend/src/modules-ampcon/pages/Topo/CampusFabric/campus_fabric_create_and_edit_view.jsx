import {<PERSON><PERSON>, <PERSON>, Flex, message, Steps} from "antd";
import Icon, {<PERSON><PERSON>eftOutlined, LeftOutlined} from "@ant-design/icons";
import styles from "@/modules-ampcon/pages/Topo/CampusFabric/campus_fabric.module.scss";
import {useState, useRef, useEffect} from "react";
import CampusFabricStep1 from "@/modules-ampcon/pages/Topo/CampusFabric/create_campus_fabric_steps/campus_fabric_step1";
import CampusFabricStep2 from "@/modules-ampcon/pages/Topo/CampusFabric/create_campus_fabric_steps/campus_fabric_step2";
import CampusFabricStep3 from "@/modules-ampcon/pages/Topo/CampusFabric/create_campus_fabric_steps/campus_fabric_step3";
import CampusFabricStep4 from "@/modules-ampcon/pages/Topo/CampusFabric/create_campus_fabric_steps/campus_fabric_step4";
import CampusFabricStep5 from "@/modules-ampcon/pages/Topo/CampusFabric/create_campus_fabric_steps/campus_fabric_step5";
import {getAllModelPhysicPortInfo} from "@/modules-ampcon/apis/config_api";
import {saveConfig} from "@/modules-ampcon/apis/campus_blueprint_api";
import {useNavigate} from "react-router-dom";
import {confirmModalAction} from "@/modules-ampcon/components/custom_modal";
import {deleteClient, getSwitchLldp} from "@/modules-ampcon/apis/monitor_api";

const fakeDataSamples = {
    site_id: 1,
    topology_name: "",
    topology: {
        layer: 2,
        as_base: "",
        loopback_prefix: "",
        subnet: ""
    },
    networks: {
        vlans: {
            // vlan nameΪkey
            vlan_name: {
                vlan_id: "",
                subnet: "",
                virtual_gateway: ""
            }
        },
        enable_vrf: "enable || disable",
        vrfs: {
            vrf_name: {
                network: "vlan_name",
                extra_routes: {
                    route: "",
                    via: ""
                }
            }
        },
        port_config: {
            name: "",
            trunk_networks: [], // vlan_name
            advanced: {
                enable: "enable || disable",
                description: "",
                mode: "trunk || access",
                network: "", // vlan_name
                speed: "",
                poe: "enbale || disable",
                stp_edge: "yes || no",
                mtu: ""
            }
        }
    },
    nodes: [
        {
            switch_sn: "",
            type: "core || aggregation || access",
            router_id: "",
            other_ip_config: [
                {
                    vlan_name: "",
                    vlan_id: "",
                    ip_address: ""
                }
            ],
            links: {
                to_core: {
                    port_id1: "target_sn",
                    port_id2: "target_sn"
                },
                to_aggregation: {
                    port_id1: "target_sn",
                    port_id2: "target_sn"
                },
                to_access: {
                    port_id1: "target_sn",
                    port_id2: "target_sn"
                }
            }
        }
    ]
};

const mlagInitData = {
    site_id: null,
    topology_name: "",
    site_config_id: "",
    topology: {
        layer: 2,
        protocol: "BGP", // BGP || OSPF
        area_id: "0.0.0.0",
        as_base: "",
        loopback_prefix: "",
        subnet: "",
        inband_subnet: ""
    },
    networks: {
        vlans: {},
        domain_id: null,
        enable_nac: "disable", // "enable || disable"
        nac_servers: [],
        enable_dhcp_snooping: "disable", // "enable || disable"
        dhcp_snooping: [],
        enable_dhcp_relay: "disable", // "enable || disable"
        dhcp_relay: []
    },
    nodes: {
        core: [],
        access: []
    }
};

const ipclosInitData = {
    site_id: null,
    topology_name: "",
    site_config_id: "",
    topology: {
        layer: 3,
        protocol: "BGP",
        area_id: "0.0.0.0",
        asn: "", // = as_base
        as_base: "",
        loopback_prefix: "", // Route ID
        subnet: "",
        vrf_subnet: "" // optional
    },
    networks: {
        vlans: {},
        domain_id: null,
        enable_vrf: "disable", // "enable || disable"
        vrfs: {},
        enable_nac: "disable", // "enable || disable"
        nac_servers: [],
        enable_dhcp_relay: "disable", // "enable || disable"
        dhcp_relay: []
    },
    nodes: {
        border: [],
        core: [],
        pods: [
            {
                podName: "",
                distribution: [],
                access: []
            }
        ]
    }
};

const CampusFabricCreateAndEditView = ({backToTableViewCallback, campusFabricData, setCampusFabricData, isEditing}) => {
    const navigate = useNavigate();
    const step1Ref = useRef(null);
    const step2Ref = useRef(null); // 创建 ref
    const step3Ref = useRef(null);
    const step4Ref = useRef(null);
    const step5Ref = useRef(null);
    const [currentStep, setCurrentStep] = useState(0);
    const [currentFabricTopologyType, setCurrentFabricTopologyType] = useState("mlag");
    const [createCampusFabricData, setCreateCampusFabricData] = useState(mlagInitData);
    const [allModelPhysicPortInfo, setAllModelPhysicPortInfo] = useState([]);
    const [isEdited, setIsEdited] = useState(true);
    const [maxContainerHeight, setMaxContainerHeight] = useState(null);
    const [lldpData, setLldpData] = useState([]);
    const [initialProtocol, setInitialProtocol] = useState(null); // 最初始的Protocol

    useEffect(() => {
        const handleResize = () => {
            setMaxContainerHeight("calc(100vh - 200px)");
        };
        handleResize();
        window.addEventListener("resize", handleResize);
        return () => {
            window.removeEventListener("resize", handleResize);
        };
    }, []);

    useEffect(() => {
        getAllModelPhysicPortInfo().then(response => {
            if (response.status === 200) {
                setAllModelPhysicPortInfo(response.data);
            } else {
                message.error(response.info);
            }
        });

        const beforeUnloadCallback = event => {
            if (isEdited) {
                event.preventDefault();
                event.returnValue = "";
            }
        };
        if (isEditing) {
            setInitialProtocol(campusFabricData.topology.protocol);
        }
        window.addEventListener("beforeunload", beforeUnloadCallback);

        return () => {
            window.removeEventListener("beforeunload", beforeUnloadCallback);
        };
    }, []);

    useEffect(() => {
        if (currentFabricTopologyType === "mlag") {
            setCreateCampusFabricData(prevData => {
                return {
                    ...mlagInitData,
                    ...prevData,
                    networks: mlagInitData.networks,
                    ...{
                        nodes: {
                            core: [],
                            access: []
                        }
                    }
                };
            });
        } else if (currentFabricTopologyType === "ip-clos") {
            setCreateCampusFabricData(prevData => {
                return {
                    ...ipclosInitData,
                    ...prevData,
                    networks: ipclosInitData.networks,
                    ...{
                        nodes: {
                            border: [],
                            core: [],
                            pods: [
                                {
                                    podName: "",
                                    distribution: [],
                                    access: []
                                }
                            ]
                        }
                    }
                };
            });
        }
    }, [currentFabricTopologyType]);

    const handleApply = async () => {
        if (isEditing) {
            if (currentFabricTopologyType === "mlag") {
                const applyData = {
                    site_id: campusFabricData.site_id,
                    config_id: campusFabricData.site_config_id,
                    topology_name: campusFabricData.topology_name,
                    topology: campusFabricData.topology,
                    networks: campusFabricData.networks,
                    type: "mlag",
                    nodes: {
                        core: campusFabricData.nodes.core.map(node => {
                            return {
                                switch_sn: node.switch_sn,
                                router_id: node.router_id,
                                mac_addr: node.mac_addr,
                                other_ip_config: node.other_ip_config,
                                links: node.links,
                                label: node.label,
                                mgt_ip: node.mgt_ip,
                                model: node.model,
                                id: node.id,
                                status: node.status
                            };
                        }),
                        access: campusFabricData.nodes.access.map(node => {
                            return {
                                switch_sn: node.switch_sn,
                                router_id: node.router_id,
                                mac_addr: node.mac_addr,
                                other_ip_config: node.other_ip_config,
                                links: node.links,
                                label: node.label,
                                mgt_ip: node.mgt_ip,
                                model: node.model,
                                id: node.id,
                                status: node.status
                            };
                        })
                    }
                };
                try {
                    const res = await saveConfig(applyData);
                    if (res.status === 200) {
                        message.success("Edit Campus Fabric Success");
                        setIsEdited(false);
                        navigate("/topo/campus_fabric");
                    }
                } catch (error) {
                    message.error(error);
                }
            } else if (currentFabricTopologyType === "ip-clos") {
                const applyData = {
                    site_id: campusFabricData.site_id,
                    config_id: campusFabricData.site_config_id,
                    topology_name: campusFabricData.topology_name,
                    topology: campusFabricData.topology,
                    networks: campusFabricData.networks,
                    type: "ip-clos",
                    nodes: {
                        border: campusFabricData.nodes.border.map(node => {
                            return {
                                switch_sn: node.switch_sn,
                                router_id: node.router_id,
                                mac_addr: node.mac_addr,
                                other_ip_config: node.other_ip_config,
                                links: node.links,
                                label: node.label,
                                mgt_ip: node.mgt_ip,
                                model: node.model,
                                id: node.id,
                                status: node.status
                            };
                        }),
                        core: campusFabricData.nodes.core.map(node => {
                            return {
                                switch_sn: node.switch_sn,
                                router_id: node.router_id,
                                mac_addr: node.mac_addr,
                                other_ip_config: node.other_ip_config,
                                links: node.links,
                                label: node.label,
                                mgt_ip: node.mgt_ip,
                                model: node.model,
                                id: node.id,
                                status: node.status
                            };
                        }),
                        pods: campusFabricData.nodes.pods.map((pod, index) => {
                            return {
                                pod_name: pod?.podName || pod?.pod_name,
                                pod_index: index,
                                ...(pod?.pod_id ? {pod_id: pod.pod_id} : {}),
                                distribution: pod.distribution.map(node => {
                                    return {
                                        switch_sn: node.switch_sn,
                                        router_id: node.router_id,
                                        mac_addr: node.mac_addr,
                                        other_ip_config: node.other_ip_config,
                                        links: node.links,
                                        label: node.label,
                                        mgt_ip: node.mgt_ip,
                                        model: node.model,
                                        id: node.id,
                                        status: node.status
                                    };
                                }),
                                access: pod.access.map(node => {
                                    return {
                                        switch_sn: node.switch_sn,
                                        router_id: node.router_id,
                                        mac_addr: node.mac_addr,
                                        other_ip_config: node.other_ip_config,
                                        links: node.links,
                                        label: node.label,
                                        mgt_ip: node.mgt_ip,
                                        model: node.model,
                                        id: node.id,
                                        status: node.status
                                    };
                                })
                            };
                        })
                    }
                };
                try {
                    const res = await saveConfig(applyData);
                    if (res.status === 200) {
                        message.success("Edit Campus Fabric Success");
                        setIsEdited(false);
                        navigate("/topo/campus_fabric");
                    }
                } catch (error) {
                    message.error(error);
                }
            }
        } else if (currentFabricTopologyType === "mlag") {
            const applyData = {
                site_id: createCampusFabricData.site_id,
                config_id: "",
                topology_name: createCampusFabricData.topology_name,
                topology: createCampusFabricData.topology,
                networks: createCampusFabricData.networks,
                type: "mlag",
                nodes: {
                    core: createCampusFabricData.nodes.core.map(node => {
                        return {
                            switch_sn: node.switch_sn,
                            router_id: node.router_id,
                            mac_addr: node.mac_addr,
                            other_ip_config: node.other_ip_config,
                            links: node.links,
                            label: node.label,
                            mgt_ip: node.mgt_ip,
                            model: node.model,
                            id: node.id,
                            status: node.status
                        };
                    }),
                    access: createCampusFabricData.nodes.access.map(node => {
                        return {
                            switch_sn: node.switch_sn,
                            router_id: node.router_id,
                            mac_addr: node.mac_addr,
                            other_ip_config: node.other_ip_config,
                            links: node.links,
                            label: node.label,
                            mgt_ip: node.mgt_ip,
                            model: node.model,
                            id: node.id,
                            status: node.status
                        };
                    })
                }
            };
            try {
                const res = await saveConfig(applyData);
                if (res.status === 200) {
                    message.success("Create Campus Fabric Success");
                    setIsEdited(false);
                    backToTableViewCallback();
                }
            } catch (error) {
                message.error(error);
            }
        } else if (currentFabricTopologyType === "ip-clos") {
            const applyData = {
                site_id: createCampusFabricData.site_id,
                config_id: "",
                topology_name: createCampusFabricData.topology_name,
                topology: createCampusFabricData.topology,
                networks: createCampusFabricData.networks,
                type: "ip-clos",
                nodes: {
                    border: createCampusFabricData.nodes.border.map(node => {
                        return {
                            switch_sn: node.switch_sn,
                            router_id: node.router_id,
                            mac_addr: node.mac_addr,
                            other_ip_config: node.other_ip_config,
                            links: node.links,
                            label: node.label,
                            mgt_ip: node.mgt_ip,
                            model: node.model,
                            id: node.id,
                            status: node.status
                        };
                    }),
                    core: createCampusFabricData.nodes.core.map(node => {
                        return {
                            switch_sn: node.switch_sn,
                            router_id: node.router_id,
                            mac_addr: node.mac_addr,
                            other_ip_config: node.other_ip_config,
                            links: node.links,
                            label: node.label,
                            mgt_ip: node.mgt_ip,
                            model: node.model,
                            id: node.id,
                            status: node.status
                        };
                    }),
                    pods: createCampusFabricData.nodes.pods.map((pod, index) => {
                        return {
                            pod_name: pod?.podName || pod?.pod_name,
                            pod_index: index,
                            distribution: pod.distribution.map(node => {
                                return {
                                    switch_sn: node.switch_sn,
                                    router_id: node.router_id,
                                    mac_addr: node.mac_addr,
                                    other_ip_config: node.other_ip_config,
                                    links: node.links,
                                    label: node.label,
                                    mgt_ip: node.mgt_ip,
                                    model: node.model,
                                    id: node.id,
                                    status: node.status
                                };
                            }),
                            access: pod.access.map(node => {
                                return {
                                    switch_sn: node.switch_sn,
                                    router_id: node.router_id,
                                    mac_addr: node.mac_addr,
                                    other_ip_config: node.other_ip_config,
                                    links: node.links,
                                    label: node.label,
                                    mgt_ip: node.mgt_ip,
                                    model: node.model,
                                    id: node.id,
                                    status: node.status
                                };
                            })
                        };
                    })
                }
            };
            try {
                const res = await saveConfig(applyData);
                if (res.status === 200) {
                    message.success("Create Campus Fabric Success");
                    setIsEdited(false);
                    backToTableViewCallback();
                }
            } catch (error) {
                message.error(error);
            }
        }
    };

    const steps = [
        {
            title: "Topology",
            content: (
                <CampusFabricStep1
                    ref={step1Ref}
                    campusFabricData={isEditing ? campusFabricData : createCampusFabricData}
                    setCampusFabricData={isEditing ? setCampusFabricData : setCreateCampusFabricData}
                    currentFabricTopologyType={isEditing ? campusFabricData.type : currentFabricTopologyType}
                    setCurrentFabricTopologyType={setCurrentFabricTopologyType}
                    isEditing={isEditing}
                    initialProtocol={initialProtocol}
                />
            )
        },
        {
            title: "Nodes",
            content: (
                <CampusFabricStep2
                    ref={step2Ref}
                    currentFabricTopologyType={currentFabricTopologyType}
                    campusFabricData={isEditing ? campusFabricData : createCampusFabricData}
                    setCampusFabricData={isEditing ? setCampusFabricData : setCreateCampusFabricData}
                    allModelPhysicPortInfo={allModelPhysicPortInfo}
                    isEditing={isEditing}
                />
            )
        },
        {
            title: "Network Settings",
            content: (
                <CampusFabricStep3
                    ref={step3Ref}
                    currentFabricTopologyType={currentFabricTopologyType}
                    campusFabricData={isEditing ? campusFabricData : createCampusFabricData}
                    setCampusFabricData={isEditing ? setCampusFabricData : setCreateCampusFabricData}
                />
            )
        },
        {
            title: "Ports",
            content: (
                <CampusFabricStep4
                    ref={step4Ref}
                    currentFabricTopologyType={currentFabricTopologyType}
                    campusFabricData={isEditing ? campusFabricData : createCampusFabricData}
                    // campusFabricData={fakeIpClosDataforStep4}
                    setCampusFabricData={isEditing ? setCampusFabricData : setCreateCampusFabricData}
                    allModelPhysicPortInfo={allModelPhysicPortInfo}
                />
            )
        },
        {
            title: "Confirm",
            content: (
                <CampusFabricStep5
                    ref={step5Ref}
                    currentFabricTopologyType={currentFabricTopologyType}
                    campusFabricData={isEditing ? campusFabricData : createCampusFabricData}
                    setCampusFabricData={isEditing ? setCampusFabricData : setCreateCampusFabricData}
                    lldpData={lldpData}
                />
            )
        }
    ];

    const stepItems = steps.map(item => ({
        key: item.title,
        title: item.title
    }));

    const validateStep = async () => {
        switch (currentStep) {
            case 0:
                if (step1Ref.current) {
                    return await step1Ref.current.validate();
                }
                return true;
            case 1:
                if (step2Ref.current) {
                    const isValid = await step2Ref.current.validate();
                    if (!isValid) return false;
                }
                return true;
            case 2:
                if (step3Ref.current) {
                    return step3Ref.current.validate();
                }
                return true;
            case 3:
                if (step4Ref.current) {
                    return step4Ref.current.validate();
                }
                return false;
            default:
                return true;
        }
    };
    const nextStep5 = async () => {
        const snList = [];

        const collectSwitchSn = devices => {
            devices.forEach(device => {
                if (device.switch_sn) {
                    snList.push(device.switch_sn);
                }
            });
        };

        const dataSource = isEditing ? campusFabricData : createCampusFabricData;

        if (currentFabricTopologyType === "mlag") {
            collectSwitchSn(dataSource.nodes.core);
            collectSwitchSn(dataSource.nodes.access);
        } else if (currentFabricTopologyType === "ip-clos") {
            collectSwitchSn(dataSource.nodes.border);
            collectSwitchSn(dataSource.nodes.core);
            dataSource.nodes.pods.forEach(pod => {
                collectSwitchSn(pod.distribution);
                collectSwitchSn(pod.access);
            });
        }

        try {
            const lldpResult = {};
            const result = await getSwitchLldp(snList);
            if (result?.status === 200) {
                Object.entries(result.data).forEach(([sourceDevice, targets]) => {
                    Object.entries(targets).forEach(([targetDevice, connections]) => {
                        connections.forEach(connection => {
                            const key = `${sourceDevice}:${connection.source_port}->${targetDevice}:${connection.target_port}`;
                            lldpResult[key] = false;
                        });
                    });
                });
            }
            setLldpData(lldpResult);
            next();
        } catch (error) {
            message.error(error);
        }
    };
    const next = async () => {
        const isValid = await validateStep();
        if (isValid) {
            setCurrentStep(currentStep + 1);
        }
    };

    const prev = () => {
        setCurrentStep(currentStep - 1);
    };

    return (
        <Card style={{display: "flex", flexDirection: "column", flex: 1}}>
            <Flex
                vertical
                flex={1}
                style={{height: "100%", maxHeight: maxContainerHeight, overflowY: "scroll", paddingRight: "5px"}}
            >
                <p className={styles.goBack} onClick={backToTableViewCallback} style={{marginBottom: "23px"}}>
                    <ArrowLeftOutlined style={{marginRight: "8px"}} />
                    <span>Back</span>
                </p>
                <Steps current={currentStep} items={stepItems} style={{width: "90%"}} />
                <Flex vertical flex={1} justify="space-between" style={{height: "100%"}}>
                    <div style={{flex: 1, marginTop: "18px"}}>{steps[currentStep].content}</div>
                    <Flex
                        id="campusfabric-footer"
                        style={{
                            marginTop: 24,
                            position: "absolute",
                            bottom: "16px",
                            right: "0px",
                            flexDirection: "row-reverse",
                            width: "calc(100% + 48px)",
                            paddingRight: "24px",
                            borderTop: "1px solid #eaeaea",
                            paddingTop: "16px"
                        }}
                    >
                        {currentStep < steps.length - 1 && (
                            <Button
                                type="primary"
                                onClick={() => {
                                    if (currentStep === 3) {
                                        nextStep5();
                                    } else {
                                        next();
                                    }
                                }}
                            >
                                Next
                            </Button>
                        )}
                        {currentStep === steps.length - 1 && (
                            <Button
                                type="primary"
                                onClick={() => {
                                    confirmModalAction(
                                        "Configurations will be immediately pushed to switches and take about 10 minutes to complete. To check the deployment status, view switch logs on the Switch page. OK to proceed?",
                                        () => {
                                            handleApply();
                                        }
                                    );
                                }}
                            >
                                Apply
                            </Button>
                        )}
                        {currentStep > 0 && (
                            <Button style={{marginRight: "16px"}} onClick={() => prev()}>
                                Previous
                            </Button>
                        )}
                    </Flex>
                </Flex>
            </Flex>
        </Card>
    );
};

export default CampusFabricCreateAndEditView;
