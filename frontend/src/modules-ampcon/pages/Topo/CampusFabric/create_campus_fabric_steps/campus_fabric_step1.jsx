import {forwardRef, useState, useEffect, useRef, useImperativeHandle} from "react";
import CampusFabricMLAGStep1 from "@/modules-ampcon/pages/Topo/CampusFabric/create_campus_fabric_steps/mlag_steps/campus_fabric_mlag_step1";
import CampusFabricIPClosStep1 from "@/modules-ampcon/pages/Topo/CampusFabric/create_campus_fabric_steps/ip_clos_steps/campus_fabric_ip_clos_step1";
import {Divider, Form, Radio, Spin, Select, message} from "antd";
import {getSite} from "@/modules-ampcon/apis/lifecycle_api";
import "./campus_fabric_step1.scss";
// import {LineChartOutlined} from "@ant-design/icons";

const {Option} = Select;

const containerStyle = {
    alignItems: "flex-start",
    marginLeft: "5px",
    padding: "0",
    boxSizing: "border-box",
    width: "100%"
};

const headerStyle = {
    fontFamily: "Lato, sans-serif",
    fontWeight: 700,
    color: "#212519",
    textAlign: "left",
    fontStyle: "normal",
    textTransform: "none",
    marginBottom: "16px"
};

const h2Style = {
    ...headerStyle,
    fontSize: "24px",
    lineHeight: "29px"
};

const h3Style = {
    ...headerStyle,
    fontSize: "18px",
    lineHeight: "22px",
    marginTop: "32px",
    marginBottom: "16px"
};

const Header = ({level, children}) => {
    if (level === 2) {
        return <h2 style={h2Style}>{children}</h2>;
    }
    if (level === 3) {
        return <h3 style={h3Style}>{children}</h3>;
    }
    return null;
};

const CampusFabricStep1 = forwardRef(
    (
        {
            currentFabricTopologyType,
            setCurrentFabricTopologyType,
            campusFabricData,
            setCampusFabricData,
            isEditing,
            initialProtocol
        },
        ref
    ) => {
        const [siteForm] = Form.useForm();
        const [siteOptions, setSiteOptions] = useState([]);
        const [loadingSites, setLoadingSites] = useState(false);
        const typeStep1Ref = useRef(null);
        setCurrentFabricTopologyType(currentFabricTopologyType);
        useImperativeHandle(ref, () => ({
            validate: async () => {
                try {
                    await siteForm.validateFields();
                    // 如果有需要校验的子组件，调用子组件的验证方法
                    if (typeStep1Ref.current) {
                        const result = await typeStep1Ref.current.validate();
                        return result; // 返回子组件的验证结果
                    }
                    return true;
                } catch (error) {
                    return false;
                }
            }
        }));

        useEffect(() => {
            const fetchData = async () => {
                try {
                    if (!isEditing) {
                        setLoadingSites(true);
                    }

                    // 获取Site数据
                    const siteResponse = await getSite();
                    if (siteResponse.status === 200) {
                        setSiteOptions(siteResponse.data);
                    } else {
                        message.error("Failed to fetch site data.");
                    }
                } catch (error) {
                    console.error("Error fetching site data:", error);
                    message.error("An error occurred while fetching site data.");
                } finally {
                    setLoadingSites(false);
                }
            };

            fetchData();
        }, [isEditing]); // 捕捉isEditing状态

        const handleSiteChange = value => {
            setCampusFabricData(prevState => {
                const isSiteChanged = value !== undefined && value !== prevState.site_id;
                const clearedNodesData =
                    currentFabricTopologyType === "mlag"
                        ? {
                              nodes: {core: [], access: []} // 如果site变化，清空nodes的core和access
                          }
                        : {
                              nodes: {
                                  border: [],
                                  core: [],
                                  pods: [
                                      {
                                          podName: "",
                                          distribution: [],
                                          access: []
                                      }
                                  ]
                              }
                          };

                return {
                    ...prevState,
                    site_id: value, // 更新site_id字段
                    ...(isSiteChanged && clearedNodesData)
                };
            });
        };

        const initializeFormValues = data => {
            if (!data) return;

            siteForm.setFieldsValue({
                site: data.site_id
            });
        };

        useEffect(() => {
            initializeFormValues(campusFabricData);
        }, [campusFabricData]);

        return (
            <div style={containerStyle}>
                <Form
                    layout="horizontal"
                    labelAlign="left"
                    labelCol={{span: 6}}
                    style={{width: "505px"}}
                    form={siteForm}
                >
                    <Header level={2}>Choose Campus Fabric Topology</Header>
                    <Header level={3}>Site Selection</Header>

                    <Form.Item
                        label="Site"
                        labelCol={{style: {width: "175px"}}}
                        name="site"
                        validateTrigger="onBlur"
                        rules={[{required: true, message: "Please select a site!"}]}
                    >
                        {loadingSites ? (
                            <Spin />
                        ) : (
                            <Select
                                className={isEditing ? "custom-disabled-select" : ""}
                                style={{
                                    width: "280px",
                                    height: "36px"
                                }}
                                placeholder="Select a site"
                                disabled={isEditing}
                                value={campusFabricData.site_id} // 绑定到 campusFabricData.site_id 以显示选中的值
                                onChange={handleSiteChange} // 更新site_id字段
                            >
                                {siteOptions.map(site => (
                                    <Option key={site} value={site}>
                                        {site}
                                    </Option>
                                ))}
                            </Select>
                        )}
                    </Form.Item>

                    <Divider style={{marginTop: "32px", marginBottom: "32px"}} />

                    <Header level={3}>Fabric Topology Type</Header>
                    <Form.Item
                        label="Type"
                        labelCol={{style: {width: "175px"}}}
                        validateTrigger="onBlur"
                        rules={[{required: true, message: "Please select a site!"}]}
                    >
                        <Radio.Group
                            onChange={e => {
                                setCurrentFabricTopologyType(e.target.value);
                            }}
                            value={currentFabricTopologyType}
                            options={[
                                {
                                    value: "mlag",
                                    label: "MLAG"
                                },
                                {
                                    value: "ip-clos",
                                    label: "IP Clos"
                                }
                            ]}
                            disabled={isEditing}
                        />
                    </Form.Item>
                    <Divider style={{marginTop: "32px", marginBottom: "32px"}} />
                </Form>

                {currentFabricTopologyType === "ip-clos" ? (
                    <CampusFabricIPClosStep1
                        ref={typeStep1Ref}
                        campusFabricData={campusFabricData}
                        setCampusFabricData={setCampusFabricData}
                        isEditing={isEditing}
                        Header={Header}
                        siteForm={siteForm}
                        initialProtocol={initialProtocol}
                    />
                ) : (
                    <CampusFabricMLAGStep1
                        ref={typeStep1Ref}
                        campusFabricData={campusFabricData}
                        setCampusFabricData={setCampusFabricData}
                        isEditing={isEditing}
                        Header={Header}
                        siteForm={siteForm}
                        initialProtocol={initialProtocol}
                    />
                )}
            </div>
        );
    }
);

export default CampusFabricStep1;
