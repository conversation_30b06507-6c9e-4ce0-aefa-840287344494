import {message, Table, Tabs} from "antd";
import React, {forwardRef, useEffect, useImperativeHandle, useRef, useState} from "react";
import ExpandIcon from "@/modules-ampcon/components/expand_icon";
import SwitchPortInfoLogicSelectPanel from "@/modules-ampcon/pages/Topo/CampusFabric/switch_port_panel/switch_port_info_logic_select_panel";
import SwitchPortInfoPhysicSelectPanel from "@/modules-ampcon/pages/Topo/CampusFabric/switch_port_panel/switch_port_info_physic_select_panel";
import CoreTabSelectedPortCard from "@/modules-ampcon/pages/Topo/CampusFabric/switch_port_panel/ipclos/core_tab_select_port_card";
import {usePopper} from "react-popper";
import BorderTabSelectedPortCard from "@/modules-ampcon/pages/Topo/CampusFabric/switch_port_panel/ipclos/border_tab_select_port_card";
import PodDisTabSelectedPortCard from "@/modules-ampcon/pages/Topo/CampusFabric/switch_port_panel/ipclos/pod_dis_tab_select_port_card";
import PodAccessTabSelectedPortCard from "@/modules-ampcon/pages/Topo/CampusFabric/switch_port_panel/ipclos/pod_access_tab_select_port_card";
import SwitchPortPhysicData from "@/modules-ampcon/pages/Topo/CampusFabric/switch_port_panel/switch_port_physic_data";
import PodsDisplayPanel from "@/modules-ampcon/pages/Topo/CampusFabric/nodes_selector/pods_display_panel";
import DeviceBriefTooltip from "@/modules-ampcon/pages/Topo/CampusFabric/tooltip/device_brief_tooltip";
import style from "@/modules-ampcon/pages/Topo/CampusFabric/campus_fabric.module.scss";
import {ArrowLeftOutlined} from "@ant-design/icons";

const generateCoreDynamicColumns = (data, isUseCoreAsBorder, isDisNodeExists, linkToWANExist) => {
    const dynamicColumnDefinitions = [
        {
            title: "Link to WAN",
            currentKey: "currentLinkToWANCount",
            maxKey: "maxLinkToWANCount",
            key: "linkToWANCount",
            width: "20%"
        },
        {
            title: "Link to Border",
            currentKey: "currentLinkToBorderCount",
            maxKey: "maxLinkToBorderCount",
            key: "linkToBorderCount",
            width: "20%"
        },
        {
            title: "Link to Core",
            currentKey: "currentLinkToCoreCount",
            maxKey: "maxLinkToCoreCount",
            key: "linkToCoreCount",
            width: "20%"
        },
        {
            title: "Link to Distribution",
            currentKey: "currentLinkToDisCount",
            maxKey: "maxLinkToDisCount",
            key: "linkToDisCount",
            width: "20%"
        },
        {
            title: "Link to Access",
            currentKey: "currentLinkToAccessCount",
            maxKey: "maxLinkToAccessCount",
            key: "linkToAccessCount",
            width: "20%"
        }
    ];

    let columnsToRender = [];

    if (isUseCoreAsBorder) {
        const wanColumn = dynamicColumnDefinitions.find(column => column.key === "linkToWANCount");
        if (wanColumn && !columnsToRender.includes(wanColumn)) {
            columnsToRender.push(wanColumn);
        }
    }

    // 原有条件判断逻辑
    if (isUseCoreAsBorder && isDisNodeExists) {
        columnsToRender = columnsToRender.concat(
            dynamicColumnDefinitions.filter(
                column => column.key === "linkToCoreCount" || column.key === "linkToDisCount"
            )
        );
    } else if (isUseCoreAsBorder && !isDisNodeExists) {
        columnsToRender = columnsToRender.concat(
            dynamicColumnDefinitions.filter(
                column => column.key === "linkToCoreCount" || column.key === "linkToAccessCount"
            )
        );
    } else if (!isUseCoreAsBorder && isDisNodeExists) {
        columnsToRender = columnsToRender.concat(
            dynamicColumnDefinitions.filter(
                column =>
                    column.key === "linkToBorderCount" ||
                    column.key === "linkToCoreCount" ||
                    column.key === "linkToDisCount"
            )
        );
    } else if (!isUseCoreAsBorder && !isDisNodeExists) {
        columnsToRender = columnsToRender.concat(
            dynamicColumnDefinitions.filter(
                column =>
                    column.key === "linkToBorderCount" ||
                    column.key === "linkToCoreCount" ||
                    column.key === "linkToAccessCount"
            )
        );
    }

    return columnsToRender.map(column => {
        if (column.key === "linkToWANCount") {
            return {
                title: column.title,
                key: column.key,
                width: "20%",
                render: (text, record) => {
                    if (!linkToWANExist) {
                        return <div>{`${record.currentLinkToWANCount}`}</div>;
                    }
                    return (
                        <div
                            style={{
                                color: record.currentLinkToWANCount === record.maxLinkToWANCount ? "#2BC174" : "#F53F3F"
                            }}
                        >
                            {`${record.currentLinkToWANCount}/${record.maxLinkToWANCount}`}
                        </div>
                    );
                }
            };
        }
        return {
            title: column.title,
            key: column.key,
            width: "20%",
            render: (text, record) => (
                <div
                    style={{
                        color: record[column.currentKey] === record[column.maxKey] ? "#2BC174" : "#F53F3F"
                    }}
                >
                    {`${record[column.currentKey]}/${record[column.maxKey]}`}
                </div>
            )
        };
    });
};

const generatePodAccessDynamicColumns = (data, isDisNodeExists) => {
    const dynamicColumnDefinitions = [
        {
            title: "Link to Core",
            currentKey: "currentLinkToCoreCount",
            maxKey: "maxLinkToCoreCount",
            key: "linkToCoreCount",
            width: "33%"
        },
        {
            title: "Link to Distribution",
            currentKey: "currentLinkToDisCount",
            maxKey: "maxLinkToDisCount",
            key: "linkToDisCount",
            width: "33%"
        }
    ];

    let columnsToRender = [];

    if (isDisNodeExists) {
        columnsToRender = dynamicColumnDefinitions.filter(column => column.key === "linkToDisCount");
    } else {
        columnsToRender = dynamicColumnDefinitions.filter(column => column.key === "linkToCoreCount");
    }

    return columnsToRender.map(column => ({
        title: column.title,
        key: column.key,
        width: column.width,
        render: (text, record) => (
            <div
                style={{
                    color: record[column.currentKey] === record[column.maxKey] ? "#2BC174" : "#F53F3F"
                }}
            >
                {`${record[column.currentKey]}/${record[column.maxKey]}`}
            </div>
        )
    }));
};

const CampusFabricIPClosStep4 = forwardRef(
    ({campusFabricData, setCampusFabricData, allModelPhysicPortInfo, currentFabricTopologyType}, ref) => {
        const coreTableRef = useRef();
        const borderTableRef = useRef();
        const podDisTableRef = useRef();
        const podAccessTableRef = useRef();
        const coreTabSelectedPortCardRef = useRef();
        const borderTabSelectedPortCardRef = useRef();
        const podDisTabSelectedPortCardRef = useRef();
        const podAccessTabSelectedPortCardRef = useRef();
        const expandedRowRefs = useRef({});
        const deviceBriefTooltipRef = useRef();

        const [referenceElement, setReferenceElement] = useState(null);
        const [popperElement, setPopperElement] = useState(null);
        const [tableBorderData, setTableBorderData] = useState([]);
        const [tableCoreData, setTableCoreData] = useState([]);
        const [tablePodDisData, setTablePodDisData] = useState([]);
        const [tablePodAccessData, setTablePodAccessData] = useState([]);
        const [isMenuCollapsed, setIsMenuCollapsed] = useState(false);

        const [borderTabExpandedRowKeys, setBorderTabExpandedRowKeys] = useState([]);
        const [coreTabExpandedRowKeys, setCoreTabExpandedRowKeys] = useState([]);
        const [podDisTabExpandedRowKeys, setPodDisTabExpandedRowKeys] = useState([]);
        const [podAccessTabExpandedRowKeys, setPodAccessTabExpandedRowKeys] = useState([]);
        const [podName, setPodName] = useState("");

        const [isUseCoreAsBorder, setIsUseCoreAsBorder] = useState(false);
        const [isDisNodeExists, setIsDisNodeExists] = useState(false);

        const isExistLinkToWAN = () => {
            return campusFabricData.nodes.core.some(item => {
                return Object.keys(item.links.to_wan).length > 0;
            });
        };

        const [linkToWANExist, setLinkToWANExist] = useState(isExistLinkToWAN());

        const [isInPodMode, setIsInPodMode] = useState(campusFabricData.nodes.pods.length !== 1);

        const [activeTabKey, setActiveTabKey] = useState(isUseCoreAsBorder ? "core" : "border");
        const {styles, attributes} = usePopper(referenceElement, popperElement, {
            placement: "bottom"
        });

        const borderSwitchTableColumns = [
            {
                title: "Switch",
                dataIndex: "sn",
                key: "sn",
                width: "25%"
            },
            {
                title: "Model",
                dataIndex: "model",
                key: "model",
                width: "25%"
            },
            {
                title: "Link to WAN",
                render: (text, record) => {
                    if (!linkToWANExist) {
                        return <div>{`${record.currentLinkToWANCount}`}</div>;
                    }
                    return (
                        <div
                            style={{
                                color: record.currentLinkToWANCount === record.maxLinkToWANCount ? "#2BC174" : "#F53F3F"
                            }}
                        >
                            {`${record.currentLinkToWANCount}/${record.maxLinkToWANCount}`}
                        </div>
                    );
                },
                key: "currentLinkToWANCount",
                width: "25%"
            },
            {
                title: "Link to Core",
                render: (text, record) => {
                    return (
                        <div
                            style={{
                                color:
                                    record.currentLinkToCoreCount === record.maxLinkToCoreCount ? "#2BC174" : "#F53F3F"
                            }}
                        >
                            {`${record.currentLinkToCoreCount}/${record.maxLinkToCoreCount}`}
                        </div>
                    );
                },
                key: "linkToCoreCount",
                width: "25%"
            }
        ];

        const coreSwitchTableColumns = [
            {
                title: "Switch",
                dataIndex: "sn",
                key: "sn",
                width: "20%"
            },
            {
                title: "Model",
                dataIndex: "model",
                key: "model",
                width: "20%"
            },
            ...generateCoreDynamicColumns(tableCoreData || [], isUseCoreAsBorder, isDisNodeExists, linkToWANExist)
        ];

        const podDisSwitchTableColumns = [
            {
                title: "Switch",
                dataIndex: "sn",
                key: "sn",
                width: "25%"
            },
            {
                title: "Model",
                dataIndex: "model",
                key: "model",
                width: "25%"
            },
            {
                title: "Link to Core",
                render: (text, record) => {
                    return (
                        <div
                            style={{
                                color:
                                    record.currentLinkToCoreCount === record.maxLinkToCoreCount ? "#2BC174" : "#F53F3F"
                            }}
                        >
                            {`${record.currentLinkToCoreCount}/${record.maxLinkToCoreCount}`}
                        </div>
                    );
                },
                key: "linkToCoreCount",
                width: "25%"
            },
            {
                title: "Link to Access",
                render: (text, record) => {
                    return (
                        <div
                            style={{
                                color:
                                    record.currentLinkToAccessCount === record.maxLinkToAccessCount
                                        ? "#2BC174"
                                        : "#F53F3F"
                            }}
                        >
                            {`${record.currentLinkToAccessCount}/${record.maxLinkToAccessCount}`}
                        </div>
                    );
                },
                key: "linkToAccessCount",
                width: "25%"
            }
        ];

        const podAccessSwitchTableColumns = [
            {
                title: "Switch",
                dataIndex: "sn",
                key: "sn",
                width: "33%"
            },
            {
                title: "Model",
                dataIndex: "model",
                key: "model",
                width: "33%"
            },
            ...generatePodAccessDynamicColumns(tablePodAccessData || [], isDisNodeExists)
        ];

        useImperativeHandle(ref, () => ({
            validate: () => {
                const borderTabValidateResult = borderTabValidate();
                const coreTabValidateResult = coreTabValidate();
                const podTabValidateResult = podTabValidate();

                let isBorderTabValid = true;
                let isCoreTabValid = true;
                let isPodTabValid = true;

                if (Object.keys(borderTabValidateResult).length > 0) {
                    setBorderTabExpandedRowKeys([
                        ...new Set([...borderTabExpandedRowKeys, ...Object.keys(borderTabValidateResult)])
                    ]);
                    isBorderTabValid = false;
                }
                if (Object.keys(coreTabValidateResult).length > 0) {
                    setCoreTabExpandedRowKeys([
                        ...new Set([...coreTabExpandedRowKeys, ...Object.keys(coreTabValidateResult)])
                    ]);
                    isCoreTabValid = false;
                }
                if (Object.keys(podTabValidateResult).length > 0) {
                    setPodDisTabExpandedRowKeys([
                        ...new Set([...podDisTabExpandedRowKeys, ...Object.keys(podTabValidateResult)])
                    ]);
                    setPodAccessTabExpandedRowKeys([
                        ...new Set([...podAccessTabExpandedRowKeys, ...Object.keys(podTabValidateResult)])
                    ]);
                    isPodTabValid = false;
                    const currentPodValid = Object.keys(podTabValidateResult).some(
                        key => podTabValidateResult[key]?.podName === podName
                    );

                    if (campusFabricData.nodes.pods.length !== 1 && !currentPodValid) {
                        setIsInPodMode(true);
                    }
                }

                if (activeTabKey === "border" && isBorderTabValid && (!isCoreTabValid || !isPodTabValid)) {
                    if (!isCoreTabValid) {
                        setActiveTabKey("core");
                    } else if (!isPodTabValid) {
                        setActiveTabKey("pod");
                    }
                } else if (activeTabKey === "core" && isCoreTabValid && (!isBorderTabValid || !isPodTabValid)) {
                    if (!isBorderTabValid) {
                        setActiveTabKey("border");
                    } else if (!isPodTabValid) {
                        setActiveTabKey("pod");
                    }
                } else if (activeTabKey === "pod" && isPodTabValid && (!isBorderTabValid || !isCoreTabValid)) {
                    if (!isBorderTabValid) {
                        setActiveTabKey("border");
                    } else if (!isCoreTabValid) {
                        setActiveTabKey("core");
                    }
                }
                return isCoreTabValid && isBorderTabValid && isPodTabValid;
            }
        }));

        useEffect(() => {
            if (isUseCoreAsBorder && activeTabKey === "border") {
                setActiveTabKey("core");
            }
        }, [isUseCoreAsBorder, activeTabKey]);

        useEffect(() => {
            // init table data
            const borderOriginData = campusFabricData.nodes.border;
            const coreOriginData = campusFabricData.nodes.core;
            const podDisOriginData = campusFabricData.nodes.pods.flatMap(pod => pod.distribution);
            const podAccessOriginData = campusFabricData.nodes.pods.flatMap(pod => pod.access);
            const podNames = campusFabricData.nodes.pods.flatMap(pod => pod.podName);

            if (campusFabricData.nodes.border.length === 0) {
                setIsUseCoreAsBorder(true);
            }

            if (campusFabricData.nodes.pods.flatMap(pod => pod.distribution).length !== 0) {
                setIsDisNodeExists(true);
            }

            const coreData = [];
            const borderData = [];
            const podDisData = [];
            const podAccessData = [];

            for (let i = 0; i < borderOriginData.length; i++) {
                const item = borderOriginData[i];
                if (item.links === undefined) {
                    item.links = {
                        to_core: item.links?.to_core ? item.links?.to_core : {},
                        to_wan: item.links?.to_wan ? item.links?.to_wan : {}
                    };
                }
                borderData.push({
                    key: item.switch_sn,
                    sn: item.switch_sn,
                    model: item.model,
                    currentLinkToCoreCount: item.links.to_core ? Object.keys(item.links.to_core).length : 0,
                    currentLinkToWANCount: item.links.to_wan ? Object.keys(item.links.to_wan).length : 0,
                    maxLinkToCoreCount: coreMaxLinkCount, // 连接数量不能超过core里面最大节点数目
                    maxLinkToWANCount: 1,
                    currentLinkToCore: item.links.to_core,
                    currentLinkToWAN: item.links.to_wan,
                    isRowExpanded: false
                });
            }
            for (let i = 0; i < coreOriginData.length; i++) {
                const item = coreOriginData[i];
                if (item.links === undefined) {
                    item.links = {
                        to_border: item.links?.to_border ? item.links?.to_border : {},
                        to_wan: item.links?.to_wan ? item.links?.to_wan : {},
                        to_dis: item.links?.to_dis ? item.links?.to_border : {}, // dis存在
                        to_access: item.links?.to_access ? item.links?.to_access : {} // dis不存在
                    };
                }
                coreData.push({
                    key: item.switch_sn,
                    sn: item.switch_sn,
                    model: item.model,
                    maxLinkToBorderCount: borderMaxLinkCount,
                    maxLinkToWANCount: 1,
                    maxLinkToDisCount: podDisMaxLinkCount,
                    maxLinkToAccessCount: podAccessMaxLinkCount,
                    maxLinkToCoreCount: 2,
                    currentLinkToBorderCount: item.links.to_border ? Object.keys(item.links.to_border).length : 0,
                    currentLinkToWANCount: item.links.to_wan ? Object.keys(item.links.to_wan).length : 0,
                    currentLinkToDisCount: item.links.to_dis ? Object.keys(item.links.to_dis).length : 0,
                    currentLinkToAccessCount: item.links.to_access ? Object.keys(item.links.to_access).length : 0,
                    currentLinkToCoreCount: item.links.to_core ? Object.keys(item.links.to_core).length : 0,
                    currentLinkToBorder: item.links.to_border,
                    currentLinkToWAN: item.links.to_wan,
                    currentLinkToDis: item.links.to_dis,
                    currentLinkToAccess: item.links.to_access,
                    currentLinkToCore: item.links.to_core,
                    isRowExpanded: false
                });
            }
            for (let i = 0; i < podDisOriginData.length; i++) {
                const item = podDisOriginData[i];
                const podName = podNames.find(podName =>
                    campusFabricData.nodes.pods.some(pod => pod.distribution.includes(item) && pod.podName === podName)
                );
                if (item.links === undefined) {
                    item.links = {
                        to_core: item.links?.to_core ? item.links?.to_core : {}, // 存在就连，不存在就不显示
                        to_access: item.links?.to_access ? item.links?.to_access : {} // 肯定存在
                    };
                }
                podDisData.push({
                    key: item.switch_sn,
                    sn: item.switch_sn,
                    model: item.model,
                    maxLinkToCoreCount: coreMaxLinkCount,
                    maxLinkToAccessCount: podAccessMaxLinkCount,
                    currentLinkToCoreCount: item.links.to_core ? Object.keys(item.links.to_core).length : 0,
                    currentLinkToAccessCount: item.links.to_access ? Object.keys(item.links.to_access).length : 0,
                    currentLinkToCore: item.links.to_core,
                    currentLinkToAccess: item.links.to_access,
                    podName,
                    isRowExpanded: false
                });
            }

            for (let i = 0; i < podAccessOriginData.length; i++) {
                const item = podAccessOriginData[i];
                const podName = podNames.find(podName =>
                    campusFabricData.nodes.pods.some(pod => pod.access.includes(item) && pod.podName === podName)
                );
                if (item.links === undefined) {
                    item.links = {
                        to_dis: item.links?.to_dis ? item.links?.to_dis : {},
                        to_core: item.links?.to_core ? item.links?.to_core : {}
                    };
                }
                podAccessData.push({
                    key: item.switch_sn,
                    sn: item.switch_sn,
                    model: item.model,
                    maxLinkToDisCount: podDisMaxLinkCount,
                    maxLinkToCoreCount: coreMaxLinkCount,
                    currentLinkToDisCount: item.links.to_dis ? Object.keys(item.links.to_dis).length : 0,
                    currentLinkToCoreCount: item.links.to_core ? Object.keys(item.links.to_core).length : 0,
                    currentLinkToDis: item.links.to_dis,
                    currentLinkToCore: item.links.to_core,
                    podName,
                    isRowExpanded: false
                });
            }

            setTableBorderData(borderData);
            setTableCoreData(coreData);
            setTablePodDisData(
                podDisData.map(item => {
                    item.podType = "distribution";
                    return item;
                })
            );
            setTablePodAccessData(
                podAccessData.map(item => {
                    item.podType = "access";
                    return item;
                })
            );

            setPodName(podNames);

            document.addEventListener("click", hideSelectedPortCardCallback);
            const getCardOffsetInterval = setInterval(() => {
                if (document.querySelectorAll(".ant-menu-inline-collapsed").length > 0) {
                    setIsMenuCollapsed(true);
                } else {
                    setIsMenuCollapsed(false);
                }
            }, 1000);
            document.addEventListener("wheel", hideSelectedPortCardCallback);

            return () => {
                document.removeEventListener("click", hideSelectedPortCardCallback);
                clearInterval(getCardOffsetInterval);
                document.removeEventListener("wheel", hideSelectedPortCardCallback);
            };
        }, []);

        const checkLinkToWANExist = tableData => {
            const allLinksEmpty = tableData.every(item => Object.keys(item.currentLinkToWAN).length === 0);

            if (allLinksEmpty) {
                setLinkToWANExist(false);
            } else {
                setLinkToWANExist(true);
            }
        };

        // 设备连接数量
        const getBorderMaxLinkToCoreCount = () => {
            return campusFabricData.nodes.border.length;
        };

        const getCoreMaxLinkCount = () => {
            // core可以连dis & acc，也可以连border & dis/acc
            return campusFabricData.nodes.core.length;
        };

        const getPodDisMaxLinkCount = () => {
            // dis存在，连 core & acc
            return campusFabricData.nodes.pods.flatMap(pod => pod.distribution).length;
        };

        const getPodAccessMaxLinkCount = () => {
            // access可以连dis/core
            return campusFabricData.nodes.pods.flatMap(pod => pod.access).length;
        };

        const borderMaxLinkCount = getBorderMaxLinkToCoreCount();
        const coreMaxLinkCount = getCoreMaxLinkCount();
        const podDisMaxLinkCount = getPodDisMaxLinkCount();
        const podAccessMaxLinkCount = getPodAccessMaxLinkCount();

        const showSelectedPortCardCallback = (record, portName, portPosX, portPosY, selectedPortType) => {
            let selectedPortCardRef;
            if (activeTabKey === "border") {
                selectedPortCardRef = borderTabSelectedPortCardRef;
            } else if (activeTabKey === "core") {
                selectedPortCardRef = coreTabSelectedPortCardRef;
            } else if (activeTabKey === "pod") {
                if (record.currentLinkToDis === undefined && record.currentLinkToAccess && record.currentLinkToCore) {
                    selectedPortCardRef = podDisTabSelectedPortCardRef;
                } else {
                    selectedPortCardRef = podAccessTabSelectedPortCardRef;
                }
            }

            selectedPortCardRef.current.showSelectedPortCard(record, portName, portPosX, portPosY, selectedPortType);
        };

        const hideSelectedPortCardCallback = e => {
            if (
                e &&
                borderTabSelectedPortCardRef.current &&
                borderTabSelectedPortCardRef.current.isSelectedPortCardShown() &&
                !borderTabSelectedPortCardRef.current.isMouseClickOnPortCard(e)
            ) {
                borderTabSelectedPortCardRef.current.hideSelectedPortCard();
                for (const key in expandedRowRefs.current) {
                    if (expandedRowRefs.current[key].current) {
                        expandedRowRefs.current[key].current.clearAllPortsSelectedStatus();
                    }
                }
            }

            if (
                e &&
                coreTabSelectedPortCardRef.current.isSelectedPortCardShown() &&
                !coreTabSelectedPortCardRef.current.isMouseClickOnPortCard(e)
            ) {
                coreTabSelectedPortCardRef.current.hideSelectedPortCard();
                for (const key in expandedRowRefs.current) {
                    if (expandedRowRefs.current[key].current) {
                        expandedRowRefs.current[key].current.clearAllPortsSelectedStatus();
                    }
                }
            }

            if (
                e &&
                podDisTabSelectedPortCardRef.current.isSelectedPortCardShown() &&
                !podDisTabSelectedPortCardRef.current.isMouseClickOnPortCard(e)
            ) {
                podDisTabSelectedPortCardRef.current.hideSelectedPortCard();
                for (const key in expandedRowRefs.current) {
                    if (expandedRowRefs.current[key].current) {
                        expandedRowRefs.current[key].current.clearAllPortsSelectedStatus();
                    }
                }
            }

            if (
                e &&
                podAccessTabSelectedPortCardRef.current.isSelectedPortCardShown() &&
                !podAccessTabSelectedPortCardRef.current.isMouseClickOnPortCard(e)
            ) {
                podAccessTabSelectedPortCardRef.current.hideSelectedPortCard();
                for (const key in expandedRowRefs.current) {
                    if (expandedRowRefs.current[key].current) {
                        expandedRowRefs.current[key].current.clearAllPortsSelectedStatus();
                    }
                }
            }
        };

        const selectedPortCardFunc = {
            showSelectedPortCard: showSelectedPortCardCallback,
            hideSelectedPortCard: hideSelectedPortCardCallback
        };

        // border窗口下连接core时状态更新情况
        const borderTabUpdateCorePortStatusCallback = (sn, portName, selectedPortType, selectedBorderSwitchSN) => {
            const tableData = tableBorderData;
            const setTableData = setTableBorderData;
            let isPortSelectedCountSmallerThanMax = true;
            const newTableData = tableData.map(item => {
                if (item.sn === sn) {
                    if (item.currentLinkToWAN && Object.keys(item.currentLinkToWAN).includes(portName)) {
                        item.currentLinkToWAN -= 1;
                        delete item.currentLinkToWAN[portName];
                    } else if (item.currentLinkToCore && Object.keys(item.currentLinkToCore).includes(portName)) {
                        item.currentLinkToCore[portName] = {
                            port_type: selectedPortType,
                            core_sn: selectedBorderSwitchSN
                        };
                        return item;
                    }
                    if (item.currentLinkToCoreCount + 1 > item.maxLinkToCoreCount) {
                        isPortSelectedCountSmallerThanMax = false;
                    } else {
                        item.currentLinkToCoreCount += 1;
                        item.currentLinkToCore[portName] = {
                            port_type: selectedPortType,
                            core_sn: selectedBorderSwitchSN
                        };
                    }
                }
                return item;
            });
            if (!isPortSelectedCountSmallerThanMax) {
                message.error("The number of selected ports has reached the maximum limit.");
                return;
            }

            checkLinkToWANExist(isUseCoreAsBorder ? tableCoreData : tableBorderData);
            setTableData(newTableData);

            const currentCampusFabricData = JSON.parse(JSON.stringify(campusFabricData));
            currentCampusFabricData.nodes.border.map(node => {
                if (node.switch_sn === sn) {
                    node.links.to_core[portName] = {port_type: selectedPortType, core_sn: selectedBorderSwitchSN};
                }
            });
            setCampusFabricData(currentCampusFabricData);
            expandedRowRefs.current[sn].current.refreshPort(portName, "selectedToCore");
            borderTabSelectedPortCardRef.current.hideSelectedPortCard();
        };

        // border窗口下默认连接
        const borderTabRemoveCorePortStatusCallback = (sn, portName) => {
            const tableData = tableBorderData;
            const setTableData = setTableBorderData;
            const newTableData = tableData.map(item => {
                if (item.sn === sn) {
                    if (item.currentLinkToCore && Object.keys(item.currentLinkToCore).includes(portName)) {
                        item.currentLinkToCoreCount -= 1;
                        delete item.currentLinkToCore[portName];
                    }
                }
                return item;
            });
            setTableData(newTableData);
            const currentCampusFabricData = JSON.parse(JSON.stringify(campusFabricData));
            currentCampusFabricData.nodes.border.map(node => {
                if (node.switch_sn === sn) {
                    delete node.links.to_core[portName];
                }
            });
            setCampusFabricData(currentCampusFabricData);
            expandedRowRefs.current[sn].current.refreshPort(portName, "common");
            borderTabSelectedPortCardRef.current.hideSelectedPortCard();
        };

        // border窗口下连接wan时状态更新情况
        const borderTabUpdateWANPortStatusCallback = (sn, portName, selectedPortType, selectedBorderSwitchSN) => {
            const tableData = tableBorderData;
            const setTableData = setTableBorderData;
            setLinkToWANExist(true);
            let isPortSelectedCountSmallerThanMax = true;
            const newTableData = tableData.map(item => {
                if (item.sn === sn) {
                    if (item.currentLinkToCore && Object.keys(item.currentLinkToCore).includes(portName)) {
                        item.currentLinkToCore -= 1;
                        delete item.currentLinkToWAN[portName];
                    } else if (item.currentLinkToWAN && Object.keys(item.currentLinkToWAN).includes(portName)) {
                        item.currentLinkToWAN[portName] = {
                            port_type: selectedPortType,
                            wan_sn: selectedBorderSwitchSN
                        };
                        return item;
                    }
                    if (item.currentLinkToWANCount + 1 > item.maxLinkToWANCount) {
                        isPortSelectedCountSmallerThanMax = false;
                    } else {
                        item.currentLinkToWANCount += 1;
                        item.currentLinkToWAN[portName] = {
                            port_type: selectedPortType,
                            wan_sn: selectedBorderSwitchSN
                        };
                    }
                }
                return item;
            });
            if (!isPortSelectedCountSmallerThanMax) {
                message.error("The number of selected ports has reached the maximum limit.");
                return;
            }

            checkLinkToWANExist(isUseCoreAsBorder ? tableCoreData : tableBorderData);
            setTableData(newTableData);

            const currentCampusFabricData = JSON.parse(JSON.stringify(campusFabricData));
            currentCampusFabricData.nodes.border.map(node => {
                if (node.switch_sn === sn) {
                    node.links.to_wan[portName] = {port_type: selectedPortType, wan_sn: selectedBorderSwitchSN};
                }
            });
            setCampusFabricData(currentCampusFabricData);
            expandedRowRefs.current[sn].current.refreshPort(portName, "selectedToWAN");
            borderTabSelectedPortCardRef.current.hideSelectedPortCard();
        };

        // border窗口下移除wan
        const borderTabRemoveWANPortStatusCallback = (sn, portName) => {
            const tableData = tableBorderData;
            const setTableData = setTableBorderData;
            const newTableData = tableData.map(item => {
                if (item.sn === sn) {
                    if (item.currentLinkToWAN && Object.keys(item.currentLinkToWAN).includes(portName)) {
                        item.currentLinkToWANCount -= 1;
                        delete item.currentLinkToWAN[portName];
                    }
                }
                return item;
            });
            checkLinkToWANExist(isUseCoreAsBorder ? tableCoreData : tableBorderData);
            setTableData(newTableData);
            const currentCampusFabricData = JSON.parse(JSON.stringify(campusFabricData));
            currentCampusFabricData.nodes.core.map(node => {
                if (node.switch_sn === sn) {
                    delete node.links.to_wan[portName];
                }
            });
            setCampusFabricData(currentCampusFabricData);
            expandedRowRefs.current[sn].current.refreshPort(portName, "common");
            coreTabSelectedPortCardRef.current.hideSelectedPortCard();
        };

        // core窗口一定连接core
        const coreTabUpdateCorePortStatusCallback = (sn, portName, selectedPortType, selectedCoreSwitchSN) => {
            const tableData = tableCoreData;
            const setTableData = setTableCoreData;
            let isPortSelectedCountSmallerThanMax = true;
            const newTableData = tableData.map(item => {
                if (item.sn === sn) {
                    if (item.currentLinkToAccess && Object.keys(item.currentLinkToAccess).includes(portName)) {
                        item.currentLinkToAccessCount -= 1;
                        delete item.currentLinkToAccess[portName];
                    } else if (item.currentLinkToWAN && Object.keys(item.currentLinkToWAN).includes(portName)) {
                        item.currentLinkToWANCount -= 1;
                        delete item.currentLinkToWAN[portName];
                    } else if (item.currentLinkToBorder && Object.keys(item.currentLinkToBorder).includes(portName)) {
                        item.currentLinkToBorderCount -= 1;
                        delete item.currentLinkToBorder[portName];
                    } else if (item.currentLinkToDis && Object.keys(item.currentLinkToDis).includes(portName)) {
                        item.currentLinkToDisCount -= 1;
                        delete item.currentLinkToDis[portName];
                    } else if (item.currentLinkToCore && Object.keys(item.currentLinkToCore).includes(portName)) {
                        item.currentLinkToCore[portName] = {port_type: selectedPortType, core_sn: selectedCoreSwitchSN};
                        return item;
                    }
                    if (item.currentLinkToCoreCount + 1 > item.maxLinkToCoreCount) {
                        isPortSelectedCountSmallerThanMax = false;
                    } else {
                        item.currentLinkToCoreCount += 1;
                        item.currentLinkToCore[portName] = {port_type: selectedPortType, core_sn: selectedCoreSwitchSN};
                    }
                }
                return item;
            });
            if (!isPortSelectedCountSmallerThanMax) {
                message.error("The number of selected ports has reached the maximum limit.");
                return;
            }
            checkLinkToWANExist(isUseCoreAsBorder ? tableCoreData : tableBorderData);
            setTableData(newTableData);
            const currentCampusFabricData = JSON.parse(JSON.stringify(campusFabricData));
            currentCampusFabricData.nodes.core.map(node => {
                if (node.switch_sn === sn) {
                    node.links.to_core[portName] = {port_type: selectedPortType, core_sn: selectedCoreSwitchSN};
                }
            });
            setCampusFabricData(currentCampusFabricData);
            expandedRowRefs.current[sn].current.refreshPort(portName, "selectedToCore");
            coreTabSelectedPortCardRef.current.hideSelectedPortCard();
        };

        const coreTabRemoveCorePortStatusCallback = (sn, portName) => {
            const tableData = tableCoreData;
            const setTableData = setTableCoreData;
            const newTableData = tableData.map(item => {
                if (item.sn === sn) {
                    if (item.currentLinkToCore && Object.keys(item.currentLinkToCore).includes(portName)) {
                        item.currentLinkToCoreCount -= 1;
                        delete item.currentLinkToCore[portName];
                    }
                }
                return item;
            });
            setTableData(newTableData);
            const currentCampusFabricData = JSON.parse(JSON.stringify(campusFabricData));
            currentCampusFabricData.nodes.core.map(node => {
                if (node.switch_sn === sn) {
                    delete node.links.to_core[portName];
                }
            });
            setCampusFabricData(currentCampusFabricData);
            expandedRowRefs.current[sn].current.refreshPort(portName, "common");
            coreTabSelectedPortCardRef.current.hideSelectedPortCard();
        };

        // core窗口下与border连接时状态更新情况（core没有成为border)，存在link to border，包括border_sn
        const coreTabUpdateBorderPortStatusCallback = (sn, portName, selectedPortType, selectedBorderSwitchSN) => {
            const tableData = tableCoreData;
            const setTableData = setTableCoreData;
            let isPortSelectedCountSmallerThanMax = true;
            const newTableData = tableData.map(item => {
                if (item.sn === sn) {
                    if (item.currentLinkToDis && Object.keys(item.currentLinkToDis).includes(portName)) {
                        item.currentLinkToDisCount -= 1;
                        delete item.currentLinkToDis[portName];
                    } else if (item.currentLinkToAccess && Object.keys(item.currentLinkToAccess).includes(portName)) {
                        item.currentLinkToAccessCount -= 1;
                        delete item.currentLinkToAccess[portName];
                    } else if (item.currentLinkToWAN && Object.keys(item.currentLinkToWAN).includes(portName)) {
                        item.currentLinkToWANCount -= 1;
                        delete item.currentLinkToWAN[portName];
                    } else if (item.currentLinkToCore && Object.keys(item.currentLinkToCore).includes(portName)) {
                        item.currentLinkToCoreCount -= 1;
                        delete item.currentLinkToCore[portName];
                    } else if (item.currentLinkToBorder && Object.keys(item.currentLinkToBorder).includes(portName)) {
                        item.currentLinkToBorder[portName] = {
                            port_type: selectedPortType,
                            border_sn: selectedBorderSwitchSN
                        };
                        return item;
                    }
                    // 当前core连border总数如果超了
                    if (item.currentLinkToBorderCount + 1 > item.maxLinkToBorderCount) {
                        isPortSelectedCountSmallerThanMax = false;
                    } else {
                        item.currentLinkToBorderCount += 1;
                        item.currentLinkToBorder[portName] = {
                            port_type: selectedPortType,
                            border_sn: selectedBorderSwitchSN
                        };
                    }
                }
                return item;
            });
            if (!isPortSelectedCountSmallerThanMax) {
                message.error("The number of selected ports has reached the maximum limit.");
                return;
            }
            setTableData(newTableData);
            const currentCampusFabricData = JSON.parse(JSON.stringify(campusFabricData));
            currentCampusFabricData.nodes.core.map(node => {
                if (node.switch_sn === sn) {
                    node.links.to_border[portName] = {port_type: selectedPortType, border_sn: selectedBorderSwitchSN};
                }
            });
            setCampusFabricData(currentCampusFabricData);
            expandedRowRefs.current[sn].current.refreshPort(portName, "selectedToBorder");
            coreTabSelectedPortCardRef.current.hideSelectedPortCard();
        };

        // core窗口下移除border
        const coreTabRemoveBorderPortStatusCallback = (sn, portName) => {
            const tableData = tableCoreData;
            const setTableData = setTableCoreData;
            const newTableData = tableData.map(item => {
                if (item.sn === sn) {
                    if (item.currentLinkToBorder && Object.keys(item.currentLinkToBorder).includes(portName)) {
                        item.currentLinkToBorderCount -= 1;
                        delete item.currentLinkToBorder[portName];
                    }
                }
                return item;
            });
            setTableData(newTableData);
            const currentCampusFabricData = JSON.parse(JSON.stringify(campusFabricData));
            currentCampusFabricData.nodes.core.map(node => {
                if (node.switch_sn === sn) {
                    delete node.links.to_border[portName];
                }
            });
            setCampusFabricData(currentCampusFabricData);
            expandedRowRefs.current[sn].current.refreshPort(portName, "common");
            coreTabSelectedPortCardRef.current.hideSelectedPortCard();
        };

        // core与wan连接时（border不存在），存在link to wan
        const coreTabUpdateWANPortStatusCallback = (sn, portName, selectedPortType, selectedDisSwitchSN) => {
            const tableData = tableCoreData;
            const setTableData = setTableCoreData;
            setLinkToWANExist(true);
            let isPortSelectedCountSmallerThanMax = true;
            const newTableData = tableData.map(item => {
                if (item.sn === sn) {
                    if (item.currentLinkToBorder && Object.keys(item.currentLinkToBorder).includes(portName)) {
                        item.currentLinkToBorderCount -= 1;
                        delete item.currentLinkToBorder[portName];
                    } else if (item.currentLinkToAccess && Object.keys(item.currentLinkToAccess).includes(portName)) {
                        item.currentLinkToAccessCount -= 1;
                        delete item.currentLinkToAccess[portName];
                    } else if (item.currentLinkToDis && Object.keys(item.currentLinkToDis).includes(portName)) {
                        item.currentLinkToDisCount -= 1;
                        delete item.currentLinkToDis[portName];
                    } else if (item.currentLinkToCore && Object.keys(item.currentLinkToCore).includes(portName)) {
                        item.currentLinkToCoreCount -= 1;
                        delete item.currentLinkToCore[portName];
                    } else if (item.currentLinkToWAN && Object.keys(item.currentLinkToWAN).includes(portName)) {
                        item.currentLinkToWAN[portName] = {port_type: selectedPortType, wan_sn: selectedDisSwitchSN};
                        return item;
                    }
                    if (item.currentLinkToWANCount + 1 > item.maxLinkToWANCount) {
                        isPortSelectedCountSmallerThanMax = false;
                    } else {
                        item.currentLinkToWANCount += 1;
                        item.currentLinkToWAN[portName] = {port_type: selectedPortType, wan_sn: selectedDisSwitchSN};
                    }
                }
                return item;
            });
            if (!isPortSelectedCountSmallerThanMax) {
                message.error("The number of selected ports has reached the maximum limit.");
                return;
            }
            checkLinkToWANExist(isUseCoreAsBorder ? tableCoreData : tableBorderData);
            setTableData(newTableData);
            const currentCampusFabricData = JSON.parse(JSON.stringify(campusFabricData));
            currentCampusFabricData.nodes.core.map(node => {
                if (node.switch_sn === sn) {
                    node.links.to_wan[portName] = {port_type: selectedPortType, wan_sn: selectedDisSwitchSN};
                }
            });
            setCampusFabricData(currentCampusFabricData);
            expandedRowRefs.current[sn].current.refreshPort(portName, "selectedToWAN");
            coreTabSelectedPortCardRef.current.hideSelectedPortCard();
        };

        //  core窗口下移除wan
        const coreTabRemoveWANPortStatusCallback = (sn, portName) => {
            const tableData = tableCoreData;
            const setTableData = setTableCoreData;
            const newTableData = tableData.map(item => {
                if (item.sn === sn) {
                    if (item.currentLinkToWAN && Object.keys(item.currentLinkToWAN).includes(portName)) {
                        item.currentLinkToWANCount -= 1;
                        delete item.currentLinkToWAN[portName];
                    }
                }
                return item;
            });
            checkLinkToWANExist(isUseCoreAsBorder ? tableCoreData : tableBorderData);
            setTableData(newTableData);
            const currentCampusFabricData = JSON.parse(JSON.stringify(campusFabricData));
            currentCampusFabricData.nodes.core.map(node => {
                if (node.switch_sn === sn) {
                    delete node.links.to_wan[portName];
                }
            });
            setCampusFabricData(currentCampusFabricData);
            expandedRowRefs.current[sn].current.refreshPort(portName, "common");
            coreTabSelectedPortCardRef.current.hideSelectedPortCard();
        };

        // core与dis连接时（dis存在），存在link to distribution，包括dis_sn
        const coreTabUpdateDisPortStatusCallback = (sn, portName, selectedPortType, selectedDisSwitchSN) => {
            const tableData = tableCoreData;
            const setTableData = setTableCoreData;
            let isPortSelectedCountSmallerThanMax = true;
            const newTableData = tableData.map(item => {
                if (item.sn === sn) {
                    if (item.currentLinkToBorder && Object.keys(item.currentLinkToBorder).includes(portName)) {
                        item.currentLinkToBorderCount -= 1;
                        delete item.currentLinkToBorder[portName];
                    } else if (item.currentLinkToAccess && Object.keys(item.currentLinkToAccess).includes(portName)) {
                        item.currentLinkToAccessCount -= 1;
                        delete item.currentLinkToAccess[portName];
                    } else if (item.currentLinkToWAN && Object.keys(item.currentLinkToWAN).includes(portName)) {
                        item.currentLinkToWANCount -= 1;
                        delete item.currentLinkToWAN[portName];
                    } else if (item.currentLinkToCore && Object.keys(item.currentLinkToCore).includes(portName)) {
                        item.currentLinkToCoreCount -= 1;
                        delete item.currentLinkToCore[portName];
                    } else if (item.currentLinkToDis && Object.keys(item.currentLinkToDis).includes(portName)) {
                        item.currentLinkToDis[portName] = {port_type: selectedPortType, dis_sn: selectedDisSwitchSN};
                        return item;
                    }
                    if (item.currentLinkToDisCount + 1 > item.maxLinkToDisCount) {
                        isPortSelectedCountSmallerThanMax = false;
                    } else {
                        item.currentLinkToDisCount += 1;
                        item.currentLinkToDis[portName] = {port_type: selectedPortType, dis_sn: selectedDisSwitchSN};
                    }
                }
                return item;
            });
            if (!isPortSelectedCountSmallerThanMax) {
                message.error("The number of selected ports has reached the maximum limit.");
                return;
            }
            setTableData(newTableData);
            const currentCampusFabricData = JSON.parse(JSON.stringify(campusFabricData));
            currentCampusFabricData.nodes.core.map(node => {
                if (node.switch_sn === sn) {
                    node.links.to_dis[portName] = {port_type: selectedPortType, dis_sn: selectedDisSwitchSN};
                }
            });
            setCampusFabricData(currentCampusFabricData);
            expandedRowRefs.current[sn].current.refreshPort(portName, "selectedToDis");
            coreTabSelectedPortCardRef.current.hideSelectedPortCard();
        };

        // core窗口下移除dis
        const coreTabRemoveDisPortStatusCallback = (sn, portName) => {
            const tableData = tableCoreData;
            const setTableData = setTableCoreData;
            const newTableData = tableData.map(item => {
                if (item.sn === sn) {
                    if (item.currentLinkToDis && Object.keys(item.currentLinkToDis).includes(portName)) {
                        item.currentLinkToDisCount -= 1;
                        delete item.currentLinkToDis[portName];
                    }
                }
                return item;
            });
            setTableData(newTableData);
            const currentCampusFabricData = JSON.parse(JSON.stringify(campusFabricData));
            currentCampusFabricData.nodes.core.map(node => {
                if (node.switch_sn === sn) {
                    delete node.links.to_dis[portName];
                }
            });
            setCampusFabricData(currentCampusFabricData);
            expandedRowRefs.current[sn].current.refreshPort(portName, "common");
            coreTabSelectedPortCardRef.current.hideSelectedPortCard();
        };

        // core与access连接时（dis不存在），存在link to access，包括access_sn
        const coreTabUpdateAccessPortStatusCallback = (sn, portName, selectedPortType, selectedAccessSwitchSN) => {
            const tableData = tableCoreData;
            const setTableData = setTableCoreData;
            let isPortSelectedCountSmallerThanMax = true;
            const newTableData = tableData.map(item => {
                if (item.sn === sn) {
                    if (item.currentLinkToBorder && Object.keys(item.currentLinkToBorder).includes(portName)) {
                        item.currentLinkToBorderCount -= 1;
                        delete item.currentLinkToBorder[portName];
                    } else if (item.currentLinkToDis && Object.keys(item.currentLinkToDis).includes(portName)) {
                        item.currentLinkToDisCount -= 1;
                        delete item.currentLinkToDis[portName];
                    } else if (item.currentLinkToWAN && Object.keys(item.currentLinkToWAN).includes(portName)) {
                        item.currentLinkToWANCount -= 1;
                        delete item.currentLinkToWAN[portName];
                    } else if (item.currentLinkToCore && Object.keys(item.currentLinkToCore).includes(portName)) {
                        item.currentLinkToCoreCount -= 1;
                        delete item.currentLinkToCore[portName];
                    } else if (item.currentLinkToAccess && Object.keys(item.currentLinkToAccess).includes(portName)) {
                        item.currentLinkToAccess[portName] = {
                            port_type: selectedPortType,
                            access_sn: selectedAccessSwitchSN
                        };
                        return item;
                    }
                    if (item.currentLinkToAccessCount + 1 > item.maxLinkToAccessCount) {
                        isPortSelectedCountSmallerThanMax = false;
                    } else {
                        item.currentLinkToAccessCount += 1;
                        item.currentLinkToAccess[portName] = {
                            port_type: selectedPortType,
                            access_sn: selectedAccessSwitchSN
                        };
                    }
                }
                return item;
            });
            if (!isPortSelectedCountSmallerThanMax) {
                message.error("The number of selected ports has reached the maximum limit.");
                return;
            }
            setTableData(newTableData);
            const currentCampusFabricData = JSON.parse(JSON.stringify(campusFabricData));
            currentCampusFabricData.nodes.core.map(node => {
                if (node.switch_sn === sn) {
                    node.links.to_access[portName] = {port_type: selectedPortType, access_sn: selectedAccessSwitchSN};
                }
            });
            setCampusFabricData(currentCampusFabricData);
            expandedRowRefs.current[sn].current.refreshPort(portName, "selectedToAccess");
            coreTabSelectedPortCardRef.current.hideSelectedPortCard();
        };

        // core窗口下移除access
        const coreTabRemoveAccessPortStatusCallback = (sn, portName) => {
            const tableData = tableCoreData;
            const setTableData = setTableCoreData;
            const newTableData = tableData.map(item => {
                if (item.sn === sn) {
                    if (item.currentLinkToAccess && Object.keys(item.currentLinkToAccess).includes(portName)) {
                        item.currentLinkToAccessCount -= 1;
                        delete item.currentLinkToAccess[portName];
                    }
                }
                return item;
            });
            setTableData(newTableData);
            const currentCampusFabricData = JSON.parse(JSON.stringify(campusFabricData));
            currentCampusFabricData.nodes.core.map(node => {
                if (node.switch_sn === sn) {
                    delete node.links.to_access[portName];
                }
            });
            setCampusFabricData(currentCampusFabricData);
            expandedRowRefs.current[sn].current.refreshPort(portName, "common");
            coreTabSelectedPortCardRef.current.hideSelectedPortCard();
        };

        // pods下dis节点存在，distribution与core连接，存在link to core，包括core_sn
        const podDisTabUpdateCorePortStatusCallback = (sn, portName, selectedPortType, selectedCoreSwitchSN) => {
            const tableData = tablePodDisData;
            const setTableData = setTablePodDisData;
            let isPortSelectedCountSmallerThanMax = true;
            const newTableData = tableData.map(item => {
                if (item.sn === sn) {
                    if (item.currentLinkToAccess && Object.keys(item.currentLinkToAccess).includes(portName)) {
                        item.currentLinkToAccessCount -= 1;
                        delete item.currentLinkToAccess[portName];
                    } else if (item.currentLinkToCore && Object.keys(item.currentLinkToCore).includes(portName)) {
                        item.currentLinkToCore[portName] = {port_type: selectedPortType, core_sn: selectedCoreSwitchSN};
                        return item;
                    }
                    if (item.currentLinkToCoreCount + 1 > item.maxLinkToCoreCount) {
                        isPortSelectedCountSmallerThanMax = false;
                    } else {
                        item.currentLinkToCoreCount += 1;
                        item.currentLinkToCore[portName] = {port_type: selectedPortType, core_sn: selectedCoreSwitchSN};
                    }
                }
                return item;
            });
            if (!isPortSelectedCountSmallerThanMax) {
                message.error("The number of selected ports has reached the maximum limit.");
                return;
            }
            setTableData(newTableData);
            const currentCampusFabricData = JSON.parse(JSON.stringify(campusFabricData));
            currentCampusFabricData.nodes.pods
                .flatMap(pod => pod.distribution)
                .map(node => {
                    if (node.switch_sn === sn) {
                        node.links.to_core[portName] = {port_type: selectedPortType, core_sn: selectedCoreSwitchSN};
                    }
                });
            setCampusFabricData(currentCampusFabricData);
            expandedRowRefs.current[sn].current.refreshPort(portName, "selectedToCore");
            podDisTabSelectedPortCardRef.current.hideSelectedPortCard();
        };

        // pods下dis窗口移除core
        const podDisTabRemoveCorePortStatusCallback = (sn, portName) => {
            const tableData = tablePodDisData;
            const setTableData = setTablePodDisData;
            const newTableData = tableData.map(item => {
                if (item.sn === sn) {
                    if (item.currentLinkToCore && Object.keys(item.currentLinkToCore).includes(portName)) {
                        item.currentLinkToCoreCount -= 1;
                        delete item.currentLinkToCore[portName];
                    }
                }
                return item;
            });
            setTableData(newTableData);
            const currentCampusFabricData = JSON.parse(JSON.stringify(campusFabricData));
            currentCampusFabricData.nodes.pods
                .flatMap(pod => pod.distribution)
                .map(node => {
                    if (node.switch_sn === sn) {
                        delete node.links.to_core[portName];
                    }
                });
            setCampusFabricData(currentCampusFabricData);
            expandedRowRefs.current[sn].current.refreshPort(portName, "common");
            podDisTabSelectedPortCardRef.current.hideSelectedPortCard();
        };

        // pods下dis节点存在，distribution与access连接，存在link to access，包括access_sn
        const podDisTabUpdateAccessPortStatusCallback = (sn, portName, selectedPortType, selectedAccessSwitchSn) => {
            const tableData = tablePodDisData;
            const setTableData = setTablePodDisData;
            let isPortSelectedCountSmallerThanMax = true;
            const newTableData = tableData.map(item => {
                if (item.sn === sn) {
                    if (item.currentLinkToCore && Object.keys(item.currentLinkToCore).includes(portName)) {
                        item.currentLinkToCoreCount -= 1;
                        delete item.currentLinkToCore[portName];
                    } else if (item.currentLinkToAccess && Object.keys(item.currentLinkToAccess).includes(portName)) {
                        item.currentLinkToAccess[portName] = {
                            port_type: selectedPortType,
                            access_sn: selectedAccessSwitchSn
                        };
                        return item;
                    }
                    if (item.currentLinkToAccessCount + 1 > item.maxLinkToAccessCount) {
                        isPortSelectedCountSmallerThanMax = false;
                    } else {
                        item.currentLinkToAccessCount += 1;
                        item.currentLinkToAccess[portName] = {
                            port_type: selectedPortType,
                            access_sn: selectedAccessSwitchSn
                        };
                    }
                }
                return item;
            });
            if (!isPortSelectedCountSmallerThanMax) {
                message.error("The number of selected ports has reached the maximum limit.");
                return;
            }
            setTableData(newTableData);
            const currentCampusFabricData = JSON.parse(JSON.stringify(campusFabricData));
            currentCampusFabricData.nodes.pods
                .flatMap(pod => pod.distribution)
                .map(node => {
                    if (node.switch_sn === sn) {
                        node.links.to_access[portName] = {
                            port_type: selectedPortType,
                            access_sn: selectedAccessSwitchSn
                        };
                    }
                });
            setCampusFabricData(currentCampusFabricData);
            expandedRowRefs.current[sn].current.refreshPort(portName, "selectedToAccess");
            podDisTabSelectedPortCardRef.current.hideSelectedPortCard();
        };

        // pods下dis窗口移除access
        const podDisTabRemoveAccessPortStatusCallback = (sn, portName) => {
            const tableData = tablePodDisData;
            const setTableData = setTablePodDisData;
            const newTableData = tableData.map(item => {
                if (item.sn === sn) {
                    if (item.currentLinkToAccess && Object.keys(item.currentLinkToAccess).includes(portName)) {
                        item.currentLinkToAccessCount -= 1;
                        delete item.currentLinkToAccess[portName];
                    }
                }
                return item;
            });
            setTableData(newTableData);
            const currentCampusFabricData = JSON.parse(JSON.stringify(campusFabricData));
            currentCampusFabricData.nodes.pods
                .flatMap(pod => pod.distribution)
                .map(node => {
                    if (node.switch_sn === sn) {
                        delete node.links.to_access[portName];
                    }
                });
            setCampusFabricData(currentCampusFabricData);
            expandedRowRefs.current[sn].current.refreshPort(portName, "common");
            podDisTabSelectedPortCardRef.current.hideSelectedPortCard();
        };

        // pods下access节点（一定存在），与dis相连接（如果dis存在）
        const podAccessTabUpdateDisPortStatusCallback = (sn, portName, selectedPortType, selecteDisSwitchSN) => {
            const tableData = tablePodAccessData;
            const setTableData = setTablePodAccessData;
            let isPortSelectedCountSmallerThanMax = true;
            const newTableData = tableData.map(item => {
                if (item.sn === sn) {
                    if (item.currentLinkToDis && Object.keys(item.currentLinkToDis).includes(portName)) {
                        item.currentLinkToDis[portName] = {
                            port_type: selectedPortType,
                            dis_sn: selecteDisSwitchSN
                        };
                        return item;
                    }
                    if (item.currentLinkToDisCount + 1 > item.maxLinkToDisCount) {
                        isPortSelectedCountSmallerThanMax = false;
                    } else {
                        item.currentLinkToDisCount += 1;
                        item.currentLinkToDis[portName] = {
                            port_type: selectedPortType,
                            dis_sn: selecteDisSwitchSN
                        };
                    }
                }
                return item;
            });
            if (!isPortSelectedCountSmallerThanMax) {
                message.error("The number of selected ports has reached the maximum limit.");
                return;
            }

            setTableData(newTableData);
            const currentCampusFabricData = JSON.parse(JSON.stringify(campusFabricData));
            currentCampusFabricData.nodes.pods
                .flatMap(pod => pod.access)
                .map(node => {
                    if (node.switch_sn === sn) {
                        node.links.to_dis[portName] = {port_type: selectedPortType, dis_sn: selecteDisSwitchSN};
                    }
                });
            setCampusFabricData(currentCampusFabricData);
            expandedRowRefs.current[sn].current.refreshPort(portName, "selectedToDis");
            podAccessTabSelectedPortCardRef.current.hideSelectedPortCard();
        };

        // pods下access窗口移除dis
        const podAccessTabRemoveDisPortStatusCallback = (sn, portName) => {
            const tableData = tablePodAccessData;
            const setTableData = setTablePodAccessData;
            const newTableData = tableData.map(item => {
                if (item.sn === sn) {
                    if (item.currentLinkToDis && Object.keys(item.currentLinkToDis).includes(portName)) {
                        item.currentLinkToDisCount -= 1;
                        delete item.currentLinkToDis[portName];
                    }
                }
                return item;
            });
            setTableData(newTableData);
            const currentCampusFabricData = JSON.parse(JSON.stringify(campusFabricData));
            currentCampusFabricData.nodes.pods
                .flatMap(pod => pod.access)
                .map(node => {
                    if (node.switch_sn === sn) {
                        delete node.links.to_dis[portName];
                    }
                });
            setCampusFabricData(currentCampusFabricData);
            expandedRowRefs.current[sn].current.refreshPort(portName, "common");
            podAccessTabSelectedPortCardRef.current.hideSelectedPortCard();
        };

        // pods下access节点（一定存在），与core相连接（如果dis不存在）
        const podAccessTabUpdateCorePortStatusCallback = (sn, portName, selectedPortType, selectedCoreSwitchSN) => {
            const tableData = tablePodAccessData;
            const setTableData = setTablePodAccessData;
            let isPortSelectedCountSmallerThanMax = true;
            const newTableData = tableData.map(item => {
                if (item.sn === sn) {
                    if (item.currentLinkToCore && Object.keys(item.currentLinkToCore).includes(portName)) {
                        item.currentLinkToCore[portName] = {
                            port_type: selectedPortType,
                            core_sn: selectedCoreSwitchSN
                        };
                        return item;
                    }
                    if (item.currentLinkToCoreCount + 1 > item.maxLinkToCoreCount) {
                        isPortSelectedCountSmallerThanMax = false;
                    } else {
                        item.currentLinkToCoreCount += 1;
                        item.currentLinkToCore[portName] = {
                            port_type: selectedPortType,
                            core_sn: selectedCoreSwitchSN
                        };
                    }
                }
                return item;
            });
            if (!isPortSelectedCountSmallerThanMax) {
                message.error("The number of selected ports has reached the maximum limit.");
                return;
            }

            setTableData(newTableData);
            const currentCampusFabricData = JSON.parse(JSON.stringify(campusFabricData));
            currentCampusFabricData.nodes.pods
                .flatMap(pod => pod.access)
                .map(node => {
                    if (node.switch_sn === sn) {
                        node.links.to_core[portName] = {port_type: selectedPortType, core_sn: selectedCoreSwitchSN};
                    }
                });
            setCampusFabricData(currentCampusFabricData);
            expandedRowRefs.current[sn].current.refreshPort(portName, "selectedToCore");
            podAccessTabSelectedPortCardRef.current.hideSelectedPortCard();
        };

        // pods下access窗口移除core
        const podAccessTabRemoveCorePortStatusCallback = (sn, portName) => {
            const tableData = tablePodAccessData;
            const setTableData = setTablePodAccessData;
            const newTableData = tableData.map(item => {
                if (item.sn === sn) {
                    if (item.currentLinkToCore && Object.keys(item.currentLinkToCore).includes(portName)) {
                        item.currentLinkToCoreCount -= 1;
                        delete item.currentLinkToCore[portName];
                    }
                }
                return item;
            });
            setTableData(newTableData);
            const currentCampusFabricData = JSON.parse(JSON.stringify(campusFabricData));
            currentCampusFabricData.nodes.pods
                .flatMap(pod => pod.access)
                .map(node => {
                    if (node.switch_sn === sn) {
                        delete node.links.to_core[portName];
                    }
                });
            setCampusFabricData(currentCampusFabricData);
            expandedRowRefs.current[sn].current.refreshPort(portName, "common");
            podAccessTabSelectedPortCardRef.current.hideSelectedPortCard();
        };

        // border窗口下拉。连接core，获取core_sn
        const getCoreSwitchSNOptionsForBorder = sn => {
            campusFabricData.nodes.border.forEach(item => {
                if (item.links === undefined) {
                    item.links = {
                        to_core: item.links?.to_core ? item.links?.to_core : {}
                    };
                }
            });

            return tableCoreData.map(coreItem => {
                return {
                    value: coreItem.sn,
                    label: coreItem.sn,
                    disabled:
                        campusFabricData.nodes.border.filter(node => {
                            return node.switch_sn === sn;
                        }).length !== 0
                            ? Object.values(
                                  campusFabricData.nodes.border.filter(node => {
                                      return node.switch_sn === sn;
                                  })[0].links
                              )
                                  .map(item => {
                                      return Object.values(item);
                                  })[0]
                                  .map(i => {
                                      return i.core_sn;
                                  })
                                  .includes(coreItem.sn)
                            : false
                };
            });
        };

        // access窗口下拉。dis存在，access连dis，获取dis_sn
        const getDisSwitchSNOptionsForAccess = sn => {
            return tablePodDisData.map(disItem => {
                return {
                    value: disItem.sn,
                    label: disItem.sn,
                    disabled:
                        campusFabricData.nodes.pods
                            .flatMap(pod => pod.access)
                            .filter(node => {
                                return node.switch_sn === sn;
                            }).length !== 0
                            ? Object.values(
                                  campusFabricData.nodes.pods
                                      .flatMap(pod => pod.access)
                                      .filter(node => {
                                          return node.switch_sn === sn;
                                      })[0].links
                              )
                                  .map(item => {
                                      return Object.values(item);
                                  })[0]
                                  .map(i => {
                                      return i.dis_sn;
                                  })
                                  .includes(disItem.sn)
                            : false
                };
            });
        };

        // access窗口下拉。dis不存在。access连core，获取core_sn
        const getCoreSwitchSNOptionsForAccess = sn => {
            return tableCoreData.map(coreItem => {
                return {
                    value: coreItem.sn,
                    label: coreItem.sn,
                    disabled:
                        campusFabricData.nodes.pods
                            .flatMap(pod => pod.access)
                            .filter(node => {
                                return node.switch_sn === sn;
                            }).length !== 0
                            ? Object.values(
                                  campusFabricData.nodes.pods
                                      .flatMap(pod => pod.access)
                                      .filter(node => {
                                          return node.switch_sn === sn;
                                      })[0].links
                              )
                                  .map(item => {
                                      return Object.values(item);
                                  })[0]
                                  .map(i => {
                                      return i.core_sn;
                                  })
                                  .includes(coreItem.sn)
                            : false
                };
            });
        };

        const borderTabValidate = () => {
            const borderTabValidateResult = {};
            const coreSwitchCount = campusFabricData.nodes.core.length;
            const wanSwitchCount = campusFabricData.nodes.border.some(node => Object.keys(node.links.to_wan).length > 0)
                ? 1
                : 0;
            for (let i = 0; i < campusFabricData.nodes.border.length; i++) {
                const item = campusFabricData.nodes.border[i];
                if (Object.keys(item.links.to_wan).length !== wanSwitchCount) {
                    if (borderTabValidateResult[item.switch_sn] === undefined) {
                        borderTabValidateResult[item.switch_sn] = {};
                    }
                    borderTabValidateResult[item.switch_sn].toWANInvalid = true;
                }
                if (Object.keys(item.links.to_core).length !== coreSwitchCount) {
                    if (borderTabValidateResult[item.switch_sn] === undefined) {
                        borderTabValidateResult[item.switch_sn] = {};
                    }
                    borderTabValidateResult[item.switch_sn].toCoreInvalid = true;
                }
            }
            return borderTabValidateResult;
        };

        const coreTabValidate = () => {
            const coreTabValidateResult = {};
            const borderSwitchCount = campusFabricData.nodes.border?.length || 0;
            const disSwitchCount = campusFabricData.nodes.pods?.flatMap(pod => pod.distribution).length || 0;
            const accessSwitchCount = campusFabricData.nodes.pods?.flatMap(pod => pod.access).length || 0;
            const wanSwitchCount = campusFabricData.nodes.core.some(node => Object.keys(node.links.to_wan).length > 0)
                ? 1
                : 0;
            for (let i = 0; i < campusFabricData.nodes.core.length; i++) {
                const item = campusFabricData.nodes.core[i];
                if (Object.keys(item.links.to_core).length !== 2) {
                    if (coreTabValidateResult[item.switch_sn] === undefined) {
                        coreTabValidateResult[item.switch_sn] = {};
                    }
                    coreTabValidateResult[item.switch_sn].toCoreInvalid = true;
                }
                if (borderSwitchCount > 0 && Object.keys(item.links.to_border).length !== borderSwitchCount) {
                    if (coreTabValidateResult[item.switch_sn] === undefined) {
                        coreTabValidateResult[item.switch_sn] = {};
                    }
                    coreTabValidateResult[item.switch_sn].toBorderInvalid = true;
                }
                if (wanSwitchCount > 0 && Object.keys(item.links.to_wan).length !== wanSwitchCount) {
                    if (coreTabValidateResult[item.switch_sn] === undefined) {
                        coreTabValidateResult[item.switch_sn] = {};
                    }
                    coreTabValidateResult[item.switch_sn].toWANInvalid = true;
                }
                if (disSwitchCount > 0 && Object.keys(item.links.to_dis).length !== disSwitchCount) {
                    if (coreTabValidateResult[item.switch_sn] === undefined) {
                        coreTabValidateResult[item.switch_sn] = {};
                    }
                    coreTabValidateResult[item.switch_sn].toDisInvalid = true;
                }
                if (!disSwitchCount > 0 && Object.keys(item.links.to_access).length !== accessSwitchCount) {
                    if (coreTabValidateResult[item.switch_sn] === undefined) {
                        coreTabValidateResult[item.switch_sn] = {};
                    }
                    coreTabValidateResult[item.switch_sn].toAccessInvalid = true;
                }
            }
            return coreTabValidateResult;
        };

        const podTabValidate = () => {
            const podTabValidateResult = {};
            const {pods} = campusFabricData.nodes;
            const coreSwitchCount = campusFabricData.nodes.core.length;

            for (let i = 0; i < pods.length; i++) {
                const currentPod = pods[i];
                const podDistributions = currentPod.distribution;
                const podAccesses = currentPod.access;
                const disSwitchCount = podDistributions.length;
                const accessSwitchCount = podAccesses.length;

                // dis存在
                if (disSwitchCount > 0) {
                    // validate dis
                    for (const dis of podDistributions) {
                        // dis->core
                        if (Object.keys(dis.links.to_core).length !== coreSwitchCount) {
                            podTabValidateResult[dis.switch_sn] = {
                                ...podTabValidateResult[dis.switch_sn],
                                disToCoreInvalid: true,
                                podName: currentPod.podName
                            };
                        }
                        // dis->access
                        if (Object.keys(dis.links.to_access).length !== accessSwitchCount) {
                            podTabValidateResult[dis.switch_sn] = {
                                ...podTabValidateResult[dis.switch_sn],
                                disToAccessInvalid: true,
                                podName: currentPod.podName
                            };
                        }
                    }

                    // access->dis
                    for (const access of podAccesses) {
                        if (Object.keys(access.links.to_dis).length !== disSwitchCount) {
                            podTabValidateResult[access.switch_sn] = {
                                ...podTabValidateResult[access.switch_sn],
                                accessToDisInvalid: true,
                                podName: currentPod.podName
                            };
                        }
                    }
                }
                // no dis
                else if (accessSwitchCount > 0) {
                    // access->core
                    for (const access of podAccesses) {
                        if (Object.keys(access.links.to_core).length !== coreSwitchCount) {
                            podTabValidateResult[access.switch_sn] = {
                                ...podTabValidateResult[access.switch_sn],
                                accessToCoreInvalid: true,
                                podName: currentPod.podName
                            };
                        }
                    }
                }
            }
            return podTabValidateResult;
        };

        const updateCurrentPortConfigForTheSameModelSwitch = ({
            currentLinkToBorder,
            currentLinkToDis,
            currentLinkToAccess,
            currentLinkToCore,
            currentLinkToWAN,
            model,
            podName,
            podType
        }) => {
            const currentCampusFabricData = JSON.parse(JSON.stringify(campusFabricData));
            const currentTableCoreData = JSON.parse(JSON.stringify(tableCoreData));
            const currentTableBorderData = JSON.parse(JSON.stringify(tableBorderData));
            const currentTablePodDisData = JSON.parse(JSON.stringify(tablePodDisData));
            const currentTablePodAccessData = JSON.parse(JSON.stringify(tablePodAccessData));

            if (activeTabKey === "border") {
                currentCampusFabricData.nodes.border.map(node => {
                    if (node.model === model) {
                        node.links = {
                            to_core: JSON.parse(JSON.stringify(currentLinkToCore)),
                            to_wan: JSON.parse(JSON.stringify(currentLinkToWAN))
                        };
                    }
                });
                currentTableBorderData.map(item => {
                    if (item.model === model) {
                        item.currentLinkToCore = JSON.parse(JSON.stringify(currentLinkToCore));
                        item.currentLinkToCoreCount = Object.keys(currentLinkToCore).length;
                        item.currentLinkToWAN = JSON.parse(JSON.stringify(currentLinkToWAN));
                        item.currentLinkToWANCount = Object.keys(currentLinkToWAN).length;
                    }
                });
                setTableBorderData(currentTableBorderData);
            } else if (activeTabKey === "core") {
                currentCampusFabricData.nodes.core.map(node => {
                    if (node.model === model) {
                        node.links = {
                            to_border: JSON.parse(JSON.stringify(currentLinkToBorder)),
                            to_dis: JSON.parse(JSON.stringify(currentLinkToDis)),
                            to_access: JSON.parse(JSON.stringify(currentLinkToAccess)),
                            to_wan: JSON.parse(JSON.stringify(currentLinkToWAN)),
                            to_core: JSON.parse(JSON.stringify(currentLinkToCore))
                        };
                    }
                });
                currentTableCoreData.map(item => {
                    if (item.model === model) {
                        item.currentLinkToBorder = JSON.parse(JSON.stringify(currentLinkToBorder));
                        item.currentLinkToBorderCount = Object.keys(currentLinkToBorder).length;
                        item.currentLinkToDis = JSON.parse(JSON.stringify(currentLinkToDis));
                        item.currentLinkToDisCount = Object.keys(currentLinkToDis).length;
                        item.currentLinkToAccess = JSON.parse(JSON.stringify(currentLinkToAccess));
                        item.currentLinkToAccessCount = Object.keys(currentLinkToAccess).length;
                        item.currentLinkToWAN = JSON.parse(JSON.stringify(currentLinkToWAN));
                        item.currentLinkToWANCount = Object.keys(currentLinkToWAN).length;
                        item.currentLinkToCore = JSON.parse(JSON.stringify(currentLinkToCore));
                        item.currentLinkToCoreCount = Object.keys(currentLinkToCore).length;
                    }
                });
                setTableCoreData(currentTableCoreData);
            } else if (activeTabKey === "pod") {
                currentCampusFabricData.nodes.pods.map(pod => {
                    if (pod.podName === podName) {
                        if (podType === "distribution") {
                            pod.distribution.map(node => {
                                if (node.model === model) {
                                    node.links = {
                                        to_core: JSON.parse(JSON.stringify(currentLinkToCore)),
                                        to_access: JSON.parse(JSON.stringify(currentLinkToAccess))
                                    };
                                }
                            });

                            currentTablePodDisData.map(item => {
                                if (item.model === model) {
                                    item.currentLinkToCore = JSON.parse(JSON.stringify(currentLinkToCore));
                                    item.currentLinkToCoreCount = Object.keys(currentLinkToCore).length;
                                    item.currentLinkToAccess = JSON.parse(JSON.stringify(currentLinkToAccess));
                                    item.currentLinkToAccessCount = Object.keys(currentLinkToAccess).length;
                                }
                            });

                            setTablePodDisData(currentTablePodDisData);
                        } else if (podType === "access") {
                            pod.access.map(node => {
                                if (node.model === model) {
                                    node.links = {
                                        to_dis: isDisNodeExists ? JSON.parse(JSON.stringify(currentLinkToDis)) : {},
                                        to_core: isDisNodeExists ? {} : JSON.parse(JSON.stringify(currentLinkToCore))
                                    };
                                }
                            });

                            currentTablePodAccessData.map(item => {
                                if (item.model === model) {
                                    if (isDisNodeExists) {
                                        item.currentLinkToDis = JSON.parse(JSON.stringify(currentLinkToDis));
                                        item.currentLinkToDisCount = Object.keys(currentLinkToDis).length;
                                    } else {
                                        item.currentLinkToCore = JSON.parse(JSON.stringify(currentLinkToCore));
                                        item.currentLinkToCoreCount = Object.keys(currentLinkToCore).length;
                                    }
                                }
                            });

                            setTablePodAccessData(currentTablePodAccessData);
                        }
                    }
                });
            }

            setCampusFabricData(currentCampusFabricData);
            setTimeout(() => {
                Object.keys(expandedRowRefs.current).forEach(sn => {
                    expandedRowRefs.current[sn].current.refreshAllPortStatus();
                });
            }, 200);
        };

        const expandedRowRender = record => {
            const rowRef = React.createRef();
            expandedRowRefs.current[record.sn] = rowRef;
            return Object.keys(SwitchPortPhysicData).includes(record.model) ? (
                <SwitchPortInfoPhysicSelectPanel
                    ref={rowRef}
                    physicPortData={SwitchPortPhysicData[record.model].portData}
                    portLabelComponent={SwitchPortPhysicData[record.model].portLabelComponent}
                    linkToCorePorts={record.currentLinkToCore ? record.currentLinkToCore : {}}
                    linkToAccessPorts={record.currentLinkToAccess ? record.currentLinkToAccess : {}}
                    linkToBorderPorts={record.currentLinkToBorder ? record.currentLinkToBorder : {}}
                    linkToDisPorts={record.currentLinkToDis ? record.currentLinkToDis : {}}
                    linkToWANPorts={record.currentLinkToWAN ? record.currentLinkToWAN : {}}
                    selectedPortCardFunc={selectedPortCardFunc}
                    updateCurrentPortConfigForTheSameModelSwitch={updateCurrentPortConfigForTheSameModelSwitch}
                    record={record}
                    tabType={activeTabKey}
                    podType={record.podType}
                    podName={record.podName}
                    currentFabricTopologyType={currentFabricTopologyType}
                    isUseCoreAsBorder={isUseCoreAsBorder}
                    isDisNodeExists={isDisNodeExists}
                    linkToWANExist={linkToWANExist}
                />
            ) : (
                <SwitchPortInfoLogicSelectPanel
                    ref={rowRef}
                    physicPortInfo={allModelPhysicPortInfo[record.model]}
                    linkToCorePorts={record.currentLinkToCore ? record.currentLinkToCore : {}}
                    linkToAccessPorts={record.currentLinkToAccess ? record.currentLinkToAccess : {}}
                    linkToBorderPorts={record.currentLinkToBorder ? record.currentLinkToBorder : {}}
                    linkToDisPorts={record.currentLinkToDis ? record.currentLinkToDis : {}}
                    linkToWANPorts={record.currentLinkToWAN ? record.currentLinkToWAN : {}}
                    selectedPortCardFunc={selectedPortCardFunc}
                    updateCurrentPortConfigForTheSameModelSwitch={updateCurrentPortConfigForTheSameModelSwitch}
                    record={record}
                    tabType={activeTabKey}
                    podType={record.podType}
                    podName={record.podName}
                    currentFabricTopologyType={currentFabricTopologyType}
                    isUseCoreAsBorder={isUseCoreAsBorder}
                    isDisNodeExists={isDisNodeExists}
                    linkToWANExist={linkToWANExist}
                />
            );
        };

        const handleBorderExpand = (expanded, record) => {
            setBorderTabExpandedRowKeys(
                expanded
                    ? [...borderTabExpandedRowKeys, record.key]
                    : borderTabExpandedRowKeys.filter(key => key !== record.key)
            );
        };

        const handleCoreExpand = (expanded, record) => {
            setCoreTabExpandedRowKeys(
                expanded
                    ? [...coreTabExpandedRowKeys, record.key]
                    : coreTabExpandedRowKeys.filter(key => key !== record.key)
            );
        };

        const handlePodDisExpand = (expanded, record) => {
            setPodDisTabExpandedRowKeys(
                expanded
                    ? [...podDisTabExpandedRowKeys, record.key]
                    : podDisTabExpandedRowKeys.filter(key => key !== record.key)
            );
        };

        const handlePodAccessExpand = (expanded, record) => {
            setPodAccessTabExpandedRowKeys(
                expanded
                    ? [...podAccessTabExpandedRowKeys, record.key]
                    : podAccessTabExpandedRowKeys.filter(key => key !== record.key)
            );
        };

        const showDeviceBriefTooltipCallback = node => {
            deviceBriefTooltipRef.current.showDeviceBriefTooltip(
                {
                    "Switch Name": node.label,
                    "MAC address": node.mac_addr,
                    Model: node.model,
                    Status: node.status,
                    Site: campusFabricData.site_id,
                    SN: node.switch_sn
                },
                {type: "create", topologyType: "ip-clos"}
            );
        };

        const hideDeviceBriefTooltipCallback = () => {
            deviceBriefTooltipRef.current.hideDeviceBriefTooltip();
        };

        const isPodNodeValid = pod => {
            let isValid = true;
            for (const distribution of pod.distribution) {
                if (Object.keys(distribution.links.to_core).length !== campusFabricData.nodes.core.length) {
                    isValid = false;
                    break;
                }
                if (Object.keys(distribution.links.to_access).length !== pod.access.length) {
                    isValid = false;
                    break;
                }
            }
            for (const access of pod.access) {
                if (Object.keys(access.links.to_dis).length !== pod.distribution.length) {
                    isValid = false;
                    break;
                }
            }
            return isValid;
        };

        const viewPodNodeCallback = (podName, podDistribution, podAccess) => {
            const transformedDis = podDistribution.map(item => {
                if (item.links === undefined) {
                    item.links = {
                        to_core: item.links?.to_core ? item.links?.to_core : {},
                        to_access: item.links?.to_access ? item.links?.to_access : {}
                    };
                }

                return {
                    key: item.switch_sn,
                    sn: item.switch_sn,
                    model: item.model,
                    maxLinkToCoreCount: coreMaxLinkCount,
                    maxLinkToAccessCount: podAccess.length,
                    currentLinkToCoreCount: item.links.to_core ? Object.keys(item.links.to_core).length : 0,
                    currentLinkToAccessCount: item.links.to_access ? Object.keys(item.links.to_access).length : 0,
                    currentLinkToCore: item.links.to_core,
                    currentLinkToAccess: item.links.to_access,
                    isRowExpanded: false,
                    podName,
                    podType: "distribution"
                };
            });

            const transformedAccess = podAccess.map(item => {
                if (item.links === undefined) {
                    item.links = {
                        to_dis: item.links?.to_dis ? item.links?.to_dis : {},
                        to_core: item.links?.to_core ? item.links?.to_core : {}
                    };
                }

                return {
                    key: item.switch_sn,
                    sn: item.switch_sn,
                    model: item.model,
                    maxLinkToDisCount: podDistribution.length,
                    maxLinkToCoreCount: coreMaxLinkCount,
                    currentLinkToDisCount: item.links.to_dis ? Object.keys(item.links.to_dis).length : 0,
                    currentLinkToCoreCount: item.links.to_core ? Object.keys(item.links.to_core).length : 0,
                    currentLinkToDis: item.links.to_dis,
                    currentLinkToCore: item.links.to_core,
                    isRowExpanded: false,
                    podName,
                    podType: "access"
                };
            });

            setTablePodDisData(transformedDis);
            setTablePodAccessData(transformedAccess);
            setPodName(podName);
            setIsInPodMode(false);
        };

        const tabItems = [
            ...(isUseCoreAsBorder
                ? []
                : [
                      {
                          key: "border",
                          label: "Service Block Border",
                          children: (
                              <Table
                                  ref={borderTableRef}
                                  columns={borderSwitchTableColumns}
                                  expandable={{
                                      expandedRowRender,
                                      expandIcon: props => (
                                          <ExpandIcon
                                              {...props}
                                              preCallback={() => {
                                                  borderTabSelectedPortCardRef.current.hideSelectedPortCard();
                                              }}
                                          />
                                      ),
                                      expandedRowKeys: borderTabExpandedRowKeys,
                                      onExpand: handleBorderExpand
                                  }}
                                  pagination={false}
                                  dataSource={tableBorderData}
                              />
                          )
                      }
                  ]),
            {
                key: "core",
                label: "Core",
                children: (
                    <Table
                        ref={coreTableRef}
                        columns={coreSwitchTableColumns}
                        expandable={{
                            expandedRowRender,
                            expandIcon: props => (
                                <ExpandIcon
                                    {...props}
                                    preCallback={() => {
                                        coreTabSelectedPortCardRef.current.hideSelectedPortCard();
                                    }}
                                />
                            ),
                            expandedRowKeys: coreTabExpandedRowKeys,
                            onExpand: handleCoreExpand
                        }}
                        pagination={false}
                        dataSource={tableCoreData}
                    />
                )
            },
            {
                key: "pod",
                label: "Pod",
                children: isInPodMode ? (
                    <PodsDisplayPanel
                        pods={campusFabricData.nodes.pods}
                        showDeviceBriefTooltipCallback={showDeviceBriefTooltipCallback}
                        hideDeviceBriefTooltipCallback={hideDeviceBriefTooltipCallback}
                        viewPodNodeCallback={viewPodNodeCallback}
                        isPodNodeValid={isPodNodeValid}
                    />
                ) : (
                    <div>
                        {campusFabricData.nodes.pods.length !== 1 ? (
                            <p
                                className={style.goBack}
                                onClick={() => {
                                    setIsInPodMode(true);
                                }}
                                style={{marginBottom: "23px"}}
                            >
                                <ArrowLeftOutlined style={{marginRight: "8px"}} />
                                <span>Back</span>
                            </p>
                        ) : null}
                        <h2 style={{marginBottom: 16}}>{podName}</h2>
                        {tablePodDisData && tablePodDisData.length > 0 && (
                            <div>
                                <h3 style={{marginBottom: 16}}>Distribution</h3>
                                <Table
                                    ref={podDisTableRef}
                                    columns={podDisSwitchTableColumns}
                                    expandable={{
                                        expandedRowRender,
                                        expandIcon: props => (
                                            <ExpandIcon
                                                {...props}
                                                preCallback={() => {
                                                    podDisTabSelectedPortCardRef.current.hideSelectedPortCard();
                                                }}
                                            />
                                        ),
                                        expandedRowKeys: podDisTabExpandedRowKeys,
                                        onExpand: handlePodDisExpand
                                    }}
                                    pagination={false}
                                    dataSource={tablePodDisData}
                                />
                            </div>
                        )}
                        <h3 style={{marginBottom: 16}}>Access</h3>
                        <Table
                            ref={podAccessTableRef}
                            columns={podAccessSwitchTableColumns}
                            expandable={{
                                expandedRowRender,
                                expandIcon: props => (
                                    <ExpandIcon
                                        {...props}
                                        preCallback={() => {
                                            podAccessTabSelectedPortCardRef.current.hideSelectedPortCard();
                                        }}
                                    />
                                ),
                                expandedRowKeys: podAccessTabExpandedRowKeys,
                                onExpand: handlePodAccessExpand
                            }}
                            pagination={false}
                            dataSource={tablePodAccessData}
                        />
                    </div>
                )
            }
        ];

        return (
            <>
                <div
                    id="campusFabricStep4"
                    ref={setPopperElement}
                    style={{
                        ...styles.popper
                    }}
                    {...attributes.popper}
                >
                    <DeviceBriefTooltip ref={deviceBriefTooltipRef} />
                    {!isUseCoreAsBorder && (
                        <BorderTabSelectedPortCard
                            borderTabUpdateCorePortStatusCallback={borderTabUpdateCorePortStatusCallback}
                            borderTabRemoveCorePortStatusCallback={borderTabRemoveCorePortStatusCallback}
                            borderTabUpdateWANPortStatusCallback={borderTabUpdateWANPortStatusCallback}
                            borderTabRemoveWANPortStatusCallback={borderTabRemoveWANPortStatusCallback}
                            getCoreSwitchSNOptionsForBorder={getCoreSwitchSNOptionsForBorder}
                            ref={borderTabSelectedPortCardRef}
                            isMenuCollapsed={isMenuCollapsed}
                        />
                    )}
                    <CoreTabSelectedPortCard
                        coreTabUpdateBorderPortStatusCallback={coreTabUpdateBorderPortStatusCallback}
                        coreTabRemoveBorderPortStatusCallback={coreTabRemoveBorderPortStatusCallback}
                        coreTabUpdateDisPortStatusCallback={coreTabUpdateDisPortStatusCallback}
                        coreTabRemoveDisPortStatusCallback={coreTabRemoveDisPortStatusCallback}
                        coreTabUpdateWANPortStatusCallback={coreTabUpdateWANPortStatusCallback}
                        coreTabRemoveWANPortStatusCallback={coreTabRemoveWANPortStatusCallback}
                        coreTabUpdateAccessPortStatusCallback={coreTabUpdateAccessPortStatusCallback}
                        coreTabRemoveAccessPortStatusCallback={coreTabRemoveAccessPortStatusCallback}
                        coreTabUpdateCorePortStatusCallback={coreTabUpdateCorePortStatusCallback}
                        coreTabRemoveCorePortStatusCallback={coreTabRemoveCorePortStatusCallback}
                        isUseCoreAsBorder={isUseCoreAsBorder}
                        isDisNodeExists={isDisNodeExists}
                        ref={coreTabSelectedPortCardRef}
                        isMenuCollapsed={isMenuCollapsed}
                    />
                    <PodDisTabSelectedPortCard
                        podDisTabUpdateCorePortStatusCallback={podDisTabUpdateCorePortStatusCallback}
                        podDisTabRemoveCorePortStatusCallback={podDisTabRemoveCorePortStatusCallback}
                        podDisTabUpdateAccessPortStatusCallback={podDisTabUpdateAccessPortStatusCallback}
                        podDisTabRemoveAccessPortStatusCallback={podDisTabRemoveAccessPortStatusCallback}
                        ref={podDisTabSelectedPortCardRef}
                        isMenuCollapsed={isMenuCollapsed}
                    />
                    <PodAccessTabSelectedPortCard
                        podAccessTabUpdateDisPortStatusCallback={podAccessTabUpdateDisPortStatusCallback}
                        podAccessTabRemoveDisPortStatusCallback={podAccessTabRemoveDisPortStatusCallback}
                        podAccessTabUpdateCorePortStatusCallback={podAccessTabUpdateCorePortStatusCallback}
                        podAccessTabRemoveCorePortStatusCallback={podAccessTabRemoveCorePortStatusCallback}
                        getDisSwitchSNOptionsForAccess={getDisSwitchSNOptionsForAccess}
                        getCoreSwitchSNOptionsForAccess={getCoreSwitchSNOptionsForAccess}
                        isDisNodeExists={isDisNodeExists}
                        ref={podAccessTabSelectedPortCardRef}
                        isMenuCollapsed={isMenuCollapsed}
                    />
                </div>
                <Tabs
                    style={{maxHeight: "calc(100vh - 350px)", overflow: "auto"}}
                    activeKey={activeTabKey}
                    rootClassName={style.unitComponent}
                    onChange={key => {
                        setActiveTabKey(key);
                        if (borderTabSelectedPortCardRef.current) {
                            borderTabSelectedPortCardRef.current.hideSelectedPortCard();
                        }
                        coreTabSelectedPortCardRef.current.hideSelectedPortCard();
                        podDisTabSelectedPortCardRef.current.hideSelectedPortCard();
                        podAccessTabSelectedPortCardRef.current.hideSelectedPortCard();
                    }}
                    items={tabItems}
                />
            </>
        );
    }
);

export default CampusFabricIPClosStep4;
