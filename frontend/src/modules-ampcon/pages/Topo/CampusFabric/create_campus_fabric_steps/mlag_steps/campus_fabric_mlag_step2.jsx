/* eslint-disable no-unused-expressions */
import React, {forwardRef, useEffect, useImperativeHandle, useRef, useState} from "react";
import {Card, message, Spin} from "antd";
import {Graph} from "@antv/x6";
import {register} from "@antv/x6-react-shape";
import AddDeviceModal from "@/modules-ampcon/pages/Topo/CampusFabric/modal/add_device_modal";
import DeviceBriefTooltip from "@/modules-ampcon/pages/Topo/CampusFabric/tooltip/device_brief_tooltip";
import {confirmModalAction} from "@/modules-ampcon/components/custom_modal";
import addBottomSvg from "../../resource/add.svg";
import addHoverBottomSvg from "../../resource/add_hover.svg";
import switchSvg from "../../resource/switch.svg";
import hoverSwitchSvg from "../../resource/hover_switch.svg";
import {fetchTopologyToBeAddedSwitch} from "@/modules-ampcon/apis/monitor_api";
import {getAllModelPhysicPortInfo} from "@/modules-ampcon/apis/config_api";
import ip from "ip";

const layoutCall = graph => {
    const model = graph.toJSON();
    const allNodes = model.cells.filter(cell => cell.shape !== "edge");
    const canvasHeight = graph.container.clientHeight;
    const canvasWidth = graph.container.clientWidth;
    const middleY = canvasHeight / 2;
    const middleX = canvasWidth / 2;
    const nodeHeight = 90;
    let spacing = canvasWidth < 1650 ? 90 : 150;
    let maxNodesPerRow = canvasWidth < 1200 ? 8 : 11;

    const {upperNodes, lowerNodes} = allNodes.reduce(
        (acc, node) => {
            if (node.isCore === undefined) {
                const isLower =
                    // eslint-disable-next-line no-nested-ternary
                    node.type === "access" ? true : node.type === "core" ? false : node.position?.y > middleY;
                isLower ? acc.lowerNodes.push(node) : acc.upperNodes.push(node);
            } else if (node.isCore) {
                acc.upperNodes.push(node);
            } else {
                acc.lowerNodes.push(node);
            }
            return acc;
        },
        {upperNodes: [], lowerNodes: []}
    );

    const layoutUpperNodes = nodes => {
        const imageNode = nodes.find(node => node.shape === "image");
        const otherNodes = nodes.filter(node => node.shape !== "image");
        if (nodes.length === 1 && nodes[0].shape === "image") {
            const graphNode = graph.getCellById(nodes[0].id);
            const position = {x: middleX - 30, y: middleY / 2 - 15};
            graphNode.position(position.x, position.y);
            return true;
        }

        otherNodes.forEach((node, index) => {
            const graphNode = graph.getCellById(node.id);
            const startX = middleX - (otherNodes.length * spacing) / 2;
            const x = startX + index * spacing;
            const y = middleY / 2 - 15;

            graphNode.position(x, y);
        });

        if (imageNode) {
            const graphNode = graph.getCellById(imageNode.id);
            const lastX = middleX - (otherNodes.length * spacing) / 2 + otherNodes.length * spacing;
            const lastY = middleY / 2 - 15;
            graphNode.position(lastX, lastY);
        }
    };

    const layoutLowerNodes = nodes => {
        const imageNode = nodes.find(node => node.shape === "image");
        const otherNodes = nodes.filter(node => node.shape !== "image");
        if (nodes.length === 1 && nodes[0].shape === "image") {
            const graphNode = graph.getCellById(nodes[0].id);
            const position = {x: middleX - 30, y: (canvasHeight / 4) * 3};
            graphNode.position(position.x, position.y);
            return;
        }
        const totalNodes = otherNodes.length;
        if (totalNodes === 0) return;
        let totalRows = Math.ceil(totalNodes / maxNodesPerRow);
        const startX = middleX - (Math.min(maxNodesPerRow, totalNodes) * spacing) / 2;
        const startY = (canvasHeight / 4) * 3;
        let layoutHeight = totalRows * nodeHeight;
        if (startY + layoutHeight > canvasHeight) {
            const availableHeight = canvasHeight - startY;
            const newMaxNodesPerRow = Math.min(15, Math.ceil(totalNodes / Math.floor(availableHeight / nodeHeight))); // 增加每行节点数量，并限制最大为18
            const newSpacing = spacing * (maxNodesPerRow / newMaxNodesPerRow);
            maxNodesPerRow = newMaxNodesPerRow;
            spacing = Math.max(20, newSpacing);

            totalRows = Math.ceil(totalNodes / maxNodesPerRow);
            layoutHeight = totalRows * nodeHeight;
        }

        otherNodes.forEach((node, index) => {
            const graphNode = graph.getCellById(node.id);
            const rowIndex = Math.floor(index / maxNodesPerRow);
            const y = startY + (rowIndex - Math.floor(totalRows / 2)) * nodeHeight;
            const x = startX + (index % maxNodesPerRow) * spacing;
            graphNode.position(x, y);
        });

        if (imageNode) {
            const graphNode = graph.getCellById(imageNode.id);
            const lastX = startX + (totalNodes % maxNodesPerRow) * spacing;
            const lastRow = Math.floor(totalNodes / maxNodesPerRow);
            const lastY = startY + (lastRow - Math.floor(totalRows / 2)) * nodeHeight;
            graphNode.position(lastX, lastY);
        }
    };

    layoutUpperNodes(upperNodes);
    layoutLowerNodes(lowerNodes);
    allNodes.forEach(node => {
        const graphNode = graph.getCellById(node.id);
        graphNode.position(node.x, node.y);
    });
};

const SwitchNode = ({node}) => {
    const {isHover = true, label = "PICOS"} = node?.store?.data || {};
    const displayLabel = label.length > 10 ? `${label.slice(0, 10)}...` : label;
    const nodeIcon = isHover ? <img src={hoverSwitchSvg} alt="hover switch" /> : <img src={switchSvg} alt="switch" />;

    return (
        <div
            style={{
                display: "flex",
                flexDirection: "column",
                justifyContent: "center",
                alignItems: "center",
                height: "100%",
                width: "100%",
                textAlign: "center"
            }}
        >
            {nodeIcon}
            <div style={{fontFamily: "Lato", fontWeight: 600, fontSize: "14px", color: "#212519", marginTop: "2px"}}>
                {displayLabel}
            </div>
        </div>
    );
};

const CampusFabricMLAGStep2 = forwardRef(
    ({campusFabricData, setCampusFabricData, allModelPhysicPortInfo, setAllModelPhysicPortInfo}, ref) => {
        const containerRef = useRef(null);
        const graphRef = useRef(null);
        const addDeviceModalRef = useRef(null);
        const deviceBriefTooltipRef = useRef(null);
        const [isShowSpin, setIsShowSpin] = useState(false);
        const [clickedNode, setClickedNode] = useState(null);
        const [model, setModel] = useState(null);
        let snList = [];
        let otnList = [];

        const registerCustomNode = () => {
            register({
                shape: "switch",
                width: 36,
                height: 44,
                // eslint-disable-next-line react/no-unstable-nested-components
                component: props => {
                    const isHover = props.node.store?.data?.isHover || true;
                    return <SwitchNode {...props} isHover={isHover} />;
                }
            });
        };

        const addNode = (graph, x, y, isCore, imageUrl, labelText) => {
            graph.addNode({
                x,
                y,
                width: 30,
                height: 30,
                shape: "image",
                imageUrl,
                isCore,
                attrs: {
                    image: {
                        refY: -4
                    },
                    label: {
                        text: labelText,
                        fontFamily: "Lato",
                        fontWeight: 600,
                        fontSize: "14px",
                        color: "#929A9E",
                        refY: 44
                    }
                }
            });
        };

        const clearAllLinks = updatedData => {
            const currentCampusFabricData = JSON.parse(JSON.stringify(updatedData));
            currentCampusFabricData.nodes.access.forEach(accessNode => {
                accessNode.links.to_core = {};
            });
            currentCampusFabricData.nodes.core.forEach(coreNode => {
                coreNode.links.to_core = {};
                coreNode.links.to_access = {};
                coreNode.links.to_wan = {};
            });
            return currentCampusFabricData;
        };

        const resizeGraph = graph => {
            const getBottomMostNode = () => {
                const nodes = graph.getNodes();
                let bottomNode = null;
                let maxBottom = -Infinity;
                nodes.forEach(node => {
                    const bbox = node.getBBox();
                    const nodeBottom = bbox.y + bbox.height;
                    if (nodeBottom > maxBottom) {
                        maxBottom = nodeBottom;
                        bottomNode = node;
                    }
                });
                return bottomNode;
            };

            const bottomMostNode = getBottomMostNode();
            const nodeBBox = bottomMostNode.getBBox();
            const newHeight =
                window.innerHeight < 800 ? nodeBBox.y + nodeBBox.height + 400 : nodeBBox.y + nodeBBox.height;
            containerRef.current.style.height = `${newHeight}px`;
            const minHeight = window.innerHeight - 420 || 0;
            const finalHeight = newHeight > minHeight ? newHeight + 24 : minHeight;
            containerRef.current.style.height = `${finalHeight}px`;
            graph.resize(containerRef.current?.clientWidth, finalHeight);
        };

        useEffect(() => {
            const container = containerRef.current;
            const graph = new Graph({
                container,
                width: containerRef.current?.clientWidth,
                height: containerRef.current?.clientHeight,
                grid: false,
                connector: "smooth",
                mousewheel: false,
                panning: false,
                resizing: false,
                interacting: false
            });
            graphRef.current = graph;
            registerCustomNode();

            const updateModel = () => {
                const jsonData = graph.toJSON();
                setModel(jsonData);
            };

            const canvasWidth = container.clientWidth;
            const canvasHeight = container.clientHeight;
            const centerY = canvasHeight / 2;
            const centerX = canvasWidth / 2;

            addNode(graph, centerX - 30, centerY / 2 - 15, true, addBottomSvg, "Select Switches");
            addNode(graph, centerX - 30, (centerY / 3) * 4 + 45, false, addBottomSvg, "Select Switches");

            if (campusFabricData?.nodes) {
                campusFabricData.nodes.core.forEach(node => {
                    graph.addNode({
                        ...node,
                        shape: "switch",
                        isHover: false
                    });
                });

                campusFabricData.nodes.access.forEach(node => {
                    graph.addNode({
                        ...node,
                        shape: "switch",
                        isHover: false
                    });
                });

                layoutCall(graph);
            }

            graph.on("node:click", ({node, e}) => {
                const labelText = node?.attrs?.label?.text || "";
                if (labelText === "Select Switches") {
                    addDeviceCallback(node?.store?.data?.isCore);
                    const position = node?.store?.data?.isCore;
                    setClickedNode(position);
                    return;
                }

                const {x, y, width} = node.getBBox();
                const rect = graph.container.getBoundingClientRect();
                const rightTopX = x + width,
                    rightTopY = y;
                const clickX = e.clientX - rect.left,
                    clickY = e.clientY - rect.top;
                if (
                    clickX >= rightTopX - 15 &&
                    clickX <= rightTopX + 5 &&
                    clickY >= rightTopY - 15 &&
                    clickY <= rightTopY + 5
                ) {
                    confirmModalAction("Are you sure you want to delete the switch?", () => {
                        node.remove();
                        if (node.device_type === 2) {
                            otnList = otnList.filter(otn => otn !== node.store.data.ne_name);
                        } else if (node.shape === "switch") {
                            snList = snList.filter(sn => sn !== node.store.data.switch_sn);
                        }
                        updateModel();
                        layoutCall(graph);
                        saveData(true, node.store.data.switch_sn, node.store.data.type === "core");
                    });
                }
            });

            graph.on("node:mouseenter", async ({node}) => {
                const labelText = node?.attrs?.label?.text || "";
                if (labelText === "Select Switches") {
                    node.attr("image/xlinkHref", addHoverBottomSvg);
                    node.attr("image/xlink:href", addHoverBottomSvg);
                    node.trigger("change:attrs", {node});
                    return;
                }
                node.prop("isHover", true);
                node.trigger("change:data", {node});
                deviceBriefTooltipRef.current.showDeviceBriefTooltip(
                    {
                        "Switch Name": node.store.data.label,
                        "MAC address": node.store.data.mac_addr,
                        Model: node.store.data.model,
                        Status: node.store.data.status || node.store.data.monitor_status,
                        Site: campusFabricData.site_id,
                        SN: node.store.data.switch_sn
                    },
                    {type: "create", topologyType: "mlag"}
                );
            });

            graph.on("node:mouseleave", ({node}) => {
                const labelText = node?.attrs?.label?.text || "";
                if (labelText === "Select Switches") {
                    node.attr("image/xlinkHref", addBottomSvg);
                    node.attr("image/xlink:href", addBottomSvg);
                    node.trigger("change:attrs", {node});
                    return;
                }
                deviceBriefTooltipRef.current.hideDeviceBriefTooltip();
                node.prop("isHover", false);
                node.trigger("change:data", {node});
                node.prop("store/data", {isHover: false});
            });

            const resizeObserver = new ResizeObserver(() => {
                if (graphRef.current) {
                    layoutCall(graphRef.current);
                }
            });

            resizeObserver.observe(container);

            if (!allModelPhysicPortInfo || allModelPhysicPortInfo.length === 0) {
                // 检查是否为空或数组为空
                getAllModelPhysicPortInfo().then(response => {
                    if (response.status === 200) {
                        setAllModelPhysicPortInfo(response.data);
                    } else {
                        message.error(response.info);
                    }
                });
            }

            resizeGraph(graph);

            return () => {
                if (graphRef.current) {
                    graphRef.current.dispose();
                }
            };
        }, [allModelPhysicPortInfo, setAllModelPhysicPortInfo]);

        const addDeviceCallback = nodePosition => {
            let type = "";
            const {coreNodes, accessNodes} = getNodeDistribution();
            const coreNodesCount = coreNodes.length;
            const accessNodesCount = accessNodes.length;
            const maxAccess = coreNodesCount !== 0 ? maxAccessCount() - 2 : 24;
            if (graphRef.current) {
                const model = graphRef.current.toJSON();
                const nodes = model.cells.filter(cell => cell.shape !== "edge");
                type = nodePosition ? "core" : "access";
                nodes.forEach(node => {
                    if (node.device_type === 2) {
                        otnList.push(node.ne_name);
                    } else if (node.shape === "switch") {
                        snList.push(node.switch_sn);
                    }
                });
                snList = [...new Set(snList)];
                otnList = [...new Set(otnList)];
            }
            if (type === "access" && accessNodesCount >= maxAccess) {
                message.error(`Only can select up to ${maxAccess} switches in Access.`);
                return;
            }
            addDeviceModalRef.current.showAddDeviceModal(
                snList,
                otnList,
                campusFabricData.site_id,
                type,
                accessNodesCount,
                coreNodesCount,
                maxAccess
            );
        };

        const allModelPhysicPortInfoUpperCase = Object.keys(allModelPhysicPortInfo).reduce((acc, model) => {
            acc[model.toUpperCase()] = allModelPhysicPortInfo[model];
            return acc;
        }, {});

        const maxAccessCount = () => {
            const {coreNodes} = getNodeDistribution();
            let minPortsModel = {model: null, totalPortsLength: Infinity};
            const models = coreNodes.map(coreNode => coreNode.model.toUpperCase());
            models.forEach(model => {
                const portsInfo = allModelPhysicPortInfoUpperCase[model];
                if (portsInfo) {
                    const totalPortsLength =
                        portsInfo.ge.length + portsInfo.qe.length + portsInfo.te.length + portsInfo.xe.length;
                    if (totalPortsLength < minPortsModel.totalPortsLength) {
                        minPortsModel = {model, totalPortsLength};
                    }
                } else if (minPortsModel.totalPortsLength > 0) {
                    minPortsModel = {model, totalPortsLength: 0};
                }
            });
            return minPortsModel.totalPortsLength;
        };

        const updateNodesCallback = async actions => {
            setIsShowSpin(true);
            const addIdList = [];

            for (const [id, action] of Object.entries(actions)) {
                if (action === "add") {
                    addIdList.push(id);
                }
            }

            if (addIdList.length > 0) {
                try {
                    const response = await fetchTopologyToBeAddedSwitch(addIdList);
                    if (response.status !== 200) {
                        message.error(response.info);
                        setIsShowSpin(false);
                        return;
                    }

                    const nodes = response.data;
                    const graph = graphRef.current;

                    if (graph && nodes.length > 0) {
                        const clickedNodeY = clickedNode.y || 0;

                        nodes.forEach(node => {
                            const newNode = JSON.parse(JSON.stringify(node));
                            graph.addNode({
                                shape: "switch",
                                ...newNode,
                                x: newNode.position?.x || 0,
                                y: clickedNodeY,
                                type: clickedNode ? "core" : "access",
                                isHover: false,
                                router_id: ""
                            });
                        });

                        layoutCall(graph);
                    }
                } catch (error) {
                    message.error("Failed to add nodes. Please try again.");
                }
            }

            setIsShowSpin(false);
            saveData();
        };

        const getNodeDistribution = isClearAllLinks => {
            const canvasHeight = graphRef.current.container.clientHeight;
            const middleY = canvasHeight / 2;
            const model = graphRef.current.toJSON();
            const allNodes = model.cells.filter(cell => cell.shape !== "edge");

            let {coreNodes, accessNodes} = allNodes
                .filter(node => node.shape !== "image")
                .reduce(
                    (acc, node) => {
                        const isUpper =
                            // eslint-disable-next-line no-nested-ternary
                            node.type === "core" ? true : node.type === "access" ? false : node.position?.y > middleY;
                        const nodeCopy = JSON.parse(JSON.stringify(node));
                        if (isUpper) {
                            acc.coreNodes.push(nodeCopy);
                        } else {
                            acc.accessNodes.push(nodeCopy);
                        }
                        return acc;
                    },
                    {coreNodes: [], accessNodes: []}
                );

            if (isClearAllLinks !== undefined) {
                coreNodes = coreNodes.map(node => {
                    const nodeCopy = JSON.parse(JSON.stringify(node));
                    nodeCopy.links = {to_core: {}, to_access: {}, to_wan: {}};
                    return nodeCopy;
                });
                accessNodes = accessNodes.map(node => {
                    const nodeCopy = JSON.parse(JSON.stringify(node));
                    nodeCopy.links = {to_core: {}};
                    return nodeCopy;
                });
            }

            return {coreNodes, accessNodes};
        };

        const saveData = isClearAllLinks => {
            const {coreNodes, accessNodes} = getNodeDistribution(isClearAllLinks);
            const currentCampusFabricData = JSON.parse(JSON.stringify(campusFabricData));

            let updatedData = {
                ...currentCampusFabricData,
                nodes: {
                    core: coreNodes.map(coreNode => ({
                        ...coreNode,
                        other_ip_config: [],
                        links: {
                            to_core: {},
                            to_access: {},
                            to_wan: {}
                        }
                    })),
                    access: accessNodes.map(accessNode => ({
                        ...accessNode,
                        other_ip_config: [],
                        links: {
                            to_core: {}
                        }
                    }))
                }
            };
            if (isClearAllLinks !== undefined) {
                updatedData = clearAllLinks(updatedData);
            }
            setCampusFabricData(updatedData);
        };

        const validate = () => {
            const {coreNodes, accessNodes} = getNodeDistribution();
            const coreNodesCount = coreNodes.length;
            const accessNodesCount = accessNodes.length;
            const maxAccess = coreNodesCount !== 0 ? maxAccessCount() - 2 : 24;

            if (coreNodesCount !== 2) {
                message.error("Please add two core switches.");
                return false;
            }

            if (accessNodesCount === 0) {
                message.error("Please add at least one access switch.");
                return false;
            }

            if (accessNodesCount > maxAccess) {
                message.error(
                    `The number of switches(${accessNodesCount}) in Access exceeds the maximum number of ports(${maxAccess}).Please remove ${accessNodesCount - maxAccess} switches in Access.`
                );
                return false;
            }
            distributeIPForAllNodes();
            return true;
        };

        const distributeIPForAllNodes = () => {
            const subnet = ip.cidrSubnet(campusFabricData.topology.loopback_prefix);
            let startIP = ip.toLong(subnet.networkAddress) + 1;

            const getNextIP = currentIP => {
                let nextIP = currentIP + 1;
                while ((nextIP & 0xff) === 0) {
                    nextIP++;
                }
                return nextIP;
            };

            campusFabricData.nodes.core.forEach(node => {
                node.router_id = ip.fromLong(startIP);
                startIP = getNextIP(startIP);
            });
            campusFabricData.nodes.access.forEach(node => {
                node.router_id = ip.fromLong(startIP);
                startIP = getNextIP(startIP);
            });
        };

        useImperativeHandle(ref, () => ({
            validate
        }));

        return (
            <>
                <Spin spinning={isShowSpin} tip="Loading..." fullscreen />
                <AddDeviceModal ref={addDeviceModalRef} updateNodesCallback={updateNodesCallback} />
                <DeviceBriefTooltip ref={deviceBriefTooltipRef} />
                <Card style={{position: "relative", height: "100%", width: "100%"}}>
                    <h2 style={{marginBottom: 16, marginTop: 0}}>Select Campus Fabric Nodes</h2>
                    <div
                        ref={containerRef}
                        style={{
                            backgroundColor: "#F8FAFB",
                            flex: 1,
                            minHeight: "calc(100% - 50px)",
                            minWidth: "100%",
                            overflow: "hidden",
                            position: "relative"
                        }}
                    />
                    <div
                        style={{
                            position: "absolute",
                            top: "85px",
                            left: "48px",
                            width: "121px",
                            height: "22px",
                            fontFamily: "Lato",
                            fontWeight: 700,
                            fontSize: "18px",
                            color: "#212519",
                            lineHeight: "17px",
                            textAlign: "left",
                            fontStyle: "normal",
                            textTransform: "none",
                            whiteSpace: "nowrap"
                        }}
                    >
                        Collapsed Core
                    </div>
                    <div
                        style={{
                            position: "absolute",
                            top: "calc(50% + 24px)",
                            left: "48px",
                            width: "121px",
                            height: "22px",
                            fontFamily: "Lato",
                            fontWeight: 700,
                            fontSize: "18px",
                            color: "#212519",
                            lineHeight: "17px",
                            textAlign: "left",
                            fontStyle: "normal",
                            textTransform: "none"
                        }}
                    >
                        Access
                    </div>
                    <div
                        style={{
                            position: "absolute",
                            top: "50%",
                            left: 24,
                            width: "calc(100% - 48px)",
                            borderBottom: "2px dashed #A2ACB2",
                            transform: "translateY(-50%)"
                        }}
                    />
                </Card>
            </>
        );
    }
);

export default CampusFabricMLAGStep2;
