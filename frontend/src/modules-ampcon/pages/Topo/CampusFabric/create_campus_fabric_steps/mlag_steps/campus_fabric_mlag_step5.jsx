import React, {useEffect, useMemo, useRef, useState} from "react";
import {Card, Spin} from "antd";
import {Graph} from "@antv/x6";
import {register} from "@antv/x6-react-shape";
import DeviceBriefTooltip from "@/modules-ampcon/pages/Topo/CampusFabric/tooltip/device_brief_tooltip";
import switchSvg from "../../resource/switch.svg";
import MLAGDeviceDetailedInformation from "../../tooltip/mlag_device_detailed_information";
import {calculateVertices} from "@/utils/topo_layout_utils";

const layoutCall = graph => {
    const model = graph.toJSON();
    const allNodes = model.cells.filter(cell => cell.shape !== "edge");
    const canvasHeight = graph.container.clientHeight;
    const canvasWidth = graph.container.clientWidth;
    const middleY = canvasHeight / 2;
    const middleX = canvasWidth / 2;
    const nodeHeight = 90;
    let spacing = canvasWidth < 1200 ? 90 : 150;
    let maxNodesPerRow = canvasWidth < 1200 ? 8 : 10;

    const {upperNodes, lowerNodes} = allNodes.reduce(
        (acc, node) => {
            const isUpper = node.type === "core";
            isUpper ? acc.upperNodes.push(node) : acc.lowerNodes.push(node);
            return acc;
        },
        {upperNodes: [], lowerNodes: []}
    );
    const layoutUpperNodes = nodes => {
        const imageNode = nodes.find(node => node.shape === "image");
        const otherNodes = nodes.filter(node => node.shape !== "image");
        if (nodes.length === 1 && nodes[0].shape === "image") {
            const graphNode = graph.getCellById(nodes[0].id);
            const position = {x: middleX - 30, y: middleY / 2 - 15};
            graphNode.position(position.x, position.y);
            return true;
        }

        otherNodes.forEach((node, index) => {
            const graphNode = graph.getCellById(node.id);
            const startX = middleX - (otherNodes.length * spacing) / 2;
            const x = startX + index * spacing;
            const y = middleY / 2 - 15;

            graphNode.position(x, y);
        });

        if (imageNode) {
            const graphNode = graph.getCellById(imageNode.id);
            const lastX = middleX - (otherNodes.length * spacing) / 2 + otherNodes.length * spacing;
            const lastY = middleY / 2 - 15;
            graphNode.position(lastX, lastY);
        }
    };

    const layoutLowerNodes = nodes => {
        const imageNode = nodes.find(node => node.shape === "image");
        const otherNodes = nodes.filter(node => node.shape !== "image");
        if (nodes.length === 1 && nodes[0].shape === "image") {
            const graphNode = graph.getCellById(nodes[0].id);
            const position = {x: middleX - 30, y: (canvasHeight / 4) * 3};
            graphNode.position(position.x, position.y);
            return;
        }
        const totalNodes = otherNodes.length;
        if (totalNodes === 0) return;
        let totalRows = Math.ceil(totalNodes / maxNodesPerRow);
        const startX = middleX - (Math.min(maxNodesPerRow, totalNodes) * spacing) / 2;
        const startY = (canvasHeight / 4) * 3;
        let layoutHeight = totalRows * nodeHeight;
        if (startY + layoutHeight > canvasHeight) {
            const availableHeight = canvasHeight - startY;
            const newMaxNodesPerRow = Math.min(18, Math.ceil(totalNodes / Math.floor(availableHeight / nodeHeight))); // 增加每行节点数量，并限制最大为18
            const newSpacing = spacing * (maxNodesPerRow / newMaxNodesPerRow); // 按比例减小spacing
            maxNodesPerRow = newMaxNodesPerRow;
            spacing = Math.max(20, newSpacing);

            totalRows = Math.ceil(totalNodes / maxNodesPerRow);
            layoutHeight = totalRows * nodeHeight;
        }

        otherNodes.forEach((node, index) => {
            const graphNode = graph.getCellById(node.id);
            const rowIndex = Math.floor(index / maxNodesPerRow);
            const y = startY + (rowIndex - Math.floor(totalRows / 2)) * nodeHeight;
            const x = startX + (index % maxNodesPerRow) * spacing;
            graphNode.position(x, y);
        });

        if (imageNode) {
            const graphNode = graph.getCellById(imageNode.id);
            const lastX = startX + (totalNodes % maxNodesPerRow) * spacing;
            const lastRow = Math.floor(totalNodes / maxNodesPerRow);
            const lastY = startY + (lastRow - Math.floor(totalRows / 2)) * nodeHeight;
            graphNode.position(lastX, lastY);
        }
    };

    layoutUpperNodes(upperNodes);
    layoutLowerNodes(lowerNodes);
    allNodes.forEach(node => {
        const graphNode = graph.getCellById(node.id);
        graphNode.position(node.x, node.y);
    });
};
// 自定义节点
const SwitchNode = ({node}) => {
    const {label = "PICOS"} = node?.store?.data || {};
    const displayLabel = label.length > 10 ? `${label.slice(0, 10)}...` : label;
    const getNodeIcon = useMemo(() => {
        return <img src={switchSvg} alt="switch" />;
    });

    return (
        <div
            style={{
                display: "flex",
                flexDirection: "column",
                justifyContent: "center",
                alignItems: "center",
                height: "100%",
                width: "100%",
                textAlign: "center",
                whiteSpace: "nowrap"
            }}
        >
            {getNodeIcon}
            <div
                style={{
                    fontFamily: "Lato",
                    fontWeight: 600,
                    fontSize: "14px",
                    color: "#212519",
                    marginTop: "2px",
                    whiteSpace: "nowrap"
                }}
            >
                {displayLabel}
            </div>
        </div>
    );
};

const CampusFabricMLAGStep5 = ({campusFabricData, lldpData, setCampusFabricData}) => {
    const containerRef = useRef(null);
    const [containerHeight, setContainerHeight] = useState(window.innerHeight);
    const graphRef = useRef(null);
    const deviceBriefTooltipRef = useRef(null);
    const [isShowSpin, setIsShowSpin] = useState(false);
    const deviceDetailedInformationRef = useRef(null);
    // 注册自定义节点
    const registerCustomNode = () => {
        register({
            shape: "switch",
            width: 36, // 默认宽度
            height: 44, // 默认高度
            // eslint-disable-next-line react/no-unstable-nested-components
            component: props => {
                return <SwitchNode {...props} />;
            }
        });
    };
    const updateAllEdgesVertices = () => {
        graphRef.current.getEdges().forEach(edge => {
            if (edge.id === "splitter1") return;
            const sourceNode = graphRef.current.getCellById(edge.store.data.source.cell);
            const targetNode = graphRef.current.getCellById(edge.store.data.target.cell);
            edge.setVertices(calculateVertices(sourceNode, targetNode));
        });
    };
    const handleGraphUnhighlight = () => {
        if (graphRef.current) {
            graphRef.current.getNodes().forEach(n => {
                const nodeView = graphRef.current.findViewByCell(n);
                nodeView.unhighlight(null, {
                    highlighter: {
                        name: "stroke",
                        args: {
                            padding: 5,
                            attrs: {
                                stroke: "#A2ACB2",
                                "stroke-width": 2,
                                "stroke-dasharray": "5,5",
                                transform: "translate(0, -11.5)"
                            }
                        }
                    }
                });
            });
        }
    };
    // 获取容器高度
    useEffect(() => {
        const handleResize = () => {
            setContainerHeight(window.innerHeight);
        };
        window.addEventListener("resize", handleResize);
        if (campusFabricData.topology.protocol === "OSPF") {
            setCampusFabricData({
                ...campusFabricData,
                topology: {
                    ...campusFabricData.topology,
                    as_base: ""
                }
            });
        }
        return () => {
            window.removeEventListener("resize", handleResize);
        };
    }, []);
    // 画布定义
    useEffect(() => {
        const container = containerRef.current;
        const graph = new Graph({
            container,
            width: containerRef.current?.clientWidth,
            height: containerRef.current?.clientHeight,
            grid: false,
            connector: "smooth",
            mousewheel: false,
            panning: false,
            resizing: false,
            interacting: false
        });
        graphRef.current = graph;
        registerCustomNode();
        if (campusFabricData?.nodes?.core) {
            campusFabricData?.nodes?.core.forEach(node => {
                graph.addNode({
                    ...node,
                    shape: "switch",
                    type: "core"
                });
                layoutCall(graph);
            });
        }
        if (campusFabricData?.nodes?.access) {
            campusFabricData?.nodes?.access.forEach(node => {
                graph.addNode({
                    ...node,
                    shape: "switch",
                    type: "access"
                });
                layoutCall(graph);
            });
        }
        const allSwitchPort = {
            core: {},
            access: {}
        };
        // 遍历 access 节点
        for (const accessNode of campusFabricData.nodes.access) {
            const {switch_sn, links} = accessNode;
            if (links.to_core) {
                if (!allSwitchPort.access[switch_sn]) {
                    allSwitchPort.access[switch_sn] = {to_core: []};
                }
                for (const port of Object.keys(links.to_core)) {
                    allSwitchPort.access[switch_sn].to_core.push(port);
                }
            }
        }
        for (const coreNode of campusFabricData.nodes.core) {
            const {switch_sn, links} = coreNode;
            if (!allSwitchPort.core[switch_sn]) {
                allSwitchPort.core[switch_sn] = {to_core: [], to_access: []};
            }
            if (links.to_core) {
                for (const port of Object.keys(links.to_core)) {
                    allSwitchPort.core[switch_sn].to_core.push(port);
                }
            }
            // 如果有 to_access 端口，分类到 core 的 to_access
            if (links.to_access) {
                for (const port of Object.keys(links.to_access)) {
                    allSwitchPort.core[switch_sn].to_access.push(port);
                }
            }
        }
        const allSwitchPortCopy = JSON.parse(JSON.stringify(allSwitchPort));

        for (const accessNode of campusFabricData.nodes.access) {
            // access to core
            for (const [port, core] of Object.entries(accessNode.links.to_core)) {
                const match = Object.keys(allSwitchPort.core).find(sn => sn === core.core_sn);
                if (match) {
                    const matchedData = allSwitchPort.core[match];
                    for (const corePort of matchedData.to_access) {
                        const key1 = `${accessNode.switch_sn}:${port}->${core?.core_sn}:${corePort}`;
                        const key2 = `${core?.core_sn}:${corePort}->${accessNode.switch_sn}:${port}`;
                        // 仅当 key1 或 key2 匹配成功时，才执行删除操作
                        let matched = false;
                        if (lldpData[key1] !== undefined) {
                            lldpData[key1] = true;
                            matched = true;
                        }
                        if (lldpData[key2] !== undefined) {
                            lldpData[key2] = true;
                            matched = true;
                        }
                        if (matched) {
                            // 仅匹配成功时删除端口;
                            const coreEntry = allSwitchPort.core[core?.core_sn];
                            if (coreEntry) {
                                coreEntry.to_core = coreEntry.to_core.filter(p => p !== corePort);
                                coreEntry.to_access = coreEntry.to_access.filter(p => p !== corePort);
                            }
                            const accessEntry = allSwitchPort.access[accessNode.switch_sn];
                            if (accessEntry) {
                                accessEntry.to_core = accessEntry.to_core.filter(p => p !== port);
                            }
                        }
                    }
                }
            }
        }
        for (const coreNode of campusFabricData.nodes.core) {
            // core to core
            // access里面只有type，没有sn了
            for (const [port, port_type] of Object.entries(coreNode.links.to_core)) {
                // eslint-disable-next-line guard-for-in
                for (const coreSn in allSwitchPort.core) {
                    const core = allSwitchPort.core[coreSn]; // 获取每个core对象
                    if (coreSn !== coreNode.switch_sn) {
                        for (const corePort of core.to_core) {
                            const key1 = `${coreNode.switch_sn}:${port}->${coreSn}:${corePort}`;
                            const key2 = `${coreSn}:${corePort}->${coreNode.switch_sn}:${port}`;
                            // console.log(key1);
                            let matched = false;
                            if (lldpData[key1] !== undefined) {
                                lldpData[key1] = true;
                                matched = true;
                            }
                            if (lldpData[key2] !== undefined) {
                                lldpData[key2] = true;
                                matched = true;
                            }
                            if (matched) {
                                const coreSource = allSwitchPort.core[coreNode.switch_sn];
                                if (coreSource) {
                                    coreSource.to_core = coreSource.to_core.filter(p => p !== port);
                                    coreSource.to_access = coreSource.to_access.filter(p => p !== port);
                                }
                                const coreTarget = allSwitchPort.core[coreSn];
                                if (coreTarget) {
                                    // 从 coreTarget 的 to_core 和 to_access 中删除匹配的端口
                                    coreTarget.to_core = coreTarget.to_core.filter(p => p !== corePort);
                                }
                            }
                        }
                    }
                }
            }
        }
        const drewEdge = nodes => {
            const drawnEdges = new Set();
            nodes.forEach(node => {
                const currentNode = {
                    index: -1,
                    type: null,
                    sn: null
                };
                const coreNode = campusFabricData?.nodes?.core.find(item => item.id === node.id);
                if (coreNode) {
                    currentNode.index = campusFabricData.nodes.core.findIndex(item => item.id === node.id);
                    currentNode.type = "core";
                    currentNode.sn = coreNode.switch_sn;
                }
                if (currentNode.index === -1) {
                    const accessNode = campusFabricData?.nodes?.access.find(item => item.id === node.id);
                    if (accessNode) {
                        currentNode.index = campusFabricData?.nodes?.access.findIndex(item => item.id === node.id);
                        currentNode.type = "access";
                        currentNode.sn = accessNode.switch_sn;
                    }
                }
                if (currentNode.index !== -1) {
                    if (currentNode.type === "core") {
                        const coreNodes = campusFabricData?.nodes?.core;
                        if (currentNode.index % 2 === 0) {
                            // core to core
                            const sourceId = coreNodes[currentNode.index].id;
                            const sourceSn = currentNode.sn; // core
                            const targetId = coreNodes[currentNode.index + 1].id;
                            const targetSn = coreNodes[currentNode.index + 1].switch_sn; // core
                            const edgeKey = `${sourceId}-${targetId}`;
                            const reverseEdgeKey = `${targetId}-${sourceId}`;
                            // 遍历所有交换机，找到 sourceSn 和 targetSn 对应的端口
                            const sourcePorts = allSwitchPortCopy.core[sourceSn]?.to_core || [];
                            const targetPorts = allSwitchPortCopy.core[targetSn]?.to_core || [];
                            let matchCount = 0; // 记录匹配成功的次数
                            let firstMatch = false;
                            let secondMatch = false;
                            // 遍历所有 sourcePort 和 targetPort 进行匹配
                            for (const sourcePort of sourcePorts) {
                                for (const targetPort of targetPorts) {
                                    const keyA = `${sourceSn}:${sourcePort}->${targetSn}:${targetPort}`;
                                    const keyB = `${targetSn}:${targetPort}->${sourceSn}:${sourcePort}`;
                                    if (lldpData[keyA] === true || lldpData[keyB] === true) {
                                        matchCount++;
                                        if (matchCount === 1) firstMatch = true;
                                        if (matchCount === 2) secondMatch = true;
                                    }
                                    // 如果找到两个匹配的端口对，提前终止
                                    if (matchCount >= 2) break;
                                }
                                if (matchCount >= 2) break;
                            }
                            const edgeColor1 = firstMatch ? "#2BC174" : "#D8D8D8";
                            const edgeColor2 = secondMatch ? "#2BC174" : "#D8D8D8";
                            if (!drawnEdges.has(edgeKey) && !drawnEdges.has(reverseEdgeKey)) {
                                drawnEdges.add(edgeKey);
                                drawnEdges.add(reverseEdgeKey);
                                graph.addEdge({
                                    source: {
                                        cell: sourceId,
                                        anchor: {
                                            name: "center",
                                            args: {dy: -17, dx: 25}
                                        }
                                    },
                                    target: {
                                        cell: targetId,
                                        anchor: {
                                            name: "center",
                                            args: {dy: -17, dx: -25}
                                        }
                                    },
                                    attrs: {
                                        line: {
                                            stroke: edgeColor1,
                                            strokeWidth: 1.5,
                                            targetMarker: null,
                                            style: {
                                                pointerEvents: "none"
                                            }
                                        }
                                    },
                                    zIndex: -1
                                });

                                graph.addEdge({
                                    source: {
                                        cell: sourceId,
                                        anchor: {
                                            name: "center",
                                            args: {dy: -3, dx: 25}
                                        }
                                    },
                                    target: {
                                        cell: targetId,
                                        anchor: {
                                            name: "center",
                                            args: {dy: -3, dx: -25}
                                        }
                                    },
                                    attrs: {
                                        line: {
                                            stroke: edgeColor2,
                                            strokeWidth: 1.5,
                                            targetMarker: null,
                                            style: {
                                                pointerEvents: "none"
                                            }
                                        }
                                    },
                                    zIndex: -1
                                });
                            }
                        } else {
                            // core to core
                            const sourceId = coreNodes[currentNode.index].id;
                            const sourceSn = currentNode.sn;
                            const targetId = coreNodes[currentNode.index - 1].id;
                            const targetSn = coreNodes[currentNode.index - 1].switch_sn;
                            const edgeKey = `${sourceId}-${targetId}`;
                            const reverseEdgeKey = `${targetId}-${sourceId}`;
                            const sourcePorts = allSwitchPortCopy.core[sourceSn]?.to_core || [];
                            const targetPorts = allSwitchPortCopy.core[targetSn]?.to_core || [];
                            let matchCount = 0;
                            let firstMatch = false;
                            let secondMatch = false;
                            for (const sourcePort of sourcePorts) {
                                for (const targetPort of targetPorts) {
                                    const keyA = `${sourceSn}:${sourcePort}->${targetSn}:${targetPort}`;
                                    const keyB = `${targetSn}:${targetPort}->${sourceSn}:${sourcePort}`;
                                    if (lldpData[keyA] === true || lldpData[keyB] === true) {
                                        matchCount++;
                                        if (matchCount === 1) firstMatch = true;
                                        if (matchCount === 2) secondMatch = true;
                                    }
                                    if (matchCount >= 2) break;
                                }
                                if (matchCount >= 2) break;
                            }
                            const edgeColor1 = firstMatch ? "#2BC174" : "#D8D8D8";
                            const edgeColor2 = secondMatch ? "#2BC174" : "#D8D8D8";
                            if (!drawnEdges.has(edgeKey) && !drawnEdges.has(reverseEdgeKey)) {
                                drawnEdges.add(edgeKey);
                                drawnEdges.add(reverseEdgeKey);

                                graph.addEdge({
                                    source: {
                                        cell: sourceId,
                                        anchor: {
                                            name: "center",
                                            args: {dy: -17, dx: -25}
                                        }
                                    },
                                    target: {
                                        cell: targetId,
                                        anchor: {
                                            name: "center",
                                            args: {dy: -17, dx: 25}
                                        }
                                    },
                                    attrs: {
                                        line: {
                                            stroke: edgeColor1,
                                            strokeWidth: 1.5,
                                            targetMarker: null,
                                            style: {
                                                pointerEvents: "none"
                                            }
                                        }
                                    },
                                    zIndex: -1
                                });

                                graph.addEdge({
                                    source: {
                                        cell: sourceId,
                                        anchor: {
                                            name: "center",
                                            args: {dy: -3, dx: -25}
                                        }
                                    },
                                    target: {
                                        cell: targetId,
                                        anchor: {
                                            name: "center",
                                            args: {dy: -3, dx: 25}
                                        }
                                    },
                                    attrs: {
                                        line: {
                                            stroke: edgeColor2,
                                            strokeWidth: 1.5,
                                            targetMarker: null,
                                            style: {
                                                pointerEvents: "none"
                                            }
                                        }
                                    },
                                    zIndex: -1
                                });
                            }
                        }
                        // core to access
                        const accessNodes = campusFabricData?.nodes?.access || [];
                        accessNodes.forEach(accessNode => {
                            const accessEdgeKey = `${node.id}-${accessNode.id}`;
                            const reverseAccessEdgeKey = `${accessNode.id}-${node.id}`;
                            const sourceSn = currentNode.sn;
                            const targetSn = accessNode.switch_sn;
                            const sourcePorts = allSwitchPortCopy.core[sourceSn]?.to_access || [];
                            const targetPorts = allSwitchPortCopy.access[targetSn]?.to_core || [];
                            let isMatchedAndTrue = false;
                            for (const sourcePort of sourcePorts) {
                                for (const targetPort of targetPorts) {
                                    const key1 = `${sourceSn}:${sourcePort}->${targetSn}:${targetPort}`;
                                    const key2 = `${targetSn}:${targetPort}->${sourceSn}:${sourcePort}`;
                                    if (lldpData[key1] === true || lldpData[key2] === true) {
                                        isMatchedAndTrue = true;
                                        break;
                                    }
                                }
                                if (isMatchedAndTrue) break;
                            }
                            const edgeColor = isMatchedAndTrue ? "#2BC174" : "#D8D8D8";
                            if (!drawnEdges.has(accessEdgeKey) && !drawnEdges.has(reverseAccessEdgeKey)) {
                                drawnEdges.add(accessEdgeKey);
                                drawnEdges.add(reverseAccessEdgeKey);

                                graph.addEdge({
                                    source: {cell: node.id, anchor: {name: "center", args: {dy: 35}}},
                                    target: {cell: accessNode.id, anchor: {name: "center", args: {dy: -35}}},
                                    attrs: {
                                        line: {stroke: edgeColor, strokeWidth: 1.5, targetMarker: null}
                                    },
                                    connector: {name: "smooth"},
                                    zIndex: -1
                                });
                            }
                        });
                    } else {
                        // access to core
                        const coreNodes = campusFabricData?.nodes?.core || [];
                        coreNodes.forEach(coreNode => {
                            const coreEdgeKey = `${coreNode.id}-${node.id}`;
                            const reverseCoreEdgeKey = `${node.id}-${coreNode.id}`;
                            const sourceSn = coreNode.switch_sn;
                            const targetSn = currentNode.sn;
                            const sourcePorts = allSwitchPortCopy.core[sourceSn]?.to_access || [];
                            const targetPorts = allSwitchPortCopy.access[targetSn]?.to_core || [];
                            let isMatchedAndTrue = false;
                            for (const sourcePort of sourcePorts) {
                                for (const targetPort of targetPorts) {
                                    const key1 = `${sourceSn}:${sourcePort}->${targetSn}:${targetPort}`;
                                    const key2 = `${targetSn}:${targetPort}->${sourceSn}:${sourcePort}`;
                                    if (lldpData[key1] === true || lldpData[key2] === true) {
                                        isMatchedAndTrue = true;
                                        break;
                                    }
                                }
                                if (isMatchedAndTrue) break;
                            }
                            const edgeColor = isMatchedAndTrue ? "#2BC174" : "#D8D8D8";
                            if (!drawnEdges.has(coreEdgeKey) && !drawnEdges.has(reverseCoreEdgeKey)) {
                                drawnEdges.add(coreEdgeKey);
                                drawnEdges.add(reverseCoreEdgeKey);
                                graph.addEdge({
                                    source: {cell: coreNode.id, anchor: {name: "center", args: {dy: 35}}},
                                    target: {cell: node.id, anchor: {name: "center", args: {dy: -35}}},
                                    attrs: {
                                        line: {stroke: edgeColor, strokeWidth: 1.5, targetMarker: null}
                                    },
                                    connector: {name: "smooth"},
                                    zIndex: -1
                                });
                            }
                        });
                    }
                }
            });
        };
        setTimeout(() => {
            if (campusFabricData?.nodes?.core?.length > 0) {
                const firstCoreNodeId = campusFabricData.nodes.core[0].id;
                const firstCoreNode = graph.getCellById(firstCoreNodeId);
                if (firstCoreNode) {
                    handleGraphUnhighlight();
                    const nodeView = graph.findViewByCell(firstCoreNode);
                    if (nodeView) {
                        nodeView.highlight(null, {
                            highlighter: {
                                name: "stroke",
                                args: {
                                    padding: 5,
                                    attrs: {
                                        stroke: "#A2ACB2",
                                        "stroke-width": 2,
                                        "stroke-dasharray": "5,5",
                                        transform: "translate(0, -11.5)"
                                    }
                                }
                            }
                        });
                        deviceDetailedInformationRef.current.showDeviceDetailedInformation({
                            type: undefined,
                            nodeType: firstCoreNode?.store?.data?.type,
                            name: firstCoreNode?.store?.data?.label,
                            "MAC Address": firstCoreNode?.store?.data?.mac_addr || "--",
                            Model: firstCoreNode?.store?.data?.model || "--",
                            Status: firstCoreNode?.store?.data?.status || "--",
                            Site: campusFabricData?.site_id || "--",
                            "Router ID": firstCoreNode?.store?.data?.router_id || "--",
                            sn: firstCoreNode?.store?.data?.switch_sn || "--",
                            mgtIp: firstCoreNode?.store?.data?.mgt_ip || "--",
                            vlans:
                                firstCoreNode?.store?.data?.other_ip_config?.map(item => ({
                                    id: item?.vlan_id,
                                    ipAddress: item?.ip_address || "--",
                                    name: item?.vlan_name
                                })) || [],
                            connectionsToCollapsedCore: Object.keys(
                                firstCoreNode?.store?.data?.links?.to_core ?? {}
                            ).map(portId => ({
                                switch: firstCoreNode?.store?.data?.label,
                                portId
                            })),
                            connectionsToAccess: Object.keys(firstCoreNode?.store?.data?.links?.to_access ?? {}).map(
                                portId => ({
                                    switch: firstCoreNode?.store?.data?.label,
                                    portId
                                })
                            ),
                            connectionsToWan: Object.keys(firstCoreNode?.store?.data?.links?.to_wan ?? {}).map(
                                portId => ({
                                    switch: firstCoreNode?.store?.data?.label,
                                    portId
                                })
                            )
                        });
                    }
                }
                drewEdge([campusFabricData?.nodes?.core[0]] || []);
                updateAllEdgesVertices();
            }
        }, 100);
        graph.on("node:click", ({node, e}) => {
            handleGraphUnhighlight();
            const nodeView = graph.findViewByCell(node);
            nodeView.highlight(null, {
                highlighter: {
                    name: "stroke",
                    args: {
                        padding: 5,
                        attrs: {
                            stroke: "#A2ACB2",
                            "stroke-width": 2,
                            "stroke-dasharray": "5,5",
                            transform: "translate(0, -11.5)"
                        }
                    }
                }
            });
            clearAllTooltips();
            clearAllEdges();
            drewEdge([node]);
            updateAllEdgesVertices();
            deviceDetailedInformationRef.current.showDeviceDetailedInformation({
                type: undefined,
                nodeType: node?.store?.data?.type,
                name: node?.store?.data?.label,
                "MAC Address": node?.store?.data?.mac_addr || "--",
                Model: node?.store?.data?.model || "--",
                Status: node?.store?.data?.status || "--",
                Site: campusFabricData?.site_id || "--",
                "Router ID": node?.store?.data?.router_id || "--",
                sn: node?.store?.data?.switch_sn || "--",
                mgtIp: node?.store?.data?.mgt_ip || "--",
                vlans:
                    node?.store?.data?.other_ip_config?.map(item => ({
                        id: item?.vlan_id,
                        ipAddress: item?.ip_address || "--",
                        name: item?.vlan_name
                    })) || [],
                connectionsToCollapsedCore: Object.keys(node?.store?.data?.links?.to_core ?? {}).map(portId => ({
                    switch: node?.store?.data?.label,
                    portId
                })),
                connectionsToAccess: Object.keys(node?.store?.data?.links?.to_access ?? {}).map(portId => ({
                    switch: node?.store?.data?.label,
                    portId
                })),
                connectionsToWan: Object.keys(node?.store?.data?.links?.to_wan ?? {}).map(portId => ({
                    switch: node?.store?.data?.label,
                    portId
                }))
            });
        });
        graph.on("blank:click", () => {
            clearAllEdges();
            handleGraphUnhighlight();
            deviceDetailedInformationRef.current.hideDeviceDetailedInformation();
            drewEdge(campusFabricData?.nodes?.core || []);
            updateAllEdgesVertices();
        });
        // hover事件
        graph.on("node:mouseenter", async ({node}) => {
            const labelText = node?.attrs?.label?.text || "";
            if (labelText === "Select Switches") {
                return;
            }
            node.store.data.isHover = true;
            node.setData(node.store.data);
            if (deviceBriefTooltipRef.current) {
                deviceBriefTooltipRef.current.showDeviceBriefTooltip(
                    {
                        "Switch Name": node.store.data.label,
                        "MAC Address": node.store.data.mac_addr,
                        Model: node.store.data.model,
                        Status: node.store.data.status || "--",
                        Site: campusFabricData.site_id,
                        "Router ID": node.store.data.router_id,
                        SN: node.store.data.switch_sn
                    },
                    {type: "create", topologyType: "mlag"}
                );
            }
        });
        // 清除所有边
        const clearAllEdges = () => {
            const edges = graph.getEdges();
            edges.forEach(edge => {
                graph.removeEdge(edge);
            });
        };
        // 鼠标移出节点
        graph.on("node:mouseleave", ({node}) => {
            const labelText = node?.attrs?.label?.text || "";
            if (labelText === "Select Switches") {
                return;
            }
            // 将 isEmpty 状态设置为 true
            clearAllTooltips();
            node.store.data.data.isEmpty = false;
            node.setData(node.store.data);
        });
        const resizeObserver = new ResizeObserver(async () => {
            if (graphRef.current) {
                await Promise.all([
                    layoutCall(graphRef.current), // 布局计算
                    updateAllEdgesVertices() // 更新线条
                ]);
            }
        });
        resizeObserver.observe(container);
        return () => {
            graph.dispose();
        };
    }, []);

    // 清除所有的 tooltip
    const clearAllTooltips = () => {
        if (deviceBriefTooltipRef.current) {
            deviceBriefTooltipRef.current.hideDeviceBriefTooltip();
        }
    };
    return (
        <>
            <Spin spinning={isShowSpin} tip="Loading..." fullscreen />
            <DeviceBriefTooltip ref={deviceBriefTooltipRef} />
            <Card style={{position: "relative", height: "100%", width: "100%"}}>
                <h2 style={{marginBottom: 16, marginTop: 0}}>Confirm</h2>
                <div
                    ref={containerRef}
                    style={{
                        backgroundColor: "#F8FAFB",
                        flex: 1,
                        minWidth: "100%",
                        minHeight: "calc(100% - 50px)",
                        overflow: "hidden",
                        position: "relative"
                    }}
                />
                <div
                    style={{
                        position: "absolute",
                        top: "85px",
                        left: "48px",
                        width: "121px",
                        height: "22px",
                        fontFamily: "Lato",
                        fontWeight: 700,
                        fontSize: "18px",
                        color: "#212519",
                        lineHeight: "17px",
                        textAlign: "left",
                        fontStyle: "normal",
                        textTransform: "none",
                        whiteSpace: "nowrap"
                    }}
                >
                    Collapsed Core
                </div>
                <div
                    style={{
                        position: "absolute",
                        top: "calc(50% + 48px)",
                        left: "48px",
                        width: "121px",
                        height: "22px",
                        fontFamily: "Lato",
                        fontWeight: 700,
                        fontSize: "18px",
                        color: "#212519",
                        lineHeight: "17px",
                        textAlign: "left",
                        fontStyle: "normal",
                        textTransform: "none"
                    }}
                >
                    Access
                </div>
                <div
                    style={{
                        position: "absolute",
                        top: "50%", // 使其位于页面的中间
                        left: 24,
                        width: "calc(100% - 48px)", // 横跨整个页面
                        borderBottom: "3px dashed #A2ACB2", // 设置虚线
                        transform: "translateY(-50%)" // 调整定位，使其准确居中
                    }}
                />
            </Card>
            <MLAGDeviceDetailedInformation
                ref={deviceDetailedInformationRef}
                handleGraphUnhighlight={handleGraphUnhighlight}
                containerHeight={containerHeight - 385}
            />
        </>
    );
};

export default CampusFabricMLAGStep5;
