import React, {forwardRef, useEffect, useImperative<PERSON><PERSON><PERSON>, useState, useRef} from "react";
import {Form, Divider, Input, Table, Config<PERSON><PERSON><PERSON>, Button, message} from "antd";
import ExpandIcon from "@/modules-ampcon/components/expand_icon";
import ip from "ip";
import {useUnmount} from "ahooks";

export const OtherIpConfiguration = forwardRef(
    ({networkData, setCampusFabricData, tableStyle, accessNodes, setAccessNodes, coreNodes, setCoreNodes}, ref) => {
        const [form] = Form.useForm();

        const columns = [
            {
                title: "Switch SN",
                dataIndex: "switch_sn",
                key: "switch_sn"
            },
            {
                title: "Label",
                dataIndex: "label",
                key: "label"
            }
        ];

        function saveIPconfig() {
            const ip_count_mapping = networkData.reduce((acc, network) => {
                acc[network.name] = 0;
                return acc;
            }, {});
            coreNodes.forEach(coreNode => {
                networkData.forEach(network => {
                    const firstAvailableIP = ip.cidrSubnet(network.subnet).firstAddress;
                    coreNode.other_ip_config.find(item => item.vlan_name === network.name).ip_address = ip.fromLong(
                        ip.toLong(firstAvailableIP) + ip_count_mapping[network.name]
                    );
                    ip_count_mapping[network.name]++;
                });
            });
            accessNodes.forEach(accessNode => {
                networkData.forEach(network => {
                    const firstAvailableIP = ip.cidrSubnet(network.subnet).firstAddress;
                    accessNode.other_ip_config.find(item => item.vlan_name === network.name).ip_address = ip.fromLong(
                        ip.toLong(firstAvailableIP) + ip_count_mapping[network.name]
                    );
                    ip_count_mapping[network.name]++;
                });
            });
            setCampusFabricData(prev => {
                return {
                    ...prev,
                    nodes: {
                        ...prev.nodes,
                        border: prev.nodes.border.map(borderNode => {
                            return {
                                ...borderNode,
                                other_ip_config: networkData.map(item => {
                                    return {
                                        vlan_name: item.name,
                                        vlan_id: item.vlan_id,
                                        ip_address: ""
                                    };
                                })
                            };
                        }),
                        core: coreNodes,
                        pods: prev.nodes.pods?.map(pod => {
                            return {
                                ...pod,
                                access: pod.access.map(accessNode => {
                                    const node = accessNodes?.find(item => item.switch_sn === accessNode.switch_sn);
                                    return {
                                        ...accessNode,
                                        other_ip_config: node.other_ip_config
                                    };
                                }),
                                distribution: pod.distribution.map(distributionNode => {
                                    return {
                                        ...distributionNode,
                                        other_ip_config: networkData.map(item => {
                                            return {
                                                vlan_name: item.name,
                                                vlan_id: item.vlan_id,
                                                ip_address: ""
                                            };
                                        })
                                    };
                                })
                            };
                        })
                    }
                };
            });
        }

        useUnmount(() => {
            if (document.querySelector("#campusfabric-footer")) {
                saveIPconfig();
            }
        });

        useImperativeHandle(ref, () => ({
            addNetworkPostAction: (vlan_name, subnet, vlan_id) => {
                const newAccessNodes = structuredClone(accessNodes);
                newAccessNodes.forEach(item => {
                    item.other_ip_config = item.other_ip_config || [];
                    item.other_ip_config.push({
                        vlan_name,
                        vlan_id,
                        ip_address: "",
                        anycast_ip_address: ip.cidrSubnet(subnet).lastAddress
                    });
                });
                setAccessNodes(newAccessNodes);
                const newCoreNodes = structuredClone(coreNodes);
                newCoreNodes.forEach(item => {
                    item.other_ip_config = item.other_ip_config || [];
                    item.other_ip_config.push({
                        vlan_name,
                        vlan_id,
                        ip_address: "",
                        anycast_ip_address: ip.cidrSubnet(subnet).lastAddress
                    });
                });
                setCoreNodes(newCoreNodes);
                setCampusFabricData(prev => {
                    return {
                        ...prev,
                        nodes: {
                            ...prev.nodes,
                            core: newCoreNodes,
                            pods: prev.nodes.pods?.map(pod => {
                                return {
                                    ...pod,
                                    access: pod.access.map(accessNode => {
                                        const node = newAccessNodes?.find(
                                            item => item.switch_sn === accessNode.switch_sn
                                        );
                                        return {
                                            ...accessNode,
                                            other_ip_config: node.other_ip_config
                                        };
                                    })
                                };
                            })
                        }
                    };
                });
                form.resetFields();
            },
            editNetworkPostAction: (vlan_name, subnet, vlan_id) => {
                const newAccessNodes = structuredClone(accessNodes);
                newAccessNodes.forEach(item => {
                    const index = item.other_ip_config.findIndex(config => config.vlan_name === vlan_name);
                    item.other_ip_config[index].anycast_ip_address = ip.cidrSubnet(subnet).lastAddress;
                    item.other_ip_config[index].vlan_id = vlan_id;
                });
                setAccessNodes(newAccessNodes);
                const newCoreNodes = structuredClone(coreNodes);
                newCoreNodes.forEach(item => {
                    const index = item.other_ip_config.findIndex(config => config.vlan_name === vlan_name);
                    item.other_ip_config[index].anycast_ip_address = ip.cidrSubnet(subnet).lastAddress;
                    item.other_ip_config[index].vlan_id = vlan_id;
                });
                setCoreNodes(newCoreNodes);
                setCampusFabricData(prev => {
                    return {
                        ...prev,
                        nodes: {
                            ...prev.nodes,
                            core: newCoreNodes,
                            pods: prev.nodes.pods?.map(pod => {
                                return {
                                    ...pod,
                                    access: pod.access.map(accessNode => {
                                        const node = newAccessNodes?.find(
                                            item => item.switch_sn === accessNode.switch_sn
                                        );
                                        return {
                                            ...accessNode,
                                            other_ip_config: node.other_ip_config
                                        };
                                    })
                                };
                            })
                        }
                    };
                });
                form.resetFields();
            },
            deleteNetworkPostAction: vlan_name => {
                const newAccessNodes = structuredClone(accessNodes);
                newAccessNodes.forEach(item => {
                    const index = item.other_ip_config.findIndex(config => config.vlan_name === vlan_name);
                    item.other_ip_config.splice(index, 1);
                });
                setAccessNodes(newAccessNodes);
                const newCoreNodes = structuredClone(coreNodes);
                newCoreNodes.forEach(item => {
                    const index = item.other_ip_config.findIndex(config => config.vlan_name === vlan_name);
                    item.other_ip_config.splice(index, 1);
                });
                setCoreNodes(newCoreNodes);
                setCampusFabricData(prev => {
                    return {
                        ...prev,
                        nodes: {
                            ...prev.nodes,
                            core: newCoreNodes,
                            pods: prev.nodes.pods?.map(pod => {
                                return {
                                    ...pod,
                                    access: pod.access.map(accessNode => {
                                        const node = newAccessNodes?.find(
                                            item => item.switch_sn === accessNode.switch_sn
                                        );
                                        return {
                                            ...accessNode,
                                            other_ip_config: node.other_ip_config
                                        };
                                    })
                                };
                            })
                        }
                    };
                });
            }
        }));

        return (
            <div>
                <h3>Other IP Configuration</h3>
                <Form form={form} validateTrigger={false}>
                    <Table
                        rowKey={record => record.switch_sn}
                        pagination={{
                            defaultPageSize: 10,
                            showSizeChanger: true,
                            hideOnSinglePage: accessNodes.length <= 10
                        }}
                        bordered
                        style={{...tableStyle, marginTop: 20}}
                        columns={columns}
                        dataSource={accessNodes}
                        expandable={{
                            expandedRowRender: record => <ExpandedRowRender dataSource={record.other_ip_config} />,
                            expandIcon: props => <ExpandIcon {...props} />
                        }}
                    />
                </Form>

                <Divider style={{marginTop: "32px", marginBottom: "32px"}} />
            </div>
        );
    }
);

const ExpandedRowRender = ({dataSource}) => {
    return (
        <ConfigProvider
            theme={{
                components: {
                    Table: {
                        headerBg: "#fcfcfd"
                    }
                }
            }}
        >
            <Table
                bordered
                style={{
                    paddingRight: 48,
                    paddingBottom: 32,
                    paddingTop: 32
                }}
                columns={[
                    {
                        title: "Network",
                        dataIndex: "vlan_name",
                        key: "network"
                    },
                    {
                        title: "Anycast IP Address",
                        dataIndex: "anycast_ip_address",
                        key: "ip_address"
                    }
                ]}
                dataSource={dataSource}
                pagination={false}
            />
        </ConfigProvider>
    );
};
