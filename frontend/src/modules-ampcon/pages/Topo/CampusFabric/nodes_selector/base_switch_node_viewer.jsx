import switchSvg from "@/modules-ampcon/pages/Topo/CampusFabric/resource/switch.svg";

const BaseSwitchNodeViewer = ({node, showDeviceBriefTooltipCallback, hideDeviceBriefTooltipCallback}) => {
    const label = node?.label || "PICOS";
    const displayLabel = label.length > 10 ? `${label.slice(0, 10)}...` : label;
    const nodeIcon = <img src={switchSvg} alt="switch" />;

    return (
        <div
            style={{
                display: "flex",
                flexDirection: "column",
                justifyContent: "center",
                alignItems: "center",
                height: "100%",
                textAlign: "center"
            }}
            onMouseEnter={() => {
                showDeviceBriefTooltipCallback(node);
            }}
            onMouseLeave={() => {
                hideDeviceBriefTooltipCallback();
            }}
        >
            {nodeIcon}
            <div
                style={{
                    fontFamily: "Lato",
                    fontWeight: 600,
                    fontSize: "14px",
                    color: "#212519",
                    marginTop: "2px",
                    whiteSpace: "nowrap"
                }}
            >
                {displayLabel}
            </div>
        </div>
    );
};

export default BaseSwitchNodeViewer;
