import {But<PERSON>, Card, Flex, Form, Input, message} from "antd";
import DistributionNodeSelector from "@/modules-ampcon/pages/Topo/CampusFabric/nodes_selector/pod/distribution_node_selector";
import AccessNodeSelector from "@/modules-ampcon/pages/Topo/CampusFabric/nodes_selector/pod/access_node_selector";
import {forwardRef, useEffect, useImperativeHandle, useState} from "react";
import deletePod from "@/modules-ampcon/pages/Topo/CampusFabric/resource/delete_pod.svg";
import PodNotConfigureYetTooltip from "@/modules-ampcon/pages/Topo/CampusFabric/tooltip/pod_not_configure_yet_tooltip";
import PodConfigureCompleteTooltip from "@/modules-ampcon/pages/Topo/CampusFabric/tooltip/pod_configure_complete_tooltip";
import "@/modules-ampcon/pages/Topo/CampusFabric/nodes_selector/nodes_selector.scss";

const PodNodeSelector = forwardRef(
    (
        {
            pod,
            index,
            podsLocal,
            colCounts,
            addDistributionSwitchCallback,
            deleteDistributionSwitchCallback,
            addAccessSwitchCallback,
            deleteAccessSwitchCallback,
            editPodNameCallback,
            deletePodCallback,
            showDeviceBriefTooltipCallback,
            hideDeviceBriefTooltipCallback,
            podNameChangeCallback,
            isSinglePodAccessNumValid,
            isHidePodValid = true
        },
        ref
    ) => {
        const [podForm] = Form.useForm();
        const [isPodNameValid, setIsPodNameValid] = useState(true);
        const [isPodValid, setIsPodValid] = useState(true);

        useEffect(() => {
            if (pod) {
                podForm.setFieldsValue({
                    podName: pod.podName || pod.pod_name || `Pod ${index + 1}`
                });
                pod.podName = pod.podName || pod.pod_name || `Pod ${index + 1}`;
            }
        }, []);

        useEffect(() => {
            updateIsPodValid();
        }, [pod.distribution, pod.access, pod.podName, pod]);

        useImperativeHandle(ref, () => ({
            validate: async () => {
                try {
                    if (!pod) {
                        return true;
                    }
                    updateIsPodValid();
                    if (pod.distribution.length === 0) {
                        message.error("Please add at least one distribution switch.");
                        setIsPodValid(false);
                        return false;
                    }

                    if (!isSinglePodAccessNumValid(pod)) {
                        message.error("Please add at least two access switches.");
                        setIsPodValid(false);
                        return false;
                    }

                    if (pod.access.length === 0) {
                        message.error("Please add at least one access switch in each pod.");
                        setIsPodValid(false);
                        return false;
                    }

                    return true;
                } catch (e) {
                    return false;
                }
            },
            getPodName: () => podForm.getFieldValue("podName"),
            setPodNameInvalid: () => setIsPodNameValid(false),
            setPodNameValid: () => setIsPodNameValid(true),
            updateIsPodValid
        }));

        const updateIsPodValid = () => {
            const currentPodName = podForm.getFieldValue("podName");
            const hasDuplicate = podsLocal.some(
                (p, i) => p !== null && i !== index && (p.podName ? p.podName : p.pod_name) === currentPodName
            );

            let isNameValid = true;
            if (!currentPodName || currentPodName.trim() === "") {
                isNameValid = false;
            } else if (currentPodName.length > 32) {
                isNameValid = false;
            } else if (hasDuplicate) {
                isNameValid = false;
            }

            const isDistributionValid = pod.distribution.length > 0;
            const isAccessValid = pod.access.length > 0 && isSinglePodAccessNumValid(pod);

            const isValid = isNameValid && isDistributionValid && isAccessValid;

            setIsPodNameValid(isNameValid);
            setIsPodValid(isValid);
        };

        const podValidLabel = () => {
            if (!isHidePodValid) {
                return isPodValid ? <PodConfigureCompleteTooltip /> : <PodNotConfigureYetTooltip />;
            }
            return null;
        };

        const getHelpMessage = () => {
            if (!isPodNameValid) {
                if (podForm.getFieldValue("podName").trim() === "") {
                    return "Pod name cannot be empty.";
                }
                if (podForm.getFieldValue("podName").length > 32) {
                    return "Pod name cannot exceed 32 characters.";
                }

                const hasDuplicate = podsLocal.some(
                    (p, i) =>
                        p !== null &&
                        i !== index &&
                        (p.podName ? p.podName : p.pod_name) === podForm.getFieldValue("podName")
                );

                if (hasDuplicate) {
                    return "Pod name cannot be the same as other.";
                }
            }
            return "";
        };

        return (
            pod && (
                <Card
                    style={{
                        minWidth: "500px",
                        width: `calc((100% - ${20 * (colCounts - 1)}px) / ${colCounts})`
                    }}
                >
                    <Button
                        className="imgSvg"
                        type="text"
                        icon={<img src={deletePod} alt="Delete Pod" />}
                        style={{
                            position: "absolute",
                            top: 10,
                            right: 10,
                            zIndex: 1,
                            backgroundColor: "transparent"
                        }}
                        onClick={() => {
                            deletePodCallback(index);
                            setTimeout(() => {
                                updateIsPodValid();
                            }, 50);
                        }}
                    />
                    <Flex vertical>
                        <Flex>
                            <h4 style={{display: "inline", marginBottom: "7px"}}>Pod Name</h4>
                            {podValidLabel()}
                        </Flex>
                        <Form
                            form={podForm}
                            onFieldsChange={(_, allFields) => {
                                podNameChangeCallback(index, allFields[0].value);
                                editPodNameCallback(index, allFields[0].value);
                            }}
                        >
                            <Form.Item
                                name="podName"
                                validateStatus={isPodNameValid ? "success" : "error"}
                                help={getHelpMessage()}
                                rules={[
                                    {
                                        required: true
                                    }
                                ]}
                                style={{marginTop: 20}}
                            >
                                <Input style={{width: 280}} />
                            </Form.Item>
                        </Form>
                        <DistributionNodeSelector
                            nodes={pod.distribution}
                            index={index}
                            addDistributionSwitchCallback={addDistributionSwitchCallback}
                            deleteDistributionSwitchCallback={deleteDistributionSwitchCallback}
                            showDeviceBriefTooltipCallback={showDeviceBriefTooltipCallback}
                            hideDeviceBriefTooltipCallback={hideDeviceBriefTooltipCallback}
                        />
                        <AccessNodeSelector
                            nodes={pod.access}
                            index={index}
                            addAccessSwitchCallback={addAccessSwitchCallback}
                            deleteAccessSwitchCallback={deleteAccessSwitchCallback}
                            showDeviceBriefTooltipCallback={showDeviceBriefTooltipCallback}
                            hideDeviceBriefTooltipCallback={hideDeviceBriefTooltipCallback}
                        />
                    </Flex>
                </Card>
            )
        );
    }
);

export default PodNodeSelector;
