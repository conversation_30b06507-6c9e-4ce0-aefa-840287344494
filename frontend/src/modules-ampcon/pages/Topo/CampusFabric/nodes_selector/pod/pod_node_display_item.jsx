import {<PERSON><PERSON>, Card, Flex, Form, message} from "antd";
import {forwardRef, useEffect, useImperativeHandle, useState} from "react";
import viewPodGrey from "@/modules-ampcon/pages/Topo/CampusFabric/resource/view_pod_grey.svg";
import DistributionNodeDisplayItem from "@/modules-ampcon/pages/Topo/CampusFabric/nodes_selector/pod/distribution_node_display_item";
import AccessNodeDisplayItem from "@/modules-ampcon/pages/Topo/CampusFabric/nodes_selector/pod/access_node_display_item";
import PodConfigureCompleteTooltip from "@/modules-ampcon/pages/Topo/CampusFabric/tooltip/pod_configure_complete_tooltip";
import PodNotConfigureYetTooltip from "@/modules-ampcon/pages/Topo/CampusFabric/tooltip/pod_not_configure_yet_tooltip";

const PodNodeDisplayItem = forwardRef(
    (
        {
            pod,
            index,
            colCounts,
            showDeviceBriefTooltipCallback,
            hideDeviceBriefTooltipCallback,
            viewPodNodeCallback,
            isPodNodeValid
        },
        ref
    ) => {
        const [podForm] = Form.useForm();
        const [isPodValid, setIsPodValid] = useState(false);

        useEffect(() => {
            setIsPodValid(isPodNodeValid(pod));
        }, []);

        useImperativeHandle(ref, () => ({
            validate: async () => {
                try {
                    await podForm.validateFields();
                    if (pod.distribution.length === 0) {
                        message.error("Please add at least one distribution switch in each pod");
                        return false;
                    }
                    if (pod.access.length === 0) {
                        message.error("Please add at least two access switches");
                        return false;
                    }
                    return true;
                } catch (e) {
                    return false;
                }
            }
        }));

        return pod === null ? null : (
            <Card
                style={{
                    minWidth: "500px",
                    width: `calc((100% - ${20 * (colCounts - 1)}px) / ${colCounts})`
                }}
            >
                <Button
                    className="view-pod-button"
                    type="text"
                    icon={<img src={viewPodGrey} alt="View Pod" />}
                    style={{
                        position: "absolute",
                        top: 10,
                        right: 10,
                        zIndex: 1,
                        backgroundColor: "transparent"
                    }}
                    onClick={() => {
                        viewPodNodeCallback(pod?.podName || pod.pod_name, pod.distribution, pod.access);
                    }}
                />
                <Flex vertical>
                    <Flex>
                        <h4 style={{display: "inline", fontSize: "18px", marginBottom: "5px", marginTop: "18px"}}>
                            {pod?.podName || pod.pod_name}
                        </h4>
                        {isPodValid ? <PodConfigureCompleteTooltip /> : <PodNotConfigureYetTooltip />}
                    </Flex>
                    <DistributionNodeDisplayItem
                        nodes={pod.distribution}
                        index={index}
                        showDeviceBriefTooltipCallback={showDeviceBriefTooltipCallback}
                        hideDeviceBriefTooltipCallback={hideDeviceBriefTooltipCallback}
                    />
                    <AccessNodeDisplayItem
                        nodes={pod.access}
                        index={index}
                        showDeviceBriefTooltipCallback={showDeviceBriefTooltipCallback}
                        hideDeviceBriefTooltipCallback={hideDeviceBriefTooltipCallback}
                    />
                </Flex>
            </Card>
        );
    }
);

export default PodNodeDisplayItem;
