import React, {useRef, useEffect, useState} from "react";
import {<PERSON>, Spin, message, Flex, Button} from "antd";
import {Graph} from "@antv/x6";
import {register} from "@antv/x6-react-shape?react";
import switchSvg from "./resource/switch.svg";
import IPCLOSServiceDetailedInformation from "./tooltip/ipclos_device_detailed_information";
import {useLocation, useNavigate} from "react-router-dom";
import styles from "@/modules-ampcon/pages/Topo/CampusFabric/campus_fabric.module.scss";
import {ArrowLeftOutlined} from "@ant-design/icons";
import {deleteSvg} from "@/utils/common/iconSvg";
import Icon from "@ant-design/icons/lib/components/Icon";
import EditSvg from "@/modules-ampcon/pages/Topo/CampusFabric/resource/edit.svg?react";
import DepStatus from "@/modules-ampcon/pages/Topo/CampusFabric/resource/dep_status.svg?react";
import {calculateVertices} from "@/utils/topo_layout_utils";
import {viewSiteTopo, deleteSiteTopo} from "@/modules-ampcon/apis/campus_blueprint_api";
import {confirmModalAction} from "@/modules-ampcon/components/custom_modal";
import CampusFabricCreateAndEditView from "./campus_fabric_create_and_edit_view";
import DeviceBriefTooltip from "@/modules-ampcon/pages/Topo/CampusFabric/tooltip/device_brief_tooltip";
import {debounce} from "lodash";
import CampusFabricSwitchStatus from "@/modules-ampcon/pages/Topo/CampusFabric/campus_fabric_deployment_status_table";
import {getSwitchLldp} from "@/modules-ampcon/apis/monitor_api";

const SwitchNode = ({node}) => {
    const {label = "PICOS"} = node?.store?.data || {};
    const displayLabel = label.length > 10 ? `${label.slice(0, 10)}...` : label;

    const containerStyle = {
        display: "flex",
        flexDirection: "column",
        justifyContent: "center",
        alignItems: "center",
        height: "100%",
        width: "100%",
        textAlign: "center"
    };

    const labelStyle = {
        fontFamily: "Lato",
        fontWeight: 600,
        fontSize: "14px",
        color: "#212519",
        marginTop: "2px",
        whiteSpace: "nowrap"
    };

    return (
        <div style={containerStyle}>
            <img src={switchSvg} alt="switch" />
            <div style={labelStyle}>{displayLabel}</div>
        </div>
    );
};

const CampusFabricIPCLOSRecordView = () => {
    const containerRef = useRef(null);
    const [containerHeight, setContainerHeight] = useState(0);
    const [containerWidth, setContainerWidth] = useState(0);
    const graphRef = useRef(null);
    const deviceBriefTooltipRef = useRef(null);
    const [isShowSpin, setIsShowSpin] = useState(false);
    const deviceDetailedInformationRef = useRef(null);
    const [campusFabricData, setCampusFabricData] = useState([]);
    const [isShowCreateView, setIsShowCreateView] = useState(false);
    const [isShowStatusView, setIsShowStatusView] = useState(false);
    const [isDataLoaded, setIsDataLoaded] = useState(false);
    const [forceRender, setForceRender] = useState(false);
    const [minPodWidth, setMinPodWidth] = useState(515);
    const {state} = useLocation();
    const [isEditing, setIsEditing] = useState(false);
    const type = state?.actionType;
    const [cardHeight, setCardHeight] = useState("100%");
    const [resTopo, setResTopo] = useState({});
    const navigate = useNavigate();
    const DEVICE_WIDTH = 70;
    const BORDER_HEIGHT = campusFabricData?.nodes?.border?.length ? 180 : 0;
    const POD_HEIGHT = 480;
    const boxHeight = 100;
    const DEVICE_HEIGHT = 60;
    const DEVICE_SPACING = 50;
    const spacingBetweenPodsTitleAndPods = 50;
    const [allSwitchPortResult, setAllSwitchPortResult] = useState();
    const [newLldpResult, setNewLldpResult] = useState({});
    const currentNodeRef = useRef(null);
    const registerCustomNode = () => {
        register({
            shape: "switch",
            width: 40,
            height: 54,
            component: SwitchNode
        });
    };
    const fetchSiteTopo = async () => {
        setIsShowSpin(true);
        if (state?.data) {
            try {
                const res = await viewSiteTopo(state?.data?.id);
                const lldpResult = {};
                if (res?.status !== 400 && res?.status !== 404) {
                    setResTopo(res);
                    setCampusFabricData(res);
                    const snList = [];
                    res.nodes.core.forEach(device => {
                        if (device.switch_sn) {
                            snList.push(device.switch_sn);
                        }
                    });
                    res.nodes.pods.forEach(pod => {
                        pod.access.forEach(deviceAcess => {
                            if (deviceAcess.switch_sn) {
                                snList.push(deviceAcess.switch_sn);
                            }
                        });
                        pod.distribution.forEach(deviceDistribution => {
                            if (deviceDistribution.switch_sn) {
                                snList.push(deviceDistribution.switch_sn);
                            }
                        });
                    });
                    try {
                        const result = await getSwitchLldp(snList);
                        if (result?.status === 200) {
                            Object.entries(result.data).forEach(([sourceDevice, targets]) => {
                                Object.entries(targets).forEach(([targetDevice, connections]) => {
                                    connections.forEach(connection => {
                                        const key = `${sourceDevice}:${connection.source_port}->${targetDevice}:${connection.target_port}`;
                                        lldpResult[key] = false;
                                    });
                                });
                            });
                        }
                    } catch (error) {
                        console.error(error);
                    }
                    const allSwitchPort = {
                        core: {},
                        pods: []
                    };
                    // 处理core
                    for (const coreNode of res.nodes.core) {
                        const {switch_sn, links} = coreNode;
                        if (!allSwitchPort.core[switch_sn]) {
                            allSwitchPort.core[switch_sn] = {to_core: [], to_dis: []};
                        }
                        // 收集 core-to-core 端口
                        if (links.to_core) {
                            allSwitchPort.core[switch_sn].to_core.push(...Object.keys(links.to_core));
                        }
                        // 如果有 to_dis 端口，分类到 core 的 to_dis
                        if (links.to_dis) {
                            allSwitchPort.core[switch_sn].to_dis.push(...Object.keys(links.to_dis));
                        }
                    }
                    // 处理 pod
                    for (const pod of res.nodes.pods) {
                        const podEntry = {
                            access: {},
                            dis: {}
                        };
                        // dis
                        for (const disNode of pod.distribution) {
                            const {switch_sn, links} = disNode;
                            if (!podEntry.dis[switch_sn]) {
                                podEntry.dis[switch_sn] = {
                                    to_core: [], // dis-to-core 连接
                                    to_access: [] // dis-to-access 连接
                                };
                            }
                            if (links.to_core) {
                                podEntry.dis[switch_sn].to_core.push(...Object.keys(links.to_core));
                            }
                            if (links.to_access) {
                                podEntry.dis[switch_sn].to_access.push(...Object.keys(links.to_access));
                            }
                        }
                        // access
                        for (const accessNode of pod.access) {
                            const {switch_sn, links} = accessNode;
                            if (!podEntry.access[switch_sn]) {
                                podEntry.access[switch_sn] = {
                                    to_dis: []
                                };
                            }
                            if (links.to_dis) {
                                podEntry.access[switch_sn].to_dis.push(...Object.keys(links.to_dis));
                            }
                        }
                        allSwitchPort.pods.push(podEntry);
                    }
                    const allSwitchPortCopy = JSON.parse(JSON.stringify(allSwitchPort));
                    // core to core 连接
                    for (const [coreSn1, coreData1] of Object.entries(allSwitchPort.core)) {
                        for (const port of coreData1.to_core) {
                            // 遍历其他 core 交换机
                            for (const [coreSn2, coreData2] of Object.entries(allSwitchPort.core)) {
                                if (coreSn1 !== coreSn2) {
                                    for (const corePort of coreData2.to_core) {
                                        const key1 = `${coreSn1}:${port}->${coreSn2}:${corePort}`;
                                        const key2 = `${coreSn2}:${corePort}->${coreSn1}:${port}`;
                                        let matched = false;
                                        if (lldpResult[key1] !== undefined) {
                                            lldpResult[key1] = true;
                                            matched = true;
                                        }
                                        if (lldpResult[key2] !== undefined) {
                                            lldpResult[key2] = true;
                                            matched = true;
                                        }
                                        if (matched) {
                                            coreData1.to_core = coreData1.to_core.filter(p => p !== port);
                                            coreData2.to_core = coreData2.to_core.filter(p => p !== corePort);
                                        }
                                    }
                                }
                            }
                        }
                    }
                    // core to dis 连接
                    for (const [coreSn, coreData] of Object.entries(allSwitchPort.core)) {
                        for (const port of coreData.to_dis) {
                            for (const pod of allSwitchPort.pods) {
                                for (const [disSn, disData] of Object.entries(pod.dis)) {
                                    for (const disPort of disData.to_core) {
                                        const key1 = `${coreSn}:${port}->${disSn}:${disPort}`;
                                        const key2 = `${disSn}:${disPort}->${coreSn}:${port}`;
                                        let matched = false;
                                        if (lldpResult[key1] !== undefined) {
                                            lldpResult[key1] = true;
                                            matched = true;
                                        }
                                        if (lldpResult[key2] !== undefined) {
                                            lldpResult[key2] = true;
                                            matched = true;
                                        }
                                        if (matched) {
                                            coreData.to_dis = coreData.to_dis.filter(p => p !== port);
                                            disData.to_core = disData.to_core.filter(p => p !== disPort);
                                        }
                                    }
                                }
                            }
                        }
                    }
                    // access to dis
                    for (const pod of res.nodes.pods) {
                        for (const accessNode of pod.access) {
                            const accessSn = accessNode.switch_sn;
                            const {links} = accessNode;
                            if (links.to_dis) {
                                for (const [port, disInfo] of Object.entries(links.to_dis)) {
                                    const disSn = disInfo.dis_sn;
                                    const podIndex = pod.pod_index;
                                    const disData = allSwitchPort.pods[podIndex]?.dis?.[disSn];
                                    if (!disData) continue;
                                    // 检查 distribution 的 to_access 端口
                                    for (const disPort of disData.to_access) {
                                        const key1 = `${accessSn}:${port}->${disSn}:${disPort}`;
                                        const key2 = `${disSn}:${disPort}->${accessSn}:${port}`;
                                        let matched = false;
                                        if (lldpResult[key1] !== undefined) {
                                            lldpResult[key1] = true;
                                            matched = true;
                                        }
                                        if (lldpResult[key2] !== undefined) {
                                            lldpResult[key2] = true;
                                            matched = true;
                                        }
                                        if (matched) {
                                            // 从 access 交换机删除匹配的端口
                                            const accessEntry = allSwitchPort.pods[podIndex].access[accessSn];
                                            if (accessEntry) {
                                                accessEntry.to_dis = accessEntry.to_dis.filter(p => p !== port);
                                            }
                                            // 从 distribution 交换机删除匹配的端口
                                            disData.to_access = disData.to_access.filter(p => p !== disPort);
                                        }
                                    }
                                }
                            }
                        }
                    }
                    setAllSwitchPortResult(allSwitchPortCopy);
                    setNewLldpResult(lldpResult);
                    setResTopo(res);
                    setCampusFabricData(res);
                    setIsShowSpin(false);
                    return res;
                }
                message.error(res.info);
            } catch (error) {
                console.error(error);
            }
        }
    };
    useEffect(() => {
        if (!state || !state?.data) {
            navigate("/topo/campus_fabric");
            return;
        }
        const fetchAndPrepare = async () => {
            await fetchSiteTopo();
            setIsDataLoaded(true); // 标记数据准备好了
        };
        fetchAndPrepare();
        // fetchSiteTopo();
        // setIsDataLoaded(true);
    }, [state, navigate]);
    const deleteSiteTopoCallback = record => {
        confirmModalAction("Are you sure want to delete?", () => {
            deleteSiteTopo(campusFabricData?.site_config_id).then(res => {
                if (res.status === 200) {
                    message.success(res.info);
                    navigate("/topo/campus_fabric");
                } else {
                    message.error(res.info);
                }
            });
        });
    };

    const boxNode = (graph, fillColor, x, y, width, height, label) => {
        const node = graph.addNode({
            attrs: {
                body: {
                    fill: fillColor,
                    stroke: fillColor === "none" ? "#F2F2F2" : "none",
                    strokeWidth: 2,
                    rx: 5,
                    ry: 5
                },
                label: {
                    text: label || "",
                    fontFamily: "Lato",
                    fontWeight: label !== "Distribution" && label !== "Access" ? 700 : 400,
                    fontSize: label !== "Distribution" && label !== "Access" ? "18px" : "14px",
                    fill: "#212519",
                    refX:
                        label === "Core" || label === "Border" || label === "Access" || label === "Distribution"
                            ? 0
                            : 30,
                    refY:
                        // eslint-disable-next-line no-nested-ternary
                        label === "Core" || label === "Border"
                            ? -30
                            : label === "Access" || label === "Distribution"
                              ? -20
                              : 40,
                    textAnchor: "start"
                }
            },
            shape: "rect",
            x,
            y,
            width,
            height,
            zIndex: -1
        });

        if (label === "Core") {
            graph.addNode({
                attrs: {
                    body: {
                        fill: "none",
                        stroke: "none"
                    },
                    label: {
                        text: "Pods",
                        fontFamily: "Lato",
                        fontWeight: 600,
                        fontSize: "18px",
                        fill: "#000",
                        refX: 20,
                        refY: 175,
                        textAnchor: "middle"
                    }
                },
                shape: "rect",
                x,
                y,
                width,
                height: 30,
                zIndex: -1
            });
        }

        return node;
    };

    const setBorderAndCore = (graph, containerWidth) => {
        const podY = BORDER_HEIGHT === 0 ? 40 : 70;
        const coreY = podY + BORDER_HEIGHT;
        if (BORDER_HEIGHT !== 0) {
            const borderGroup = boxNode(graph, "#F9FAFA", 0, 40, containerWidth, 140, "Border");
            layoutDevice(campusFabricData?.nodes?.border, podY, "border", borderGroup);
        }

        const coreGroup = boxNode(graph, "#F9FAFA", 0, coreY, containerWidth, 140, "Core");
        layoutDevice(campusFabricData?.nodes?.core, coreY, "core", coreGroup);
    };

    const layoutDevice = (pod, podY, type, box, distributionDevicesBox) => {
        const maxNodesPerRow = Math.floor(box.getBBox().width / (DEVICE_WIDTH + DEVICE_SPACING));
        let row = 0;
        let list = [];
        if (type === "access") {
            podY += distributionDevicesBox.getBBox().height + 50;
            list = pod?.access ?? [];
        } else if (type === "distribution") {
            list = pod?.distribution ?? [];
        } else if (type === "core") {
            podY -= 90;
            list = campusFabricData?.nodes?.core ?? [];
        } else if (type === "border") {
            podY -= 120;
            list = campusFabricData?.nodes?.border ?? [];
        }

        if (list.length > 0) {
            list.forEach((device, deviceIndex) => {
                const col = deviceIndex % maxNodesPerRow;
                row = Math.floor(deviceIndex / maxNodesPerRow);
                const nodesInRow = Math.min(maxNodesPerRow, list.length - row * maxNodesPerRow);
                const rowWidth = nodesInRow * DEVICE_WIDTH + (nodesInRow - 1) * DEVICE_SPACING;
                const offsetX =
                    type === "access" || type === "distribution"
                        ? (box.getBBox().width - rowWidth) / 2 + 60
                        : (box.getBBox().width - rowWidth) / 2 + 30;
                const deviceX = offsetX + col * (DEVICE_WIDTH + DEVICE_SPACING);
                const deviceY = podY + 150 + row * (DEVICE_HEIGHT + DEVICE_SPACING);
                const deviceNode = graphRef.current.addNode({
                    ...device,
                    id: device.id,
                    shape: "switch",
                    x: deviceX,
                    y: deviceY
                });
                box.addChild(deviceNode);
            });
        }

        const totalRows = row + 1;
        const height = totalRows === 1 ? 140 : totalRows * (DEVICE_HEIGHT + DEVICE_SPACING) + 10;
        box.resize(box.getBBox().width, height);
        return height;
    };

    const addNewPodNode = (graph, pod, index, containerWidth) => {
        const podSpacing = 20;

        const maxPodsPerRow = Math.floor((containerWidth - 20) / (minPodWidth + podSpacing));
        const totalPods = campusFabricData?.nodes?.pods.length;

        let podWidth = 0;
        if (totalPods > maxPodsPerRow) {
            podWidth =
                totalPods === 1
                    ? Math.floor(containerWidth / maxPodsPerRow)
                    : Math.floor(containerWidth / maxPodsPerRow) - 16;
        } else {
            podWidth =
                totalPods === 1 ? Math.floor(containerWidth / totalPods) : Math.floor(containerWidth / totalPods) - 16;
        }
        if (podWidth < minPodWidth) podWidth = minPodWidth;

        const podY = 0 + BORDER_HEIGHT;
        const podGroup = boxNode(graph, "none", 0, podY, podWidth, POD_HEIGHT, pod.pod_name);
        podGroup.store.data.podIndex = index;

        const distributionDevicesBox = boxNode(
            graph,
            "#F9FAFA",
            30,
            podY + boxHeight,
            podWidth - 60,
            140,
            "Distribution"
        );
        const accessDevicesBox = boxNode(
            graph,
            "#F9FAFA",
            30,
            podY + boxHeight * 2 + spacingBetweenPodsTitleAndPods + 50,
            podWidth - 60,
            140,
            "Access"
        );

        podGroup.addChild(accessDevicesBox);
        podGroup.addChild(distributionDevicesBox);

        layoutDevice(pod, podY, "distribution", distributionDevicesBox);
        layoutDevice(pod, podY, "access", accessDevicesBox, distributionDevicesBox);

        const accessHeight = accessDevicesBox.getBBox().height;
        const distributionHeight = distributionDevicesBox.getBBox().height;
        const totalHeight = accessHeight + distributionHeight + 180;
        podGroup.resize(podWidth, totalHeight);

        const updateInternalLayout = () => {
            const distributionHeight = distributionDevicesBox.getBBox().height;
            const accessY = distributionDevicesBox.getBBox().y + distributionHeight + 50;
            accessDevicesBox.setPosition(30, accessY);
        };

        updateInternalLayout();

        distributionDevicesBox.on("change:size", updateInternalLayout);
        accessDevicesBox.on("change:size", updateInternalLayout);

        return podGroup;
    };

    const setPod = (graph, currentPods, containerWidth) => {
        const podSpacing = 20;
        const maxPodsPerRow = Math.floor((containerWidth - 20) / (minPodWidth + podSpacing));
        const rows = [];
        const validMaxPodsPerRow = Math.max(1, Math.floor(Number(maxPodsPerRow) || 1));
        for (let i = 0; i < currentPods.length; i += validMaxPodsPerRow) {
            rows.push(currentPods.slice(i, i + validMaxPodsPerRow));
        }

        const rowHeights = [];
        let currentY = BORDER_HEIGHT === 0 ? 250 : 270;

        rows.forEach((rowPods, rowIndex) => {
            let maxRowHeight = 0;
            const podGroups = [];

            rowPods.forEach((pod, podIndexInRow) => {
                const index = rowIndex * maxPodsPerRow + podIndexInRow;
                const podGroup = addNewPodNode(graph, pod, index, containerWidth);
                podGroups.push(podGroup);

                const podHeight = podGroup.getBBox().height;
                if (podHeight > maxRowHeight) {
                    maxRowHeight = podHeight;
                }
            });

            podGroups.forEach(podGroup => {
                const currentHeight = podGroup.getBBox().height;
                if (currentHeight < maxRowHeight) {
                    podGroup.resize(podGroup.getBBox().width, maxRowHeight);
                }
            });

            rowHeights.push(maxRowHeight);

            podGroups.forEach((podGroup, podIndexInRow) => {
                const podWidth = podGroup.getBBox().width;
                const shiftX = podIndexInRow * (podWidth + podSpacing);
                podGroup.translate(shiftX, currentY);
            });

            currentY += maxRowHeight + podSpacing;
        });

        resizeGraph(graph);
    };

    const resizeGraph = graph => {
        const getBottomMostNode = () => {
            const nodes = graph.getNodes();
            let bottomNode = null;
            let maxBottom = -Infinity;
            nodes.forEach(node => {
                const bbox = node.getBBox();
                const nodeBottom = bbox.y + bbox.height;
                if (nodeBottom > maxBottom) {
                    maxBottom = nodeBottom;
                    bottomNode = node;
                }
            });
            return bottomNode;
        };

        const getRightMostNode = () => {
            const nodes = graph.getNodes();
            let rightNode = null;
            let maxRight = -Infinity;
            nodes.forEach(node => {
                const bbox = node.getBBox();
                const nodeRight = bbox.x + bbox.width;
                if (nodeRight > maxRight) {
                    maxRight = nodeRight;
                    rightNode = node;
                }
            });
            return rightNode;
        };

        const bottomMostNode = getBottomMostNode();
        const nodeBBox = bottomMostNode?.getBBox ? bottomMostNode.getBBox() : {x: 0, y: 0, width: 0, height: 0};
        const newHeight = nodeBBox.y + nodeBBox.height;
        const minHeight = window.innerHeight - 400 || 0;
        const finalHeight = newHeight > minHeight ? newHeight + 24 : minHeight;

        const rightMostNode = getRightMostNode();
        const rightBBox = rightMostNode?.getBBox ? rightMostNode.getBBox() : {x: 0, y: 0, width: 0, height: 0};
        const newWidth = rightBBox.x + rightBBox.width;
        const minWidth = window.innerWidth - 340 || 0;
        const finalWidth = newWidth > minWidth ? newWidth : minWidth;

        if (containerRef.current) {
            containerRef.current.style.width = `${finalWidth}px`;
            containerRef.current.style.height = `${finalHeight}px`;
            containerRef.current.style.overflow = "hidden";
        }

        graph.resize(finalWidth, finalHeight);

        setContainerHeight(finalHeight);
        setContainerWidth(finalWidth);
    };

    const updateAllEdgesVertices = () => {
        graphRef.current.getEdges().forEach(edge => {
            if (edge.id === "splitter1") return;
            const sourceNode = graphRef.current.getCellById(edge.store.data.source.cell);
            const targetNode = graphRef.current.getCellById(edge.store.data.target.cell);
            edge.setVertices(calculateVertices(sourceNode, targetNode));
        });
    };

    // 取消高亮
    const handleGraphUnhighlight = () => {
        if (graphRef.current) {
            graphRef.current.getNodes().forEach(n => {
                const nodeView = graphRef.current.findViewByCell(n);
                nodeView.unhighlight(null, {
                    highlighter: {
                        name: "stroke",
                        args: {
                            padding: 5,
                            attrs: {
                                stroke: "#A2ACB2",
                                "stroke-width": 2,
                                "stroke-dasharray": "5,5",
                                transform: "scale(1, 0.8) translate(-0.5, -8)"
                            }
                        }
                    }
                });
            });
        }
    };
    const clearAllEdges = () => {
        const graph = graphRef.current;
        const edges = graph.getEdges();
        edges.forEach(edge => {
            graph.removeEdge(edge);
        });
    };
    useEffect(() => {
        if (!isDataLoaded) return; // 只有在数据加载完成后才执行
        const container = containerRef.current;
        if (!container) return;
        const graph = new Graph({
            container,
            width: container?.clientWidth,
            height: container?.clientHeight,
            grid: false,
            connector: "smooth",
            mousewheel: {
                enabled: false,
                modifiers: "ctrl"
            },
            panning: false,
            resizing: false,
            interacting: false
        });
        graphRef.current = graph;
        registerCustomNode();
        setPod(graph, campusFabricData?.nodes?.pods, containerRef.current.clientWidth);
        setBorderAndCore(graph, containerRef.current.clientWidth);
        const handleResize = debounce(() => {
            if (containerRef.current) {
                const newWidth = window.innerWidth - 365 || 0;
                const height = window.innerHeight - 400 || 0;
                containerRef.current.style.height = `${height}px`;

                setContainerWidth(newWidth);

                if (newWidth > 600) {
                    setMinPodWidth(100);
                }
                setTimeout(() => {
                    graph.clearCells();
                    graph.resize(newWidth, height);
                    setPod(graph, campusFabricData?.nodes?.pods, newWidth);
                    setBorderAndCore(graph, newWidth);
                    if (currentNodeRef.current) {
                        if (currentNodeRef.current.length > 1) {
                            drawEdge(currentNodeRef.current || []);
                        } else {
                            deviceDetailedInformationRef.current.hideDeviceDetailedInformation();
                            // const nodeId = currentNodeRef.current.id;
                            // const node = graph.getCellById(nodeId);
                            // const nodeView = graph.findViewByCell(node);
                            // handleGraphUnhighlight();
                            // nodeView.highlight(null, {
                            //     highlighter: {
                            //         name: "stroke",
                            //         args: {
                            //             padding: 5,
                            //             attrs: {
                            //                 stroke: "#A2ACB2",
                            //                 "stroke-width": 2,
                            //                 "stroke-dasharray": "5,5",
                            //                 transform: "scale(1, 0.8) translate(-0.5, -8)"
                            //             }
                            //         }
                            //     }
                            // });
                            drawEdge([currentNodeRef.current] || []);
                        }
                    }
                    updateAllEdgesVertices();
                }, 50);
            } else {
                graph.clearCells();
            }
        }, 75);

        window.addEventListener("resize", handleResize);
        handleResize();

        const drawEdge = nodes => {
            const graph = graphRef.current;
            const drawnEdges = new Set();
            nodes.forEach(node => {
                const currentNode = {
                    index: -1,
                    type: null,
                    podIndex: -1
                };
                currentNode.index = campusFabricData?.nodes?.core.findIndex(item => item.id === node.id);
                currentNode.type = "core";

                if (currentNode.index === -1) {
                    currentNode.index = campusFabricData?.nodes?.border.findIndex(item => item.id === node.id);
                    currentNode.type = "border";
                }
                if (currentNode.index === -1) {
                    campusFabricData?.nodes?.pods?.forEach((pod, podIdx) => {
                        const distIndex = pod.distribution.findIndex(item => item.id === node.id);
                        if (distIndex !== -1) {
                            currentNode.index = distIndex;
                            currentNode.type = "distribution";
                            currentNode.podIndex = podIdx;
                            return;
                        }

                        const accessIndex = pod.access.findIndex(item => item.id === node.id);
                        if (accessIndex !== -1) {
                            currentNode.index = accessIndex;
                            currentNode.type = "access";
                            currentNode.podIndex = podIdx;
                            return;
                        }
                    });
                }
                if (currentNode.index !== -1) {
                    if (currentNode.type === "core") {
                        // 点击core节点，显示core节点与border节点的连线
                        const borderNodes = campusFabricData?.nodes?.border || [];
                        borderNodes.forEach(borderNode => {
                            const borderEdgeKey = `${node.id}-${borderNode.id}`;
                            const reverseBorderEdgeKey = `${borderNode.id}-${node.id}`;

                            if (!drawnEdges.has(borderEdgeKey) && !drawnEdges.has(reverseBorderEdgeKey)) {
                                drawnEdges.add(borderEdgeKey);
                                drawnEdges.add(reverseBorderEdgeKey);

                                graph.addEdge({
                                    source: {cell: node.id, anchor: {name: "top", args: {dy: -20}}},
                                    target: {cell: borderNode.id, anchor: {name: "bottom"}},
                                    attrs: {
                                        line: {stroke: "#D8D8D8", strokeWidth: 1.5, targetMarker: null}
                                    },
                                    connector: {name: "smooth"},
                                    zIndex: -1
                                });
                            }
                        });

                        // 点击core节点，显示core节点与core节点的连线
                        const coreNodes = campusFabricData?.nodes?.core;
                        const targetIndex = currentNode.index % 2 === 0 ? currentNode.index + 1 : currentNode.index - 1;
                        if (coreNodes[targetIndex]) {
                            const sourceId = coreNodes[currentNode.index].id;
                            const sourceSn = coreNodes[currentNode.index].switch_sn;
                            const targetId = coreNodes[targetIndex].id;
                            const targetSn = coreNodes[targetIndex].switch_sn;
                            const edgeKey = `${sourceId}-${targetId}`;
                            const reverseEdgeKey = `${targetId}-${sourceId}`;
                            // 遍历所有交换机，找到 sourceSn 和 targetSn 对应的端口
                            let sourcePorts = [];
                            let targetPorts = [];
                            if (allSwitchPortResult) {
                                sourcePorts = allSwitchPortResult.core[sourceSn]?.to_core || [];
                                targetPorts = allSwitchPortResult.core[targetSn]?.to_core || [];
                            }
                            let matchCount = 0; // 记录匹配成功的次数
                            let firstMatch = false;
                            let secondMatch = false;
                            // 遍历所有 sourcePort 和 targetPort 进行匹配
                            for (const sourcePort of sourcePorts) {
                                for (const targetPort of targetPorts) {
                                    const keyA = `${sourceSn}:${sourcePort}->${targetSn}:${targetPort}`;
                                    const keyB = `${targetSn}:${targetPort}->${sourceSn}:${sourcePort}`;
                                    if (newLldpResult[keyA] === true || newLldpResult[keyB] === true) {
                                        matchCount++;
                                        if (matchCount === 1) firstMatch = true;
                                        if (matchCount === 2) secondMatch = true;
                                    }
                                    if (matchCount >= 2) break;
                                }
                                if (matchCount >= 2) break;
                            }
                            const edgeColor1 = firstMatch ? "#2BC174" : "#D8D8D8";
                            const edgeColor2 = secondMatch ? "#2BC174" : "#D8D8D8";
                            if (!drawnEdges.has(edgeKey) && !drawnEdges.has(reverseEdgeKey)) {
                                drawnEdges.add(edgeKey);
                                drawnEdges.add(reverseEdgeKey);

                                graph.addEdge({
                                    source: {
                                        cell: sourceId,
                                        anchor: {
                                            name: "center",
                                            args: {dy: -20, dx: sourceId < targetId ? 25 : -25}
                                        }
                                    },
                                    target: {
                                        cell: targetId,
                                        anchor: {
                                            name: "center",
                                            args: {dy: -20, dx: sourceId < targetId ? -25 : 25}
                                        }
                                    },
                                    attrs: {
                                        line: {
                                            stroke: edgeColor1,
                                            strokeWidth: 1.5,
                                            targetMarker: null,
                                            style: {pointerEvents: "none"}
                                        }
                                    },
                                    zIndex: -1
                                });
                                graph.addEdge({
                                    source: {
                                        cell: sourceId,
                                        anchor: {
                                            name: "center",
                                            args: {dy: 0, dx: sourceId < targetId ? 25 : -25}
                                        }
                                    },
                                    target: {
                                        cell: targetId,
                                        anchor: {
                                            name: "center",
                                            args: {dy: 0, dx: sourceId < targetId ? -25 : 25}
                                        }
                                    },
                                    attrs: {
                                        line: {
                                            stroke: edgeColor2,
                                            strokeWidth: 1.5,
                                            targetMarker: null,
                                            style: {pointerEvents: "none"}
                                        }
                                    },
                                    zIndex: -1
                                });
                            }
                        }
                        // core to dis
                        campusFabricData?.nodes?.pods?.forEach(pod => {
                            pod.distribution.forEach(distributionNode => {
                                const sourceSn = node.store.data.switch_sn;
                                const targetSn = distributionNode.switch_sn;
                                let sourcePorts = [];
                                let targetPorts = [];
                                if (allSwitchPortResult) {
                                    sourcePorts = allSwitchPortResult?.core[sourceSn]?.to_dis || [];
                                    targetPorts =
                                        allSwitchPortResult?.pods[pod.pod_index]?.dis[targetSn]?.to_core || [];
                                }
                                const distEdgeKey = `${node.id}-${distributionNode.id}`;
                                const reverseDistEdgeKey = `${distributionNode.id}-${node.id}`;
                                let isMatchedAndTrue = false;
                                for (const sourcePort of sourcePorts) {
                                    for (const targetPort of targetPorts) {
                                        const keyA = `${sourceSn}:${sourcePort}->${targetSn}:${targetPort}`;
                                        const keyB = `${targetSn}:${targetPort}->${sourceSn}:${sourcePort}`;
                                        if (newLldpResult[keyA] === true || newLldpResult[keyB] === true) {
                                            isMatchedAndTrue = true;
                                            break;
                                        }
                                    }
                                    if (isMatchedAndTrue) break;
                                }
                                const edgeColor = isMatchedAndTrue ? "#2BC174" : "#D8D8D8";
                                if (!drawnEdges.has(distEdgeKey) && !drawnEdges.has(reverseDistEdgeKey)) {
                                    drawnEdges.add(distEdgeKey);
                                    drawnEdges.add(reverseDistEdgeKey);
                                    graph.addEdge({
                                        source: {cell: node.id, anchor: {name: "bottom"}},
                                        target: {cell: distributionNode.id, anchor: {name: "top", args: {dy: -20}}},
                                        attrs: {
                                            line: {stroke: edgeColor, strokeWidth: 1.5, targetMarker: null}
                                        },
                                        connector: {name: "smooth"},
                                        zIndex: -1
                                    });
                                }
                            });
                        });
                    } else if (currentNode.type === "distribution") {
                        const currentPod = campusFabricData?.nodes?.pods?.find(pod =>
                            pod.distribution.some(dNode => dNode.id === node.id)
                        );
                        if (currentPod) {
                            // dis to access
                            currentPod.access.forEach(accessNode => {
                                const sourceSn = node.store.data.switch_sn; // distribution
                                const targetSn = accessNode.switch_sn; // access
                                const sourcePorts =
                                    allSwitchPortResult.pods[currentPod.pod_index]?.dis[sourceSn]?.to_access || [];
                                const targetPorts =
                                    allSwitchPortResult.pods[currentPod.pod_index]?.access[targetSn]?.to_dis || [];
                                let isMatchedAndTrue = false;
                                for (const sourcePort of sourcePorts) {
                                    for (const targetPort of targetPorts) {
                                        const keyA = `${sourceSn}:${sourcePort}->${targetSn}:${targetPort}`;
                                        const keyB = `${targetSn}:${targetPort}->${sourceSn}:${sourcePort}`;
                                        if (newLldpResult[keyA] === true || newLldpResult[keyB] === true) {
                                            isMatchedAndTrue = true;
                                            break;
                                        }
                                    }
                                }
                                const accessEdgeKey = `${node.id}-${accessNode.id}`;
                                const reverseAccessEdgeKey = `${accessNode.id}-${node.id}`;
                                const edgeColor = isMatchedAndTrue ? "#2BC174" : "#D8D8D8";
                                if (!drawnEdges.has(accessEdgeKey) && !drawnEdges.has(reverseAccessEdgeKey)) {
                                    drawnEdges.add(accessEdgeKey);
                                    drawnEdges.add(reverseAccessEdgeKey);
                                    graph.addEdge({
                                        source: {cell: node.id, anchor: {name: "bottom"}},
                                        target: {cell: accessNode.id, anchor: {name: "top", args: {dy: -20}}},
                                        attrs: {
                                            line: {stroke: edgeColor, strokeWidth: 1.5, targetMarker: null}
                                        },
                                        connector: {name: "smooth"},
                                        zIndex: -1
                                    });
                                }
                            });
                        }
                        const coreNodes = campusFabricData?.nodes?.core || [];
                        // dis to core
                        coreNodes.forEach(coreNode => {
                            const coreEdgeKey = `${node.id}-${coreNode.id}`;
                            const reverseCoreEdgeKey = `${coreNode.id}-${node.id}`;
                            const sourceSn = node.store.data.switch_sn;
                            const targetSn = coreNode.switch_sn;
                            const sourcePorts =
                                allSwitchPortResult.pods[currentPod.pod_index]?.dis[sourceSn]?.to_core || [];
                            const targetPorts = allSwitchPortResult.core[targetSn]?.to_dis || [];
                            let isMatchedAndTrue = false;
                            for (const sourcePort of sourcePorts) {
                                for (const targetPort of targetPorts) {
                                    const keyA = `${sourceSn}:${sourcePort}->${targetSn}:${targetPort}`;
                                    const keyB = `${targetSn}:${targetPort}->${sourceSn}:${sourcePort}`;
                                    if (newLldpResult[keyA] === true || newLldpResult[keyB] === true) {
                                        isMatchedAndTrue = true;
                                        break;
                                    }
                                }
                            }
                            const edgeColor = isMatchedAndTrue ? "#2BC174" : "#D8D8D8";
                            if (!drawnEdges.has(coreEdgeKey) && !drawnEdges.has(reverseCoreEdgeKey)) {
                                drawnEdges.add(coreEdgeKey);
                                drawnEdges.add(reverseCoreEdgeKey);
                                graph.addEdge({
                                    source: {cell: node.id, anchor: {name: "top", args: {dy: -20}}},
                                    target: {cell: coreNode.id, anchor: {name: "bottom"}},
                                    attrs: {
                                        line: {stroke: edgeColor, strokeWidth: 1.5, targetMarker: null}
                                    },
                                    connector: {name: "smooth"},
                                    zIndex: -1
                                });
                            }
                        });
                    } else if (currentNode.type === "access") {
                        const currentPod = campusFabricData?.nodes?.pods?.find(pod =>
                            pod.access.some(aNode => aNode.id === node.id)
                        );
                        if (currentPod) {
                            // access to dis
                            currentPod.distribution.forEach(distributionNode => {
                                const accessDistEdgeKey = `${node.id}-${distributionNode.id}`;
                                const reverseAccessDistEdgeKey = `${distributionNode.id}-${node.id}`;
                                const sourceSn = node.store.data.switch_sn;
                                const targetSn = distributionNode.switch_sn;
                                const sourcePorts =
                                    allSwitchPortResult.pods[currentPod.pod_index]?.access?.[sourceSn]?.to_dis || [];
                                const targetPorts =
                                    allSwitchPortResult.pods[currentPod.pod_index]?.dis?.[targetSn]?.to_access || [];
                                let isMatchedAndTrue = false;
                                for (const sourcePort of sourcePorts) {
                                    for (const targetPort of targetPorts) {
                                        const keyA = `${sourceSn}:${sourcePort}->${targetSn}:${targetPort}`;
                                        const keyB = `${targetSn}:${targetPort}->${sourceSn}:${sourcePort}`;
                                        if (newLldpResult[keyA] === true || newLldpResult[keyB] === true) {
                                            isMatchedAndTrue = true;
                                            break;
                                        }
                                    }
                                    if (isMatchedAndTrue) break;
                                }
                                const edgeColor = isMatchedAndTrue ? "#2BC174" : "#D8D8D8";
                                if (!drawnEdges.has(accessDistEdgeKey) && !drawnEdges.has(reverseAccessDistEdgeKey)) {
                                    drawnEdges.add(accessDistEdgeKey);
                                    drawnEdges.add(reverseAccessDistEdgeKey);
                                    graph.addEdge({
                                        source: {cell: node.id, anchor: {name: "top", args: {dy: -20}}},
                                        target: {cell: distributionNode.id, anchor: {name: "bottom"}},
                                        attrs: {
                                            line: {stroke: edgeColor, strokeWidth: 1.5, targetMarker: null}
                                        },
                                        connector: {name: "smooth"},
                                        zIndex: -1
                                    });
                                }
                            });
                        }
                    } else if (currentNode.type === "border") {
                        // 点击border节点，显示border节点与core节点的连线
                        const coreNodes = campusFabricData?.nodes?.core || [];
                        coreNodes.forEach(coreNode => {
                            const coreEdgeKey = `${node.id}-${coreNode.id}`;
                            const reverseCoreEdgeKey = `${coreNode.id}-${node.id}`;
                            if (!drawnEdges.has(coreEdgeKey) && !drawnEdges.has(reverseCoreEdgeKey)) {
                                drawnEdges.add(coreEdgeKey);
                                drawnEdges.add(reverseCoreEdgeKey);
                                graph.addEdge({
                                    source: {cell: node.id, anchor: {name: "bottom"}},
                                    target: {cell: coreNode.id, anchor: {name: "top", args: {dy: -20}}},
                                    attrs: {
                                        line: {stroke: "#D8D8D8", strokeWidth: 1.5, targetMarker: null}
                                    },
                                    connector: {name: "smooth"},
                                    zIndex: -1
                                });
                            }
                        });
                    }
                }
            });
        };
        setTimeout(() => {
            if (campusFabricData?.nodes?.core?.length > 0) {
                const firstCoreNodeId = campusFabricData.nodes.core[0].id;
                const firstCoreNode = graph.getCellById(firstCoreNodeId);
                if (firstCoreNode) {
                    handleGraphUnhighlight();
                    const nodeView = graph.findViewByCell(firstCoreNode);
                    if (nodeView) {
                        nodeView.highlight(null, {
                            highlighter: {
                                name: "stroke",
                                args: {
                                    padding: 5,
                                    attrs: {
                                        stroke: "#A2ACB2",
                                        "stroke-width": 2,
                                        "stroke-dasharray": "5,5",
                                        transform: "scale(1, 0.8) translate(-0.5, -8)"
                                    }
                                }
                            }
                        });
                        deviceDetailedInformationRef.current.showDeviceDetailedInformation({
                            type,
                            nodeType:
                                (firstCoreNode?.store?.data?.type || "switch").charAt(0).toUpperCase() +
                                (firstCoreNode?.store?.data?.type || "switch").slice(1),
                            name: firstCoreNode?.store?.data?.label || "--",
                            "MAC Address": firstCoreNode?.store?.data?.mac_addr || "--",
                            Model: firstCoreNode?.store?.data?.model || "--",
                            Status: firstCoreNode?.store?.data?.status || "--",
                            Site: campusFabricData?.site_id || "--",
                            "Router ID": firstCoreNode?.store?.data?.router_id || "--",
                            sn: firstCoreNode?.store?.data?.switch_sn || "--",
                            mgtIp: firstCoreNode?.store?.data?.mgt_ip || "--",
                            vlans:
                                firstCoreNode?.store?.data?.other_ip_config?.map(item => ({
                                    id: item?.vlan_id,
                                    ipAddress: item?.ip_address || "--",
                                    name: item?.vlan_name
                                })) || [],
                            connectionsToCollapsedCore: Object.keys(
                                firstCoreNode?.store?.data?.links?.to_core ?? {}
                            ).map(portId => ({
                                switch: firstCoreNode?.store?.data?.label,
                                portId
                            })),
                            connectionsToAccess: Object.keys(firstCoreNode?.store?.data?.links?.to_access ?? {}).map(
                                portId => ({
                                    switch: firstCoreNode?.store?.data?.label,
                                    portId
                                })
                            ),
                            connectionsToDistribution: Object.keys(firstCoreNode?.store?.data?.links?.to_dis ?? {}).map(
                                portId => ({
                                    switch: firstCoreNode?.store?.data?.label,
                                    portId
                                })
                            ),
                            connectionsToBorder: Object.keys(firstCoreNode?.store?.data?.links?.to_border ?? {}).map(
                                portId => ({
                                    switch: firstCoreNode?.store?.data?.label,
                                    portId
                                })
                            ),
                            connectionsToWAN: Object.keys(firstCoreNode?.store?.data?.links?.to_wan ?? {}).map(
                                portId => ({
                                    switch: firstCoreNode?.store?.data?.label,
                                    portId
                                })
                            )
                        });
                    }
                    currentNodeRef.current = firstCoreNode;
                    drawEdge([firstCoreNode] || []);
                    updateAllEdgesVertices();
                }
            }
        }, 200);
        graph.on("node:click", ({node, e}) => {
            if (node.store?.data?.shape !== "switch") {
                handleGraphUnhighlight();
                deviceDetailedInformationRef.current.hideDeviceDetailedInformation();
                const nodes = graph.getNodes();
                currentNodeRef.current = nodes;
                drawEdge(nodes || []);
                updateAllEdgesVertices();
                return;
            }
            handleGraphUnhighlight();
            const nodeView = graph.findViewByCell(node);
            nodeView.highlight(null, {
                highlighter: {
                    name: "stroke",
                    args: {
                        padding: 5,
                        attrs: {
                            stroke: "#A2ACB2",
                            "stroke-width": 2,
                            "stroke-dasharray": "5,5",
                            transform: "scale(1, 0.8) translate(-0.5, -8)"
                        }
                    }
                }
            });
            if (deviceBriefTooltipRef.current) {
                deviceBriefTooltipRef.current.hideDeviceBriefTooltip();
            }
            clearAllEdges();
            currentNodeRef.current = node;
            drawEdge([node]);
            updateAllEdgesVertices();
            deviceDetailedInformationRef.current.showDeviceDetailedInformation({
                type,
                nodeType: node?.parent?.store?.data?.attrs?.label?.text || "switch",
                name: node?.store?.data?.label || "--",
                "MAC Address": node?.store?.data?.mac_addr || "--",
                Model: node?.store?.data?.model || "--",
                Status: node?.store?.data?.status || "--",
                Site: campusFabricData?.site_id || "--",
                "Router ID": node?.store?.data?.router_id || "--",
                sn: node?.store?.data?.switch_sn || "--",
                mgtIp: node?.store?.data?.mgt_ip || "--",
                vlans:
                    node?.store?.data?.other_ip_config?.map(item => ({
                        id: item?.vlan_id,
                        ipAddress: item?.ip_address || "--",
                        name: item?.vlan_name
                    })) || [],
                connectionsToCollapsedCore: Object.keys(node?.store?.data?.links?.to_core ?? {}).map(portId => ({
                    switch: node?.store?.data?.label,
                    portId
                })),
                connectionsToAccess: Object.keys(node?.store?.data?.links?.to_access ?? {}).map(portId => ({
                    switch: node?.store?.data?.label,
                    portId
                })),
                connectionsToDistribution: Object.keys(node?.store?.data?.links?.to_dis ?? {}).map(portId => ({
                    switch: node?.store?.data?.label,
                    portId
                })),
                connectionsToBorder: Object.keys(node?.store?.data?.links?.to_border ?? {}).map(portId => ({
                    switch: node?.store?.data?.label,
                    portId
                })),
                connectionsToWAN: Object.keys(node?.store?.data?.links?.to_wan ?? {}).map(portId => ({
                    switch: node?.store?.data?.label,
                    portId
                }))
            });
        });

        graph.on("blank:click", () => {
            clearAllEdges();
            handleGraphUnhighlight();
            deviceDetailedInformationRef.current.hideDeviceDetailedInformation();
            const nodes = graph.getNodes();
            currentNodeRef.current = nodes;
            drawEdge(nodes || []);
            updateAllEdgesVertices();
        });

        graph.on("node:mouseenter", async ({node}) => {
            if (node.store?.data?.shape !== "switch") {
                return;
            }
            if (deviceBriefTooltipRef.current) {
                deviceBriefTooltipRef.current.showDeviceBriefTooltip(
                    {
                        "Switch Name": node.store.data.label,
                        "MAC Address": node.store.data.mac_addr,
                        Model: node.store.data.model,
                        Status: node.store.data.status || "--",
                        Site: campusFabricData.site_id,
                        "Router ID": node.store.data.router_id,
                        SN: node.store.data.switch_sn
                    },
                    {type: "view", topologyType: "ip-clos"}
                );
            }
        });

        graph.on("node:mouseleave", ({node}) => {
            if (node.store?.data?.shape !== "switch") {
                return;
            }
            if (deviceBriefTooltipRef.current) {
                deviceBriefTooltipRef.current.hideDeviceBriefTooltip();
            }
        });

        resizeGraph(graph);

        if (containerRef.current) {
            const resizeObserver = new ResizeObserver(entries => {
                let offX = 0;
                for (const entry of entries) {
                    if (entry.contentRect.height === 0) {
                        offX = containerHeight;
                    } else {
                        offX = entry.contentRect.height;
                    }
                    setCardHeight(`${offX + 240}px`);
                }
            });
            resizeObserver.observe(containerRef.current);

            return () => resizeObserver.disconnect();
        }

        return () => {
            window.removeEventListener("resize", handleResize);
            graph.dispose();
        };
    }, [campusFabricData, forceRender, isDataLoaded]);

    const goToCreateView = async () => {
        const res = await fetchSiteTopo();
        if (!res) return;
        setCampusFabricData(res);
        setIsShowCreateView(true);
    };

    const goToStatusView = () => {
        setIsShowStatusView(true);
    };

    const backToTableViewCallback = () => {
        setCampusFabricData(resTopo);
        setIsShowCreateView(false);
        setIsShowStatusView(false);
        setIsEditing(false);
        setForceRender(!forceRender);
    };
    return (
        <>
            <Spin spinning={isShowSpin} tip="Loading..." fullscreen />

            {isShowCreateView && (
                <CampusFabricCreateAndEditView
                    backToTableViewCallback={backToTableViewCallback}
                    campusFabricData={campusFabricData}
                    setCampusFabricData={setCampusFabricData}
                    isEditing={isEditing}
                />
            )}

            {isShowStatusView && !isShowCreateView && (
                <CampusFabricSwitchStatus
                    backToTableViewCallback={backToTableViewCallback}
                    campusFabricData={campusFabricData}
                    setCampusFabricData={setCampusFabricData}
                />
            )}

            {!isShowCreateView && !isShowStatusView && (
                <>
                    <DeviceBriefTooltip ref={deviceBriefTooltipRef} />
                    <Card style={{position: "relative", height: cardHeight, width: "100%"}}>
                        <p className={styles.goBack} onClick={() => window.history.back()}>
                            <ArrowLeftOutlined style={{marginRight: "8px"}} />
                            <span>Back</span>
                        </p>
                        <Flex
                            style={{
                                position: "absolute",
                                top: "74px",
                                right: "24px",
                                alignItems: "flex-end",
                                zIndex: 10
                            }}
                        >
                            <Button
                                style={{marginRight: 16}}
                                icon={<Icon component={DepStatus} style={{height: 20, transform: "scale(1.2)"}} />}
                                onClick={goToStatusView}
                            >
                                Deployment Status
                            </Button>
                            <Button
                                style={{marginRight: 16}}
                                icon={<Icon component={EditSvg} style={{height: 20}} />}
                                onClick={() => {
                                    goToCreateView();
                                    setIsEditing(true);
                                }}
                            >
                                Edit Config
                            </Button>
                            <Button
                                icon={<Icon component={deleteSvg} style={{height: 20}} />}
                                onClick={deleteSiteTopoCallback}
                            >
                                Delete
                            </Button>
                        </Flex>
                        <h2>{state?.data?.topology_name}</h2>
                        <div
                            ref={containerRef}
                            style={{
                                backgroundColor: "#FFFFFF",
                                flex: 1,
                                minHeight: "calc(100% - 105px)",
                                minWidth: "calc(100% - 24px)",
                                overflow: "hidden",
                                position: "relative"
                            }}
                        />
                    </Card>
                    <IPCLOSServiceDetailedInformation
                        ref={deviceDetailedInformationRef}
                        handleGraphUnhighlight={handleGraphUnhighlight}
                        containerHeight={window.innerHeight - 220}
                    />
                </>
            )}
        </>
    );
};

export default CampusFabricIPCLOSRecordView;
