import {forwardRef, useEffect, useImperative<PERSON>andle, useState} from "react";
import {
    AmpConCustomModalTable,
    createColumnConfigMultipleParams,
    TableFilterDropdown
} from "@/modules-ampcon/components/custom_table";
import {querySwitchPinBySNList} from "@/modules-ampcon/apis/config_api";
import {Button, Divider, Flex, message, Space} from "antd";
import {onlineSvg, offlineSvg, exclamationSvg} from "@/utils/common/iconSvg";
import Icon from "@ant-design/icons/lib/components/Icon";
import styles from "@/modules-ampcon/pages/System/user_management.module.scss";

const UpdateClientTableModal = forwardRef((props, ref) => {
    const {saveCallback} = props;

    const title = "Select Associated Switch";

    const matchFieldsList = [
        {name: "host_name", matchMode: "fuzzy"},
        {name: "sn", matchMode: "fuzzy"},
        {name: "platform_model", matchMode: "fuzzy"},
        {name: "status", matchMode: "fuzzy"},
        {name: "mgt_ip", matchMode: "fuzzy"}
    ];
    const searchFieldsList = ["host_name", "sn", "platform_model", "status", "mgt_ip"];
    const [selectedSwitchSN, setSelectedSwitchSN] = useState(null);
    const [isShowModal, setIsShowModal] = useState(false);

    const rowSelection = {
        selectedRowKeys: selectedSwitchSN ? [selectedSwitchSN] : [],
        onChange: (selectedRowKeys, selectedRows) => {
            setSelectedSwitchSN(selectedRows[0].sn);
            saveCallback([selectedRows[0].sn]);
            setIsShowModal(false);
        }
    };

    useImperativeHandle(ref, () => ({
        showCreateClientTableModal: switchSN => {
            if (switchSN !== undefined) {
                setSelectedSwitchSN(switchSN);
            } else {
                setSelectedSwitchSN(null);
            }
            setIsShowModal(true);
        },
        hideCreateClientTableModal: () => {
            setIsShowModal(false);
        }
    }));

    useEffect(() => {
        if (isShowModal) {
            setSelectedSwitchSN(null);
        }
    }, [isShowModal]);

    return (
        <AmpConCustomModalTable
            modalClass="ampcon-max-modal"
            title={title}
            selectModalOpen={isShowModal}
            rowSelection={{type: "radio", ...rowSelection}}
            onCancel={() => {
                setIsShowModal(false);
            }}
            columns={[
                createColumnConfigMultipleParams({
                    title: "Sysname",
                    dataIndex: "host_name",
                    filterDropdownComponent: TableFilterDropdown,
                    width: "20%"
                }),
                createColumnConfigMultipleParams({
                    title: "SN / Service Tag",
                    dataIndex: "sn",
                    filterDropdownComponent: TableFilterDropdown,
                    width: "19%"
                }),
                createColumnConfigMultipleParams({
                    title: "Model",
                    dataIndex: "platform_model",
                    filterDropdownComponent: TableFilterDropdown,
                    width: "15%"
                }),
                createColumnConfigMultipleParams({
                    title: "Version",
                    width: "15%",
                    enableSorter: false,
                    enableFilter: false,
                    render: (_, record) => {
                        if (record.revision === null) {
                            return "";
                        }
                        if (record.version === null) {
                            return `${record.version}`;
                        }
                        return `${record.version}/${record.revision}`;
                    }
                }),
                createColumnConfigMultipleParams({
                    title: "Status",
                    dataIndex: "status",
                    filterDropdownComponent: TableFilterDropdown,
                    width: "15%"
                }),
                createColumnConfigMultipleParams({
                    title: "Mgmt IP",
                    dataIndex: "mgt_ip",
                    filterDropdownComponent: TableFilterDropdown,
                    width: "15%",
                    render: (_, record) => {
                        if (!record.mgt_ip) {
                            return null;
                        }
                        let iconComponent;
                        if (record.reachable_status === 0) {
                            iconComponent = onlineSvg;
                        } else if (record.reachable_status === 1) {
                            iconComponent = offlineSvg;
                        } else {
                            iconComponent = exclamationSvg;
                        }

                        return (
                            <Space>
                                <Icon component={iconComponent} />
                                {record.link_ip_addr ? `${record.mgt_ip}/${record.link_ip_addr}` : record.mgt_ip}
                            </Space>
                        );
                    }
                })
            ]}
            matchFieldsList={matchFieldsList}
            searchFieldsList={searchFieldsList}
            buttonProps={[]}
            fetchAPIInfo={querySwitchPinBySNList}
            fetchAPIParams={[selectedSwitchSN === null ? [] : [selectedSwitchSN]]}
        />
    );
});

export default UpdateClientTableModal;
