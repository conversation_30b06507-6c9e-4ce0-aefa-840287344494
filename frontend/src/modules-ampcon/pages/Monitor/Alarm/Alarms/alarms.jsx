import React, {useState, useEffect, useRef} from "react";
import {Card, Tag, Space, message, Button} from "antd";
import {
    AmpConCustomTable,
    createColumnConfig,
    TableSelectDropdown,
    TableTimeFilterDropdown,
    TableFilterDropdown
} from "@/modules-ampcon/components/custom_table";
import {
    getAllAlarmList,
    updateAlarmStatus,
    getUnreadAlarmList,
    removeAlarmsAPI
} from "@/modules-ampcon/apis/monitor_api";
import {getAlarmCount, updateAlarmSearch, updateAlarmSearchStatus} from "@/store/modules/common/alarm_slice";
import {useDispatch, useSelector} from "react-redux";
import styles from "@/modules-ampcon/pages/Monitor/Alarm/alarm.module.scss";
import Icon, {CheckCircleOutlined} from "@ant-design/icons";
import {allMessagesSvg} from "@/utils/common/iconSvg";
import {confirmModalAction} from "@/modules-ampcon/components/custom_modal";

const Alarms = () => {
    const alarmRef = useRef(null);
    const dispatch = useDispatch();
    const alarmSearch = useSelector(state => state.alarm.alarmSearch);
    const [searchTxt, setSearchTxt] = useState(alarmSearch);
    const count = useSelector(state => state.alarm.alarmClickCount);
    const [searchCount, setSearchCount] = useState(count);
    const [showAll, setShowAll] = useState(false);
    const isFirstRender = useRef(true);
    const [selectedRowKeys, setSelectedRowKeys] = useState([]);

    useEffect(() => {
        if (searchTxt) {
            return;
        }
        alarmRef.current.refreshTable();
    }, []);

    useEffect(() => {
        setSearchTxt(alarmSearch === "" ? searchTxt : alarmSearch);
        setSearchCount(count);
    }, [count]);

    useEffect(() => {
        if (isFirstRender.current) {
            isFirstRender.current = false;
            return;
        }
        setSelectedRowKeys([]);

        alarmRef.current.refreshTable();
    }, [showAll]);

    const handleToggleView = () => {
        setShowAll(!showAll);
    };

    const handleBatchOperation = keys => {
        if (showAll) {
            handleRemove(keys);
        } else {
            handleMarkAsRead(keys);
        }
    };

    const handleMarkAsRead = async recordId => {
        if (!recordId || (Array.isArray(recordId) && recordId.length === 0)) return;
        const recordIds = Array.isArray(recordId) ? recordId : [recordId];
        try {
            await Promise.all(recordIds.map(id => updateAlarmStatus(id)));
            message.success("Marked selected alarms as read.");
            setSelectedRowKeys([]);
            alarmRef.current.refreshTable();
            dispatch(getAlarmCount());
            dispatch(updateAlarmSearch(""));
            dispatch(updateAlarmSearchStatus(false));
        } catch (error) {
            message.error("Failed to mark some alarms as read.");
        }
    };

    const handleRemove = async recordId => {
        if (!recordId || (Array.isArray(recordId) && recordId.length === 0)) return;
        const recordIds = Array.isArray(recordId) ? recordId : [recordId];
        confirmModalAction(
            "This action will remove this alarm, Do you want to continue?",
            async () => {
                try {
                    await Promise.all(
                        recordIds.map(id =>
                            removeAlarmsAPI(id).then(res => {
                                if (res.status !== 200) {
                                    throw new Error(res.msg);
                                }
                            })
                        )
                    );
                    message.success("Removed selected alarms successfully.");
                    setSelectedRowKeys([]);
                    alarmRef.current.refreshTable();
                } catch (error) {
                    message.error("Failed to remove some alarms.");
                }
            },
            () => {},
            true
        );
    };

    const tagStyle = type => {
        if (type === "info") {
            return <Tag className={styles.infoStyle}>Info</Tag>;
        }
        if (type === "warn") {
            return <Tag className={styles.warnStyle}>Warning</Tag>;
        }
        if (type === "error") {
            return <Tag className={styles.errorStyle}>Error</Tag>;
        }
    };

    const alarmColumns = [
        createColumnConfig("Last Time", "modified_time", TableTimeFilterDropdown, "", "18%"),
        createColumnConfig("Switch SN", "sn", TableFilterDropdown, "", "16%"),
        {
            ...createColumnConfig("Type", "type", TableSelectDropdown, searchTxt, "16%"),
            render: (_, record) => (
                <div>
                    <Space>{tagStyle(record.type)}</Space>
                </div>
            )
        },
        {
            ...createColumnConfig("Message", "msg", null, "", "40%"),
            render: (_, record) => <div style={{"text-wrap": "auto", whiteSpace: "normal"}}>{record.msg}</div>
        },
        createColumnConfig("Count", "count", null, "", "10%"),
        {
            title: "Operation",
            render: (_, record) => (
                <Space size="large" className="actionLink">
                    {!showAll && <a onClick={() => handleMarkAsRead(record.id)}>Mark as read</a>}
                    {showAll && <a onClick={() => handleRemove(record.id)}>Remove</a>}
                </Space>
            )
        }
    ];

    const alarmSearchFieldsList = ["modified_time", "sn", "msg", "type"];
    const alarmMatchFieldsList = [
        {name: "type", matchMode: "fuzzy"},
        {name: "sn", matchMode: "fuzzy"}
    ];

    const onSelectChange = newSelectedRowKeys => {
        setSelectedRowKeys(newSelectedRowKeys);
    };

    return (
        <Card style={{flex: 1}} className="alarmTablestyle">
            <h2 style={{margin: "8px 0 20px"}}>Alert List</h2>
            <AmpConCustomTable
                disableInternalRowSelection
                rowSelection={{selectedRowKeys, onChange: onSelectChange}}
                columns={alarmColumns}
                fetchAPIInfo={showAll ? getAllAlarmList : getUnreadAlarmList}
                searchFieldsList={alarmSearchFieldsList}
                matchFieldsList={alarmMatchFieldsList}
                ref={alarmRef}
                readTag={!showAll}
                extraButton={
                    <>
                        <Button type="primary" onClick={handleToggleView}>
                            <Icon component={allMessagesSvg} />
                            {showAll ? "Back to Alarms" : "All Messages"}
                        </Button>
                        <Button
                            onClick={() => handleBatchOperation(selectedRowKeys)}
                            disabled={selectedRowKeys.length === 0}
                        >
                            <Icon component={CheckCircleOutlined} />
                            {showAll ? "Remove Selected" : "Mark Selected as Read"}
                        </Button>
                    </>
                }
            />
        </Card>
    );
};

export default Alarms;
