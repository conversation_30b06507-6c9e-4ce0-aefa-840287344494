import React, {useEffect, useState} from "react";
import {Tabs} from "antd";
import {useLocation, useNavigate} from "react-router-dom";
import ProtectedRoute from "@/modules-ampcon/utils/util";
import TelemetryView from "@/modules-ampcon/pages/Dashboard/Telemetry/telemetry_view";
import NICsMonitoring from "@/modules-ampcon/pages/Service/Nics/Monitoring/nic_monitoring";
import styles from "./performance_statistics_index.module.scss";

const items = [
    {
        key: "switch",
        label: "Switch",
        children: <ProtectedRoute component={TelemetryView} />
    },
    {
        key: "nic",
        label: "NIC",
        children: <ProtectedRoute component={NICsMonitoring} />
    }
];

const PerformanceStatisticsIndex = () => {
    const navigate = useNavigate();
    const location = useLocation();
    const [currentActiveKey, setCurrentActiveKey] = useState();

    useEffect(() => {
        const currentPath = location.pathname;
        if (/(nic|switch)$/.test(currentPath)) {
            setCurrentActiveKey(currentPath.match(/(nic|switch)$/)[0]);
        }
    }, [location.pathname]);

    const onChange = key => {
        const currentPath = location.pathname;
        if (/(nic|switch)$/.test(currentPath)) {
            const matchLength = currentPath.match(/(nic|switch)$/)[0].length;
            const parentPath = currentPath.slice(0, -matchLength);
            navigate(parentPath + key);
        }
    };

    return (
        <div className={styles.performanceStatisticsTabPageDC}>
            <Tabs activeKey={currentActiveKey} items={items} onChange={onChange} destroyInactiveTabPane />
        </div>
    );
};

export default PerformanceStatisticsIndex;
