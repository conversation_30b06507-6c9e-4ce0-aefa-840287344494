import React, {useEffect, useRef, useState} from "react";
import {<PERSON>, Button, Modal, Divider, Form, Input, message, Row, Col, Space, Card} from "antd";
import styles from "@/modules-ampcon/pages/System/user_management.module.scss";
import {
    addPlaybook,
    checkPlaybookName,
    editPlaybook,
    removePlaybookAPI,
    fetchPlaybookFileContent
} from "@/modules-ampcon/apis/automation_api";
import Icon from "@ant-design/icons";
import TextEditor from "@/modules-ampcon/components/text_editor";
import {cloneDeep} from "lodash";
import {addSvg, addGreySvg, deleteSvg, deleteGreySvg} from "@/utils/common/iconSvg";

const {DirectoryTree} = Tree;

const PlaybookEditor = ({
    setIsModalOpen,
    treeData,
    setTreeData,
    isSaveAllDisabled,
    playbookName,
    setPlaybookName,
    playbookDesc,
    setPlaybookDesc,
    isModalOpenEdit,
    checkStatus,
    setCheckStatus,
    checkHelp,
    setCheckHelp
}) => {
    const [isModalOpenFile, setIsModalOpenFile] = useState(false);
    const [isModalOpenFolder, setIsModalOpenFolder] = useState(false);
    const [editorContent, setEditorContent] = useState("");
    const [selectTreeNode, setSelectTreeNode] = useState("");
    const [editable, setEditable] = useState(false);
    const editorRef = useRef(null);
    const [editorKey, setEditorKey] = useState(0);
    const [screenHeight, setScreenHeight] = useState(window.innerHeight);
    const [addFileForm] = Form.useForm();
    const [addFolderForm] = Form.useForm();

    useEffect(() => {
        const handleResize = () => {
            setScreenHeight(window.innerHeight);
        };
        window.addEventListener("resize", handleResize);
        return () => {
            window.removeEventListener("resize", handleResize);
        };
    }, []);
    const convertFromTreeData = data => {
        const fileList = [];

        function traverse(node) {
            const item = {
                name: node.title,
                path: node.key,
                isFolder: !!node.children,
                fileContent: node.file_content || ""
            };

            fileList.push(item);

            if (node.children) {
                node.children.forEach(child => {
                    traverse(child);
                });
            }
        }

        data.forEach(item => {
            traverse(item);
        });

        return fileList;
    };

    const addNodeRecursively = (nodes, currentNode, value, isFolder) => {
        nodes.forEach(node => {
            if (node.key === currentNode.key) {
                node.children = node.children || [];
                const isDuplicate = node.children.some(child => child.key === `${node.key}/${value}`);
                if (isDuplicate) {
                    message.error(isFolder ? "Folder already exists" : "File already exists");
                    return;
                }
                node.children.push({
                    title: `${value}`,
                    key: `${node.key}/${value}`,
                    ...(isFolder ? {children: []} : {isLeaf: true})
                });
            } else if (node.children) {
                addNodeRecursively(node.children, currentNode, value, isFolder);
            }
        });
    };

    const findTreeDataContent = (treeData, currentNode) => {
        let curNodeContent = "";
        treeData.forEach(node => {
            if (node.key === currentNode.key) {
                curNodeContent = node.file_content || "";
                return;
            }
            if (node.children) {
                const content = findTreeDataContent(node.children, currentNode);
                if (content) {
                    curNodeContent = content;
                    return;
                }
            }
        });
        return curNodeContent;
    };

    const updateNode = (found, nodes, currentNode, currentNodeInfo) => {
        if (found) return;
        nodes.forEach(node => {
            if (node.key === currentNode.key) {
                node.file_content = currentNodeInfo || editorRef.current.getValue();
                found = true;
                return;
            }
            if (node.children && !found) {
                updateNode(found, node.children, currentNode, currentNodeInfo);
            }
        });
    };

    const updatePreData = () => {
        const newTreeData = [...treeData];
        if (newTreeData) {
            updateNode(false, newTreeData, selectTreeNode, "");
            setTreeData(newTreeData);
        }
    };

    const [clickedNode, setClickedNode] = useState([]);
    const [oldData, setOldData] = useState([]);
    useEffect(() => {
        setOldData(cloneDeep(treeData));
    }, []);

    const handleTreeSelect = async (_, {node}) => {
        if (isModalOpenEdit && node.isLeaf) {
            if (!clickedNode.some(clicked => clicked.key === node.key)) {
                await fetchPlaybookFileContent({filepath: node.key}).then(res => {
                    const newTreeData = [...treeData];
                    if (res.status === 200) {
                        if (newTreeData) {
                            updateNode(false, newTreeData, node, res.info);
                            setTreeData(newTreeData);
                        }
                    } else {
                        updateNode(false, newTreeData, node, "");
                        setTreeData(newTreeData);
                    }
                    setClickedNode([...clickedNode, node]);
                });
            }
        }

        updatePreData();

        setSelectTreeNode(node);

        if (node.isLeaf) {
            setEditorContent(findTreeDataContent(treeData, node));
            setEditable(true);
        } else {
            setEditorContent("");
            setEditable(false);
        }
        setEditorKey(prevState => prevState + 1);
    };

    const compareTrees = (tree1, tree2) => {
        const editEvents = {
            add_folder: [],
            add_file: [],
            modify: {},
            delete_folder: [],
            delete_file: []
        };

        const isFileNode = node => {
            return Object.prototype.hasOwnProperty.call(node, "isLeaf");
        };

        const compareNodes = (node1, node2, path) => {
            if (isFileNode(node1) && isFileNode(node2)) {
                if (node1.file_content !== node2.file_content) {
                    editEvents.modify[node1.key] = node2.file_content;
                }
            } else {
                const children1 = node1.children || [];
                const children2 = node2.children || [];

                const keys1 = new Set(children1.map(child => child.key));
                const keys2 = new Set(children2.map(child => child.key));

                children2.forEach(child => {
                    if (!keys1.has(child.key)) {
                        if (isFileNode(child)) {
                            editEvents.add_file.push(child.key);
                            // 查找该文件对应的文件夹
                            const parentFolderKey = path.join("/");
                            if (parentFolderKey !== "") {
                                if (!editEvents.add_folder.includes(parentFolderKey)) {
                                    editEvents.add_folder.push(parentFolderKey);
                                }
                            }
                            editEvents.modify[child.key] = "";
                        } else {
                            editEvents.add_folder.push(child.key);
                            const matchingChild = children1.find(child1 => child1.key === child.key);
                            if (matchingChild) {
                                compareNodes(child, matchingChild, [...path, child.title]);
                            } else {
                                compareNodes({}, child, [...path, child.title]);
                            }
                        }
                    }
                });

                children1.forEach(child => {
                    if (!keys2.has(child.key)) {
                        if (isFileNode(child)) {
                            editEvents.delete_file.push(child.key);
                        } else {
                            editEvents.delete_folder.push(child.key);
                        }
                    }
                });

                children1.forEach(child1 => {
                    const matchingChild = children2.find(child2 => child1.key === child2.key);
                    if (matchingChild) {
                        compareNodes(child1, matchingChild, [...path, child1.title]);
                    }
                });
            }
        };

        compareNodes(tree1[0], tree2[0], []);

        return editEvents;
    };

    const onClear = () => {
        setCheckStatus("");
        setCheckHelp(null);
        setTreeData([]);
        setPlaybookName("");
        setPlaybookDesc("");
    };

    const onAddFileOk = async () => {
        try {
            await addFileForm.validateFields();
        } catch (error) {
            return;
        }
        const {fileName} = addFileForm.getFieldsValue();
        if (!fileName.trim()) {
            message.error("File name is required.");
            return;
        }
        const newTreeData = [...treeData];
        addNodeRecursively(newTreeData, selectTreeNode, fileName);
        setTreeData(newTreeData);
        setIsModalOpenFile(false);
        addFileForm.resetFields();
    };

    const onAddFileCancel = () => {
        setIsModalOpenFile(false);
        addFileForm.resetFields();
    };

    const onAddFolderOk = async () => {
        try {
            await addFolderForm.validateFields();
        } catch (error) {
            return;
        }
        const {folderName} = addFolderForm.getFieldsValue();
        if (!folderName.trim()) {
            message.error("Folder name is required.");
            return;
        }
        const newTreeData = [...treeData];
        addNodeRecursively(newTreeData, selectTreeNode, folderName, true);
        setTreeData(newTreeData);
        setIsModalOpenFolder(false);
        addFolderForm.resetFields();
    };

    const onAddFolderCancel = () => {
        setIsModalOpenFolder(false);
        addFolderForm.resetFields();
    };

    return (
        <Card>
            <Modal
                title={
                    <div>
                        Add File <Divider style={{marginTop: 8, marginBottom: 0}} />
                    </div>
                }
                className="ampcon-middle-modal"
                open={isModalOpenFile}
                destroyOnClose
                onText="Add"
                onCancel={onAddFileCancel}
                onOk={onAddFileOk}
                footer={[
                    <Divider style={{marginTop: 0, marginBottom: 20}} />,
                    <Button key="cancel" onClick={onAddFileCancel}>
                        Cancel
                    </Button>,
                    <Button key="ok" type="primary" onClick={onAddFileOk}>
                        OK
                    </Button>
                ]}
            >
                <Form labelAlign="left" style={{minHeight: "268px"}} form={addFileForm}>
                    <Form.Item
                        label="Name"
                        name="fileName"
                        labelCol={{span: 3}}
                        // wrapperCol={{span: 14}}
                        rules={[
                            {required: true, message: "Name is required!"},
                            {max: 32, message: "Enter a maximum of 32 characters"},
                            {
                                validator: (_, value) => {
                                    if (value === "All") {
                                        return Promise.reject(new Error("Please input a valid name!"));
                                    }
                                    if (value.trim() !== value) {
                                        return Promise.reject(
                                            new Error("Name should not have leading or trailing spaces.")
                                        );
                                    }
                                    if (value.includes(" ")) {
                                        return Promise.reject(new Error("Name should not have internal spaces."));
                                    }
                                    if (!/^[\w.:-]+$/.test(value)) {
                                        return Promise.reject(
                                            new Error(
                                                "Name can only contain letters, numbers, underscores, hyphens, colons, and periods."
                                            )
                                        );
                                    }
                                    return Promise.resolve();
                                }
                            }
                        ]}
                    >
                        <Input style={{width: "280px"}} />
                    </Form.Item>
                </Form>
            </Modal>

            <Modal
                title={
                    <div>
                        Add Folder <Divider style={{marginTop: 8, marginBottom: 0}} />
                    </div>
                }
                className="ampcon-middle-modal"
                onCancel={onAddFolderCancel}
                open={isModalOpenFolder}
                destroyOnClose
                onOk={onAddFolderOk}
                onText="Add"
                footer={[
                    <Divider style={{marginTop: 0, marginBottom: 20}} />,
                    <Button key="cancel" onClick={onAddFolderCancel}>
                        Cancel
                    </Button>,
                    <Button key="ok" type="primary" onClick={onAddFolderOk}>
                        OK
                    </Button>
                ]}
            >
                <Form labelAlign="left" style={{minHeight: "268px"}} form={addFolderForm}>
                    <Form.Item
                        label="Name"
                        name="folderName"
                        labelCol={{span: 3}}
                        // wrapperCol={{span: 14}}
                        rules={[
                            {required: true, message: "name is required!"},
                            {max: 32, message: "Enter a maximum of 32 characters"},
                            {
                                validator: (_, value) => {
                                    if (value === "All") {
                                        return Promise.reject(new Error("Please input a valid name!"));
                                    }
                                    if (value.trim() !== value) {
                                        return Promise.reject(
                                            new Error("Name should not have leading or trailing spaces.")
                                        );
                                    }
                                    if (value.includes(" ")) {
                                        return Promise.reject(new Error("Name should not have internal spaces."));
                                    }
                                    if (!/^[\w.:-]+$/.test(value)) {
                                        return Promise.reject(
                                            new Error(
                                                "Name can only contain letters, numbers, underscores, hyphens, colons, and periods."
                                            )
                                        );
                                    }
                                    return Promise.resolve();
                                }
                            }
                        ]}
                    >
                        <Input style={{width: "280px"}} />
                    </Form.Item>
                </Form>
            </Modal>
            <Form>
                <Row style={{marginTop: "10px"}}>
                    <Col span={8}>
                        <Form.Item
                            name="playbookName"
                            label="Name"
                            validateStatus={checkStatus}
                            help={checkHelp}
                            rules={[{required: true, message: "Please input playbook name !"}]}
                            initialValue={playbookName}
                        >
                            <Input
                                placeholder="Playbook Name"
                                className={styles.formWidth}
                                {...(isModalOpenEdit ? {disabled: true} : null)}
                                onChange={e => {
                                    if (e.target.value === "") {
                                        setCheckHelp(null);
                                        setCheckStatus("");
                                        setTreeData([]);
                                        setPlaybookName(e.target.value);
                                        setCheckStatus("error");
                                    } else {
                                        checkPlaybookName(
                                            e.target.value.trim() === "" ? `"${e.target.value}"` : e.target.value
                                        )
                                            .then(res => {
                                                if (res.status === 200) {
                                                    setCheckHelp(null);
                                                    setCheckStatus("success");
                                                    setTreeData([
                                                        {
                                                            title: e.target.value,
                                                            key: e.target.value,
                                                            children: [
                                                                {
                                                                    title: "playbook.yml",
                                                                    key: `${e.target.value}/playbook.yml`,
                                                                    isLeaf: true
                                                                }
                                                            ]
                                                        }
                                                    ]);
                                                    setPlaybookName(e.target.value);
                                                } else {
                                                    setCheckStatus("error");
                                                    setCheckHelp(res.info);
                                                    setTreeData([]);
                                                    setPlaybookName(e.target.value);
                                                }
                                            })
                                            .catch(() => {
                                                setCheckStatus("error");
                                                setCheckHelp("Failed to check playbook name");
                                            });
                                    }
                                }}
                            />
                        </Form.Item>
                    </Col>
                    <Col span={9}>
                        <Form.Item name="playbookDesc" label="Description" initialValue={playbookDesc}>
                            <Input
                                placeholder="Description"
                                className={styles.formWidth}
                                onChange={e => {
                                    setPlaybookDesc(e.target.value);
                                }}
                                {...(isModalOpenEdit ? {disabled: true} : null)}
                            />
                        </Form.Item>
                    </Col>
                </Row>
                <Row>
                    <Col span={8}>
                        <Space size="middle" style={{marginBottom: "20px"}}>
                            <Button
                                style={{height: "32px"}}
                                type="primary"
                                onClick={() => {
                                    setIsModalOpenFile(true);
                                }}
                                {...(selectTreeNode && selectTreeNode.children ? null : {disabled: true})}
                            >
                                {selectTreeNode && selectTreeNode.children ? (
                                    <Icon component={addSvg} />
                                ) : (
                                    <Icon component={addGreySvg} />
                                )}
                                Add File
                            </Button>
                            <Button
                                style={{height: "32px"}}
                                type="primary"
                                onClick={() => {
                                    setIsModalOpenFolder(true);
                                }}
                                {...(selectTreeNode && selectTreeNode.children ? null : {disabled: true})}
                            >
                                {selectTreeNode && selectTreeNode.children ? (
                                    <Icon component={addSvg} />
                                ) : (
                                    <Icon component={addGreySvg} />
                                )}
                                Add Folder
                            </Button>

                            <Button
                                style={{height: "32px"}}
                                htmlType="button"
                                onClick={() => {
                                    if (!isModalOpenEdit) {
                                        const newTreeData = [...treeData];
                                        const deleteNode = (nodes, currentNode) => {
                                            nodes.forEach(node => {
                                                if (node.key === currentNode.key) {
                                                    nodes.splice(nodes.indexOf(node), 1);
                                                    return;
                                                }
                                                if (node.children) {
                                                    deleteNode(node.children, currentNode);
                                                }
                                            });
                                        };
                                        deleteNode(newTreeData, selectTreeNode);
                                        setTreeData(newTreeData);
                                        if (newTreeData.length === 0) {
                                            onClear();
                                        }
                                    } else {
                                        const newTreeData = [...treeData];
                                        const deleteNode = (nodes, currentNode) => {
                                            nodes.forEach(node => {
                                                if (node.key === currentNode.key) {
                                                    nodes.splice(nodes.indexOf(node), 1);
                                                    return;
                                                }
                                                if (node.children) {
                                                    deleteNode(node.children, currentNode);
                                                }
                                            });
                                        };
                                        deleteNode(newTreeData, selectTreeNode);
                                        setTreeData(newTreeData);
                                        if (newTreeData.length === 0) {
                                            onClear();
                                            removePlaybookAPI(selectTreeNode.key).then(res => {
                                                if (res.status === 200) {
                                                    setIsModalOpen(false);
                                                    message.success(res.info);
                                                } else {
                                                    message.error(res.info);
                                                }
                                            });
                                        }
                                    }
                                }}
                                {...(selectTreeNode ? null : {disabled: true})}
                            >
                                {selectTreeNode ? <Icon component={deleteSvg} /> : <Icon component={deleteGreySvg} />}
                                Delete
                            </Button>
                        </Space>
                        {treeData ? (
                            <DirectoryTree
                                // autoExpandParent
                                defaultExpandAll
                                onSelect={handleTreeSelect}
                                treeData={treeData}
                                height={0.54 * screenHeight}
                            />
                        ) : null}
                    </Col>
                    <Col span={16}>
                        <TextEditor ref={editorRef} code={editorContent} isEditable={editable} key={editorKey} />
                    </Col>
                </Row>
                <Row justify="end" style={{marginTop: "20px"}}>
                    <Col>
                        <Button
                            type="primary"
                            {...(isSaveAllDisabled ? {disabled: true} : null)}
                            onClick={async () => {
                                if (!isModalOpenEdit) {
                                    updatePreData();
                                    await addPlaybook({
                                        name: playbookName,
                                        description: playbookDesc,
                                        filelist: convertFromTreeData(treeData)
                                    }).then(res => {
                                        if (res.status === 200) {
                                            setIsModalOpen(false);
                                            message.success(res.info);
                                        } else {
                                            message.error(res.info);
                                        }
                                        onClear();
                                    });
                                } else {
                                    clickedNode.forEach(node => {
                                        fetchPlaybookFileContent({filepath: node.key}).then(res => {
                                            const newTreeData = [...oldData];
                                            if (newTreeData) {
                                                updateNode(false, newTreeData, node, res.info);
                                                setTreeData(newTreeData);
                                            }
                                        });
                                    });
                                    updatePreData();
                                    const editEvents = compareTrees(oldData, treeData);
                                    await editPlaybook({
                                        name: playbookName,
                                        description: playbookDesc,
                                        editEvents
                                    }).then(res => {
                                        if (res.status === 200) {
                                            message.success(res.info);
                                            setIsModalOpen(false);
                                        } else {
                                            message.error(res.info);
                                        }
                                        onClear();
                                    });
                                }
                                setClickedNode([]);
                            }}
                        >
                            Save All
                        </Button>
                    </Col>
                </Row>
            </Form>
        </Card>
    );
};

export default PlaybookEditor;
