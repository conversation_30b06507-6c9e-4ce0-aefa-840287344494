import {forwardRef, useImperativeHandle, useState} from "react";
import {<PERSON><PERSON>, But<PERSON>, Checkbox, Divider, Flex, Modal} from "antd";
import {AmpConCustomTable, createColumnConfig, TableFilterDropdown} from "@/modules-ampcon/components/custom_table";
import {InfoSvg} from "@/utils/common/iconSvg";
import {exportHwidExcel, getQuickActivationTableData} from "@/modules-ampcon/apis/config_api";
import dayjs from "dayjs";

const CreateDownloadModel = forwardRef((props, ref) => {
    const [isShowModal, setIsShowModal] = useState(false);
    const [selectedRowKeys, setSelectedRowKeys] = useState([]);
    const [selectedRows, setSelectedRows] = useState([]);
    const searchFieldsList = ["model", "sn", "hardware_id"];
    const [checkedValues, setCheckedValues] = useState(["Hardware ID"]);
    useImperativeHandle(ref, () => ({
        showCreateDownloadModal: () => {
            // 重置选中状态
            setSelectedRowKeys([]);
            setSelectedRows([]);
            setCheckedValues(["Hardware ID"]);
            setIsShowModal(true);
        },
        hideCreateDownloadModal: () => {
            setIsShowModal(false);
        }
    }));
    const handleCheckboxChange = value => {
        setCheckedValues(value.filter(value => value !== "selectAll"));
    };
    const checkboxOptions = [
        {label: <span style={{lineHeight: "32px"}}>Switch Model</span>, value: "Model"},
        {label: <span style={{lineHeight: "32px"}}>Switch SN</span>, value: "SN"},
        {
            label: <span style={{lineHeight: "32px"}}>Hardware ID</span>,
            value: "Hardware ID",
            checked: true,
            disabled: true
        }
    ];

    const columns = [
        createColumnConfig("Switch Model", "model", TableFilterDropdown),
        createColumnConfig("Switch SN", "sn", TableFilterDropdown),
        createColumnConfig("Hardware ID", "hardware_id", TableFilterDropdown)
    ];
    const rowSelection = {
        selectedRowKeys,
        selectedRows,
        onChange: (newSelectedKeys, newSelectedRows) => {
            setSelectedRowKeys(newSelectedKeys);
            setSelectedRows(newSelectedRows);
        }
    };
    const matchFieldsList = [
        {name: "model", matchMode: "fuzzy"},
        {name: "sn", matchMode: "fuzzy"},
        {name: "hardware_id", matchMode: "fuzzy"}
    ];

    const handleDownload = async () => {
        const data = selectedRows.map(item => ({
            model: item.model,
            sn: item.sn,
            hwid: item.hardware_id
        }));
        const response = await exportHwidExcel({
            label: checkedValues,
            data
        });

        const blob = new Blob([response], {
            type: "application/vnd.ms-excel"
        });
        const url = URL.createObjectURL(blob);
        const link = document.createElement("a");
        link.href = url;
        link.download = `license-create-template_${dayjs().format("YYYYMMDD_HHmmss")}.xls`;
        link.click();
        setTimeout(() => URL.revokeObjectURL(url), 100);
    };
    return isShowModal ? (
        <Modal
            className="ampcon-max-modal"
            title={"Download"}
            open={isShowModal}
            onCancel={() => {
                setIsShowModal(false);
            }}
            footer={
                <Flex vertical>
                    <Divider />
                    <Flex justify="flex-end">
                        <Button
                            htmlType="button"
                            onClick={() => {
                                setIsShowModal(false);
                            }}
                        >
                            Cancel
                        </Button>
                        <Button type="primary" onClick={handleDownload}>
                            Download
                        </Button>
                    </Flex>
                </Flex>
            }
        >
            <Divider />
            <Alert
                message={
                    <div style={{paddingLeft: "8px"}}>
                        <span style={{fontWeight: 700}}>Note: </span>
                        To activate licenses manually, select only <span style={{fontWeight: 700}}>Hardware ID</span> in
                        the Download <span style={{fontWeight: 700}}>Content</span> field.
                    </div>
                }
                type="info"
                showIcon
                icon={<InfoSvg />}
                closable
                style={{
                    marginBottom: 16,
                    backgroundColor: "#F3F8FF",
                    border: "1px solid #F3F8FF",
                    color: "#367EFF",
                    width: 1304,
                    height: 40,
                    borderRadius: 2
                }}
            />
            <AmpConCustomTable
                extraButton={
                    <Flex align="center" gap={32}>
                        <span style={{lineHeight: "32px"}}>Download Content:</span>
                        <Checkbox.Group
                            options={checkboxOptions}
                            onChange={handleCheckboxChange}
                            value={checkedValues}
                        />
                    </Flex>
                }
                rowSelection={rowSelection}
                preSavedSelectedRowKeys={true}
                matchFieldsList={matchFieldsList}
                columns={columns}
                fetchAPIInfo={getQuickActivationTableData}
                searchFieldsList={searchFieldsList}
            />
        </Modal>
    ) : null;
});

export default CreateDownloadModel;
