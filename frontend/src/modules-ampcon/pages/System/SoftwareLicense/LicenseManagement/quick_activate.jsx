import React, {useEffect, useRef, useState} from "react";
import {ArrowLeftOutlined} from "@ant-design/icons";
import styles from "@/modules-ampcon/pages/System/SoftwareLicense/LicenseManagement/quick_activate.module.scss";
import {But<PERSON>, Card, message, Space, Spin} from "antd";
import {AmpConCustomTable, createColumnConfig, TableFilterDropdown} from "@/modules-ampcon/components/custom_table";
import {ActivateDisableSvg, ActivateSvg, downLoadSvg, refreshSvg} from "@/utils/common/iconSvg";
import Icon from "@ant-design/icons/lib/components/Icon";
import {activateLicense, getQuickActivationTableData} from "@/modules-ampcon/apis/config_api";
import CreateDownloadModel from "@/modules-ampcon/pages/System/SoftwareLicense/LicenseManagement/create_download_modal";
import UserExpirationDateSelect from "@/modules-ampcon/pages/System/SoftwareLicense/LicenseManagement/user_expiration_date_select";

const QuickActivate = ({backToLicenseManagement, currentLicenseType}) => {
    const createDownloadModalRef = useRef();
    const userExpirationDateSelectRef = useRef(null);
    const [currentUser, setCurrentUser] = useState("");
    const [currentExpireDate, setCurrentExpireDate] = useState("");
    const [currentQuantity, setCurrentQuantity] = useState(null);
    const [isActivateDisabled, setIsActivateDisabled] = useState(true);
    const [initialized, setInitialized] = useState(false);
    const [isShowSpin, setIsShowSpin] = useState(false);
    const tableRef = useRef(null);
    const searchFieldsList = ["model", "sn", "hardware_id"];
    const [selectedRowKeys, setSelectedRowKeys] = useState([]);
    const [selectedRows, setSelectedRows] = useState([]);
    const columns = [
        {
            ...createColumnConfig("Switch Model", "model", TableFilterDropdown),
        },
        {
            ...createColumnConfig("Switch SN", "sn", TableFilterDropdown),
        },
        {
            ...createColumnConfig("Hardware ID", "hardware_id", TableFilterDropdown),
        }
    ];
    const rowSelection = {
        selectedRowKeys,
        selectedRows,
        onChange: (newSelectedKeys, newSelectedRows) => {
            setSelectedRowKeys(newSelectedKeys);
            setSelectedRows(newSelectedRows);
        },
        getCheckboxProps: record => ({
            disabled:
                currentQuantity &&
                currentQuantity > 0 &&
                selectedRowKeys.length >= currentQuantity &&
                !selectedRowKeys.includes(record.id)
        })
    };
    const matchFieldsList = [
        {name: "model", matchMode: "fuzzy"},
        {name: "sn", matchMode: "fuzzy"},
        {name: "hardware_id", matchMode: "fuzzy"}
    ];

    const handleRefresh = async () => {
        setIsShowSpin(true);
        await userExpirationDateSelectRef.current.refreshLicenseInfo();
        tableRef.current.refreshTable();
        setIsShowSpin(false);
    };

    const authenticationFailedMessage = (
        <Space direction="vertical" size={0} align="start">
            <>The License Portal authentication failed.</>
            <>Please try again after updating license portal information in the global system configuration.</>
        </Space>
    );

    const handleActivate = async () => {
        setIsShowSpin(true);
        try {
            const expire_date = currentExpireDate.split(" <")[0];
            const hwid_list = selectedRows.map(item => item.hardware_id);
            const license_type = currentExpireDate.includes("<Trial>") ? "trial" : "standard";
            const response = await activateLicense({
                expire_date,
                hwid_list,
                license_type
            });
            if (response.status === 200) {
                message.success(response.info);
                await userExpirationDateSelectRef.current.refreshLicenseInfo();
                tableRef.current.refreshTable();
                setSelectedRows([]);
                setSelectedRowKeys([]);
                tableRef.current.clearSelectedRow();
            } else {
                message.error(response.status === 401 ? authenticationFailedMessage : response.info);
            }
        } catch (error) {
            console.error("Error activating license:", error);
        }
        setIsShowSpin(false);
    };

    useEffect(() => {
        if (tableRef.current) {
            setSelectedRows([]);
            setSelectedRowKeys([]);
            tableRef.current.clearSelectedRow();
        }
    }, [currentExpireDate]);
    useEffect(() => {
        if (!currentQuantity || !selectedRowKeys.length) {
            setIsActivateDisabled(true);
        } else {
            setIsActivateDisabled(false);
        }
    }, [setCurrentQuantity, selectedRowKeys.length]);

    return (
        <Card style={{display: "flex", flex: 1}}>
            <Spin spinning={isShowSpin} tip="Loading..." fullscreen />
            <p className={styles.goBack} onClick={backToLicenseManagement} style={{marginBottom: "23px"}}>
                <ArrowLeftOutlined style={{marginRight: "8px"}} /> Back
            </p>
            <h2>Quick Activate</h2>
            <UserExpirationDateSelect
                ref={userExpirationDateSelectRef}
                setInitialized={setInitialized}
                showLicenseTypes={currentLicenseType === "none" ? ["standard", "trial"] : [currentLicenseType]}
                currentUser={currentUser}
                setCurrentUser={setCurrentUser}
                currentQuantity={currentQuantity}
                setCurrentQuantity={setCurrentQuantity}
                currentExpireDate={currentExpireDate}
                setCurrentExpireDate={setCurrentExpireDate}
            />
            {initialized && (
                <AmpConCustomTable
                    ref={tableRef}
                    rowSelection={rowSelection}
                    columns={columns}
                    quantity={currentQuantity}
                    fetchAPIInfo={getQuickActivationTableData}
                    searchFieldsList={searchFieldsList}
                    matchFieldsList={matchFieldsList}
                    preSavedSelectedRowKeys={true}
                    extraButton={
                        <Space size={16}>
                            <Button
                                type="primary"
                                onClick={handleActivate}
                                style={{height: "32px", width: "108px"}}
                                disabled={isActivateDisabled}
                                icon={<Icon component={isActivateDisabled ? ActivateDisableSvg : ActivateSvg} className={styles.buttonIcon} />}
                            >
                                Activate
                            </Button>
                            <Button onClick={handleRefresh} style={{height: "32px", width: "104px"}}>
                                <Icon component={refreshSvg} className={styles.buttonIcon}/>
                                Refresh
                            </Button>
                            <Button
                                onClick={() => {
                                    createDownloadModalRef.current.showCreateDownloadModal();
                                }}
                                style={{height: "32px", width: "120px"}}
                            >
                                <Icon component={downLoadSvg} className={styles.buttonIcon} />
                                Download
                            </Button>
                        </Space>
                    }
                />
            )}

            <CreateDownloadModel ref={createDownloadModalRef} />
        </Card>
    );
};
export default QuickActivate;
