import React, {useEffect, useState, useRef, forwardRef} from "react";
import {useLocation, useNavigate} from "react-router-dom";
import {Card, Space, Menu, message, Table, Form, Input, InputNumber, Select, Radio, Button} from "antd";
import Icon, {PlusOutlined, ArrowLeftOutlined} from "@ant-design/icons";
import DeleteSvg from "@/modules-ampcon/pages/PhysicalNetwork/resource/delete.svg?react";
import DeleteGreyHover from "@/modules-ampcon/pages/PhysicalNetwork/resource/deleteGreyHover.svg?react";

import styles from "@/modules-ampcon/pages/PhysicalNetwork/Design/FabricManagement/fabric_management.module.scss";
import SelectSwitchPortModal from "@/modules-ampcon/pages/Resource/ResourceInterconnection/NodeAddition/selectSwitchPortModal";

// import API
import {
    fetchFabricInfo,
    fetchPODInfo,
    list_vd_dropdown_data,
    saveCloudNode
} from "@/modules-ampcon/apis/node_addition_api";
import {viewFabric} from "@/modules-ampcon/apis/dc_template_api";
import FabricTopo from "@/modules-ampcon/pages/PhysicalNetwork/Design/FabricManagement/fabric_topo";

/**
 * Create Fabric Modal
 */
const CreateCloudNodeAddition = forwardRef(() => {
    const {state} = useLocation();
    const navigate = useNavigate();
    const [form] = Form.useForm();
    const [nodeInfoCopy, setNodeInfoCopy] = useState({});
    const NAME_MATCH_REGEX = /^[\s\w.:-]+$/;
    const NAME_MATCH_REGEX2 = /^[\s\w-]+$/;
    const [topoInfo, setTopoInfo] = useState({});

    // Link Host组
    const [editNodeInfoCopy, setEditNodeInfoCopy] = useState({});
    const [switchGroupNumLimit, setSwitchGroupNumLimit] = useState(1);
    const [host_link, setHostLinkList] = useState({});
    const [isSelectSwitchPortModal, setIsSelectSwitchPortModal] = useState(false);
    const [port_info, setPort_info] = useState([]);
    const [nodeDetails, setNodeDetails] = useState({});

    const [deleteSVGHover, setDeleteSVGHover] = useState(false);

    /**
     * 获取Fabric列表
     */
    const [fabricList, setFabricList] = useState([]);
    const getFabricList = async () => {
        const result = await fetchFabricInfo();
        if (result.status === 200) {
            setFabricList(result.data);
        } else message.error(result.info);
    };

    // 获取topoInfo信息
    const getTopoData = async e => {
        const fabric_topo_item = fabricList.find(obj => obj.id === e) || {};
        const res = await viewFabric({fabric_topo_id: fabric_topo_item?.fabric_topo_id});
        if (res.status === 200) {
            setTopoInfo(res.data);
        } else message.error(res.info);
    };

    /**
     * 获取POD列表
     */
    const [podList, setPodList] = useState([]);
    const getPodList = async e => {
        const result = await fetchPODInfo({type: "Cloud", fabric_id: e});
        if (result.status === 200) {
            setPodList(result.data);
        } else message.error(result.info);
    };

    /**
     * 获取VLAN Domain列表
     */
    const [vlanDomainList, setVlanDomainList] = useState([]);
    const getVlanDomain = async e => {
        setVlanDomainList([]);
        const result = await list_vd_dropdown_data({fabric_id: e});
        if (result.status === 200) {
            setVlanDomainList(result.data);
        } else message.error(result.info);
    };

    const selectNICPortTableColumns = [
        {
            title: "VLAN Domain",
            dataIndex: "vlan_domain_name",
            key: "vlan_domain_name",
            render: (_, record) => {
                const vlanDomain = vlanDomainList.find(domain =>
                    domain.switch_info.some(device => device.logic_device_id === record.logic_device_id)
                );
                return <span>{vlanDomain?.name || "--"}</span>;
            }
        },
        {
            title: "Sysname",
            dataIndex: "hostname",
            key: "hostname",
            render: (_, record) => {
                const device = vlanDomainList
                    .flatMap(d => d.switch_info)
                    .find(item => item.logic_device_id === record.logic_device_id);
                return <span>{device?.hostname || "--"}</span>;
            }
        },
        {
            title: "Ports",
            render: (_, record) => {
                return (
                    <div>
                        <div>
                            <Space size="large" className="actionLink">
                                <a
                                    onClick={() => {
                                        if (!record.link_count) {
                                            message.warning(
                                                "Please enter the physical link count for each leaf switch first"
                                            );
                                            return false;
                                        }
                                        setPort_info(record);
                                        setNodeDetails(form.getFieldValue());
                                        setIsSelectSwitchPortModal(true);
                                    }}
                                >
                                    Select Switch Ports
                                </a>
                            </Space>
                        </div>
                    </div>
                );
            }
        }
    ];

    // 更新 Switch Port Group组数据
    const handleLinkItemChange = (name, value, key, nameIndex) => {
        // currentHostLink1当前修改的Switch Port Group对象
        const hostLinkData = form.getFieldValue("host_link") || [];
        const currentHostLink = {...host_link[`link_item${key}`]};
        let currentSwitchCopy = {};
        if (editNodeInfoCopy) {
            currentSwitchCopy = editNodeInfoCopy?.host_link?.find(
                obj => obj.port_group_name === currentHostLink.port_group_name
            );
        }
        switch (name) {
            case "port_group_name":
                currentHostLink.port_group_name = value;
                break;
            case "vlan_domain_id":
                // chooseSwitch:当前VD下的switch list
                const chooseSwitch = vlanDomainList?.find(obj => obj.id === value) || [];
                currentHostLink.vlan_domain_id = value;
                currentHostLink.vdSwitchList = chooseSwitch?.switch_info;
                currentHostLink.hostname = chooseSwitch?.switch_info?.map(device => device.hostname).join(",");
                currentHostLink.link_type = chooseSwitch.switch_info.length >= 2 ? "MLAG Leaf" : "Single leaf"; // MLAG Leaf || Single leaf
                currentHostLink.access_mlag_mode = "Single-Homed";
                currentHostLink.peer_leaf = chooseSwitch?.switch_info[0]?.logic_device_id;
                currentHostLink.connect_mode = currentHostLink.connect_mode ? currentHostLink.connect_mode : "access";
                currentHostLink.link_count = "1";
                currentHostLink.port_info = [];
                const newPortObj = {
                    logic_device_id: chooseSwitch?.switch_info[0].logic_device_id,
                    switch_sn: chooseSwitch?.switch_info[0].sn,
                    disable_port_list: [],
                    port_name: []
                };
                currentHostLink.port_info.push(newPortObj);
                break;

            // Access MLAG Mode
            case "access_mlag_mode":
                currentHostLink.access_mlag_mode = value; // Single-Homed || Dual-Homed
                if (value === "Dual-Homed") {
                    currentHostLink.port_info = [];
                    currentHostLink.port_info = currentHostLink?.vdSwitchList?.map((device, index) => ({
                        logic_device_id: device.logic_device_id,
                        switch_sn: device.sn,
                        disable_port_list:
                            currentSwitchCopy?.port_info?.find(obj => obj.switch_sn === device.sn)?.port_info
                                ?.disable_port_list || [],
                        port_name:
                            currentSwitchCopy?.port_info?.find(obj => obj.switch_sn === device.sn)?.port_info
                                ?.port_name || []
                    }));
                } else {
                    currentHostLink.port_info = [];
                    const newPortObj = {
                        logic_device_id: currentHostLink?.vdSwitchList[0].logic_device_id,
                        switch_sn: currentHostLink?.vdSwitchList[0].sn,
                        disable_port_list:
                            currentSwitchCopy?.port_info?.find(
                                obj => obj.switch_sn === currentHostLink?.vdSwitchList[0].sn
                            )?.port_info?.disable_port_list || [],

                        port_name:
                            currentSwitchCopy?.port_info?.find(
                                obj => obj.switch_sn === currentHostLink?.vdSwitchList[0].sn
                            )?.port_info?.port_name || []
                    };
                    currentHostLink.port_info.push(newPortObj);
                }
                break;

            // Peer Leaf
            case "peer_leaf":
                currentHostLink.peer_leaf = value;
                currentHostLink.port_info = [];
                const currentSwitch = currentHostLink?.vdSwitchList.find(obj => obj.logic_device_id === value);
                const newPortObj2 = {
                    logic_device_id: currentSwitch?.logic_device_id,
                    switch_sn: currentSwitch?.sn,
                    disable_port_list:
                        currentSwitchCopy?.port_info?.find(obj => obj.switch_sn === currentSwitch.sn)?.port_info
                            ?.disable_port_list || [],
                    port_name:
                        currentSwitchCopy?.port_info?.find(obj => obj.switch_sn === currentSwitch.sn)?.port_info
                            ?.port_name || []
                };
                currentHostLink.port_info.push(newPortObj2);

                break;

            case "connect_mode":
                currentHostLink.connect_mode = value;
                break;

            // Physical link count per individual switch
            case "link_count":
                currentHostLink.link_count = value;
                break;
            default:
                break;
        }
        currentHostLink.port_info?.map((item, index) => {
            item.port_group_name = currentHostLink?.port_group_name;
            item.link_count = currentHostLink?.link_count;
            item.vlan_domain_id = currentHostLink?.vlan_domain_id;
            item.group_index = index + 1;
            return item;
        });
        setHostLinkList(prev => ({...prev, [`link_item${key}`]: currentHostLink}));
        // 更新form;
        const newHostLink2 = hostLinkData.map((item, index) => {
            if (index === nameIndex) {
                return {
                    ...item,
                    ...currentHostLink
                };
            }
            return item;
        });
        form.setFieldsValue({host_link: newHostLink2});
    };

    const saveSelectSwitchPort = data => {
        const formData = form.getFieldsValue();
        const updatedFormData = {
            ...formData,
            host_link: formData.host_link.map(group => {
                if (group.vlan_domain_id === data.vlan_domain_id && group.port_group_name === data.port_group_name) {
                    const updatedPortInfo = group.port_info.map(info => {
                        if (info.logic_device_id === data.logic_device_id) {
                            return {
                                ...info,
                                port_id: data.port_id,
                                link_count: data.link_count,
                                group_index: data.group_index,
                                port_name: data.port_name,
                                disable_port_list: data.disable_port_list,
                                vlan_domain_id: data.vlan_domain_id,
                                port_group_name: data.port_group_name
                            };
                        }
                        return info;
                    });

                    return {
                        ...group,
                        port_info: updatedPortInfo
                    };
                }

                return group;
            })
        };
        form.setFieldsValue(updatedFormData); // 更新表单数据

        updatedFormData?.host_link?.forEach((item, index) => {
            const chooseSwitch = vlanDomainList?.find(obj => obj.id === item?.vlan_domain_id) || [];
            item.vdSwitchList = chooseSwitch?.switch_info || [];
            item.hostname = chooseSwitch?.switch_info?.map(device => device.hostname).join(",") || "";
            item?.port_info?.forEach((groupItem, groupIndex) => {
                groupItem.port_group_name = item.port_group_name;
                groupItem.vlan_domain_id = item.vlan_domain_id;
                groupItem.link_count = item.link_count;
                groupItem.group_index = groupIndex + 1;
            });
            if (item.access_mlag_mode === "Single-Homed") {
                item.peer_leaf = item?.port_info[0]?.logic_device_id;
            }

            // 更新Switch Port Group数据
            setHostLinkList(prev => {
                let foundKey = null;
                if (item.id) {
                    foundKey = Object.keys(prev).find(key => prev[key].id === item.id);
                }

                if (!foundKey && item.port_group_name && item.vlan_domain_id) {
                    foundKey = Object.keys(prev).find(
                        key =>
                            prev[key].port_group_name === item.port_group_name &&
                            prev[key].vlan_domain_id === item.vlan_domain_id
                    );
                }
                if (foundKey) {
                    return {
                        ...prev,
                        [foundKey]: {
                            ...prev[foundKey],
                            ...item
                        }
                    };
                }
            });
        });
        setIsSelectSwitchPortModal(false);
    };

    /**
     * 编辑信息初始化
     */
    const editDataFormatting = data => {
        data?.host_link?.forEach((item, index) => {
            const chooseSwitch = vlanDomainList?.find(obj => obj.id === item?.vlan_domain_id) || [];
            item.vdSwitchList = chooseSwitch?.switch_info || [];
            item.hostname = chooseSwitch?.switch_info?.map(device => device.hostname).join(",") || "";
            item?.port_info?.forEach((groupItem, groupIndex) => {
                groupItem.port_group_name = item.port_group_name;
                groupItem.vlan_domain_id = item.vlan_domain_id;
                groupItem.link_count = item.link_count;
                groupItem.group_index = groupIndex + 1;
                const {disable_port_list, nic_port_list, port_list, sw_port_info} = groupItem.port_info || {};
                groupItem.disable_port_list = disable_port_list;
                groupItem.nic_port_list = nic_port_list;
                groupItem.port_list = port_list;
                groupItem.sw_port_info = sw_port_info;
            });
            if (item.access_mlag_mode === "Single-Homed") {
                item.peer_leaf = item?.port_info[0]?.logic_device_id;
            }
            setHostLinkList(prev => ({...prev, [`link_item${index}`]: item}));
        });
        setNodeInfoCopy(data);
        form.setFieldsValue(data);
    };

    /**
     * 校验Create/Edit下发数据
     */
    const checkSubmitData = data => {
        if (!data.host_link || data.host_link.length === 0) {
            return true;
        }
        for (const item of data.host_link) {
            if (!item.port_info || !Array.isArray(item.port_info)) {
                continue;
            }
            for (const portInfo of item.port_info) {
                if (portInfo?.port_name?.length === 0) {
                    return false;
                }
            }
        }
        return true;
    };

    // 新增/编辑节点
    const handleApply = async () => {
        const formData = await form.validateFields();
        const updatedObject = {
            id: state.data.id,
            host_name: formData.host_name,
            description: formData.description || "",
            management_ip: formData.management_ip,
            username: formData.username,
            password: formData.password,
            az_id: formData.az_id,
            host_link: formData.host_link
                ? formData.host_link.map(group => ({
                      host_id: group.host_id,
                      id: group.id,
                      vlan_domain_id: group.vlan_domain_id,
                      port_group_name: group.port_group_name,
                      connect_mode: group.connect_mode,
                      link_type: group.link_type,
                      link_count: group.link_count,
                      port_info:
                          group.port_info.map(info => ({
                              port_id: info.port_id,
                              logic_device_id: info.logic_device_id,
                              switch_sn: info.switch_sn,
                              disable_port_list: info?.disable_port_list || [],
                              port_name: info?.port_name || []
                          })) || []
                  }))
                : []
        };
        if (!checkSubmitData(updatedObject)) {
            message.warning("Please select a switch port");
            return false;
        }
        try {
            const res = await saveCloudNode(updatedObject);
            if (res.status === 200) {
                message.success(res.info);
                navigate(`/resource/resource_interconnection/node_addition`, {state: {tabKey: "cloudPlatformNodes"}});
            } else {
                message.error(res.info);
            }
        } catch (error) {
            // error
        }
    };

    useEffect(() => {
        getFabricList();
    }, []);
    useEffect(() => {
        if (state.actionType === "Edit" && fabricList.length > 0) {
            getTopoData(state.data.fabric_id);
            getPodList(state.data.fabric_id);
        }
    }, [state, fabricList]);

    useEffect(() => {
        form.resetFields();
        form.setFieldsValue({});
        setHostLinkList({});
        if (state.actionType === "Edit") {
            getVlanDomain(state.data.fabric_id);
            editDataFormatting(state.data);
            const copyData = JSON.parse(JSON.stringify(state.data));
            setEditNodeInfoCopy(copyData);
        }
    }, [state, form]);

    useEffect(() => {
        editDataFormatting(state.data);
    }, [state, fabricList, podList, vlanDomainList]);

    return (
        <div style={{display: "flex", flex: 1, position: "relative"}}>
            <div className={styles.editNodeAdditionBox}>
                <h2 className={styles.pagesTitle}>{state.actionType} Cloud Platform Nodes </h2>
                <div className={styles.editNodeAdditionContent}>
                    <Form
                        form={form}
                        labelAlign="left"
                        rootClassName={styles.nodesForm}
                        style={{width: 710, minWidth: 350, maxHeight: "100%", overflowY: "auto"}}
                    >
                        <Form.Item
                            name="fabric_id"
                            label="Fabric"
                            labelCol={{style: {width: 154}}}
                            className={styles.nodeItem}
                            rules={[{required: true, message: "Please select fabric!"}]}
                        >
                            <Select
                                style={{width: 280}}
                                onChange={e => {
                                    getVlanDomain(e);
                                    getTopoData(e);
                                    getPodList(e);
                                }}
                                disabled={state.actionType === "Edit"}
                                options={fabricList?.map(item => ({
                                    value: item.id,
                                    label: item.fabric_name
                                }))}
                            />
                        </Form.Item>

                        <h2 className={styles.secondaryTitle}>Node</h2>
                        <Form.Item
                            name="host_name"
                            label="Node Name"
                            labelCol={{style: {width: 110}}}
                            className={styles.nodeItem}
                            rules={[
                                {
                                    required: true,
                                    validator: async (_, value) => {
                                        if (!value || value.trim() === "") {
                                            return Promise.reject(new Error("Please enter the  node name!"));
                                        }
                                        if (value.length > 64) {
                                            return Promise.reject(new Error("Enter a maximum of 64 characters"));
                                        }
                                        if (value === "All") {
                                            return Promise.reject(new Error("Please input a valid node name!"));
                                        }
                                        if (value.trim() !== value) {
                                            return Promise.reject(
                                                new Error("Node name should not have leading or trailing spaces.")
                                            );
                                        }
                                        if (!NAME_MATCH_REGEX.test(value)) {
                                            return Promise.reject(
                                                new Error(
                                                    "Node name can only contain letters, numbers, underscores, hyphens and spaces."
                                                )
                                            );
                                        }
                                        return Promise.resolve();
                                    }
                                }
                            ]}
                        >
                            <Input className={styles.formWidth} disabled={state.actionType === "Edit"} />
                        </Form.Item>

                        <Form.Item
                            name="description"
                            label="Description"
                            labelCol={{style: {width: 154}}}
                            className={styles.nodeItem}
                        >
                            <Input className={styles.formWidth} maxLength="128" />
                        </Form.Item>

                        <Form.Item
                            name="management_ip"
                            label="IP"
                            labelCol={{style: {width: 110}}}
                            className={styles.nodeItem}
                            rules={[
                                {required: true, message: "Please enter the  IP!"},
                                {
                                    pattern:
                                        /^(25[0-5]|2[0-4]\d|[01]?\d{1,2})\.(25[0-5]|2[0-4]\d|[01]?\d{1,2})\.(25[0-5]|2[0-4]\d|[01]?\d{1,2})\.(25[0-5]|2[0-4]\d|[01]?\d{1,2})$/,
                                    message: "Invalid IP!"
                                }
                            ]}
                        >
                            <Input className={styles.formWidth} placeholder="x.x.x.x" />
                        </Form.Item>

                        <Form.Item
                            name="username"
                            label="User"
                            labelCol={{style: {width: 110}}}
                            className={styles.nodeItem}
                        >
                            <Input className={styles.formWidth} maxLength="128" />
                        </Form.Item>

                        <Form.Item
                            name="password"
                            label="Password"
                            labelCol={{style: {width: 110}}}
                            className={styles.nodeItem}
                        >
                            <Input.Password className={styles.formWidth} maxLength="128" />
                        </Form.Item>

                        <Form.Item
                            name="az_id"
                            label="PoD"
                            labelCol={{style: {width: 154}}}
                            className={styles.nodeItem}
                            rules={[{required: true, message: "Please select the PoD!"}]}
                        >
                            <Select
                                style={{width: 280}}
                                disabled={nodeInfoCopy.id || state?.actionType === "Edit"}
                                options={podList?.map(item => ({
                                    value: item.id,
                                    label: item.az_name
                                }))}
                            />
                        </Form.Item>

                        <Form.Item name="host_link" label="">
                            <Form.List name="host_link">
                                {(fields, {add, remove}) => (
                                    <>
                                        {fields.map(({key, name, fieldKey, ...restField}) => {
                                            const currentHostLinkItem = {
                                                port_group_name: host_link[`link_item${key}`]?.port_group_name || "",
                                                vlan_domain_id: host_link[`link_item${key}`]?.vlan_domain_id || "",
                                                vdSwitchList: host_link[`link_item${key}`]?.vdSwitchList || [],
                                                hostname: host_link[`link_item${key}`]?.hostname || "",
                                                nicSelectPortGroupNum:
                                                    host_link[`link_item${key}`]?.nicSelectPortGroupNum || 0,
                                                link_type: host_link[`link_item${key}`]?.link_type || "", // MLAG Leaf || Single leaf
                                                access_mlag_mode: host_link[`link_item${key}`]?.access_mlag_mode || "", // Single-Homed || Dual-Homed
                                                peer_leaf: host_link[`link_item${key}`]?.peer_leaf || "",
                                                connect_mode: host_link[`link_item${key}`]?.connect_mode || "", // Access || Trunk
                                                link_count: host_link[`link_item${key}`]?.link_count || "",
                                                port_info: host_link[`link_item${key}`]?.port_info || []
                                            };
                                            // 获取除当前项之外的所有 Port Group Name
                                            const otherPortGroupNames = [];

                                            for (const itemKey in host_link) {
                                                if (itemKey !== `link_item${key}`) {
                                                    const portGroupName = host_link[itemKey].port_group_name;
                                                    if (portGroupName) {
                                                        otherPortGroupNames.push(portGroupName);
                                                    }
                                                }
                                            }

                                            const isLinkId = host_link[`link_item${key}`]?.id;

                                            return (
                                                <div key={key} className={styles.nodeLinkList}>
                                                    <div className={styles.groupHeader}>
                                                        <p>Switch Port Group</p>
                                                        <div className={styles.activeIcon}>
                                                            {!state.data.usage_state && (
                                                                <Icon
                                                                    className={styles.deleteLink}
                                                                    component={
                                                                        !deleteSVGHover ? DeleteSvg : DeleteGreyHover
                                                                    }
                                                                    onMouseEnter={() => {
                                                                        setDeleteSVGHover(true);
                                                                    }}
                                                                    onMouseLeave={() => {
                                                                        setDeleteSVGHover(false);
                                                                    }}
                                                                    onClick={() => {
                                                                        remove(name);
                                                                        delete host_link[`link_item${key}`];
                                                                        setHostLinkList(host_link);
                                                                    }}
                                                                    style={{marginLeft: 8, cursor: "pointer"}}
                                                                />
                                                            )}
                                                        </div>
                                                    </div>

                                                    <Form.Item
                                                        name={[name, "port_group_name"]}
                                                        label="Port Group Name"
                                                        labelCol={{style: {width: 164}}}
                                                        className={styles.linkItem}
                                                        rules={[
                                                            {
                                                                required: true,
                                                                validator: async (_, value) => {
                                                                    if (!value || value.trim() === "") {
                                                                        return Promise.reject(
                                                                            new Error(
                                                                                "Please input a valid port group name!"
                                                                            )
                                                                        );
                                                                    }
                                                                    if (value.length > 64) {
                                                                        return Promise.reject(
                                                                            new Error(
                                                                                "Enter a maximum of 64 characters"
                                                                            )
                                                                        );
                                                                    }
                                                                    if (value === "All") {
                                                                        return Promise.reject(
                                                                            new Error(
                                                                                "Please input a valid port group name!"
                                                                            )
                                                                        );
                                                                    }
                                                                    if (value.trim() !== value) {
                                                                        return Promise.reject(
                                                                            new Error(
                                                                                "Port group name should not have leading or trailing spaces."
                                                                            )
                                                                        );
                                                                    }
                                                                    if (!NAME_MATCH_REGEX2.test(value)) {
                                                                        return Promise.reject(
                                                                            new Error(
                                                                                "Port group name can only contain letters, numbers, underscores, hyphens and spaces."
                                                                            )
                                                                        );
                                                                    }
                                                                    if (otherPortGroupNames.includes(value)) {
                                                                        return Promise.reject(
                                                                            new Error(
                                                                                "Port Group Name cannot be repeated."
                                                                            )
                                                                        );
                                                                    }
                                                                    return Promise.resolve();
                                                                }
                                                            }
                                                        ]}
                                                    >
                                                        <Input
                                                            disabled={isLinkId}
                                                            className={styles.formWidth}
                                                            onChange={e => {
                                                                handleLinkItemChange(
                                                                    "port_group_name",
                                                                    e.target.value,
                                                                    key,
                                                                    name
                                                                );
                                                            }}
                                                        />
                                                    </Form.Item>

                                                    <Form.Item
                                                        name={[name, "vlan_domain_id"]}
                                                        label="VLAN Domain"
                                                        labelCol={{style: {width: 164}}}
                                                        className={styles.linkItem}
                                                        rules={[{required: true, message: "VLAN Domain is required!"}]}
                                                    >
                                                        <Select
                                                            style={{width: 280}}
                                                            disabled={
                                                                (state?.actionType === "Edit" && isLinkId) ||
                                                                !currentHostLinkItem.port_group_name
                                                            }
                                                            onChange={e => {
                                                                handleLinkItemChange("vlan_domain_id", e, key, name);
                                                            }}
                                                            options={vlanDomainList?.map(item => ({
                                                                value: item.id,
                                                                label: item.name
                                                            }))}
                                                        />
                                                    </Form.Item>

                                                    <Form.Item
                                                        name={[name, "hostname"]}
                                                        colon={false}
                                                        wrapperCol={{offset: 0, span: 24}}
                                                        style={{marginBottom: 0}}
                                                    >
                                                        <span className="custom-label">Switch: </span>
                                                        <span className="ml-2">{currentHostLinkItem?.hostname}</span>
                                                    </Form.Item>

                                                    {currentHostLinkItem.vlan_domain_id !== "" &&
                                                        currentHostLinkItem?.link_type === "MLAG Leaf" && (
                                                            <>
                                                                <Form.Item
                                                                    name={[name, "access_mlag_mode"]}
                                                                    label="Access MLAG Mode"
                                                                    labelCol={{style: {width: 164}}}
                                                                    className={styles.linkItem}
                                                                >
                                                                    <Radio.Group
                                                                        defaultValue="Single-Homed"
                                                                        onChange={e => {
                                                                            handleLinkItemChange(
                                                                                "access_mlag_mode",
                                                                                e.target.value,
                                                                                key,
                                                                                name
                                                                            );
                                                                        }}
                                                                    >
                                                                        <Radio value="Single-Homed">Single-Homed</Radio>
                                                                        <Radio value="Dual-Homed">Dual-Homed</Radio>
                                                                    </Radio.Group>
                                                                </Form.Item>

                                                                {/* 控制 Peer Leaf 字段的显示与隐藏 */}
                                                                {currentHostLinkItem?.access_mlag_mode ===
                                                                    "Single-Homed" && (
                                                                    <Form.Item
                                                                        name={[name, "peer_leaf"]}
                                                                        label="Peer Leaf"
                                                                        labelCol={{style: {width: 164}}}
                                                                        className={styles.linkItem}
                                                                    >
                                                                        <Radio.Group
                                                                            name="radiogroup"
                                                                            disabled={
                                                                                state?.actionType === "Edit" && isLinkId
                                                                            }
                                                                            defaultValue={
                                                                                currentHostLinkItem?.vdSwitchList[0]
                                                                                    ?.logic_device_id
                                                                            }
                                                                            onChange={e => {
                                                                                handleLinkItemChange(
                                                                                    "peer_leaf",
                                                                                    e.target.value,
                                                                                    key,
                                                                                    name
                                                                                );
                                                                            }}
                                                                            options={currentHostLinkItem?.vdSwitchList.map(
                                                                                device => ({
                                                                                    label: device.hostname,
                                                                                    value: device.logic_device_id
                                                                                })
                                                                            )}
                                                                        />
                                                                    </Form.Item>
                                                                )}
                                                            </>
                                                        )}

                                                    <Form.Item
                                                        name={[name, "connect_mode"]}
                                                        label="Port Mode"
                                                        labelCol={{style: {width: 164}}}
                                                        className={styles.linkItem}
                                                    >
                                                        <Radio.Group
                                                            defaultValue="access"
                                                            onChange={e => {
                                                                handleLinkItemChange(
                                                                    "connect_mode",
                                                                    e.target.value,
                                                                    key,
                                                                    name
                                                                );
                                                            }}
                                                        >
                                                            <Radio value="access">Access</Radio>
                                                            <Radio value="trunk">Trunk</Radio>
                                                        </Radio.Group>
                                                    </Form.Item>

                                                    <Form.Item
                                                        name={[name, "link_count"]}
                                                        label="Physical Link Count per Leaf Switch"
                                                        className={styles.linkItem}
                                                        rules={[
                                                            {
                                                                required: true,
                                                                message:
                                                                    "Please select Physical Link Count per Leaf Switch!"
                                                            }
                                                        ]}
                                                    >
                                                        <Radio.Group
                                                            onChange={e => {
                                                                handleLinkItemChange(
                                                                    "link_count",
                                                                    e.target.value,
                                                                    key,
                                                                    name
                                                                );
                                                            }}
                                                        >
                                                            <Radio value="1">Single</Radio>
                                                            <Radio value="2">Dual</Radio>
                                                        </Radio.Group>
                                                    </Form.Item>

                                                    <br />

                                                    <div>
                                                        <Table
                                                            border
                                                            columns={selectNICPortTableColumns}
                                                            dataSource={currentHostLinkItem?.port_info}
                                                            pagination={false}
                                                        />
                                                    </div>
                                                </div>
                                            );
                                        })}
                                        <Button
                                            className={styles.addSwitchPortGroupBtn}
                                            type="dashed"
                                            onClick={() => {
                                                if (fields.length < switchGroupNumLimit) {
                                                    add();
                                                } else {
                                                    message.warning(
                                                        `Up to ${switchGroupNumLimit} Switch Port Groups can be created`
                                                    );
                                                }
                                            }}
                                        >
                                            <PlusOutlined style={{marginRight: 8}} />
                                            Switch Port Group
                                        </Button>
                                    </>
                                )}
                            </Form.List>
                        </Form.Item>
                    </Form>

                    <div className={styles.topologyGraph}>
                        <FabricTopo topoInfo={topoInfo} />
                    </div>
                </div>

                <SelectSwitchPortModal
                    title="Select Switch Ports"
                    nodeType="Cloud"
                    portgroup_info={port_info}
                    nodeDetails={nodeDetails}
                    isSelectSwitchPortModal={isSelectSwitchPortModal}
                    onCancel={() => setIsSelectSwitchPortModal(false)}
                    onSubmit={saveSelectSwitchPort}
                    modalClass="ampcon-max-modal"
                />
            </div>
            <div className={styles.IssueOperationBar}>
                <Button
                    onClick={() => {
                        navigate(`/resource/resource_interconnection/node_addition`, {
                            state: {tabKey: "cloudPlatformNodes"}
                        });
                    }}
                >
                    Cancel
                </Button>
                <Button
                    type="primary"
                    onClick={() => {
                        handleApply();
                    }}
                >
                    Apply
                </Button>
            </div>
        </div>
    );
});
export default CreateCloudNodeAddition;
