import {But<PERSON>, <PERSON><PERSON><PERSON>, Flex, Input, Modal} from "antd";
import {forwardRef, useImperativeHandle, useState} from "react";

const UpgradeTaskLogModal = forwardRef((props, ref) => {
    useImperativeHandle(ref, () => ({
        showTaskLogModal: sn => {
            setSelectedSwitchSN(sn);
            setIsShowModal(true);
        },
        hideTaskLogModal: () => {
            resetModal();
        }
    }));

    const [isShowModal, setIsShowModal] = useState(false);
    const [selectedSwitchSN, setSelectedSwitchSN] = useState("");
    const [logContent, setLogContent] = useState("");

    const resetModal = () => {
        setIsShowModal(false);
        setSelectedSwitchSN("");
        setLogContent("");
    };

    return isShowModal ? (
        <Modal
            className="ampcon-middle-modal"
            title={
                <>
                    <div style={{display: "flex", justifyContent: "space-between", alignItems: "center"}}>Task Log</div>
                    <Divider style={{marginTop: 8, marginBottom: 0}} />
                </>
            }
            open={isShowModal}
            onCancel={() => {
                resetModal();
            }}
            footer={null}
        >
            <Flex vertical style={{flex: 1}}>
                <Input.TextArea value={logContent} rows={19} readOnly />
            </Flex>
        </Modal>
    ) : null;
});

export default UpgradeTaskLogModal;
