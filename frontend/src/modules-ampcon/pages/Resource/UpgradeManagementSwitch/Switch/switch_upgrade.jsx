import {But<PERSON>, Flex, message, Spin, Card, Tabs} from "antd";
import {useEffect, useRef, useState} from "react";
import {
    AmpConCustomTable,
    createColumnConfigMultipleParams,
    TableFilterDropdown
} from "@/modules-ampcon/components/custom_table";
import {deleteImage, queryImages} from "@/modules-ampcon/apis/config_api";
import Icon from "@ant-design/icons";
import styles from "@/modules-ampcon/pages/Resource/UpgradeManagementSwitch/upgrade_management_switch.module.scss";
import AddImageModal from "@/modules-ampcon/pages/Resource/UpgradeManagementSwitch/add_image_modal";
import UploadMd5Modal from "@/modules-ampcon/pages/Resource/UpgradeManagementSwitch/upload_md5_modal";
import {fetchUpgradeSwitchTableData} from "@/modules-ampcon/apis/lifecycle_api";
import LogViewTextareaModal from "@/modules-ampcon/components/log_view_textarea_modal";
import BatchSwitchUpgradeModal from "@/modules-ampcon/pages/Resource/UpgradeManagementSwitch/batch_switch_upgrade_modal";
import {batchUpgradeSwitch, checkImage, pushImage} from "@/modules-ampcon/apis/rma_api";
import BatchSwitchPushImageModal from "@/modules-ampcon/pages/Resource/UpgradeManagementSwitch/batch_switch_push_image_modal";
import {refreshSvg, updateWhiteSvg, updateGreySvg} from "@/utils/common/iconSvg";
import {useSelector} from "react-redux";
import {confirmModalAction} from "@/modules-ampcon/components/custom_modal";

const SwitchUpgrade = () => {
    const imageTableMatchFieldsList = [
        {name: "image_name", matchMode: "fuzzy"},
        {name: "platform", matchMode: "fuzzy"}
    ];
    const switchTableMatchFieldsList = [
        {name: "host_name", matchMode: "fuzzy"},
        {name: "mgt_ip", matchMode: "fuzzy"},
        {name: "sn", matchMode: "fuzzy"},
        {name: "platform", matchMode: "fuzzy"}
    ];
    const imageTableSearchFieldsList = ["image_name", "platform"];
    const switchTableSearchFieldsList = ["host_name", "mgt_ip", "sn"];

    const addImageModalRef = useRef(null);
    const uploadMd5ModalModalRef = useRef(null);
    const logViewTextareaModalRef = useRef(null);
    const batchSwitchUpgradeModal = useRef(null);
    const batchSwitchPushImageModal = useRef(null);
    const imageTableRef = useRef(null);
    const switchListTableRef = useRef(null);
    const [activeTab, setActiveTab] = useState("switch");

    const [selectedImage, setSelectedImage] = useState([]);
    const [isUpgradeDisabled, setIsUpgradeDisabled] = useState(true);

    const [isShowSpin, setIsShowSpin] = useState(false);
    const currentUser = useSelector(state => state.user.userInfo);
    const userType = currentUser?.type;

    useEffect(() => {
        if (selectedImage.length !== 0) {
            setIsUpgradeDisabled(false);
        }
    }, [selectedImage]);

    return (
        <>
            <Card style={{flex: 1, overflowY: "auto"}}>
                <Spin spinning={isShowSpin} tip="Loading..." fullscreen />
                <div>
                    <AddImageModal
                        saveCallback={() => {
                            imageTableRef.current.refreshTable();
                        }}
                        ref={addImageModalRef}
                    />
                    <UploadMd5Modal
                        saveCallback={() => {
                            imageTableRef.current.refreshTable();
                        }}
                        ref={uploadMd5ModalModalRef}
                    />
                    <LogViewTextareaModal ref={logViewTextareaModalRef} />
                    <BatchSwitchUpgradeModal
                        saveCallback={() => {
                            switchListTableRef.current.clearSelectedRow();
                            switchListTableRef.current.refreshTable();
                        }}
                        ref={batchSwitchUpgradeModal}
                    />
                    <BatchSwitchPushImageModal
                        saveCallback={() => {
                            switchListTableRef.current.clearSelectedRow();
                            switchListTableRef.current.refreshTable();
                        }}
                        ref={batchSwitchPushImageModal}
                    />
                </div>
                <Flex vertical className={styles.container_software}>
                    <span
                        style={{
                            fontSize: "18px",
                            paddingBottom: "12px",
                            borderBottom: "1px solid rgb(242, 242, 242)",
                            marginBottom: "24px",
                            fontWeight: 700
                        }}
                    >
                        Software
                    </span>
                    <AmpConCustomTable
                        ref={imageTableRef}
                        rowSelection={{
                            type: "radio",
                            onChange: (_, selectedRows) => {
                                switchListTableRef.current.clearSelectedRow();
                                setSelectedImage(selectedRows);
                                console.log("selectedImage：", selectedImage);
                            }
                        }}
                        extraButton={
                            <>
                                {userType === "superuser" ? (
                                    <Button
                                        type="primary"
                                        onClick={() => {
                                            addImageModalRef.current.showAddImageModal();
                                        }}
                                        icon={<Icon component={updateWhiteSvg} />}
                                    >
                                        Upload
                                    </Button>
                                ) : null}
                                <Button
                                    htmlType="button"
                                    onClick={() => {
                                        imageTableRef.current.refreshTable();
                                        message.success("Image table refresh success.");
                                    }}
                                >
                                    <Icon component={refreshSvg} />
                                    Refresh
                                </Button>
                            </>
                        }
                        columns={[
                            createColumnConfigMultipleParams({
                                title: "Image",
                                dataIndex: "image_name",
                                filterDropdownComponent: TableFilterDropdown,
                                width: "20%"
                            }),
                            createColumnConfigMultipleParams({
                                title: "Platform",
                                dataIndex: "platform",
                                filterDropdownComponent: TableFilterDropdown,
                                width: "12%"
                            }),
                            createColumnConfigMultipleParams({
                                title: "Version",
                                dataIndex: "version",
                                enableFilter: false,
                                enableSorter: false,
                                width: "12%"
                            }),
                            createColumnConfigMultipleParams({
                                title: "Revision",
                                dataIndex: "revision",
                                enableFilter: false,
                                enableSorter: false,
                                width: "12%"
                            }),
                            createColumnConfigMultipleParams({
                                title: "Create Time",
                                dataIndex: "create_time",
                                enableFilter: false,
                                enableSorter: true,
                                width: "12%"
                            }),
                            createColumnConfigMultipleParams({
                                title: "Update Time",
                                dataIndex: "modified_time",
                                enableFilter: false,
                                enableSorter: true,
                                width: "12%",
                                defaultSortOrder: "descend"
                            }),
                            createColumnConfigMultipleParams({
                                title: "Operation",
                                enableFilter: false,
                                enableSorter: false,
                                render: (_, record) => {
                                    return userType === "superuser" ? (
                                        <Flex
                                            style={{flexWrap: "wrap", columnGap: "24px", rowGap: "5px"}}
                                            className="actionLink"
                                        >
                                            <a
                                                onClick={() => {
                                                    confirmModalAction(
                                                        `This action will delete Image:${record.image_name}, Do you want to continue?`,
                                                        () => {
                                                            setIsShowSpin(true);
                                                            try {
                                                                deleteImage(record.id).then(res => {
                                                                    if (res.status !== 200) {
                                                                        message.error(res.info);
                                                                    } else {
                                                                        message.success(res.info);
                                                                        imageTableRef.current.refreshTable();
                                                                    }
                                                                });
                                                            } catch (e) {
                                                                message.error(
                                                                    "An error occurred during the process of delete"
                                                                );
                                                            } finally {
                                                                setIsShowSpin(false);
                                                            }
                                                        }
                                                    );
                                                }}
                                            >
                                                Delete
                                            </a>
                                            <a
                                                onClick={() => {
                                                    uploadMd5ModalModalRef.current.showUploadMd5Modal(record.id);
                                                }}
                                            >
                                                Upload Md5
                                            </a>
                                        </Flex>
                                    ) : null;
                                }
                            })
                        ]}
                        matchFieldsList={imageTableMatchFieldsList}
                        searchFieldsList={imageTableSearchFieldsList}
                        buttonProps={[]}
                        fetchAPIInfo={queryImages}
                    />
                </Flex>
                <Flex vertical className={styles.container_software}>
                    <span
                        style={{
                            fontSize: "18px",
                            paddingBottom: "12px",
                            borderBottom: "1px solid rgb(242, 242, 242)",
                            marginBottom: "24px",
                            fontWeight: 700
                        }}
                    >
                        Switch
                    </span>

                    <AmpConCustomTable
                        ref={switchListTableRef}
                        rowSelection={{
                            selectedRowKeys: [],
                            selectedRows: [],
                            getCheckboxProps: record => {
                                return {
                                    disabled:
                                        selectedImage.length === 0
                                            ? true
                                            : record.platform !== selectedImage[0].platform &&
                                              selectedImage[0].platform !== "other",
                                    // Column configuration not to be checked
                                    name: record.sn
                                };
                            }
                        }}
                        columns={[
                            createColumnConfigMultipleParams({
                                title: "Host Name",
                                dataIndex: "host_name",
                                filterDropdownComponent: TableFilterDropdown,
                                width: "10%"
                            }),
                            createColumnConfigMultipleParams({
                                title: "IP Address",
                                dataIndex: "mgt_ip",
                                filterDropdownComponent: TableFilterDropdown,
                                width: "10%"
                            }),
                            createColumnConfigMultipleParams({
                                title: "Switch SN",
                                dataIndex: "sn",
                                filterDropdownComponent: TableFilterDropdown,
                                width: "10%"
                            }),
                            createColumnConfigMultipleParams({
                                title: "Version/Revision",
                                dataIndex: "version",
                                enableFilter: false,
                                enableSorter: false,
                                width: "12%"
                            }),
                            createColumnConfigMultipleParams({
                                title: "Platform",
                                dataIndex: "platform",
                                enableFilter: true,
                                filterDropdownComponent: TableFilterDropdown,
                                enableSorter: false,
                                width: "6%"
                            }),
                            createColumnConfigMultipleParams({
                                title: "License Expire",
                                dataIndex: "license_expired",
                                enableFilter: false,
                                enableSorter: false,
                                width: "10%"
                            }),
                            createColumnConfigMultipleParams({
                                title: "License Status",
                                dataIndex: "license_status",
                                enableFilter: true,
                                filterDropdownComponent: TableFilterDropdown,
                                enableSorter: false,
                                width: "10%"
                            }),
                            createColumnConfigMultipleParams({
                                title: "Upgrade Status",
                                enableFilter: false,
                                enableSorter: false,
                                width: "10%",
                                render: (_, record) => {
                                    if (record.upgrade_status === 0) {
                                        return "Upgrading";
                                    }
                                    if (record.upgrade_status === 1) {
                                        return "Upgraded";
                                    }
                                    if (record.upgrade_status === 2) {
                                        return "Upgrade Failed";
                                    }
                                    return "----";
                                }
                            }),
                            createColumnConfigMultipleParams({
                                title: "Operation",
                                enableFilter: false,
                                enableSorter: false,
                                render: (_, record) => {
                                    return (
                                        <Flex
                                            style={{flexWrap: "wrap", columnGap: "24px", rowGap: "5px"}}
                                            className="actionLink"
                                        >
                                            <a
                                                style={
                                                    selectedImage.length === 0 ||
                                                    ((selectedImage[0].platform !== record.platform ||
                                                        (record.version !== undefined &&
                                                            record.version.includes("/") &&
                                                            record.version.split("/")[1] ===
                                                                selectedImage[0].revision)) &&
                                                        selectedImage[0].platform !== "other")
                                                        ? {display: "none"}
                                                        : {}
                                                }
                                                onClick={() => {
                                                    if (selectedImage.length === 0) {
                                                        message.error("Please select an image first");
                                                        return;
                                                    }
                                                    if (selectedImage[0].platform !== record.platform) {
                                                        message.error(
                                                            "The platform of the selected image is different from the platform of the switch"
                                                        );
                                                        return;
                                                    }
                                                    confirmModalAction(
                                                        `Do you want to upgrade ${record.sn} using image ${selectedImage[0].image_name} ?`,
                                                        () => {
                                                            return new Promise((resolve, reject) => {
                                                                batchUpgradeSwitch(
                                                                    selectedImage[0].image_name,
                                                                    [record.sn],
                                                                    []
                                                                )
                                                                    .then(response => {
                                                                        if (response.status !== 200) {
                                                                            message.error(response.info);
                                                                            reject();
                                                                        } else {
                                                                            message.success(response.info);
                                                                            resolve();
                                                                            switchListTableRef.current.refreshTable();
                                                                        }
                                                                    })
                                                                    .finally(() => {
                                                                        switchListTableRef.current.refreshTable();
                                                                        resolve();
                                                                    });
                                                            });
                                                        }
                                                    );
                                                }}
                                            >
                                                Upgrade
                                            </a>
                                            <a
                                                style={
                                                    selectedImage.length === 0 ||
                                                    ((selectedImage[0].platform !== record.platform ||
                                                        (record.version !== undefined &&
                                                            record.version.includes("/") &&
                                                            record.version.split("/")[1] ===
                                                                selectedImage[0].revision)) &&
                                                        selectedImage[0].platform !== "other")
                                                        ? {display: "none"}
                                                        : {}
                                                }
                                                onClick={async () => {
                                                    if (selectedImage.length === 0) {
                                                        message.error("Please select an image first");
                                                        return;
                                                    }
                                                    if (selectedImage[0].platform !== record.platform) {
                                                        message.error(
                                                            "The platform of the selected image is different from the platform of the switch"
                                                        );
                                                        return;
                                                    }
                                                    try {
                                                        setIsShowSpin(true);
                                                        await checkImage(record.sn, selectedImage[0].image_name)
                                                            .then(response => {
                                                                if (response.status !== 200) {
                                                                    message.error(response.info);
                                                                } else if (
                                                                    response.info.includes("The image not exist")
                                                                ) {
                                                                    return confirmModalAction(
                                                                        `Do you want to push ${selectedImage[0].image_name} image to ${record.sn} ? This step will take a while.`,
                                                                        () => {
                                                                            return new Promise((resolve, reject) => {
                                                                                pushImage(
                                                                                    record.sn,
                                                                                    selectedImage[0].image_name
                                                                                )
                                                                                    .then(response => {
                                                                                        if (response.status !== 200) {
                                                                                            message.error(
                                                                                                response.info
                                                                                            );
                                                                                            reject();
                                                                                        } else {
                                                                                            message.success(
                                                                                                response.info
                                                                                            );
                                                                                            switchListTableRef.current.refreshTable();
                                                                                            resolve();
                                                                                        }
                                                                                    })
                                                                                    .finally(() => {
                                                                                        switchListTableRef.current.refreshTable();
                                                                                        resolve();
                                                                                    });
                                                                            });
                                                                        }
                                                                    );
                                                                } else {
                                                                    message.success(response.info);
                                                                }
                                                            })
                                                            .finally(() => {
                                                                setIsShowSpin(false);
                                                            });
                                                    } catch (e) {
                                                        message.error(
                                                            "An error occurred during the process of upgrade"
                                                        );
                                                    } finally {
                                                        setIsShowSpin(false);
                                                    }
                                                }}
                                            >
                                                Push Image
                                            </a>
                                            <a
                                                onClick={() => {
                                                    logViewTextareaModalRef.current.showLogViewTextareaModal(record.sn);
                                                }}
                                            >
                                                Log
                                            </a>
                                        </Flex>
                                    );
                                }
                            })
                        ]}
                        searchFieldsList={switchTableSearchFieldsList}
                        matchFieldsList={switchTableMatchFieldsList}
                        fetchAPIInfo={fetchUpgradeSwitchTableData}
                        fetchAPIParams={[selectedImage.length === 0 ? "" : selectedImage[0].platform]}
                        extraButton={
                            <>
                                <Button
                                    type="primary"
                                    onClick={() => {
                                        if (
                                            switchListTableRef.current.getSelectedRow().tableSelectedRowKey.length === 0
                                        ) {
                                            message.error("Please select a switch first");
                                            return;
                                        }
                                        batchSwitchUpgradeModal.current.showBatchSwitchUpgradeModal(
                                            selectedImage,
                                            switchListTableRef.current.getSelectedRow()
                                        );
                                    }}
                                    disabled={isUpgradeDisabled}
                                    icon={
                                        isUpgradeDisabled ? (
                                            <Icon component={updateGreySvg} />
                                        ) : (
                                            <Icon component={updateWhiteSvg} />
                                        )
                                    }
                                >
                                    Upgrade
                                </Button>
                                <Button
                                    type="primary"
                                    onClick={() => {
                                        if (
                                            switchListTableRef.current.getSelectedRow().tableSelectedRowKey.length === 0
                                        ) {
                                            message.error("Please select a switch first");
                                            return;
                                        }
                                        batchSwitchPushImageModal.current.showBatchSwitchPushImageModal(
                                            selectedImage,
                                            switchListTableRef.current.getSelectedRow()
                                        );
                                    }}
                                    disabled={isUpgradeDisabled}
                                    icon={
                                        isUpgradeDisabled ? (
                                            <Icon component={updateGreySvg} />
                                        ) : (
                                            <Icon component={updateWhiteSvg} />
                                        )
                                    }
                                >
                                    Push Image
                                </Button>
                                <Button
                                    htmlType="button"
                                    onClick={() => {
                                        switchListTableRef.current.refreshTable();
                                        message.success("Switch table refresh success.");
                                    }}
                                    icon={<Icon component={refreshSvg} />}
                                >
                                    Refresh
                                </Button>
                            </>
                        }
                    />
                </Flex>
            </Card>
            <div />
        </>
    );
};

export default SwitchUpgrade;
