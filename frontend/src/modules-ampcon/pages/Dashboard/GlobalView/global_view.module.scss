.globalView {
    display: grid;
    height: 100%;
    width: 100%;
    gap: 18px 24px;
    grid-template-columns: repeat(12, 1fr);
    grid-template-rows: 0.5fr 1fr 1.5fr;
    > div {

        &:nth-child(1),
        &:nth-child(2),
        &:nth-child(3),
        &:nth-child(4) {
            grid-column: span 3;
        }
    
        &:nth-child(6),
        &:nth-child(8),
        &:nth-child(7) {
            grid-column: span 4;
            grid-row: 2;
        }
    
        &:nth-child(5) {
            grid-column: 1 / -1;
            grid-row: 3;
        }
    }

    &_custom_title {
        font-size: 18px;
    }
    &_cpuUtilization {
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
    }
    &_recentAlarms{
        :global{
          .ant-card-body{
            overflow: hidden;
          }
        }
        &_alarmBody {
            flex: 1;
            display: flex;
            padding: 14px 0;
            height: calc(100% - 10px);
            vertical-align: center;
            flex-direction: column;
            justify-content: flex-start;
            overflow: auto;
            &_alarmList {
                display: flex;
                flex-direction: row;
                align-items: flex-start;
                padding-bottom: 12px;
                &_dot {
                    width: 10px;
                    height: 10px;
                    margin-top: 7px;
                    border-radius: 50%;
                }
        
                &_text {
                flex: 1;
                padding-left: 8px;
                }
            }
        }
    }
}

.alarms {
    height: 100%;
    width: 100%;
    display: flex;
    justify-content: space-around;
    align-items: center;
    overflow: hidden;
    &_alarms_value {
        display: flex;
        font-weight: 700;
        font-size: 20px;
    }
    &_alarms_number {
        margin-left: 8px;
    }
    &_title {
        color: #929A9E;
        font-size: 16px;
        text-align: left;
    }
}

@media (min-width: 1921px) and (max-width: 2560px) {
    .globalView {
        &_headerCard {
            height: 20vh;
            canvas {
                // scale: 1.1;
            }
        }
        &_cpuUtilization {
            canvas {
                // scale: 0.9;
            }
        }
    }
}

.device-status {
  display: inline-flex;
  align-items: center;
  padding: 2px 8px;
  border-radius: 2px;
  font-size: 14px;
  
  &--online {
    font-family: Lato, Lato;
    color: #2BC174;
    background: rgba(43,193,116,0.1);
    border-radius: 2px 2px 2px 2px;
    border: 1px solid #2BC174;
  }
  
  &--offline {
    font-family: Lato, Lato;
    color: #F53F3F;
    background: rgba(245, 63, 63, 0.1);
    border-radius: 2px 2px 2px 2px;
    border: 1px solid #F53F3F;
  }

  &--unkown {
    font-family: Lato, Lato;
    color: #B3BBC8;
    background: rgba(244, 245, 247, 1);
    border-radius: 2px 2px 2px 2px;
    border: 1px solid #DADCE1 ;
  }
}