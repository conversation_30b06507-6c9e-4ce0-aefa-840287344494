import {useEffect, useState, forwardRef, useImper<PERSON><PERSON><PERSON><PERSON>, useMemo} from "react";
import {useForm} from "antd/es/form/Form";
import {DatePicker, Divider, Button, Form, Checkbox, Row, Col, Card, message, Select, Empty} from "antd";
import EmptyPic from "@/assets/images/App/empty.png";
import Icon from "@ant-design/icons/lib/components/Icon";
import {AmpConCustomModalForm} from "@/modules-ampcon/components/custom_table";
import {fetchInterfaceTopK, fetchModulesTopK, fetchUsageTopK} from "@/modules-ampcon/apis/monitor_api";
import {CustomLineChart} from "@/modules-ampcon/components/echarts_common";
import settingGreySvg from "../../Topo/Topology/resource/site_grey.svg?react";
import settingGreenSvg from "../../Topo/Topology/resource/site_green.svg?react";

const {RangePicker} = DatePicker;
const {Option} = Select;

const usageOptions = {
    cpu: "CPU (%)",
    memory: "Memory (%)"
};

const interfaceCheckboxOptions = {
    "in-octets": "In Octets",
    "in-pkts": "In Pkts",
    "in-discards": "In Discards",
    "in-errors": "In Errors",
    "in-fcs-errors": "In Fcs Errors",
    "out-octets": "Out Octets",
    "out-pkts": "Out Pkts",
    "out-discards": "Out Discards",
    "out-errors": "Out Errors",
    "out-bits-rate": "Out Bits Rate (bits/s)",
    "in-bits-rate": "In Bits Rate (bits/s)",
    "out-pkts-rate": "Out Pkts Rate (packets/s)",
    "in-pkts-rate": "In Pkts Rate (packets/s)"
};

const modulesCheckboxOptions = {
    "output-power": "Tx Power (dBm)",
    "input-power": "Rx Power (dBm)",
    "laser-temperature": "Temperature (℃)"
    // attenuation: "Output Power - Input Power (dB)"
};

const aiCheckboxOptions = {
    "ecn-marked-packets": "ECN-Marked-Packets",
    "send-pfc-pause-frames": "Send-PFC-Pause-Frames",
    "receive-pfc-pause-frames": "Receive-PFC-Pause-Frames",
    "pfc-deadlock-monitor-count": "PFC-Deadlock-Monitor-Count",
    "pfc-deadlock-recovery-count": "PFC-Deadlock-Recovery-Count"
};

const pfcKind = [
    "send-pfc-pause-frames",
    "receive-pfc-pause-frames",
    "pfc-deadlock-monitor-count",
    "pfc-deadlock-recovery-count"
];

const TelemetryView = () => {
    const [form] = useForm();
    const [timeRange, setTimeRange] = useState(["", ""]);
    const [counters, setCounters] = useState({
        usage: Object.keys(usageOptions),
        interface: Object.keys(interfaceCheckboxOptions),
        modules: Object.keys(modulesCheckboxOptions),
        ai: Object.keys(aiCheckboxOptions)
    });
    const [isSelectCountersModalOpen, setSelectCountersModalOpen] = useState(false);
    const [isHovered, setIsHovered] = useState(false);

    const onChangeCounters = () => {
        // console.log(form.getFieldsValue());
        // console.log(timeRange);
        if (form.getFieldsValue().usage !== undefined) {
            setCounters(prevCounters => ({
                ...prevCounters,
                usage: form.getFieldsValue().usage
            }));
        }
        if (form.getFieldsValue().interface !== undefined) {
            setCounters(prevCounters => ({
                ...prevCounters,
                interface: form.getFieldsValue().interface
            }));
        }
        if (form.getFieldsValue().modules !== undefined) {
            setCounters(prevCounters => ({
                ...prevCounters,
                modules: form.getFieldsValue().modules
            }));
        }
        if (form.getFieldsValue().ai !== undefined) {
            setCounters(prevCounters => ({
                ...prevCounters,
                ai: form.getFieldsValue().ai
            }));
        }

        setSelectCountersModalOpen(false);
    };

    useEffect(() => {
        document.querySelector(".ant-layout .ant-layout-content")?.scrollTo(0, 0);
        // 在组件挂载时设置默认值
        form.setFieldsValue(counters);
    }, []);

    // const handleFocus = e => {
    //     const activeBar = e.target.closest(".ant-picker").querySelector(".ant-picker-active-bar");
    //     if (activeBar) {
    //         activeBar.style.opacity = "0";
    //         activeBar.style.display = "none";
    //     }
    // };
    const commonCardStyle = {
        borderColor: "#E7E7E7",
        borderWidth: "1px",
        borderStyle: "solid",
        boxShadow: "none"
    };

    const allCards = useMemo(
        () => [
            counters.usage.length > 0 &&
                counters.usage.map(usageItem => (
                    <TelemetryCard
                        key={usageItem}
                        name={usageItem}
                        type="usage"
                        timeRange={timeRange}
                        cardstyle={commonCardStyle}
                    />
                )),
            counters.interface.length > 0 &&
                counters.interface.map(interfaceItem => (
                    <TelemetryCard
                        key={interfaceItem}
                        name={interfaceItem}
                        type="interface"
                        timeRange={timeRange}
                        cardstyle={commonCardStyle}
                    />
                )),
            counters.modules.length > 0 &&
                counters.modules.map(modulesItem => (
                    <TelemetryCard
                        key={modulesItem}
                        name={modulesItem}
                        type="modules"
                        timeRange={timeRange}
                        cardstyle={commonCardStyle}
                    />
                ))
        ],
        [counters, timeRange]
    );

    return (
        <Card style={{display: "flex", flexDirection: "column", flex: 1, border: "none"}}>
            <div>
                <div style={{display: "flex", justifyContent: "space-between"}}>
                    <h2 style={{margin: "8px 0 20px"}}>Telemetry Dashboard</h2>
                    <div style={{display: "flex", alignItems: "center", marginTop: "8px", marginBottom: "20px"}}>
                        <div style={{fontSize: "16px"}}>Time</div>
                        <RangePicker
                            showTime={{format: "HH:mm"}}
                            format="YYYY-MM-DD HH:mm"
                            style={{height: "32px", marginLeft: "32px"}}
                            // onFocus={handleFocus}
                            onChange={(_, dateString) => {
                                setTimeRange(dateString);
                            }}
                            placeholder={["Start Date", "End Date"]}
                            disabledDate={current => {
                                const now = new Date();
                                now.setHours(23);
                                now.setMinutes(59);
                                const oneMonthAgo = new Date();
                                oneMonthAgo.setMonth(now.getMonth() - 1);
                                oneMonthAgo.setHours(23);
                                return current && (current > now || current < oneMonthAgo);
                            }}
                            disabledTime={current => {
                                const now = new Date();
                                const currentDay = now.getDate();
                                const currentHours = now.getHours();
                                const currentMinutes = now.getMinutes();

                                const targetDate = new Date(current);
                                const targetDay = targetDate.getDate();
                                const targetHours = targetDate.getHours();
                                const disabledTimeLists = {};
                                if (!current || targetDay === currentDay) {
                                    disabledTimeLists.disabledHours = () =>
                                        Array.from(
                                            {length: 24 - currentHours + 1},
                                            (_, hour) => hour + currentHours + 1
                                        );
                                    disabledTimeLists.disabledMinutes = () =>
                                        !current || targetHours === currentHours
                                            ? Array.from(
                                                  {length: 60 - currentMinutes + 1},
                                                  (_, minute) => minute + currentMinutes + 1
                                              )
                                            : [];
                                }
                                return disabledTimeLists;
                            }}
                        />
                        <Divider type="vertical" style={{height: "30px", marginLeft: "16px", marginRight: "16px"}} />
                        <Button
                            style={{borderColor: isHovered ? "#34DCCF" : "#d9d9d9"}}
                            icon={<Icon component={isHovered ? settingGreenSvg : settingGreySvg} />}
                            onClick={() => setSelectCountersModalOpen(true)}
                            onMouseEnter={() => {
                                setIsHovered(true);
                            }}
                            onMouseLeave={() => {
                                setIsHovered(false);
                            }}
                        />
                        {/* <Button
                            icon={<Icon component={settingGreenSvg} />}
                            onClick={() => setSelectCountersModalOpen(true)}
                        /> */}
                    </div>
                </div>
            </div>
            <div style={{width: "100%", marginBottom: "18px"}}>
                <Row gutter={[24, 24]}>
                    {allCards.flat().map((card, index) =>
                        card ? (
                            <Col key={index} span={24} xxl={12} style={{display: "flex", justifyContent: "center"}}>
                                {card}
                            </Col>
                        ) : null
                    )}
                </Row>
            </div>
            <TelemetrySetting
                form={form}
                isModalOpen={isSelectCountersModalOpen}
                onCancel={() => {
                    form.setFieldsValue(counters);
                    setSelectCountersModalOpen(false);
                }}
                onChange={onChangeCounters}
                showAI={false}
            />
        </Card>
    );
};

export const TelemetryCard = forwardRef(({name, type, timeRange, cardstyle, target = ""}, ref) => {
    const [chartData, setChartData] = useState([]);
    const [xAxisData, setXAxisData] = useState([]);
    const [topK, setTopK] = useState(5);
    const [xAxisInterval, setXAxisInterval] = useState(1);

    const fetchData = async () => {
        let response;
        if (type === "usage") {
            response = await fetchUsageTopK(name, topK, target, timeRange[0], timeRange[1]);
        } else if (type === "interface") {
            response = await fetchInterfaceTopK(name, topK, target, timeRange[0], timeRange[1]);
        } else if (type === "modules") {
            response = await fetchModulesTopK(name, topK, target, timeRange[0], timeRange[1]);
        } else {
            response = await fetchInterfaceTopK(name, topK, target, timeRange[0], timeRange[1]);
        }

        if (response.status !== 200) {
            message.error(response.info);
            setChartData([]);
            setXAxisData([]);
        } else if (response.data.length > 0) {
            const series = response.data.map(item =>
                pfcKind.includes(name)
                    ? {
                          name: `${item.target}: ${item.interface_name}--${item.queue_name}`,
                          data: item.values.map(([x, y]) => [x, y])
                      }
                    : {
                          name: `${item.target}: ${item.interface_name}`,
                          data: item.values.map(([x, y]) => [x, y])
                      }
            );
            setChartData(series);
            const xAxisData = Array.from(new Set(response.data.flatMap(item => item.values.map(([x]) => x)))).sort();
            if (timeRange[0] && timeRange[1]) {
                const totalPoints = xAxisData.length;
                const interval = Math.floor(totalPoints / 5);
                setXAxisInterval(interval);
            } else {
                setXAxisInterval(1);
            }
            setXAxisData(xAxisData);
        } else {
            setChartData([]);
            setXAxisData([]);
        }
    };

    const option = {
        tooltip: {
            trigger: "axis",
            formatter: params => {
                const sortedParams = params.sort((a, b) => b.value[1] - a.value[1]);
                let content = `
                <div style="width: 100%; margin: 0; padding: 0;">
                    <div style="background-color: #F8FAFB; width: calc(100% + 22px); padding: 5px;padding-left:14px; margin: -11px -12px 10px -11px;border-bottom: 1px solid #F2F2F2;">
                        <div style="font-size: 16px;front-weight: 600 ; color: #212519">${params[0].name}</div>
                    </div>
            `;
                sortedParams.forEach(item => {
                    content += `
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                          <div style="display: flex; align-items: center;">
                            <span style="display:inline-block;margin-right:5px;border-radius:1px;width:12px;height:12px;background-color:${item.color};"></span>
                            <span style="front-weight: 400; font-size: 14px; color: #929A9E; margin-letf: 2px;">${item.seriesName}</span>
                          </div>
                          <span style="margin-left: 20px;front-weight: 400; font-size: 14px; color: #212519">${item.value[1]}</span>
                        </div>
                    `;
                });
                content += `</div>`;

                return content;
            },
            position(pos, params, el, elRect, size) {
                const obj = {};
                const [x, y] = pos;
                const tooltipWidth = el.getBoundingClientRect().width;
                const parentRect = el.parentElement.getBoundingClientRect();
                const rightSpace = parentRect.width - x;
                if (y > window.innerHeight / 2) {
                    obj.bottom = "30px";
                    delete obj.top;
                }
                if (rightSpace < x - 10 - tooltipWidth) {
                    obj.left = `${x - tooltipWidth - 10}px`;
                } else {
                    obj.left = `${x + 10}px`;
                }

                return obj;
            }
        },
        legend: {
            data: chartData.map(item => item.name),
            orient: "horizontal", // 设置图例的方向为水平
            top: "88%", // 设置图例的垂直位置
            left: "3%", // 设置图例的水平位置
            right: "5%",
            textStyle: {
                // 图例文字样式
                fontSize: 15
            },
            itemWidth: 10, // 图例图形的宽度
            itemHeight: 10, // 图例图形的高度
            type: "scroll",
            pageIconColor: "#A2ACB2", // 默认可点击色值
            pageIconInactiveColor: "#E3E5EB", // 不可点击色值
            width: "95%",
            icon: "rect"
        },
        grid: {
            left: "3%",
            right: "3%",
            top: "5%",
            bottom: "10%",
            containLabel: true,
            width: "95%",
            height: "75%"
        },
        xAxis: {
            type: "category",
            data: xAxisData,
            axisLabel: {
                interval: xAxisInterval,
                formatter(value) {
                    const date = new Date(value);
                    const startDate = new Date(timeRange[0] || Date.now() - 5 * 60 * 1000);
                    const endDate = new Date(timeRange[1] || Date.now());
                    const hour = date.getHours().toString().padStart(2, "0");
                    const minute = date.getMinutes().toString().padStart(2, "0");
                    const second = date.getSeconds().toString().padStart(2, "0");
                    if (startDate.getMonth() !== endDate.getMonth() || startDate.getDate() !== endDate.getDate()) {
                        return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, "0")}-${date.getDate().toString().padStart(2, "0")} ${hour}:${minute}`;
                    }
                    return `${hour}:${minute}:${second}`;
                }
            },
            splitLine: {
                show: true
            }
        },
        yAxis: {
            type: "value",
            axisLabel: {
                formatter(value) {
                    if (value > 1e9) {
                        return `${value.toExponential(2)}`;
                    }
                    if (value >= 1000000) {
                        return `${value / 1000000}M`;
                    }
                    if (value >= 1000) {
                        return `${value / 1000}k`;
                    }
                    return value;
                }
            }
        },
        series: chartData.map(item => ({
            name: item.name,
            type: "line",
            data: item.data,
            symbol: "none"
        })),
        width: "100%",
        height: "180px"
    };

    useImperativeHandle(ref, () => ({
        refreshTelemetry: () => {
            fetchData();
        }
    }));

    useEffect(() => {
        fetchData();
    }, [name, timeRange, topK]);

    let label;
    if (type === "usage") {
        label = <span>{usageOptions[name]}</span>;
    } else if (type === "interface") {
        label = <span>{interfaceCheckboxOptions[name]}</span>;
    } else if (type === "modules") {
        label = <span>{modulesCheckboxOptions[name]}</span>;
    } else {
        label = <span>{aiCheckboxOptions[name]}</span>;
    }

    if (name === "both") {
        label = <span>Usage</span>;
    }

    return (
        <Card
            title={
                <div style={{display: "flex", justifyContent: "space-between", alignItems: "center"}}>
                    {label}
                    <Select
                        style={{width: 120}}
                        onChange={value => {
                            setTopK(parseInt(value));
                        }}
                        defaultValue="5"
                    >
                        <Option value="5">Top 5</Option>
                        <Option value="10">Top 10</Option>
                        <Option value="25">Top 25</Option>
                    </Select>
                </div>
            }
            bordered={false}
            style={{
                height: "350px",
                width: "100%",
                ...(cardstyle ?? {})
            }}
            className="linechart"
        >
            {option.series.length === 0 ? (
                <div style={{display: "flex", justifyContent: "center", alignItems: "center"}}>
                    <Empty image={EmptyPic} description="No Data" imageStyle={{display: "block", margin: 0}} />
                </div>
            ) : (
                <CustomLineChart chartOption={option} />
            )}
        </Card>
    );
});

export const TelemetrySetting = ({form, isModalOpen, onCancel, onChange, showAI}) => {
    const formItems = () => {
        return (
            <>
                <div style={{fontSize: "18px", fontWeight: "bold", marginBottom: "10px"}}>Port</div>
                <Form.Item name="interface" label="">
                    <Checkbox.Group>
                        <Row gutter={[16, 16]}>
                            <>
                                {Object.entries(interfaceCheckboxOptions).map(([value, title]) => (
                                    <Col span={8}>
                                        <Checkbox value={value}>{title}</Checkbox>
                                    </Col>
                                ))}
                            </>
                        </Row>
                    </Checkbox.Group>
                </Form.Item>
                <div style={{fontSize: "18px", fontWeight: "bold", marginBottom: "10px"}}>Modules</div>
                <Form.Item name="modules" label="">
                    <Checkbox.Group>
                        <Row gutter={[16, 16]}>
                            <>
                                {Object.entries(modulesCheckboxOptions).map(([value, title]) => (
                                    <Col span={10}>
                                        <Checkbox value={value}>{title}</Checkbox>
                                    </Col>
                                ))}
                            </>
                        </Row>
                    </Checkbox.Group>
                </Form.Item>
                {showAI && (
                    <>
                        <div style={{fontSize: "18px", fontWeight: "bold", marginBottom: "10px"}}>AI</div>
                        <Form.Item name="ai" label="">
                            <Checkbox.Group>
                                <Row gutter={[16, 16]}>
                                    <>
                                        {Object.entries(aiCheckboxOptions).map(([value, title]) => (
                                            <Col span={10}>
                                                <Checkbox value={value}>{title}</Checkbox>
                                            </Col>
                                        ))}
                                    </>
                                </Row>
                            </Checkbox.Group>
                        </Form.Item>
                    </>
                )}
            </>
        );
    };

    return (
        <AmpConCustomModalForm
            title="All Counters"
            isModalOpen={isModalOpen}
            formInstance={form}
            layoutProps={{
                labelCol: {
                    span: 5
                }
            }}
            CustomFormItems={formItems}
            onCancel={onCancel}
            onSubmit={onChange}
            modalClass="ampcon-middle-modal"
        />
    );
};

export default TelemetryView;
