import FlowChart from "@/modules-ampcon/components/flow_chart";
import React, {memo, useEffect, useRef, useState} from "react";
import LogViewTextareaModal from "@/modules-ampcon/components/log_view_textarea_modal";
import {useNavigate} from "react-router-dom";
import {Button, Card, Col, Dropdown, List, Progress, Radio, Row, Space, Typography, Skeleton} from "antd";
import PanelStatisticComponent from "@/modules-ampcon/components/panel_statistic";
import {LogModal, ViewExportModal} from "@/modules-ampcon/components/custom_modal";
import {DownOutlined, FileSearchOutlined} from "@ant-design/icons";
import {BarEcharts, BasePieEcharts, PieEcharts} from "@/modules-ampcon/components/echarts_common";
import {
    fetchAllSwitch,
    fetchConfigTemplate,
    fetchDeploymentTask,
    fetchDeploymentOnlyTask,
    fetchLicenseExpiring,
    fetchLicenseStatus,
    fetchPlatformModel,
    fetchStatistic,
    fetchSwitchStatus,
    queryConfig,
    queryReport
} from "@/modules-ampcon/apis/dashboard_api";
import {
    AmpConCustomModalTable,
    AmpConCustomTable,
    createColumnConfig,
    TableFilterDropdown
} from "@/modules-ampcon/components/custom_table";
import GeneralConfigGrey from "./resource/General_Config.svg?react";
import GlobalConfigGrey from "./resource/Global_Config.svg?react";
import HardwareModelConfiguredGrey from "./resource/Hardware_Model_Configured.svg?react";
import RetrievedConfigGrey from "./resource/Retrieved_Config.svg?react";
import SiteConfigGrey from "./resource/Site_Config.svg?react";
import TemplateGrey from "./resource/Template.svg?react";
import GeneralConfigHover from "./resource/General_Config_Hover.svg?react";
import GlobalConfigHover from "./resource/Global_Config_Hover.svg?react";
import HardwareModelConfiguredHover from "./resource/Hardware_Model_Configured_Hover.svg?react";
import RetrievedConfigHover from "./resource/Retrieved_Config_Hover.svg?react";
import SiteConfigHover from "./resource/Site_Config_Hover.svg?react";
import TemplateHover from "./resource/Template_Hover.svg?react";
import PlaybooksNumberSvg from "./resource/Playbooks_Number.svg?react";
import JobsNumberSvg from "./resource/Jobs_Number.svg?react";
import IDLEJobes from "./resource/IDLE_Jobs.svg?react";
import RunningJobsSvg from "./resource/Running_Jobs.svg?react";
import ExecutedJobsSvg from "./resource/Executed_Jobs.svg?react";
import styles from "./switch_view.module.scss";

const {Text} = Typography;

const SwitchView = () => {
    const [deploymentData, setDeploymentData] = useState({});
    const [licenseUsageData, setLicenseUsageData] = useState([]);
    const [isDeployMentModalOpen, setIsDeployMentModalOpen] = useState(false);
    const navigate = useNavigate();

    const fetchData = async () => {
        document.querySelector(".ant-layout .ant-layout-content")?.scrollTo(0, 0);
        await fetchSwitchStatus().then(res => {
            if (res.status === 200) {
                setDeploymentData(res.data);
            }
        });
        await fetchLicenseStatus().then(res => {
            if (res.status === 200) {
                const data = [];
                res.data.map(item => {
                    data.push({value: item.value, name: `${item.name} ${item.value}`});
                });
                setLicenseUsageData(data);
            }
        });
    };

    const handleDeploymentButton = () => {
        setIsDeployMentModalOpen(true);
    };

    const onCloseDeployMentModal = () => {
        setIsDeployMentModalOpen(false);
    };

    useEffect(() => {
        fetchData();
        const intervalId = setInterval(fetchData, 600000);
        return () => {
            clearInterval(intervalId);
        };
    }, []);

    const makeDeploymentData = () => {
        return [
            {value: deploymentData.parking, name: "Parking Switch"},
            {value: deploymentData.deployed, name: "Deployed Switch"},
            {value: deploymentData.imported, name: "Imported Switch"},
            {
                value: deploymentData.configured + deploymentData.staged,
                name: "Configured/Staged"
            },
            {value: deploymentData.decom, name: "Decom Switch"},
            {value: deploymentData.provisioning, name: "Provisioning Switch"}
        ];
    };

    return (
        <div className={styles.switchView}>
            <ConfigTemplateCard />
            <StatisticsRow />
            <Card
                style={{height: "100%"}}
                title={<div className={styles.card_title}>Deployment and Lifecycle Work Flow</div>}
                extra={
                    <Button
                        htmlType="button"
                        onClick={() => {
                            navigate("/device/switches");
                        }}
                    >
                        Switch List
                    </Button>
                }
            >
                <FlowChart deploymentData={deploymentData} />
            </Card>

            <Card title={<div className={styles.card_title}>Hardware Models</div>} style={{height: "100%"}}>
                <PieEcharts name="Hardware Models" fetchDataApi={fetchPlatformModel} showPercent={false} />
            </Card>

            <Card title={<div className={styles.card_title}>Deployment</div>} style={{height: "100%"}}>
                <BasePieEcharts name="Deployment" seriesData={makeDeploymentData()} showPercent={false} />
            </Card>

            <Card title={<div className={styles.card_title}>License Expiration</div>} style={{height: "100%"}}>
                <BarEcharts fetchDataApi={fetchLicenseExpiring} colorList={["#1CA3F7"]} />
            </Card>

            <Card title={<div className={styles.card_title}>License Usage</div>} style={{height: "100%"}}>
                <BasePieEcharts
                    name="License Usage"
                    seriesData={licenseUsageData}
                    chartType="ring"
                    colorList={["#FFBB00", "#14C9BB"]}
                    showPercent={false}
                />
            </Card>
            <DeploymentTaskCard handleDeploymentButton={handleDeploymentButton} />
            <ActivityForSwitchModal isModalOpen={isDeployMentModalOpen} onCancel={onCloseDeployMentModal} />
        </div>
    );
};

const ConfigTemplateCard = memo(({refreshInterval}) => {
    const [configData, setConfigData] = useState([]);
    const [gridConfig, setGridConfig] = useState({
        gutter: 1,
        column: 6
    });
    const navigate = useNavigate();
    const [hoveredIndex, setHoveredIndex] = useState(null);

    const fetchData = async () => {
        await fetchConfigTemplate().then(res => {
            if (res.status === 200) {
                const config = [
                    {
                        title: "Global Config",
                        value: res.data.global_file,
                        path: "/device/device_profiles/global_configuration",
                        icon: <GlobalConfigGrey />,
                        hoverIcon: <GlobalConfigHover />
                    },
                    {
                        title: "General Config",
                        value: res.data.general_config,
                        path: "/device/config_templates/push_config",
                        icon: <GeneralConfigGrey />,
                        hoverIcon: <GeneralConfigHover />
                    },
                    {
                        title: "Template",
                        value: res.data.template_file,
                        path: "/device/config_templates/template_list",
                        icon: <TemplateGrey />,
                        hoverIcon: <TemplateHover />
                    },
                    {
                        title: "Site Config",
                        value: res.data.site_file,
                        path: "/device/device_profiles/switch_configuration",
                        icon: <SiteConfigGrey />,
                        hoverIcon: <SiteConfigHover />
                    },
                    {
                        title: "Retrieved Config",
                        value: res.data.retrieved_config,
                        path: "/device/config_templates/config_backup",
                        icon: <RetrievedConfigGrey />,
                        hoverIcon: <RetrievedConfigHover />
                    },
                    {
                        title: "Hardware Model Configured",
                        value: res.data.configured_hardware,
                        path: "/device/device_profiles/switch_model",
                        icon: <HardwareModelConfiguredGrey />,
                        hoverIcon: <HardwareModelConfiguredHover />
                    }
                ];
                setConfigData(config);
            }
        });
    };

    const handleItemClick = path => {
        if (path) {
            navigate(path);
        }
    };

    useEffect(() => {
        fetchData();
        if (refreshInterval) {
            const intervalId = setInterval(fetchData, refreshInterval);
            return () => {
                clearInterval(intervalId);
            };
        }
    }, []);

    const updateGridConfig = () => {
        if (window.innerWidth < 1500) {
            setGridConfig({
                gutter: 1,
                column: 3
            });
        } else {
            setGridConfig({
                gutter: 1,
                column: 6
            });
        }
    };

    useEffect(() => {
        updateGridConfig();

        window.addEventListener("resize", updateGridConfig);
        return () => {
            window.removeEventListener("resize", updateGridConfig);
        };
    }, []);

    return (
        <Card title={<div className={styles.card_title}>Config/Template</div>} style={{height: "100%"}}>
            <List
                style={{marginTop: "10px"}}
                dataSource={configData}
                split={false}
                grid={gridConfig}
                renderItem={(item, index) => {
                    const isLastInRow = (index + 1) % gridConfig.column === 0;
                    return (
                        <List.Item
                            key={item.title}
                            onClick={() => handleItemClick(item.path)}
                            onMouseEnter={() => setHoveredIndex(index)}
                            onMouseLeave={() => setHoveredIndex(null)}
                            style={{
                                cursor: "pointer",
                                marginTop: "20px",
                                display: "flex",
                                alignItems: "center",
                                flexWrap: "nowrap",
                                padding: "16px",
                                position: "relative"
                            }}
                        >
                            {!isLastInRow && (
                                <div
                                    style={{
                                        position: "absolute",
                                        right: "0",
                                        top: "20%",
                                        bottom: "20%",
                                        width: "1px",
                                        backgroundColor: "#E7E7E7"
                                    }}
                                />
                            )}
                            <Row
                                style={{
                                    display: "flex",
                                    justifyContent: "flex-start",
                                    alignItems: "center",
                                    width: "100%",
                                    flexWrap: "nowrap",
                                    minHeight: "70px"
                                }}
                            >
                                <Col
                                    style={{
                                        flex: "0 0 50px",
                                        textAlign: "center",
                                        marginRight: "8px"
                                    }}
                                >
                                    {hoveredIndex === index ? item.hoverIcon : item.icon}
                                </Col>
                                <Col
                                    style={{
                                        flex: "1"
                                    }}
                                >
                                    <Text
                                        style={{
                                            color: hoveredIndex === index ? "#14C9BB" : "inherit"
                                        }}
                                    >
                                        {item.title}
                                    </Text>
                                    <div
                                        className={styles.configTemplateItem}
                                        style={{
                                            marginTop: "4px",
                                            fontWeight: "bold",
                                            color: hoveredIndex === index ? "#14C9BB" : "inherit"
                                        }}
                                    >
                                        {item.value}
                                    </div>
                                </Col>
                            </Row>
                        </List.Item>
                    );
                }}
            />
        </Card>
    );
});

const StatisticsRow = memo(({refreshInterval}) => {
    const [statisticsData, setStatisticsData] = useState(null); // 初始状态为 null
    const [loading, setLoading] = useState(true); // 添加加载状态

    const fetchData = async () => {
        const res = await fetchStatistic();
        if (res.status === 200) {
            setStatisticsData(res.data);
            setLoading(false); // 数据获取完成，设置加载状态为 false
        }
    };

    useEffect(() => {
        fetchData();
        if (refreshInterval) {
            const intervalId = setInterval(fetchData, refreshInterval);
            return () => {
                clearInterval(intervalId);
            };
        }
    }, []);

    return (
        <Card title={<div className={styles.card_title}>Available Licenses</div>} style={{height: "100%"}}>
            {loading ? (
                <Skeleton active />
            ) : (
                <PanelStatisticComponent
                    title={<div className={styles.card_title}>Available Licenses</div>}
                    perCent={statisticsData.license}
                    iconColor="#2BC174"
                    iconBackgroundColor="#EAF9F0"
                    icon={<FileSearchOutlined />}
                    style={{height: "100%"}}
                    onClick={() => {
                        window.open(statisticsData.license_portal_url, "_blank");
                    }}
                />
            )}
        </Card>
    );
});

const DeploymentTaskCard = memo(({refreshInterval, handleDeploymentButton}) => {
    const [automationJobInfo, setAutomationJobInfo] = useState({});
    const taskRadioOptions = [
        {label: "System Tasks", value: "System Tasks"},
        {label: "Automation Jobs", value: "Automation Jobs"}
    ];
    const [taskPanelSelect, setTaskPanelSelect] = useState("System Tasks");
    const systemTaskRef = useRef(null);
    const onChangeTaskPanelSelect = ({target: {value}}) => {
        setTaskPanelSelect(value);
    };

    const sdnTaskPanelContent = () => {
        if (taskPanelSelect === "System Tasks") {
            return <SystemTasksTable systemTaskRef={systemTaskRef} />;
        }
        if (taskPanelSelect === "Automation Jobs") {
            return <AutomationJobsList configData={makeAutomationJobsData()} />;
        }
    };

    const fetchData = async () => {
        await fetchDeploymentTask().then(res => {
            if (res.status === 200) {
                setAutomationJobInfo(res.data);
            }
        });
        systemTaskRef.current.refreshTable();
    };

    useEffect(() => {
        fetchData();
        if (refreshInterval) {
            const intervalId = setInterval(fetchData, refreshInterval);
            return () => {
                clearInterval(intervalId);
            };
        }
    }, []);

    const makeAutomationJobsData = () => {
        const data = [
            {
                title: "Playbooks Number",
                value: automationJobInfo.playbook_count,
                path: "/maintain/automation/ansible_jobs/job_view",
                icon: <PlaybooksNumberSvg />
            },
            {
                title: "Jobs Number",
                value:
                    automationJobInfo.idle_count + automationJobInfo.running_count + automationJobInfo.executed_count,
                path: "/maintain/automation/ansible_jobs/job_view",
                icon: <JobsNumberSvg />
            },
            {
                title: "IDLE Jobs",
                value: automationJobInfo.idle_count,
                path: "/maintain/automation/ansible_jobs/job_view",
                icon: <IDLEJobes />
            },
            {
                title: "Running Jobs",
                value: automationJobInfo.running_count,
                path: "/maintain/automation/ansible_jobs/job_view",
                icon: <RunningJobsSvg />
            },
            {
                title: "Executed Jobs",
                value: automationJobInfo.executed_count,
                path: "/maintain/automation/ansible_jobs/job_view",
                icon: <ExecutedJobsSvg />
            }
        ];
        return data;
    };

    return (
        <Card
            title={<div className={styles.card_title}>Deployment Tasks</div>}
            style={{height: "100%", minHeight: "390px"}}
            extra={
                <Button htmlType="button" onClick={handleDeploymentButton}>
                    Deployment
                </Button>
            }
        >
            <Row style={{margin: "12px 0px 4px"}}>
                <Col span={24}>
                    <Radio.Group
                        options={taskRadioOptions}
                        optionType="button"
                        value={taskPanelSelect}
                        onChange={onChangeTaskPanelSelect}
                    />
                </Col>
            </Row>
            <Row gutter={24}>
                <Col span={24}>{sdnTaskPanelContent()}</Col>
            </Row>
        </Card>
    );
});

const SystemTasksTable = ({systemTaskRef}) => {
    const taskColumns = [
        createColumnConfig("Running Task", "task_name"),
        createColumnConfig("Type", "type"),
        createColumnConfig("Start Time", "start_time"),
        createColumnConfig("Status", "status")
    ];
    const taskMatchFiledList = [{name: "type", matchMode: "fuzzy"}];
    return (
        <AmpConCustomTable
            fetchAPIInfo={fetchDeploymentOnlyTask}
            columns={taskColumns}
            matchFieldsList={taskMatchFiledList}
            ref={systemTaskRef}
        />
    );
};

const AutomationJobsList = ({configData}) => {
    const navigate = useNavigate();
    const handleItemClick = path => {
        if (path) {
            navigate(path);
        }
    };
    return (
        <List
            style={{
                marginTop: "10px"
            }}
            dataSource={configData}
            split={false}
            grid={{gutter: 40, column: 3}}
            renderItem={item => (
                <List.Item
                    key={item.title}
                    onClick={() => handleItemClick(item.path)}
                    onMouseEnter={e => {
                        e.currentTarget.style.backgroundColor = "#F7F8F9";
                        e.currentTarget.style.border = "1px solid #34dccf";
                    }}
                    onMouseLeave={e => {
                        e.currentTarget.style.backgroundColor = "#FAFBFC";
                        e.currentTarget.style.border = "1px solid #E8EBEA";
                    }}
                    style={{
                        cursor: "pointer",
                        padding: "16px",
                        backgroundColor: "#FAFBFC",
                        borderRadius: "4px",
                        display: "flex",
                        alignItems: "center",
                        marginTop: "15px",
                        border: "1px solid #E8EBEA",
                        minWidth: "170px"
                    }}
                >
                    <div style={{flex: "0 0 50px", textAlign: "center", marginRight: "16px", marginTop: "6px"}}>
                        <span style={{fontSize: "24px"}}>{item.icon}</span>
                    </div>
                    <div style={{flex: "1"}}>
                        <Text>{item.title}</Text>
                        <div style={{marginTop: "4px", fontWeight: "bold"}}>{item.value}</div>
                    </div>
                </List.Item>
            )}
        />
    );
};

const ActivityForSwitchModal = ({isModalOpen, onCancel}) => {
    const [selectedRow, setSelectedRow] = useState("");
    const [isViewReportModalOpen, setIsViewReportModalOpen] = useState(false);
    const [isConfigModalOpen, setIsConfigModalOpen] = useState(false);
    const [displayParams, setDisplayParams] = useState({});
    const LogViewTextareaModalRef = useRef(null);

    const searchFieldsList = ["sn", "modified_time"];
    const matchFieldsList = [
        {name: "sn", matchMode: "fuzzy"},
        {name: "modified_time", matchMode: "fuzzy"}
    ];

    const columns = [
        createColumnConfig("Switch SN", "sn", TableFilterDropdown),
        createColumnConfig("Time", "modified_time", TableFilterDropdown),
        {
            title: "Status",
            width: "30%",
            render: (_, record) => renderStatusProgress("status", _, record)
        },
        {
            title: "Progress",
            width: "15%",
            render: (_, record) => renderStatusProgress("progress", _, record)
        },
        {
            title: "Operation",
            render: (_, record) => {
                return (
                    <Space size={24} className="actionLink">
                        {record.status !== "Imported" && renderDropDownMenu(record)}
                        <a
                            onClick={() => {
                                LogViewTextareaModalRef.current.showLogViewTextareaModal(record.sn);
                            }}
                        >
                            Log
                        </a>
                        <a onClick={() => showReport(record)}>Report</a>
                    </Space>
                );
            }
        }
    ];

    const renderStatusProgress = (columnName, text, record) => {
        let rmaText = "";
        if (record.rma) {
            rmaText = "RMA";
        }
        let msg = "";
        if (record.status === "Provisioning Failed") {
            msg = "failed";
        }
        let step = 0;
        if (record.status === "Configured") {
            step = 0;
        } else if (record.status === "Staged") {
            step = 1;
        } else if (["DECOM", "Registered Not-staged", "DECOM-Init", "DECOM-Pending"].includes(record.status)) {
            step = 9;
        } else if (record.status === "Imported") {
            step = 8;
        } else {
            step = record.step + 2;
        }

        const progressWidth = step !== 9 ? (step / 8) * 100 : 0;

        if (columnName === "status") {
            const titles = {
                0: `step1: ${rmaText} ${record.sn} is configured ${msg}`,
                1: `step2: ${rmaText} ${record.sn} is staged ${msg}`,
                2: `step3: ${rmaText} ${record.sn} pushing security config ${msg}`,
                3: `step4: ${rmaText} ${record.sn} pushing regional config ${msg}`,
                4: `step5: ${rmaText} ${record.sn} check license and install license ${msg}`,
                5: `step6: ${rmaText} ${record.sn} check version and upgrade image if needed ${msg}`,
                6: `step7: ${rmaText} ${record.sn} pushing full configuration ${msg}`,
                7: `step8: ${rmaText} ${record.sn} install patched tar file ${msg}`,
                8: `step9: ${rmaText} ${record.sn} deploy success ${msg}`,
                9: `${rmaText} ${record.sn} ${record.status} ${msg}`
            };
            if (record.status === "Imported") {
                return <li>Imported</li>;
            }
            return <li>{titles[step]}</li>;
        }
        if (columnName === "progress") {
            return (
                <Progress
                    percent={progressWidth}
                    showInfo={false}
                    strokeColor={step === 8 ? "#14C9BB" : "#26A5E1"}
                    style={{width: "100%"}}
                />
            );
        }
    };

    const renderDropDownMenu = record => {
        const handleMenuClick = item => {
            if (item.key === "init") {
                setDisplayParams({
                    title: `${record.sn} Config`,
                    params: record.sn
                });
                setIsConfigModalOpen(true);
            } else if (item.key === "rma") {
                setDisplayParams({
                    title: `${record.sn} Config`,
                    params: `${record.sn}.rma`
                });
                setIsConfigModalOpen(true);
            }
        };

        const menuItems = [];
        if (!record.rma) {
            menuItems.push({key: "init", label: "Init Deploy Config"});
            // if (["Provisioning Success", "DECOM", "DECOM-Init", "DECOM-Pending"].includes(record.status)) {
            //     menuItems.push({key: "rma", label: "Current Config"});
            // }
        } else {
            menuItems.push({key: "rma", label: "Current Config"});
        }

        const menuProps = {
            items: menuItems,
            onClick: handleMenuClick
        };

        return (
            <a>
                <Dropdown menu={menuProps} trigger={["click"]}>
                    <a>
                        Display <DownOutlined />
                    </a>
                </Dropdown>
            </a>
        );
    };

    const showReport = record => {
        setIsViewReportModalOpen(true);
        setSelectedRow(record);
    };

    return (
        <div style={{marginTop: "20px"}}>
            <AmpConCustomModalTable
                title="Activity for All Switches"
                selectModalOpen={isModalOpen}
                onCancel={onCancel}
                columns={columns}
                matchFieldsList={matchFieldsList}
                searchFieldsList={searchFieldsList}
                buttonProps={[]}
                fetchAPIInfo={fetchAllSwitch}
                modalClass="ampcon-max-modal"
            />
            <LogViewTextareaModal ref={LogViewTextareaModalRef} />
            <LogModal
                title={`${selectedRow.sn} Report`}
                isModalOpen={isViewReportModalOpen}
                onCancel={() => setIsViewReportModalOpen(false)}
                fetchLogAPI={queryReport}
                fetchAPIParams={[selectedRow.sn]}
                modalClass="ampcon-middle-modal"
            />
            <ViewExportModal
                title={displayParams.title}
                isModalOpen={isConfigModalOpen}
                onCancel={() => setIsConfigModalOpen(false)}
                fetchDataAPI={queryConfig}
                fetchAPIParams={[displayParams.params]}
                modalClass="ampcon-middle-modal"
            />
        </div>
    );
};

export default SwitchView;
