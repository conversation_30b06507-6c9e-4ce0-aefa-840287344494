import React, {useRef, useState} from "react";
import Icon, {QuestionCircleOutlined, PlusOutlined, MinusOutlined} from "@ant-design/icons";
import {Space, Button, Input, Form, message, Divider, Flex, Radio, Tooltip, Row, Col, Spin} from "antd";
import {useForm} from "antd/es/form/Form";

import {easydeployConfigSave, easydeployConfigList, easydeployConfigDelete} from "@/modules-ampcon/apis/roce_api";
import {addSvg} from "@/utils/common/iconSvg";
import {confirmModalAction} from "@/modules-ampcon/components/custom_modal";
import {
    createColumnConfig,
    AmpConCustomTable,
    TableFilterDropdown,
    AmpConCustomModalForm
} from "@/modules-ampcon/components/custom_table";
import {ConfigurationDetails} from "./configuration_details";

const RoCEEasydeploy = ({onSwitchTab}) => {
    const tableRef = useRef(null);
    const [form] = useForm();
    const [formData, setFormData] = useState({});
    const [applyForm] = useForm();

    const [isEdit, setIsEdit] = useState(false);
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [applyConfimModalOpen, setApplyConfirmModalOpen] = useState(false);
    const [isShowSpin, setIsShowSpin] = useState(false);

    const [ApplyDetails, setApplyDetails] = useState(null);

    const columns = [
        createColumnConfig("Sysname", "sysname", TableFilterDropdown),
        {
            ...createColumnConfig("Ports", "port"),
            render: (_, record) => {
                const port = record.port
                    .join(", ")
                    .replace(/_/g, " ")
                    .replace(/\b\w/g, match => match.toUpperCase());
                return record?.port.length > 5 ? (
                    <Tooltip placement="bottom" title={port}>
                        <p style={{maxWidth: 280, overflow: "hidden", textOverflow: "ellipsis"}}>{port}</p>
                    </Tooltip>
                ) : (
                    <span>{port}</span>
                );
            }
        },
        {
            ...createColumnConfig("Queues", "queue"),
            render: (_, record) => {
                const queue = Array.isArray(record.queue) ? record.queue.join(", ") : record.queue;
                return <span>{queue}</span>;
            }
        },
        {
            ...createColumnConfig("RoCE EasyDeploy", "enabled"),
            render: (_, record) => {
                return record.enabled ? <span>Enabled</span> : <span>Disabled</span>;
            }
        },
        {
            title: "Operation",
            render: (_, record) => {
                return (
                    <div>
                        <Space size="large" className="actionLink">
                            <a onClick={() => editConfiguration(record)}>Edit</a>
                            <a
                                onClick={() =>
                                    confirmModalAction("Are you sure want to delete the configuration item?", () =>
                                        delConfiguration(record.id)
                                    )
                                }
                            >
                                Delete
                            </a>
                        </Space>
                    </div>
                );
            }
        }
    ];

    const matchFieldsList = [{name: "sysname", matchMode: "fuzzy"}];
    const searchFieldsList = ["sysname"];

    const onSubmit = async () => {
        const configuration = JSON.parse(JSON.stringify(form.getFieldsValue().configurations));
        if (Array.isArray(configuration)) {
            configuration.forEach((config, index) => {
                if (!config) {
                    configuration[index] = {};
                }
                const sysnameTemp = configuration[index].sysname;
                const selectSysname = [];
                const selectSN = [];
                sysnameTemp?.map(item => {
                    selectSysname.push(item.split(" ")[0]);
                    selectSN.push(item.split(" ")[1]);
                });
                configuration[index].sysname = selectSysname;
                configuration[index].switch_sn = selectSN;
            });
        }

        let data;
        if (isEdit && enable === "disable") {
            configuration.forEach((_, index) => {
                configuration[index].ports = [];
                configuration[index].queue_num = [];
            });
            data = {
                configurations: configuration
            };
        } else {
            data = {
                enable_pfc: enable,
                mode: enable === "disable" ? null : form.getFieldsValue().mode,
                configurations: configuration
            };
        }
        setFormData(data);
        const ret = await easydeployConfigSave(data, "preview");
        if (ret.status === 200) {
            let content = ``;
            ret.commands.map(command => {
                command.cli.map(cli => {
                    content += cli;
                    content += `\n`;
                });
            });
            setApplyDetails(content);
            setIsModalOpen(false);
            setApplyConfirmModalOpen(true);
        } else {
            message.error(ret.msg);
        }
    };
    const onApplyClick = async () => {
        setIsShowSpin(true);
        const ret = await easydeployConfigSave(formData, "save");
        setIsShowSpin(false);
        if (ret.status === 200) {
            message.success(ret.msg);

            form.resetFields();
            tableRef.current.refreshTable();
            setApplyDetails();
            setApplyConfirmModalOpen(false);

            onSwitchTab("overview");
        } else {
            message.error(ret.msg);
        }
    };

    const [enable, setEnable] = useState("enable");
    const [mode, setMode] = useState("");

    const [fields, setFields] = useState([{fabric: null, sysname: [], ports: [], queue_num: []}]);

    const formItems = () => {
        return (
            <>
                <Form.Item
                    name="enabled"
                    label={
                        <>
                            <span style={{marginRight: "4px"}}>RoCE EasyDeploy</span>
                            <Tooltip title="Enables RoCE configurations (lossy mode or lossless mode) in one click. The default mode is lossless mode.">
                                <QuestionCircleOutlined />
                            </Tooltip>
                        </>
                    }
                >
                    <Row gutter={[16, 16]}>
                        <Col span={8}>
                            <Radio.Group value={enable} onChange={e => setEnable(e.target.value)}>
                                <Radio value="enable">
                                    <span style={{marginRight: "26px"}}>Enable</span>
                                </Radio>
                                <Radio value="disable">Disable</Radio>
                            </Radio.Group>
                        </Col>
                    </Row>
                </Form.Item>
                <Form.Item name="mode" label="RoCE Mode" style={{display: enable === "enable" ? "" : "none"}}>
                    <Row gutter={[16, 16]}>
                        <Col span={8}>
                            <Radio.Group value={mode} onChange={e => setMode(e.target.value)}>
                                <Radio value="lossless">
                                    <span style={{marginRight: "4px"}}>Lossless</span>
                                    <Tooltip title="Lossless mode enables both PFC and ECN to avoid packet loss and ensure reliable and lossless data transmission.">
                                        <QuestionCircleOutlined />
                                    </Tooltip>
                                </Radio>
                                <Radio value="lossy">
                                    <span style={{marginRight: "4px"}}>Lossy</span>
                                    <Tooltip title="Lossy mode enables only ECN and allows packet loss to improve network throughput.">
                                        <QuestionCircleOutlined />
                                    </Tooltip>
                                </Radio>
                            </Radio.Group>
                        </Col>
                    </Row>
                </Form.Item>
                <div
                    style={{
                        fontSize: "18px",
                        fontWeight: "bold",
                        borderBottom: "1px solid rgba(5, 5, 5, 0.06)",
                        paddingBottom: "10px",
                        marginLeft: "-32px",
                        marginRight: "-32px",
                        paddingLeft: "32px",
                        paddingRight: "32px",
                        marginBottom: "20px"
                    }}
                >
                    Configuration Details
                </div>
                <Form.List name="configurations" initialValue={fields}>
                    {(fields, {add, remove}) => (
                        <>
                            {fields.map(({key, name, fieldNames, ...restField}, index) => {
                                return (
                                    <Row key={key}>
                                        <Col span={24}>
                                            <ConfigurationDetails
                                                name={name}
                                                index={index}
                                                form={form}
                                                isModalOpen={isModalOpen}
                                                isEdit={isEdit}
                                                enable={enable}
                                            />
                                        </Col>
                                        <Col>
                                            {index === 0 ? (
                                                <Button
                                                    onClick={() => add()}
                                                    style={{
                                                        backgroundColor: "transparent",
                                                        color: "#BFBFBF",
                                                        marginBottom: "24px",
                                                        marginTop: "40px",
                                                        marginLeft: "-130px"
                                                    }}
                                                    type="link"
                                                    icon={<PlusOutlined />}
                                                />
                                            ) : (
                                                <Button
                                                    style={{
                                                        backgroundColor: "transparent",
                                                        color: "#BFBFBF",
                                                        marginBottom: "24px",
                                                        marginLeft: "-130px"
                                                    }}
                                                    type="link"
                                                    icon={<MinusOutlined />}
                                                    onClick={() => {
                                                        remove(index);
                                                    }}
                                                />
                                            )}
                                        </Col>
                                    </Row>
                                );
                            })}
                        </>
                    )}
                </Form.List>
            </>
        );
    };

    const editConfiguration = record => {
        const configurations = [
            {
                config_id: record.id,
                fabric: record.fabric,
                sysname: [`${record.sysname} ${record.switch_sn}`],
                ports: record.port,
                queue_num: record.queue
            }
        ];
        form.setFieldsValue({
            id: record.id,
            enabled: record.enabled ? "enable" : "disable",
            mode: record.mode,
            configurations
        });
        setEnable(record.enabled ? "enable" : "disable");
        setMode(record.mode);
        setIsEdit(true);
        setIsModalOpen(true);
    };

    const delConfiguration = async configId => {
        const data = {
            config_id: configId
        };
        const ret = await easydeployConfigDelete(data);
        if (ret.status === 200) {
            message.success(ret.msg);
        } else {
            message.error(ret.msg);
        }
        tableRef.current.refreshTable();
    };

    return (
        <>
            <AmpConCustomModalForm
                title={isEdit ? "Edit Configuration" : "Create Configuration"}
                isModalOpen={isModalOpen}
                formInstance={form}
                layoutProps={{
                    labelCol: {
                        span: 4
                    }
                }}
                CustomFormItems={formItems}
                onCancel={() => {
                    setIsModalOpen(false);
                    form.resetFields();
                }}
                onSubmit={onSubmit}
                modalClass="ampcon-max-modal"
                footer={[
                    <Divider style={{marginTop: 0, marginBottom: 20}} />,
                    <Button
                        key="cancel"
                        onClick={() => {
                            setIsModalOpen(false);
                            form.resetFields();
                        }}
                    >
                        Cancel
                    </Button>,
                    <Button key="ok" type="primary" onClick={form.submit}>
                        Apply
                    </Button>
                ]}
            />
            <AmpConCustomModalForm
                modalClass="CheckResult-custom-wider-modal"
                title="Confirm RoCE Configurations to Be Applied"
                isModalOpen={applyConfimModalOpen}
                formInstance={applyForm}
                layoutProps={{
                    labelCol: {
                        span: 5
                    }
                }}
                CustomFormItems={
                    <Flex flex={1} layout="horizontal" style={{minHeight: "260.23px"}}>
                        <Input.TextArea
                            style={{
                                height: `${window.innerHeight / 2}px`,
                                border: "none",
                                backgroundColor: "#F8FAFB",
                                fontSize: "16px",
                                borderRadius: "4px",
                                boxShadow: "none",
                                resize: "none",
                                padding: "16px"
                            }}
                            value={ApplyDetails}
                            readOnly
                        />
                    </Flex>
                }
                footer={
                    <Flex vertical>
                        <Divider style={{marginTop: 0, marginBottom: 20}} />
                        <Flex justify="flex-end">
                            <Button
                                onClick={() => {
                                    setApplyConfirmModalOpen(false);
                                    setIsModalOpen(true);
                                    applyForm.resetFields();
                                }}
                            >
                                Cancel
                            </Button>
                            <Button type="primary" onClick={() => onApplyClick()}>
                                Apply
                            </Button>
                        </Flex>
                    </Flex>
                }
                onCancel={() => {
                    form.resetFields();
                    setApplyConfirmModalOpen(false);
                }}
            />
            <AmpConCustomTable
                columns={columns}
                fetchAPIInfo={easydeployConfigList}
                searchFieldsList={searchFieldsList}
                matchFieldsList={matchFieldsList}
                extraButton={
                    <Button
                        type="primary"
                        style={{width: 140, height: 32}}
                        onClick={() => {
                            setIsEdit(false);
                            setIsModalOpen(true);
                            form.setFieldValue("enabled", "enable");
                            form.setFieldValue("mode", "lossless");
                        }}
                    >
                        <Icon component={addSvg} />
                        Configuration
                    </Button>
                }
                ref={tableRef}
            />
            <Spin spinning={isShowSpin} tip="Loading..." fullscreen />
        </>
    );
};

export default RoCEEasydeploy;
