import React, {useEffect, useState, useRef} from "react";
import {Row, Col, Form, Select, TreeSelect, message} from "antd";

import {getFabric} from "@/modules-ampcon/apis/lifecycle_api";
import {
    getFabricSwitches,
    getSwitchPorts,
    easydeployConfigList,
    getFilterSwitchPorts,
    validateEasydeployConfig
} from "@/modules-ampcon/apis/roce_api";

import shrinkSvg from "@/modules-ampcon/pages/Service/Nics/Monitoring/resource/shrink.svg?react";
import unfoldSvg from "@/modules-ampcon/pages/Service/Nics/Monitoring/resource/unfold.svg?react";
import shrinkHoverSvg from "@/modules-ampcon/pages/Service/Nics/Monitoring/resource/shrink_hover.svg?react";
import unfoldHoverSvg from "@/modules-ampcon/pages/Service/Nics/Monitoring/resource/unfold_hover.svg?react";

import style from "./configuration_details.module.scss";

const {Option} = Select;

export const ConfigurationDetails = ({name, index, form, isModalOpen, isEdit, enable}) => {
    const prevSysnames = useRef([]);

    const [record, setRecord] = useState([]);
    const [fabricLists, setFabricLists] = useState([]);
    const [selectedFabric, setSelectedFabric] = useState();
    const [allSysnameLists, setAllSysnameLists] = useState([]);
    const [filteredSysnameLists, setFilteredSysnameLists] = useState([]);
    const [sysname, setSysname] = useState([]);
    const [selectedSysname, setSelectedSysname] = useState([]);
    const [selectedSN, setSelectedSN] = useState([]);
    const [queueLists, setQueueLists] = useState([]);
    const [portLists, setPortLists] = useState([]);
    const [selectPorts, setSelectPorts] = useState([]);

    const [hoveredIcons, setHoveredIcons] = useState({});

    const handleMouseEnter = id => {
        setHoveredIcons(prev => ({...prev, [id]: true}));
    };
    const handleMouseLeave = id => {
        setHoveredIcons(prev => ({...prev, [id]: false}));
    };

    const getIconComponent = (expanded, isHovered) => {
        if (expanded) {
            return isHovered ? shrinkHoverSvg : shrinkSvg;
        }
        return isHovered ? unfoldHoverSvg : unfoldSvg;
    };

    const switcherIcon = ({expanded, id}) => {
        const IconComponent = getIconComponent(expanded, hoveredIcons[id]);

        return (
            <IconComponent
                style={{width: "16px", height: "16px", marginTop: "4px"}}
                alt={expanded ? "shrink" : "unfold"}
                onMouseEnter={() => handleMouseEnter(id)}
                onMouseLeave={() => handleMouseLeave(id)}
            />
        );
    };

    const fetchFabricList = async () => {
        try {
            const response = await getFabric();
            if (response.status === 200) {
                setFabricLists(response.data);
                setFabricLists(prevLists => ["All Fabrics", ...prevLists]);
            } else {
                message.error(response.info);
            }
        } catch (error) {
            message.error("Failed to get Fabric.");
        }
    };

    const getExistingSysnames = () => {
        const existingSysnames = new Set();
        const configurations = form.getFieldValue("configurations") || [];
        configurations.forEach(config => {
            if (config?.sysname) {
                config.sysname.map(item => {
                    existingSysnames.add(item);
                });
            }
        });
        return existingSysnames;
    };

    const onChangeSysname = value => {
        setSysname(value);
        const configuration = form.getFieldsValue().configurations;
        configuration[index].ports = [];

        const selectSysname = [];
        const selectSN = [];
        value.map(item => {
            selectSysname.push(item.split(" ")[0]);
            selectSN.push(item.split(" ")[1]);
        });
        setSelectedSysname(selectSysname);
        setSelectedSN(selectSN);
    };

    const fetchSysnameLists = async () => {
        try {
            const response = await getFabricSwitches();
            if (response.status === 200) {
                const record = await easydeployConfigList(1, 10, [], [], {});
                if (record.status === 200) {
                    setRecord(record.data);
                } else {
                    return message.error(record.msg);
                }
                if (selectedFabric === "All Fabrics") {
                    let sysnameList = Object.values(response.data).flatMap(array =>
                        array
                            .filter(item => item.sysname !== null && item.sysname !== undefined)
                            .map(item => {
                                return `${item.sysname} ${item.switch_sn}`;
                            })
                    );
                    const existingSysnames = getExistingSysnames();
                    const config_ids = form.getFieldsValue().configurations.map(item => item?.config_id);
                    record.data
                        .filter(item => !config_ids.includes(item.id))
                        .map(item => {
                            existingSysnames.add(`${item.sysname} ${item.switch_sn}`);
                        });
                    sysnameList = sysnameList.filter(item => !existingSysnames.has(item));

                    const updatedConfigurations = form.getFieldsValue().configurations;
                    updatedConfigurations[index] = {
                        ...updatedConfigurations[index],
                        sysname: sysnameList
                    };
                    form.setFieldsValue({
                        configurations: updatedConfigurations
                    });
                    onChangeSysname(sysnameList);
                    form.validateFields([
                        ["configurations", index, "sysname"],
                        ["configurations", index, "ports"]
                    ]);
                } else {
                    const sysnameList = response.data[selectedFabric]
                        .filter(item => item.sysname !== null && item.sysname !== undefined)
                        .map(item => {
                            return `${item.sysname} ${item.switch_sn}`;
                        });
                    setAllSysnameLists(sysnameList);

                    const existingSysnames = getExistingSysnames();
                    const config_ids = form.getFieldsValue().configurations.map(item => item?.config_id);
                    record.data
                        .filter(item => !config_ids.includes(item.id))
                        .map(item => {
                            existingSysnames.add(`${item.sysname} ${item.switch_sn}`);
                        });
                    const filteredSysnameList = sysnameList.filter(item => !existingSysnames.has(item));
                    setFilteredSysnameLists(filteredSysnameList);
                }
            } else {
                setAllSysnameLists([]);
                message.error(response.info);
            }
        } catch (error) {
            message.error("Failed to obtain sysname information.");
        }
    };

    const fetchPortLists = async () => {
        form.getFieldsValue().configurations.map(item => {
            const configId = item?.config_id || "";
            item?.sysname.map(async eachSysname => {
                const sysname = eachSysname.split(" ")[0];
                const switch_sn = eachSysname.split(" ")[1];
                const data = {
                    config_id: configId,
                    sysname,
                    switch_sn
                };
                const ret = await validateEasydeployConfig(data);
                if (ret.status !== 200) return;
            });
        });
        let treeData = [
            {
                key: "all_ports",
                value: "all_ports",
                title: "All Ports"
            }
        ];
        if (sysname.length === 1) {
            try {
                const data = {
                    switch_sn: selectedSN,
                    query_model: "RoceEasyDeployConfiguration"
                };
                let hasCheckedPorts = [];
                let hasCheckedQueues = [];
                hasCheckedPorts = record.filter(item => item.switch_sn === selectedSN[0]).flatMap(item => item.port);
                hasCheckedQueues = record.filter(item => item.switch_sn === selectedSN[0]).flatMap(item => item.queue);
                setQueueLists(["0", "1", "2", "4", "5", "7"].filter(item => !hasCheckedQueues.includes(item)));
                // const response = await getSwitchPorts(data);
                const response = await getFilterSwitchPorts(data);
                if (response.status === 200) {
                    if (response.data.length !== 0) {
                        treeData = [
                            {
                                key: "all_ports",
                                value: "all_ports",
                                title: "All Ports"
                            },
                            ...response.data
                                .filter(item => item.enabled === true)
                                .map(item => ({
                                    // disabled: hasCheckedPorts.includes(item.port_name),
                                    title: item.port_name[0].toUpperCase() + item.port_name.slice(1),
                                    value: item.port_name,
                                    key: item.port_name
                                }))
                        ];
                    }
                    setPortLists(treeData);
                } else {
                    message.error(response.info);
                }
            } catch (error) {
                message.error("Failed to obtain port information.");
            }
        }
        if (sysname.length > 1) {
            setPortLists(treeData);
            setSelectPorts(["all_ports"]);
            setQueueLists(["0", "1", "2", "4", "5", "7"]);
            const updatedConfigurations = form.getFieldsValue().configurations;
            updatedConfigurations[index] = {
                ...updatedConfigurations[index],
                ports: ["all_ports"]
            };
            form.setFieldsValue({
                configurations: updatedConfigurations
            });
            form.validateFields([["configurations", index, "ports"]]);
        }
    };

    useEffect(() => {
        const config = form.getFieldsValue().configurations;
        setSelectedFabric(config[index]?.fabric);
        const sysname = config[index]?.sysname;
        setSysname(config[index]?.sysname);
        const selectSysname = [];
        const selectSN = [];
        sysname?.map(item => {
            selectSysname.push(item.split(" ")[0]);
            selectSN.push(item.split(" ")[1]);
        });
        setSelectedSysname(selectSysname);
        setSelectedSN(selectSN);
    }, [isModalOpen]);

    useEffect(() => {
        fetchFabricList();
    }, []);

    useEffect(() => {
        if (selectedFabric) {
            fetchSysnameLists();
        }
    }, [selectedFabric]);

    useEffect(() => {
        if (selectedFabric) {
            const configuration = form.getFieldsValue().configurations || [];
            const currentSysnames = configuration.map(config => config?.sysname).filter(Boolean);

            if (JSON.stringify(currentSysnames) !== JSON.stringify(prevSysnames.current)) {
                const existingSysnames = getExistingSysnames();
                const config_ids = form.getFieldsValue().configurations.map(item => item?.config_id);
                record
                    .filter(item => !config_ids.includes(item.id))
                    .map(item => {
                        existingSysnames.add(`${item.sysname} ${item.switch_sn}`);
                    });
                const sysnameList = allSysnameLists.filter(item => !existingSysnames.has(item));
                setFilteredSysnameLists(sysnameList);
            }
            prevSysnames.current = currentSysnames;
        }
    }, [form.getFieldsValue().configurations]);

    useEffect(() => {
        setPortLists();
        if (sysname) {
            fetchPortLists();
        }
    }, [sysname]);

    // const handlePortSelectChange = selectedKeys => {
    //     if (selectedKeys.includes("all_ports")) {
    //         const allValues = portLists.map(item => item.value).filter(value => value !== "all_ports");
    //         setSelectPorts(allValues);
    //         return allValues;
    //     }
    //     setSelectPorts(selectedKeys);
    //     return selectedKeys;
    // };

    return (
        <Row gutter={8}>
            <Col span={5} style={{marginRight: 24}}>
                <Form.Item
                    // {...restField}
                    name={[name, "fabric"]}
                    label={
                        index === 0 ? (
                            <>
                                Fabric <span className={style.requiredIcon1}>*</span>
                            </>
                        ) : (
                            ""
                        )
                    }
                    labelCol={{span: 24}}
                    wrapperCol={{span: 24}}
                    rules={[{required: true, message: "Please select fabric!"}]}
                >
                    <Select
                        disabled={
                            (isEdit && !!form.getFieldValue(["configurations", name, "config_id"])) ||
                            (isEdit &&
                                enable === "disable" &&
                                !!form.getFieldValue(["configurations", name, "config_id"]))
                        }
                        value={selectedFabric}
                        onChange={value => {
                            setSelectedFabric(value);
                            const configuration = form.getFieldsValue().configurations;
                            configuration[index].sysname = [];
                            configuration[index].ports = [];
                        }}
                        placeholder="Fabric"
                        style={{width: "100%"}}
                    >
                        {fabricLists.map(name => (
                            <Option key={name} value={name}>
                                {name}
                            </Option>
                        ))}
                    </Select>
                </Form.Item>
            </Col>
            <Col span={5} style={{marginRight: 24}}>
                <Form.Item
                    name={[name, "sysname"]}
                    label={
                        index === 0 ? (
                            <>
                                Sysname <span className={style.requiredIcon1}>*</span>
                            </>
                        ) : (
                            ""
                        )
                    }
                    labelCol={{span: 24}}
                    wrapperCol={{span: 24}}
                    rules={[{required: true, message: "Please select sysname!"}]}
                >
                    <Select
                        disabled={
                            selectedFabric === "All Fabrics" ||
                            (isEdit && !!form.getFieldValue(["configurations", name, "config_id"])) ||
                            (isEdit &&
                                enable === "disable" &&
                                !!form.getFieldValue(["configurations", name, "config_id"]))
                        }
                        mode="multiple"
                        showSearch={false}
                        allowClear
                        value={selectedSysname}
                        onChange={value => onChangeSysname(value)}
                        placeholder="Sysname"
                        style={{width: "100%"}}
                    >
                        {filteredSysnameLists.map(name => (
                            <Option key={name} value={name}>
                                {name}
                            </Option>
                        ))}
                    </Select>
                </Form.Item>
            </Col>
            <Col span={5} style={{marginRight: 24}}>
                <Form.Item
                    name={[name, "ports"]}
                    label={
                        index === 0 ? (
                            <>Ports {enable !== "disable" && <span className={style.requiredIcon1}>*</span>}</>
                        ) : (
                            ""
                        )
                    }
                    labelCol={{span: 24}}
                    wrapperCol={{span: 24}}
                    rules={[{required: enable !== "disable", message: "Please select port!"}]}
                >
                    <TreeSelect
                        disabled={sysname?.length > 1 || enable === "disable"}
                        maxTagCount={2}
                        maxTagTextLength={6}
                        treeData={portLists}
                        value={selectPorts}
                        onChange={value => setSelectPorts(value)}
                        // onChange={handlePortSelectChange}
                        treeCheckable
                        showSearch={false}
                        treeDefaultExpandAll
                        switcherIcon={({expanded, value}) => switcherIcon({expanded, id: value})}
                        placeholder="Ports"
                        style={{height: 32, width: "100%"}}
                        allowClear
                        virtual={false}
                        showCheckedStrategy={TreeSelect.SHOW_CHILD}
                    />
                </Form.Item>
            </Col>
            <Col span={5}>
                <Form.Item
                    name={[name, "queue_num"]}
                    label={
                        index === 0 ? (
                            <>Queues {enable !== "disable" && <span className={style.requiredIcon1}>*</span>}</>
                        ) : (
                            ""
                        )
                    }
                    labelCol={{span: 24}}
                    wrapperCol={{span: 24}}
                    rules={[{required: enable !== "disable", message: "Please select queue!"}]}
                >
                    <Select
                        disabled={(isEdit && enable === "disable") || enable === "disable"}
                        mode="multiple"
                        placeholder="Queues"
                        style={{width: "100%"}}
                        allowClear
                    >
                        {queueLists.map(name => (
                            <Option key={name} value={name}>
                                {name}
                            </Option>
                        ))}
                    </Select>
                </Form.Item>
            </Col>
        </Row>
    );
};
