import React, {useState, useEffect, useRef} from "react";
import {Button, Modal, Form, Select, Radio, Row, Col, Input, Space, Tooltip, message, TreeSelect, Divider} from "antd";
import {PlusOutlined, MinusOutlined, QuestionCircleOutlined} from "@ant-design/icons";
import {
    createColumnConfig,
    TableFilterDropdown,
    AmpConCustomTable,
    AmpConCustomModalForm
} from "@/modules-ampcon/components/custom_table";
import {
    getEcnConfigList,
    getFabricSwitches,
    getSwitchPorts,
    saveEcnConfig,
    deleteEcnConfig,
    getFilterSwitchPorts,
    getEcnConfigDetailBySwitch,
    updateEcnConfig
} from "@/modules-ampcon/apis/roce_api";
import CustomQuestionIcon from "./QuestionIcon";
import {confirmModalAction} from "@/modules-ampcon/components/custom_modal";
import CustomTreeSelect from "./customTreeSelect";
import {PaginationTable} from "./ext_table";
import style from "./roce_policies.module.scss";

const {Option} = Select;

const ECNConfig = () => {
    const [selectedSysnames, setSelectedSysnames] = useState([]);
    const [isModalVisible, setIsModalVisible] = useState(false);
    const [form] = Form.useForm();

    const [fabricTreeData, setFabricTreeData] = useState([]);
    const [portTreeData, setPortTreeData] = useState([]);
    const [portDataCache, setPortDataCache] = useState(new Map());
    const [editRecord, setEditRecord] = useState(null);
    const [isEditMode, setIsEditMode] = useState(false);
    const [currentSwitchSn, setCurrentSwitchSn] = useState(null);
    const [easyEcnValue, setEasyEcnValue] = useState("Enabled");
    const [editPortData, setEditPortData] = useState([]); // 添加缺失的editPortData状态

    const queueLists = ["0", "1", "2", "3", "4", "5", "6", "7"];

    const tableRef = useRef(null);

    const fetchFabricTreeData = async () => {
        try {
            const res = await getFabricSwitches();
            if (res && res.data) {
                const fabricKeys = Object.keys(res.data); // ["default"]
                const fabricValues = Object.values(res.data); // [switch array]

                const tree = fabricKeys.map((fabricName, index) => {
                    const switches = fabricValues[index];

                    // Filter out switches with enabled=false
                    const enabledSwitches = switches.filter(sw => sw.enabled === true);

                    return {
                        title: fabricName,
                        value: fabricName,
                        key: fabricName,
                        disabled: true,
                        children: enabledSwitches.map(sw => ({
                            title: sw.sysname || "Unknown",
                            value: JSON.stringify({sn: sw.switch_sn, sysname: sw.sysname}),
                            key: JSON.stringify({sn: sw.switch_sn, sysname: sw.sysname})
                        }))
                    };
                });

                setFabricTreeData(tree);
            }
        } catch (e) {
            message.error("Failed to get fabric switches");
        }
    };

    // 添加端口数据缓存
    const initPortTreeData = portData => {
        const allOccupiedPorts = new Set();
        console.log("portData", portData);
        portData.forEach(item => {
            if (!item.enabled) {
                allOccupiedPorts.add(item.port_name);
            }
        });
        console.log("allOccupiedPorts", allOccupiedPorts);

        const allPortsForDisplay = portData.map(item => ({
            title: item.port_name,
            value: item.port_name,
            key: item.port_name,
            disabled: allOccupiedPorts.has(item.port_name)
        }));

        // 检查是否有任意端口被占用
        const hasAnyPortOccupied = portData.some(item => allOccupiedPorts.has(item.port_name));

        const tree = [
            {
                title: "All Ports",
                value: JSON.stringify(portData.map(item => item.port_name)),
                disabled: hasAnyPortOccupied,
                children: allPortsForDisplay
            }
        ];

        setPortTreeData(tree);
    };

    // 简化 fetchPortsBySn
    const fetchPortsBySn = async (sn, forceRefresh = false) => {
        try {
            const res = await getFilterSwitchPorts({
                switch_sn: sn,
                query_model: "EcnConfigurationDetail"
            });

            if (res && res.status === 200 && Array.isArray(res.data)) {
                const portData = res.data;
                setPortDataCache(prev => new Map(prev).set(sn, portData));
                initPortTreeData(portData);
            } else {
                message.error(res.msg);
            }
        } catch (error) {
            console.error("Failed to fetch filtered ports:", error);
            message.error("Failed to fetch ports");
            setPortTreeData([]);
        }
    };

    // 简化为类似 PFCConfiguration 的端口状态计算函数
    const calculatePortTreeData = (sn, portTreeData, action = null, currentPorts = [], index = null) => {
        const allOccupiedPorts = new Set();
        const currentFormValues = form.getFieldsValue();

        const portData = portTreeData[0].children;
        console.log("portTreeData", portTreeData);
        console.log("portData", portData);

        portData.forEach(item => {
            if (item.disabled) {
                allOccupiedPorts.add(item.value);
            }
        });

        if (currentFormValues.portConfigurations) {
            currentFormValues.portConfigurations.forEach((config, configIndex) => {
                // 修正索引冲突：使用configIndex而不是index
                if (config.sysname) {
                    try {
                        const sysObj = JSON.parse(config.sysname);
                        if (sysObj.sn === sn && config.ports) {
                            let selectedPorts = [];
                            if (config.ports && Array.isArray(config.ports) && config.ports.length > 0) {
                                try {
                                    const allPortsValue = JSON.stringify(portData.map(item => item.value));
                                    if (config.ports[0] === allPortsValue) {
                                        selectedPorts = JSON.parse(config.ports[0]);
                                    } else {
                                        selectedPorts = config.ports;
                                    }
                                } catch (e) {
                                    selectedPorts = config.ports;
                                }
                            }
                            selectedPorts.forEach(port => allOccupiedPorts.add(port));
                        }
                    } catch (e) {
                        console.error("Error parsing sysname or ports:", e);
                    }
                }
            });
        }

        // 根据action处理指定的端口
        if (action === "enable") {
            // 释放指定的端口
            currentPorts.forEach(port => allOccupiedPorts.delete(port));
        } else if (action === "disable") {
            currentPorts.forEach(port => allOccupiedPorts.add(port));
        }

        console.log("allOccupiedPorts", allOccupiedPorts);

        // 计算最终的端口可用性
        const allPortsForDisplay = portData.map(item => ({
            title: item.value,
            value: item.value,
            key: item.value,
            disabled: allOccupiedPorts.has(item.value)
        }));

        console.log("allPortsForDisplay", allPortsForDisplay);

        // 检查是否有任意端口被占用
        const hasAnyPortOccupied = portData.some(item => allOccupiedPorts.has(item.value));

        const tree = [
            {
                title: "All Ports",
                value: JSON.stringify(portData.map(item => item.value)),
                disabled: hasAnyPortOccupied,
                children: allPortsForDisplay
            }
        ];

        setPortTreeData(tree);
    };

    const handleCreate = () => {
        setIsModalVisible(true);
        setEditRecord(null);
        setIsEditMode(false);
        setCurrentSwitchSn(null);
        setEasyEcnValue("Enabled");
        form.resetFields();
        setSelectedSysnames([]);
        form.setFieldsValue({
            easy_ecn: "Enabled",
            ecn_mode: "throughput-first",
            sysname: []
        });

        fetchFabricTreeData();
    };

    const handleModalCancel = () => {
        setIsModalVisible(false);
        setIsEditMode(false);
        setCurrentSwitchSn(null);
        setEasyEcnValue("Enabled");
        form.resetFields();
        setPortTreeData([]);
        setEditPortData([]); // 重置editPortData状态
    };

    const [selectSwicth, setSelectSwitch] = useState([]);

    // 修改编辑处理函数 - 根据switch_sn加载数据
    const handleECNConfigEdit = async record => {
        try {
            setIsEditMode(true);
            setEditRecord(record); // 设置当前编辑的记录
            setCurrentSwitchSn(record.switch_sn);
            setIsModalVisible(true);

            fetchFabricTreeData();
            fetchPortsBySn(record.switch_sn, true);

            // 根据 switch_sn 查询该交换机下所有的 ECN 配置记录
            const detailResponse = await getEcnConfigDetailBySwitch({
                switch_sn: record.switch_sn
            });

            if (detailResponse && detailResponse.status === 200) {
                const configs = detailResponse.data || [];
                const sysnameValue = JSON.stringify({sn: record.switch_sn, sysname: record.sysname});
                const filterSwitch = fabricTreeData.map(fabric => ({
                    ...fabric,
                    children: fabric.children.filter(item => item.value === sysnameValue)
                }));
                setSelectSwitch(filterSwitch);

                // 初始化配置数据
                let mainConfig = null;
                const portConfigurations = [];

                // 处理每个主配置及其详细配置
                configs.forEach(config => {
                    // 记录第一个主配置用于表单初始化
                    if (!mainConfig) {
                        mainConfig = config;
                    }

                    // 处理详细配置数据
                    if (config.details && Array.isArray(config.details) && config.details.length > 0) {
                        config.details.forEach(detail => {
                            // 处理端口数据
                            let ports = [];
                            if (detail.is_all_ports && detail.port) {
                                // 全选端口时，port字段存储的是JSON字符串数组
                                ports = Array.isArray(detail.port) ? [JSON.stringify(detail.port)] : [detail.port];
                            } else {
                                // 非全选时，直接使用port数组
                                ports = Array.isArray(detail.port) ? detail.port : [];
                            }

                            // 处理队列数据
                            let queues = [];
                            if (detail.is_all_queues && detail.queue) {
                                // 全选队列时，queue字段存储的是JSON字符串数组
                                queues = Array.isArray(detail.queue) ? [JSON.stringify(detail.queue)] : [detail.queue];
                            } else {
                                // 非全选时，直接使用queue数组
                                queues = Array.isArray(detail.queue) ? detail.queue : [];
                            }

                            portConfigurations.push({
                                detail_id: detail.id,
                                config_id: config.id,
                                sysname: sysnameValue,
                                ports,
                                queues,
                                min_threshold: detail.min_threshold ?? "",
                                max_threshold: detail.max_threshold ?? "",
                                drop_probability: detail.drop_probability ?? "",
                                ecn_threshold: detail.ecn_threshold ?? "",
                                wred_status: detail.wred_enable ? "True" : "False",
                                is_all_ports: detail.is_all_ports || false,
                                is_all_queues: detail.is_all_queues || false
                            });
                        });
                    }
                });

                // 如果没有详细配置但有主配置，创建一个空的配置项供编辑
                if (portConfigurations.length === 0 && mainConfig) {
                    portConfigurations.push({
                        detail_id: null,
                        config_id: mainConfig.id,
                        sysname: sysnameValue,
                        ports: [],
                        queues: [],
                        min_threshold: "",
                        max_threshold: "",
                        drop_probability: "",
                        ecn_threshold: "",
                        wred_status: "False",
                        is_all_ports: false,
                        is_all_queues: false
                    });
                }

                // 确定Easy ECN状态
                const easyEcnStatus = mainConfig ? (mainConfig.enabled ? "Enabled" : "Disabled") : "Disabled";

                // 设置表单数据
                form.setFieldsValue({
                    sysname: [sysnameValue],
                    easy_ecn: easyEcnStatus,
                    ecn_mode: mainConfig && mainConfig.mode ? mainConfig.mode : "throughput-first",
                    portConfigurations
                });

                // 同步更新Easy ECN状态到组件状态
                setEasyEcnValue(easyEcnStatus);

                console.log("ECN Detail loaded:", {
                    configs,
                    mainConfig,
                    easyEcnStatus,
                    portConfigurations
                });
            } else {
                message.error("加载 ECN 配置详情失败");
            }
        } catch (error) {
            console.error("加载 ECN 配置详情失败:", error);
            message.error("加载配置详情失败");
        }
    };

    const handleSubmit = async values => {
        try {
            const isEasyEcnEnabled = values.easy_ecn === "Enabled";
            let groupedConfigurations = [];
            const configBySwitch = {};

            const portConfigurations = values.portConfigurations || [];
            let selectedSwitches = values.sysname || [];
            if (typeof selectedSwitches === "string") {
                selectedSwitches = [selectedSwitches];
            }

            selectedSwitches.forEach(switchValue => {
                let sysObj = {};
                try {
                    sysObj = JSON.parse(switchValue);
                } catch {
                    /* empty */
                }

                if (sysObj.sysname && sysObj.sn) {
                    configBySwitch[sysObj.sn] = {
                        enabled: isEasyEcnEnabled,
                        mode: isEasyEcnEnabled ? values.ecn_mode : null,
                        sysname: sysObj.sysname,
                        switch_sn: sysObj.sn,
                        config_id: editRecord?.id || null,
                        details: []
                    };
                }
            });

            portConfigurations.forEach(item => {
                if (!item.sysname) return;

                let sysObj = {};
                try {
                    sysObj = JSON.parse(item.sysname);
                } catch {
                    sysObj = JSON.parse(values.sysname);
                }

                if (sysObj.sysname && sysObj.sn && configBySwitch[sysObj.sn]) {
                    const portData = portDataCache.get(sysObj.sn);
                    const allPortsValue = portData ? JSON.stringify(portData.map(item => item.port_name)) : null;
                    const allQueuesValue = JSON.stringify(queueLists);

                    // 增加安全检查
                    const is_all_ports =
                        item.ports && item.ports.length === 1 && allPortsValue && item.ports[0] === allPortsValue;

                    const is_all_queues = item.queues && item.queues.length === 1 && item.queues[0] === allQueuesValue;

                    let ports = [];
                    let queues = [];

                    if (is_all_ports && allPortsValue) {
                        try {
                            ports = JSON.parse(item.ports[0]);
                        } catch (e) {
                            console.error("Failed to parse all ports:", e);
                            ports = item.ports || [];
                        }
                    } else {
                        ports = item.ports || [];
                    }

                    if (is_all_queues) {
                        try {
                            queues = JSON.parse(item.queues[0]);
                        } catch (e) {
                            console.error("Failed to parse all queues:", e);
                            queues = item.queues || [];
                        }
                    } else {
                        queues = item.queues || [];
                    }

                    if (!isEasyEcnEnabled) {
                        // 如果item有config_id且当前没有设置，则使用item的config_id
                        if (item.config_id && !configBySwitch[sysObj.sn].config_id) {
                            configBySwitch[sysObj.sn].config_id = item.config_id;
                        }
                        configBySwitch[sysObj.sn].details.push({
                            detail_id: item.detail_id || null,
                            enabled: false,
                            mode: values.ecn_mode,
                            port: ports,
                            queue: queues,
                            max_threshold: item.max_threshold ? Number(item.max_threshold) : null,
                            min_threshold: item.min_threshold ? Number(item.min_threshold) : null,
                            drop_probability: item.drop_probability ? Number(item.drop_probability) : null,
                            ecn_threshold: item.ecn_threshold ? Number(item.ecn_threshold) : null,
                            wred_enable: item.wred_status ? item.wred_status === "True" : null,
                            is_all_ports,
                            is_all_queues
                        });
                    }
                }
            });

            groupedConfigurations = Object.values(configBySwitch);

            const params = {
                configurations: groupedConfigurations
            };

            if (!isEasyEcnEnabled && groupedConfigurations.length === 0) {
                message.warning("Please select at least one switch when Easy ECN is disabled");
                return;
            }

            // 编辑模式下需要传递 switch_sn 参数
            if (isEditMode && currentSwitchSn) {
                params.switch_sn = currentSwitchSn;
            }

            // 根据编辑模式选择不同的API函数
            const ret = isEditMode ? await updateEcnConfig(params) : await saveEcnConfig(params);
            if (ret.status === 200) {
                message.success(ret.msg);
                setIsModalVisible(false);
                setEditRecord(null);
                setIsEditMode(false);
                setCurrentSwitchSn(null);
                setEasyEcnValue("Enabled");
                form.resetFields();
                setPortTreeData([]);
                setEditPortData([]); // 重置editPortData状态
                tableRef.current.refreshTable();
            } else {
                message.error(ret.msg);
            }
        } catch (error) {
            console.error("Save failed:", error);
            message.error("Save failed");
        }
    };

    const handleDeleteConfirm = async record => {
        try {
            const ret = await deleteEcnConfig({config_id: record.id});
            if (ret.status === 200) {
                message.success(ret.msg);
                tableRef.current.refreshTable();
            } else {
                message.error(ret.msg);
            }
        } catch (e) {
            message.error("deleted failed");
        }
    };

    const formItems = () => {
        return (
            <>
                <Form.Item
                    name="sysname"
                    label="Switch"
                    labelAlign="left"
                    labelCol={{flex: "90px"}}
                    rules={[{required: true, message: "Please select a switch!"}]}
                    initialValue={[]}
                >
                    <CustomTreeSelect
                        disabled={isEditMode}
                        treeData={fabricTreeData}
                        placeholder="Please select switches"
                        style={{width: 280}}
                        multiple
                        treeCheckable
                        showCheckedStrategy={TreeSelect.SHOW_CHILD}
                        onChange={(values, labels) => {
                            setSelectedSysnames(values);
                            const filterChildren = fabricTreeData.map(fabric => ({
                                ...fabric,
                                children: fabric.children.filter(item => values.includes(item.value))
                            }));
                            setSelectSwitch(filterChildren);

                            values.forEach(value => {
                                try {
                                    const obj = JSON.parse(value);
                                    if (obj.sn) {
                                        fetchPortsBySn(obj.sn);
                                    }
                                } catch (e) {
                                    console.error("Failed to parse switch value:", e);
                                }
                            });

                            if (easyEcnValue === "Disabled") {
                                const currentConfigurations = form.getFieldValue("portConfigurations") || [];
                                const newConfigurations = values.map(value => {
                                    const existingConfig = currentConfigurations.find(
                                        config => config.sysname === value
                                    );
                                    return (
                                        existingConfig || {
                                            sysname: value,
                                            ports: [],
                                            queues: [],
                                            min_threshold: "",
                                            max_threshold: "",
                                            drop_probability: "",
                                            ecn_threshold: "",
                                            wred_status: null
                                        }
                                    );
                                });
                                form.setFieldsValue({portConfigurations: newConfigurations});
                            }
                        }}
                        dropdownStyle={{
                            maxHeight: 200,
                            overflow: "auto"
                        }}
                        treeDefaultExpandAll
                        maxTagCount={1}
                        maxTagTextLength={10}
                        maxTagPlaceholder={omittedValues => `+${omittedValues.length} more`}
                        allowClear
                    />
                </Form.Item>

                <Form.Item
                    name="easy_ecn"
                    label={
                        <div style={{display: "flex", alignItems: "center", gap: "4px"}}>
                            Easy ECN
                            <CustomQuestionIcon
                                title="Configure ECN for congestionmanagement through Easy ECN orstandard ECN:· To use Easy ECN for globally default ECNconfigurations in a click, select Enable.. To use standard ECN for fine-tuning ofECN configurations on specific interfacequeues, select Disable"
                                placement="right"
                            />
                        </div>
                    }
                    labelAlign="left"
                    labelCol={{flex: "90px"}}
                    rules={[{required: true, message: "Please select Easy ECN status!"}]}
                    initialValue="Enabled"
                >
                    <Radio.Group
                        onChange={e => {
                            const newValue = e.target.value;
                            setEasyEcnValue(newValue);

                            if (newValue === "Disabled" && selectedSysnames.length > 0) {
                                const portConfigurations = selectedSysnames.map(value => ({
                                    sysname: value,
                                    ports: [],
                                    queues: [],
                                    min_threshold: "",
                                    max_threshold: "",
                                    drop_probability: "",
                                    ecn_threshold: "",
                                    wred_status: null
                                }));
                                form.setFieldsValue({portConfigurations});
                            }
                        }}
                    >
                        <Radio value="Enabled">Enabled</Radio>
                        <Radio value="Disabled">Disabled</Radio>
                    </Radio.Group>
                </Form.Item>
                {easyEcnValue === "Enabled" && (
                    <Form.Item
                        name="ecn_mode"
                        label="ECN Mode"
                        labelAlign="left"
                        labelCol={{flex: "90px"}}
                        rules={[{required: true, message: "Please select ECN mode!"}]}
                        initialValue="throughput-first"
                    >
                        <Radio.Group>
                            <Radio value="throughput-first">
                                Throughput first{" "}
                                <CustomQuestionIcon
                                    title="This mode prioritizes minimizing latency,ensuring that packets experience the leastpossible delay. This is particularlyimportant for real-time applications suchas VolP, video conferencing, or onlinegaming, where even small delays cannegatively impact performance."
                                    placement="right"
                                />
                            </Radio>
                            <Radio value="latency-first">
                                Latency first{" "}
                                <CustomQuestionIcon
                                    title="This mode prioritizes maximizing theamount of data transferred across thenetwork, making it ideal for use casessuch as bulk data transfers or contentdelivery systems."
                                    placement="right"
                                />
                            </Radio>
                        </Radio.Group>
                    </Form.Item>
                )}

                {easyEcnValue === "Disabled" && (
                    <>
                        <div
                            style={{
                                fontSize: "18px",
                                fontWeight: "bold",
                                borderBottom: "1px solid rgba(5, 5, 5, 0.06)",
                                paddingBottom: "10px",
                                marginLeft: "-32px",
                                marginRight: "-32px",
                                paddingLeft: "32px",
                                paddingRight: "32px",
                                marginBottom: "20px",
                                marginTop: "40px"
                            }}
                        >
                            Port Configuration
                        </div>
                        <Form.List
                            name="portConfigurations"
                            initialValue={selectedSysnames.map(value => ({
                                sysname: value,
                                ports: [],
                                queues: [],
                                min_threshold: "",
                                max_threshold: "",
                                drop_probability: "",
                                ecn_threshold: "",
                                wred_status: null
                            }))}
                            rules={[{required: false}]}
                        >
                            {(fields, {add, remove}) => (
                                <>
                                    {fields.map(({key, name}, index) => (
                                        <Row key={key} gutter={8}>
                                            <Form.Item name={[name, "detail_id"]} hidden>
                                                <Input type="hidden" />
                                            </Form.Item>
                                            <Col span="2">
                                                <Form.Item
                                                    name={[name, "sysname"]}
                                                    label={
                                                        index === 0 ? (
                                                            <>
                                                                Switch <span className={style.requiredIcon1}>*</span>
                                                            </>
                                                        ) : (
                                                            ""
                                                        )
                                                    }
                                                    labelCol={{span: 24}}
                                                    wrapperCol={{span: 24}}
                                                    rules={[{required: true, message: "Please select a switch!"}]}
                                                >
                                                    <TreeSelect
                                                        disabled={isEditMode}
                                                        style={{width: "100%"}}
                                                        treeData={selectSwicth}
                                                        placeholder="Select a switch"
                                                        dropdownStyle={{
                                                            maxHeight: 200,
                                                            overflow: "auto"
                                                        }}
                                                        treeDefaultExpandAll
                                                        onChange={value => {
                                                            console.log("value", value);
                                                            if (value !== null) {
                                                                try {
                                                                    const obj = JSON.parse(value);
                                                                    fetchPortsBySn(obj.sn, true);
                                                                } catch (e) {
                                                                    console.error("Error parsing sysname:", e);
                                                                }
                                                            }

                                                            form.setFields([
                                                                {
                                                                    name: ["portConfigurations", name, "ports"],
                                                                    value: []
                                                                }
                                                            ]);
                                                        }}
                                                        allowClear
                                                    />
                                                </Form.Item>
                                            </Col>
                                            <Col span="2">
                                                <Form.Item
                                                    name={[name, "ports"]}
                                                    label={
                                                        index === 0 ? (
                                                            <>
                                                                Ports <span className={style.requiredIcon1}>*</span>
                                                            </>
                                                        ) : (
                                                            ""
                                                        )
                                                    }
                                                    labelCol={{span: 24}}
                                                    wrapperCol={{span: 24}}
                                                    rules={[{required: true, message: "Please select port!"}]}
                                                >
                                                    <CustomTreeSelect
                                                        treeData={portTreeData}
                                                        placeholder="Ports"
                                                        onFocus={() => {
                                                            // 直接获取当前字段的值
                                                            const currentPortValue = form.getFieldValue([
                                                                "portConfigurations",
                                                                name,
                                                                "ports"
                                                            ]);
                                                            const currentSysname = form.getFieldValue([
                                                                "portConfigurations",
                                                                name,
                                                                "sysname"
                                                            ]);
                                                            // console.log("currentPortValue", currentPortValue);
                                                            if (currentSysname && currentPortValue) {
                                                                try {
                                                                    const sysObj = JSON.parse(currentSysname);
                                                                    if (sysObj.sn) {
                                                                        const portData = portDataCache.get(sysObj.sn);
                                                                        if (portData) {
                                                                            // 解析端口值
                                                                            let portsToEnable = [];
                                                                            if (
                                                                                Array.isArray(currentPortValue) &&
                                                                                currentPortValue.length > 0
                                                                            ) {
                                                                                try {
                                                                                    const allPortsValue =
                                                                                        JSON.stringify(
                                                                                            portData.map(
                                                                                                item => item.port_name
                                                                                            )
                                                                                        );
                                                                                    if (
                                                                                        currentPortValue[0] ===
                                                                                        allPortsValue
                                                                                    ) {
                                                                                        portsToEnable = JSON.parse(
                                                                                            currentPortValue[0]
                                                                                        );
                                                                                    } else {
                                                                                        portsToEnable =
                                                                                            currentPortValue;
                                                                                    }
                                                                                } catch (e) {
                                                                                    portsToEnable = currentPortValue;
                                                                                }
                                                                            }
                                                                            console.log("portTreeData", portTreeData);
                                                                            // 直接调用 calculatePortTreeData 释放端口
                                                                            calculatePortTreeData(
                                                                                sysObj.sn,
                                                                                portTreeData,
                                                                                "enable",
                                                                                portsToEnable,
                                                                                name
                                                                            );
                                                                        }
                                                                    }
                                                                } catch (e) {
                                                                    console.error("Error parsing sysname:", e);
                                                                }
                                                            }
                                                        }}
                                                        onBlur={() => {
                                                            console.log("onBlur");
                                                            const currentSysname = form.getFieldValue([
                                                                "portConfigurations",
                                                                name,
                                                                "sysname"
                                                            ]);
                                                            try {
                                                                const sysObj = JSON.parse(currentSysname);
                                                                calculatePortTreeData(
                                                                    sysObj.sn,
                                                                    portTreeData,
                                                                    "disable",
                                                                    editPortData,
                                                                    name
                                                                );
                                                            } catch (e) {
                                                                console.error("Error parsing sysname:", e);
                                                            }
                                                        }}
                                                        onChange={(value, label) => {
                                                            console.log("value", value);
                                                            setEditPortData(value);
                                                        }}
                                                    />
                                                </Form.Item>
                                            </Col>
                                            <Col span="2">
                                                <Form.Item
                                                    name={[name, "queues"]}
                                                    label={
                                                        index === 0 ? (
                                                            <>
                                                                Queues <span className={style.requiredIcon1}>*</span>
                                                            </>
                                                        ) : (
                                                            ""
                                                        )
                                                    }
                                                    labelCol={{span: 24}}
                                                    wrapperCol={{span: 24}}
                                                    rules={[{required: true, message: "Please select queue!"}]}
                                                >
                                                    <CustomTreeSelect
                                                        treeData={[
                                                            {
                                                                title: "All Queues",
                                                                value: JSON.stringify(queueLists),
                                                                children: queueLists.map(queue => ({
                                                                    title: queue.toString(),
                                                                    value: queue.toString(),
                                                                    key: queue.toString()
                                                                }))
                                                            }
                                                        ]}
                                                        placeholder="Queues"
                                                    />
                                                </Form.Item>
                                            </Col>
                                            <Col span="2">
                                                <Form.Item
                                                    name={[name, "min_threshold"]}
                                                    label={index === 0 ? "Min Threshold" : ""}
                                                    labelCol={{span: 24}}
                                                    wrapperCol={{span: 24}}
                                                >
                                                    <Input placeholder="Min Threshold" />
                                                </Form.Item>
                                            </Col>
                                            <Col span="2">
                                                <Form.Item
                                                    name={[name, "max_threshold"]}
                                                    label={index === 0 ? "Max Threshold" : ""}
                                                    labelCol={{span: 24}}
                                                    wrapperCol={{span: 24}}
                                                >
                                                    <Input placeholder="Max Threshold" />
                                                </Form.Item>
                                            </Col>
                                            <Col span="2">
                                                <Form.Item
                                                    name={[name, "drop_probability"]}
                                                    label={index === 0 ? "Drop Probability (%)" : ""}
                                                    labelCol={{
                                                        span: 24,
                                                        style: {
                                                            whiteSpace: "nowrap",
                                                            overflow: "hidden",
                                                            textOverflow: "ellipsis"
                                                        }
                                                    }}
                                                    wrapperCol={{span: 24}}
                                                >
                                                    <Input placeholder="Drop Probability (%)" />
                                                </Form.Item>
                                            </Col>
                                            <Col span="2">
                                                <Form.Item
                                                    name={[name, "ecn_threshold"]}
                                                    label={index === 0 ? "ECN Threshold" : ""}
                                                    labelCol={{
                                                        span: 24,
                                                        style: {
                                                            whiteSpace: "nowrap",
                                                            overflow: "hidden",
                                                            textOverflow: "ellipsis"
                                                        }
                                                    }}
                                                    wrapperCol={{span: 24}}
                                                >
                                                    <Input placeholder="ECN Threshold" />
                                                </Form.Item>
                                            </Col>
                                            <Col span="2">
                                                <Form.Item
                                                    name={[name, "wred_status"]}
                                                    label={index === 0 ? "WRED Status" : ""}
                                                    labelCol={{span: 24}}
                                                    wrapperCol={{span: 24}}
                                                >
                                                    <Select placeholder="WRED Status" style={{width: "100%"}}>
                                                        <Option value="True">True</Option>
                                                        <Option value="False">False</Option>
                                                    </Select>
                                                </Form.Item>
                                            </Col>
                                            <Col>
                                                {index === 0 ? (
                                                    <Button
                                                        onClick={() => {
                                                            // 获取当前顶层选择的交换机
                                                            const currentSysnames = form.getFieldValue("sysname") || [];
                                                            const currentConfigurations =
                                                                form.getFieldValue("portConfigurations") || [];

                                                            // 确定新行要使用的交换机
                                                            const defaultSysname =
                                                                currentSysnames.length > 0 ? currentSysnames[0] : null;

                                                            // 添加新的配置行
                                                            add({
                                                                detail_id: null,
                                                                sysname: editRecord ? defaultSysname : null, // 默认使用第一个选择的交换机
                                                                ports: [],
                                                                queues: [],
                                                                min_threshold: "",
                                                                max_threshold: "",
                                                                drop_probability: "",
                                                                ecn_threshold: "",
                                                                wred_status: null
                                                            });

                                                            if (defaultSysname) {
                                                                if (editRecord === null) {
                                                                    const parsed = JSON.parse(defaultSysname);
                                                                    if (parsed.sn) {
                                                                        // 检查缓存中是否已有该交换机的端口数据
                                                                        if (!portDataCache.has(parsed.sn)) {
                                                                            // 如果没有缓存，则获取端口数据
                                                                            fetchPortsBySn(parsed.sn, false);
                                                                        } else {
                                                                            // 如果有缓存，可选择强制刷新获取最新数据
                                                                            fetchPortsBySn(parsed.sn, true);
                                                                        }
                                                                    }
                                                                }

                                                                // 获取添加后的所有配置的sysname
                                                                const updatedConfigurations = [
                                                                    ...currentConfigurations,
                                                                    {
                                                                        sysname: defaultSysname
                                                                        // ... other fields
                                                                    }
                                                                ];

                                                                // 提取所有唯一的sysname
                                                                const allSysnames = [
                                                                    ...new Set(
                                                                        updatedConfigurations
                                                                            .map(config => config.sysname)
                                                                            .filter(Boolean)
                                                                    )
                                                                ];

                                                                // 同步更新顶层选择器
                                                                const finalSysnames = [
                                                                    ...new Set([...currentSysnames, ...allSysnames])
                                                                ];
                                                                form.setFieldsValue({sysname: finalSysnames});
                                                            }
                                                        }}
                                                        style={{
                                                            backgroundColor: "transparent",
                                                            color: "#BFBFBF",
                                                            marginBottom: "24px",
                                                            marginTop: index === 0 ? "40px" : "0"
                                                        }}
                                                        type="link"
                                                        icon={<PlusOutlined />}
                                                    />
                                                ) : (
                                                    <Button
                                                        style={{
                                                            backgroundColor: "transparent",
                                                            color: "#BFBFBF",
                                                            marginBottom: "24px",
                                                            marginTop: index === 0 ? "40px" : "0"
                                                        }}
                                                        type="link"
                                                        icon={<MinusOutlined />}
                                                        onClick={() => {
                                                            const currentValues = form.getFieldsValue();
                                                            const configToRemove =
                                                                currentValues.portConfigurations[name];

                                                            remove(name);

                                                            // 删除配置项后重新计算端口状态
                                                            if (configToRemove && configToRemove.sysname) {
                                                                try {
                                                                    const sysObj = JSON.parse(configToRemove.sysname);
                                                                    if (sysObj.sn) {
                                                                        setTimeout(() => {
                                                                            const portData = portDataCache.get(
                                                                                sysObj.sn
                                                                            );
                                                                            if (portData) {
                                                                                // 重新初始化端口树以清理被删除配置的端口占用
                                                                                initPortTreeData(portData);
                                                                                // 然后重新计算剩余配置的端口占用
                                                                                calculatePortTreeData(sysObj.sn, [
                                                                                    {
                                                                                        title: "All Ports",
                                                                                        value: JSON.stringify(
                                                                                            portData.map(
                                                                                                item => item.port_name
                                                                                            )
                                                                                        ),
                                                                                        children: portData.map(
                                                                                            item => ({
                                                                                                title: item.port_name,
                                                                                                value: item.port_name,
                                                                                                key: item.port_name,
                                                                                                disabled: !item.enabled
                                                                                            })
                                                                                        )
                                                                                    }
                                                                                ]);
                                                                            }
                                                                        }, 100);
                                                                    }
                                                                } catch (e) {
                                                                    console.error("Error parsing sysname:", e);
                                                                }

                                                                // 更新顶层选择器的逻辑保持不变
                                                                const currentConfigurations =
                                                                    form.getFieldValue("portConfigurations") || [];
                                                                const remainingSysnames = currentConfigurations
                                                                    .filter((_, idx) => idx !== name)
                                                                    .map(config => config.sysname)
                                                                    .filter(Boolean);

                                                                form.setFieldsValue({sysname: remainingSysnames});
                                                            }
                                                        }}
                                                    />
                                                )}
                                            </Col>
                                        </Row>
                                    ))}
                                </>
                            )}
                        </Form.List>
                    </>
                )}
            </>
        );
    };

    const ecnColumns = [
        {
            title: "",
            key: "expand",
            width: 50,
            render: (_, record) => {}
        },
        createColumnConfig("Sysname", "sysname", TableFilterDropdown),
        {
            ...createColumnConfig("Easy ECN", "easy_ecn"),
            render: (_, record) => {
                return <div className="force-wrap-text">{record.enabled ? "True" : "False"}</div>;
            }
        },
        createColumnConfig("Mode", "mode", TableFilterDropdown),
        {
            title: "Operation",
            key: "operation",
            render: (_, record) => (
                <Space size="middle">
                    <Button type="link" onClick={() => handleECNConfigEdit(record)}>
                        Edit
                    </Button>
                    <Button
                        type="link"
                        onClick={() =>
                            confirmModalAction("Are you sure you want to delete the configuration items?", () =>
                                handleDeleteConfirm(record)
                            )
                        }
                    >
                        Delete
                    </Button>
                </Space>
            )
        }
    ];

    return (
        <div>
            <h2>
                ECN Configuration
                <CustomQuestionIcon
                    title="Configure ECN for end-to-end notificationof network congestion without droppingpackets, and make dynamic adjustmentsto ECN thresholds as needed to respondto changing network conditions."
                    placement="right"
                />
            </h2>
            <div style={{marginBottom: 16}}>
                <Button type="primary" icon={<PlusOutlined />} onClick={handleCreate}>
                    Configuration
                </Button>
            </div>
            <PaginationTable columns={ecnColumns} fetchAPIInfo={getEcnConfigList} ref={tableRef} bordered={false} />

            <AmpConCustomModalForm
                title={editRecord ? "Edit ECN Configuration" : "Create ECN Configuration"}
                isModalOpen={isModalVisible}
                formInstance={form}
                layoutProps={{
                    labelCol: {
                        span: 4
                    }
                }}
                CustomFormItems={formItems}
                onCancel={handleModalCancel}
                onSubmit={handleSubmit}
                modalClass="ampcon-max-modal"
                footer={[
                    <Divider style={{marginTop: 0, marginBottom: 20}} />,
                    <Button key="cancel" onClick={handleModalCancel}>
                        Cancel
                    </Button>,
                    <Button key="ok" type="primary" onClick={form.submit}>
                        Apply
                    </Button>
                ]}
            />
        </div>
    );
};

export default ECNConfig;
