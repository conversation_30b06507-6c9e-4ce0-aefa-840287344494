import React, {useState, useEffect, useRef} from "react";
import {<PERSON>ton, Tooltip, Form, Space, Row, Col, Select, Input, TreeSelect, message, Divider, InputNumber} from "antd";
import {QuestionCircleFilled, PlusOutlined, MinusOutlined} from "@ant-design/icons";
import CustomTreeSelect from "./customTreeSelect";
import {
    AmpConCustomTable,
    createColumnConfig,
    TableFilterDropdown,
    AmpConCustomModalForm
} from "@/modules-ampcon/components/custom_table";
import {
    getFabricSwitches,
    getSwitchPorts,
    getFilterSwitchPorts,
    getPfcBufferTrafficConfigList,
    savePfcBufferConfig,
    updatePfcBufferConfig,
    deletePfcBufferConfig,
    getPfcBufferConfigDetailBySwitch
} from "@/modules-ampcon/apis/roce_api";
import {confirmModalAction} from "@/modules-ampcon/components/custom_modal";
import CustomQuestionIcon from "./QuestionIcon";
import {ExtendedTable} from "./ext_table";

const PFCBufferConfiguration = () => {
    const [isBufferModalVisible, setIsBufferModalVisible] = useState(false);
    const [bufferForm] = Form.useForm();

    const [headerPortTreeData, setHeaderPortTreeData] = useState([]); // 表头的端口选项数据

    // 模态框的状态
    const [fabricTreeData, setFabricTreeData] = useState([]); // 模态框的交换机树形数据
    // 添加统一的端口树数据状态，参考PFCConfiguration
    const [ingressPortTreeData, setIngressPortTreeData] = useState([]);
    const [egressPortTreeData, setEgressPortTreeData] = useState([]);
    const [editPortData, setEditPortData] = useState([]);

    const [trafficType, setTrafficType] = useState("Ingress Queue");

    // 为两种类型的表格分别创建 ref
    const ingressTableRef = useRef(null);
    const egressTableRef = useRef(null);

    // 在组件开头添加状态变量来区分是创建还是编辑模式
    const [isEditMode, setIsEditMode] = useState(false);

    const queueList = Array.from({length: 8}, (_, i) => i);

    // 为表头选择器添加专门的状态
    const [selectHeaderSysname, setSelectHeaderSysname] = useState(null);
    const [selectHeaderPort, setSelectHeaderPort] = useState([]);

    // 添加端口数据缓存，参考SchedulingConfig
    const [portDataCache, setPortDataCache] = useState(new Map());

    // 表头 sysname 变化处理函数
    const handleHeaderSysnameChange = value => {
        setSelectHeaderSysname(value);
        // 清空端口选择
        setSelectHeaderPort([]);
    };

    // 表头 port 变化处理函数
    const handleHeaderPortChange = (value, label) => {
        let headerPorts = [];
        if (label[0] === "All Ports") {
            // 如果选择了 "All Ports"，解析其值获取所有端口
            headerPorts = JSON.parse(value);
        } else {
            headerPorts = value;
        }
        setSelectHeaderPort(headerPorts);
    };

    // 监听表头 sysname 变化
    useEffect(() => {
        const updateHeaderPorts = async () => {
            if (selectHeaderSysname) {
                try {
                    const parsed = JSON.parse(selectHeaderSysname);
                    if (parsed.sn) {
                        const portTree = await fetchHeaderPorts(parsed.sn);
                        setHeaderPortTreeData(portTree);
                    }
                } catch (error) {
                    setHeaderPortTreeData([]);
                }
            } else {
                setHeaderPortTreeData([]);
                setSelectHeaderPort([]);
            }
        };
        updateHeaderPorts();
    }, [selectHeaderSysname]);

    // 监听 trafficType 变化，更新 currentTableRef
    useEffect(() => {
        if (trafficType === "Ingress Queue") {
            ingressTableRef.current.refreshTable();
        } else {
            egressTableRef.current.refreshTable();
        }
    }, [trafficType, selectHeaderPort, selectHeaderPort]);

    // 修改获取端口的函数，使用普通的getSwitchPorts（不过滤）
    const fetchHeaderPorts = async switchSn => {
        if (switchSn) {
            try {
                const res = await getSwitchPorts({switch_sn: switchSn});
                if (res && Array.isArray(res.data)) {
                    const tree = [
                        {
                            title: "All Ports",
                            value: JSON.stringify(res.data.map(item => item.port_name)),
                            children: res.data.map(item => ({
                                title: item.port_name,
                                value: item.port_name,
                                key: item.port_name
                            }))
                        }
                    ];
                    return tree;
                }
            } catch (error) {
                message.error("Failed to get ports");
            }
        }
        return [];
    };

    // 初始化端口树数据，参考PFCConfiguration
    const initPortTreeData = (portData, configType) => {
        const allOccupiedPorts = new Set();

        portData.forEach(item => {
            if (!item.enabled) {
                allOccupiedPorts.add(item.port_name);
            }
        });

        const allPortsForDisplay = portData.map(item => ({
            title: item.port_name,
            value: item.port_name,
            key: item.port_name,
            disabled: allOccupiedPorts.has(item.port_name)
        }));

        const hasAnyPortOccupied = portData.some(item => allOccupiedPorts.has(item.port_name));

        const tree = [
            {
                title: "All Ports",
                value: JSON.stringify(portData.map(item => item.port_name)),
                disabled: hasAnyPortOccupied,
                children: allPortsForDisplay
            }
        ];

        if (configType === "ingress") {
            setIngressPortTreeData(tree);
        } else {
            setEgressPortTreeData(tree);
        }
    };

    // 添加计算端口树数据的函数，参考PFCConfiguration
    const calculatePortTreeData = (sn, portTreeData, configType, action = null, currentPorts = [], index = null) => {
        const allOccupiedPorts = new Set();
        const currentFormValues = bufferForm.getFieldsValue();

        const portData = portTreeData[0].children;

        portData.forEach(item => {
            if (item.disabled) {
                allOccupiedPorts.add(item.value);
            }
        });

        // 根据配置类型获取相应的表单字段
        const formFieldName = configType === "ingress" ? "ingressConfigurations" : "egressConfigurations";
        if (currentFormValues[formFieldName]) {
            currentFormValues[formFieldName].forEach((config, configIndex) => {
                if (config.sysname) {
                    try {
                        const sysObj = JSON.parse(config.sysname);
                        if (sysObj.sn === sn && config.ports) {
                            let selectedPorts = [];
                            if (config.ports && Array.isArray(config.ports) && config.ports.length > 0) {
                                try {
                                    const allPortsValue = JSON.stringify(portData.map(item => item.value));
                                    if (config.ports[0] === allPortsValue) {
                                        selectedPorts = JSON.parse(config.ports[0]);
                                    } else {
                                        selectedPorts = config.ports;
                                    }
                                } catch (e) {
                                    selectedPorts = config.ports;
                                }
                            }
                            selectedPorts.forEach(port => allOccupiedPorts.add(port));
                        }
                    } catch (e) {
                        console.error("Error parsing sysname or ports:", e);
                    }
                }
            });
        }

        // 根据action处理指定的端口
        if (action === "enable") {
            currentPorts.forEach(port => allOccupiedPorts.delete(port));
        } else if (action === "disable") {
            currentPorts.forEach(port => allOccupiedPorts.add(port));
        }

        // 计算最终的端口可用性
        const allPortsForDisplay = portData.map(item => ({
            title: item.value,
            value: item.value,
            key: item.value,
            disabled: allOccupiedPorts.has(item.value)
        }));

        // 检查是否有任意端口被占用
        const hasAnyPortOccupied = portData.some(item => allOccupiedPorts.has(item.value));

        const tree = [
            {
                title: "All Ports",
                value: JSON.stringify(portData.map(item => item.value)),
                disabled: hasAnyPortOccupied,
                children: allPortsForDisplay
            }
        ];

        if (configType === "ingress") {
            setIngressPortTreeData(tree);
        } else {
            setEgressPortTreeData(tree);
        }
    };

    // 添加获取过滤端口数据的函数，参考PFCConfiguration
    const fetchFilteredPortsBySn = async (sn, configType, forceRefresh = false) => {
        try {
            // 确定query_model参数
            const queryModel =
                configType === "ingress" ? "PfcBufferIngressConfiguration" : "PfcBufferEgressConfiguration";

            // 检查缓存
            const cacheKey = `${sn}_${queryModel}`;
            let portData = portDataCache.get(cacheKey);

            // 如果缓存中没有数据或者强制刷新，则调用接口
            if (!portData || forceRefresh) {
                const res = await getFilterSwitchPorts({
                    switch_sn: sn,
                    query_model: queryModel
                });

                if (res && Array.isArray(res.data)) {
                    portData = res.data;
                    // 更新缓存
                    setPortDataCache(prev => new Map(prev).set(cacheKey, portData));
                } else {
                    if (configType === "ingress") {
                        setIngressPortTreeData([]);
                    } else {
                        setEgressPortTreeData([]);
                    }
                    return;
                }
            }

            // 初始化端口树数据
            initPortTreeData(portData, configType);
        } catch (error) {
            console.error("Failed to fetch filtered ports:", error);
            message.error("Failed to fetch ports");
            if (configType === "ingress") {
                setIngressPortTreeData([]);
            } else {
                setEgressPortTreeData([]);
            }
        }
    };

    // 修改获取配置列表的包装函数
    const getBufferIngressConfigListWrapper = async (page, pageSize, filterFields, sortFields, searchFields) => {
        let switchSn = null;
        if (selectHeaderSysname) {
            try {
                const parsed = JSON.parse(selectHeaderSysname);
                switchSn = parsed.sn;
            } catch (e) {
                console.error("Failed to parse selectHeaderSysname:", e);
            }
        }

        const extraParams = {
            ...(switchSn ? {switch_sn: [switchSn]} : {}),
            traffic_type: "ingress",
            ...(selectHeaderPort.length > 0 ? {port: selectHeaderPort} : {})
        };

        return await getPfcBufferTrafficConfigList(page, pageSize, filterFields, sortFields, searchFields, extraParams);
    };

    const getBufferEgressConfigListWrapper = async (page, pageSize, filterFields, sortFields, searchFields) => {
        let switchSn = null;
        if (selectHeaderSysname) {
            try {
                const parsed = JSON.parse(selectHeaderSysname);
                switchSn = parsed.sn;
            } catch (e) {
                console.error("Failed to parse selectedSysname:", e);
            }
        }

        const extraParams = {
            ...(switchSn ? {switch_sn: [switchSn]} : {}),
            traffic_type: "egress"
        };

        if (selectHeaderPort && selectHeaderPort.length > 0) {
            if (selectHeaderPort.includes("all_ports")) {
                // 如果选择了 "All Ports"，不需要指定具体端口
                extraParams.port = ["all_ports"];
            } else {
                extraParams.port = selectHeaderPort;
            }
        }

        console.log("Egress extraParams:", extraParams);
        return await getPfcBufferTrafficConfigList(page, pageSize, filterFields, sortFields, searchFields, extraParams);
    };

    const fetchFabricTreeData = async () => {
        try {
            const res = await getFabricSwitches();
            if (res && res.data) {
                const fabricKeys = Object.keys(res.data); // ["default"]
                const fabricValues = Object.values(res.data); // [switch array]

                const tree = fabricKeys.map((fabricName, index) => {
                    const switches = fabricValues[index];

                    // Filter out switches with enabled=false
                    const enabledSwitches = switches.filter(sw => sw.enabled === true);

                    return {
                        title: fabricName,
                        value: fabricName,
                        key: fabricName,
                        disabled: true,
                        children: enabledSwitches.map(sw => ({
                            title: sw.sysname || "Unknown",
                            value: JSON.stringify({sn: sw.switch_sn, sysname: sw.sysname}),
                            key: JSON.stringify({sn: sw.switch_sn, sysname: sw.sysname})
                        }))
                    };
                });

                setFabricTreeData(tree);
            }
        } catch (e) {
            message.error("Failed to get fabric switches");
        }
    };

    // 修改删除处理函数
    const handleDeleteBufferConfig = async record => {
        const ret = await deletePfcBufferConfig({
            config_id: record.id,
            traffic_type: trafficType === "Ingress Queue" ? "ingress" : "egress"
        });
        if (ret.status === 200) {
            message.success(ret.msg);
        } else {
            message.error(ret.msg);
        }
        // 使用当前活动表格刷新
        if (trafficType === "Ingress Queue") {
            ingressTableRef.current.refreshTable();
        } else {
            egressTableRef.current.refreshTable();
        }
    };

    // 初始化时获取Fabric数据
    useEffect(() => {
        fetchFabricTreeData();
    }, []);

    // 修改编辑处理函数，根据switch_sn加载所有相关配置
    const handleBufferEdit = async record => {
        try {
            setIsEditMode(true);
            setIsBufferModalVisible(true);
            fetchFabricTreeData();
            const defaultSysname = JSON.stringify({sn: record.switch_sn, sysname: record.sysname});

            // 根据switch_sn获取该交换机下的所有PFC Buffer配置
            const result = await getPfcBufferConfigDetailBySwitch({
                switch_sn: record.switch_sn
            });

            if (result.status === 200 && result.data) {
                // 处理ingress配置
                const ingressConfigurations = result.data.ingress.map(config => {
                    const sysnameValue = JSON.stringify({sn: config.switch_sn, sysname: config.sysname});

                    let ports = [];
                    let queues = [];
                    try {
                        ports = Array.isArray(config.port) ? config.port : JSON.parse(config.port);
                        queues = Array.isArray(config.queue) ? config.queue : JSON.parse(config.queue);
                    } catch (e) {
                        console.error("Failed to parse port or queue data", e);
                        ports = config.port;
                        queues = config.queue;
                    }

                    return {
                        config_id: config.id,
                        sysname: sysnameValue,
                        ports: config.is_all_ports ? [JSON.stringify(ports)] : ports,
                        ingress_queues: config.is_all_queues ? [JSON.stringify(queues)] : queues,
                        shared_ratio: config.shared_ratio,
                        guaranteed: config.guaranteed,
                        reset_offset: config.reset_offset,
                        headroom: config.headroom,
                        threshold: config.threshold,
                        is_all_ports: config.is_all_ports,
                        is_all_queues: config.is_all_queues
                    };
                });

                // 处理egress配置
                const egressConfigurations = result.data.egress.map(config => {
                    const sysnameValue = JSON.stringify({sn: config.switch_sn, sysname: config.sysname});

                    let ports = [];
                    let queues = [];
                    try {
                        ports = Array.isArray(config.port) ? config.port : JSON.parse(config.port);
                        queues = Array.isArray(config.queue) ? config.queue : JSON.parse(config.queue);
                    } catch (e) {
                        console.error("Failed to parse port or queue data", e);
                        ports = config.port;
                        queues = config.queue;
                    }

                    return {
                        config_id: config.id,
                        sysname: sysnameValue,
                        ports: config.is_all_ports ? [JSON.stringify(ports)] : ports,
                        egress_queues: config.is_all_queues ? [JSON.stringify(queues)] : queues,
                        shared_ratio: config.shared_ratio,
                        threshold: config.threshold,
                        is_all_ports: config.is_all_ports,
                        is_all_queues: config.is_all_queues
                    };
                });

                // 设置表单值
                bufferForm.setFieldsValue({
                    ingressConfigurations:
                        ingressConfigurations.length > 0
                            ? ingressConfigurations
                            : [{config_id: null, sysname: defaultSysname}],
                    egressConfigurations:
                        egressConfigurations.length > 0
                            ? egressConfigurations
                            : [{config_id: null, sysname: defaultSysname}]
                });

                // 获取端口数据，参考PFCConfiguration
                if (ingressConfigurations.length > 0) {
                    await fetchFilteredPortsBySn(record.switch_sn, "ingress", true);
                }
                if (egressConfigurations.length > 0) {
                    await fetchFilteredPortsBySn(record.switch_sn, "egress", true);
                }
            } else {
                message.error(result.msg);
                setIsEditMode(false);
                setIsBufferModalVisible(false);
            }
        } catch (error) {
            console.error("Failed to load PFC Buffer configurations:", error);
            message.error("Failed to load configurations");
            setIsEditMode(false);
            setIsBufferModalVisible(false);
        }
    };

    // 修改取消处理函数
    const handleBufferModalCancel = () => {
        setIsBufferModalVisible(false);
        setIsEditMode(false);
        setIngressPortTreeData([]); // 清空端口数据
        setEgressPortTreeData([]); // 清空端口数据
        setEditPortData([]);
        bufferForm.resetFields();
    };

    // 修改创建按钮的处理函数
    const handleBufferCreate = () => {
        setIsEditMode(false);
        setIsBufferModalVisible(true);
        setIngressPortTreeData([]); // 清空端口数据
        setEgressPortTreeData([]); // 清空端口数据
        setEditPortData([]);
        fetchFabricTreeData();

        // 根据当前选中的 trafficType 设置不同的初始值
        bufferForm.setFieldsValue({
            ingressConfigurations: [{}],
            egressConfigurations: [{}]
        });
    };

    // 修改表格的操作列
    const getOperationColumn = () => ({
        title: "Operation",
        key: "operation",
        render: (_, record) => (
            <Space size="middle">
                <Button type="link" onClick={() => handleBufferEdit(record)}>
                    Edit
                </Button>
                <Button
                    type="link"
                    onClick={() =>
                        confirmModalAction("Are you sure you want to delete the configuration items?", () =>
                            handleDeleteBufferConfig(record)
                        )
                    }
                >
                    Delete
                </Button>
            </Space>
        )
    });

    // 修改保存提交函数，参考SchedulingConfig的处理方式
    const handleBufferSubmit = async values => {
        try {
            const configurations = [];
            let switch_sn = "";

            // 处理 ingressConfigurations
            (values.ingressConfigurations || []).forEach(item => {
                let sysObj = {};
                try {
                    sysObj = JSON.parse(item.sysname);
                } catch (e) {
                    console.error("Failed to parse sysname:", e);
                    return;
                }
                if (!sysObj.sysname || !sysObj.sn) return;

                // 参考SchedulingConfig: 根据实际值动态计算is_all_ports和is_all_queues
                const portData = portDataCache.get(`${sysObj.sn}_PfcBufferIngressConfiguration`);
                const allPortsValue = portData ? JSON.stringify(portData.map(port => port.port_name)) : null;
                const allQueuesValue = JSON.stringify(queueList);

                // 计算is_all_ports - 根据实际值判断
                const is_all_ports = item.ports && item.ports.length === 1 && item.ports[0] === allPortsValue;

                // 计算is_all_queues - 根据实际值判断
                const is_all_queues =
                    item.ingress_queues &&
                    item.ingress_queues.length === 1 &&
                    item.ingress_queues[0] === allQueuesValue;

                let ports = [];
                let queues = [];

                if (is_all_ports) {
                    ports = JSON.parse(item.ports[0]);
                } else {
                    ports = item.ports || [];
                }

                if (is_all_queues) {
                    queues = JSON.parse(item.ingress_queues[0]);
                } else {
                    queues = item.ingress_queues || [];
                }

                switch_sn = sysObj.sn;

                configurations.push({
                    config_id: item.config_id,
                    sysname: sysObj.sysname,
                    switch_sn: sysObj.sn,
                    traffic_type: "ingress",
                    port: ports,
                    queue: queues,
                    is_all_ports, // 动态计算
                    is_all_queues, // 动态计算
                    shared_ratio: item.shared_ratio,
                    threshold: item.threshold,
                    guaranteed: item.guaranteed,
                    reset_offset: item.reset_offset,
                    headroom: item.headroom
                });
            });

            // 处理 egressConfigurations
            (values.egressConfigurations || []).forEach(item => {
                let sysObj = {};
                try {
                    sysObj = JSON.parse(item.sysname);
                } catch (e) {
                    console.error("Failed to parse sysname:", e);
                    return;
                }
                if (!sysObj.sysname || !sysObj.sn) return;

                // 参考SchedulingConfig: 根据实际值动态计算is_all_ports和is_all_queues
                const portData = portDataCache.get(`${sysObj.sn}_PfcBufferEgressConfiguration`);
                const allPortsValue = portData ? JSON.stringify(portData.map(port => port.port_name)) : null;
                const allQueuesValue = JSON.stringify(queueList);

                // 计算is_all_ports - 根据实际值判断
                const is_all_ports = item.ports && item.ports.length === 1 && item.ports[0] === allPortsValue;

                // 计算is_all_queues - 根据实际值判断
                const is_all_queues =
                    item.egress_queues && item.egress_queues.length === 1 && item.egress_queues[0] === allQueuesValue;

                let ports = [];
                let queues = [];

                if (is_all_ports) {
                    ports = JSON.parse(item.ports[0]);
                } else {
                    ports = item.ports || [];
                }

                if (is_all_queues) {
                    queues = JSON.parse(item.egress_queues[0]);
                } else {
                    queues = item.egress_queues || [];
                }

                switch_sn = sysObj.sn;

                configurations.push({
                    config_id: item.config_id,
                    sysname: sysObj.sysname,
                    switch_sn: sysObj.sn,
                    traffic_type: "egress",
                    port: ports,
                    queue: queues,
                    is_all_ports, // 动态计算
                    is_all_queues, // 动态计算
                    shared_ratio: item.shared_ratio,
                    threshold: item.threshold
                });
            });

            // 根据是否处于编辑模式来判断调用哪个接口
            let res;

            if (isEditMode) {
                // 编辑模式：使用 update 接口
                res = await updatePfcBufferConfig({switch_sn, configurations});
            } else {
                // 创建模式：使用 save 接口
                res = await savePfcBufferConfig({configurations});
            }

            if (res.status === 200) {
                message.success(res.msg);
                if (trafficType === "Ingress Queue") {
                    ingressTableRef.current.refreshTable();
                } else {
                    egressTableRef.current.refreshTable();
                }
                setIsBufferModalVisible(false);
                setIngressPortTreeData([]); // 清空端口数据
                setEgressPortTreeData([]); // 清空端口数据
                setEditPortData([]);
                bufferForm.resetFields();
            } else {
                message.error(res.msg);
            }
        } catch (e) {
            console.error("Submit error:", e);
            message.error("Failed to save configuration");
        }
        // finally {

        // }
    };

    // 添加一个通用的渲染函数
    const renderArrayColumn = (text, maxItems = 2) => {
        try {
            const arr = Array.isArray(text) ? text : JSON.parse(text);
            if (!Array.isArray(arr)) return text;

            // 处理特殊值
            if (arr.includes("all_ports")) return "All Ports";
            if (arr.includes("all_queues")) return "All Queues";

            // 处理普通数组
            if (arr.length <= maxItems) {
                return (
                    <div className="force-wrap-text" title={arr.join(", ")}>
                        {arr.join(", ")}
                    </div>
                );
            }

            const displayItems = arr.slice(0, maxItems);
            const remainingCount = arr.length - maxItems;
            const displayText = `${displayItems.join(", ")} +${remainingCount} more`;
            return (
                <div className="force-wrap-text" title={arr.join(", ")}>
                    {displayText}
                </div>
            );
        } catch {
            return (
                <div className="force-wrap-text" title={text}>
                    {text}
                </div>
            );
        }
    };

    // 修改表格列定义
    const bufferColumnsIngress = [
        {
            title: "",
            key: "expand",
            width: 50,
            render: (_, record) => {}
        },
        {
            ...createColumnConfig("Sysname", "sysname", TableFilterDropdown)
        },
        {
            ...createColumnConfig("Port", "port", TableFilterDropdown),
            ellipsis: true,
            render: (_, record) => {
                if (record.is_all_ports) {
                    return "All Ports";
                }
                return renderArrayColumn(record.port);
            }
        },
        {
            ...createColumnConfig("Queue", "queue"),
            ellipsis: true,
            render: (_, record) => {
                if (record.is_all_queues) {
                    return "All Queues";
                }
                return renderArrayColumn(record.queue);
            }
        },
        {
            ...createColumnConfig("Shared Ratio(%)", "shared_ratio")
        },
        {
            ...createColumnConfig("Guaranteed", "guaranteed")
        },
        {
            ...createColumnConfig("Reset Offset", "reset_offset")
        },
        {
            ...createColumnConfig("Headroom", "headroom")
        },
        {
            ...createColumnConfig("Threshold", "threshold")
        },
        getOperationColumn()
    ];

    const bufferColumnsEgress = [
        {
            title: "",
            key: "expand",
            width: 50,
            render: (_, record) => {}
        },
        {
            ...createColumnConfig("Sysname", "sysname", TableFilterDropdown),
            width: 150,
            ellipsis: true
        },
        {
            ...createColumnConfig("Port", "port", TableFilterDropdown),
            width: 200,
            render: (_, record) => {
                if (record.is_all_ports) {
                    return "All Ports";
                }
                return renderArrayColumn(record.port);
            }
        },
        {
            ...createColumnConfig("Queue", "queue"),
            width: 150,
            render: (_, record) => {
                if (record.is_all_queues) {
                    return "All Queues";
                }
                return renderArrayColumn(record.queue);
            }
        },
        {
            ...createColumnConfig("Shared Ratio(%)", "shared_ratio"),
            width: 150,
            ellipsis: true
        },
        {
            ...createColumnConfig("Threshold", "threshold"),
            width: 120,
            ellipsis: true
        },
        getOperationColumn()
    ];

    // 表单项
    const bufferFormItems = () => (
        <Form.Item
            noStyle
            shouldUpdate
            rules={[
                {
                    validator: async (_, value) => {
                        const values = bufferForm.getFieldsValue();
                        const hasIngressData = values.ingressConfigurations?.some(
                            item => item.sysname && item.ports?.length && item.ingress_queues?.length
                        );
                        const hasEgressData = values.egressConfigurations?.some(
                            item => item.sysname && item.ports?.length && item.egress_queues?.length
                        );

                        if (!hasIngressData && !hasEgressData) {
                            return Promise.reject("Please complete either Ingress or Egress configuration");
                        }
                        return Promise.resolve();
                    }
                }
            ]}
        >
            {/* Ingress Buffer Configuration */}
            <div
                style={{
                    fontSize: "18px",
                    fontWeight: "bold",
                    borderBottom: "1px solid rgba(5, 5, 5, 0.06)",
                    paddingBottom: "10px",
                    marginLeft: "-32px",
                    marginRight: "-32px",
                    paddingLeft: "32px",
                    paddingRight: "32px",
                    marginBottom: "20px"
                }}
            >
                Ingress Buffer Configuration
            </div>
            <Form.List name="ingressConfigurations" initialValue={[{}]}>
                {(fields, {add, remove}) => (
                    <>
                        {fields.map(({key, name}, index) => (
                            <Row key={key} gutter={16}>
                                {/* Sysname */}
                                <Col flex="140px">
                                    <Form.Item
                                        name={[name, "sysname"]}
                                        label={index === 0 ? "Sysname" : ""}
                                        labelCol={{span: 24}}
                                        wrapperCol={{span: 24}}
                                        rules={[
                                            {
                                                validator: async (_, value) => {
                                                    // 如果其他字段有值，则 sysname 必填
                                                    const currentConfig = bufferForm.getFieldValue([
                                                        "ingressConfigurations",
                                                        name
                                                    ]);
                                                    if (
                                                        currentConfig.ports?.length ||
                                                        currentConfig.ingress_queues?.length
                                                    ) {
                                                        if (!value) {
                                                            return Promise.reject("Please select sysname!");
                                                        }
                                                    }
                                                    return Promise.resolve();
                                                }
                                            }
                                        ]}
                                    >
                                        <TreeSelect
                                            disabled={isEditMode}
                                            style={{width: "100%"}}
                                            treeData={fabricTreeData}
                                            placeholder="Sysname"
                                            treeDefaultExpandAll
                                            onChange={value => handleModalSysnameChange(value, name, "ingress")}
                                            allowClear
                                        />
                                    </Form.Item>
                                </Col>
                                {/* Ports */}
                                <Col flex="140px">
                                    <Form.Item
                                        name={[name, "ports"]}
                                        label={index === 0 ? "Ports" : ""}
                                        labelCol={{span: 24}}
                                        wrapperCol={{span: 24}}
                                        rules={[
                                            {
                                                validator: async (_, value) => {
                                                    const currentConfig = bufferForm.getFieldValue([
                                                        "ingressConfigurations",
                                                        name
                                                    ]);
                                                    if (currentConfig.sysname || currentConfig.ingress_queues?.length) {
                                                        if (!value?.length) {
                                                            return Promise.reject("Please select ports!");
                                                        }
                                                    }
                                                    return Promise.resolve();
                                                }
                                            }
                                        ]}
                                    >
                                        <CustomTreeSelect
                                            style={{width: "100%"}}
                                            treeData={ingressPortTreeData}
                                            placeholder="Ports"
                                            onFocus={() => {
                                                // 直接获取当前字段的值，参考PFCConfiguration
                                                const currentPortValue = bufferForm.getFieldValue([
                                                    "ingressConfigurations",
                                                    name,
                                                    "ports"
                                                ]);
                                                const currentSysname = bufferForm.getFieldValue([
                                                    "ingressConfigurations",
                                                    name,
                                                    "sysname"
                                                ]);

                                                if (currentSysname && currentPortValue) {
                                                    try {
                                                        const sysObj = JSON.parse(currentSysname);
                                                        if (sysObj.sn) {
                                                            // 解析端口值
                                                            let portsToEnable = [];
                                                            if (
                                                                Array.isArray(currentPortValue) &&
                                                                currentPortValue.length > 0
                                                            ) {
                                                                try {
                                                                    const cacheKey = `${sysObj.sn}_PfcBufferIngressConfiguration`;
                                                                    const portData = portDataCache.get(cacheKey);
                                                                    const allPortsValue = portData
                                                                        ? JSON.stringify(
                                                                              portData.map(item => item.port_name)
                                                                          )
                                                                        : null;
                                                                    if (currentPortValue[0] === allPortsValue) {
                                                                        portsToEnable = JSON.parse(currentPortValue[0]);
                                                                    } else {
                                                                        portsToEnable = currentPortValue;
                                                                    }
                                                                } catch (e) {
                                                                    portsToEnable = currentPortValue;
                                                                }
                                                            }
                                                            // 调用 calculatePortTreeData 释放端口
                                                            calculatePortTreeData(
                                                                sysObj.sn,
                                                                ingressPortTreeData,
                                                                "ingress",
                                                                "enable",
                                                                portsToEnable,
                                                                name
                                                            );
                                                        }
                                                    } catch (e) {
                                                        console.error("Error parsing sysname:", e);
                                                    }
                                                }
                                            }}
                                            onBlur={() => {
                                                const currentSysname = bufferForm.getFieldValue([
                                                    "ingressConfigurations",
                                                    name,
                                                    "sysname"
                                                ]);
                                                if (currentSysname) {
                                                    try {
                                                        const sysObj = JSON.parse(currentSysname);
                                                        calculatePortTreeData(
                                                            sysObj.sn,
                                                            ingressPortTreeData,
                                                            "ingress",
                                                            "disable",
                                                            editPortData,
                                                            name
                                                        );
                                                    } catch (e) {
                                                        console.error("Error parsing sysname:", e);
                                                    }
                                                }
                                            }}
                                            onChange={(value, label) => {
                                                console.log("ingress value", value);
                                                setEditPortData(value);
                                            }}
                                        />
                                    </Form.Item>
                                </Col>
                                {/* Ingress Queues */}
                                <Col flex="140px">
                                    <Form.Item
                                        name={[name, "ingress_queues"]}
                                        label={index === 0 ? "Ingress Queues" : ""}
                                        labelCol={{span: 24}}
                                        wrapperCol={{span: 24}}
                                        // rules={[{required: true, message: "Please select ingress queues!"}]}
                                    >
                                        <CustomTreeSelect
                                            treeData={[
                                                {
                                                    title: "All Queues",
                                                    value: JSON.stringify(queueList),
                                                    children: queueList.map(queue => ({
                                                        title: queue,
                                                        value: queue,
                                                        key: queue
                                                    }))
                                                }
                                            ]}
                                            placeholder="Ingress Queues"
                                            onChange={(value, label) => {
                                                // 简化：只设置实际的队列值，不设置is_all_queues
                                                bufferForm.setFieldValue(
                                                    ["ingressConfigurations", name, "ingress_queues"],
                                                    value
                                                );
                                            }}
                                        />
                                    </Form.Item>
                                </Col>

                                <Form.Item
                                    shouldUpdate={(prev, curr) =>
                                        prev?.ingressConfigurations?.[name]?.shared_ratio !==
                                            curr?.ingressConfigurations?.[name]?.shared_ratio ||
                                        prev?.ingressConfigurations?.[name]?.threshold !==
                                            curr?.ingressConfigurations?.[name]?.threshold
                                    }
                                    noStyle
                                >
                                    {({getFieldValue}) => {
                                        const sharedRatio = getFieldValue([
                                            "ingressConfigurations",
                                            name,
                                            "shared_ratio"
                                        ]);
                                        const threshold = getFieldValue(["ingressConfigurations", name, "threshold"]);

                                        return (
                                            <>
                                                {/* Shared Ratio */}
                                                <Col flex="140px">
                                                    <Form.Item
                                                        name={[name, "shared_ratio"]}
                                                        label={index === 0 ? "Shared Ratio(%)" : ""}
                                                        labelCol={{span: 24}}
                                                        wrapperCol={{span: 24}}
                                                        rules={[
                                                            {
                                                                type: "number",
                                                                min: 1,
                                                                max: 100,
                                                                message: "Shared Ratio must between 1 to 100"
                                                            }
                                                        ]}
                                                    >
                                                        <InputNumber
                                                            placeholder="1-100, default: 5"
                                                            style={{width: "100%"}}
                                                            disabled={!!threshold}
                                                            controls={false}
                                                        />
                                                    </Form.Item>
                                                </Col>

                                                {/* Guaranteed */}
                                                <Col flex="140px">
                                                    <Form.Item
                                                        name={[name, "guaranteed"]}
                                                        label={index === 0 ? "Guaranteed" : ""}
                                                        labelCol={{span: 24}}
                                                        wrapperCol={{span: 24}}
                                                        // rules={[{required: true, message: "Please input guaranteed value!"}]}
                                                        rules={[
                                                            {
                                                                type: "number",
                                                                min: 1,
                                                                max: 65535,
                                                                message: "Guaranteed must between 1 to 65535"
                                                            }
                                                        ]}
                                                    >
                                                        <InputNumber
                                                            placeholder="1-65535, default:16"
                                                            style={{width: "100%"}}
                                                            controls={false}
                                                        />
                                                    </Form.Item>
                                                </Col>
                                                {/* Reset Offset */}
                                                <Col flex="140px">
                                                    <Form.Item
                                                        name={[name, "reset_offset"]}
                                                        label={index === 0 ? "Reset Offset" : ""}
                                                        labelCol={{span: 24}}
                                                        wrapperCol={{span: 24}}
                                                        // rules={[{required: true, message: "Please input reset offset!"}]}
                                                        rules={[
                                                            {
                                                                type: "number",
                                                                min: 1,
                                                                max: 65535,
                                                                message: "Reset Offset must between 1 to 65535"
                                                            }
                                                        ]}
                                                    >
                                                        <InputNumber
                                                            placeholder="1-65535, defualt: 0"
                                                            style={{width: "100%"}}
                                                            controls={false}
                                                        />
                                                    </Form.Item>
                                                </Col>
                                                {/* Headroom */}
                                                <Col flex="140px">
                                                    <Form.Item
                                                        name={[name, "headroom"]}
                                                        label={index === 0 ? "Headroom" : ""}
                                                        labelCol={{span: 24}}
                                                        wrapperCol={{span: 24}}
                                                        // rules={[{required: true, message: "Please input headroom!"}]}
                                                        rules={[
                                                            {
                                                                type: "number",
                                                                min: 1,
                                                                max: 65535,
                                                                message: "Headroom must between 1 to 65535"
                                                            }
                                                        ]}
                                                    >
                                                        <InputNumber
                                                            placeholder="1-65535, default: 768"
                                                            style={{width: "100%"}}
                                                            controls={false}
                                                        />
                                                    </Form.Item>
                                                </Col>

                                                {/* Threshold */}
                                                <Col flex="140px">
                                                    <Form.Item
                                                        name={[name, "threshold"]}
                                                        label={index === 0 ? "Threshold" : ""}
                                                        labelCol={{span: 24}}
                                                        wrapperCol={{span: 24}}
                                                        rules={[
                                                            {
                                                                type: "number",
                                                                min: 1,
                                                                max: 65535,
                                                                message: "Threshold must between 1 to 65535"
                                                            }
                                                        ]}
                                                    >
                                                        <InputNumber
                                                            placeholder="1-65535"
                                                            style={{width: "100%"}}
                                                            controls={false}
                                                            disabled={!!sharedRatio}
                                                        />
                                                    </Form.Item>
                                                </Col>
                                            </>
                                        );
                                    }}
                                </Form.Item>

                                <Form.Item name={[name, "config_id"]} hidden>
                                    <Input type="hidden" />
                                </Form.Item>
                                <Col>
                                    {index === 0 ? (
                                        <Button
                                            onClick={() => {
                                                if (isEditMode === null) {
                                                    add();
                                                } else {
                                                    const currentSysname = bufferForm.getFieldValue([
                                                        "ingressConfigurations",
                                                        name,
                                                        "sysname"
                                                    ]);
                                                    add({
                                                        sysname: currentSysname
                                                    });
                                                }
                                            }}
                                            style={{
                                                backgroundColor: "transparent",
                                                color: "#BFBFBF",
                                                marginBottom: "24px",
                                                marginTop: index === 0 ? "40px" : "0"
                                            }}
                                            type="link"
                                            icon={<PlusOutlined />}
                                        />
                                    ) : (
                                        <Button
                                            style={{
                                                backgroundColor: "transparent",
                                                color: "#BFBFBF",
                                                marginBottom: "24px",
                                                marginTop: index === 0 ? "40px" : "0"
                                            }}
                                            type="link"
                                            icon={<MinusOutlined />}
                                            onClick={() => remove(name)}
                                        />
                                    )}
                                </Col>
                            </Row>
                        ))}
                    </>
                )}
            </Form.List>

            {/* Egress Buffer Configuration */}
            <div
                style={{
                    fontSize: "18px",
                    fontWeight: "bold",
                    borderBottom: "1px solid rgba(5, 5, 5, 0.06)",
                    paddingBottom: "10px",
                    marginLeft: "-32px",
                    marginRight: "-32px",
                    paddingLeft: "32px",
                    paddingRight: "32px",
                    marginBottom: "20px",
                    marginTop: "40px"
                }}
            >
                Egress Buffer Configuration
            </div>
            <Form.List name="egressConfigurations" initialValue={[{}]}>
                {(fields, {add, remove}) => (
                    <>
                        {fields.map(({key, name}, index) => (
                            <Row key={key}>
                                {/* Sysname */}
                                <Col flex="140px" style={{marginRight: 24}}>
                                    <Form.Item
                                        name={[name, "sysname"]}
                                        label={index === 0 ? "Sysname" : ""}
                                        labelCol={{span: 24}}
                                        wrapperCol={{span: 24}}
                                        rules={[
                                            {
                                                validator: async (_, value) => {
                                                    // 如果其他字段有值，则 sysname 必填
                                                    const currentConfig = bufferForm.getFieldValue([
                                                        "egressConfigurations",
                                                        name
                                                    ]);
                                                    if (
                                                        currentConfig.ports?.length ||
                                                        currentConfig.egress_queues?.length
                                                    ) {
                                                        if (!value) {
                                                            return Promise.reject("Please select sysname!");
                                                        }
                                                    }
                                                    return Promise.resolve();
                                                }
                                            }
                                        ]}
                                    >
                                        <TreeSelect
                                            disabled={isEditMode}
                                            style={{width: "100%"}}
                                            treeData={fabricTreeData}
                                            placeholder="Sysname"
                                            treeDefaultExpandAll
                                            onChange={value => handleModalSysnameChange(value, name, "egress")}
                                            allowClear
                                        />
                                    </Form.Item>
                                </Col>
                                {/* Ports */}
                                <Col flex="140px" style={{marginRight: 24}}>
                                    <Form.Item
                                        name={[name, "ports"]}
                                        label={index === 0 ? "Ports" : ""}
                                        labelCol={{span: 24}}
                                        wrapperCol={{span: 24}}
                                        rules={[
                                            {
                                                validator: async (_, value) => {
                                                    const currentConfig = bufferForm.getFieldValue([
                                                        "egressConfigurations",
                                                        name
                                                    ]);
                                                    if (currentConfig.sysname || currentConfig.egress_queues?.length) {
                                                        if (!value?.length) {
                                                            return Promise.reject("Please select ports!");
                                                        }
                                                    }
                                                    return Promise.resolve();
                                                }
                                            }
                                        ]}
                                    >
                                        <CustomTreeSelect
                                            style={{width: "100%"}}
                                            treeData={egressPortTreeData}
                                            placeholder="Ports"
                                            onFocus={() => {
                                                // 直接获取当前字段的值，参考PFCConfiguration
                                                const currentPortValue = bufferForm.getFieldValue([
                                                    "egressConfigurations",
                                                    name,
                                                    "ports"
                                                ]);
                                                const currentSysname = bufferForm.getFieldValue([
                                                    "egressConfigurations",
                                                    name,
                                                    "sysname"
                                                ]);

                                                if (currentSysname && currentPortValue) {
                                                    try {
                                                        const sysObj = JSON.parse(currentSysname);
                                                        if (sysObj.sn) {
                                                            // 解析端口值
                                                            let portsToEnable = [];
                                                            if (
                                                                Array.isArray(currentPortValue) &&
                                                                currentPortValue.length > 0
                                                            ) {
                                                                try {
                                                                    const cacheKey = `${sysObj.sn}_PfcBufferEgressConfiguration`;
                                                                    const portData = portDataCache.get(cacheKey);
                                                                    const allPortsValue = portData
                                                                        ? JSON.stringify(
                                                                              portData.map(item => item.port_name)
                                                                          )
                                                                        : null;
                                                                    if (currentPortValue[0] === allPortsValue) {
                                                                        portsToEnable = JSON.parse(currentPortValue[0]);
                                                                    } else {
                                                                        portsToEnable = currentPortValue;
                                                                    }
                                                                } catch (e) {
                                                                    portsToEnable = currentPortValue;
                                                                }
                                                            }
                                                            // 调用 calculatePortTreeData 释放端口
                                                            calculatePortTreeData(
                                                                sysObj.sn,
                                                                egressPortTreeData,
                                                                "egress",
                                                                "enable",
                                                                portsToEnable,
                                                                name
                                                            );
                                                        }
                                                    } catch (e) {
                                                        console.error("Error parsing sysname:", e);
                                                    }
                                                }
                                            }}
                                            onBlur={() => {
                                                const currentSysname = bufferForm.getFieldValue([
                                                    "egressConfigurations",
                                                    name,
                                                    "sysname"
                                                ]);
                                                if (currentSysname) {
                                                    try {
                                                        const sysObj = JSON.parse(currentSysname);
                                                        calculatePortTreeData(
                                                            sysObj.sn,
                                                            egressPortTreeData,
                                                            "egress",
                                                            "disable",
                                                            editPortData,
                                                            name
                                                        );
                                                    } catch (e) {
                                                        console.error("Error parsing sysname:", e);
                                                    }
                                                }
                                            }}
                                            onChange={(value, label) => {
                                                console.log("egress value", value);
                                                setEditPortData(value);
                                            }}
                                        />
                                    </Form.Item>
                                </Col>
                                {/* Egress Queues */}
                                <Col flex="140px" style={{marginRight: 24}}>
                                    <Form.Item
                                        name={[name, "egress_queues"]}
                                        label={index === 0 ? "Egress Queues" : ""}
                                        labelCol={{span: 24}}
                                        wrapperCol={{span: 24}}
                                        // rules={[{required: true, message: "Please select egress queues!"}]}
                                    >
                                        <CustomTreeSelect
                                            treeData={[
                                                {
                                                    title: "All Queues",
                                                    value: JSON.stringify(queueList),
                                                    children: queueList.map(queue => ({
                                                        title: queue,
                                                        value: queue,
                                                        key: queue
                                                    }))
                                                }
                                            ]}
                                            placeholder="Egress Queues"
                                            onChange={(value, label) => {
                                                // 简化：只设置实际的队列值，不设置is_all_queues
                                                bufferForm.setFieldValue(
                                                    ["egressConfigurations", name, "egress_queues"],
                                                    value
                                                );
                                            }}
                                        />
                                    </Form.Item>
                                </Col>
                                {/* Shared Ratio */}
                                {/* <Col flex="140px" style={{marginRight: 24}}> */}
                                <Form.Item
                                    shouldUpdate={(prev, curr) =>
                                        prev?.egressConfigurations?.[name]?.shared_ratio !==
                                            curr?.egressConfigurations?.[name]?.shared_ratio ||
                                        prev?.egressConfigurations?.[name]?.threshold !==
                                            curr?.egressConfigurations?.[name]?.threshold
                                    }
                                    noStyle
                                >
                                    {({getFieldValue}) => {
                                        const sharedRatio = getFieldValue([
                                            "egressConfigurations",
                                            name,
                                            "shared_ratio"
                                        ]);
                                        const threshold = getFieldValue(["egressConfigurations", name, "threshold"]);

                                        return (
                                            <>
                                                {/* Shared Ratio */}
                                                <Col flex="140px" style={{marginRight: 24}}>
                                                    <Form.Item
                                                        name={[name, "shared_ratio"]}
                                                        label={index === 0 ? "Shared Ratio(%)" : ""}
                                                        labelCol={{span: 24}}
                                                        wrapperCol={{span: 24}}
                                                        rules={[
                                                            {
                                                                type: "number",
                                                                min: 1,
                                                                max: 100,
                                                                message: "Shared Ratio must between 1 to 100"
                                                            }
                                                        ]}
                                                    >
                                                        <InputNumber
                                                            placeholder="1-100, default: 5"
                                                            style={{width: "100%"}}
                                                            controls={false}
                                                            disabled={!!threshold}
                                                        />
                                                    </Form.Item>
                                                </Col>

                                                {/* Threshold */}
                                                <Col flex="140px">
                                                    <Form.Item
                                                        name={[name, "threshold"]}
                                                        label={index === 0 ? "Threshold" : ""}
                                                        labelCol={{span: 24}}
                                                        wrapperCol={{span: 24}}
                                                        rules={[
                                                            {
                                                                type: "number",
                                                                min: 1,
                                                                max: 65535,
                                                                message: "Threshold must between 1 to 65535"
                                                            }
                                                        ]}
                                                    >
                                                        <InputNumber
                                                            placeholder="1-65535"
                                                            style={{width: "100%"}}
                                                            controls={false}
                                                            disabled={!!sharedRatio}
                                                        />
                                                    </Form.Item>
                                                </Col>
                                            </>
                                        );
                                    }}
                                </Form.Item>
                                {/* </Col> */}
                                <Form.Item name={[name, "config_id"]} hidden>
                                    <Input type="hidden" />
                                </Form.Item>
                                <Col>
                                    {index === 0 ? (
                                        <Button
                                            onClick={() => {
                                                if (isEditMode === null) {
                                                    add();
                                                } else {
                                                    const currentSysname = bufferForm.getFieldValue([
                                                        "egressConfigurations",
                                                        name,
                                                        "sysname"
                                                    ]);
                                                    add({
                                                        sysname: currentSysname
                                                    });
                                                }
                                            }}
                                            style={{
                                                backgroundColor: "transparent",
                                                color: "#BFBFBF",
                                                marginBottom: "24px",
                                                marginTop: index === 0 ? "40px" : "0"
                                            }}
                                            type="link"
                                            icon={<PlusOutlined />}
                                        />
                                    ) : (
                                        <Button
                                            style={{
                                                backgroundColor: "transparent",
                                                color: "#BFBFBF",
                                                marginBottom: "24px",
                                                marginTop: index === 0 ? "40px" : "0"
                                            }}
                                            type="link"
                                            icon={<MinusOutlined />}
                                            onClick={() => remove(name)}
                                        />
                                    )}
                                </Col>
                            </Row>
                        ))}
                    </>
                )}
            </Form.List>
        </Form.Item>
    );

    // 修改模态框的 sysname 变化处理函数，参考PFCConfiguration
    const handleModalSysnameChange = async (value, rowIndex, configType) => {
        // 根据配置类型设置表单字段值
        const formField = configType === "ingress" ? "ingressConfigurations" : "egressConfigurations";

        // 清空端口选择
        bufferForm.setFields([
            {
                name: [formField, rowIndex, "ports"],
                value: []
            }
        ]);

        if (value !== null) {
            let obj = {};
            try {
                obj = JSON.parse(value);
            } catch (e) {
                console.error("Failed to parse sysname:", e);
                return;
            }

            if (obj.sn) {
                try {
                    // 使用过滤后的端口数据，根据配置类型使用不同的query_model
                    await fetchFilteredPortsBySn(obj.sn, configType, true);
                } catch (error) {
                    message.error("Failed to get ports");
                }
            }
        } else {
            // 如果清空sysname，也清空相应的端口树数据
            if (configType === "ingress") {
                setIngressPortTreeData([]);
            } else {
                setEgressPortTreeData([]);
            }
        }
    };

    const handleTrafficTypeChange = value => {
        setTrafficType(value);
        console.log(value);
        console.log(trafficType);
    };

    return (
        <div style={{marginBottom: 30}}>
            <h3>
                PFC Buffer Configuration
                <CustomQuestionIcon
                    placement="right"
                    title="Configure PFC buffer to implement trafficcontrol and PFC watchdog, which is basedon interface and priority queue. Thestorage space of each interface is dividedinto different buffers independently and acertain action will be executed after thenumber of accumulated packets reachesthe buffer threshold (the unit is cell)."
                />
            </h3>
            <div style={{marginBottom: 16, display: "flex", justifyContent: "space-between", alignItems: "center"}}>
                <Button type="primary" icon={<PlusOutlined />} onClick={handleBufferCreate}>
                    Configuration
                </Button>
                <Space>
                    <Form.Item label="Traffic" style={{marginBottom: 0}}>
                        <Select style={{width: 200}} value={trafficType} onChange={handleTrafficTypeChange}>
                            <Select.Option value="Ingress Queue">Ingress Queue</Select.Option>
                            <Select.Option value="Egress Queue">Egress Queue</Select.Option>
                        </Select>
                    </Form.Item>
                    <Form.Item label="Sysname" style={{marginBottom: 0}}>
                        <TreeSelect
                            style={{width: 200}}
                            dropdownStyle={{maxHeight: 400, overflow: "auto"}}
                            onChange={handleHeaderSysnameChange}
                            value={selectHeaderSysname}
                            placeholder="Please select"
                            allowClear
                            treeData={fabricTreeData}
                            treeDefaultExpandAll
                        />
                    </Form.Item>
                    <Form.Item label="Port" style={{marginBottom: 0}}>
                        <CustomTreeSelect
                            style={{width: 200}}
                            value={selectHeaderPort}
                            onChange={handleHeaderPortChange}
                            placeholder="Please select"
                            treeData={headerPortTreeData}
                        />
                    </Form.Item>
                </Space>
            </div>
            {trafficType === "Ingress Queue" ? (
                <ExtendedTable
                    columns={bufferColumnsIngress}
                    fetchAPIInfo={getBufferIngressConfigListWrapper}
                    // loading={loading}
                    isShowPagination
                    ref={ingressTableRef}
                    bordered={false}
                />
            ) : (
                <ExtendedTable
                    columns={bufferColumnsEgress}
                    fetchAPIInfo={getBufferEgressConfigListWrapper}
                    // loading={loading}
                    isShowPagination
                    ref={egressTableRef}
                    bordered={false}
                />
            )}

            <AmpConCustomModalForm
                title={isEditMode ? "Edit PFC Buffer Configuration" : "Create PFC Buffer Configuration"}
                isModalOpen={isBufferModalVisible}
                formInstance={bufferForm}
                layoutProps={{
                    labelCol: {
                        span: 4
                    }
                }}
                CustomFormItems={bufferFormItems}
                onCancel={handleBufferModalCancel}
                onSubmit={handleBufferSubmit}
                modalClass="ampcon-max-modal"
                footer={[
                    <Divider style={{marginTop: 0, marginBottom: 20}} />,
                    <Button key="cancel" onClick={handleBufferModalCancel}>
                        Cancel
                    </Button>,
                    <Button key="ok" type="primary" onClick={bufferForm.submit}>
                        Apply
                    </Button>
                ]}
            />
        </div>
    );
};

export default PFCBufferConfiguration;
