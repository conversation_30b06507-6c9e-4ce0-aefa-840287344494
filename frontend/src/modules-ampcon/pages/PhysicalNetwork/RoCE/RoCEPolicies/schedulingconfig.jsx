import {useState, useRef, useEffect, useMemo} from "react";
import {Button, Tooltip, Form, Space, Row, Col, Select, Input, TreeSelect, Divider, message} from "antd";
import {QuestionCircleOutlined, PlusOutlined, MinusOutlined, DownOutlined} from "@ant-design/icons";
import {
    AmpConCustomTable,
    createColumnConfig,
    TableFilterDropdown,
    AmpConCustomModalForm
} from "@/modules-ampcon/components/custom_table";
import {
    getSchedulingConfigList,
    getFabricSwitches,
    getSwitchPorts,
    getFilterSwitchPorts,
    saveSchedulingConfig,
    updateSchedulingConfig,
    deleteSchedulingConfig,
    getSchedulerList,
    getQosConfigDetailBySwitch
} from "@/modules-ampcon/apis/roce_api";
import {confirmModalAction} from "@/modules-ampcon/components/custom_modal";
import CustomTreeSelect from "./customTreeSelect";
import {ExtendedTable} from "./ext_table";
import CustomQuestionIcon from "./QuestionIcon";
import style from "./roce_policies.module.scss";

const {Option} = Select;

const SchedulingConfig = () => {
    // 状态管理 (State Management)
    const [isModalVisible, setIsModalVisible] = useState(false);
    const [form] = Form.useForm();

    // 优化数据源管理 - 参考 PFCConfiguration
    const [fabricTreeData, setFabricTreeData] = useState([]);
    const [ingressPortTreeData, setIngressPortTreeData] = useState([]); // 分离ingress端口树数据
    const [egressPortTreeData, setEgressPortTreeData] = useState([]); // 分离egress端口树数据
    const [portDataCache, setPortDataCache] = useState(new Map()); // 添加端口数据缓存

    // 在 SchedulingConfig 组件中添加状态变量
    const tableRef = useRef(null);

    // 参考 PFCConfiguration 添加编辑相关状态
    const [editRecord, setEditRecord] = useState(null);
    const [saveLoading, setSaveLoading] = useState(false);
    const [editPortData, setEditPortData] = useState([]);

    // 修改状态定义，支持未选择sysname的情况
    const [schedulerOptionsMap, setSchedulerOptionsMap] = useState({}); // 格式: {switch_sn: [scheduler1, scheduler2, ...], 'temp_row_0': [...], 'temp_row_1': [...]}
    const [searchValueMap, setSearchValueMap] = useState({}); // 存储每行的临时输入值
    const [schedulerCount, setSchedulerCount] = useState(1);
    const [searchValue, setSearchValue] = useState("");

    // 添加状态来跟踪每行的scheduler值
    const [schedulerValuesMap, setSchedulerValuesMap] = useState({}); // 格式: {row_0: 'scheduler_name', row_1: 'scheduler_name'}

    const ingressInitValue = {
        sysname: null,
        classifier: "",
        trust_mode: null,
        ports: [],
        forwarding_class: null,
        queue: []
    };

    const egressInitValue = {
        sysname: null,
        scheduler_profile: "",
        scheduler: null,
        ports: [],
        forwarding_class: null,
        local_priority: ""
    };

    useEffect(() => {
        fetchFabricTreeData();
    }, []);

    useEffect(() => {
        console.log("schedulerOptionsMap updated:", schedulerOptionsMap);
    }, [schedulerOptionsMap]);

    const fetchFabricTreeData = async () => {
        try {
            const res = await getFabricSwitches();
            if (res && res.data) {
                const fabricKeys = Object.keys(res.data); // ["default"]
                const fabricValues = Object.values(res.data); // [switch array]

                const tree = fabricKeys.map((fabricName, index) => {
                    const switches = fabricValues[index];

                    // Filter out switches with enabled=false
                    const enabledSwitches = switches.filter(sw => sw.enabled === true);

                    return {
                        title: fabricName,
                        value: fabricName,
                        key: fabricName,
                        disabled: true,
                        children: enabledSwitches.map(sw => ({
                            title: sw.sysname || "Unknown",
                            value: JSON.stringify({switch_sn: sw.switch_sn, sysname: sw.sysname}),
                            key: JSON.stringify({switch_sn: sw.switch_sn, sysname: sw.sysname})
                        }))
                    };
                });

                setFabricTreeData(tree);
            }
        } catch (e) {
            message.error("Failed to get fabric switches");
        }
    };

    // 修改为分离的端口数据初始化函数
    const initIngressPortTreeData = portData => {
        const allOccupiedPorts = new Set();
        console.log("ingress portData", portData);
        portData.forEach(item => {
            if (!item.enabled) {
                allOccupiedPorts.add(item.port_name);
            }
        });
        console.log("ingress allOccupiedPorts", allOccupiedPorts);

        const allPortsForDisplay = portData.map(item => ({
            title: item.port_name,
            value: item.port_name,
            key: item.port_name,
            disabled: allOccupiedPorts.has(item.port_name)
        }));

        // 检查是否有任意端口被占用
        const hasAnyPortOccupied = portData.some(item => allOccupiedPorts.has(item.port_name));

        const tree = [
            {
                title: "All Ports",
                value: JSON.stringify(portData.map(item => item.port_name)),
                disabled: hasAnyPortOccupied,
                children: allPortsForDisplay
            }
        ];

        setIngressPortTreeData(tree);
    };

    const initEgressPortTreeData = portData => {
        const allOccupiedPorts = new Set();
        console.log("egress portData", portData);
        portData.forEach(item => {
            if (!item.enabled) {
                allOccupiedPorts.add(item.port_name);
            }
        });
        console.log("egress allOccupiedPorts", allOccupiedPorts);

        const allPortsForDisplay = portData.map(item => ({
            title: item.port_name,
            value: item.port_name,
            key: item.port_name,
            disabled: allOccupiedPorts.has(item.port_name)
        }));

        // 检查是否有任意端口被占用
        const hasAnyPortOccupied = portData.some(item => allOccupiedPorts.has(item.port_name));

        const tree = [
            {
                title: "All Ports",
                value: JSON.stringify(portData.map(item => item.port_name)),
                disabled: hasAnyPortOccupied,
                children: allPortsForDisplay
            }
        ];

        setEgressPortTreeData(tree);
    };

    // 分离ingress和egress的端口状态计算函数
    const calculateIngressPortTreeData = (sn, portTreeData, action = null, currentPorts = [], index = null) => {
        const allOccupiedPorts = new Set();
        const currentFormValues = form.getFieldsValue();

        const portData = portTreeData[0].children;
        console.log("ingress portTreeData", portTreeData);
        console.log("ingress portData", portData);

        portData.forEach(item => {
            if (item.disabled) {
                allOccupiedPorts.add(item.value);
            }
        });

        // 只收集ingress配置项占用的端口
        if (currentFormValues.ingressConfigurations) {
            currentFormValues.ingressConfigurations.forEach((config, configIndex) => {
                if (config.sysname) {
                    try {
                        const sysObj = JSON.parse(config.sysname);
                        if (sysObj.switch_sn === sn && config.ports) {
                            let selectedPorts = [];
                            if (config.ports && Array.isArray(config.ports) && config.ports.length > 0) {
                                try {
                                    const allPortsValue = JSON.stringify(portData.map(item => item.value));
                                    if (config.ports[0] === allPortsValue) {
                                        selectedPorts = JSON.parse(config.ports[0]);
                                    } else {
                                        selectedPorts = config.ports;
                                    }
                                } catch (e) {
                                    selectedPorts = config.ports;
                                }
                            }
                            selectedPorts.forEach(port => allOccupiedPorts.add(port));
                        }
                    } catch (e) {
                        console.error("Error parsing sysname or ports:", e);
                    }
                }
            });
        }

        // 根据action处理指定的端口
        if (action === "enable") {
            // 释放指定的端口
            currentPorts.forEach(port => allOccupiedPorts.delete(port));
        } else if (action === "disable") {
            currentPorts.forEach(port => allOccupiedPorts.add(port));
        }

        console.log("ingress allOccupiedPorts", allOccupiedPorts);

        // 计算最终的端口可用性
        const allPortsForDisplay = portData.map(item => ({
            title: item.value,
            value: item.value,
            key: item.value,
            disabled: allOccupiedPorts.has(item.value)
        }));

        console.log("ingress allPortsForDisplay", allPortsForDisplay);

        // 检查是否有任意端口被占用
        const hasAnyPortOccupied = portData.some(item => allOccupiedPorts.has(item.value));

        const tree = [
            {
                title: "All Ports",
                value: JSON.stringify(portData.map(item => item.value)),
                disabled: hasAnyPortOccupied,
                children: allPortsForDisplay
            }
        ];

        setIngressPortTreeData(tree);
    };

    const calculateEgressPortTreeData = (sn, portTreeData, action = null, currentPorts = [], index = null) => {
        const allOccupiedPorts = new Set();
        const currentFormValues = form.getFieldsValue();

        const portData = portTreeData[0].children;
        console.log("egress portTreeData", portTreeData);
        console.log("egress portData", portData);

        portData.forEach(item => {
            if (item.disabled) {
                allOccupiedPorts.add(item.value);
            }
        });

        // 只收集egress配置项占用的端口
        if (currentFormValues.egressConfigurations) {
            currentFormValues.egressConfigurations.forEach((config, configIndex) => {
                if (config.sysname) {
                    try {
                        const sysObj = JSON.parse(config.sysname);
                        if (sysObj.switch_sn === sn && config.ports) {
                            let selectedPorts = [];
                            if (config.ports && Array.isArray(config.ports) && config.ports.length > 0) {
                                try {
                                    const allPortsValue = JSON.stringify(portData.map(item => item.value));
                                    if (config.ports[0] === allPortsValue) {
                                        selectedPorts = JSON.parse(config.ports[0]);
                                    } else {
                                        selectedPorts = config.ports;
                                    }
                                } catch (e) {
                                    selectedPorts = config.ports;
                                }
                            }
                            selectedPorts.forEach(port => allOccupiedPorts.add(port));
                        }
                    } catch (e) {
                        console.error("Error parsing sysname or ports:", e);
                    }
                }
            });
        }

        // 根据action处理指定的端口
        if (action === "enable") {
            // 释放指定的端口
            currentPorts.forEach(port => allOccupiedPorts.delete(port));
        } else if (action === "disable") {
            currentPorts.forEach(port => allOccupiedPorts.add(port));
        }

        console.log("egress allOccupiedPorts", allOccupiedPorts);

        // 计算最终的端口可用性
        const allPortsForDisplay = portData.map(item => ({
            title: item.value,
            value: item.value,
            key: item.value,
            disabled: allOccupiedPorts.has(item.value)
        }));

        console.log("egress allPortsForDisplay", allPortsForDisplay);

        // 检查是否有任意端口被占用
        const hasAnyPortOccupied = portData.some(item => allOccupiedPorts.has(item.value));

        const tree = [
            {
                title: "All Ports",
                value: JSON.stringify(portData.map(item => item.value)),
                disabled: hasAnyPortOccupied,
                children: allPortsForDisplay
            }
        ];

        setEgressPortTreeData(tree);
    };

    // 修改fetchPortsBySn函数，支持分离的端口数据初始化
    const fetchPortsBySn = async (sn, query_model, forceRefresh = false) => {
        try {
            const cacheKey = `${sn}_${query_model}`;
            const res = await getFilterSwitchPorts({
                switch_sn: sn,
                query_model
            });

            if (res && res.status === 200 && Array.isArray(res.data)) {
                const portData = res.data;
                setPortDataCache(prev => new Map(prev).set(cacheKey, portData));

                // 根据query_model类型初始化对应的端口树数据
                if (query_model === "QosIngressConfiguration") {
                    initIngressPortTreeData(portData);
                } else if (query_model === "QosEgressConfiguration") {
                    initEgressPortTreeData(portData);
                }
            } else {
                message.error(res.msg);
            }
        } catch (error) {
            console.error("Failed to fetch filtered ports:", error);
            message.error("Failed to fetch ports");

            // 根据query_model类型清空对应的端口树数据
            if (query_model === "QosIngressConfiguration") {
                setIngressPortTreeData([]);
            } else if (query_model === "QosEgressConfiguration") {
                setEgressPortTreeData([]);
            }
        }
    };

    // 修改编辑处理函数 - 根据switch_sn加载数据
    const handleEdit = async record => {
        try {
            setIsModalVisible(true);
            setEditRecord(record);

            // 预加载端口数据
            if (record.switch_sn) {
                fetchPortsBySn(record.switch_sn, "QosIngressConfiguration", true);
                fetchPortsBySn(record.switch_sn, "QosEgressConfiguration", true);
            }

            // 根据 switch_sn 查询该交换机下所有的 QoS 配置记录
            const detailResponse = await getQosConfigDetailBySwitch({
                switch_sn: record.switch_sn
            });

            if (detailResponse && detailResponse.status === 200) {
                const configs = detailResponse.data || [];
                const sysnameValue = JSON.stringify({switch_sn: record.switch_sn, sysname: record.sysname});

                // 处理主配置数据
                const configurations = configs.map(config => ({
                    sysname: sysnameValue,
                    config_id: config.id,
                    forwarding_class: config.forwarding_class,
                    local_priority: config.local_priority,
                    scheduler: config.scheduler,
                    qos_mode: config.mode,
                    weight: config.weight,
                    guaranteed_rate: config.guaranteed_rate
                }));

                // 处理入站配置数据
                const ingressConfigurations = [];
                configs.forEach(config => {
                    if (config.ingress_configurations && config.ingress_configurations.length > 0) {
                        config.ingress_configurations.forEach(ingressConfig => {
                            // 处理端口数据
                            let ports = [];
                            if (ingressConfig.is_all_ports && ingressConfig.port) {
                                ports = Array.isArray(ingressConfig.port)
                                    ? [JSON.stringify(ingressConfig.port)]
                                    : [ingressConfig.port];
                            } else {
                                ports = Array.isArray(ingressConfig.port)
                                    ? ingressConfig.port
                                    : JSON.parse(ingressConfig.port);
                            }

                            // 处理队列数据
                            let queues = [];
                            if (ingressConfig.is_all_queues && ingressConfig.queue) {
                                queues = Array.isArray(ingressConfig.queue)
                                    ? [JSON.stringify(ingressConfig.queue)]
                                    : [ingressConfig.queue];
                            } else {
                                queues = Array.isArray(ingressConfig.queue)
                                    ? ingressConfig.queue
                                    : JSON.parse(ingressConfig.queue);
                            }

                            ingressConfigurations.push({
                                sysname: sysnameValue,
                                config_id: config.id,
                                ingress_id: ingressConfig.id,
                                classifier: ingressConfig.classifier,
                                trust_mode: ingressConfig.trust_mode,
                                ports,
                                forwarding_class: ingressConfig.forwarding_class,
                                queues,
                                is_all_ports: ingressConfig.is_all_ports,
                                is_all_queues: ingressConfig.is_all_queues
                            });
                        });
                    }
                });

                // 处理出站配置数据
                const egressConfigurations = [];
                configs.forEach(config => {
                    if (config.egress_configurations && config.egress_configurations.length > 0) {
                        config.egress_configurations.forEach(egressConfig => {
                            // 处理端口数据
                            let ports = [];
                            if (egressConfig.is_all_ports && egressConfig.port) {
                                ports = Array.isArray(egressConfig.port)
                                    ? [JSON.stringify(egressConfig.port)]
                                    : [egressConfig.port];
                            } else {
                                ports = Array.isArray(egressConfig.port)
                                    ? egressConfig.port
                                    : JSON.parse(egressConfig.port);
                            }

                            egressConfigurations.push({
                                sysname: sysnameValue,
                                config_id: config.id,
                                egress_id: egressConfig.id,
                                scheduler_profile: egressConfig.scheduler_profile,
                                scheduler: egressConfig.scheduler,
                                ports,
                                forwarding_class: egressConfig.forwarding_class,
                                local_priority: egressConfig.local_priority,
                                is_all_ports: egressConfig.is_all_ports
                            });
                        });
                    }
                });

                // 如果没有配置数据，提供默认值
                if (configurations.length === 0) {
                    configurations.push({
                        sysname: sysnameValue,
                        config_id: null,
                        forwarding_class: "",
                        local_priority: "",
                        scheduler: "",
                        qos_mode: "SP",
                        weight: "",
                        guaranteed_rate: ""
                    });
                }

                if (ingressConfigurations.length === 0) {
                    ingressConfigurations.push({...ingressInitValue, sysname: sysnameValue});
                }

                if (egressConfigurations.length === 0) {
                    egressConfigurations.push({...egressInitValue, sysname: sysnameValue});
                }

                // 预加载scheduler选项
                if (record.switch_sn) {
                    await fetchSchedulerList(record.switch_sn, false);
                }

                // 设置表单数据
                form.setFieldsValue({
                    configurations,
                    ingressConfigurations,
                    egressConfigurations
                });

                console.log("QoS Detail loaded:", {
                    configs,
                    configurations,
                    ingressConfigurations,
                    egressConfigurations
                });
            } else {
                message.error(detailResponse.msg);
            }
        } catch (error) {
            console.error("failed to load scheduling config detail:", error);
            message.error("failed to load scheduling config detail");
        }
    };

    // 参考 PFCConfiguration 的 handleCreate 实现
    const handleCreate = () => {
        setIsModalVisible(true);
        setEditRecord(null);
        form.resetFields();
        setIngressPortTreeData([]); // 清空ingress端口数据
        setEgressPortTreeData([]); // 清空egress端口数据

        // 设置默认值
        form.setFieldsValue({
            configurations: [
                {
                    sysname: null,
                    forwarding_class: "",
                    local_priority: "",
                    scheduler: null,
                    qos_mode: null,
                    weight: null,
                    guaranteed_rate: null
                }
            ],
            ingressConfigurations: [ingressInitValue],
            egressConfigurations: [egressInitValue]
        });
    };

    // 修改 handleModalCancel 函数
    const handleModalCancel = () => {
        setIsModalVisible(false);
        setEditRecord(null);
        form.resetFields();
        setIngressPortTreeData([]); // 清空ingress端口数据
        setEgressPortTreeData([]); // 清空egress端口数据
        setPortDataCache(new Map()); // 清空端口缓存
        setEditPortData([]); // 清空编辑端口数据
        // 重置 scheduler 相关状态
        setSchedulerOptionsMap({});
        setSearchValueMap({});
        setSchedulerValuesMap({}); // 清空scheduler值记录
        setSearchValue("");
    };

    // 修改 handleSubmit 函数，参考 PFCConfiguration
    const handleSubmit = async values => {
        console.log("Form values before processing:", values); // 调试用，可以删除
        setSaveLoading(true);
        try {
            const configurationsArray = Array.isArray(values.configurations) ? values.configurations : [];

            const configs = configurationsArray.map(config => {
                let sysObj = {};
                try {
                    sysObj = JSON.parse(config.sysname);
                } catch (e) {
                    console.error("Failed to parse sysname:", e);
                    return;
                }

                // Configuration 部分不需要端口数据处理

                return {
                    // 如果是编辑，带上 config_id
                    ...(config.config_id ? {config_id: config.config_id} : {}),
                    sysname: sysObj.sysname || config.sysname || "",
                    switch_sn: sysObj.switch_sn || "",
                    forwarding_class: config.forwarding_class,
                    local_priority: config.local_priority,
                    scheduler: config.scheduler,
                    mode: config.qos_mode,
                    weight: config.weight,
                    guaranteed_rate: config.guaranteed_rate
                };
            });

            const params = {
                configurations: configs.filter(Boolean),
                ingress_configurations: (values.ingressConfigurations || []).map(item => {
                    let sysObj = {};
                    try {
                        sysObj = JSON.parse(item.sysname);
                    } catch {}

                    // 参考PFCWDConfiguration处理ingress配置
                    const portData = portDataCache.get(`${sysObj.switch_sn}_QosIngressConfiguration`);
                    const allPortsValue = portData ? JSON.stringify(portData.map(port => port.port_name)) : null;
                    const queueLists = ["0", "1", "2", "3", "4", "5", "6", "7"];
                    const allQueuesValue = JSON.stringify(queueLists);

                    // 根据实际值判断是否为全选
                    const is_all_ports = item.ports && item.ports.length === 1 && item.ports[0] === allPortsValue;
                    const is_all_queues = item.queues && item.queues.length === 1 && item.queues[0] === allQueuesValue;

                    let ports = [];
                    let queues = [];

                    if (is_all_ports) {
                        ports = JSON.parse(item.ports[0]);
                    } else {
                        ports = item.ports || [];
                    }

                    if (is_all_queues) {
                        queues = JSON.parse(item.queues[0]);
                    } else {
                        queues = item.queues || [];
                    }

                    return {
                        ...(item.config_id ? {config_id: item.config_id} : {}),
                        ...(item.ingress_id ? {ingress_id: item.ingress_id} : {}),
                        sysname: sysObj.sysname || item.sysname || "",
                        switch_sn: sysObj.switch_sn || "",
                        classifier: item.classifier,
                        trust_mode: item.trust_mode,
                        port: ports,
                        forwarding_class: item.forwarding_class,
                        queue: queues,
                        is_all_ports,
                        is_all_queues
                    };
                }),
                egress_configurations: (values.egressConfigurations || []).map(item => {
                    let sysObj = {};
                    try {
                        sysObj = JSON.parse(item.sysname);
                    } catch {}

                    // 参考PFCWDConfiguration处理egress配置
                    const portData = portDataCache.get(`${sysObj.switch_sn}_QosEgressConfiguration`);
                    const allPortsValue = portData ? JSON.stringify(portData.map(port => port.port_name)) : null;

                    // 根据实际值判断是否为全选
                    const is_all_ports = item.ports && item.ports.length === 1 && item.ports[0] === allPortsValue;

                    let ports = [];

                    if (is_all_ports) {
                        ports = JSON.parse(item.ports[0]);
                    } else {
                        ports = item.ports || [];
                    }

                    return {
                        ...(item.config_id ? {config_id: item.config_id} : {}),
                        ...(item.egress_id ? {egress_id: item.egress_id} : {}),
                        sysname: sysObj.sysname || item.sysname || "",
                        switch_sn: sysObj.switch_sn || "",
                        scheduler_profile: item.scheduler_profile,
                        scheduler: item.scheduler,
                        port: ports,
                        forwarding_class: item.forwarding_class,
                        local_priority: item.local_priority,
                        is_all_ports,
                        is_all_queues: false // egress配置没有queue字段
                    };
                })
            };
            console.log("Submitting params:", params); // 调试用，可以删除

            let ret;
            if (editRecord) {
                // 编辑模式：调用 update API，需要正确组织数据结构
                const updateParams = {
                    switch_sn: editRecord.switch_sn,
                    configurations: params.configurations.map(config => {
                        // 为该主配置收集相关的 ingress 和 egress 配置
                        const relatedIngress = params.ingress_configurations.filter(
                            ingress => ingress.switch_sn === config.switch_sn
                        );
                        const relatedEgress = params.egress_configurations.filter(
                            egress => egress.switch_sn === config.switch_sn
                        );

                        return {
                            ...config,
                            // 确保包含 ingress_id 和 egress_id 用于后端识别
                            ingress_configurations: relatedIngress.map(ingress => ({
                                ...ingress,
                                // 如果有 ingress_id 则保留（编辑现有），没有则创建新的
                                ...(ingress.ingress_id ? {ingress_id: ingress.ingress_id} : {})
                            })),
                            egress_configurations: relatedEgress.map(egress => ({
                                ...egress,
                                // 如果有 egress_id 则保留（编辑现有），没有则创建新的
                                ...(egress.egress_id ? {egress_id: egress.egress_id} : {})
                            }))
                        };
                    })
                };
                console.log("Update params:", updateParams); // 调试用，可以删除
                ret = await updateSchedulingConfig(updateParams);
            } else {
                // 创建模式：调用 save API
                ret = await saveSchedulingConfig(params);
            }

            if (ret.status === 200) {
                form.resetFields();
                setIsModalVisible(false);
                setEditRecord(null);
                message.success(ret.msg);
                tableRef.current?.refreshTable();
            } else {
                message.error(ret.msg);
            }
        } catch (e) {
            console.error("Save failed:", e);
            message.error("Save failed");
        } finally {
            setSaveLoading(false);
        }
    };

    // 删除处理函数
    const handleDeleteConfirm = async record => {
        try {
            const ret = await deleteSchedulingConfig({config_id: record.id});
            if (ret.status === 200) {
                message.success(ret.msg);
            } else {
                message.error(ret.msg);
            }
            tableRef.current.refreshTable();
        } catch (e) {
            console.error(e);
            message.error("Deleted failed");
        }
    };

    // 修改获取 scheduler 列表的函数，支持合并现有选项
    const fetchSchedulerList = async (switch_sn, preserveExisting = true) => {
        try {
            const response = await getSchedulerList({switch_sn});
            if (response.status === 200 && Array.isArray(response.data)) {
                setSchedulerOptionsMap(prev => {
                    const existingOptions = preserveExisting ? prev[switch_sn] || [] : [];
                    const newOptions = response.data || [];

                    // 合并现有选项和新获取的选项
                    const mergedOptions = [...new Set([...existingOptions, ...newOptions])];

                    const newMap = {
                        ...prev,
                        [switch_sn]: mergedOptions
                    };
                    console.log("schedulerOptionsMap updated:", newMap);
                    return newMap;
                });
            }
        } catch (error) {
            console.error("Failed to fetch scheduler list:", error);
            message.error("获取 scheduler 列表失败");
        }
    };

    // 添加获取scheduler选项的辅助函数
    const getSchedulerOptions = (sysname, rowIndex) => {
        let switch_sn = "";

        if (sysname) {
            try {
                const obj = JSON.parse(sysname);
                switch_sn = obj.switch_sn;
            } catch (e) {
                console.error("Failed to parse sysname for scheduler options:", e);
            }
        }

        // 如果有switch_sn，使用switch_sn作为key；否则使用临时行key
        const key = switch_sn || `temp_row_${rowIndex}`;
        return schedulerOptionsMap[key] || [];
    };

    // 修改setSchedulerOptions函数，同时记录值
    const setSchedulerOptions = (sysname, rowIndex, newScheduler) => {
        let switch_sn = "";

        if (sysname) {
            try {
                const obj = JSON.parse(sysname);
                switch_sn = obj.switch_sn;
            } catch (e) {
                console.error("Failed to parse sysname for setting scheduler options:", e);
            }
        }

        // 如果有switch_sn，使用switch_sn作为key；否则使用临时行key
        const key = switch_sn || `temp_row_${rowIndex}`;

        setSchedulerOptionsMap(prev => {
            const currentOptions = prev[key] || [];
            if (!currentOptions.includes(newScheduler)) {
                return {
                    ...prev,
                    [key]: [...currentOptions, newScheduler]
                };
            }
            return prev;
        });

        // 同时记录这个值到schedulerValuesMap
        setSchedulerValuesMap(prev => ({
            ...prev,
            [`row_${rowIndex}`]: newScheduler
        }));
    };

    // 优化表单项函数中的端口配置
    const formItems = () => {
        const [selectedSysnames, setSelectedSysnames] = useState([]);

        // 监听 configuration 区块
        const configurations = Form.useWatch(["configurations"], form) || [];
        const snFcPriorityMap = useMemo(() => {
            const map = {};

            configurations.forEach(cfg => {
                try {
                    const sys = JSON.parse(cfg.sysname || "{}");
                    const sn = sys.switch_sn;
                    const fc = cfg.forwarding_class;
                    const lp = cfg.local_priority;

                    if (sn && fc !== undefined && lp !== undefined && lp !== "") {
                        map[`${sn}-${fc}`] = lp;
                    }
                } catch (e) {
                    // 忽略非法 sysname
                }
            });

            return map;
        }, [JSON.stringify(configurations)]);

        const forwardingClassOptions = configurations.map(item => item?.forwarding_class).filter(Boolean);
        const queueOptions = Array.from({length: 8}, (_, i) => i);

        return (
            <>
                {/* Configuration Section */}
                <div
                    style={{
                        fontSize: "18px",
                        fontWeight: "bold",
                        borderBottom: "1px solid rgba(5, 5, 5, 0.06)",
                        paddingBottom: "10px",
                        marginLeft: "-32px",
                        marginRight: "-32px",
                        paddingLeft: "32px",
                        paddingRight: "32px",
                        marginBottom: "20px"
                    }}
                >
                    Configuration
                </div>
                <Form.List name="configurations" initialValue={[{}]}>
                    {(fields, {add, remove}) => (
                        <>
                            {fields.map(({key, name}, index) => (
                                <Row key={key} gutter={9}>
                                    <Col span="3">
                                        <Form.Item
                                            name={[name, "sysname"]}
                                            label={
                                                index === 0 ? (
                                                    <>
                                                        Sysname <span className={style.requiredIcon1}>*</span>
                                                    </>
                                                ) : (
                                                    ""
                                                )
                                            }
                                            labelCol={{span: 24}}
                                            wrapperCol={{span: 24}}
                                            rules={[{required: true, message: "Please select sysname!"}]}
                                        >
                                            <TreeSelect
                                                disabled={editRecord === null ? false : true}
                                                treeData={fabricTreeData}
                                                placeholder="Sysname"
                                                dropdownStyle={{
                                                    maxHeight: 200,
                                                    overflow: "auto"
                                                }}
                                                treeDefaultExpandAll
                                                maxTagCount={1}
                                                maxTagTextLength={10}
                                                maxTagPlaceholder={omittedValues => `+${omittedValues.length} more`}
                                                onChange={async value => {
                                                    if (value !== null) {
                                                        try {
                                                            const obj = JSON.parse(value);
                                                            if (obj.switch_sn) {
                                                                // 获取当前行的临时scheduler选项和记录的scheduler值
                                                                const tempKey = `temp_row_${name}`;
                                                                const tempSchedulers =
                                                                    schedulerOptionsMap[tempKey] || [];
                                                                const savedSchedulerValue =
                                                                    schedulerValuesMap[`row_${name}`];

                                                                // 先获取API数据（如果需要的话）
                                                                if (!schedulerOptionsMap[obj.switch_sn]) {
                                                                    await fetchSchedulerList(obj.switch_sn, false);
                                                                }

                                                                // 然后合并所有选项
                                                                setTimeout(() => {
                                                                    setSchedulerOptionsMap(prev => {
                                                                        const existingOptions =
                                                                            prev[obj.switch_sn] || [];

                                                                        // 合并所有选项：现有选项 + 临时选项 + 保存的值
                                                                        const allOptions = [
                                                                            ...existingOptions,
                                                                            ...tempSchedulers,
                                                                            ...(savedSchedulerValue
                                                                                ? [savedSchedulerValue]
                                                                                : [])
                                                                        ];
                                                                        const mergedOptions = [...new Set(allOptions)];

                                                                        // 移除临时key，添加到正式key
                                                                        const newMap = {...prev};
                                                                        delete newMap[tempKey];
                                                                        newMap[obj.switch_sn] = mergedOptions;

                                                                        return newMap;
                                                                    });

                                                                    // 恢复保存的scheduler值
                                                                    if (savedSchedulerValue) {
                                                                        form.setFieldValue(
                                                                            ["configurations", name, "scheduler"],
                                                                            savedSchedulerValue
                                                                        );
                                                                    }
                                                                }, 200); // 等待API调用完成
                                                            }
                                                        } catch (e) {
                                                            console.error("Error parsing sysname:", e);
                                                        }
                                                    }

                                                    setSelectedSysnames(prev => {
                                                        const newSysnames = [...prev];
                                                        newSysnames[name] = value;
                                                        return newSysnames;
                                                    });
                                                }}
                                                allowClear
                                            />
                                        </Form.Item>
                                    </Col>
                                    <Col span="3">
                                        <Form.Item
                                            name={[name, "forwarding_class"]}
                                            label={
                                                index === 0 ? (
                                                    <>
                                                        Forwarding Class <span className={style.requiredIcon1}>*</span>
                                                    </>
                                                ) : (
                                                    ""
                                                )
                                            }
                                            labelCol={{
                                                span: 24,
                                                style: {
                                                    whiteSpace: "nowrap",
                                                    overflow: "hidden",
                                                    textOverflow: "ellipsis"
                                                }
                                            }}
                                            wrapperCol={{span: 24}}
                                            rules={[{required: true, message: "Please input forwarding class!"}]}
                                        >
                                            <Input placeholder="Forwarding Class" />
                                        </Form.Item>
                                    </Col>
                                    <Col span="3">
                                        <Form.Item
                                            name={[name, "local_priority"]}
                                            label={
                                                index === 0 ? (
                                                    <>
                                                        Local Priority <span className={style.requiredIcon1}>*</span>
                                                    </>
                                                ) : (
                                                    ""
                                                )
                                            }
                                            labelCol={{span: 24}}
                                            wrapperCol={{span: 24}}
                                            rules={[{required: true, message: "Please input local priority!"}]}
                                        >
                                            <Input placeholder="Local Priority" />
                                        </Form.Item>
                                    </Col>
                                    <Col span="3">
                                        <Form.Item
                                            name={[name, "scheduler"]}
                                            label={
                                                index === 0 ? (
                                                    <>
                                                        Scheduler <span className={style.requiredIcon1}>*</span>
                                                    </>
                                                ) : (
                                                    ""
                                                )
                                            }
                                            labelCol={{span: 24}}
                                            wrapperCol={{span: 24}}
                                            rules={[{required: true, message: "Please select or add a scheduler!"}]}
                                        >
                                            <Select
                                                showSearch
                                                suffixIcon={<DownOutlined />}
                                                placeholder="Select or input a scheduler"
                                                style={{width: "100%"}}
                                                options={(() => {
                                                    const sysname = form.getFieldValue([
                                                        "configurations",
                                                        name,
                                                        "sysname"
                                                    ]);

                                                    // 使用辅助函数获取选项
                                                    const options = getSchedulerOptions(sysname, name);
                                                    return options.map(item => ({
                                                        label: item,
                                                        value: item
                                                    }));
                                                })()}
                                                filterOption={(input, option) =>
                                                    option?.label.toLowerCase().includes(input.toLowerCase())
                                                }
                                                onChange={value => {
                                                    // 同步更新schedulerValuesMap
                                                    setSchedulerValuesMap(prev => ({
                                                        ...prev,
                                                        [`row_${name}`]: value
                                                    }));
                                                }}
                                                onSearch={value => {
                                                    setSearchValueMap(prev => ({
                                                        ...prev,
                                                        [name]: value
                                                    }));
                                                }}
                                                onBlur={() => {
                                                    const sysname = form.getFieldValue([
                                                        "configurations",
                                                        name,
                                                        "sysname"
                                                    ]);

                                                    const currentSearchValue = searchValueMap[name];
                                                    if (currentSearchValue?.trim()) {
                                                        // 使用辅助函数设置选项
                                                        setSchedulerOptions(sysname, name, currentSearchValue);

                                                        // 设置字段值并清除错误
                                                        form.setFields([
                                                            {
                                                                name: ["configurations", name, "scheduler"],
                                                                value: currentSearchValue,
                                                                errors: []
                                                            }
                                                        ]);

                                                        // 手动触发该字段验证以确保错误状态被清除
                                                        setTimeout(() => {
                                                            form.validateFields([
                                                                ["configurations", name, "scheduler"]
                                                            ]).catch(() => {
                                                                // 忽略验证错误，因为我们已经设置了值
                                                            });
                                                        }, 0);
                                                    }

                                                    setSearchValueMap(prev => ({
                                                        ...prev,
                                                        [name]: ""
                                                    }));
                                                }}
                                            />
                                        </Form.Item>
                                    </Col>
                                    <Col span="3">
                                        <Form.Item
                                            name={[name, "qos_mode"]}
                                            label={
                                                index === 0 ? (
                                                    <>
                                                        QoS Mode <span className={style.requiredIcon1}>*</span>
                                                    </>
                                                ) : (
                                                    ""
                                                )
                                            }
                                            labelCol={{span: 24}}
                                            wrapperCol={{span: 24}}
                                            rules={[{required: true, message: "Please select QoS mode!"}]}
                                        >
                                            <Select placeholder="QoS Mode">
                                                <Option value="SP">SP</Option>
                                                <Option value="WFQ">WFQ</Option>
                                                <Option value="WRR">WRR</Option>
                                            </Select>
                                        </Form.Item>
                                    </Col>
                                    <Col span="3">
                                        <Form.Item
                                            name={[name, "weight"]}
                                            label={
                                                index === 0 ? (
                                                    <>
                                                        Weight <span className={style.requiredIcon1}>*</span>
                                                    </>
                                                ) : (
                                                    ""
                                                )
                                            }
                                            labelCol={{span: 24}}
                                            wrapperCol={{span: 24}}
                                            rules={[{required: true, message: "Please input weight!"}]}
                                        >
                                            <Input placeholder="Weight" />
                                        </Form.Item>
                                    </Col>
                                    <Col span="3">
                                        <Form.Item
                                            name={[name, "guaranteed_rate"]}
                                            label={index === 0 ? "Guaranteed Rate" : ""}
                                            labelCol={{
                                                span: 24,
                                                style: {
                                                    whiteSpace: "nowrap",
                                                    overflow: "hidden",
                                                    textOverflow: "ellipsis"
                                                }
                                            }}
                                            wrapperCol={{span: 24}}
                                            // rules={[{required: true, message: "Please input guaranteed rate!"}]}
                                        >
                                            <Input placeholder="Guaranteed Rate" />
                                        </Form.Item>
                                    </Col>
                                    <Col>
                                        {index === 0 ? (
                                            <Button
                                                onClick={() => {
                                                    if (editRecord === null) {
                                                        add();
                                                    } else {
                                                        const currentSysname = form.getFieldValue([
                                                            "configurations",
                                                            name,
                                                            "sysname"
                                                        ]);
                                                        add({
                                                            sysname: currentSysname
                                                        });
                                                    }

                                                    // 获取当前有端口数据的交换机，重新计算端口状态
                                                    const currentValues = form.getFieldsValue();

                                                    // 找到有ingress端口数据的交换机并重新计算
                                                    const switchesWithIngressPorts = new Set();
                                                    const switchesWithEgressPorts = new Set();

                                                    const allConfigs = [
                                                        ...(currentValues.ingressConfigurations || []),
                                                        ...(currentValues.egressConfigurations || [])
                                                    ];

                                                    allConfigs.forEach(config => {
                                                        if (config.sysname) {
                                                            try {
                                                                const sysObj = JSON.parse(config.sysname);
                                                                if (sysObj.switch_sn) {
                                                                    const ingressCacheKey = `${sysObj.switch_sn}_QosIngressConfiguration`;
                                                                    const egressCacheKey = `${sysObj.switch_sn}_QosEgressConfiguration`;

                                                                    if (portDataCache.has(ingressCacheKey)) {
                                                                        switchesWithIngressPorts.add(sysObj.switch_sn);
                                                                    }
                                                                    if (portDataCache.has(egressCacheKey)) {
                                                                        switchesWithEgressPorts.add(sysObj.switch_sn);
                                                                    }
                                                                }
                                                            } catch (e) {}
                                                        }
                                                    });

                                                    // 重新计算ingress端口状态
                                                    switchesWithIngressPorts.forEach(sn => {
                                                        const cacheKey = `${sn}_QosIngressConfiguration`;
                                                        const portData = portDataCache.get(cacheKey);
                                                        if (portData) {
                                                            setTimeout(
                                                                () =>
                                                                    calculateIngressPortTreeData(sn, [
                                                                        {
                                                                            title: "All Ports",
                                                                            value: JSON.stringify(
                                                                                portData.map(item => item.port_name)
                                                                            ),
                                                                            children: portData.map(item => ({
                                                                                title: item.port_name,
                                                                                value: item.port_name,
                                                                                key: item.port_name,
                                                                                disabled: !item.enabled
                                                                            }))
                                                                        }
                                                                    ]),
                                                                100
                                                            );
                                                        }
                                                    });

                                                    // 重新计算egress端口状态
                                                    switchesWithEgressPorts.forEach(sn => {
                                                        const cacheKey = `${sn}_QosEgressConfiguration`;
                                                        const portData = portDataCache.get(cacheKey);
                                                        if (portData) {
                                                            setTimeout(
                                                                () =>
                                                                    calculateEgressPortTreeData(sn, [
                                                                        {
                                                                            title: "All Ports",
                                                                            value: JSON.stringify(
                                                                                portData.map(item => item.port_name)
                                                                            ),
                                                                            children: portData.map(item => ({
                                                                                title: item.port_name,
                                                                                value: item.port_name,
                                                                                key: item.port_name,
                                                                                disabled: !item.enabled
                                                                            }))
                                                                        }
                                                                    ]),
                                                                100
                                                            );
                                                        }
                                                    });
                                                }}
                                                style={{
                                                    backgroundColor: "transparent",
                                                    color: "#BFBFBF",
                                                    marginBottom: "24px",
                                                    marginTop: "40px"
                                                }}
                                                type="link"
                                                icon={<PlusOutlined />}
                                            />
                                        ) : (
                                            <Button
                                                onClick={() => {
                                                    const currentValues = form.getFieldsValue();
                                                    const configToRemove = currentValues.configurations[name];

                                                    remove(name);

                                                    if (configToRemove && configToRemove.sysname) {
                                                        try {
                                                            const sysObj = JSON.parse(configToRemove.sysname);
                                                            if (sysObj.switch_sn) {
                                                                setTimeout(() => {
                                                                    // Configuration 部分不需要端口数据重新计算
                                                                }, 100);
                                                            }
                                                        } catch (e) {
                                                            console.error("Error parsing sysname:", e);
                                                        }
                                                    }
                                                }}
                                                style={{
                                                    backgroundColor: "transparent",
                                                    color: "#BFBFBF",
                                                    marginBottom: "24px"
                                                }}
                                                type="link"
                                                icon={<MinusOutlined />}
                                            />
                                        )}
                                    </Col>
                                </Row>
                            ))}
                        </>
                    )}
                </Form.List>
                {/* Ingress Port Configuration Section */}
                <div
                    style={{
                        fontSize: "18px",
                        fontWeight: "bold",
                        borderBottom: "1px solid rgba(5, 5, 5, 0.06)",
                        paddingBottom: "10px",
                        marginLeft: "-32px",
                        marginRight: "-32px",
                        paddingLeft: "32px",
                        paddingRight: "32px",
                        marginBottom: "20px",
                        marginTop: "40px"
                    }}
                >
                    Ingress Port Configuration
                </div>
                <Form.List name="ingressConfigurations" initialValue={[{}]}>
                    {(fields, {add, remove}) => (
                        <>
                            {fields.map(({key, name}, index) => (
                                <Row key={key}>
                                    <Col span="3" style={{marginRight: 24}}>
                                        <Form.Item
                                            name={[name, "sysname"]}
                                            label={
                                                index === 0 ? (
                                                    <>
                                                        Sysname <span className={style.requiredIcon1}>*</span>
                                                    </>
                                                ) : (
                                                    ""
                                                )
                                            }
                                            labelCol={{span: 24}}
                                            wrapperCol={{span: 24}}
                                            rules={[{required: true, message: "Please select sysname!"}]}
                                        >
                                            <TreeSelect
                                                disabled={editRecord === null ? false : true}
                                                treeData={fabricTreeData}
                                                placeholder="Sysname"
                                                treeDefaultExpandAll
                                                onChange={value => {
                                                    if (value !== null) {
                                                        try {
                                                            const obj = JSON.parse(value);
                                                            if (obj.switch_sn) {
                                                                fetchPortsBySn(
                                                                    obj.switch_sn,
                                                                    "QosIngressConfiguration",
                                                                    true
                                                                );
                                                            }
                                                        } catch (e) {
                                                            console.error("Error parsing sysname:", e);
                                                        }
                                                    }

                                                    form.setFields([
                                                        {
                                                            name: ["ingressConfigurations", name, "ports"],
                                                            value: []
                                                        }
                                                    ]);
                                                }}
                                                allowClear
                                            />
                                        </Form.Item>
                                    </Col>
                                    <Col span="3" style={{marginRight: 24}}>
                                        <Form.Item
                                            name={[name, "classifier"]}
                                            label={
                                                index === 0 ? (
                                                    <>
                                                        Classifier <span className={style.requiredIcon1}>*</span>
                                                    </>
                                                ) : (
                                                    ""
                                                )
                                            }
                                            labelCol={{span: 24}}
                                            wrapperCol={{span: 24}}
                                            rules={[{required: true, message: "Please input classifier!"}]}
                                        >
                                            <Input placeholder="Classifier" />
                                        </Form.Item>
                                    </Col>
                                    <Col span="3" style={{marginRight: 24}}>
                                        <Form.Item
                                            name={[name, "trust_mode"]}
                                            label={
                                                index === 0 ? (
                                                    <>
                                                        Trust Mode <span className={style.requiredIcon1}>*</span>
                                                    </>
                                                ) : (
                                                    ""
                                                )
                                            }
                                            labelCol={{span: 24}}
                                            wrapperCol={{span: 24}}
                                            rules={[{required: true, message: "Please select trust mode!"}]}
                                        >
                                            <Select placeholder="Trust Mode">
                                                <Option value="dscp">dscp</Option>
                                                <Option value="inet-precedence">inet-precedence</Option>
                                                <Option value="ieee-802.1">ieee-802.1</Option>
                                            </Select>
                                        </Form.Item>
                                    </Col>
                                    <Col span="3" style={{marginRight: 24}}>
                                        <Form.Item
                                            name={[name, "ports"]}
                                            label={
                                                index === 0 ? (
                                                    <>
                                                        Ports <span className={style.requiredIcon1}>*</span>
                                                    </>
                                                ) : (
                                                    ""
                                                )
                                            }
                                            labelCol={{span: 24}}
                                            wrapperCol={{span: 24}}
                                            rules={[{required: true, message: "Please select ports!"}]}
                                        >
                                            <CustomTreeSelect
                                                treeData={ingressPortTreeData}
                                                placeholder="Ports"
                                                onFocus={() => {
                                                    // 直接获取当前字段的值
                                                    const currentPortValue = form.getFieldValue([
                                                        "ingressConfigurations",
                                                        name,
                                                        "ports"
                                                    ]);
                                                    const currentSysname = form.getFieldValue([
                                                        "ingressConfigurations",
                                                        name,
                                                        "sysname"
                                                    ]);
                                                    // console.log("currentPortValue", currentPortValue);
                                                    if (currentSysname && currentPortValue) {
                                                        try {
                                                            const sysObj = JSON.parse(currentSysname);
                                                            if (sysObj.switch_sn) {
                                                                const cacheKey = `${sysObj.switch_sn}_QosIngressConfiguration`;
                                                                const portData = portDataCache.get(cacheKey);
                                                                if (portData) {
                                                                    // 解析端口值
                                                                    let portsToEnable = [];
                                                                    if (
                                                                        Array.isArray(currentPortValue) &&
                                                                        currentPortValue.length > 0
                                                                    ) {
                                                                        try {
                                                                            const allPortsValue = JSON.stringify(
                                                                                portData.map(item => item.port_name)
                                                                            );
                                                                            if (currentPortValue[0] === allPortsValue) {
                                                                                portsToEnable = JSON.parse(
                                                                                    currentPortValue[0]
                                                                                );
                                                                            } else {
                                                                                portsToEnable = currentPortValue;
                                                                            }
                                                                        } catch (e) {
                                                                            portsToEnable = currentPortValue;
                                                                        }
                                                                    }
                                                                    console.log(
                                                                        "ingress portTreeData",
                                                                        ingressPortTreeData
                                                                    );
                                                                    // 直接调用 calculatePortTreeData 释放端口
                                                                    calculateIngressPortTreeData(
                                                                        sysObj.switch_sn,
                                                                        ingressPortTreeData,
                                                                        "enable",
                                                                        portsToEnable,
                                                                        name
                                                                    );
                                                                }
                                                            }
                                                        } catch (e) {
                                                            console.error("Error parsing sysname:", e);
                                                        }
                                                    }
                                                }}
                                                onBlur={() => {
                                                    const currentSysname = form.getFieldValue([
                                                        "ingressConfigurations",
                                                        name,
                                                        "sysname"
                                                    ]);
                                                    try {
                                                        const sysObj = JSON.parse(currentSysname);
                                                        calculateIngressPortTreeData(
                                                            sysObj.switch_sn,
                                                            ingressPortTreeData,
                                                            "disable",
                                                            editPortData,
                                                            name
                                                        );
                                                    } catch (e) {
                                                        console.error("Error parsing sysname:", e);
                                                    }
                                                }}
                                                onChange={(value, label) => {
                                                    console.log("value", value);
                                                    setEditPortData(value);
                                                    const isAllPorts = label[0] === "All Ports";
                                                    form.setFieldValue(
                                                        ["ingressConfigurations", name, "is_all_ports"],
                                                        isAllPorts
                                                    );
                                                    form.setFieldValue(["ingressConfigurations", name, "ports"], value);
                                                }}
                                            />
                                        </Form.Item>
                                    </Col>
                                    <Col span="3" style={{marginRight: 24}}>
                                        <Form.Item
                                            name={[name, "forwarding_class"]}
                                            label={
                                                index === 0 ? (
                                                    <>
                                                        Forwarding Class <span className={style.requiredIcon1}>*</span>
                                                    </>
                                                ) : (
                                                    ""
                                                )
                                            }
                                            labelCol={{
                                                span: 24,
                                                style: {
                                                    whiteSpace: "nowrap",
                                                    overflow: "hidden",
                                                    textOverflow: "ellipsis"
                                                }
                                            }}
                                            wrapperCol={{span: 24}}
                                            rules={[{required: true, message: "Please select forwarding class!"}]}
                                        >
                                            <Select placeholder="Forwarding Class">
                                                {forwardingClassOptions.map(opt => (
                                                    <Option key={opt} value={opt}>
                                                        {opt}
                                                    </Option>
                                                ))}
                                            </Select>
                                        </Form.Item>
                                    </Col>
                                    <Col span="3" style={{marginRight: 24}}>
                                        <Form.Item
                                            name={[name, "queues"]}
                                            label={
                                                index === 0 ? (
                                                    <>
                                                        Queues <span className={style.requiredIcon1}>*</span>
                                                    </>
                                                ) : (
                                                    ""
                                                )
                                            }
                                            labelCol={{span: 24}}
                                            wrapperCol={{span: 24}}
                                            rules={[{required: true, message: "Please select queues!"}]}
                                        >
                                            <CustomTreeSelect
                                                treeData={[
                                                    {
                                                        title: "All Queues",
                                                        value: JSON.stringify(
                                                            Array.from({length: 8}, (_, i) => i.toString())
                                                        ),
                                                        children: Array.from({length: 8}, (_, i) => ({
                                                            title: i.toString(),
                                                            value: i.toString(),
                                                            key: i.toString()
                                                        }))
                                                    }
                                                ]}
                                                placeholder="Queues"
                                                onChange={(value, label) => {
                                                    const isAllQueues = label[0] === "All Queues";
                                                    form.setFieldValue(
                                                        ["ingressConfigurations", name, "is_all_queues"],
                                                        isAllQueues
                                                    );
                                                    form.setFieldValue(
                                                        ["ingressConfigurations", name, "queues"],
                                                        value
                                                    );
                                                }}
                                            />
                                        </Form.Item>
                                    </Col>
                                    <Col>
                                        {index === 0 ? (
                                            <Button
                                                onClick={() => {
                                                    if (editRecord === null) {
                                                        add();
                                                    } else {
                                                        const currentSysname = form.getFieldValue([
                                                            "ingressConfigurations",
                                                            name,
                                                            "sysname"
                                                        ]);
                                                        add({
                                                            sysname: currentSysname
                                                        });
                                                    }
                                                    // 获取当前有端口数据的交换机，重新计算端口状态
                                                    const currentValues = form.getFieldsValue();
                                                    const allConfigs = [
                                                        ...(currentValues.ingressConfigurations || []),
                                                        ...(currentValues.egressConfigurations || [])
                                                    ];

                                                    // 找到有端口数据的交换机并重新计算
                                                    const switchesWithPorts = new Set();
                                                    allConfigs.forEach(config => {
                                                        if (config.sysname) {
                                                            try {
                                                                const sysObj = JSON.parse(config.sysname);
                                                                if (
                                                                    sysObj.switch_sn &&
                                                                    portDataCache.has(
                                                                        `${sysObj.switch_sn}_QosIngressConfiguration`
                                                                    )
                                                                ) {
                                                                    switchesWithPorts.add(sysObj.switch_sn);
                                                                }
                                                            } catch (e) {}
                                                        }
                                                    });

                                                    // 重新计算每个交换机的端口状态
                                                    switchesWithPorts.forEach(sn => {
                                                        const cacheKey = `${sn}_QosIngressConfiguration`;
                                                        const portData = portDataCache.get(cacheKey);
                                                        if (portData) {
                                                            setTimeout(
                                                                () =>
                                                                    calculateIngressPortTreeData(sn, [
                                                                        {
                                                                            title: "All Ports",
                                                                            value: JSON.stringify(
                                                                                portData.map(item => item.port_name)
                                                                            ),
                                                                            children: portData.map(item => ({
                                                                                title: item.port_name,
                                                                                value: item.port_name,
                                                                                key: item.port_name,
                                                                                disabled: !item.enabled
                                                                            }))
                                                                        }
                                                                    ]),
                                                                100
                                                            );
                                                        }
                                                    });
                                                }}
                                                style={{
                                                    backgroundColor: "transparent",
                                                    color: "#BFBFBF",
                                                    marginBottom: "24px",
                                                    marginTop: "40px"
                                                }}
                                                type="link"
                                                icon={<PlusOutlined />}
                                            />
                                        ) : (
                                            <Button
                                                onClick={() => {
                                                    const currentValues = form.getFieldsValue();
                                                    const configToRemove = currentValues.ingressConfigurations[name];

                                                    remove(name);

                                                    if (configToRemove && configToRemove.sysname) {
                                                        try {
                                                            const sysObj = JSON.parse(configToRemove.sysname);
                                                            if (sysObj.switch_sn) {
                                                                setTimeout(() => {
                                                                    // 使用QosIngressConfiguration的缓存key
                                                                    const cacheKey = `${sysObj.switch_sn}_QosIngressConfiguration`;
                                                                    const portData = portDataCache.get(cacheKey);
                                                                    if (portData) {
                                                                        calculateIngressPortTreeData(sysObj.switch_sn, [
                                                                            {
                                                                                title: "All Ports",
                                                                                value: JSON.stringify(
                                                                                    portData.map(item => item.port_name)
                                                                                ),
                                                                                children: portData.map(item => ({
                                                                                    title: item.port_name,
                                                                                    value: item.port_name,
                                                                                    key: item.port_name,
                                                                                    disabled: !item.enabled
                                                                                }))
                                                                            }
                                                                        ]);
                                                                    }
                                                                }, 100);
                                                            }
                                                        } catch (e) {
                                                            console.error("Error parsing sysname:", e);
                                                        }
                                                    }
                                                }}
                                                style={{
                                                    backgroundColor: "transparent",
                                                    color: "#BFBFBF",
                                                    marginBottom: "24px"
                                                }}
                                                type="link"
                                                icon={<MinusOutlined />}
                                            />
                                        )}
                                    </Col>
                                </Row>
                            ))}
                        </>
                    )}
                </Form.List>
                {/* Egress Port Configuration Section */}
                <div
                    style={{
                        fontSize: "18px",
                        fontWeight: "bold",
                        borderBottom: "1px solid rgba(5, 5, 5, 0.06)",
                        paddingBottom: "10px",
                        marginLeft: "-32px",
                        marginRight: "-32px",
                        paddingLeft: "32px",
                        paddingRight: "32px",
                        marginBottom: "20px",
                        marginTop: "40px"
                    }}
                >
                    Egress Port Configuration
                </div>
                <Form.List name="egressConfigurations" initialValue={[{}]}>
                    {(fields, {add, remove}) => (
                        <>
                            {fields.map(({key, name}, index) => (
                                <Row key={key}>
                                    <Col span="3" style={{marginRight: 24}}>
                                        <Form.Item
                                            name={[name, "sysname"]}
                                            label={
                                                index === 0 ? (
                                                    <>
                                                        Sysname <span className={style.requiredIcon1}>*</span>
                                                    </>
                                                ) : (
                                                    ""
                                                )
                                            }
                                            labelCol={{span: 24}}
                                            wrapperCol={{span: 24}}
                                            rules={[{required: true, message: "Please select sysname!"}]}
                                        >
                                            <TreeSelect
                                                disabled={editRecord === null ? false : true}
                                                treeData={fabricTreeData}
                                                placeholder="Sysname"
                                                treeDefaultExpandAll
                                                onChange={value => {
                                                    if (value !== null) {
                                                        try {
                                                            const obj = JSON.parse(value);
                                                            if (obj.switch_sn) {
                                                                fetchPortsBySn(
                                                                    obj.switch_sn,
                                                                    "QosEgressConfiguration",
                                                                    true
                                                                );
                                                            }
                                                        } catch (e) {
                                                            console.error("Error parsing sysname:", e);
                                                        }
                                                    }

                                                    form.setFields([
                                                        {
                                                            name: ["egressConfigurations", name, "ports"],
                                                            value: []
                                                        }
                                                    ]);
                                                }}
                                                allowClear
                                            />
                                        </Form.Item>
                                    </Col>
                                    <Col span="3" style={{marginRight: 24}}>
                                        <Form.Item
                                            name={[name, "scheduler_profile"]}
                                            label={
                                                index === 0 ? (
                                                    <>
                                                        Scheduler Profile <span className={style.requiredIcon1}>*</span>
                                                    </>
                                                ) : (
                                                    ""
                                                )
                                            }
                                            labelCol={{
                                                span: 24,
                                                style: {
                                                    whiteSpace: "nowrap",
                                                    overflow: "hidden",
                                                    textOverflow: "ellipsis"
                                                }
                                            }}
                                            wrapperCol={{span: 24}}
                                            rules={[{required: true, message: "Please input scheduler profile!"}]}
                                        >
                                            <Input placeholder="Scheduler Profile" />
                                        </Form.Item>
                                    </Col>
                                    <Col span="3" style={{marginRight: 24}}>
                                        <Form.Item
                                            name={[name, "scheduler"]}
                                            label={
                                                index === 0 ? (
                                                    <>
                                                        Scheduler <span className={style.requiredIcon1}>*</span>
                                                    </>
                                                ) : (
                                                    ""
                                                )
                                            }
                                            labelCol={{span: 24}}
                                            wrapperCol={{span: 24}}
                                            rules={[{required: true, message: "Please select scheduler!"}]}
                                        >
                                            <Select
                                                placeholder="Select scheduler"
                                                style={{width: "100%"}}
                                                options={(() => {
                                                    const sysname = form.getFieldValue([
                                                        "egressConfigurations",
                                                        name,
                                                        "sysname"
                                                    ]);

                                                    // 使用辅助函数获取选项
                                                    const options = getSchedulerOptions(sysname, `egress_${name}`);
                                                    return options.map(item => ({
                                                        label: item,
                                                        value: item
                                                    }));
                                                })()}
                                                allowClear
                                            />
                                        </Form.Item>
                                    </Col>
                                    <Col span="3" style={{marginRight: 24}}>
                                        <Form.Item
                                            name={[name, "ports"]}
                                            label={
                                                index === 0 ? (
                                                    <>
                                                        Ports <span className={style.requiredIcon1}>*</span>
                                                    </>
                                                ) : (
                                                    ""
                                                )
                                            }
                                            labelCol={{span: 24}}
                                            wrapperCol={{span: 24}}
                                            rules={[{required: true, message: "Please select ports!"}]}
                                        >
                                            <CustomTreeSelect
                                                treeData={egressPortTreeData}
                                                placeholder="Ports"
                                                onFocus={() => {
                                                    // 直接获取当前字段的值
                                                    const currentPortValue = form.getFieldValue([
                                                        "egressConfigurations",
                                                        name,
                                                        "ports"
                                                    ]);
                                                    const currentSysname = form.getFieldValue([
                                                        "egressConfigurations",
                                                        name,
                                                        "sysname"
                                                    ]);
                                                    // console.log("currentPortValue", currentPortValue);
                                                    if (currentSysname && currentPortValue) {
                                                        try {
                                                            const sysObj = JSON.parse(currentSysname);
                                                            if (sysObj.switch_sn) {
                                                                const cacheKey = `${sysObj.switch_sn}_QosEgressConfiguration`;
                                                                const portData = portDataCache.get(cacheKey);
                                                                if (portData) {
                                                                    // 解析端口值
                                                                    let portsToEnable = [];
                                                                    if (
                                                                        Array.isArray(currentPortValue) &&
                                                                        currentPortValue.length > 0
                                                                    ) {
                                                                        try {
                                                                            const allPortsValue = JSON.stringify(
                                                                                portData.map(item => item.port_name)
                                                                            );
                                                                            if (currentPortValue[0] === allPortsValue) {
                                                                                portsToEnable = JSON.parse(
                                                                                    currentPortValue[0]
                                                                                );
                                                                            } else {
                                                                                portsToEnable = currentPortValue;
                                                                            }
                                                                        } catch (e) {
                                                                            portsToEnable = currentPortValue;
                                                                        }
                                                                    }
                                                                    console.log(
                                                                        "egress portTreeData",
                                                                        egressPortTreeData
                                                                    );
                                                                    // 直接调用 calculatePortTreeData 释放端口
                                                                    calculateEgressPortTreeData(
                                                                        sysObj.switch_sn,
                                                                        egressPortTreeData,
                                                                        "enable",
                                                                        portsToEnable,
                                                                        name
                                                                    );
                                                                }
                                                            }
                                                        } catch (e) {
                                                            console.error("Error parsing sysname:", e);
                                                        }
                                                    }
                                                }}
                                                onBlur={() => {
                                                    const currentSysname = form.getFieldValue([
                                                        "egressConfigurations",
                                                        name,
                                                        "sysname"
                                                    ]);
                                                    try {
                                                        const sysObj = JSON.parse(currentSysname);
                                                        calculateEgressPortTreeData(
                                                            sysObj.switch_sn,
                                                            egressPortTreeData,
                                                            "disable",
                                                            editPortData,
                                                            name
                                                        );
                                                    } catch (e) {
                                                        console.error("Error parsing sysname:", e);
                                                    }
                                                }}
                                                onChange={(value, label) => {
                                                    console.log("value", value);
                                                    setEditPortData(value);
                                                    const isAllPorts = label[0] === "All Ports";
                                                    form.setFieldValue(
                                                        ["egressConfigurations", name, "is_all_ports"],
                                                        isAllPorts
                                                    );
                                                    form.setFieldValue(["egressConfigurations", name, "ports"], value);
                                                }}
                                            />
                                        </Form.Item>
                                    </Col>
                                    <Col span="3" style={{marginRight: 24}}>
                                        <Form.Item
                                            name={[name, "forwarding_class"]}
                                            label={
                                                index === 0 ? (
                                                    <>
                                                        Forwarding Class <span className={style.requiredIcon1}>*</span>
                                                    </>
                                                ) : (
                                                    ""
                                                )
                                            }
                                            labelCol={{
                                                span: 24,
                                                style: {
                                                    whiteSpace: "nowrap",
                                                    overflow: "hidden",
                                                    textOverflow: "ellipsis"
                                                }
                                            }}
                                            wrapperCol={{span: 24}}
                                            rules={[{required: true, message: "Please select forwarding class!"}]}
                                        >
                                            <Select
                                                placeholder="Forwarding Class"
                                                onChange={() => {
                                                    const sysname = form.getFieldValue([
                                                        "egressConfigurations",
                                                        name,
                                                        "sysname"
                                                    ]);
                                                    const forwardclass = form.getFieldValue([
                                                        "egressConfigurations",
                                                        name,
                                                        "forwarding_class"
                                                    ]);
                                                    const key = `${JSON.parse(sysname).switch_sn}-${forwardclass}`;
                                                    form.setFieldValue(
                                                        ["egressConfigurations", name, "local_priority"],
                                                        snFcPriorityMap[key] || ""
                                                    );
                                                }}
                                            >
                                                {forwardingClassOptions.map(opt => (
                                                    <Option key={opt} value={opt}>
                                                        {opt}
                                                    </Option>
                                                ))}
                                            </Select>
                                        </Form.Item>
                                    </Col>
                                    <Col span="3" style={{marginRight: 24}}>
                                        <Form.Item
                                            name={[name, "local_priority"]}
                                            label={index === 0 ? "Local Priority" : ""}
                                            labelCol={{span: 24}}
                                            wrapperCol={{span: 24}}
                                            // rules={[{required: true, message: "Please input local priority!"}]}
                                        >
                                            <Input placeholder="Local Priority" disabled />
                                        </Form.Item>
                                    </Col>
                                    <Col>
                                        {index === 0 ? (
                                            <Button
                                                onClick={() => {
                                                    if (editRecord === null) {
                                                        add();
                                                    } else {
                                                        const currentSysname = form.getFieldValue([
                                                            "egressConfigurations",
                                                            name,
                                                            "sysname"
                                                        ]);
                                                        add({
                                                            sysname: currentSysname
                                                        });
                                                    }
                                                    // 获取当前有端口数据的交换机，重新计算端口状态
                                                    const currentValues = form.getFieldsValue();
                                                    const allConfigs = [
                                                        ...(currentValues.ingressConfigurations || []),
                                                        ...(currentValues.egressConfigurations || [])
                                                    ];

                                                    // 找到有端口数据的交换机并重新计算
                                                    const switchesWithPorts = new Set();
                                                    allConfigs.forEach(config => {
                                                        if (config.sysname) {
                                                            try {
                                                                const sysObj = JSON.parse(config.sysname);
                                                                if (
                                                                    sysObj.switch_sn &&
                                                                    portDataCache.has(
                                                                        `${sysObj.switch_sn}_QosEgressConfiguration`
                                                                    )
                                                                ) {
                                                                    switchesWithPorts.add(sysObj.switch_sn);
                                                                }
                                                            } catch (e) {}
                                                        }
                                                    });

                                                    // 重新计算每个交换机的端口状态
                                                    switchesWithPorts.forEach(sn => {
                                                        const cacheKey = `${sn}_QosEgressConfiguration`;
                                                        const portData = portDataCache.get(cacheKey);
                                                        if (portData) {
                                                            setTimeout(
                                                                () =>
                                                                    calculateEgressPortTreeData(sn, [
                                                                        {
                                                                            title: "All Ports",
                                                                            value: JSON.stringify(
                                                                                portData.map(item => item.port_name)
                                                                            ),
                                                                            children: portData.map(item => ({
                                                                                title: item.port_name,
                                                                                value: item.port_name,
                                                                                key: item.port_name,
                                                                                disabled: !item.enabled
                                                                            }))
                                                                        }
                                                                    ]),
                                                                100
                                                            );
                                                        }
                                                    });
                                                }}
                                                style={{
                                                    backgroundColor: "transparent",
                                                    color: "#BFBFBF",
                                                    marginBottom: "24px",
                                                    marginTop: "40px"
                                                }}
                                                type="link"
                                                icon={<PlusOutlined />}
                                            />
                                        ) : (
                                            <Button
                                                onClick={() => {
                                                    const currentValues = form.getFieldsValue();
                                                    const configToRemove = currentValues.egressConfigurations[name];

                                                    remove(name);

                                                    if (configToRemove && configToRemove.sysname) {
                                                        try {
                                                            const sysObj = JSON.parse(configToRemove.sysname);
                                                            if (sysObj.switch_sn) {
                                                                setTimeout(() => {
                                                                    // 使用QosEgressConfiguration的缓存key
                                                                    const cacheKey = `${sysObj.switch_sn}_QosEgressConfiguration`;
                                                                    const portData = portDataCache.get(cacheKey);
                                                                    if (portData) {
                                                                        calculateEgressPortTreeData(sysObj.switch_sn, [
                                                                            {
                                                                                title: "All Ports",
                                                                                value: JSON.stringify(
                                                                                    portData.map(item => item.port_name)
                                                                                ),
                                                                                children: portData.map(item => ({
                                                                                    title: item.port_name,
                                                                                    value: item.port_name,
                                                                                    key: item.port_name,
                                                                                    disabled: !item.enabled
                                                                                }))
                                                                            }
                                                                        ]);
                                                                    }
                                                                }, 100);
                                                            }
                                                        } catch (e) {
                                                            console.error("Error parsing sysname:", e);
                                                        }
                                                    }
                                                }}
                                                style={{
                                                    backgroundColor: "transparent",
                                                    color: "#BFBFBF",
                                                    marginBottom: "24px"
                                                }}
                                                type="link"
                                                icon={<MinusOutlined />}
                                            />
                                        )}
                                    </Col>
                                </Row>
                            ))}
                        </>
                    )}
                </Form.List>
            </>
        );
    };

    // Service Scheduling 表格配置
    const schedulingColumns = [
        {
            title: "",
            key: "expand",
            width: 50,
            render: (_, record) => {}
        },
        createColumnConfig("Sysname", "sysname", TableFilterDropdown),
        createColumnConfig("Forwarding Class", "forwarding_class"),
        createColumnConfig("Local Priority", "local_priority"),
        createColumnConfig("Scheduler", "scheduler"),
        createColumnConfig("Work mode", "mode"),
        createColumnConfig("Weight", "weight"),
        createColumnConfig("Guaranteed Rate", "guaranteed_rate"),
        {
            ...createColumnConfig("Ingress Ports", "ingress_ports"),
            render: (text, record) => {
                // 根据 is_ingress_all_ports 字段判断显示内容
                if (record.is_ingress_all_ports) {
                    return "All Ports";
                }

                try {
                    const arr = JSON.parse(text);
                    return Array.isArray(arr) ? arr.flat().join(", ") : text;
                } catch {
                    return text;
                }
            }
        },
        {
            ...createColumnConfig("Egress Ports", "egress_ports"),
            render: (text, record) => {
                // 根据 is_egress_all_ports 字段判断显示内容
                if (record.is_egress_all_ports) {
                    return "All Ports";
                }

                try {
                    const arr = JSON.parse(text);
                    return Array.isArray(arr) ? arr.flat().join(", ") : text;
                } catch {
                    return text;
                }
            }
        },
        {
            title: "Operation",
            key: "operation",
            render: (_, record) => (
                <Space size="middle">
                    <Button type="link" onClick={() => handleEdit(record)}>
                        Edit
                    </Button>
                    <Button
                        type="link"
                        onClick={() =>
                            confirmModalAction("Are you sure you want to delete the configuration items?", () =>
                                handleDeleteConfirm(record)
                            )
                        }
                    >
                        Delete
                    </Button>
                </Space>
            )
        }
    ];

    return (
        <div>
            <h2>
                Service Scheduling
                <CustomQuestionIcon
                    title="Configure PFC buffer to implement trafficcontrol and PFC watchdog, which is basedon interface and priority queue. Thestorage space of each interface is dividedinto different buffers independently and acertain action will be executed after thenumber of accumulated packets reachesthe buffer threshold (the unit is cell)."
                    placement="right"
                />
            </h2>
            <div style={{marginBottom: 16}}>
                <Button type="primary" icon={<PlusOutlined />} onClick={handleCreate}>
                    Configuration
                </Button>
            </div>
            <ExtendedTable
                columns={schedulingColumns}
                fetchAPIInfo={getSchedulingConfigList}
                ref={tableRef}
                bordered={false}
            />

            {/* 修改模态框，参考 PFCConfiguration */}
            <AmpConCustomModalForm
                title={editRecord ? "Edit Configuration" : "Create Scheduling Configuration"}
                isModalOpen={isModalVisible}
                formInstance={form}
                layoutProps={{
                    labelCol: {
                        span: 4
                    }
                }}
                CustomFormItems={formItems}
                onCancel={handleModalCancel}
                onSubmit={handleSubmit}
                modalClass="ampcon-max-modal"
                confirmLoading={saveLoading}
                footer={[
                    <Divider style={{marginTop: 0, marginBottom: 20}} />,
                    <Button key="cancel" onClick={handleModalCancel}>
                        Cancel
                    </Button>,
                    <Button key="ok" type="primary" onClick={form.submit}>
                        Apply
                    </Button>
                ]}
            />
        </div>
    );
};

export default SchedulingConfig;
