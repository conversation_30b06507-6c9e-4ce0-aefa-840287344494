import React, {useState, useEffect, useRef, useMemo} from "react";
import {Button, Tooltip, Form, Space, Row, Col, Select, Input, TreeSelect, message, Divider} from "antd";
import {QuestionCircleFilled, PlusOutlined, MinusOutlined} from "@ant-design/icons";
import {
    AmpConCustomTable,
    createColumnConfig,
    TableFilterDropdown,
    AmpConCustomModalForm
} from "@/modules-ampcon/components/custom_table";
import {
    getPfcConfigList,
    savePfcConfig,
    updatePfcConfig,
    getFabricSwitches,
    getSwitchPorts,
    getFilterSwitchPorts,
    deletePfcConfig,
    validatePfcConfig,
    getPfcConfigDetailBySwitch
} from "@/modules-ampcon/apis/roce_api";
import {confirmModalAction} from "@/modules-ampcon/components/custom_modal";
import CustomQuestionIcon from "./QuestionIcon";
import CustomTreeSelect from "./customTreeSelect";
import {ExtendedTable} from "./ext_table";
import style from "./roce_policies.module.scss";

const {Option} = Select;

const PFCConfiguration = () => {
    // 状态管理
    const [isModalVisible, setIsModalVisible] = useState(false);
    const [form] = Form.useForm();
    const [fabricTreeData, setFabricTreeData] = useState([]);
    const [portTreeData, setPortTreeData] = useState([]);
    const [editRecord, setEditRecord] = useState(null);
    const [editPortData, setEditPortData] = useState([]);

    // PFC Enabled 选项
    const pfcEnabledOptions = ["True", "False"];
    const queueLists = ["0", "1", "2", "3", "4", "5", "6", "7"];

    const [portDataCache, setPortDataCache] = useState(new Map());

    const tableRef = useRef(null);

    const fetchFabricTreeData = async () => {
        try {
            const res = await getFabricSwitches();
            if (res && res.data) {
                const fabricKeys = Object.keys(res.data); // ["default"]
                const fabricValues = Object.values(res.data); // [switch array]

                const tree = fabricKeys.map((fabricName, index) => {
                    const switches = fabricValues[index];

                    // Filter out switches with enabled=false
                    const enabledSwitches = switches.filter(sw => sw.enabled === true);

                    return {
                        title: fabricName,
                        value: fabricName,
                        key: fabricName,
                        disabled: true,
                        children: enabledSwitches.map(sw => ({
                            title: sw.sysname || "Unknown",
                            value: JSON.stringify({sn: sw.switch_sn, sysname: sw.sysname}),
                            key: JSON.stringify({sn: sw.switch_sn, sysname: sw.sysname})
                        }))
                    };
                });

                setFabricTreeData(tree);
            }
        } catch (e) {
            message.error("Failed to get fabric switches");
        }
    };

    // 添加端口数据缓存
    const initPortTreeData = portData => {
        const allOccupiedPorts = new Set();
        console.log("portData", portData);
        portData.forEach(item => {
            if (!item.enabled) {
                allOccupiedPorts.add(item.port_name);
            }
        });
        console.log("allOccupiedPorts", allOccupiedPorts);

        const allPortsForDisplay = portData.map(item => ({
            title: item.port_name,
            value: item.port_name,
            key: item.port_name,
            disabled: allOccupiedPorts.has(item.port_name)
        }));

        // 检查是否有任意端口被占用
        const hasAnyPortOccupied = portData.some(item => allOccupiedPorts.has(item.port_name));

        const tree = [
            {
                title: "All Ports",
                value: JSON.stringify(portData.map(item => item.port_name)),
                disabled: hasAnyPortOccupied,
                children: allPortsForDisplay
            }
        ];

        setPortTreeData(tree);
    };

    // 简化为类似 PFCWDConfiguration 的端口状态计算函数
    const calculatePortTreeData = (sn, portTreeData, action = null, currentPorts = [], index = null) => {
        const allOccupiedPorts = new Set();
        const currentFormValues = form.getFieldsValue();

        const portData = portTreeData[0].children;
        console.log("portTreeData", portTreeData);
        console.log("portData", portData);

        portData.forEach(item => {
            if (item.disabled) {
                allOccupiedPorts.add(item.value);
            }
        });

        if (currentFormValues.configurations) {
            currentFormValues.configurations.forEach((config, index) => {
                if (config.sysname) {
                    try {
                        const sysObj = JSON.parse(config.sysname);
                        if (sysObj.sn === sn && config.ports) {
                            let selectedPorts = [];
                            if (config.ports && Array.isArray(config.ports) && config.ports.length > 0) {
                                try {
                                    const allPortsValue = JSON.stringify(portData.map(item => item.value));
                                    if (config.ports[0] === allPortsValue) {
                                        selectedPorts = JSON.parse(config.ports[0]);
                                    } else {
                                        selectedPorts = config.ports;
                                    }
                                } catch (e) {
                                    selectedPorts = config.ports;
                                }
                            }
                            selectedPorts.forEach(port => allOccupiedPorts.add(port));
                        }
                    } catch (e) {
                        console.error("Error parsing sysname or ports:", e);
                    }
                }
            });
        }

        // 根据action处理指定的端口
        if (action === "enable") {
            // 释放指定的端口
            currentPorts.forEach(port => allOccupiedPorts.delete(port));
        } else if (action === "disable") {
            currentPorts.forEach(port => allOccupiedPorts.add(port));
        }

        console.log("allOccupiedPorts", allOccupiedPorts);

        // 计算最终的端口可用性
        const allPortsForDisplay = portData.map(item => ({
            title: item.value,
            value: item.value,
            key: item.value,
            disabled: allOccupiedPorts.has(item.value)
        }));

        console.log("allPortsForDisplay", allPortsForDisplay);

        // 检查是否有任意端口被占用
        const hasAnyPortOccupied = portData.some(item => allOccupiedPorts.has(item.value));

        const tree = [
            {
                title: "All Ports",
                value: JSON.stringify(portData.map(item => item.value)),
                disabled: hasAnyPortOccupied,
                children: allPortsForDisplay
            }
        ];

        setPortTreeData(tree);
    };

    // 简化 fetchPortsBySn
    const fetchPortsBySn = async (sn, forceRefresh = false) => {
        try {
            const res = await getFilterSwitchPorts({
                switch_sn: sn,
                query_model: "PfcConfiguration"
            });

            if (res && res.status === 200 && Array.isArray(res.data)) {
                const portData = res.data;
                setPortDataCache(prev => new Map(prev).set(sn, portData));
                initPortTreeData(portData);
            } else {
                message.error(res.msg);
            }
        } catch (error) {
            console.error("Failed to fetch filtered ports:", error);
            message.error("Failed to fetch ports");
            setPortTreeData([]);
        }
    };

    const handleCreate = () => {
        setIsModalVisible(true);
        setEditRecord(null);
        form.resetFields();
        setPortTreeData([]);

        // 在创建时获取 fabric 数据
        fetchFabricTreeData();

        form.setFieldsValue({
            configurations: [
                {
                    sysname: null,
                    pfc_profile: "",
                    ports: [],
                    queues: [],
                    pfc_enabled: null
                }
            ]
        });
    };

    // 编辑
    const handleEdit = async record => {
        try {
            setIsModalVisible(true);
            setEditRecord(record);

            // 在编辑时获取 fabric 数据
            fetchFabricTreeData();
            fetchPortsBySn(record.switch_sn, true);

            // 根据 switch_sn 获取该交换机下的所有PFC配置
            const result = await getPfcConfigDetailBySwitch({
                switch_sn: record.switch_sn
            });

            if (result.status === 200 && result.data) {
                // 将获取到的配置数据转换为表单需要的格式
                const configurations = result.data.map(config => {
                    const sysnameValue = JSON.stringify({sn: config.switch_sn, sysname: config.sysname});

                    return {
                        config_id: config.id,
                        sysname: sysnameValue,
                        pfc_profile: config.profile_name,
                        ports: config.is_all_ports ? [JSON.stringify(config.port)] : config.port,
                        queues: config.is_all_queues ? [JSON.stringify(config.queue)] : config.queue,
                        pfc_enabled: config.enabled ? "True" : "False",
                        is_all_ports: config.is_all_ports,
                        is_all_queues: config.is_all_queues
                    };
                });

                form.setFieldsValue({
                    configurations
                });

                // 初始化端口占用记录
                const newConfigPortOccupancy = new Map();
                configurations.forEach((config, index) => {
                    try {
                        const sysObj = JSON.parse(config.sysname);
                        const configKey = `${sysObj.sn}-${index}`;
                        let ports = [];

                        if (config.ports && Array.isArray(config.ports)) {
                            if (config.is_all_ports) {
                                ports = JSON.parse(config.ports[0]);
                            } else {
                                ports = config.ports;
                            }
                        }
                        console.log("configKey", configKey, ports);
                        newConfigPortOccupancy.set(configKey, ports);
                    } catch (e) {
                        console.error("Error initializing port occupancy:", e);
                    }
                });
                // setConfigPortOccupancy(newConfigPortOccupancy);
            } else {
                // 如果接口调用失败，回退到使用原始record数据
                const sysnameValue = JSON.stringify({sn: record.switch_sn, sysname: record.sysname});
                form.setFieldsValue({
                    configurations: [
                        {
                            config_id: record.id,
                            sysname: sysnameValue,
                            pfc_profile: record.profile_name,
                            ports: record.is_all_ports ? [JSON.stringify(record.port)] : record.port,
                            queues: record.is_all_queues ? [JSON.stringify(record.queue)] : record.queue,
                            pfc_enabled: record.enabled ? "True" : "False",
                            is_all_ports: record.is_all_ports,
                            is_all_queues: record.is_all_queues
                        }
                    ]
                });

                message.warning("Failed to load switch configurations, using current record data");
            }
        } catch (error) {
            console.error("Failed to load PFC configurations:", error);

            // 出错时回退到使用原始record数据
            const sysnameValue = JSON.stringify({sn: record.switch_sn, sysname: record.sysname});
            form.setFieldsValue({
                configurations: [
                    {
                        config_id: record.id,
                        sysname: sysnameValue,
                        pfc_profile: record.profile_name,
                        ports: record.is_all_ports ? [JSON.stringify(record.port)] : record.port,
                        queues: record.is_all_queues ? [JSON.stringify(record.queue)] : record.queue,
                        pfc_enabled: record.enabled ? "True" : "False",
                        is_all_ports: record.is_all_ports,
                        is_all_queues: record.is_all_queues
                    }
                ]
            });

            // 初始化端口占用记录（异常模式）
            const newConfigPortOccupancy = new Map();
            const configKey = `${record.switch_sn}-0`;
            let ports = [];
            if (record.is_all_ports) {
                ports = record.port;
            } else {
                ports = record.port;
            }

            message.error("Failed to load configurations");
        }
    };

    // 取消
    const handleModalCancel = () => {
        setIsModalVisible(false);
        form.resetFields();
        setPortTreeData([]);
    };

    // 修改提交函数，在提交时计算 is_all_ports 和 is_all_queues
    const handleSubmit = async values => {
        try {
            const configurationsArray = Array.isArray(values.configurations) ? values.configurations : [];

            const configs = configurationsArray.map(config => {
                let sysObj = {};
                try {
                    sysObj = JSON.parse(config.sysname);
                } catch (e) {
                    console.error("Failed to parse sysname:", e);
                    return;
                }

                // 获取对应交换机的端口数据来判断是否是全选
                const portData = portDataCache.get(sysObj.sn);
                const allPortsValue = portData ? JSON.stringify(portData.map(item => item.port_name)) : null;
                const allQueuesValue = JSON.stringify(queueLists);

                // 计算 is_all_ports
                const is_all_ports = config.ports && config.ports.length === 1 && config.ports[0] === allPortsValue;

                // 计算 is_all_queues
                const is_all_queues =
                    config.queues && config.queues.length === 1 && config.queues[0] === allQueuesValue;

                let ports = [];
                let queues = [];

                if (is_all_ports) {
                    ports = JSON.parse(config.ports[0]);
                } else {
                    ports = config.ports;
                }

                if (is_all_queues) {
                    queues = JSON.parse(config.queues[0]);
                } else {
                    queues = config.queues.length === 0 ? queueLists : config.queues;
                }

                return {
                    sysname: sysObj.sysname,
                    switch_sn: sysObj.sn,
                    port: ports,
                    queue: queues,
                    profile_name: config.pfc_profile,
                    enabled: config.pfc_enabled === "True",
                    is_all_ports,
                    is_all_queues: queues.length === queueLists.length ? true : is_all_queues,
                    ...(config.config_id ? {config_id: config.config_id} : {})
                };
            });

            const filteredConfigs = configs.filter(Boolean);
            let ret;

            if (editRecord) {
                // 编辑模式：使用 update 接口
                const params = {
                    switch_sn: editRecord.switch_sn,
                    configurations: filteredConfigs
                };
                console.log("Update params:", params);
                ret = await updatePfcConfig(params);
            } else {
                // 创建模式：使用 save 接口
                const params = {configurations: filteredConfigs};
                console.log("Create params:", params);
                ret = await savePfcConfig(params);
            }

            if (ret.status === 200) {
                form.resetFields();
                setIsModalVisible(false);
                setEditRecord(null);
                message.success(ret.msg);
                tableRef.current.refreshTable();
            } else {
                message.error(ret.msg);
            }
        } catch (e) {
            console.error("Submit failed:", e);
            message.error("Submit failed");
        }
    };

    // 删除
    const handleDeleteConfirm = async record => {
        try {
            const ret = await deletePfcConfig(record.id);
            if (ret.status === 200) {
                message.success(ret.msg);
            } else {
                message.error(ret.msg);
            }
            tableRef.current.refreshTable();
        } catch (e) {
            message.error("Delete failed");
        }
    };

    // 重新调整 formItems 函数，接受必要的参数
    const formItems = () => {
        return (
            <Form.List name="configurations">
                {(fields, {add, remove}) => (
                    <>
                        {fields.map(({key, name}, index) => (
                            <Row key={key} gutter={16}>
                                {/* Sysname */}
                                <Col span={4} style={{marginRight: 24}}>
                                    <Form.Item
                                        name={[name, "sysname"]}
                                        label={
                                            index === 0 ? (
                                                <>
                                                    Sysname <span className={style.requiredIcon1}>*</span>
                                                </>
                                            ) : (
                                                ""
                                            )
                                        }
                                        labelCol={{span: 24}}
                                        wrapperCol={{span: 24}}
                                        rules={[{required: true, message: "Please select sysname!"}]}
                                    >
                                        <TreeSelect
                                            disabled={editRecord === null ? false : true}
                                            treeData={fabricTreeData}
                                            placeholder="Sysname"
                                            dropdownStyle={{
                                                maxHeight: 200,
                                                overflow: "auto"
                                            }}
                                            treeDefaultExpandAll
                                            maxTagCount={1}
                                            maxTagTextLength={10}
                                            maxTagPlaceholder={omittedValues => `+${omittedValues.length} more`}
                                            onChange={value => {
                                                if (value !== null) {
                                                    const obj = JSON.parse(value);
                                                    fetchPortsBySn(obj.sn, true);
                                                }

                                                form.setFields([
                                                    {
                                                        name: ["configurations", name, "ports"],
                                                        value: []
                                                    }
                                                ]);
                                            }}
                                            showSearch={false}
                                            allowClear
                                        />
                                    </Form.Item>
                                </Col>

                                {/* PFC Profile */}
                                <Col span={4} style={{marginRight: 24}}>
                                    <Form.Item
                                        name={[name, "pfc_profile"]}
                                        label={
                                            index === 0 ? (
                                                <>
                                                    PFC Profile <span className={style.requiredIcon1}>*</span>
                                                </>
                                            ) : (
                                                ""
                                            )
                                        }
                                        labelCol={{
                                            span: 24,
                                            style: {
                                                whiteSpace: "nowrap",
                                                overflow: "hidden",
                                                textOverflow: "ellipsis"
                                            }
                                        }}
                                        wrapperCol={{span: 24}}
                                        validateTrigger="onBlur"
                                        rules={[
                                            {required: true, message: "Please input PFC Profile!"},
                                            {
                                                max: 30,
                                                message: "PFC Profile cannot exceed 30 characters"
                                            },
                                            {
                                                validator: async (_, value) => {
                                                    if (!value || value.length === 0) {
                                                        return Promise.resolve();
                                                    }

                                                    // 获取当前表单的值
                                                    const formValues = form.getFieldsValue();
                                                    const currentConfig = formValues.configurations[name];

                                                    // 如果sysname为空，不调用接口校验
                                                    if (!currentConfig || !currentConfig.sysname) {
                                                        return Promise.resolve();
                                                    }

                                                    try {
                                                        const sysObj = JSON.parse(currentConfig.sysname);

                                                        // 构造校验请求数据
                                                        const validateData = {
                                                            configurations: [
                                                                {
                                                                    config_id: currentConfig.config_id || "",
                                                                    sysname: sysObj.sysname,
                                                                    switch_sn: sysObj.sn,
                                                                    profile_name: value
                                                                }
                                                            ]
                                                        };

                                                        const result = await validatePfcConfig(validateData);

                                                        if (result.status !== 200) {
                                                            return Promise.reject(new Error(result.msg));
                                                        }

                                                        return Promise.resolve();
                                                    } catch (error) {
                                                        // 如果是解析错误，忽略校验
                                                        if (
                                                            error.message &&
                                                            (error.message.includes("JSON") ||
                                                                error.message.includes("parse"))
                                                        ) {
                                                            return Promise.resolve();
                                                        }
                                                        // 其他错误，显示错误信息
                                                        return Promise.reject(
                                                            new Error(
                                                                error.msg ||
                                                                    error.message ||
                                                                    "Profile name validation failed"
                                                            )
                                                        );
                                                    }
                                                }
                                            }
                                        ]}
                                    >
                                        <Input placeholder="PFC Profile" maxLength={30} />
                                    </Form.Item>
                                </Col>

                                {/* Ports */}
                                <Col span={4} style={{marginRight: 24}}>
                                    <Form.Item
                                        name={[name, "ports"]}
                                        label={
                                            index === 0 ? (
                                                <>
                                                    Ports <span className={style.requiredIcon1}>*</span>
                                                </>
                                            ) : (
                                                ""
                                            )
                                        }
                                        labelCol={{span: 24}}
                                        wrapperCol={{span: 24}}
                                        rules={[{required: true, message: "Please select port!"}]}
                                    >
                                        <CustomTreeSelect
                                            treeData={portTreeData}
                                            placeholder="Ports"
                                            onFocus={() => {
                                                // 直接获取当前字段的值
                                                const currentPortValue = form.getFieldValue([
                                                    "configurations",
                                                    name,
                                                    "ports"
                                                ]);
                                                const currentSysname = form.getFieldValue([
                                                    "configurations",
                                                    name,
                                                    "sysname"
                                                ]);
                                                // console.log("currentPortValue", currentPortValue);
                                                if (currentSysname && currentPortValue) {
                                                    try {
                                                        const sysObj = JSON.parse(currentSysname);
                                                        if (sysObj.sn) {
                                                            const portData = portDataCache.get(sysObj.sn);
                                                            if (portData) {
                                                                // 解析端口值
                                                                let portsToEnable = [];
                                                                if (
                                                                    Array.isArray(currentPortValue) &&
                                                                    currentPortValue.length > 0
                                                                ) {
                                                                    try {
                                                                        const allPortsValue = JSON.stringify(
                                                                            portData.map(item => item.port_name)
                                                                        );
                                                                        if (currentPortValue[0] === allPortsValue) {
                                                                            portsToEnable = JSON.parse(
                                                                                currentPortValue[0]
                                                                            );
                                                                        } else {
                                                                            portsToEnable = currentPortValue;
                                                                        }
                                                                    } catch (e) {
                                                                        portsToEnable = currentPortValue;
                                                                    }
                                                                }
                                                                console.log("portTreeData", portTreeData);
                                                                // 直接调用 calculatePortTreeData 释放端口
                                                                calculatePortTreeData(
                                                                    sysObj.sn,
                                                                    portTreeData,
                                                                    "enable",
                                                                    portsToEnable,
                                                                    name
                                                                );
                                                            }
                                                        }
                                                    } catch (e) {
                                                        console.error("Error parsing sysname:", e);
                                                    }
                                                }
                                            }}
                                            onBlur={() => {
                                                const currentSysname = form.getFieldValue([
                                                    "configurations",
                                                    name,
                                                    "sysname"
                                                ]);
                                                const sysObj = JSON.parse(currentSysname);
                                                calculatePortTreeData(
                                                    sysObj.sn,
                                                    portTreeData,
                                                    "disable",
                                                    editPortData,
                                                    name
                                                );
                                            }}
                                            onChange={(value, label) => {
                                                console.log("value", value);
                                                setEditPortData(value);
                                            }}
                                        />
                                    </Form.Item>
                                </Col>

                                {/* Queues */}
                                <Col span={4} style={{marginRight: 24}}>
                                    <Form.Item
                                        name={[name, "queues"]}
                                        label={index === 0 ? "Queues" : ""}
                                        labelCol={{span: 24}}
                                        wrapperCol={{span: 24}}
                                        // rules={[{required: true, message: "Please select queue!"}]}
                                    >
                                        <CustomTreeSelect
                                            treeData={[
                                                {
                                                    title: "All Queues",
                                                    value: JSON.stringify(queueLists),
                                                    children: queueLists.map(queue => ({
                                                        title: queue.toString(),
                                                        value: queue.toString(),
                                                        key: queue.toString()
                                                    }))
                                                }
                                            ]}
                                            placeholder="Queues"
                                            onChange={(value, label) => {
                                                form.setFieldsValue({
                                                    configurations: {
                                                        [name]: {
                                                            queues: value
                                                        }
                                                    }
                                                });
                                            }}
                                        />
                                    </Form.Item>
                                </Col>

                                {/* PFC Enabled */}
                                <Col span={4}>
                                    <Form.Item
                                        name={[name, "pfc_enabled"]}
                                        label={
                                            index === 0 ? (
                                                <>
                                                    PFC Enabled <span className={style.requiredIcon1}>*</span>
                                                </>
                                            ) : (
                                                ""
                                            )
                                        }
                                        labelCol={{span: 24}}
                                        wrapperCol={{span: 24}}
                                        rules={[{required: true, message: "Please select PFC status!"}]}
                                    >
                                        <Select
                                            placeholder="PFC Enabled"
                                            dropdownStyle={{
                                                maxHeight: 200,
                                                overflow: "auto"
                                            }}
                                        >
                                            {pfcEnabledOptions.map(option => (
                                                <Option key={option} value={option}>
                                                    {option}
                                                </Option>
                                            ))}
                                        </Select>
                                    </Form.Item>
                                </Col>

                                {/* 添加按钮列 */}
                                <Col>
                                    {index === 0 ? (
                                        <Button
                                            onClick={() => {
                                                if (editRecord === null) {
                                                    add({
                                                        sysname: null,
                                                        pfc_profile: "",
                                                        ports: [],
                                                        queues: [],
                                                        pfc_enabled: null
                                                    });
                                                } else {
                                                    const currentSysname = form.getFieldValue([
                                                        "configurations",
                                                        name,
                                                        "sysname"
                                                    ]);
                                                    add({
                                                        sysname: currentSysname,
                                                        pfc_profile: "",
                                                        ports: [],
                                                        queues: [],
                                                        pfc_enabled: null
                                                    });
                                                }
                                            }}
                                            style={{
                                                backgroundColor: "transparent",
                                                color: "#BFBFBF",
                                                marginBottom: "24px",
                                                marginTop: index === 0 ? "40px" : "0"
                                            }}
                                            type="link"
                                            icon={<PlusOutlined />}
                                        />
                                    ) : (
                                        <Button
                                            style={{
                                                backgroundColor: "transparent",
                                                color: "#BFBFBF",
                                                marginBottom: "24px",
                                                marginTop: index === 0 ? "40px" : "0"
                                            }}
                                            type="link"
                                            icon={<MinusOutlined />}
                                            onClick={() => {
                                                remove(name);
                                            }}
                                        />
                                    )}
                                </Col>

                                {/* 隐藏的表单项 */}
                                <Form.Item name={[name, "config_id"]} hidden>
                                    <Input type="hidden" />
                                </Form.Item>
                            </Row>
                        ))}
                    </>
                )}
            </Form.List>
        );
    };

    // 表格列
    const renderArrayColumn = (text, maxItems = 2) => {
        try {
            const arr = Array.isArray(text) ? text : JSON.parse(text);
            if (!Array.isArray(arr)) return text;

            if (arr.length <= maxItems) {
                return (
                    <div className="force-wrap-text" title={arr.join(", ")}>
                        {arr.join(", ")}
                    </div>
                );
            }

            const displayItems = arr.slice(0, maxItems);
            const remainingCount = arr.length - maxItems;
            const displayText = `${displayItems.join(", ")} +${remainingCount} more`;
            return (
                <div className="force-wrap-text" title={arr.join(", ")}>
                    {displayText}
                </div>
            );
        } catch {
            return (
                <div className="force-wrap-text" title={text}>
                    {text}
                </div>
            );
        }
    };

    const pfcColumns = [
        {
            title: "",
            key: "expand",
            width: 50,
            render: (_, record) => {}
        },
        {
            ...createColumnConfig("Sysname", "sysname", TableFilterDropdown),
            width: 150,
            ellipsis: true
        },
        {
            ...createColumnConfig("PFC Profile", "profile_name"),
            width: 150,
            ellipsis: true
        },
        {
            ...createColumnConfig("Port", "port", TableFilterDropdown),
            width: 200,
            render: (_, record) => {
                if (record.is_all_ports) {
                    return "All Ports";
                }
                return renderArrayColumn(record.port);
            }
        },
        {
            ...createColumnConfig("Queue", "queue"),
            width: 150,
            render: (_, record) => {
                if (record.is_all_queues) {
                    return "All Queues";
                }
                return renderArrayColumn(record.queue);
            }
        },
        {
            ...createColumnConfig("Enable", "enabled"),
            width: 100,
            render: (_, record) => {
                return <div>{record.enabled ? "True" : "False"}</div>;
            }
        },
        {
            title: "Operation",
            key: "operation",
            render: (_, record) => (
                <Space size="middle">
                    <Button type="link" onClick={() => handleEdit(record)}>
                        Edit
                    </Button>
                    <Button
                        type="link"
                        onClick={() =>
                            confirmModalAction("Are you sure you want to delete the configuration items?", () =>
                                handleDeleteConfirm(record)
                            )
                        }
                    >
                        Delete
                    </Button>
                </Space>
            )
        }
    ];

    return (
        <div>
            <div style={{marginBottom: 30}}>
                <h3>
                    PFC Configuration
                    <CustomQuestionIcon
                        title="Configure Priority Flow Control (PFC) tomanage congestion on Ethernet networks.PFC helps maintain performance andreliability for critical applications."
                        placement="right"
                    />
                </h3>
                <div style={{marginBottom: 16}}>
                    <Button type="primary" icon={<PlusOutlined />} onClick={handleCreate}>
                        Configuration
                    </Button>
                </div>
                <ExtendedTable columns={pfcColumns} fetchAPIInfo={getPfcConfigList} ref={tableRef} bordered={false} />
            </div>
            <AmpConCustomModalForm
                title={editRecord ? "Edit Configuration" : "Create PFC Configuration"}
                isModalOpen={isModalVisible}
                formInstance={form}
                layoutProps={{
                    labelCol: {
                        span: 4
                    }
                }}
                CustomFormItems={formItems}
                onCancel={handleModalCancel}
                onSubmit={handleSubmit}
                modalClass="ampcon-max-modal"
                loading
                // confirmLoading={
                footer={[
                    <Divider style={{marginTop: 0, marginBottom: 20}} />,
                    <Button key="cancel" onClick={handleModalCancel}>
                        Cancel
                    </Button>,
                    <Button key="ok" type="primary" onClick={form.submit}>
                        Apply
                    </Button>
                ]}
            />
        </div>
    );
};

export default PFCConfiguration;
