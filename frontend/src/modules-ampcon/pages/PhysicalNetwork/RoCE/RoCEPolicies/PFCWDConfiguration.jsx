import React, {useState, useEffect, useRef} from "react";
import {
    <PERSON>ton,
    Tooltip,
    Form,
    Space,
    Row,
    Col,
    Select,
    Input,
    TreeSelect,
    Modal,
    Divider,
    message,
    InputNumber
} from "antd";
import {QuestionCircleFilled, PlusOutlined, MinusOutlined} from "@ant-design/icons";
import {
    AmpConCustomTable,
    createColumnConfig,
    TableFilterDropdown,
    AmpConCustomModalForm
} from "@/modules-ampcon/components/custom_table";
import {
    getPfcWdConfigList,
    getFabricSwitches,
    getSwitchPorts,
    savePfcWdConfig,
    updatePfcWdConfig,
    deletePfcWdConfig,
    getFilterSwitchPorts,
    getPfcWdConfigDetailBySwitch,
    getFilterSwitchQueues
} from "@/modules-ampcon/apis/roce_api";
import {confirmModalAction} from "@/modules-ampcon/components/custom_modal";
import CustomQuestionIcon from "./QuestionIcon";
import CustomTreeSelect from "./customTreeSelect";
import {ExtendedTable} from "./ext_table";
import style from "./roce_policies.module.scss";

const PFCWDConfiguration = () => {
    const [isWatchdogModalVisible, setIsWatchdogModalVisible] = useState(false);
    const [watchdogForm] = Form.useForm();
    const [fabricTreeData, setFabricTreeData] = useState([]);
    const [portTreeData, setPortTreeData] = useState([]);
    const [portDataCache, setPortDataCache] = useState(new Map());
    const [isEditMode, setIsEditMode] = useState(false);
    const [currentSwitchSn, setCurrentSwitchSn] = useState(null);
    // 添加editPortData状态，参考PFCConfiguration
    const [editPortData, setEditPortData] = useState([]);

    const tableRef = useRef(null);

    const queueLists = ["0", "1", "2", "3", "4", "5", "6", "7"];
    const pfcEnabledOptions = ["True", "False"];

    const fetchFabricTreeData = async () => {
        try {
            const res = await getFabricSwitches();
            if (res && res.data) {
                const fabricKeys = Object.keys(res.data); // ["default"]
                const fabricValues = Object.values(res.data); // [switch array]

                const tree = fabricKeys.map((fabricName, index) => {
                    const switches = fabricValues[index];

                    // Filter out switches with enabled=false
                    const enabledSwitches = switches.filter(sw => sw.enabled === true);

                    return {
                        title: fabricName,
                        value: fabricName,
                        key: fabricName,
                        disabled: true,
                        children: enabledSwitches.map(sw => ({
                            title: sw.sysname || "Unknown",
                            value: JSON.stringify({sn: sw.switch_sn, sysname: sw.sysname}),
                            key: JSON.stringify({sn: sw.switch_sn, sysname: sw.sysname})
                        }))
                    };
                });

                setFabricTreeData(tree);
            }
        } catch (e) {
            message.error("Failed to get fabric switches");
        }
    };

    // 添加初始化端口树数据函数，参考PFCConfiguration
    const initPortTreeData = portData => {
        const allOccupiedPorts = new Set();

        portData.forEach(item => {
            if (!item.enabled) {
                allOccupiedPorts.add(item.port_name);
            }
        });

        const allPortsForDisplay = portData.map(item => ({
            title: item.port_name,
            value: item.port_name,
            key: item.port_name,
            disabled: allOccupiedPorts.has(item.port_name)
        }));

        const hasAnyPortOccupied = portData.some(item => allOccupiedPorts.has(item.port_name));

        const tree = [
            {
                title: "All Ports",
                value: JSON.stringify(portData.map(item => item.port_name)),
                disabled: hasAnyPortOccupied,
                children: allPortsForDisplay
            }
        ];

        setPortTreeData(tree);
    };

    // 修改calculatePortTreeData函数，参考PFCConfiguration
    const calculatePortTreeData = (sn, portTreeData, action = null, currentPorts = [], index = null) => {
        const allOccupiedPorts = new Set();
        const currentFormValues = watchdogForm.getFieldsValue();

        const portData = portTreeData[0].children;

        portData.forEach(item => {
            if (item.disabled) {
                allOccupiedPorts.add(item.value);
            }
        });

        // 收集表单中所有配置项占用的端口
        if (currentFormValues.configurations) {
            currentFormValues.configurations.forEach((config, configIndex) => {
                if (config.sysname) {
                    try {
                        const sysObj = JSON.parse(config.sysname);
                        if (sysObj.sn === sn && config.port) {
                            let selectedPorts = [];
                            if (config.port && Array.isArray(config.port) && config.port.length > 0) {
                                try {
                                    const allPortsValue = JSON.stringify(portData.map(item => item.value));
                                    if (config.port[0] === allPortsValue) {
                                        selectedPorts = JSON.parse(config.port[0]);
                                    } else {
                                        selectedPorts = config.port;
                                    }
                                } catch (e) {
                                    selectedPorts = config.port;
                                }
                            }
                            selectedPorts.forEach(port => allOccupiedPorts.add(port));
                        }
                    } catch (e) {
                        console.error("Error parsing sysname or ports:", e);
                    }
                }
            });
        }

        // 根据action处理指定的端口
        if (action === "enable") {
            currentPorts.forEach(port => allOccupiedPorts.delete(port));
        } else if (action === "disable") {
            currentPorts.forEach(port => allOccupiedPorts.add(port));
        }

        // 计算最终的端口可用性
        const allPortsForDisplay = portData.map(item => ({
            title: item.value,
            value: item.value,
            key: item.value,
            disabled: allOccupiedPorts.has(item.value)
        }));

        // 检查是否有任意端口被占用
        const hasAnyPortOccupied = portData.some(item => allOccupiedPorts.has(item.value));

        const tree = [
            {
                title: "All Ports",
                value: JSON.stringify(portData.map(item => item.value)),
                disabled: hasAnyPortOccupied,
                children: allPortsForDisplay
            }
        ];

        setPortTreeData(tree);
    };

    const fetchPortsBySn = async (sn, forceRefresh = false) => {
        try {
            // 检查缓存
            let portData = portDataCache.get(sn);

            // 如果缓存中没有数据或者强制刷新，则调用接口
            if (!portData || forceRefresh) {
                const res = await getFilterSwitchPorts({
                    switch_sn: sn,
                    query_model: "PfcWdConfiguration"
                });

                if (res && Array.isArray(res.data)) {
                    portData = res.data;
                    // 更新缓存
                    setPortDataCache(prev => new Map(prev).set(sn, portData));
                } else {
                    setPortTreeData([]);
                    return;
                }
            }

            // 使用新的初始化函数
            initPortTreeData(portData);
        } catch (error) {
            console.error("Failed to fetch filtered ports:", error);
            message.error("Failed to fetch ports");
            setPortTreeData([]);
        }
    };

    const [queueDataCache, setQueueDataCache] = useState(new Map());
    const [queueTreeData, setQueueTreeData] = useState([]);
    const calculateQueueTreeData = (sn, queueData, action = null, queues = []) => {
        const allOccupiedQueues = new Set();
        queueData.forEach(item => {
            if (!item.enabled) {
                allOccupiedQueues.add(item.queue_name);
            }
        });

        const currentFormValues = watchdogForm.getFieldsValue();
        if (currentFormValues.configurations) {
            currentFormValues.configurations.forEach(config => {
                if (config.sysname) {
                    try {
                        const sysObj = JSON.parse(config.sysname);
                        if (sysObj.sn === sn && config.queue) {
                            let selectedQueues = [];
                            if (config.is_all_queues && Array.isArray(config.queue) && config.queue.length > 0) {
                                selectedQueues = JSON.parse(config.queue[0]);
                            } else if (Array.isArray(config.queue)) {
                                selectedQueues = config.queue;
                            }
                            selectedQueues.forEach(queue => allOccupiedQueues.add(queue));
                        }
                    } catch (e) {
                        console.error("Error parsing sysname or queues:", e);
                    }
                }
            });
        }
        if (action === "enable") {
            queues.forEach(queue => allOccupiedQueues.delete(queue));
        } else if (action === "disable") {
            queues.forEach(queue => allOccupiedQueues.add(queue));
        }
        const allQueuesForDisplay = queueData.map(item => ({
            title: item.queue_name,
            value: item.queue_name,
            key: item.queue_name,
            disabled: allOccupiedQueues.has(item.queue_name)
        }));

        const hasAnyQueueOccupied = queueData.some(item => allOccupiedQueues.has(item.queue_name));
        const tree = [
            {
                title: "All Queues",
                value: JSON.stringify(queueData.map(item => item.queue_name)),
                disabled: hasAnyQueueOccupied,
                children: allQueuesForDisplay
            }
        ];
        setQueueTreeData(tree);
    };

    const fetchQueuesBySn = async (sn, forceRefresh = false) => {
        try {
            // 检查缓存
            let queueData = queueDataCache.get(sn);

            // 如果缓存中没有数据或者强制刷新，则调用接口
            if (!queueData || forceRefresh) {
                const res = await getFilterSwitchQueues({
                    switch_sn: sn,
                    query_model: "PfcWdConfiguration"
                });

                if (res && Array.isArray(res.data)) {
                    queueData = res.data.map(item => ({
                        queue_name: item.queue_name,
                        enabled: item.enabled
                    }));
                    // 更新缓存
                    setQueueDataCache(prev => new Map(prev).set(sn, queueData));
                } else {
                    setQueueTreeData([]);
                    return;
                }
            }

            // 初始化队列状态
            calculateQueueTreeData(sn, queueData);
        } catch (error) {
            console.error("Failed to fetch PFC WD queues:", error);
            message.error("Failed to fetch queues");
            setQueueTreeData([]);
        }
    };

    // 修改编辑处理函数 - 根据switch_sn加载数据
    const handleWatchdogEdit = async record => {
        try {
            setIsEditMode(true);
            setCurrentSwitchSn(record.switch_sn);
            setIsWatchdogModalVisible(true);

            fetchFabricTreeData();
            fetchPortsBySn(record.switch_sn, true);
            fetchQueuesBySn(record.switch_sn, true);

            // 根据 switch_sn 查询该交换机下所有的配置
            const response = await getPfcWdConfigDetailBySwitch({
                switch_sn: record.switch_sn
            });

            if (response.status === 200 && response.data) {
                const configs = response.data;

                // 将配置数据转换为表单格式
                const formConfigurations = configs.map(config => {
                    const sysnameValue = JSON.stringify({sn: config.switch_sn, sysname: config.sysname});

                    return {
                        config_id: config.id,
                        sysname: sysnameValue,
                        port: config.is_all_ports ? [JSON.stringify(config.port)] : config.port,
                        queue: config.is_all_queues ? [JSON.stringify(config.queue)] : config.queue,
                        enable: config.enabled ? "True" : "False",
                        granularity: config.granularity,
                        restore_mode: config.restore_mode,
                        restore_action: config.restore_action,
                        detection_interval: config.detection_interval,
                        restore_interval: config.restore_interval,
                        threshold_period: config.threshold_period,
                        threshold_count: config.threshold_count,
                        is_all_ports: config.is_all_ports,
                        is_all_queues: config.is_all_queues
                    };
                });

                watchdogForm.setFieldsValue({
                    configurations: formConfigurations
                });
            } else {
                message.error("Failed to load PFC WD configurations");
            }
        } catch (error) {
            console.error("Error loading PFC WD configurations:", error);
            message.error("Failed to load configurations");
        }
    };

    // 简化取消处理函数
    const handleWatchdogModalCancel = () => {
        setIsWatchdogModalVisible(false);
        setIsEditMode(false);
        setCurrentSwitchSn(null);
        watchdogForm.resetFields();
        setPortTreeData([]);
        setQueueTreeData([]);
        setEditPortData([]);
    };

    // 简化创建按钮的处理函数
    const handleWatchdogCreate = () => {
        setIsEditMode(false);
        setCurrentSwitchSn(null);
        setIsWatchdogModalVisible(true);
        watchdogForm.resetFields();
        setPortTreeData([]);
        setQueueTreeData([]);
        setEditPortData([]);

        fetchFabricTreeData();

        watchdogForm.setFieldsValue({
            configurations: [
                {
                    sysname: null,
                    port: [],
                    queue: [],
                    enable: null,
                    granularity: null,
                    restore_mode: null,
                    restore_action: null,
                    detection_interval: null,
                    restore_interval: null,
                    threshold_period: null,
                    threshold_count: null,
                    is_all_ports: false,
                    is_all_queues: false
                }
            ]
        });
    };

    // 保存
    const handleWatchdogSubmit = async values => {
        try {
            const configurationsArray = Array.isArray(values.configurations) ? values.configurations : [];

            const configs = configurationsArray.map(config => {
                let sysObj = {};
                try {
                    sysObj = JSON.parse(config.sysname);
                } catch (e) {
                    console.error("Failed to parse sysname:", e);
                    return;
                }

                // 获取对应交换机的端口数据来判断是否是全选
                const portData = portDataCache.get(sysObj.sn);
                const allPortsValue = portData ? JSON.stringify(portData.map(item => item.port_name)) : null;
                const allQueuesValue = JSON.stringify(queueLists);

                // 计算 is_all_ports - 严格匹配全选逻辑
                // 只有当选择数组长度为1且第一个元素是所有端口的JSON字符串时才认为是全选
                // 额外检查：确保不是单个端口被误判为全选
                let is_all_ports = false;
                if (config.port && config.port.length === 1 && allPortsValue !== null) {
                    const selectedValue = config.port[0];
                    // 检查是否是JSON字符串格式（全选时的格式）
                    try {
                        const parsedValue = JSON.parse(selectedValue);
                        // 如果能解析为数组，且与所有端口匹配，才认为是全选
                        if (Array.isArray(parsedValue) && selectedValue === allPortsValue) {
                            is_all_ports = true;
                        }
                    } catch (e) {
                        // 如果不能解析为JSON，说明是单个端口选择，不是全选
                        is_all_ports = false;
                    }
                }

                // 计算 is_all_queues - 使用相同的安全检查逻辑
                let is_all_queues = false;
                if (config.queue && config.queue.length === 1) {
                    const selectedValue = config.queue[0];
                    try {
                        const parsedValue = JSON.parse(selectedValue);
                        // 如果能解析为数组，且与所有队列匹配，才认为是全选
                        if (Array.isArray(parsedValue) && selectedValue === allQueuesValue) {
                            is_all_queues = true;
                        }
                    } catch (e) {
                        // 如果不能解析为JSON，说明是单个队列选择，不是全选
                        is_all_queues = false;
                    }
                }

                let ports = [];
                let queues = [];

                if (is_all_ports) {
                    ports = JSON.parse(config.port[0]);
                } else {
                    ports = config.port;
                }

                if (is_all_queues) {
                    queues = JSON.parse(config.queue[0]);
                } else {
                    queues = config.queue;
                }

                const configData = {
                    sysname: sysObj.sysname,
                    switch_sn: sysObj.sn,
                    port: ports,
                    queue: queues,
                    enabled: config.enable === "True",
                    granularity: config.granularity,
                    restore_mode: config.restore_mode,
                    restore_action: config.restore_action,
                    detection_interval: config.detection_interval,
                    restore_interval: config.restore_interval,
                    threshold_period: config.threshold_period,
                    threshold_count: config.threshold_count,
                    is_all_ports,
                    is_all_queues
                };

                // 如果是编辑模式，添加config_id
                if (config.config_id) {
                    configData.config_id = config.config_id;
                }

                return configData;
            });

            const params = {configurations: configs.filter(Boolean)};

            // 编辑模式下需要传递 switch_sn 参数
            if (isEditMode && currentSwitchSn) {
                params.switch_sn = currentSwitchSn;
            }

            // 根据编辑模式选择不同的API函数
            const ret = isEditMode ? await updatePfcWdConfig(params) : await savePfcWdConfig(params);

            if (ret.status === 200) {
                message.success(ret.msg);
                setIsWatchdogModalVisible(false);
                setIsEditMode(false);
                setCurrentSwitchSn(null);
                watchdogForm.resetFields();
                setPortTreeData([]);
                setQueueTreeData([]);
                setEditPortData([]);
                tableRef.current.refreshTable();
            } else {
                message.error(ret.msg);
            }
        } catch (error) {
            console.error("Error saving PFC WD configuration:", error);
            message.error("保存失败");
        }
    };

    // 删除确认
    const handleDelete = record => {
        confirmModalAction("Are you sure you want to delete this configuration?", async () => {
            try {
                const ret = await deletePfcWdConfig({config_id: record.id});
                if (ret.status === 200) {
                    message.success(ret.msg);
                } else {
                    message.error(ret.msg);
                }
                tableRef.current.refreshTable();
            } catch (error) {
                console.error("Error deleting PFC WD configuration:", error);
                message.error("Delete Failed");
            }
        });
    };

    // 表单项
    const watchdogFormItems = () => (
        <Form.List name="configurations">
            {(fields, {add, remove}) => (
                <>
                    {fields.map(({key, name}, index) => (
                        <div key={key}>
                            {index !== 0 && <Divider />}
                            <Row gutter={8}>
                                <Col span="3">
                                    <Form.Item
                                        name={[name, "sysname"]}
                                        label={
                                            index === 0 ? (
                                                <>
                                                    Sysname <span className={style.requiredIcon1}>*</span>
                                                </>
                                            ) : (
                                                ""
                                            )
                                        }
                                        labelCol={{span: 24}}
                                        wrapperCol={{span: 24}}
                                        rules={[{required: true, message: "Please select sysname!"}]}
                                    >
                                        <TreeSelect
                                            disabled={isEditMode}
                                            treeData={fabricTreeData}
                                            placeholder="Sysname"
                                            dropdownStyle={{
                                                maxHeight: 200,
                                                overflow: "auto"
                                            }}
                                            treeDefaultExpandAll
                                            maxTagCount={1}
                                            maxTagTextLength={10}
                                            maxTagPlaceholder={omittedValues => `+${omittedValues.length} more`}
                                            onChange={value => {
                                                if (value !== null) {
                                                    let obj = {};
                                                    try {
                                                        obj = JSON.parse(value);
                                                    } catch (e) {
                                                        console.error("Failed to parse sysname:", e);
                                                        return;
                                                    }
                                                    if (obj.sn) {
                                                        // 每次选择sysname都调用接口，强制刷新端口数据
                                                        fetchPortsBySn(obj.sn, true);
                                                        fetchQueuesBySn(obj.sn, true);
                                                    }
                                                } else {
                                                    // 如果清空sysname，也清空端口树数据
                                                    setPortTreeData([]);
                                                    setQueueTreeData([]);
                                                }

                                                watchdogForm.setFields([
                                                    {
                                                        name: ["configurations", name, "port"],
                                                        value: []
                                                    }
                                                ]);
                                            }}
                                            allowClear
                                        />
                                    </Form.Item>
                                </Col>
                                <Col span="3">
                                    <Form.Item
                                        name={[name, "port"]}
                                        label={
                                            index === 0 ? (
                                                <>
                                                    Port <span className={style.requiredIcon1}>*</span>
                                                </>
                                            ) : (
                                                ""
                                            )
                                        }
                                        labelCol={{span: 24}}
                                        wrapperCol={{span: 24}}
                                        rules={[{required: true, message: "Please select port!"}]}
                                    >
                                        <CustomTreeSelect
                                            treeData={portTreeData}
                                            placeholder="Ports"
                                            onFocus={() => {
                                                // 直接获取当前字段的值，参考PFCConfiguration
                                                const currentPortValue = watchdogForm.getFieldValue([
                                                    "configurations",
                                                    name,
                                                    "port"
                                                ]);
                                                const currentSysname = watchdogForm.getFieldValue([
                                                    "configurations",
                                                    name,
                                                    "sysname"
                                                ]);

                                                if (currentSysname && currentPortValue) {
                                                    try {
                                                        const sysObj = JSON.parse(currentSysname);
                                                        if (sysObj.sn) {
                                                            // 解析端口值
                                                            let portsToEnable = [];
                                                            if (
                                                                Array.isArray(currentPortValue) &&
                                                                currentPortValue.length > 0
                                                            ) {
                                                                try {
                                                                    const portData = portDataCache.get(sysObj.sn);
                                                                    const allPortsValue = portData
                                                                        ? JSON.stringify(
                                                                              portData.map(item => item.port_name)
                                                                          )
                                                                        : null;
                                                                    if (currentPortValue[0] === allPortsValue) {
                                                                        portsToEnable = JSON.parse(currentPortValue[0]);
                                                                    } else {
                                                                        portsToEnable = currentPortValue;
                                                                    }
                                                                } catch (e) {
                                                                    portsToEnable = currentPortValue;
                                                                }
                                                            }
                                                            // 调用 calculatePortTreeData 释放端口
                                                            calculatePortTreeData(
                                                                sysObj.sn,
                                                                portTreeData,
                                                                "enable",
                                                                portsToEnable,
                                                                name
                                                            );
                                                        }
                                                    } catch (e) {
                                                        console.error("Error parsing sysname:", e);
                                                    }
                                                }
                                            }}
                                            onBlur={() => {
                                                const currentSysname = watchdogForm.getFieldValue([
                                                    "configurations",
                                                    name,
                                                    "sysname"
                                                ]);
                                                if (currentSysname) {
                                                    try {
                                                        const sysObj = JSON.parse(currentSysname);
                                                        calculatePortTreeData(
                                                            sysObj.sn,
                                                            portTreeData,
                                                            "disable",
                                                            editPortData,
                                                            name
                                                        );
                                                    } catch (e) {
                                                        console.error("Error parsing sysname:", e);
                                                    }
                                                }
                                            }}
                                            onChange={(value, label) => {
                                                setEditPortData(value);
                                            }}
                                        />
                                    </Form.Item>
                                </Col>
                                <Col span="3">
                                    <Form.Item
                                        name={[name, "queue"]}
                                        label={
                                            index === 0 ? (
                                                <>
                                                    Queue <span className={style.requiredIcon1}>*</span>
                                                </>
                                            ) : (
                                                ""
                                            )
                                        }
                                        labelCol={{span: 24}}
                                        wrapperCol={{span: 24}}
                                        rules={[{required: true, message: "Please select queue!"}]}
                                    >
                                        <CustomTreeSelect
                                            treeData={queueTreeData}
                                            placeholder="Queues"
                                            onFocus={() => {
                                                // 直接获取当前字段的值，参考PFCConfiguration
                                                const currentQueueValue = watchdogForm.getFieldValue([
                                                    "configurations",
                                                    name,
                                                    "queue"
                                                ]);
                                                const currentSysname = watchdogForm.getFieldValue([
                                                    "configurations",
                                                    name,
                                                    "sysname"
                                                ]);

                                                if (currentSysname && currentQueueValue) {
                                                    try {
                                                        const sysObj = JSON.parse(currentSysname);
                                                        if (sysObj.sn) {
                                                            // 解析队列值
                                                            let queuesToEnable = [];
                                                            if (
                                                                Array.isArray(currentQueueValue) &&
                                                                currentQueueValue.length > 0
                                                            ) {
                                                                try {
                                                                    const queueData = queueDataCache.get(sysObj.sn);
                                                                    const allQueuesValue = queueData
                                                                        ? JSON.stringify(
                                                                              queueData.map(item => item.queue_name)
                                                                          )
                                                                        : null;
                                                                    if (currentQueueValue[0] === allQueuesValue) {
                                                                        queuesToEnable = JSON.parse(
                                                                            currentQueueValue[0]
                                                                        );
                                                                    } else {
                                                                        queuesToEnable = currentQueueValue;
                                                                    }
                                                                } catch (e) {
                                                                    queuesToEnable = currentQueueValue;
                                                                }
                                                            }
                                                            const queueData = queueDataCache.get(sysObj.sn);
                                                            if (queueData) {
                                                                calculateQueueTreeData(
                                                                    sysObj.sn,
                                                                    queueData,
                                                                    "enable",
                                                                    queuesToEnable
                                                                );
                                                            }
                                                        }
                                                    } catch (e) {
                                                        console.error("Error parsing sysname:", e);
                                                    }
                                                }
                                            }}
                                            onBlur={() => {
                                                const currentSysname = watchdogForm.getFieldValue([
                                                    "configurations",
                                                    name,
                                                    "sysname"
                                                ]);
                                                if (currentSysname) {
                                                    try {
                                                        const sysObj = JSON.parse(currentSysname);
                                                        if (sysObj.sn) {
                                                            const queueData = queueDataCache.get(sysObj.sn);
                                                            if (queueData) {
                                                                // 重新计算所有队列状态
                                                                calculateQueueTreeData(sysObj.sn, queueData);
                                                            }
                                                        }
                                                    } catch (e) {
                                                        console.error("Error parsing sysname:", e);
                                                    }
                                                }
                                            }}
                                            onChange={(value, label) => {
                                                watchdogForm.setFieldsValue({
                                                    configurations: {
                                                        [name]: {
                                                            queue: value
                                                        }
                                                    }
                                                });
                                            }}
                                        />
                                    </Form.Item>
                                </Col>
                                <Col span="3">
                                    <Form.Item
                                        name={[name, "enable"]}
                                        label={
                                            index === 0 ? (
                                                <>
                                                    Enable <span className={style.requiredIcon1}>*</span>
                                                </>
                                            ) : (
                                                ""
                                            )
                                        }
                                        labelCol={{span: 24}}
                                        wrapperCol={{span: 24}}
                                        rules={[{required: true, message: "Please select enable status!"}]}
                                    >
                                        <Select placeholder="Enable" style={{width: "100%"}}>
                                            {pfcEnabledOptions.map(option => (
                                                <Select.Option key={option} value={option}>
                                                    {option}
                                                </Select.Option>
                                            ))}
                                        </Select>
                                    </Form.Item>
                                </Col>
                                <Col span="3">
                                    <Form.Item
                                        name={[name, "granularity"]}
                                        label={index === 0 ? "Granularity" : ""}
                                        labelCol={{span: 24}}
                                        wrapperCol={{span: 24}}
                                        // rules={[{required: true, message: "Please input granularity value!"}]}
                                        rules={[
                                            {
                                                validator: (_, value) => {
                                                    if (value === undefined || value === "") {
                                                        return Promise.resolve();
                                                    }
                                                    if (Number(value) === 10 || Number(value) === 100) {
                                                        return Promise.resolve();
                                                    }
                                                    return Promise.reject(new Error("Only 10 or 100 is allowed!"));
                                                }
                                            }
                                        ]}
                                    >
                                        <InputNumber placeholder="Granularity" style={{width: "100%"}} />
                                    </Form.Item>
                                </Col>
                                <Col span="3">
                                    <Form.Item
                                        name={[name, "restore_mode"]}
                                        label={index === 0 ? "Restore Mode" : ""}
                                        labelCol={{span: 24}}
                                        wrapperCol={{span: 24}}
                                        // rules={[{required: true, message: "Please select restore mode!"}]}
                                    >
                                        <Select placeholder="Restore Mode" style={{width: "100%"}}>
                                            <Select.Option value="manual">Manual</Select.Option>
                                            <Select.Option value="auto">Auto</Select.Option>
                                        </Select>
                                    </Form.Item>
                                </Col>
                                <Col span="3">
                                    <Form.Item
                                        name={[name, "restore_action"]}
                                        label={index === 0 ? "Restore Action" : ""}
                                        labelCol={{span: 24}}
                                        wrapperCol={{span: 24}}
                                        // rules={[{required: true, message: "Please select restore action!"}]}
                                    >
                                        <Select placeholder="Restore Action" style={{width: "100%"}}>
                                            <Select.Option value="forward">Forward</Select.Option>
                                            <Select.Option value="drop">Drop</Select.Option>
                                        </Select>
                                    </Form.Item>
                                </Col>
                                <Col span="3">
                                    <Form.Item
                                        name={[name, "detection_interval"]}
                                        label={index === 0 ? "Detection Interval" : ""}
                                        // labelCol={{span: 24}}
                                        labelCol={{
                                            span: 24,
                                            style: {
                                                whiteSpace: "nowrap",
                                                overflow: "hidden",
                                                textOverflow: "ellipsis"
                                            }
                                        }}
                                        wrapperCol={{span: 24}}
                                        // rules={[{required: true, message: "Please input detection interval!"}]}
                                        rules={[
                                            {
                                                validator: (_, value) => {
                                                    if (value === undefined || value === null || value === "") {
                                                        return Promise.resolve();
                                                    }
                                                    if (value < 1 || value > 15) {
                                                        return Promise.reject(
                                                            new Error("Detection Interval must between 1 to 15")
                                                        );
                                                    }
                                                    return Promise.resolve();
                                                }
                                            }
                                        ]}
                                    >
                                        <InputNumber
                                            placeholder="Detection Interval"
                                            controls={false}
                                            style={{width: "100%"}}
                                        />
                                    </Form.Item>
                                </Col>
                                <Col span="3">
                                    <Form.Item
                                        name={[name, "restore_interval"]}
                                        label={index === 0 ? "Restore Interval" : ""}
                                        labelCol={{span: 24}}
                                        wrapperCol={{span: 24}}
                                        // rules={[{required: true, message: "Please input restore interval!"}]}
                                        rules={[
                                            {
                                                validator: (_, value) => {
                                                    if (value === undefined || value === null || value === "") {
                                                        return Promise.resolve();
                                                    }
                                                    if (value < 1 || value > 15) {
                                                        return Promise.reject(
                                                            new Error("Restore Interval must between 1 to 15")
                                                        );
                                                    }
                                                    return Promise.resolve();
                                                }
                                            }
                                        ]}
                                    >
                                        <InputNumber
                                            placeholder="Restore Interval"
                                            controls={false}
                                            style={{width: "100%"}}
                                        />
                                    </Form.Item>
                                </Col>
                                <Col span="3">
                                    <Form.Item
                                        name={[name, "threshold_period"]}
                                        label={index === 0 ? "Threshold Period" : ""}
                                        labelCol={{span: 24}}
                                        wrapperCol={{span: 24}}
                                        // rules={[{required: true, message: "Please input threshold period!"}]}
                                        rules={[
                                            {
                                                validator: (_, value) => {
                                                    if (value === undefined || value === null || value === "") {
                                                        return Promise.resolve();
                                                    }
                                                    if (value < 1 || value > 60) {
                                                        return Promise.reject(
                                                            new Error("Threshold Period must between 1 to 60")
                                                        );
                                                    }
                                                    return Promise.resolve();
                                                }
                                            }
                                        ]}
                                    >
                                        <InputNumber
                                            placeholder="Threshold Period"
                                            controls={false}
                                            style={{width: "100%"}}
                                        />
                                    </Form.Item>
                                </Col>
                                <Col span="3">
                                    <Form.Item
                                        name={[name, "threshold_count"]}
                                        label={index === 0 ? "Threshold Count" : ""}
                                        labelCol={{span: 24}}
                                        wrapperCol={{span: 24}}
                                        // rules={[{required: true, message: "Please input threshold count!"}]}
                                        rules={[
                                            {
                                                validator: (_, value) => {
                                                    if (value === undefined || value === null || value === "") {
                                                        return Promise.resolve();
                                                    }
                                                    if (value < 1 || value > 500) {
                                                        return Promise.reject(
                                                            new Error("Threshold Count must between 1 to 500")
                                                        );
                                                    }
                                                    return Promise.resolve();
                                                }
                                            }
                                        ]}
                                    >
                                        <InputNumber
                                            placeholder="Threshold Count"
                                            controls={false}
                                            style={{width: "100%"}}
                                        />
                                    </Form.Item>
                                </Col>
                                <Col>
                                    {index === 0 ? (
                                        <Button
                                            onClick={() => {
                                                if (isEditMode === null) {
                                                    add();
                                                } else {
                                                    const currentSysname = watchdogForm.getFieldValue([
                                                        "configurations",
                                                        name,
                                                        "sysname"
                                                    ]);
                                                    add({
                                                        sysname: currentSysname
                                                    });
                                                }
                                            }}
                                            style={{
                                                backgroundColor: "transparent",
                                                color: "#BFBFBF",
                                                marginBottom: "24px",
                                                marginTop: index === 0 ? "40px" : "0"
                                            }}
                                            type="link"
                                            icon={<PlusOutlined />}
                                        />
                                    ) : (
                                        <Button
                                            style={{
                                                backgroundColor: "transparent",
                                                color: "#BFBFBF",
                                                marginBottom: "24px",
                                                marginTop: index === 0 ? "40px" : "0"
                                            }}
                                            type="link"
                                            icon={<MinusOutlined />}
                                            onClick={() => {
                                                const currentValues = watchdogForm.getFieldsValue();
                                                const configToRemove = currentValues.configurations[name];

                                                remove(name);

                                                if (configToRemove && configToRemove.sysname) {
                                                    try {
                                                        const sysObj = JSON.parse(configToRemove.sysname);
                                                        if (sysObj.sn) {
                                                            setTimeout(() => {
                                                                const portData = portDataCache.get(sysObj.sn);
                                                                if (portData) {
                                                                    initPortTreeData(portData);
                                                                }
                                                            }, 100);
                                                        }
                                                    } catch (e) {
                                                        console.error("Error parsing sysname:", e);
                                                    }
                                                }
                                            }}
                                        />
                                    )}
                                </Col>
                            </Row>
                            <Form.Item name={[name, "is_all_ports"]} hidden initialValue={false}>
                                <Input type="hidden" />
                            </Form.Item>
                            <Form.Item name={[name, "is_all_queues"]} hidden initialValue={false}>
                                <Input type="hidden" />
                            </Form.Item>
                            <Form.Item name={[name, "config_id"]} hidden>
                                <Input type="hidden" />
                            </Form.Item>
                        </div>
                    ))}
                </>
            )}
        </Form.List>
    );

    // 添加 renderArrayColumn 函数
    const renderArrayColumn = (text, maxItems = 2) => {
        try {
            const arr = Array.isArray(text) ? text : JSON.parse(text);
            if (!Array.isArray(arr)) return text;

            // 处理特殊值
            if (arr.includes("all_ports")) return "All Ports";
            if (arr.includes("all_queues")) return "All Queues";

            // 处理普通数组
            if (arr.length <= maxItems) {
                return (
                    <div className="force-wrap-text" title={arr.join(", ")}>
                        {arr.join(", ")}
                    </div>
                );
            }

            const displayItems = arr.slice(0, maxItems);
            const remainingCount = arr.length - maxItems;
            const displayText = `${displayItems.join(", ")} +${remainingCount} more`;
            return (
                <div className="force-wrap-text" title={arr.join(", ")}>
                    {displayText}
                </div>
            );
        } catch {
            return (
                <div className="force-wrap-text" title={text}>
                    {text}
                </div>
            );
        }
    };

    // 修改表格列定义
    const watchdogColumns = [
        {
            title: "",
            key: "expand",
            width: 50,
            render: (_, record) => {}
        },
        {
            ...createColumnConfig("Sysname", "sysname", TableFilterDropdown),
            width: 150,
            ellipsis: true
        },
        {
            ...createColumnConfig("Port", "port", TableFilterDropdown),
            width: 200,
            render: (_, record) => {
                if (record.is_all_ports) {
                    return "All Ports";
                }
                return renderArrayColumn(record.port);
            }
        },
        {
            ...createColumnConfig("Queue", "queue", TableFilterDropdown),
            width: 150,
            render: (_, record) => {
                if (record.is_all_queues) {
                    return "All Queues";
                }
                return renderArrayColumn(record.queue);
            }
        },
        {
            ...createColumnConfig("Enable", "enabled"),
            width: 100,
            render: (_, record) => {
                return <div>{record.enabled ? "True" : "False"}</div>;
            }
        },
        {
            ...createColumnConfig("Granularity", "granularity", TableFilterDropdown),
            width: 150,
            ellipsis: true
        },
        {
            ...createColumnConfig("Restore Action", "restore_action", TableFilterDropdown),
            width: 150,
            ellipsis: true
        },
        {
            ...createColumnConfig("Restore Mode", "restore_mode", TableFilterDropdown),
            width: 150,
            ellipsis: true
        },
        {
            title: "Operation",
            key: "operation",
            render: (_, record) => (
                <Space size="middle">
                    <Button type="link" onClick={() => handleWatchdogEdit(record)}>
                        Edit
                    </Button>
                    <Button type="link" onClick={() => handleDelete(record)}>
                        Delete
                    </Button>
                </Space>
            )
        }
    ];

    return (
        <div style={{marginBottom: 30}}>
            <h3>
                PFC WatchDog Configuration
                <CustomQuestionIcon
                    title="Enable PFC watchdog to detect andresolve PFC deadlocks. PFC watchdogmonitors the duration of PFC pauseframes and takes corrective actions if apotential deadlock is detected."
                    placement="right"
                />
            </h3>
            <div style={{marginBottom: 16}}>
                <Button type="primary" icon={<PlusOutlined />} onClick={handleWatchdogCreate}>
                    Configuration
                </Button>
            </div>
            <ExtendedTable
                columns={watchdogColumns}
                fetchAPIInfo={getPfcWdConfigList}
                ref={tableRef}
                bordered={false}
            />

            <AmpConCustomModalForm
                title={isEditMode ? "Edit PFC WatchDog Configuration" : "Create PFC WatchDog Configuration"}
                isModalOpen={isWatchdogModalVisible}
                formInstance={watchdogForm}
                layoutProps={{
                    labelCol: {
                        span: 4
                    }
                }}
                CustomFormItems={watchdogFormItems}
                onCancel={handleWatchdogModalCancel}
                onSubmit={handleWatchdogSubmit}
                modalClass="ampcon-max-modal"
                footer={[
                    <Divider style={{marginTop: 0, marginBottom: 20}} />,
                    <Button key="cancel" onClick={handleWatchdogModalCancel}>
                        Cancel
                    </Button>,
                    <Button key="ok" type="primary" onClick={watchdogForm.submit}>
                        Apply
                    </Button>
                ]}
            />
        </div>
    );
};

export default PFCWDConfiguration;
