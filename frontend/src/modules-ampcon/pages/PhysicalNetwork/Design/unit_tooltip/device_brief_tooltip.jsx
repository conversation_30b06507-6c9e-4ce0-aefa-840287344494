import {useState, forwardRef, useImperativeHandle, useEffect, useRef} from "react";
import {Flex} from "antd";
import {flushSync} from "react-dom";

const DeviceTooltipBody = ({deviceInfo, deviceInfoKeys, deviceInfoValues}) => {
    return (
        <Flex vertical style={{fontFamily: "Lato, Lato"}}>
            <Flex
                style={{
                    height: "40px",
                    background: "#F8FAFB",
                    borderRadius: "4px 4px 0px 0px",
                    fontWeight: 600,
                    fontSize: "16px",
                    color: "#212519",
                    lineHeight: "19px",
                    textAlign: "left",
                    paddingTop: "10px",
                    paddingLeft: "16px",
                    paddingRight: "16px"
                }}
            >
                {deviceInfo !== null ? deviceInfo.name : "no name"}
            </Flex>
            <Flex
                style={{
                    margin: "16px",
                    width: "100%",
                    height: "100%",
                    marginTop: "17px",
                    textAlign: "Left",
                    fontSize: "14px",
                    fontStyle: "normal",
                    textTransform: "none"
                }}
            >
                <Flex vertical>
                    {deviceInfoKeys.map((key, index) => (
                        <div
                            key={`key-${index}`}
                            style={{
                                height: "17px",
                                color: "#929A9E",
                                fontWeight: 400,
                                lineHeight: "17px",
                                marginBottom: index === deviceInfoKeys.length - 1 ? "0px" : "12px"
                            }}
                        >
                            {key}
                        </div>
                    ))}
                </Flex>
                <Flex vertical style={{marginLeft: "16px"}}>
                    {deviceInfoValues.map((value, index) => (
                        <div
                            key={`value-${index}`}
                            style={{
                                height: "17px",
                                color: "#212519",
                                fontStyle: "Lato-Regular",
                                fontWeight: 400,
                                lineHeight: "17px",
                                marginBottom: index === deviceInfoValues.length - 1 ? "0px" : "12px"
                            }}
                        >
                            {value}
                        </div>
                    ))}
                </Flex>
            </Flex>
        </Flex>
    );
};

const DeviceBriefTooltip = forwardRef(({offsetX, offsetY, topoType}, ref) => {
    const baseStyle = {
        position: "fixed",
        background: "#FFFFFF",
        boxShadow: "0px 1px 12px 1px #E6E8EA",
        zIndex: 10000,
        display: "block",
        pointerEvents: "none",
        transform: "none",
        whiteSpace: "pre"
    };

    const [isTooltipVisible, setTooltipVisible] = useState(false);
    const [deviceInfo, setDeviceInfo] = useState(null);
    const [deviceStyle, setDeviceStyle] = useState(baseStyle);
    const [deviceInfoKeys] = useState(["Role", "Sysname"]);
    const [deviceInfoValues, setDeviceInfoValues] = useState([]);

    // 使用 ref 记录是否需要显示 tooltip
    const shouldShowTooltip = useRef(false);

    useEffect(() => {
        const handleMouseMove = event => {
            calculateTooltipPosition(event.clientX, event.clientY);
        };
        window.addEventListener("mousemove", handleMouseMove);
        return () => window.removeEventListener("mousemove", handleMouseMove);
    }, [isTooltipVisible]);

    // 监听 deviceInfo 和 deviceInfoValues 变化后触发显示
    useEffect(() => {
        if (shouldShowTooltip.current) {
            setTooltipVisible(true);
            shouldShowTooltip.current = false;
        }
    }, [deviceInfo, deviceInfoValues]);

    useImperativeHandle(ref, () => ({
        showDeviceBriefTooltip: deviceInfoLocal => {
            // 使用 flushSync 强制同步更新，确保隐藏的 Tooltip 先渲染
            flushSync(() => {
                setDeviceInfo(deviceInfoLocal);
                if (topoType === "fabric") {
                    setDeviceInfoValues([deviceInfoLocal.type, deviceInfoLocal.hostname]);
                } else {
                    setDeviceInfoValues([deviceInfoLocal.type, deviceInfoLocal.name]);
                }
            });

            setTooltipVisible(true); // 保证第一次渲染并进入节点能出现 tooltip
            shouldShowTooltip.current = true;
        },
        hideDeviceBriefTooltip: () => {
            setTooltipVisible(false);
        }
    }));

    const calculateTooltipPosition = (x, y) => {
        const adjustX = x + offsetX;
        const adjustY = y + offsetY;
        const baseStyleTemp = {...baseStyle};
        const deviceBriefTooltipHidden = document.getElementsByClassName("device_brief_tooltip_hidden")[0];

        if (deviceBriefTooltipHidden) {
            const tooltipHidden = deviceBriefTooltipHidden.getBoundingClientRect();

            // 水平位置计算
            if (adjustX + 320 + tooltipHidden.width > window.innerWidth) {
                baseStyleTemp.right = "50px";
                delete baseStyleTemp.left;
            } else {
                baseStyleTemp.left = `${adjustX + 10}px`;
                delete baseStyleTemp.right;
            }

            // 垂直位置计算
            if (adjustY + 200 + tooltipHidden.height > window.innerHeight) {
                baseStyleTemp.bottom = "70px";
                delete baseStyleTemp.top;
            } else {
                baseStyleTemp.top = `${adjustY + 10}px`;
                delete baseStyleTemp.bottom;
            }

            setDeviceStyle(baseStyleTemp);
        }
    };

    return (
        <>
            {isTooltipVisible && (
                <div ref={ref} className="device_brief_tooltip" style={deviceStyle}>
                    <DeviceTooltipBody
                        deviceInfo={deviceInfo}
                        deviceInfoKeys={deviceInfoKeys}
                        deviceInfoValues={deviceInfoValues}
                    />
                </div>
            )}
            <div
                ref={ref}
                className="device_brief_tooltip_hidden"
                style={{
                    position: "fixed",
                    background: "#FFFFFF",
                    boxShadow: "0px 1px 12px 1px #E6E8EA",
                    right: window.innerWidth / 2,
                    bottom: window.innerHeight / 2,
                    display: "block",
                    pointerEvents: "none",
                    zIndex: -1,
                    transform: "none",
                    whiteSpace: "pre",
                    visibility: "hidden"
                }}
            >
                <DeviceTooltipBody
                    deviceInfo={deviceInfo}
                    deviceInfoKeys={deviceInfoKeys}
                    deviceInfoValues={deviceInfoValues}
                />
            </div>
        </>
    );
});

export default DeviceBriefTooltip;
