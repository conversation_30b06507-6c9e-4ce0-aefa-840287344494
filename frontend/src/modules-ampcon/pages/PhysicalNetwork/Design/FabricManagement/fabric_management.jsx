import {
    AmpConCustomTable,
    createColumnConfig,
    AmpConCustomModalForm,
    AmpConCustomModalTable,
    createColumnWithoutFilter,
    TableFilterDropdown
} from "@/modules-ampcon/components/custom_table";
import {
    fetchFabricInfo,
    createFabric,
    saveFabric,
    editFabricTableData,
    deleteFabric
} from "@/modules-ampcon/apis/lifecycle_api";
import {fetchDCTemplateInfo, createFabricTopo, deployment_status} from "@/modules-ampcon/apis/dc_template_api";
import {tipModalAction, confirmModalActionWithCheckbox} from "@/modules-ampcon/components/custom_modal";
import {Flex, Table, Button, Divider, Space, Tooltip, Form, Input, message, Card, Select, Row, Modal, Tag} from "antd";
import {useState, useRef, useEffect} from "react";
import {useNavigate} from "react-router-dom";
import {useForm} from "antd/es/form/Form";
import Icon, {SearchOutlined} from "@ant-design/icons";
import {addSvg, onlineSvg, offlineSvg, exclamationSvg} from "@/utils/common/iconSvg";
import styles from "@/modules-ampcon/pages/PhysicalNetwork/Design/FabricManagement/fabric_management.module.scss";

const {TextArea} = Input;

const FabricManagement = () => {
    const navigate = useNavigate();
    const tableRef = useRef(null);
    const [loading, setLoading] = useState(false);
    const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
    const [isEditModalOpen, setIsEditModalOpen] = useState(false);
    const [isCreateTopoModalOpen, setIsCreateTopoModalOpen] = useState(false);
    const [editFabricName, setEditFabricName] = useState("");
    const [fabricID, setFabricID] = useState("");
    const [isLogModalOpen, setIsLogModalOpen] = useState(false);
    const fabricColumns = [
        {
            ...createColumnConfig("Fabric Name", "fabric_name"),
            render: (_, record) => {
                const isDefault = record.fabric_name === "default";
                return (
                    <Space size="large" className="actionLink">
                        {isDefault ? (
                            <span>{record.fabric_name}</span>
                        ) : (
                            <a
                                onClick={() => {
                                    // navigate(`/device/switches/${record.sn}`);
                                    if (record.fabric_topo_id === 0) {
                                        setIsCreateTopoModalOpen(true);
                                        setEditFabricName(record.fabric_name);
                                    } else {
                                        const fabric_topo_info = {
                                            fabric_name: record.fabric_name,
                                            template_name: record.template_name,
                                            id: record.fabric_topo_id,
                                            fabric_id: record.id
                                        };
                                        edit_fabric(fabric_topo_info);
                                    }
                                }}
                            >
                                {record.fabric_name}
                            </a>
                        )}
                    </Space>
                );
            }
        },
        {
            ...createColumnConfig("Description", "description"),
            render: (_, record) =>
                record?.description.length > 50 ? (
                    <Tooltip placement="bottom" title={record.description}>
                        <p style={{maxWidth: 200, overflow: "hidden", textOverflow: "ellipsis", cursor: "pointer"}}>
                            {record.description}
                        </p>
                    </Tooltip>
                ) : (
                    <span>{record.description}</span>
                )
        },
        createColumnConfig("Switch", "switch_count"),
        createColumnConfig("PoD", "az_count"),
        {
            title: "Underlay Routing Protocol",
            dataIndex: "underlay_routing_protocol",
            sorter: false
        },
        {
            title: "Last Modified Time",
            dataIndex: "modified_time",
            sorter: false
        },
        {
            title: "Status",
            dataIndex: "status",
            sorter: false,
            render: (text, record) => {
                let tagClass;
                switch (record.status) {
                    case "Deployed":
                        tagClass = styles.successTag;
                        break;
                    case "Deploy Failed":
                        tagClass = styles.failedTag;
                        break;
                    case "Deploying":
                        tagClass = styles.runningTag;
                        break;
                    default:
                        tagClass = styles.pendingTag;
                        break;
                }
                return <Tag className={tagClass}>{record.status}</Tag>;
            }
        },
        {
            title: "Operation",
            render: (_, record) => {
                return (
                    <div>
                        <div>
                            <Space size="large" className="actionLink">
                                <a
                                    onClick={() => {
                                        setEditFabricName(record.fabric_name);
                                        setIsEditModalOpen(true);
                                    }}
                                >
                                    Edit
                                </a>
                                <a
                                    onClick={
                                        record.fabric_name !== "default"
                                            ? () => {
                                                  if (
                                                      record.vd_count !== 0 ||
                                                      record.az_count !== 0 ||
                                                      record.logical_count !== 0
                                                  ) {
                                                      return tipModalAction(
                                                          "There are resources associated that cannot be deleted! Please release all resources and try again.",
                                                          () => {}
                                                      );
                                                  }
                                                  return confirmModalActionWithCheckbox(
                                                      "Deleting the fabric will clear the underlying configurations on switches in this fabric.  If switches are not connected to AmpCon-DC, the underlay configurations might fail to be deleted.",
                                                      "Continue to delete the fabric?",
                                                      () => handleDeleteFabric(record.fabric_name)
                                                  );
                                              }
                                            : null
                                    }
                                    style={{
                                        color: record.fabric_name !== "default" ? "#14C9BB" : "#b2b2b2",
                                        cursor: record.fabric_name !== "default" ? "pointer" : "not-allowed"
                                    }}
                                >
                                    Delete
                                </a>
                                <a
                                    onClick={() => {
                                        setFabricID(record.fabric_topo_id);
                                        setIsLogModalOpen(true);
                                    }}
                                    style={{
                                        color: record.status !== "Not Deployed" ? "#14C9BB" : "#b2b2b2",
                                        cursor: record.status !== "Not Deployed" ? "pointer" : "not-allowed"
                                    }}
                                >
                                    Log
                                </a>
                            </Space>
                        </div>
                    </div>
                );
            }
        }
    ];
    const fabricSearchFieldsList = ["fabric_name", "description", "status"];
    const switchMatchFieldsList = [
        {name: "fabric_name", matchMode: "fuzzy"},
        {name: "description", matchMode: "fuzzy"},
        {name: "status", matchMode: "fuzzy"},
        {name: "modified_time", matchMode: "fuzzy"}
    ];

    const handleDeleteFabric = async selectedFabric => {
        setLoading(true);
        const response = await deleteFabric(selectedFabric);
        if (response.status !== 200) {
            message.error(response.info);
        } else {
            message.success(response.info);
            tableRef.current.refreshTable();
        }
        setLoading(false);
    };

    const create_fabric = async formData => {
        formData.fabric_name = editFabricName;
        const result = await createFabricTopo(formData);
        if (result.status === 200) {
            message.success(result.info);
            setIsCreateTopoModalOpen(false);
            const fabric_topo_info = {
                fabric_name: editFabricName,
                template_name: result.data.template_name,
                id: result.data.id,
                fabric_id: result.data.fabric_id
            };
            edit_fabric(fabric_topo_info);
        } else message.error(result.info);
    };

    const edit_fabric = fabric_topo_info => {
        navigate(`/physical_network/fabrics/${fabric_topo_info.fabric_name}`, {
            state: {actionType: "Edit", data: fabric_topo_info}
        });
    };

    return (
        <div style={{minHeight: "100%"}}>
            <Card style={{display: "flex", flex: 1, minHeight: "100%"}}>
                <h2 style={{marginTop: "8px", marginBottom: "20px"}}>Fabrics</h2>
                <AmpConCustomTable
                    columns={fabricColumns}
                    searchFieldsList={fabricSearchFieldsList}
                    extraButton={
                        <Button
                            type="primary"
                            icon={<Icon component={addSvg} style={{height: 20}} />}
                            onClick={() => {
                                setIsCreateModalOpen(true);
                            }}
                        >
                            Fabric
                        </Button>
                    }
                    matchFieldsList={switchMatchFieldsList}
                    fetchAPIInfo={fetchFabricInfo}
                    loading={loading}
                    ref={tableRef}
                />
            </Card>
            <CreateFabricModal
                isModalOpen={isCreateModalOpen}
                onCancel={() => {
                    setIsCreateModalOpen(false);
                    tableRef.current.refreshTable();
                }}
            />
            <EditFabricModal
                fabricName={editFabricName}
                isModalOpen={isEditModalOpen}
                onCancel={() => {
                    setIsEditModalOpen(false);
                    tableRef.current.refreshTable();
                }}
            />
            <CreateModal
                title="Import Template"
                createModalOpen={isCreateTopoModalOpen}
                setCreateModalOpen={setIsCreateTopoModalOpen}
                onCancel={() => setIsCreateTopoModalOpen(false)}
                onSubmit={create_fabric}
                modalClass="ampcon-mini-modal"
            />
            <LogFabricModal
                fabricID={fabricID}
                isModalOpen={isLogModalOpen}
                onCancel={() => {
                    setIsLogModalOpen(false);
                }}
            />
        </div>
    );
};

const CreateFabricModal = ({isModalOpen, onCancel}) => {
    const [form] = useForm();

    const formItems = () => {
        return (
            <>
                <Form.Item
                    name="fabricName"
                    label="Fabric Name"
                    rules={[
                        {required: true, message: "Fabric Name cannot be empty."},
                        {max: 64, message: "Enter a maximum of 64 characters"},
                        {
                            validator: (_, value) => {
                                if (value === "All") {
                                    return Promise.reject(new Error("Please input a valid fabric name!"));
                                }
                                if (value?.trim() !== value) {
                                    return Promise.reject(
                                        new Error("Fabric name should not have leading or trailing spaces.")
                                    );
                                }
                                if (value?.includes(" ")) {
                                    return Promise.reject(new Error("Fabric name should not have internal spaces."));
                                }
                                if (!/^[\s\w-]+$/.test(value)) {
                                    return Promise.reject(
                                        new Error(
                                            "Fabric name can only contain letters, numbers, underscores and hyphens."
                                        )
                                    );
                                }
                                return Promise.resolve();
                            }
                        }
                    ]}
                >
                    <Input style={{width: "280px"}} />
                </Form.Item>
                <Form.Item
                    name="description"
                    label="Description"
                    rules={[{max: 256, message: "Enter a maximum of 256 characters"}]}
                >
                    <TextArea rows={5} style={{width: "280px"}} />
                </Form.Item>
            </>
        );
    };

    const onSubmit = async values => {
        const response = await createFabric(values.fabricName, values.description);
        if (response.status !== 200) {
            message.error(response.info);
        } else {
            message.success(response.info);
            onCancelModal();
        }
    };

    const onCancelModal = () => {
        form.resetFields();
        onCancel();
    };

    return (
        <AmpConCustomModalForm
            title="Create Fabric"
            isModalOpen={isModalOpen}
            formInstance={form}
            layoutProps={{
                labelCol: {
                    span: 6
                }
            }}
            CustomFormItems={formItems}
            onCancel={onCancelModal}
            onSubmit={onSubmit}
            modalClass="ampcon-middle-modal"
            footer={[
                <Divider style={{marginTop: 0, marginBottom: 20}} />,
                <Button key="cancel" onClick={onCancelModal}>
                    Cancel
                </Button>,
                <Button key="ok" type="primary" onClick={form.submit}>
                    Apply
                </Button>
            ]}
        />
    );
};

const EditFabricModal = ({fabricName, isModalOpen, onCancel}) => {
    const modalTableRef = useRef(null);

    const onSave = async () => {
        // setIsEditModalOpen(false);

        const actions = modalTableRef.current.getTableRef().current.getOperations();
        const selectedData = modalTableRef.current.getTableRef().current.getSelectedRow().tableSelectedRows;
        const removedData = modalTableRef.current.getTableRef().current.getRemovedRow().tableRemovedRows;
        const removeSnList = [];
        const addSnList = [];

        for (const [id, action] of Object.entries(actions)) {
            if (action === "remove") {
                removeSnList.push(removedData.find(temp => temp.id === parseInt(id))?.sn);
            } else if (action === "add") {
                addSnList.push(selectedData.find(temp => temp.id === parseInt(id))?.sn);
            }
        }

        const response = await saveFabric(fabricName, removeSnList, addSnList);
        if (response.status !== 200) {
            message.error(response.info);
        } else {
            message.success(response.info);
            onCancel();
        }
    };

    const InitSwitchTableParams = ({moreColumn = []}) => {
        const matchFieldsList = [
            {name: "host_name", matchMode: "fuzzy"},
            {name: "mgt_ip", matchMode: "fuzzy"},
            {name: "platform_model", matchMode: "fuzzy"},
            {name: "address", matchMode: "fuzzy"},
            {name: "version", matchMode: "fuzzy"},
            {name: "status", matchMode: "fuzzy"}
        ];

        const searchFieldsList = ["sn", "host_name", "mgt_ip", "platform_model"];

        const columns = [
            createColumnConfig("Sysname", "host_name", TableFilterDropdown),
            {...createColumnWithoutFilter("SN/Service Tag", "sn"), fixed: "left"},
            createColumnConfig("Model", "platform_model", TableFilterDropdown),
            createColumnConfig("Version", "version", TableFilterDropdown),
            {
                ...createColumnConfig("Status", "status", TableFilterDropdown),
                render: (text, record) => {
                    if (record.status === "Imported" || record.status === "Provisioning Success") {
                        return <Tag className={styles.successTag}>{record.status}</Tag>;
                    }
                    return <Tag className={styles.failedTag}>{record.status}</Tag>;
                }
            },
            {
                ...createColumnConfig("IP address", "mgt_ip", TableFilterDropdown),
                render: (_, record) => {
                    let iconComponent;
                    if (record.reachable_status === 0) {
                        iconComponent = onlineSvg;
                    } else if (record.reachable_status === 1) {
                        iconComponent = offlineSvg;
                    } else {
                        iconComponent = exclamationSvg;
                    }

                    return (
                        <Space>
                            <Icon component={iconComponent} />
                            {record.mgt_ip}
                        </Space>
                    );
                }
            },
            ...(moreColumn || [])
        ];

        const switchTableParams = [matchFieldsList, searchFieldsList, columns];

        return switchTableParams;
    };

    const [matchFieldsListModal, searchFieldsListModal, columnsModal] = InitSwitchTableParams({
        moreColumn: []
    });

    return (
        <AmpConCustomModalTable
            ref={modalTableRef}
            title="Edit Fabric"
            modalClass="ampcon-max-modal"
            columns={columnsModal}
            matchFieldsList={matchFieldsListModal}
            searchFieldsList={searchFieldsListModal}
            selectModalOpen={isModalOpen}
            fetchAPIInfo={editFabricTableData}
            buttonProps={[]}
            fetchAPIParams={[fabricName]}
            onCancel={onCancel}
            rowSelection={{
                selectedRowKeys: [],
                selectedRows: [],
                onChange: () => {}
            }}
            footer={
                <div>
                    <Divider />
                    <Button
                        onClick={() => {
                            onCancel();
                        }}
                    >
                        Cancel
                    </Button>
                    <Button type="primary" onClick={onSave}>
                        Apply
                    </Button>
                </div>
            }
        />
    );
};

const CreateModal = ({
    title,
    createModalOpen,
    // setCreateModalOpen,
    onCancel,
    onSubmit
}) => {
    const [form] = Form.useForm();
    const [templateList, setTemplateList] = useState([]);

    const dcTemplateFetchData = async () => {
        let allData = [];
        let currentPage = 1;
        let total = 0;
        do {
            const response = await fetchDCTemplateInfo(currentPage, 10);

            if (response.status === 200) {
                const {data, total: responseTotal} = response;
                allData = allData.concat(data);
                total = responseTotal;
                currentPage++;
            } else {
                message.error("get template list failed");
                return;
            }
        } while (allData.length < total);
        setTemplateList(allData);
    };

    const handleOk = async () => {
        const formData = await form.validateFields();
        await onSubmit(formData);
    };
    useEffect(() => {
        dcTemplateFetchData();
    }, []);

    return (
        <Modal
            className="fabric-ampcon-mini-modal"
            title={title}
            open={createModalOpen}
            onCancel={onCancel}
            footer={
                <Space>
                    <Button onClick={onCancel}> Cancel </Button>
                    <Button type="primary" onClick={handleOk}>
                        Apply
                    </Button>
                </Space>
            }
        >
            <Form form={form} labelAlign="left" style={{minHeight: "70px"}}>
                <Form.Item
                    name="fabric_template_id"
                    label="DC Template"
                    rules={[{required: true, message: "Please select a DC template!"}]}
                    labelCol={{style: {width: 120, marginLeft: 30}}}
                >
                    <Select
                        style={{width: 280}}
                        options={templateList?.map(item => ({
                            value: item.id,
                            label: item.name
                        }))}
                    />
                </Form.Item>
            </Form>
        </Modal>
    );
};

const LogFabricModal = ({isModalOpen, onCancel, fabricID}) => {
    const [data, setData] = useState([]);
    const [deployData, setDeployData] = useState([]);
    const [chooseLogInfo, setChooseLogInfo] = useState("");
    const [viewLogModal, setViewLogModal] = useState(false);

    const [searchText, setSearchText] = useState("");
    const [filteredData, setFilteredData] = useState(data);

    const handleSort = dataIndex => {
        return (a, b) => {
            const getValue = (obj, keyPath) => {
                return keyPath.split(".").reduce((o, key) => (o ? o[key] : ""), obj);
            };

            const valueA = getValue(a, dataIndex);
            const valueB = getValue(b, dataIndex);

            if (typeof valueA === "string" && typeof valueB === "string") {
                return valueA.localeCompare(valueB);
            }
            return (valueA || 0) - (valueB || 0);
        };
    };

    const fetchDeploymentStatus = async () => {
        try {
            const res = await deployment_status({fabric_topo_id: fabricID});
            if (res.status === 200) {
                const mergedMap = new Map();

                res.data.forEach(item => {
                    const logicDevice = item.logic_device.replace(/_/g, "-");

                    mergedMap.set(logicDevice, {
                        ...item,
                        logic_device: logicDevice
                    });
                });

                const mergedData = Array.from(mergedMap.values());

                setDeployData(mergedData);
            }
        } catch (error) {
            console.error("fetchDeploymentStatus error", error);
        }
    };

    const deploymentColumns = [
        {...createColumnConfig("Hostname", "logic_device"), sorter: handleSort("logic_device")},
        {
            ...createColumnConfig("Mgmt IP", "mgt_ip"),
            sorter: handleSort("mgt_ip")
        },
        {...createColumnConfig("Device Type", "role"), sorter: handleSort("role")},
        {
            ...createColumnConfig("Deployment Status", "task_status"),
            sorter: handleSort("task_status"),
            render: (text, record) => {
                let tagClass;
                switch (record.task_status) {
                    case "SUCCEED":
                        tagClass = styles.successTag;
                        break;
                    case "FAILED":
                        tagClass = styles.failedTag;
                        break;
                    case "RUNNING":
                        tagClass = styles.runningTag;
                        break;
                    default:
                        tagClass = styles.pendingTag;
                        break;
                }
                return <Tag className={tagClass}>{record.task_status}</Tag>;
            }
        },
        {
            title: "Operation",
            render: (_, record) => (
                <Space size="middle" className={styles.actionLink}>
                    <a
                        onClick={() => {
                            setViewLogModal(true);
                            setChooseLogInfo(record);
                        }}
                    >
                        Log
                    </a>
                </Space>
            )
        }
    ];

    const handleSearchChange = value => {
        setSearchText(value);
        const newData = data.filter(record => {
            return (
                record.logic_device.toLowerCase().includes(value?.toLowerCase()) ||
                record.mgt_ip.includes(value) ||
                record.role.toLowerCase().includes(value) ||
                record.task_status.toLowerCase().includes(value)
            );
        });

        setFilteredData(newData);
    };

    useEffect(() => {
        const fetchData = async () => {
            await fetchDeploymentStatus();
        };
        fetchData();

        const intervalId = setInterval(() => {
            fetchDeploymentStatus();
        }, 30000);

        return () => clearInterval(intervalId);
    }, [fabricID]);

    useEffect(() => {
        if (deployData.length > 0) {
            setData(deployData);
            setFilteredData(deployData);
        }
    }, [deployData]);

    const paginationConfig = {
        showSizeChanger: true,
        showTotal: (total, range) => {
            const start = range[0];
            const end = range[1];
            return `${start}-${end} of ${total} items`;
        },
        total: filteredData.length
    };

    return (
        <Modal
            open={isModalOpen}
            title="Deployment Status"
            className="ampcon-max-modal"
            footer={
                <Space>
                    <Button onClick={onCancel}> Cancel </Button>
                </Space>
            }
            onCancel={onCancel}
        >
            <Input
                placeholder="input search text"
                onChange={e => handleSearchChange(e.target.value)}
                allowClear
                prefix={<SearchOutlined />}
                style={{width: 280, float: "right", clear: "both"}}
            />

            <div style={{marginTop: 48}}>
                <Table
                    columns={deploymentColumns}
                    dataSource={filteredData}
                    pagination={paginationConfig}
                    rowKey={record => record.logic_device}
                />
            </div>

            <LogViewModal
                title="Logs"
                chooseLogInfo={chooseLogInfo}
                viewLogModal={viewLogModal}
                onCancel={() => setViewLogModal(false)}
                modalClass="ampcon-max-modal"
            />
        </Modal>
    );
};

const LogViewModal = ({chooseLogInfo, viewLogModal, onCancel}) => {
    const readonlyStyle = {
        minHeight: "330px",
        height: "58vh",
        resize: "vertical",
        border: "1px solid rgb(217, 217, 217)",
        fontSize: "16px",
        borderRadius: "4px",
        boxShadow: "none",
        maxHeight: "calc(100vh - 500px)"
    };

    return (
        <Modal
            className="ampcon-middle-modal"
            title={
                <>
                    <div style={{display: "flex", justifyContent: "space-between", alignItems: "center"}}>
                        {`${chooseLogInfo.logic_device} logs`}
                        <Button type="text" className="ant-modal-close" style={{marginRight: "30px"}} />
                    </div>
                    <Divider style={{marginTop: 8, marginBottom: 0}} />
                </>
            }
            open={viewLogModal}
            onCancel={onCancel}
            footer={null}
        >
            <Flex vertical style={{flex: 1}}>
                <Input.TextArea style={readonlyStyle} value={chooseLogInfo.task_log} rows={19} readOnly />
            </Flex>
        </Modal>
    );
};

export default FabricManagement;
