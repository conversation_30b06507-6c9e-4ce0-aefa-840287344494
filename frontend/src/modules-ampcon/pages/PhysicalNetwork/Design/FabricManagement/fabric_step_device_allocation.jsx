import React, {useImperative<PERSON>andle, useEffect, useState, useRef, forwardRef} from "react";
import {useLocation} from "react-router-dom";
import {Space, Tabs, message, Tag, Table, Select} from "antd";
import Icon from "@ant-design/icons";
import {useTableInitialElement} from "@/modules-ampcon/hooks/useModalTable";
import {createColumnConfig} from "@/modules-ampcon/components/custom_table";

import styles from "@/modules-ampcon/pages/PhysicalNetwork/Design/FabricManagement/fabric_management.module.scss";
import {onlineSvg, offlineSvg, exclamationSvg} from "@/utils/common/iconSvg";

// import API
import {editFabric, viewFabric, allocate_resourced} from "@/modules-ampcon/apis/dc_template_api";
import {getResourcePoolDropdownList} from "@/modules-ampcon/apis/resource_pool_api";

import EditDeviceModal from "../unit_modal/edit_device_modal";

const DeviceAllocationStep = forwardRef(({submitValid, physicalDeviceData, setPhysicalDeviceData}, ref) => {
    const {state} = useLocation();
    const [topoInfo, setTopoInfo] = useState({});

    const [activeTab, setActiveTab] = useState("switch_list");

    const [switchData, setSwitchData] = useState([]);

    const [PhysicalDeviceList, setPhysicalDeviceList] = useState([]);
    const [ipPoolList, setIpPoolList] = useState([]);
    const [ipPoolPagination, setIpPoolPagination] = useState({
        current: 1,
        pageSize: 10
    });
    const [isLoading, setIsLoading] = useState(false);

    const [ipLinkData, setIpLinkData] = useTableInitialElement([], true);

    const editDeviceModalRef = useRef();

    useImperativeHandle(ref, () => ({
        showEditDeviceModal: node => {
            editDeviceModalRef.current.showEditDeviceModal(node, PhysicalDeviceList);
        },
        getRoutedInterfaceAddressPool: () => {
            return ipLinkData[0]?.routed_interface_address_pool;
        }
    }));

    // 更新Physical Device下拉列表，禁用已选中数据
    const updatePhysicalDevice = data => {
        const updatedDeviceData = data?.map(device => {
            const isDisabled = switchData.some(item => item.switch_sn === device.sn);
            return {...device, disabled: isDisabled};
        });
        setPhysicalDeviceList(updatedDeviceData);
    };

    const getIpPool = async () => {
        setIsLoading(true);
        try {
            const response = await getResourcePoolDropdownList({poolTypeList: ["ipv4"]});
            if (response.status === 200) {
                setIpPoolList(prevList => {
                    return [...prevList, ...response.data.ipv4];
                });
            }
        } catch (error) {
            console.error("Error fetching data:", error);
        } finally {
            setIsLoading(false);
        }
    };

    // table排序
    const handleSort = (dataIndex, getValueFn) => {
        return (a, b) => {
            const getValue = (obj, keyPath) => {
                const keys = keyPath.split(".");
                let value = obj;
                for (const key of keys) {
                    if (key.includes("[")) {
                        const [prop, indexStr] = key.split("[");
                        const index = parseInt(indexStr.replace("]", ""), 10);
                        value = value && value[prop] && Array.isArray(value[prop]) && value[prop][index];
                    } else {
                        value = value ? value[key] : null;
                    }
                    if (!value) break;
                }
                return value;
            };

            const valueA = getValueFn ? getValueFn(a) : getValue(a, dataIndex);
            const valueB = getValueFn ? getValueFn(b) : getValue(b, dataIndex);

            if (typeof valueA === "string" && typeof valueB === "string") {
                return valueA.localeCompare(valueB);
            }
            return (valueA || 0) - (valueB || 0);
        };
    };

    // Physical Device下拉选中事件
    const deviceChange = (item, values) => {
        if (values) {
            const selectDevice = PhysicalDeviceList?.find(obj => obj.sn === values);
            if (!selectDevice) return item;
            const updatedSwitchData = switchData?.map(record =>
                record.logic_device === item.logic_device ? {...record, switch_sn: values} : record
            );
            setSwitchData(updatedSwitchData);
        } else {
            const updatedSwitchData = switchData?.map(record =>
                record.logic_device === item.logic_device ? {...record, switch_sn: values} : record
            );
            setSwitchData(updatedSwitchData);
        }
    };
    // physicalDeviceData数据筛选，根据type获取对应数据
    const extractionData = (type, record) => {
        const currentValue = PhysicalDeviceList?.find(obj => obj.sn === record.switch_sn);
        if (!currentValue) return;
        if (currentValue[type] === undefined) return "";
        return currentValue[type];
    };

    const switchColumns = [
        {
            ...createColumnConfig("Hostname", "hostname"),
            sorter: handleSort("node_info.hostname"),
            render: (text, record) => <Space>{record?.node_info?.hostname}</Space>
        },
        // {
        //     ...createColumnConfig("Device Index", "group"),
        //     sorter: handleSort("group"),
        //     render: (text, record) => {
        //         if (record.type === "spine") {
        //             return <Space>{record.logic_device}</Space>;
        //         }
        //         const logicDevice = record.logic_device || "";
        //         const group = `${record.group}_` || "";
        //         const result = logicDevice.replace(group, "").trim();
        //         return <Space>{result}</Space>;
        //     }
        // },
        {...createColumnConfig("Role", "type"), sorter: handleSort("type")},
        {
            ...createColumnConfig("Physical Device", "switch_sn"),
            sorter: handleSort("switch_sn"),
            render: (text, record) => (
                <Space>
                    <Select
                        allowClear
                        value={record?.switch_sn}
                        onChange={value => {
                            deviceChange(record, value);
                        }}
                        disabled={topoInfo.status === "Deployed"}
                        style={{width: 280}}
                        options={PhysicalDeviceList?.map(item => ({
                            value: item.sn,
                            label: `${item.sn} (${item.mgt_ip})`,
                            disabled: item.disabled
                        }))}
                    />
                </Space>
            )
        },
        {
            ...createColumnConfig("Switch SN", "switch_sn"),
            sorter: handleSort("switch_sn")
        },
        {
            ...createColumnConfig("Model", "platform_model"),
            sorter: handleSort("platform_model", record => extractionData("platform_model", record)),
            render: (text, record) => <Space>{extractionData("platform_model", record)}</Space>
        },
        {
            ...createColumnConfig("Mgmt IP", "mgt_ip"),
            sorter: handleSort("mgt_ip", record => extractionData("mgt_ip", record)),
            render: (_, record) => {
                const mgtIP = extractionData("mgt_ip", record);
                if (!mgtIP) {
                    return null;
                }
                const mgtIPStatus = extractionData("reachable_status", record);
                const link_ip_addr = extractionData("link_ip_addr", record) || "";

                let iconComponent;
                if (mgtIPStatus === 0) iconComponent = onlineSvg;
                else if (mgtIPStatus === 1) iconComponent = offlineSvg;
                else iconComponent = exclamationSvg;

                return (
                    <Space>
                        <Icon component={iconComponent} />
                        {link_ip_addr ? `${mgtIP}/${link_ip_addr}` : mgtIP}
                    </Space>
                );
            }
        },
        {
            ...createColumnConfig("Status", "status"),
            sorter: handleSort("status", record => extractionData("status", record)),
            render: (text, record) => {
                const status = extractionData("status", record);
                if (status === "Imported" || status === "Provisioning Success") {
                    return <Tag className={styles.successTag}>{extractionData("status", record)}</Tag>;
                }
                return <Tag className={styles.failedTag}>{extractionData("status", record)}</Tag>;
            }
        }
    ];

    /**
     * Ip Link Address
     */
    // Subnet数据显示处理
    const intToIp = num => {
        return [
            Math.floor(num / 256 ** 3),
            Math.floor((num % 256 ** 3) / 256 ** 2),
            Math.floor((num % 256 ** 2) / 256),
            num % 256
        ].join(".");
    };
    const computeSubnet = (startInt, endInt) => {
        const range = endInt - startInt + 1;

        let maskSize = 32;
        let diff = startInt ^ endInt;
        while (diff !== 0 && maskSize > 0) {
            maskSize--;
            diff >>>= 1;
        }
        const subnet = `${intToIp(startInt)}/${maskSize}`;
        return {
            subnet,
            subnetSize: range
        };
    };
    const findPoolInfo = values => {
        const selectedPool = ipPoolList.find(obj => obj.id === values);
        if (!selectedPool) return "";
        const subnet = [];
        selectedPool?.ranges?.forEach(ele => {
            subnet.push(computeSubnet(ele.start_value, ele.end_value));
        });
        return subnet;
    };

    const poolNameCheck = async (item, values) => {
        // Pool Name变更时，重新获取subnet
        const newObj = {routed_interface_address_pool: values || "", subnet: ""};
        const updatedItem = {...item, ...newObj};
        setIpLinkData([updatedItem]);
    };

    const ipLinkColumns = [
        {
            ...createColumnConfig("Link Type", "type"),
            render: (_, record) => <Space>{record.type === "3-stage" ? "spine--leaf" : "superspine--spine"}</Space>
        },
        {
            ...createColumnConfig("Pool Name", "routed_interface_address_pool"),
            render: (text, record) => (
                <Space>
                    <Select
                        allowClear
                        style={{width: 240}}
                        value={record.routed_interface_address_pool}
                        onChange={e => {
                            poolNameCheck(record, e);
                        }}
                        disabled={topoInfo.status === "Deployed"}
                        options={ipPoolList?.map(item => ({value: item.id, label: item.name}))}
                    />
                </Space>
            )
        }
        // {
        //     ...createColumnConfig("Subnet", "subnet"),
        //     render: (_, record) => (
        //         <Space>
        //             {record?.subnet && record?.subnet.length > 0 ? (
        //                 record.subnet.map((item, index) => (
        //                     <p key={index}>  {item.subnet} </p>
        //                 ))
        //             ) : (
        //                 <p> -- </p>
        //             )}
        //         </Space >
        //     )
        // },
    ];

    const getTopoInfo = async () => {
        const res = await viewFabric({fabric_topo_id: state.data.id});
        if (res.status === 200) {
            setTopoInfo(res.data);
            const newSwitchDatas = res?.data?.fabric_config?.topology?.nodes?.map(item => ({...item})) ?? [];
            setSwitchData(newSwitchDatas);

            const routed_interface_address_pool = res.data?.fabric_config?.routed_interface_address_pool || "";
            const ipLinkNewData = {
                type: res.data?.fabric_config?.type,
                routed_interface_address_pool: res.data?.fabric_config?.routed_interface_address_pool,
                subnet: routed_interface_address_pool ? findPoolInfo(routed_interface_address_pool) : []
            };
            setIpLinkData([ipLinkNewData]);
        }
    };

    // 检查 SN和routed_interface_address_pool 是否存在为值
    const checkData = topo => {
        let validateTopo = true;
        // 1. 判断 routed_interface_address_pool 是否为空
        if (!ipLinkData[0]?.routed_interface_address_pool) {
            validateTopo = false;
        }

        // 2. 遍历 topo?.fabric_config?.topology?.nodes
        const nodes = topo?.fabric_config?.topology?.nodes;
        if (nodes) {
            for (const record of nodes) {
                // 判断 node_info.switch_sn 是否为空
                if (!record.switch_sn) validateTopo = false;

                // 判断 status、mgt_ip"、platform_model等返回值是否为空
                const status = extractionData("status", record);
                const mgt_ip = extractionData("mgt_ip", record);
                const platform_model = extractionData("platform_model", record);

                if (!status || !mgt_ip || !platform_model) {
                    validateTopo = false;
                }
            }
        }
        submitValid("Allocate Device", topo, validateTopo);
    };

    useEffect(() => {
        getIpPool();
        getTopoInfo();
        checkData(topoInfo);
    }, [state]);

    useEffect(() => {
        updatePhysicalDevice(physicalDeviceData);
    }, [state, topoInfo, physicalDeviceData, switchData]);

    useEffect(() => {
        setTopoInfo({
            ...topoInfo,
            fabric_config: {
                ...topoInfo.fabric_config,
                topology: {
                    ...topoInfo?.fabric_config?.topology,
                    nodes: switchData
                }
            }
        });
        updatePhysicalDevice(physicalDeviceData);
    }, [switchData, physicalDeviceData]);

    useEffect(() => {
        checkData(topoInfo);
    }, [topoInfo, ipLinkData]);

    const paginationConfig = {
        showSizeChanger: true,
        showTotal: (total, range) => {
            const start = range[0];
            const end = range[1];
            return `${start}-${end} of ${total} items`;
        },
        total: switchData?.length
    };

    return (
        <>
            <Tabs
                className={`${styles.tabsBorder} radioGroupTabs customTab`}
                activeKey={activeTab}
                onChange={key => setActiveTab(key)}
                items={[
                    {
                        key: "switch_list",
                        label: "Switch List",
                        forceRender: false,
                        children: (
                            <div>
                                <Table
                                    columns={switchColumns}
                                    bordered
                                    dataSource={switchData}
                                    pagination={paginationConfig}
                                />
                            </div>
                        )
                    },
                    {
                        key: "ip_link_address",
                        label: "IP Link Address",
                        forceRender: false,
                        children: (
                            <div>
                                <Table pagination={false} bordered columns={ipLinkColumns} dataSource={ipLinkData} />
                            </div>
                        )
                    }
                ]}
            />
            <EditDeviceModal ref={editDeviceModalRef} deviceChange={deviceChange} />
        </>
    );
});
export default DeviceAllocationStep;
