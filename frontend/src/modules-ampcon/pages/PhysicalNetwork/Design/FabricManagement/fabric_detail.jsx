import React, {useRef, useEffect, useState} from "react";
import {useNavigate, useLocation} from "react-router-dom";
import {Card, Row, Col, Steps, message, Input, But<PERSON>, Spin} from "antd";
import styles from "@/modules-ampcon/pages/PhysicalNetwork/Design/FabricManagement/fabric_management.module.scss";

// Step组件
import DeviceAllocationStep from "./fabric_step_device_allocation";
import ConfigurationConfirmStep from "./fabric_step_configuration_confirm";
import TopoCheckStep from "./fabric_step_topo_check";
import DeploymentStep from "./fabric_step_deployment";

// import API
import {
    editFabric,
    viewFabric,
    allocate_resourced,
    fabric_deployment,
    load_fabric_switch
} from "@/modules-ampcon/apis/dc_template_api";
import FabricTopo from "@/modules-ampcon/pages/PhysicalNetwork/Design/FabricManagement/fabric_topo";

const FabircDetail = () => {
    const navigate = useNavigate();
    const {state} = useLocation();
    const [topoInfo, setTopoInfo] = useState({});
    const [isLoading, setIsLoading] = useState(false);
    const [deploymentData, setDeploymentData] = useState({});

    const [unit, setUnit] = useState([]);
    const [pod, setPod] = useState([]);

    const [current, setCurrent] = useState(0);
    const [isContinuing, setIsContinuing] = useState(false);
    const [physicalDeviceData, setPhysicalDeviceData] = useState([]);

    const [fabricTopoStatus, setFabricTopoStatus] = useState([]);

    const getPhysicalDevice = async () => {
        const response = await load_fabric_switch({fabricName: state.data.fabric_name});
        if (response.status === 200) setPhysicalDeviceData(response.data);
    };

    const deviceAllocationStepRef = useRef(null);

    const updateStepData = async () => {
        const newSteps = steps.map(step => {
            if (step.title === "Allocate Device") {
                return {
                    ...step,
                    content: (
                        <DeviceAllocationStep
                            ref={deviceAllocationStepRef}
                            submitValid={handleFormValid}
                            topoInfo={topoInfo}
                            setTopoInfo={setTopoInfo}
                            physicalDeviceData={physicalDeviceData}
                            setPhysicalDeviceData={setPhysicalDeviceData}
                        />
                    )
                };
            }
            return {
                ...step,
                content: (
                    <step.content.type
                        submitValid={handleFormValid}
                        topoInfo={topoInfo}
                        setTopoInfo={setTopoInfo}
                        physicalDeviceData={physicalDeviceData}
                        setPhysicalDeviceData={setPhysicalDeviceData}
                    />
                )
            };
        });
        setSteps(newSteps);
    };

    const handleFormValid = (title, topoData, checkStatus, pageData) => {
        // console.log(title, topoData, checkStatus);
        if (title === "Deploy") {
            setDeploymentData(pageData);
        }
        setIsContinuing(checkStatus);
        setTopoInfo(topoData);
    };

    const [steps, setSteps] = useState([
        {
            title: "Allocate Device",
            content: (
                <DeviceAllocationStep
                    submitValid={handleFormValid}
                    topoInfo={topoInfo}
                    setTopoInfo={setTopoInfo}
                    physicalDeviceData={physicalDeviceData}
                    setPhysicalDeviceData={setPhysicalDeviceData}
                />
            )
        },
        {
            title: "Confirm Configuration",
            content: (
                <ConfigurationConfirmStep
                    submitValid={handleFormValid}
                    topoInfo={topoInfo}
                    setTopoInfo={setTopoInfo}
                    physicalDeviceData={physicalDeviceData}
                    setPhysicalDeviceData={setPhysicalDeviceData}
                />
            )
        },
        {
            title: "Check Topo",
            content: (
                <TopoCheckStep
                    submitValid={handleFormValid}
                    topoInfo={topoInfo}
                    setTopoInfo={setTopoInfo}
                    physicalDeviceData={physicalDeviceData}
                    setPhysicalDeviceData={setPhysicalDeviceData}
                />
            )
        },
        {
            title: "Deploy",
            content: (
                <DeploymentStep
                    submitValid={handleFormValid}
                    topoInfo={topoInfo}
                    setTopoInfo={setTopoInfo}
                    physicalDeviceData={physicalDeviceData}
                    setPhysicalDeviceData={setPhysicalDeviceData}
                />
            )
        }
    ]);

    const next = () => {
        if (isContinuing) {
            setCurrent(current + 1);
        }
    };
    const allocate_device_apply = async topoData => {
        setIsContinuing(false);
        setIsLoading(true);
        const routedInterfaceAddressPool = deviceAllocationStepRef?.current?.getRoutedInterfaceAddressPool();
        const topoDataCopy = JSON.parse(JSON.stringify(topoData));
        try {
            const data = {
                fabric_name: state.data.fabric_name,
                template_name: state.data.template_name,
                fabric_config: topoDataCopy.fabric_config
            };
            const result = await editFabric(data);
            if (result.status === 200) {
                try {
                    const data2 = {
                        fabric_topo_id: state.data.id,
                        routed_interface_address_pool: routedInterfaceAddressPool
                    };
                    if (data2.routed_interface_address_pool) {
                        const result = await allocate_resourced(data2);
                        if (result.status === 200) {
                            setIsContinuing(true);
                            if (current === steps.length - 1) message.success(result.info);
                            next();
                        } else {
                            message.error(result.info);
                            setIsContinuing(false);
                        }
                    }
                } catch (error) {
                    console.error("Error fetching data:", error);
                } finally {
                    setIsLoading(false);
                }
            } else {
                if (current === steps.length - 1) message.error(result.info);
                setIsContinuing(false);
            }
        } catch (error) {
            console.error("Error fetching data:", error);
        } finally {
            setIsLoading(false);
        }
    };

    const apply = async topo => {
        setIsContinuing(false);
        setIsLoading(true);
        const data = {
            fabric_name: state.data.fabric_name,
            template_name: state.data.template_name,
            fabric_config: topo ? topo.fabric_config : topoInfo.fabric_config
        };
        if (data.fabric_config) {
            try {
                const result = await editFabric(data);
                if (result.status === 200) {
                    setIsContinuing(true);
                    setIsLoading(false);
                    if (current === steps.length - 1) message.success(result.info);
                    if (steps[current].title !== "Check Topo" && steps[current].title !== "Allocate Device") {
                        next();
                    }
                } else {
                    if (current === steps.length - 1) message.error(result.info);
                    setIsContinuing(false);
                    setIsLoading(false);
                }
            } catch (error) {
                console.error("Error fetching data:", error);
            } finally {
                setIsLoading(false);
            }
        }
    };

    const deployment = async () => {
        setIsLoading(true);
        try {
            const result = await fabric_deployment({fabric_topo_id: state.data.id});
            if (result.status === 200) {
                message.success(result.info);
            } else {
                message.error(result.info);
            }
        } catch (error) {
            console.error("Error fetching data:", error);
        } finally {
            setIsLoading(false);
        }
    };

    const getTopoInfo = async () => {
        try {
            const res = await viewFabric({fabric_topo_id: state.data.id});
            if (res.status === 200) {
                setTopoInfo(res.data);
                setUnit(res.data?.fabric_config?.unit);
                setPod(res.data?.fabric_config?.pod);
                setFabricTopoStatus(res.data.status);
            }
        } catch (error) {
            // error
        }
    };

    useEffect(() => {
        getPhysicalDevice();
        getTopoInfo();
    }, [state]);

    useEffect(() => {
        updateStepData();
        if (current === 1) setIsContinuing(true);
    }, [current]);

    const handleNodeClick = node => {
        deviceAllocationStepRef?.current?.showEditDeviceModal(node);
    };
    useEffect(() => {
        updateStepData();
    }, [topoInfo, physicalDeviceData]);

    return (
        <>
            <Spin spinning={isLoading} tip="Loading..." fullscreen />
            <Card style={{display: "flex", flex: 1, position: "relative", paddingTop: "12px"}}>
                <Row style={{alignItems: "center", marginBottom: "24px"}}>
                    <Col span={24}>
                        <Steps
                            className={styles.stepsItem}
                            current={current}
                            items={steps.map(item => ({
                                key: item.title,
                                title: item.title
                            }))}
                            size="small"
                        />
                    </Col>
                </Row>

                <div className={styles.editBox} style={{overflowY: "auto"}}>
                    <div className={styles.fabricTopo}>
                        <FabricTopo topoInfo={topoInfo} onNodeClick={handleNodeClick} />
                    </div>
                    <div className={styles.fabricContent}>{steps[current]?.content}</div>
                </div>
                <div className={styles.button_box}>
                    <Button
                        onClick={() => {
                            if (current === 0) {
                                navigate(-1);
                            } else {
                                setCurrent(current - 1);
                            }
                        }}
                    >
                        Back
                    </Button>

                    {current !== steps.length - 1 && fabricTopoStatus === "Deployed" && (
                        <Button
                            type="primary"
                            onClick={async () => {
                                setCurrent(current + 1);
                            }}
                        >
                            Continue
                        </Button>
                    )}
                    {current !== steps.length - 1 && fabricTopoStatus !== "Deployed" && (
                        <Button
                            type="primary"
                            onClick={async () => {
                                if (isContinuing) {
                                    if (
                                        steps[current].title !== "Check Topo" &&
                                        steps[current].title !== "Allocate Device"
                                    ) {
                                        apply();
                                    } else if (steps[current].title === "Allocate Device") {
                                        await allocate_device_apply(topoInfo);
                                    } else {
                                        next();
                                    }
                                }
                            }}
                            disabled={!isContinuing}
                        >
                            Continue
                        </Button>
                    )}
                    {current === steps.length - 1 && fabricTopoStatus !== "Deployed" && (
                        <Button type="primary" onClick={() => deployment()} disabled={!isContinuing}>
                            Apply
                        </Button>
                    )}
                </div>
            </Card>
        </>
    );
};

export default FabircDetail;
