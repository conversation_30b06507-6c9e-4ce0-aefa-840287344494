h3:nth-child(1) {
    margin: 0 0 24px 0;
}

h3 {
    font-size: 18px;
    margin: 40px 0 24px 0;
    font-weight: 700;
    color: #212519;
    text-align: left;
    font-style: normal;
    text-transform: none;
}

.pagesTitle {
    margin: 0 24px;
}

.secondaryTitle {
    font-size: 18px;
    margin: 8px 0;
}

.overflowP {
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

select {
    width: 280px;
}

.formWidth {
    width: 280px !important;
}

.fabricContent {
    width: 100%;
}

.fabricContent table {

}

// 影响其他组件的ul属性，Menu
ul {
    list-style: none;
    padding-left: 0;
}

.editBox {
    overflow-y: hidden;
    height: calc(100vh - 290px);
    margin-bottom: 32px;
}

.loading {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: #e7e6e68a;
    z-index: 1000;
}

.podContainerBox {
    margin-bottom: 24px;
}

.addNewPodBtn {
    width: 100%;
    height: 48px;
    border-radius: 4px 4px 4px 4px;
    border: 1px dotted #14C9BB;
    color: #14C9BB;
    display: block;
    background: none;
    cursor: pointer;
    margin-top: 24px;
}

.topology {
    width: calc(100% - 570px);
    min-width: 300px;
}

.button_box {
    width: calc(100% + 48px);
    display: flex;
    align-items: center;
    justify-content: flex-end;
    // position: absolute;
    // right: 0;
    // bottom: 0;
    // height: 68px;
    border-top: 1px solid #E7E7E7;
    padding: 20px 0 4px;
    background: #fff;
    margin-left: -24px;

    button {
        margin-left: -8px;
        margin-right: 24px;
    }
}

.button_box2 {
    width: calc(100% + 48px);
    display: flex;
    align-items: center;
    justify-content: flex-start;
    margin-left: -24px;

    button {
        margin-left: -8px;
        margin-right: 24px;
    }
}

.fabricInfo {
    display: block;

    ul {
        display: flex;
        flex-wrap: wrap;
        padding-left: 0;
    }

    li {
        list-style: none;
        width: 390px;
        height: 40px;
        line-height: 40px;
        padding: 0 24px;
        background: #F7F9F9;
        margin-bottom: 24px;
        display: inline-flex;
    }

    li:nth-child(2n-1) {
        margin-right: 12px;
    }

    span:first-of-type {
        color: #788389;
        display: inline-block;
        width: 120px;
    }

    span:last-of-type {
        color: #212519;
        display: inline-block;
        width: calc(100% - 145px);
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis
    }
}

.tabsBorder {
    // background: #FFFFFF;
    // border-radius: 8px;
    border-top: 1px solid #E7E7E7;
    padding: 24px 0px 0px 0px;
}

.successTag {
    color: #2BC174;
    background: rgba(43, 193, 116, 0.1);
    border-radius: 2px 2px 2px 2px;
    border: 1px solid #2BC174;
}

.failedTag {
    color: #F53F3F;
    background: rgba(245, 63, 63, 0.1);
    border-radius: 2px 2px 2px 2px;
    border: 1px solid #F53F3F;
}

.uncheckedTag {
    color: #B3BBC8;
    background: #F4F5F7;
    border-radius: 2px 2px 2px 2px;
    border: 1px solid #DADCE1;
}

.runningTag {
    color: #FFBB00;
    background: rgba(255, 187, 0, 0.1);
    border-radius: 2px 2px 2px 2px;
    border: 1px solid #FFBB00
}

.pendingTag {
    color: #B3BBC8;
    background: #F4F5F7;
    border-radius: 2px 2px 2px 2px;
    border: 1px solid #B3BBC8
}

.fabricTopo {
    width: 100%;
    height: 380px;
    background: #F8FAFB;
    border-radius: 4px 4px 4px 4px;
    margin-bottom: 24px;
    overflow: hidden;
    position: relative;
}

.stepsItem .ant-steps-item-icon {
    width: 20px !important;
    height: 20px !important;
    margin-top: 6px !important;
    line-height: 20px !important;
}

.stepsItem {
    [class*="ant-steps-item"] [class*="ant-steps-item-icon"] {
        color: #C4CDDB;
        border-color: #C4CDDB;
        background: #fff;
    }

    [class*="ant-steps-item-active"] {

        [class*="ant-steps-item-title"],
        [class*="ant-steps-item-icon"] span {
            color: #14C9BB !important;
        }

        [class*="ant-steps-item-icon"] {
            color: #14C9BB !important;
            border-color: #14C9BB !important;
        }
    }

    [class*="ant-steps-item-finish"] {
        [class*="ant-steps-item-title"] {
            color: #14C9BB !important;
        }

        [class*="ant-steps-item-icon"] span {
            color: #fff !important;
        }

        [class*="ant-steps-item-icon"] {
            border-color: #14C9BB !important;
            background-color: #14C9BB !important;

        }
    }
}

@media screen and (max-width: 1200px) {
    .editBox {
        overflow-y: auto;
    }

    .formBox {
        height: 100%;
        overflow-y: auto
    }
}

.errorTips {
    max-width: 350px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    color: red;
    margin: 0;
}

.azHoverbox {

    [class*="ant-tooltip-inner"] {
        padding: 16px;
        width: 240px;
        background: #FFFFFF;
        box-shadow: 0px 1px 12px 1px #E6E8EA;
        border-radius: 4px 4px 4px 4px;
    }

    .azHoverTitle {
        margin: 0;
        padding: 0;

        li {
            display: flex;
            align-items: center;
            justify-content: space-between;
            color: #212519;
            margin-bottom: 8px;

            span:first-of-type {
                color: #929A9E;
            }
        }
    }

}

.editNodeAdditionBox {
    // display: flex;
    background: #fff;
    position: relative;
    height: 98.6%;
    width: 100%;
    padding:24px 0 0 0;

    .editNodeAdditionContent {
        display: flex;
        flex-wrap: wrap;
        background: #fff;
        height: calc(100vh - 205px);
        overflow: hidden;
        padding: 12px 24px;

        .topologyGraph {
            width: calc(100% - 710px);
            // flex: 1;
            min-width: 500px;
            height: 700px;
            position: relative;
            background: #F8FAFB;
        }

        .nodesForm {
            // width: 800px;
            flex: 1;
            gap: 24px;
            padding-right: 24px;
            padding: 0 0 24px 0;
            margin-right: 24px;

            .nodeItem[class*="ant-form-item"] {
                width: 280px;
                display: inline-flex !important;
                margin-bottom: 16px;
            }

            .nodeItem[class*="ant-form-item"] {
                margin-right: 24px;
            }

            [class*="ant-row"] {
                display: flow;
            }

            .addSwitchPortGroupBtn {
                width: 100%;
                height: 48px;
                color: #14C9BB;
                border-radius: 4px 4px 4px 4px;
                border: 1px dotted #14C9BB;
                cursor: pointer;
                margin-bottom: 24px;
            }

            .addSwitchPortGroupBtn:hover {
                background-color: #E7F9F8;
            }

            .nodeLinkList {
                border: 1px dotted #DCDCDC;
                margin-top: 12px;
                margin-bottom: 12px;
                position: relative;
                padding: 0 24px 24px;

                .linkItem[class*="ant-form-item"] {
                    width: 280px;
                    display: inline-flex !important;
                    margin-bottom: 16px;
                }

                .linkItem[class*="ant-form-item"] {
                    margin-right: 24px;
                }

                .portGroupItem[class*="ant-form-item"] {
                    width: 450px;
                    display: inline-flex !important;
                }

                .groupHeader {
                    display: flex;
                    align-items: center;
                    justify-content: space-between;

                    p {
                        font-weight: 700;
                        font-size: 18px;
                        color: #212519;
                    }

                    .backUpLink:hover {
                        color: #14C9BB;
                    }

                    .deleteLink:hover {
                        color: #14C9BB;
                    }
                }
            }

            .nodePortGroup {
                .portGroupsItembox {
                    display: flex;
                    flex-wrap: wrap;
                }

                .portGroupItem[class*="ant-form-item"] {
                    margin-right: 80px;
                }
            }


        }
    }
}

@media (max-width: 1180px) {
    .editNodeAdditionBox {
        height: 77vh !important;
        overflow: auto;
        .editNodeAdditionContent {
            height: auto;
            overflow: auto;
            .topologyGraph{
                width: 100%;
            }
        }
    }
}

.IssueOperationBar {
    width: 100%;
    height: 68px;
    display: flex;
    align-items: center;
    justify-content: end;
    border-top: 1px solid #E7E7E7;
    background-color: #fff;
    position: absolute;
    bottom: 0;
    left: 0;
    z-index: 1000;

    Button {
        margin-left: -8px;
        margin-right: 24px;
    }

    Button:last-of-type {
        margin-right: 24px;
    }
}

.editVlanDomainBox {
    background: #fff;
    position: relative;
    height: 99.9%;

    .editCount {
        background: #fff;
        height: calc(100vh - 205px);
        overflow: hidden;
        overflow: auto;
        padding-bottom: 100px;
    }

    .IssueOperationBar {
        width: calc(100% + 48px);
        margin-left: -24px;
        height: 68px;
        display: flex;
        align-items: center;
        justify-content: end;
        border-top: 1px solid #E7E7E7;
        background-color: #fff;
        position: absolute;
        bottom: 0;
        left: 0;
        z-index: 1000;

        Button {
            margin-left: -8px;
            margin-right: 24px;
        }

        Button:last-of-type {
            margin-right: 24px;
        }
    }
}

.NICPortsli {
    margin-top: 0;
    display: flex;
    flex-wrap: wrap;
    gap: 4px;

    li {
        width: 28px !important;
        height: 24px !important;
        background: #e4e2e2;
        border-radius: 2px 2px 2px 2px;
        margin-right: 8px;

        span {
            width: 20px;
            height: 16px;
            font-size: 12px;
            margin: 3.5px;
            text-align: center;
            line-height: 14px;
            display: inline-block;
            border: 1px solid #fff;
            background: #e4e2e2;
            color: #Fff;
        }
    }

    .NICPortsliItemUP {
        background: #D0DC42;

        span {
            background: #D0DC42;
        }
    }
}

.customNodeForm {
    .btnGroup {
        Button {
            margin-right: 24px;
            margin-bottom: 24px;
        }
    }
}

.linkInfo {
    display: flex;
    flex-wrap: wrap;
    gap: 24px;

    li {
        width: 48%;
        height: 40px;
        padding: 0 16px;
        box-sizing: border-box;
        display: inline-flex;
        align-items: center;
        justify-content: space-between;
        background-color: #F8FAFB;
    }
}

.titleF18 {
    font-size: 18px;
    color: #212519;
    margin: 0 0 12px 0;
    font-weight: 600;
}

.nodeDetails_box {
    margin-top: 0;

    li {
        display: flex;
        height: 40px;
        color: #929A9E;
        align-items: center;
        padding: 0 24px;
        gap: 24px;

        div {
            flex: 1;
            overflow: hidden;
            text-wrap: wrap;
            display: inline-flex;
            align-items: center;

            span:first-of-type {
                display: inline-block;
                width: 140px;
                margin-right: 24px;
            }

            span:last-of-type {
                color: #212519;
                width: calc(100% - 160px);
                text-wrap: wrap;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }
        }
    }

    li:nth-child(2n - 1) {
        background-color: #F8FAFB;
    }
}

.select_switch_ports_box {
    li:nth-child(1) {
        width: 100%;

        .liITem {
            display: flex;
            flex-wrap: wrap;
            align-items: center;
            height: 40px;
            background: #F8FAFB;

            div {
                flex: 1;
                display: flex;
                padding: 0 24px;

                span:first-of-type {
                    margin-right: 32px;
                    color: #929A9E;
                }

                span:last-of-type {
                    color: #212519;
                }
            }

            .NICPortsli span {
                color: #Fff !important;
            }
        }
    }

    .select_ports_box {
        height: 112px;
        background: linear-gradient(180deg, #EEF1F6 0%, #E0E6EF 100%);
        border-radius: 2px 2px 2px 2px;
        overflow-x: auto;
        padding: 4px 40px;
        box-sizing: border-box;
        margin: 16px 0 32px 0;

        ul {
            display: flex;
            margin-bottom: 10px;

            li {
                width: 30px;
                text-align: center;
                margin-right: 8px;

                div {
                    width: 28px;
                    height: 24px;
                    background: #C1CBD8;
                    border-radius: 2px 2px 2px 2px;
                    cursor: pointer;
                }

                div::before {
                    content: '';
                    display: inline-block;
                    width: 20px;
                    height: 16px;
                    margin: 3px 3px;
                    border: 1px solid #fff;
                    border-radius: 1px;
                }

                .isUsed {
                    background: #D0DC42;
                }

                .isDisabled {
                    background: #8D98A7;
                }
            }

            li:nth-child(4n) {
                margin-right: 32px;
            }

            li:last-of-type {
                margin-right: 0
            }
        }

        ul:last-of-type {
            margin-bottom: 0;
        }
    }
}


.portTypeLegend {
    height: 50px;
    display: inline-flex;
}

.actionLink a {
    color: #14C9BB;
}

.actionLink a:hover {
    color: #34DCCF;
}

.goBack {
    font-weight: 500;
    font-size: 14px;
    color: #14C9BB;
    margin-bottom: 26px;
}

.goBackCursor {
    cursor: pointer;
}

.formItemsBox {
    display: flex;
    flex-flow: wrap;

    .nodeItem[class*="ant-form-item"] {
        margin-right: 60px;

        &:last-of-type {
            margin-right: 0;
        }
    }
}

.customizeSelect {

    .NodeTemplateItem {
        min-height: 160px;
        height: auto !important;
        position: relative;
        border-bottom: 1px solid #F2F2F2;
        border-radius: 0 !important;
        padding: 12px;

        li {
            p {
                height: 20px;
                line-height: 20px;
                margin: 0 0 8px 0;
                color: #929A9E
            }

            p:last-of-type {
                color: #212519;
            }
        }

        .NodeTemplateItemActive {
            position: absolute;
            right: 3px;
            top: 3px;

            .DeleteSvg:hover,
            .EditGreySvg:hover {
                color: #14c9bb !important;
                fill: #14c9bb !important;

                &:hover {
                    color: var(--icon-hover-color);
                }
            }

        }

        .portLegend {
            display: flex;
            flex-wrap: wrap;
        }

    }

    .NodeTemplateItem:hover {
        background: #F5F5F5 !important;

        .NodeTemplateItemActive {
            color: rgba(0, 0, 0, 0.88);
        }
    }

    .CustomNodeTemplate {
        color: #929A9E;
    }

    .CustomNodeTemplate:hover {
        color: #14c9bb;
    }

}

.portLegend {
    display: flex;
    flex-wrap: wrap;

    li {
        margin-right: 3px;
        margin-bottom: 3px;
        text-align: center;

        div {
            width: 16px;
            height: 16px;
            line-height: 16px;
            color: #fff;
            text-align: center;
            background: #FF8308;
            border-radius: 1px 1px 1px 1px;
            margin: 0 auto;
        }

        div.legend_1G {
            background: #FF8308;
        }

        div.legend_10G {
            background: #3E9CEB;
        }

        div.legend_25G {
            background: #F4B814;
        }

        div.legend_40G {
            background: #8971CD;
        }

        div.legend_100G {
            background: #9B57B8;
        }

        div.legend_200G {
            background: #C76BA5;
        }

        div.legend_400G {
            background: #D0DC42;
        }

        .legend_default {
            background: #B2B2B2;
        }
    }


}

.CustomNodeModal div {
    width: 24px !important;
    height: 24px !important;
    line-height: 24px !important;
}