import {Graph} from "@antv/x6";
import {MiniMap} from "@antv/x6-plugin-minimap";
import {useEffect, useRef} from "react";
import {calculateVertices, unitTopoLayout} from "@/utils/topo_layout_utils";
import DeviceBriefTooltip from "../unit_tooltip/device_brief_tooltip";
import LinkBriefTooltip from "../unit_tooltip/link_brief_tooltip";

const FabricTopo = ({topoInfo, onNodeClick = () => {}}) => {
    const containerRef = useRef(null);
    const graphRef = useRef(null);
    const miniMapContainerRef = useRef(null);
    const deviceBriefTooltipRef = useRef(null);
    const linkBriefTooltipRef = useRef(null);

    const isFabricTopo = true;

    // const updateAllEdgesVertices = () => {
    //     graphRef.current.getEdges().forEach(edge => {
    //         if (edge.id === "splitter1") return;
    //         const sourceNode = graphRef.current.getCellById(edge.store.data.source.cell);
    //         const targetNode = graphRef.current.getCellById(edge.store.data.target.cell);

    //         // 由于不渲染access节点，所以需要判断sourceNode和targetNode是否存在
    //         if (sourceNode === null || targetNode === null) return;
    //         edge.setVertices(calculateVertices(sourceNode, targetNode));
    //     });
    // };

    useEffect(() => {
        graphRef.current = new Graph({
            container: containerRef.current,
            width: containerRef.current.clientWidth,
            height: containerRef.current.clientHeight,

            grid: true,
            rotating: {
                enabled: true
            },
            panning: {
                enabled: true,
                eventTypes: ["leftMouseDown", "mouseWheel"]
            },
            interacting: {
                nodeMovable: false,
                edgeMovable: false
            },
            connecting: {
                connector: {
                    name: "smooth"
                }
            },
            mousewheel: {
                enabled: true,
                modifiers: "ctrl"
            }
        });

        if (topoInfo === undefined || topoInfo.fabric_config === undefined) return;
        const {super_spine_count: superSpineCount, spine_count: spineCount} = topoInfo.fabric_config;

        const {edges, nodes} = topoInfo.fabric_config.topology;

        graphRef.current.use(
            new MiniMap({
                container: miniMapContainerRef.current,
                width: 300,
                height: 180,
                scalable: true,
                scaling: 1, // 设置小地图的缩放比例（例如主图的20%）
                minScale: 0.1,
                maxScale: 1,
                graphOptions: {
                    async: true,
                    createCellView(cell) {
                        if (cell.isEdge()) {
                            return null;
                        }
                    }
                }
            })
        );

        const x = 100;
        const spineY = 200;
        const superSpineY = 100;
        const leafY = 300;
        const accessY = 400;

        const dottedFrameId = 1;
        const dottedFrameX = 70;
        const dottedFrameY = 270;
        const dottedFrameInterval = 40;

        const nodeHeight = 30;
        const nodeWidth = 100;
        const nodeInterval = 25;

        const spineNodesDetailedInfo = [];
        const superSpineNodesID = [];
        const leafNodesDetailedInfo = [];
        const accessNodesID = [];
        const dottedFrameInfo = [];

        let allLeafNodesCount = 0;

        let groupX = 100;
        let groupY = 300;

        let leafWidth = 0;

        const groupData = {};
        nodes?.forEach(node => {
            if (node.group !== "") {
                if (groupData[node.group] === undefined) {
                    groupData[node.group] = [];
                    groupData[node.group].push(node);
                } else {
                    groupData[node.group].push(node);
                }
            } else if (groupData.no_group === undefined) {
                groupData.no_group = [];
                groupData.no_group.push(node);
            } else {
                groupData.no_group.push(node);
            }
        });

        const groupKeys = Object.keys(groupData);
        let groupDataKeysCount = Object.keys(groupData).length;

        // 计算每个dottedFrame的宽度，并和间隙加起来，就是下面leaf整体的宽度
        groupKeys.forEach(key => {
            if (key === "no_group") {
                groupDataKeysCount -= 1;
                return;
            }
            const nodes = groupData[key];

            let dottedFrameWidth = 0;

            if (nodes?.length >= 4) {
                dottedFrameWidth += 4 * (nodeWidth + nodeInterval) + nodeInterval;
            } else {
                dottedFrameWidth += nodes.length * (nodeWidth + nodeInterval) + nodeInterval;
            }
            leafWidth += dottedFrameWidth;
        });

        leafWidth += (groupDataKeysCount - 1) * dottedFrameInterval;

        // 渲染no_group的节点
        const spineNodes = groupData?.no_group?.filter(node => node.type === "spine");

        if (spineNodes === undefined) {
            //
        } else {
            let spineWidth;
            if (spineNodes.length >= 8) {
                spineWidth = (leafWidth - nodeInterval * 7) / 8;
                if (spineWidth <= nodeWidth) {
                    spineWidth = nodeWidth;
                    leafWidth = spineWidth * 8 + nodeInterval * 7;
                }
            } else if (spineNodes.length > 0) {
                spineWidth = (leafWidth - nodeInterval * (spineNodes.length - 1)) / spineNodes.length;
                if (spineWidth <= nodeWidth) {
                    spineWidth = nodeWidth;
                    leafWidth = spineWidth * spineNodes.length + nodeInterval * (spineNodes.length - 1);
                }
            }

            groupY = spineY + Math.ceil(spineNodes.length / 8) * (nodeHeight + nodeInterval) + 1.5 * nodeInterval;

            spineNodes?.forEach((node, index) => {
                spineNodesDetailedInfo.push({
                    ...node,
                    id: node.logic_device,
                    shape: "rect",
                    hostname: node.node_info.hostname,
                    label: node.group === "" ? node.logic_device : node.logic_device.replace(`${node.group}_`, ""),
                    name: node.group === "" ? node.logic_device : node.logic_device.replace(`${node.group}_`, ""),
                    x: x + (index % 8) * (spineWidth + nodeInterval) - nodeInterval,
                    y: spineY + Math.floor(index / 8) * (nodeHeight + nodeInterval),
                    width: spineWidth,
                    height: nodeHeight,
                    type: "spine",
                    attrs: {
                        body: {
                            fill: "#90D7F9",
                            stroke: "transparent",
                            fillOpacity: 0.5,
                            rx: 4,
                            ry: 4
                        },
                        label: {
                            fill: "#0E7CB0 ",
                            fontSize: 14,
                            fontWeight: 500,
                            textAnchor: "middle",
                            verticalAnchor: "middle"
                        }
                    },
                    ports: {
                        groups: {
                            out: {
                                position: "bottom",
                                attrs: {
                                    circle: {
                                        r: 1,
                                        magnet: false,
                                        stroke: "none",
                                        strokeWidth: 1,
                                        fill: "none"
                                    }
                                }
                            }
                        },
                        items: [
                            {
                                id: "spinePort",
                                group: "out"
                            }
                        ]
                    },
                    zIndex: 1
                });
            });
        }

        let allDottedFrameWidth = leafWidth - (groupDataKeysCount - 1) * dottedFrameInterval;
        let rowLeafNodesCount = 0;

        groupKeys.forEach((key, index) => {
            if (key === "no_group") return;
            const nodes = groupData[key];
            const leafNodes = nodes?.filter(node => node.type === "leaf");
            // access节点暂时不渲染
            if (leafNodes.length >= 4) {
                allDottedFrameWidth -= 5 * nodeInterval;
                rowLeafNodesCount += 4;
            } else {
                allDottedFrameWidth -= (leafNodes.length + 1) * nodeInterval;
                rowLeafNodesCount += leafNodes.length;
            }
        });

        const leafNodeWidth = allDottedFrameWidth / rowLeafNodesCount;

        groupKeys.forEach((key, index) => {
            if (key === "no_group") return;
            const nodes = groupData[key];
            const leafNodes = nodes?.filter(node => node.type === "leaf");
            const accessNodes = nodes?.filter(node => node.type === "access"); // access节点暂时不渲染

            allLeafNodesCount += leafNodes.length;

            leafNodes?.forEach((node, index) => {
                const label =
                    node.node_info?.hostname === undefined || node.node_info?.hostname === ""
                        ? "no hostname"
                        : node.node_info.hostname;
                let textLength;
                let nodeLabel;
                if (leafNodeWidth - 10 > label.length * 8) {
                    textLength = label.length * 8;
                    nodeLabel = label;
                } else {
                    textLength = leafNodeWidth - 10;
                    nodeLabel = `${label.substring(0, Math.floor(leafNodeWidth / 8) - 3)}...`;
                }

                leafNodesDetailedInfo.push({
                    ...node,
                    id: node.logic_device,
                    label: nodeLabel,
                    name: node.logic_device,
                    hostname: node.node_info.hostname,
                    shape: "rect",
                    x: groupX + (index % 4) * (leafNodeWidth + nodeInterval),
                    y: groupY + Math.floor(index / 4) * (nodeHeight + nodeInterval),
                    width: leafNodeWidth,
                    height: nodeHeight,
                    ports: {
                        groups: {
                            out: {
                                position: "bottom",
                                attrs: {
                                    circle: {r: 1, magnet: false, stroke: "none", strokeWidth: 1, fill: "none"}
                                }
                            },
                            in: {
                                position: "top",
                                attrs: {
                                    circle: {r: 1, magnet: false, stroke: "none", strokeWidth: 1, fill: "none"}
                                }
                            }
                        },
                        items: [
                            {group: "out", id: "leafBottomPort"},
                            {group: "in", id: "leafTopPort"}
                        ]
                    },
                    attrs: {
                        body: {
                            fill: "#B0E6FF",
                            stroke: "transparent",
                            fillOpacity: 0.5,
                            rx: 4,
                            ry: 4
                        },
                        label: {
                            textLength,
                            fill: "#26A5E1",
                            fontSize: 14,
                            fontWeight: 500,
                            textAnchor: "middle"
                        }
                    }
                });
            });

            let dottedFrameWidth = 0;
            const dottedFrameHeight =
                Math.ceil(leafNodes.length / 4) * (nodeHeight + nodeInterval) + 1.5 * nodeInterval;

            if (nodes?.length >= 4) {
                dottedFrameWidth += 4 * (leafNodeWidth + nodeInterval) + nodeInterval;
            } else {
                dottedFrameWidth += nodes.length * (leafNodeWidth + nodeInterval) + nodeInterval;
            }

            dottedFrameInfo.push({
                id: `group_${index + 1}`,
                shape: "rect",
                label: key,
                x: groupX - nodeInterval,
                y: groupY - nodeInterval * 1.5,
                width: dottedFrameWidth,
                height: dottedFrameHeight,
                type: "dottedFrame",
                attrs: {
                    body: {
                        borderRadius: 4,
                        fill: "none",
                        stroke: "grey",
                        strokeWidth: 2,
                        strokeDasharray: "4,5"
                    },
                    label: {
                        refX: 10,
                        refY: 10,
                        textAnchor: "start",
                        textVerticalAnchor: "top"
                    }
                },
                zIndex: 1
            });
            groupX += dottedFrameWidth + dottedFrameInterval;
        });

        const edgesInfo = [];
        edges?.forEach(edge => {
            if (edge.type === "peer_link") {
                edge.link_info.forEach((link, index) => {
                    edgesInfo.push({
                        ...edge,
                        id: index,
                        source: edge.source,
                        target: edge.target,
                        sourcePort: link.source_port,
                        targetPort: link.target_port,
                        attrs: {
                            line: {
                                stroke: "#D8D8D8",
                                strokeWidth: 1.5,
                                targetMarker: null
                            }
                        },
                        zIndex: 0
                    });
                });
            } else {
                edgesInfo.push({
                    ...edge,
                    source: edge.source,
                    target: edge.target,
                    attrs: {
                        line: {
                            stroke: "#D8D8D8",
                            strokeWidth: 1.5,
                            targetMarker: null
                        }
                    },
                    zIndex: 0
                });
            }
        });

        graphRef.current.startBatch("renderTopo");
        try {
            graphRef.current.fromJSON({
                nodes: [...spineNodesDetailedInfo, ...leafNodesDetailedInfo, ...dottedFrameInfo],
                edges: edgesInfo
            });
        } finally {
            graphRef.current.stopBatch("renderTopo");
        }

        if (nodes?.length > 0) {
            //
        }
        // const leafNodes = nodes.filter(node => node.type === "leaf");
        // const spineNodes = nodes.filter(node => node.type === "spine");
        // const accessNodes = nodes.filter(node => node.type === "access");
        // const maxNodesCount = Math.max(leafNodes.length, accessNodes.length);
        // maxWidth = nodeInterval * (maxNodesCount - 1) + nodeWidth * maxNodesCount;
        // let autoNodeInterval;

        // if (accessNodes.length === maxNodesCount) {
        //     autoNodeInterval = (maxWidth - leafNodes.length * nodeWidth) / (leafNodes.length + 1);
        // } else {
        //     autoNodeInterval = (maxWidth - accessNodes.length * nodeWidth) / (accessNodes.length + 1);
        // }

        // leafNodes.forEach((node, index) => {
        //     const label = node.node_info.hostname === "" ? "no name" : node.node_info.hostname;
        //     let textLength;
        //     let nodeLabel;
        //     if (nodeWidth > label.length * 8) {
        //         textLength = label.length * 8;
        //         nodeLabel = label;
        //     } else {
        //         textLength = nodeWidth;
        //         nodeLabel = `${label.substring(0, Math.floor(nodeWidth / 8) - 3)}...`;
        //     }

        //     leafNodesID.push(
        //         graphRef.current.addNode({
        //             ...node,
        //             id: node.logic_device,
        //             label: nodeLabel,
        //             name: node.logic_device,
        //             hostname: node.node_info.hostname,
        //             shape: "rect",
        //             x:
        //                 leafNodes.length === maxNodesCount
        //                     ? x + (nodeWidth + nodeInterval) * index
        //                     : x + (autoNodeInterval + nodeWidth) * index + autoNodeInterval,
        //             y: leafY,
        //             width: nodeWidth,
        //             height: nodeHeight,
        //             ports: {
        //                 groups: {
        //                     out: {
        //                         position: "bottom",
        //                         attrs: {
        //                             circle: {r: 1, magnet: false, stroke: "none", strokeWidth: 1, fill: "none"}
        //                         }
        //                     },
        //                     in: {
        //                         position: "top",
        //                         attrs: {
        //                             circle: {r: 1, magnet: false, stroke: "none", strokeWidth: 1, fill: "none"}
        //                         }
        //                     }
        //                 },
        //                 items: [
        //                     {group: "out", id: "leafBottomPort"},
        //                     {group: "in", id: "leafTopPort"}
        //                 ]
        //             },
        //             attrs: {
        //                 body: {
        //                     fill: "#B0E6FF",
        //                     stroke: "transparent",
        //                     fillOpacity: 0.5,
        //                     rx: 4,
        //                     ry: 4
        //                 },
        //                 label: {
        //                     textLength,
        //                     fill: "#26A5E1",
        //                     fontSize: 14,
        //                     fontWeight: 500,
        //                     textAnchor: "middle"
        //                 }
        //             }
        //         }).id
        //     );
        // });

        //     accessNodes.forEach((node, index) => {
        //         const label = node.logic_device === "" ? "no name" : node.logic_device.replace(`${node.group}_`, "");
        //         let textLength;
        //         let nodeLabel;
        //         if (nodeWidth > label.length * 8) {
        //             textLength = label.length * 8;
        //             nodeLabel = label;
        //         } else {
        //             textLength = nodeWidth;
        //             nodeLabel = `${label.substring(0, Math.floor(nodeWidth / 8) - 3)}...`;
        //         }

        //         accessNodesID.push(
        //             graphRef.current.addNode({
        //                 ...node,
        //                 id: node.logic_device,
        //                 label: nodeLabel,
        //                 name: nodeLabel,
        //                 hostname: node.node_info.hostname,
        //                 shape: "rect",
        //                 x:
        //                     accessNodes.length === maxNodesCount
        //                         ? x + (nodeWidth + nodeInterval) * index
        //                         : x + (autoNodeInterval + nodeWidth) * index + autoNodeInterval,
        //                 y: accessY,
        //                 width: nodeWidth,
        //                 height: nodeHeight,
        //                 ports: {
        //                     groups: {
        //                         in: {
        //                             position: "top",
        //                             attrs: {
        //                                 circle: {r: 1, magnet: false, stroke: "none", strokeWidth: 1, fill: "none"}
        //                             }
        //                         }
        //                     },
        //                     items: [{group: "in", id: "accessPort"}]
        //                 },
        //                 attrs: {
        //                     body: {
        //                         fill: "#9B43FF",
        //                         fillOpacity: 0.1,
        //                         stroke: "transparent",
        //                         rx: 4,
        //                         ry: 4
        //                     },
        //                     label: {
        //                         textLength,
        //                         fill: "#9B43FF",
        //                         fontSize: 14,
        //                         fontWeight: 500,
        //                         textAnchor: "middle"
        //                     }
        //                 }
        //             }).id
        //         );
        //     });

        //     if (spineNodes.length !== 0) {
        //         spineNodes?.forEach((node, index) => {
        //             const spineWidth = (maxWidth - nodeInterval * (spineNodes.length - 1)) / spineNodes.length;
        //             spineNodesID.push(
        //                 graphRef.current.addNode({
        //                     ...node,
        //                     id: node.logic_device,
        //                     shape: "rect",
        //                     hostname: node.node_info.hostname,
        //                     label:
        //                         node.group === "" ? node.logic_device : node.logic_device.replace(`${node.group}_`, ""),
        //                     name:
        //                         node.group === "" ? node.logic_device : node.logic_device.replace(`${node.group}_`, ""),
        //                     x: x + (spineWidth + nodeInterval) * index,
        //                     y: spineY,
        //                     width: spineWidth,
        //                     height: nodeHeight,
        //                     type: "spine",
        //                     attrs: {
        //                         body: {
        //                             fill: "#90D7F9",
        //                             stroke: "transparent",
        //                             fillOpacity: 0.5,
        //                             rx: 4,
        //                             ry: 4
        //                         },
        //                         label: {
        //                             fill: "#0E7CB0 ",
        //                             fontSize: 14,
        //                             fontWeight: 500,
        //                             textAnchor: "middle",
        //                             verticalAnchor: "middle"
        //                         }
        //                     },
        //                     ports: {
        //                         groups: {
        //                             out: {
        //                                 position: "bottom",
        //                                 attrs: {
        //                                     circle: {
        //                                         r: 1,
        //                                         magnet: false,
        //                                         stroke: "none",
        //                                         strokeWidth: 1,
        //                                         fill: "none"
        //                                     }
        //                                 }
        //                             }
        //                         },
        //                         items: Array.from({length: leafNodes.length}, (_, index) => {
        //                             return {
        //                                 id: `spinePort-${index}`,
        //                                 group: "out"
        //                             };
        //                         })
        //                     },
        //                     zIndex: 1
        //                 }).id
        //             );
        //         });
        //     }

        //     if (superSpineCount !== "") {
        //         const superSpineWidth = (maxWidth - nodeInterval * (superSpineCount - 1)) / superSpineCount;
        //         for (let i = 1; i <= superSpineCount; i++) {
        //             superSpineNodesID.push(
        //                 graphRef.current.addNode({
        //                     id: `super_spine_${i}`,
        //                     shape: "rect",
        //                     label: `super_spine_${i}`,
        //                     name: `super_spine_${i}`,
        //                     x: x + (i - 1) * (nodeInterval + superSpineWidth),
        //                     y: superSpineY,
        //                     width: superSpineWidth,
        //                     height: nodeHeight,
        //                     type: "superSpine",
        //                     attrs: {
        //                         body: {
        //                             fill: "#23A7E5",
        //                             stroke: "transparent",
        //                             fillOpacity: 0.5,
        //                             rx: 4,
        //                             ry: 4
        //                         },
        //                         label: {
        //                             fill: "#FFFFFF",
        //                             fontSize: 14,
        //                             fontWeight: 500,
        //                             textAnchor: "middle",
        //                             verticalAnchor: "middle"
        //                         }
        //                     },
        //                     ports: {
        //                         groups: {
        //                             out: {
        //                                 position: "bottom",
        //                                 attrs: {
        //                                     circle: {
        //                                         r: 1,
        //                                         magnet: false,
        //                                         stroke: "none",
        //                                         strokeWidth: 1,
        //                                         fill: "none"
        //                                     }
        //                                 }
        //                             }
        //                         },
        //                         items: Array.from({length: spineNodes.length}, (_, index) => {
        //                             return {
        //                                 id: `superSpinePort-${index}`,
        //                                 group: "out"
        //                             };
        //                         })
        //                     },
        //                     zIndex: 1
        //                 }).id
        //             );
        //         }
        //     }
        // }

        graphRef.current.centerContent();

        graphRef.current.on("node:mouseenter", deviceInfo => {
            if (deviceInfo.node.store.data.type === "dottedFrame") return;
            deviceBriefTooltipRef.current.showDeviceBriefTooltip(deviceInfo.node.store.data);
        });

        graphRef.current.on("node:mouseleave", () => {
            deviceBriefTooltipRef.current.hideDeviceBriefTooltip();
        });

        graphRef.current.on("blank:mouseenter", () => {
            deviceBriefTooltipRef.current.hideDeviceBriefTooltip();
            linkBriefTooltipRef.current.hideLinkBriefTooltip();
        });

        graphRef.current.on("edge:mouseenter", ({edge}) => {
            linkBriefTooltipRef.current.showLinkBriefTooltip(edge.store.data);
        });

        graphRef.current.on("edge:mouseleave", () => {
            linkBriefTooltipRef.current.hideLinkBriefTooltip();
        });

        graphRef.current.on("node:click", node => {
            onNodeClick(node);
        });

        return () => {
            graphRef.current.dispose();
        };
    }, [topoInfo]);

    return (
        <>
            <div
                style={{
                    position: "relative",
                    flex: "1",
                    width: "100%",
                    height: "100%"
                }}
            >
                <div ref={containerRef} style={{width: "100%", height: "100%"}} />
                <div
                    ref={miniMapContainerRef}
                    style={{
                        position: "absolute",
                        right: 0,
                        bottom: 0,
                        zIndex: 999,
                        width: "300px",
                        height: "180px",
                        backgroundColor: "#f0f0f0"
                    }}
                />
            </div>
            <DeviceBriefTooltip ref={deviceBriefTooltipRef} offsetX={10} offsetY={10} topoType="fabric" />
            <LinkBriefTooltip ref={linkBriefTooltipRef} offsetX={10} offsetY={10} isFabricTopo />
        </>
    );
};

export default FabricTopo;
