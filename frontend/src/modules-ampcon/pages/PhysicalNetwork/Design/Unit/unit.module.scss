h3 {
    font-size: 18px;
    margin: 40px 0 24px 0;
    font-weight: 700;
    color: #212519;
    text-align: left;
    font-style: normal;
    text-transform: none;
}

.contentBox {
    border: 1px dashed #DCDCDC;
    width: 632px;
    // height: 328px; 
    min-height: 200px;
    padding: 24px;
    display: flex;
    flex-wrap: wrap;
    column-gap: 12px;
    margin-bottom: 16px;
}

.unitComponent [class*="ant-tabs-nav"] {
    background-color: transparent;
    margin: 0;
}
.unitComponent [class*="ant-tabs-nav"]::before{
    border: none !important;
}

.unitComponent [class*="ant-tabs-tab"] {
    padding: 8px 0 !important;
    background: #fff !important;
    border: none !important;
}

.unitComponent [class*="ant-tabs-tabpane"] {
    padding: 10px 0 !important;
}

.unitComponent [class*="ant-tabs-tab"]+[class*="ant-tabs-tab"]:not([class*="ant-tabs-tabpane"]) {
    margin: 0 0 0 32px !important;
}

.unitComponent [class*="ant-tabs-ink-bar"] {
    top: auto !important;
}

.viewContainer {
    height: 70vh;

    & div>div {
        display: flex;

        &>div {
            width: 17vw;
            height: 40px;
            line-height: 40px;
            background: #F7F9F9;
            padding: 0 24px;

            &>span+span {
                margin-left: 24px;
            }

            &+div {
                margin-left: 12px;
            }
        }
    }
}

.unitCard {
    display: flex;
    flex: 1;
    position: relative;
}

.button_box {
    position: absolute;
    width: 100%;
    right: 0;
    bottom: 0;
    padding: 16px 0;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    border-top: 1px solid #E7E7E7;
    background: #fff;
}

.selectCreateBtn {
    border: 1px solid #B2B2B2;
    color: #B2B2B2;
    width: 91%;
    margin: 8px 12px;
    background: #fff;
}

.selectCreateBtn:hover {
    background: rgba(20, 201, 187, 0.1) !important;
}
.requiredIcon1{
    color: rgb(245, 63, 63);
    font-size: 23px;
    vertical-align: middle;
    height: 23px;
    margin-left: 3px;
}
