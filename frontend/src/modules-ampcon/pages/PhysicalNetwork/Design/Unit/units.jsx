import React, {useEffect, useState, useRef} from "react";
import {Space, Table, Button, Form, Card, Tooltip, message, Modal, Divider, Row, Input} from "antd";
import Icon from "@ant-design/icons";
import {
    TableFilterDropdown,
    handleTableChange,
    createMatchMode,
    createColumnConfig,
    GlobalSearchInput,
    createFilterFields
} from "@/modules-ampcon/components/custom_table";
import {useTableInitialElement} from "@/modules-ampcon/hooks/useModalTable";
import {addSvg} from "@/utils/common/iconSvg";
import {confirmModalAction} from "@/modules-ampcon/components/custom_modal";
import {fetchUnitListInfo, cloneUnitInfo, delUnitInfo} from "@/modules-ampcon/apis/unit_api";
import {useNavigate} from "react-router-dom";
import styles from "@/modules-ampcon/pages/Topo/Topology/topo.module.scss";

const TopoUnit = ({setComponent}) => {
    const navigate = useNavigate();
    const [
        selectModalOpen,
        setSelectModalOpen,
        searchFields,
        setSearchFields,
        data,
        setData,
        loading,
        setLoading,
        pagination,
        setPagination
    ] = useTableInitialElement([], true);

    const [cloneModalOpen, setCloneModalOpen] = useState(false);
    const [cloneData, setCloneData] = useState({});

    const checkSortedColumn = columns => {
        for (const columnKey in columns) {
            if (Object.prototype.hasOwnProperty.call(columns, columnKey)) {
                const columnConfig = columns[columnKey];
                if (columnConfig.defaultSortOrder !== null) {
                    return [columnConfig.dataIndex, columnConfig.defaultSortOrder];
                }
            }
        }
        return [undefined, undefined];
    };
    const [sorter, setSorter] = useState({});
    const [filters, setFilters] = useState({});
    const [metaData, setMetaData] = useState([]);
    const tableRef = useRef(null);

    const fetchData = async () => {
        setLoading(true);

        const filterFields = filters ? createFilterFields(filters, matchModes) : [];
        const sortFields = [];
        if (sorter.field && sorter.order) {
            sortFields.push({
                field: sorter.field,
                order: sorter.order === "ascend" ? "asc" : "desc"
            });
        }

        try {
            const response = await fetchUnitListInfo(
                pagination.current,
                pagination.pageSize,
                filterFields,
                sortFields,
                searchFields
            );

            setData(response.data);
            setMetaData(response.data);
            setPagination(prev => ({
                ...prev,
                total: response.total,
                current: response.page,
                pageSize: response.pageSize
            }));
        } catch (error) {
            // error
        } finally {
            setLoading(false);
        }
    };

    const clone_unit = async formData => {
        const ret = await cloneUnitInfo(formData);
        if (ret.status === 200) {
            message.success(ret.info);
            setCloneModalOpen(false);
        } else {
            message.error(ret.info);
        }
        await fetchData();
    };

    const del_topo = async record => {
        const response = await delUnitInfo(record.id);
        if (response.status === 200) {
            message.success(response.info);
            if (data.length === 1) {
                pagination.current = pagination.current === 1 ? 1 : pagination.current - 1;
            }
        } else {
            message.error(response.info);
        }
        await fetchData();
    };

    useEffect(() => {
        fetchData().then(() => {
            const [sortedColumn, sortedOrder] = checkSortedColumn(tableColumns);
            if (sortedColumn) {
                sorter.field = sortedColumn;
                sorter.order = sortedOrder;
                tableChange("", "", sorter);
            }
        });
    }, []);

    useEffect(() => {
        setData(metaData.filter(item => item.name.includes(searchFields.value)).filter(item => item));
        // fetchData().then();
    }, [searchFields]);

    const addTopo = () => {
        navigate(`/physical_network/design/units/create`, {state: {actionType: "Add"}});
    };

    const editTopo = record => {
        navigate(`/physical_network/design/units/${record.name}`, {state: {actionType: "Edit", data: record}});
    };

    const handleSearchChange = e => {
        setSearchFields({
            fields: ["name", "email"],
            value: e.target.value
        });
    };

    const tableColumns = [
        createColumnConfig("Unit Name", "name"),
        {
            title: "Description",
            dataIndex: "description",
            width: 200,
            render: (_, record) =>
                record?.description.length > 50 ? (
                    <Tooltip placement="bottom" title={record.description}>
                        <p style={{maxWidth: 200, overflow: "hidden", textOverflow: "ellipsis"}}>
                            {record.description}
                        </p>
                    </Tooltip>
                ) : (
                    <span>{record.description}</span>
                )
        },
        {title: "Leaf Count", dataIndex: "leaf_count"},
        {title: "MLAG Count", dataIndex: "mlag_count"},
        // {title: "Access Count", dataIndex: "access_count", width: 200},
        {
            title: "Operation",
            width: 240,
            render: (_, record) => (
                <Space size="middle" className={styles.actionLink}>
                    <a onClick={() => editTopo(record)}>Edit</a>
                    <a
                        onClick={() =>
                            navigate(`/physical_network/design/units/${record.name}`, {
                                state: {actionType: "View", data: record}
                            })
                        }
                    >
                        View
                    </a>
                    <a
                        onClick={() => {
                            setCloneModalOpen(true);
                            setCloneData({
                                ...record,
                                description: ""
                            });
                        }}
                    >
                        Copy
                    </a>
                    <a onClick={() => confirmModalAction("Are you sure want to delete?", () => del_topo(record))}>
                        Delete
                    </a>
                </Space>
            )
        }
    ];

    const matchModes = createMatchMode([
        {name: "name", matchMode: "exact"},
        {name: "email", matchMode: "fuzzy"},
        {name: "ctime", matchMode: "fuzzy"}
    ]);

    const tableChange = async (pagination, filters, sorter) => {
        setSorter(sorter);
        setFilters(filters);
        await handleTableChange(
            pagination,
            filters,
            sorter,
            setPagination,
            searchFields,
            fetchUnitListInfo,
            "",
            setData,
            matchModes,
            setLoading
        );
    };

    return (
        <Card style={{display: "flex", flex: 1}}>
            <h2 style={{margin: "8px 0 20px"}}>Units</h2>
            <Space size={16} style={{marginBottom: "20px"}}>
                <Button type="primary" block onClick={() => addTopo()}>
                    <Icon component={addSvg} />
                    Unit
                </Button>
            </Space>
            <GlobalSearchInput onChange={handleSearchChange} />
            <div>
                <Table
                    ref={tableRef}
                    columns={tableColumns}
                    bordered
                    rowKey={record => record.id}
                    loading={loading}
                    dataSource={data}
                    pagination={pagination}
                    onChange={tableChange}
                />
            </div>

            <CloneModal
                title="Copy Unit"
                cloneModalOpen={cloneModalOpen}
                setCloneModalOpen={setCloneModalOpen}
                onCancel={() => setCloneModalOpen(false)}
                cloneData={cloneData}
                onSubmit={clone_unit}
                modalClass="ampcon-middle-modal"
            />
        </Card>
    );
};

const CloneModal = ({
    title,
    cloneModalOpen,
    // setCloneModalOpen,
    modalClass,
    cloneData,
    onCancel,
    onSubmit
}) => {
    const [form] = Form.useForm();

    useEffect(() => {
        if (cloneData) {
            form.setFieldsValue({
                clone_unit_id: cloneData.id,
                name: cloneData.name,
                description: cloneData.description
            });
        }
    }, [cloneData, form]);

    const handleOk = async () => {
        const formData = await form.validateFields();
        await onSubmit(formData);
    };

    return (
        <Space>
            <Modal
                title={
                    <div>
                        {title}
                        <Divider style={{marginTop: 8, marginBottom: 0}} />
                    </div>
                }
                open={cloneModalOpen}
                onCancel={onCancel}
                className={modalClass}
                footer={
                    <>
                        <Divider style={{marginBottom: "20px", marginTop: "0px"}} />
                        <Row justify="end">
                            <Space>
                                <Button onClick={onCancel}> Cancel </Button>
                                <Button type="primary" onClick={handleOk}>
                                    Apply
                                </Button>
                            </Space>
                        </Row>
                    </>
                }
            >
                <Form form={form} labelAlign="left" style={{minHeight: "267.23px"}}>
                    <Form.Item name="clone_unit_id" label="Clone ID" labelCol={{style: {width: 110}}}>
                        <Input placeholder="Clone ID" disabled className={styles.formWidth} />
                    </Form.Item>

                    <Form.Item
                        name="name"
                        label="Unit Name"
                        labelCol={{style: {width: 110}}}
                        rules={[
                            {required: true, message: "Please input Unit name!"},
                            {
                                validator: (_, value) => {
                                    if (!value || !/\s/.test(value)) {
                                        return Promise.resolve();
                                    }
                                    return Promise.reject(new Error("Unit name cannot contain spaces"));
                                }
                            }
                        ]}
                    >
                        <Input placeholder="Fabric Name" className={styles.formWidth} />
                    </Form.Item>

                    <Form.Item
                        name="description"
                        label="Description"
                        labelCol={{style: {width: 110}}}
                        rules={[
                            {
                                validator: (_, value) => {
                                    if (!value || /^[^\u4e00-\u9fa5]*$/.test(value)) {
                                        return Promise.resolve();
                                    }
                                    return Promise.reject(
                                        new Error("Please enter English, numbers, or English characters")
                                    );
                                }
                            }
                        ]}
                    >
                        <Input.TextArea
                            placeholder="Enter English, numbers, or English characters"
                            className={styles.formWidth}
                        />
                    </Form.Item>
                </Form>
            </Modal>
        </Space>
    );
};

export default TopoUnit;
