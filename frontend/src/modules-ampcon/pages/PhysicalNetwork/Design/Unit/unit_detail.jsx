import React, {useEffect, useState, useRef} from "react";
import {Space, Button, Form, Card, Input, InputNumber, Tabs, Select, Divider, message} from "antd";
import Icon, {ArrowLeftOutlined, PlusOutlined} from "@ant-design/icons";
import {addGreenSvg, deleteGreySvg, backUpGraySvg} from "@/utils/common/iconSvg";
import topoStyle from "./unit.module.scss";
import UnitTopo from "@/modules-ampcon/pages/PhysicalNetwork/Design/Unit/unit_topo";
import {useLocation, useNavigate} from "react-router-dom";
import {saveUnitInfo, fetchUnitDetailedInfo} from "@/modules-ampcon/apis/unit_api";
import {getResourcePoolDropdownList} from "@/modules-ampcon/apis/resource_pool_api";
import CreatePoolModal from "@/modules-ampcon/pages/Resource/Pool/IpPool/create_pool_modal";

// class LeafUnit extends Array {
//     toSpliced(index, delIndex, ...arg) {
//         return this.constructor(...Array.prototype.toSpliced.call(this, index, delIndex, ...arg));
//     }
// }
// class AccessUnit extends Array {
//     toSpliced(index, delIndex, ...arg) {
//         return this.constructor(...Array.prototype.toSpliced.call(this, index, delIndex, ...arg));
//     }
// }

const UnitForm = ({unitType, subForm, editDisabled, setAccessNodes, setLeafNodes}) => {
    const NAME_MATCH_REGEX = /^[\dA-Za-z-]+$/;
    const formItemCSS = {
        width: "280px",
        // height: "62px",
        marginBottom: "16px"
    };
    const createPoolModalRef = useRef(null);
    const fieldName = {LeafUnit: "leaf", AccessUnit: "access"}[unitType];
    const [IPV4Pool, setIPV4Pool] = useState();
    const getResourcePoolList = async () => {
        getResourcePoolDropdownList({poolTypeList: ["ipv4"]}).then(res => {
            if (res.status === 200) {
                setIPV4Pool(res.data);
            }
        });
    };
    useEffect(() => {
        if (fieldName === "leaf") {
            getResourcePoolList();
        }
    }, []);
    const [formItemNames, formItemLabels] = [
        {
            LeafUnit: ["name", "strategy", "mlag_peerlink_vlanid", "mlag_vlanid", "l3_interface"],
            AccessUnit: ["name", "link_description", "leaf", "access_method", "peer_leaf"]
        }[unitType],
        {
            LeafUnit: [
                <>
                    Leaf Name <span className={topoStyle.requiredIcon1}>*</span>
                </>,
                <>
                    Leaf Strategy <span className={topoStyle.requiredIcon1}>*</span>
                </>,
                <>
                    MLAG Peer-Link VLAN ID <span className={topoStyle.requiredIcon1}>*</span>
                </>,
                <>
                    MLAG VLAN ID <span className={topoStyle.requiredIcon1}>*</span>
                </>,
                <>
                    L3 Peer-Link Interface <span className={topoStyle.requiredIcon1}>*</span>
                </>
            ],
            AccessUnit: [
                <>
                    Access Name <span className={topoStyle.requiredIcon1}>*</span>
                </>,
                <>
                    Link Description <span className={topoStyle.requiredIcon1}>*</span>
                </>,
                <>
                    Leaf <span className={topoStyle.requiredIcon1}>*</span>
                </>,
                <>
                    Access Method <span className={topoStyle.requiredIcon1}>*</span>
                </>,
                <>
                    Peer Leaf <span className={topoStyle.requiredIcon1}>*</span>
                </>
            ]
        }[unitType]
    ];
    const validateName = (rule, value) => {
        const names = subForm
            .getFieldValue(fieldName)
            .map(item => {
                if (item.name === value) return item.name;
            })
            .filter(item => item); // Same Name detected
        if (names.length > 1) return Promise.reject(Error("It cannot be the same name"));
        return Promise.resolve();
    };
    const formRules = {
        LeafUnit: [
            [
                {
                    required: true,
                    message: "Please input your leaf name!"
                },
                {
                    max: 20,
                    message: "Enter a maximum of 20 characters"
                },
                {
                    validator: (_, value) => {
                        if (value === "All") {
                            return Promise.reject(new Error("Please input a valid leaf name!"));
                        }
                        if (value.trim() !== value) {
                            return Promise.reject(new Error("Leaf name should not have leading or trailing spaces."));
                        }
                        if (!NAME_MATCH_REGEX.test(value)) {
                            return Promise.reject(new Error("Leaf name can only contain letters, numbers, hyphen."));
                        }
                        return validateName(_, value);
                    }
                }
            ],
            [],
            [
                {
                    required: true,
                    message: "Please input your Peer-Link VLAN ID!"
                }
            ],
            [
                {
                    required: true,
                    message: "Please input your MLAG VLAN ID!"
                }
            ],
            [
                {
                    required: true,
                    message: "Please input your Peer-Link Interface!"
                }
            ]
        ],
        AccessUnit: [
            [
                {
                    required: true,
                    message: "Please input your Access name!"
                },
                {
                    max: 20,
                    message: "Enter a maximum of 20 characters"
                },
                {
                    validator: (_, value) => {
                        if (value === "All") {
                            return Promise.reject(new Error("Please input a valid access name!"));
                        }
                        if (value.trim() !== value) {
                            return Promise.reject(new Error("Access name should not have leading or trailing spaces."));
                        }
                        if (!NAME_MATCH_REGEX.test(value)) {
                            return Promise.reject(new Error("Access name can only contain letters, numbers, hyphen."));
                        }
                        return Promise.resolve();
                    }
                }
            ],
            [],
            [
                {
                    required: true,
                    message: "Please input your leaf name!"
                }
            ],
            [],
            [
                {
                    required: true,
                    message: "Please input your Peer Leaf!"
                }
            ]
        ]
    }[unitType];
    const initUnitData = {
        LeafUnit: {
            // key: crypto.randomUUID(),
            name: "",
            strategy: "MLAG", // none || mlag
            mlag_peerlink_vlanid: "3966", // 多个vlan用分号隔开
            mlag_vlanid: "",
            l3_interface: ""
        },
        AccessUnit: {
            // key: crypto.randomUUID(),
            name: "",
            link_description: "",
            leaf: "",
            access_method: "Single-Homed", // single|| dual
            peer_leaf: "" // None || leaf_node_name
        }
    }[unitType];
    const addItem = () => {
        const fieldValue = subForm.getFieldValue(fieldName) || [];

        subForm.setFieldValue(fieldName, [...fieldValue, initUnitData]);

        if (fieldName === "leaf") {
            setLeafNodes([...fieldValue, initUnitData]);
        }
        if (fieldName === "access") {
            setAccessNodes([...fieldValue, initUnitData]);
        }
    };
    const copyItem = index => {
        const fieldValue = subForm.getFieldValue(fieldName);

        const newArray = [
            ...fieldValue,
            {
                ...fieldValue[index],
                name: `${fieldValue[index].name}-copy`
            }
        ];

        subForm.setFieldValue(fieldName, newArray);

        if (fieldName === "leaf") {
            setLeafNodes(newArray);
        }
        if (fieldName === "access") {
            setAccessNodes(newArray);
        }
    };

    const delItem = index => {
        const fieldValue = subForm.getFieldValue(fieldName);

        const newArray = fieldValue.filter((_, i) => i !== index);

        subForm.setFieldValue(fieldName, newArray);

        if (fieldName === "leaf") {
            setLeafNodes(newArray);
        }
        if (fieldName === "access") {
            setAccessNodes(newArray);
        }
    };

    const handleFieldsChange = (changedFields, allFields) => {
        allFields.forEach(field => {
            if (field.name.length === 1 && field.name[0] === "leaf") {
                subForm.setFieldValue(field.name[0], field.value);
            }
            if (field.name.length === 1 && field.name[0] === "access") {
                subForm.setFieldValue(field.name[0], field.value);
            }
        });
        if (changedFields[0].name[2] === "name" || changedFields[0].name[2] === "peer_leaf") {
            allFields.forEach(field => {
                if (field.name.length === 1 && field.name[0] === "leaf") {
                    subForm.setFieldValue(field.name[0], field.value);
                    setLeafNodes(field.value);
                }
                if (field.name.length === 1 && field.name[0] === "access") {
                    subForm.setFieldValue(field.name[0], field.value);
                    setAccessNodes(field.value);
                }
            });
        }
        if (changedFields[0].name[2] === "leaf") {
            const leafObj = subForm.getFieldValue("leaf").filter(item => item.name === changedFields[0].value)[0];
            if (leafObj.strategy === "None") {
                const leafName = leafObj.name;
                subForm.setFieldValue(["access", changedFields[0].name[1], "access_method"], "Single-Homed");
                subForm.setFieldValue(["access", changedFields[0].name[1], "peer_leaf"], `${leafName}_1`);
                allFields.forEach(field => {
                    if (field.name.length === 1 && field.name[0] === "leaf") {
                        setLeafNodes(field.value);
                    }
                    if (field.name.length === 1 && field.name[0] === "access") {
                        subForm.getFieldValue("access").forEach((item, index) => {
                            if (item.leaf === leafName) {
                                field.value[index].access_method = "Single-Homed";
                                field.value[index].peer_leaf = `${leafName}_1`;
                                setLeafNodes(field.value);
                            }
                        });
                        setAccessNodes(field.value);
                    }
                });
            } else if (leafObj.strategy === "MLAG") {
                const accessMethod = subForm.getFieldValue("access")[changedFields[0].name[1]].access_method;
                if (accessMethod === "Single-Homed") {
                    const leafName = subForm.getFieldValue("access")[changedFields[0].name[1]].leaf;
                    subForm.setFieldValue(["access", changedFields[0].name[1], "peer_leaf"], `${leafName}_1`);
                    allFields.forEach(field => {
                        if (field.name.length === 1 && field.name[0] === "leaf") {
                            subForm.setFieldValue(field.name[0], field.value);
                            setLeafNodes(field.value);
                        }
                        if (field.name.length === 1 && field.name[0] === "access") {
                            subForm.setFieldValue(field.name[0], field.value);
                            field.value[changedFields[0].name[1]].peer_leaf = `${leafName}_1`;
                            setAccessNodes(field.value);
                        }
                    });
                } else if (accessMethod === "Dual-Homed") {
                    allFields.forEach(field => {
                        if (field.name.length === 1 && field.name[0] === "leaf") {
                            subForm.setFieldValue(field.name[0], field.value);
                            setLeafNodes(field.value);
                        }
                        if (field.name.length === 1 && field.name[0] === "access") {
                            subForm.setFieldValue(field.name[0], field.value);
                            setAccessNodes(field.value);
                        }
                    });
                }
            }
        } else if (changedFields[0].name[2] === "strategy" && changedFields[0].value === "None") {
            subForm.setFieldValue(["leaf", changedFields[0].name[1], "mlag_peerlink_vlanid"], "3966");
            subForm.setFieldValue(["leaf", changedFields[0].name[1], "l3_interface"], "");
            const leafName = subForm.getFieldValue("leaf")[changedFields[0].name[1]].name;
            subForm.getFieldValue("access").forEach((item, index) => {
                if (item.leaf === leafName) {
                    subForm.setFieldValue(["access", index, "access_method"], "");
                    subForm.setFieldValue(["access", index, "peer_leaf"], "");
                }
            });
            allFields.forEach(field => {
                if (field.name.length === 1 && field.name[0] === "leaf") {
                    field.value[changedFields[0].name[1]].mlag_peerlink_vlanid = "3966";
                    field.value[changedFields[0].name[1]].l3_interface = "";
                    setLeafNodes(field.value);
                }
                if (field.name.length === 1 && field.name[0] === "access") {
                    subForm.getFieldValue("access").forEach((item, index) => {
                        if (item.leaf === leafName) {
                            field.value[index].access_method = "";
                            field.value[index].peer_leaf = "";
                            setLeafNodes(field.value);
                        }
                    });
                    setAccessNodes(field.value);
                }
            });
        } else if (changedFields[0].name[2] === "strategy" && changedFields[0].value === "MLAG") {
            subForm.setFieldValue(["leaf", changedFields[0].name[1], "mlag_peerlink_vlanid"], "3966");
            allFields.forEach(field => {
                if (field.name.length === 1 && field.name[0] === "leaf") {
                    field.value[changedFields[0].name[1]].mlag_peerlink_vlanid = "3966";
                    setLeafNodes(field.value);
                }
                if (field.name.length === 1 && field.name[0] === "access") {
                    setAccessNodes(field.value);
                }
            });
        } else if (changedFields[0].name[2] === "access_method") {
            if (changedFields[0].value === "Dual-Homed") {
                subForm.setFieldValue(["access", changedFields[0].name[1], "peer_leaf"], "");
                allFields.forEach(field => {
                    if (field.name.length === 1 && field.name[0] === "leaf") {
                        setLeafNodes(field.value);
                    }
                    if (field.name.length === 1 && field.name[0] === "access") {
                        field.value[changedFields[0].name[1]].peer_leaf = "";
                        setAccessNodes(field.value);
                    }
                });
            } else if (changedFields[0].value === "Single-Homed") {
                const leafName = subForm.getFieldValue("access")[changedFields[0].name[1]].leaf;
                subForm.setFieldValue(["access", changedFields[0].name[1], "peer_leaf"], `${leafName}_1`);
                allFields.forEach(field => {
                    if (field.name.length === 1 && field.name[0] === "leaf") {
                        setLeafNodes(field.value);
                    }
                    if (field.name.length === 1 && field.name[0] === "access") {
                        field.value[changedFields[0].name[1]].peer_leaf = `${leafName}_1`;
                        setAccessNodes(field.value);
                    }
                });
            }
        }
    };
    return (
        <div style={{overflow: "auto", overflowX: "hidden", height: "calc(-480px + 100vh)", minHeight: "268px"}}>
            <div>
                {/* {formData.map((item, index) => ( */}
                <Form
                    // key={item.key}
                    form={subForm}
                    layout="vertical"
                    className="label-wrap"
                    style={{
                        display: "flex",
                        justifyContent: "space-evenly",
                        flexFlow: "wrap",
                        borderRadius: "4px",
                        width: "632px"
                    }}
                    onFieldsChange={handleFieldsChange}
                    disabled={editDisabled}
                >
                    <Form.List name={{LeafUnit: "leaf", AccessUnit: "access"}[unitType]}>
                        {fields => (
                            <>
                                {fields.map(({key, name, fieldKey, ...restField}, index) => (
                                    <div key={key} className={topoStyle.contentBox}>
                                        <h3
                                            style={{
                                                width: "100%",
                                                marginBottom: "24px",
                                                display: "flex",
                                                justifyContent: "space-between"
                                            }}
                                        >
                                            <span>
                                                {
                                                    {
                                                        LeafUnit: "Leaf",
                                                        AccessUnit: "Access"
                                                    }[unitType]
                                                }
                                            </span>
                                            {!editDisabled && (
                                                <span>
                                                    <Icon
                                                        component={backUpGraySvg}
                                                        onClick={() => {
                                                            copyItem(index);
                                                        }}
                                                    />
                                                    <Icon
                                                        component={deleteGreySvg}
                                                        onClick={() => delItem(index)}
                                                        style={{
                                                            marginLeft: "16px",
                                                            display:
                                                                subForm.getFieldValue(fieldName).length > 1
                                                                    ? "inline-flex"
                                                                    : "none"
                                                        }}
                                                    />
                                                </span>
                                            )}
                                        </h3>
                                        <Form.Item
                                            style={formItemCSS}
                                            name={[name, formItemNames[0]]}
                                            label={formItemLabels[0]}
                                            rules={formRules[0]}
                                            initialValue=""
                                            layout="vertical"
                                        >
                                            <Input maxLength={20} style={{width: "280px"}} />
                                        </Form.Item>
                                        <Form.Item
                                            name={[name, formItemNames[1]]}
                                            label={formItemLabels[1]}
                                            rules={formRules[1]}
                                            layout="vertical"
                                            style={formItemCSS}
                                        >
                                            {
                                                {
                                                    LeafUnit: (
                                                        <Select
                                                            style={{width: "280px"}}
                                                            options={[
                                                                {value: "None", label: "None"},
                                                                {value: "MLAG", label: "MLAG"}
                                                            ]}
                                                        />
                                                    ),
                                                    AccessUnit: <Input style={{width: "280px"}} />
                                                }[unitType]
                                            }
                                        </Form.Item>

                                        {fieldName === "leaf" &&
                                            subForm.getFieldValue("leaf")[index].strategy === "MLAG" && (
                                                <>
                                                    <Form.Item
                                                        name={[name, formItemNames[2]]}
                                                        label={formItemLabels[2]}
                                                        rules={(() => {
                                                            if (
                                                                (fieldName === "leaf" &&
                                                                    subForm.getFieldValue("leaf").length >= index + 1 &&
                                                                    subForm.getFieldValue("leaf")[index].strategy !==
                                                                        "None") ||
                                                                fieldName === "access"
                                                            ) {
                                                                return formRules[2];
                                                            }
                                                            return null;
                                                        })()}
                                                        layout="vertical"
                                                        style={formItemCSS}
                                                    >
                                                        {
                                                            {
                                                                LeafUnit: (
                                                                    <InputNumber
                                                                        min={2}
                                                                        max={3966}
                                                                        disabled
                                                                        placeholder="Range (2-3966)"
                                                                        // disabled={
                                                                        //     subForm.getFieldValue(fieldName)[index].strategy !==
                                                                        //         "MLAG" || editDisabled
                                                                        // }
                                                                        style={{width: 280}}
                                                                    />
                                                                ),
                                                                AccessUnit: (
                                                                    <Select
                                                                        style={{width: "280px"}}
                                                                        options={subForm
                                                                            .getFieldValue("leaf")
                                                                            ?.map(item => ({
                                                                                value: item.name,
                                                                                label: item.name
                                                                            }))}
                                                                    />
                                                                )
                                                            }[unitType]
                                                        }
                                                    </Form.Item>

                                                    {(() => {
                                                        return {
                                                            LeafUnit: null,
                                                            AccessUnit: (
                                                                <Form.Item
                                                                    name={[name, formItemNames[3]]}
                                                                    label={formItemLabels[3]}
                                                                    rules={(() => {
                                                                        if (
                                                                            fieldName === "leaf" &&
                                                                            subForm.getFieldValue("leaf")[index]
                                                                                .strategy !== "None"
                                                                        ) {
                                                                            return formRules[3];
                                                                        }
                                                                        return null;
                                                                    })()}
                                                                    layout="vertical"
                                                                    style={formItemCSS}
                                                                >
                                                                    <Select
                                                                        disabled={(() => {
                                                                            const f = subForm
                                                                                .getFieldValue("leaf")
                                                                                .filter(
                                                                                    item =>
                                                                                        item.name ===
                                                                                        subForm.getFieldValue(
                                                                                            fieldName
                                                                                        )[index].leaf
                                                                                );
                                                                            return (
                                                                                !(
                                                                                    f.length > 0 &&
                                                                                    f[0].strategy === "MLAG"
                                                                                ) || editDisabled
                                                                            );
                                                                        })()}
                                                                        style={{width: "280px"}}
                                                                        options={[
                                                                            {
                                                                                value: "Single-Homed",
                                                                                label: "Single-Homed"
                                                                            },
                                                                            {value: "Dual-Homed", label: "Dual-Homed"}
                                                                        ]}
                                                                    />
                                                                </Form.Item>
                                                            )
                                                        }[unitType];
                                                    })()}

                                                    <Form.Item
                                                        name={[name, formItemNames[4]]}
                                                        label={formItemLabels[4]}
                                                        rules={(() => {
                                                            if (
                                                                (fieldName === "leaf" &&
                                                                    subForm.getFieldValue("leaf")[index].strategy !==
                                                                        "None") ||
                                                                (fieldName === "access" &&
                                                                    subForm
                                                                        .getFieldValue("leaf")
                                                                        .filter(
                                                                            item =>
                                                                                item.name ===
                                                                                subForm.getFieldValue("access")[index]
                                                                                    .leaf
                                                                        ).strategy !== "None" &&
                                                                    subForm.getFieldValue("access")[index]
                                                                        .access_method === "Single-Homed")
                                                            ) {
                                                                return formRules[4];
                                                            }
                                                            return null;
                                                        })()}
                                                        layout="vertical"
                                                        style={(() => {
                                                            return {
                                                                ...formItemCSS,
                                                                flex: 1
                                                            };
                                                        })()}
                                                    >
                                                        {
                                                            {
                                                                LeafUnit: (
                                                                    <Select
                                                                        disabled={
                                                                            subForm.getFieldValue(fieldName)[index]
                                                                                .strategy !== "MLAG" || editDisabled
                                                                        }
                                                                        allowClear
                                                                        style={{width: 280}}
                                                                        dropdownRender={menu => (
                                                                            <>
                                                                                {menu}
                                                                                <Divider style={{margin: "8px 0"}} />
                                                                                <Button
                                                                                    type="text"
                                                                                    icon={
                                                                                        <PlusOutlined
                                                                                            display={
                                                                                                IPV4Pool?.ipv4
                                                                                                    ?.length >= 0
                                                                                            }
                                                                                        />
                                                                                    }
                                                                                    className={
                                                                                        topoStyle.selectCreateBtn
                                                                                    }
                                                                                    onClick={() => {
                                                                                        createPoolModalRef.current.showCreatePoolModal();
                                                                                    }}
                                                                                >
                                                                                    Add Pool
                                                                                </Button>
                                                                            </>
                                                                        )}
                                                                        options={IPV4Pool?.ipv4?.map(item => ({
                                                                            value: item.id,
                                                                            label: item.name
                                                                        }))}
                                                                    />
                                                                ),
                                                                AccessUnit: (
                                                                    <Select
                                                                        disabled={(() => {
                                                                            const f1 =
                                                                                subForm.getFieldValue(fieldName)[index]
                                                                                    .access_method === "Single-Homed";
                                                                            const f2 = subForm
                                                                                .getFieldValue("leaf")
                                                                                .filter(
                                                                                    item =>
                                                                                        item.name ===
                                                                                        subForm.getFieldValue(
                                                                                            fieldName
                                                                                        )[index].leaf
                                                                                );
                                                                            return (
                                                                                !(
                                                                                    f1 &&
                                                                                    f2.length > 0 &&
                                                                                    f2[0].strategy === "MLAG"
                                                                                ) || editDisabled
                                                                            );
                                                                        })()}
                                                                        style={{width: "280px"}}
                                                                        options={[
                                                                            {
                                                                                value: `${subForm.getFieldValue(fieldName)[index].leaf}_1`,
                                                                                label: `${subForm.getFieldValue(fieldName)[index].leaf}_1`
                                                                            },
                                                                            {
                                                                                value: `${subForm.getFieldValue(fieldName)[index].leaf}_2`,
                                                                                label: `${subForm.getFieldValue(fieldName)[index].leaf}_2`
                                                                            }
                                                                        ]}
                                                                    />
                                                                )
                                                            }[unitType]
                                                        }
                                                    </Form.Item>
                                                </>
                                            )}

                                        {/* <Form.Item
                                            name={[name, formItemNames[3]]}
                                            label={formItemLabels[3]}
                                            rules={formRules[3]}
                                            layout="vertical"
                                            style={formItemCSS}
                                        >
                                            {
                                                {
                                                    LeafUnit: (
                                                        <InputNumber
                                                            min={2}
                                                            max={3966}
                                                            placeholder="Range (2-3966)"
                                                            disabled={
                                                                subForm.getFieldValue(fieldName)[index].strategy !==
                                                                    "MLAG" || editDisabled
                                                            }
                                                            style={{width: 280}}
                                                        />
                                                    ),
                                                    AccessUnit: (
                                                        <Select
                                                            disabled={(() => {
                                                                const f = subForm
                                                                    .getFieldValue("leaf")
                                                                    .filter(
                                                                        item =>
                                                                            item.name ===
                                                                            subForm.getFieldValue(fieldName)[index].leaf
                                                                    );
                                                                return (
                                                                    !(f.length > 0 && f[0].strategy === "MLAG") ||
                                                                    editDisabled
                                                                );
                                                            })()}
                                                            style={{width: "280px"}}
                                                            options={[
                                                                {value: "Single-Homed", label: "Single-Homed"},
                                                                {value: "Dual-Homed", label: "Dual-Homed"}
                                                            ]}
                                                        />
                                                    )
                                                }[unitType]
                                            }
                                        </Form.Item> */}
                                    </div>
                                ))}
                            </>
                        )}
                    </Form.List>
                </Form>
                {/* ))} */}
            </div>
            {!editDisabled && (
                <Button
                    style={{
                        border: "1px dashed #14c9bb",
                        borderRadius: "4px",
                        width: "100%",
                        height: "48px"
                    }}
                    onClick={() => addItem()}
                    disabled={editDisabled}
                >
                    <Icon component={addGreenSvg} />
                    Add
                </Button>
            )}
            <CreatePoolModal
                ref={createPoolModalRef}
                saveCallback={() => {
                    getResourcePoolList();
                }}
            />
        </div>
    );
};

const TopoUnitDetail = () => {
    const location = useLocation();
    const navigate = useNavigate();
    const [actionType, setActionType] = useState(location?.state?.actionType || "View");
    const [form] = Form.useForm();
    const [subForm] = Form.useForm();
    const [accessNodes, setAccessNodes] = useState([]);
    const [leafNodes, setLeafNodes] = useState([]);
    const [editDisabled, setEditDisabled] = useState(actionType === "View");
    const [activeTab, setActiveTab] = useState("leaf");
    const [UNIT_ID, setUNIT_ID] = useState(location?.state?.data?.id || "");

    useEffect(() => {
        if (location.state.actionType === "Add") {
            subForm.setFieldsValue({
                leaf: location?.state?.data?.unit_info?.leaf || [
                    {
                        name: "",
                        strategy: "MLAG", // none || mlag
                        mlag_peerlink_vlanid: "3966", // 多个vlan用分号隔开
                        mlag_vlanid: "",
                        l3_interface: ""
                    }
                ],
                access: location?.state?.data?.unit_info?.access || [
                    {
                        name: "",
                        link_description: "",
                        leaf: "",
                        access_method: "Single-Homed", // single|| dual
                        peer_leaf: "" // None || leaf_node_name
                    }
                ]
            });
            setLeafNodes(subForm.getFieldValue("leaf"));
            setAccessNodes(subForm.getFieldValue("access"));
        } else {
            fetchUnitDetailedInfo({
                unit_id: UNIT_ID
            })
                .then(res => {
                    if (res.status !== 200) {
                        return Promise.reject(res.info);
                    }
                    form.setFieldsValue(res.data);
                    subForm.setFieldsValue({
                        leaf: res.data.unit_info?.leaf || [
                            {
                                name: "",
                                strategy: "MLAG", // none || mlag
                                mlag_peerlink_vlanid: "3966", // 多个vlan用分号隔开
                                mlag_vlanid: "",
                                l3_interface: ""
                            }
                        ],
                        access: res.data.unit_info?.access || [
                            {
                                name: "",
                                link_description: "",
                                leaf: "",
                                access_method: "Single-Homed", // single|| dual
                                peer_leaf: "" // None || leaf_node_name
                            }
                        ]
                    });
                    setLeafNodes(subForm.getFieldValue("leaf"));
                    setAccessNodes(subForm.getFieldValue("access"));
                })
                .catch(err => {
                    setActiveTab(err);
                });
        }
    }, []);

    const submitUnit = values => {
        try {
            subForm
                .validateFields()
                .then(subValues => {
                    subValues.leaf.forEach(item => {
                        item.mlag_peerlink_vlanid = "3966";
                    });
                    const payload = {
                        ...values,
                        unit_info: subValues
                    };
                    actionType === "Edit" && (payload.id = location.state?.data?.id || UNIT_ID);
                    saveUnitInfo(payload).then(res => {
                        if (res.status === 200) {
                            message.success(res.info);
                            navigate("/physical_network/design/units");
                        } else {
                            message.error(res.info);
                        }
                    });
                })
                .catch(err => {
                    setActiveTab(err.errorFields[0].name[0]);
                });
        } catch (err) {
            // console.log(err);
        }
    };
    return (
        <Card className={topoStyle.unitCard}>
            <div direction="vertical">
                <h2 style={{margin: "8px 0 20px"}}>
                    {{Add: "Create Unit", Edit: "Edit Unit", View: "View Unit"}[actionType]}
                </h2>
                <div style={{overflowY: "scroll", height: " calc(100vh - 264px)"}}>
                    <h3>Basic Info</h3>
                    <Form
                        layout="horizontal"
                        labelAlign="left"
                        // labelWrap
                        className="label-wrap"
                        form={form}
                        onFinish={submitUnit}
                        style={{display: "inline-flex", flexWrap: "wrap"}}
                        disabled={editDisabled}
                    >
                        <Form.Item
                            name="name"
                            label="Unit Name"
                            maxLength={20}
                            style={{marginRight: "80px"}}
                            labelCol={{style: {width: 97}}}
                            rules={[
                                {required: true, message: "Please input your unit name!"},
                                {max: 20, message: "Enter a maximum of 20 characters"},
                                {
                                    validator: (_, value) => {
                                        if (value === "All") {
                                            return Promise.reject(new Error("Please input a valid unit name!"));
                                        }
                                        if (value.trim() !== value) {
                                            return Promise.reject(
                                                new Error("Unit name should not have leading or trailing spaces.")
                                            );
                                        }
                                        if (!/^[\dA-Za-z-]+$/.test(value)) {
                                            return Promise.reject(
                                                new Error("Unit name can only contain letters, numbers, hyphen.")
                                            );
                                        }
                                        return Promise.resolve();
                                    }
                                }
                            ]}
                            initialValue=""
                            layout="inline"
                        >
                            <Input style={{width: "280px", marginRight: 50}} />
                        </Form.Item>
                        <Form.Item
                            name="description"
                            label="Description"
                            initialValue=""
                            layout="inline"
                            labelCol={{style: {width: 97}}}
                            rules={[{max: 128, message: "Enter a maximum of 128 characters"}]}
                        >
                            <Input style={{width: "280px"}} maxLength={128} />
                        </Form.Item>
                    </Form>
                    <div
                        style={{
                            display: "flex",
                            justifyContent: "space-between",
                            flexWrap: "wrap",
                            columnGap: "24px"
                        }}
                    >
                        <div
                            style={{
                                width: "632px",
                                height: "calc(100vh - 430px)",
                                minHeight: "350px",
                                marginBottom: "24px"
                            }}
                        >
                            <Tabs
                                activeKey={activeTab}
                                onChange={key => setActiveTab(key)}
                                rootClassName={topoStyle.unitComponent}
                                items={[
                                    {
                                        key: "leaf",
                                        label: "Leafs",
                                        forceRender: true,
                                        children: (
                                            <UnitForm
                                                unitType="LeafUnit"
                                                subForm={subForm}
                                                editDisabled={editDisabled}
                                                setAccessNodes={setAccessNodes}
                                                setLeafNodes={setLeafNodes}
                                            />
                                        )
                                    }
                                    // {
                                    //     key: "access",
                                    //     label: "Access Switches",
                                    //     forceRender: true,
                                    //     children: (
                                    //         <UnitForm
                                    //             unitType="AccessUnit"
                                    //             subForm={subForm}
                                    //             editDisabled={editDisabled}
                                    //             setAccessNodes={setAccessNodes}
                                    //             setLeafNodes={setLeafNodes}
                                    //         />
                                    //     )
                                    // }
                                ]}
                            />
                        </div>
                        <div
                            style={{
                                width: "calc(100% - 570px)",
                                height: "calc(100vh - 410px)",
                                minWidth: "300px",
                                minHeight: "350px",
                                flex: 1,
                                marginRight: "16px"
                            }}
                        >
                            <h3 style={{marginBottom: "10px", lineHeight: "51px"}}>Topology</h3>
                            <div
                                style={{
                                    width: "100%",
                                    height: "calc(100% - 62px)",
                                    maxHeight: "1000px",
                                    borderRadius: "5px",
                                    overflow: "hidden"
                                }}
                            >
                                <UnitTopo accessNodes={accessNodes} leafNodes={leafNodes} />
                            </div>
                        </div>
                    </div>
                </div>
                {/* <Divider style={{marginBottom: "0"}} /> */}
                <div className={topoStyle.button_box}>
                    <Button type="default" onClick={() => navigate(-1)}>
                        Cancel
                    </Button>
                    {!editDisabled && (
                        <Button type="primary" style={{margin: "0 16px"}} onClick={form.submit}>
                            Apply
                        </Button>
                    )}
                    {editDisabled && (
                        <Button
                            type="primary"
                            style={{margin: "0 16px"}}
                            onClick={() => {
                                setEditDisabled(false);
                                setActionType("Edit");
                            }}
                        >
                            Edit
                        </Button>
                    )}
                </div>
            </div>
        </Card>
    );
};

export default TopoUnitDetail;
