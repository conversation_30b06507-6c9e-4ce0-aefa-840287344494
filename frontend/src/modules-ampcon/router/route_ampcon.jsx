import AnsibleJobsList from "@/modules-ampcon/pages/Maintain/NetworkConfiguration/Automation/AnsibleJobsList/ansible_jobs_list";
import OtherDevices from "@/modules-ampcon/pages/Maintain/NetworkConfiguration/Automation/OtherDevices/other_devices";
import Playbook from "@/modules-ampcon/pages/Maintain/NetworkConfiguration/Automation/Playbooks/playbook";
import Schedule from "@/modules-ampcon/pages/Maintain/NetworkConfiguration/Automation/Schedules/schedule";
import UserManagement from "@/modules-ampcon/pages/System/user_management";
import SwitchView from "@/modules-ampcon/pages/Dashboard/SwitchView/switch_view";
import ConfigTemplateIndex from "@/modules-ampcon/pages/Service/Switch/ConfigTemplate/config_template_index";
import RoCESwitchManager from "../pages/Monitor/RoCE_Counts/Switch/Switch_manager";
import EventManager from "../pages/Monitor/Event/Event_manager";
import RoceConfigManager from "../pages/Service/Nics/RoceConfig/RoCE_Config_Manager";
import SwitchManager from "@/modules-ampcon/pages/Resource/AuthorityManagement/authority_manager";
import GroupManagement from "@/modules-ampcon/pages/Resource/AuthorityManagement/GroupManagement/group_management";
import SiteManagement from "@/modules-ampcon/pages/Resource/AuthorityManagement/SiteManagement/site_management";
import CLIConfiguration from "@/modules-ampcon/pages/Maintain/CliConfig/cli_config";
import DCSwitch from "@/modules-ampcon/pages/Service/Switch/switch";
import Switch from "@/modules-ampcon/pages/Service/Switch/Switch/switch";
import GlobalConfiguration from "@/modules-ampcon/pages/Service/Switch/GlobalConfiguration/global_configuration";
import SwitchConfiguration from "@/modules-ampcon/pages/Service/Switch/SwitchConfiguration/switch_configuration";
import ConfigFileView from "@/modules-ampcon/pages/Service/Switch/ConfigFileView/configfile_view";
import SwitchModel from "@/modules-ampcon/pages/Service/Switch/SwitchModel/switch_model";
import AmpConSystemConfig from "@/modules-ampcon/pages/Service/Switch/SystemConfig/system_config";
import SystemBackup from "@/modules-ampcon/pages/Maintain/NetworkConfiguration/SystemBackup/system_backup";
import ProtectedRoute from "@/modules-ampcon/utils/util";
import Alarms from "@/modules-ampcon/pages/Monitor/Alarm/Alarms/alarms";
import AlarmNotificationRules from "@/modules-ampcon/pages/Monitor/Alarm/AlarmNotificationRules/alarm_notification_rules";
import HistoricalAlarmEmailLogs from "@/modules-ampcon/pages/Monitor/Alarm/HistoricalAlarmEmailLogs/historical_alarm_email_logs";
import WiredClients from "@/modules-ampcon/pages/Monitor/WiredClients/wired_clients";
import LicenseView from "@/modules-ampcon/pages/System/SoftwareLicense/LicenseView/license_view";
import LicenseManagement from "@/modules-ampcon/pages/System/SoftwareLicense/LicenseManagement/license_management";
import LicenseLog from "@/modules-ampcon/pages/System/SoftwareLicense/LicenseLog/license_log";
import TelemetryView from "@/modules-ampcon/pages/Dashboard/Telemetry/telemetry_view";
import WirelessView from "@/modules-ampcon/pages/Dashboard/WirelessView/wireless_view";
import TopoEntrance from "@/modules-ampcon/pages/Topo/Topology/topo_entrance";
import TopoUnit from "@/modules-ampcon/pages/PhysicalNetwork/Design/Unit/units";
import TopoUnitDetail from "@/modules-ampcon/pages/PhysicalNetwork/Design/Unit/unit_detail";
import DCTemplates from "@/modules-ampcon/pages/PhysicalNetwork/Design/DCTemplate/dc_templates";
import DCTemplatesDetail from "@/modules-ampcon/pages/PhysicalNetwork/Design/DCTemplate/dc_templates_detail";
import FabricDetail from "@/modules-ampcon/pages/PhysicalNetwork/Design/FabricManagement/fabric_detail";
import CampusFabric from "@/modules-ampcon/pages/Topo/CampusFabric/campus_fabric_entrance";
import SwitchTelemetry from "@/modules-ampcon/pages/Service/Switch/switch_telemetry";
import IpPool from "@/modules-ampcon/pages/Resource/Pool/IpPool/ip_pool";
import AsnPool from "@/modules-ampcon/pages/Resource/Pool/AsnPool/asn_pool";
import VniPool from "@/modules-ampcon/pages/Resource/Pool/VniPool/vni_pool";
import NICsInventory from "../pages/Service/Nics/Inventory/nic_inventory";
import NICsMonitoring from "../pages/Service/Nics/Monitoring/nic_monitoring";
import DLB from "../pages/Monitor/Network/dlb";
// import SwitchPage from "../pages/Monitor/RoCE_Counts/Switch/Switch";
import RoceNicsMonitor from "../pages/Monitor/RoCE_Counts/Nics/roce_monitor";
import CampusFabricMLAGRecordView from "@/modules-ampcon/pages/Topo/CampusFabric/campus_fabric_mlag_record_view";
import CampusFabricIPCLOSRecordView from "../pages/Topo/CampusFabric/campus_fabric_ipclos_record_view";
import EmailSetting from "@/modules-ampcon/pages/System/EmailSetting/email_setting";
import HostDevices from "@/modules-ampcon/pages/Service/Hosts/Device/devices";
import HostInventory from "@/modules-ampcon/pages/Service/Hosts/Inventory/inventory";
import HostIndex from "@/modules-ampcon/pages/Service/Hosts/host_index";
import ServerPage from "../pages/Service/Nics/RoceConfig/server";
// import EventPage from "../pages/Monitor/Event/Event";
// import NICsModulesOverview from "../pages/Service/Nics/ModulesOverview/nic_modules_overview";
import NicsIndex from "../pages/Service/Nics/nics_index";
import DeviceProfileIndex from "../pages/Service/Switch/device_profile_index";
import OpticalModulesIndex from "@/modules-ampcon/pages/Monitor/Telemetry/OpticalModules/optical_modules_index";
import PerformanceStatisticsIndex from "@/modules-ampcon/pages/Monitor/Telemetry/Performancestatistics/performance_statistics_index";
import FabricManagement from "../pages/PhysicalNetwork/Design/FabricManagement/fabric_management";
import RoCEEasydeployManager from "../pages/PhysicalNetwork/RoCE/RoCEEasydeploy/roce_easydeploy_manager";
import AZPool from "@/pages/Resource/AZ/az_pool";
import AZDetail from "@/pages/Resource/AZ/az_detail";
import UpgradeManagementSwitch from "@/modules-ampcon/pages/Resource/UpgradeManagementSwitch/upgrade_management_switch";
import OpticalModules from "@/modules-ampcon/pages/Telemetry/Switch/optical_modules";
import LogicalNetworks from "@/modules-ampcon/pages/Service Provision/Logical Networks/logical_networks";
import LogicalNetworkDetails from "@/modules-ampcon/pages/Service Provision/Logical Networks/logical_network_details";
import LogicalSwitches from "@/modules-ampcon/pages/Service Provision/Logical Switches/logical_switches";
import LogicalSwitchDetail from "@/modules-ampcon/pages/Service Provision/Logical Switches/logical_switches_detail";
import LogicalRouters from "@/modules-ampcon/pages/Service Provision/Logical Routers/logical_routers";
import LogicalRouterDetail from "@/modules-ampcon/pages/Service Provision/Logical Routers/logical_router_detail";
import NetworkAccess from "@/modules-ampcon/pages/Service Provision/Network Access/network_access_entrance";
import SwitchTemplates from "../pages/Topo/SwitchTemplates/switch_templates_entrance";
import AZManager from "../pages/Resource/ResourceInterconnection/ResourceInterconnection_manager";
import VLANDomainDetails from "../pages/Resource/ResourceInterconnection/VlanDomain/vlanDomainDetails";
import CreateBareMetalNodeAddition from "../pages/Resource/ResourceInterconnection/NodeAddition/createBareMetalNodeAddition";
import CreateCloudNodeAddition from "../pages/Resource/ResourceInterconnection/NodeAddition/createCloudNodeAddition";
import LoadBalancingManager from "../pages/PhysicalNetwork/RoCE/LoadBalancing/LoadBalancing_manager";
import RoCEPoliciesManager from "../pages/PhysicalNetwork/RoCE/RoCEPolicies/RoCEPolicies_manager";

export const ampConRoute = [
    {
        path: "dashboard/switch_view",
        element: <ProtectedRoute component={SwitchView} />
    },
    {
        path: "maintain/network_config/automation/playbooks",
        element: <ProtectedRoute component={Playbook} />
    },
    {
        path: "maintain/network_config/automation/other_devices",
        element: <ProtectedRoute component={OtherDevices} />
    },
    {
        path: "maintain/network_config/automation/ansible_jobs_list/:type",
        element: <AnsibleJobsList />
    },
    {
        path: "maintain/network_config/automation/schedule",
        element: <ProtectedRoute component={Schedule} />
    },
    {
        path: "maintain/network_config/system_backup",
        element: <ProtectedRoute component={SystemBackup} />
    },
    {
        path: "maintain/cli_configuration",
        element: <ProtectedRoute component={CLIConfiguration} />
    },
    {
        path: "system/user_management",
        element: <ProtectedRoute component={UserManagement} />
    },
    {
        path: "system/email_settings",
        element: <ProtectedRoute component={EmailSetting} />
    },
    {
        path: "resource/auth_management/device_license_management/:type",
        element: <SwitchManager />
    },
    {
        path: "resource/auth_management/group_management",
        element: <ProtectedRoute component={GroupManagement} />
    },
    {
        path: "service/switch/config_template/:type",
        element: <ConfigTemplateIndex />
    },
    {
        path: "service/switch/switch",
        element: <ProtectedRoute component={Switch} />
    },
    {
        path: "service/switch/global_configuration",
        element: <ProtectedRoute component={GlobalConfiguration} />
    },
    {
        path: "service/switch/switch_configuration",
        element: <ProtectedRoute component={SwitchConfiguration} />
    },
    {
        path: "service/switch/config_file_view",
        element: <ProtectedRoute component={ConfigFileView} />
    },
    {
        path: "service/switch/switch_model",
        element: <ProtectedRoute component={SwitchModel} />
    },
    {
        path: "service/switch/system_management",
        element: <ProtectedRoute component={AmpConSystemConfig} />
    },
    {
        path: "dashboard/telemetry_dashboard",
        element: <ProtectedRoute component={TelemetryView} />
    },
    {
        path: "service/switch/:sn",
        element: <ProtectedRoute component={SwitchTelemetry} />
    },
    {
        path: "topo/topology",
        element: <ProtectedRoute component={TopoEntrance} />
    },
    {
        path: "topo/CampusFabric",
        element: <ProtectedRoute component={CampusFabric} />
    }
];

export const ampConDCRoute = [
    {
        path: "dashboard/switch_overview",
        element: <ProtectedRoute component={SwitchView} />
    },
    {
        path: "dashboard/telemetry_dashboard",
        element: <ProtectedRoute component={TelemetryView} />
    },
    {
        path: "maintain/automation/playbooks",
        element: <ProtectedRoute component={Playbook} />
    },
    {
        path: "maintain/automation/other_devices",
        element: <ProtectedRoute component={OtherDevices} />
    },
    {
        path: "maintain/automation/ansible_jobs/:type",
        element: <AnsibleJobsList />
    },
    {
        path: "maintain/automation/scheduler",
        element: <ProtectedRoute component={Schedule} />
    },
    {
        path: "maintain/system_backup",
        element: <ProtectedRoute component={SystemBackup} />
    },
    {
        path: "maintain/web_access",
        element: <ProtectedRoute component={CLIConfiguration} />
    },
    {
        path: "system/user_management",
        element: <ProtectedRoute component={UserManagement} />
    },
    {
        path: "system/email_settings",
        element: <ProtectedRoute component={EmailSetting} />
    },
    {
        path: "system/software_licenses/licenses_view",
        element: <ProtectedRoute component={LicenseView} />
    },
    {
        path: "system/software_licenses/licenses_management",
        element: <ProtectedRoute component={LicenseManagement} />
    },
    {
        path: "system/software_licenses/licenses_log",
        element: <ProtectedRoute component={LicenseLog} />
    },
    {
        path: "resource/auth_management/device_license_management/:type",
        element: <SwitchManager />
    },
    {
        path: "resource/auth_management/group_management",
        element: <ProtectedRoute component={GroupManagement} />
    },
    {
        path: "resource/auth_management/site_management",
        element: <ProtectedRoute component={SiteManagement} />
    },
    {
        path: "resource/device_licenses/:type",
        element: <SwitchManager />
    },
    {
        path: "/resource/group_management",
        element: <ProtectedRoute component={GroupManagement} />
    },
    {
        path: "resource/pools/ip_pools",
        element: <ProtectedRoute component={IpPool} />
    },
    {
        path: "resource/pools/asn_pools",
        element: <ProtectedRoute component={AsnPool} />
    },
    {
        path: "resource/pools/vni_pools",
        element: <ProtectedRoute component={VniPool} />
    },
    {
        path: "/resource/resource_interconnection/:type",
        element: <ProtectedRoute component={AZManager} />
    },
    {
        path: "resource/resource_interconnection/vlan_domain/:vlanDomainName",
        element: <ProtectedRoute component={VLANDomainDetails} />
    },
    {
        path: "resource/resource_interconnection/PoD/:az_name",
        element: <ProtectedRoute component={AZDetail} />
    },
    {
        path: "resource/resource_interconnection/node_addition/bare_metal/:fabric_name",
        element: <ProtectedRoute component={CreateBareMetalNodeAddition} />
    },
    {
        path: "resource/resource_interconnection/node_addition/cloud/:fabric_name",
        element: <ProtectedRoute component={CreateCloudNodeAddition} />
    },

    // {
    //     path: "service/config_template/:type",
    //     element: <ConfigTemplateIndex />
    // },
    // {
    //     path: "service/switch",
    //     element: <ProtectedRoute component={Switch} />
    // },
    // {
    //     path: "service/global_configuration",
    //     element: <ProtectedRoute component={GlobalConfiguration} />
    // },
    // {
    //     path: "service/switch_configuration",
    //     element: <ProtectedRoute component={SwitchConfiguration} />
    // },
    // {
    //     path: "service/config_file_view",
    //     element: <ProtectedRoute component={ConfigFileView} />
    // },
    // {
    //     path: "service/switch_model",
    //     element: <ProtectedRoute component={SwitchModel} />
    // },
    // {
    //     path: "service/system_configuration",
    //     element: <ProtectedRoute component={AmpConSystemConfig} />
    // },
    {
        path: "device/switches",
        element: <ProtectedRoute component={DCSwitch} />
    },
    {
        path: "/device/hosts/:type",
        element: <HostIndex />
    },
    {
        path: "device/switches/:sn",
        element: <ProtectedRoute component={SwitchTelemetry} />
    },
    {
        path: "/device/NICs/:type",
        element: <NicsIndex />
    },
    {
        path: "/device/device_profiles/:type",
        element: <DeviceProfileIndex />
    },
    {
        path: "/device/config_templates/:type",
        element: <ConfigTemplateIndex />
    },
    {
        path: "/service_provision/logical_networks",
        element: <ProtectedRoute component={LogicalNetworks} />
    },
    {
        path: "/service_provision/logical_networks/:logical_network_name",
        element: <ProtectedRoute component={LogicalNetworkDetails} />
    },
    {
        path: "/service_provision/logical_switches",
        element: <ProtectedRoute component={LogicalSwitches} />
    },
    {
        path: "/service_provision/logical_switches/:logical_switches_name",
        element: <ProtectedRoute component={LogicalSwitchDetail} />
    },
    {
        path: "/service_provision/logical_routers",
        element: <ProtectedRoute component={LogicalRouters} />
    },
    {
        path: "/service_provision/logical_routers/:logical_routers_name",
        element: <ProtectedRoute component={LogicalRouterDetail} />
    },
    {
        path: "/service_provision/network_access",
        element: <ProtectedRoute component={NetworkAccess} />
    },
    {
        path: "/monitor/alerts/alert_list",
        element: <ProtectedRoute component={Alarms} />
    },
    {
        path: "/monitor/alerts/notification_rules",
        element: <ProtectedRoute component={AlarmNotificationRules} />
    },
    {
        path: "/monitor/alerts/notification_history",
        element: <ProtectedRoute component={HistoricalAlarmEmailLogs} />
    },
    // {
    //     path: "monitor/network/DLB",
    //     element: <ProtectedRoute component={DLB} />
    // },
    // {
    //     path: "monitor/RoCE_counters/switch/:type",
    //     element: <RoCESwitchManager />
    // },
    // {
    //     path: "monitor/RoCE_counters/NICs",
    //     element: <ProtectedRoute component={RoceNicsMonitor} />
    // },
    {
        path: "/monitor/telemetry/performance_statistics/:type",
        element: <PerformanceStatisticsIndex />
    },
    {
        path: "/monitor/telemetry/optical_modules/:type",
        element: <OpticalModulesIndex />
    },
    {
        path: "/monitor/telemetry/RoCE_counters/:type",
        element: <RoCESwitchManager />
    },
    {
        path: "monitor/event_log/:type",
        element: <EventManager />
    },
    // {
    //     path: "service/switch/:sn",
    //     element: <ProtectedRoute component={SwitchTelemetry} />
    // },
    // {
    //     path: "service/NICs/inventory",
    //     element: <ProtectedRoute component={NICsInventory} />
    // },
    // {
    //     path: "service/NICs/monitoring",
    //     element: <ProtectedRoute component={NICsMonitoring} />
    // },
    // {
    //     path: "service/hosts/inventory",
    //     element: <ProtectedRoute component={HostInventory} />
    // },
    // {
    //     path: "service/hosts/device_discovery",
    //     element: <ProtectedRoute component={HostDevices} />
    // },
    // {
    //     path: "service/NICs/modules_overview",
    //     element: <ProtectedRoute component={NICsModulesOverview} />
    // },
    // {
    //     path: "/service/NICs/RoCE_configuration/:type",
    //     element: <RoceConfigManager />
    // },
    {
        path: "physical_network/topologies",
        element: <ProtectedRoute component={TopoEntrance} />
    },
    {
        path: "physical_network/design/units",
        element: <ProtectedRoute component={TopoUnit} />
    },
    {
        path: "physical_network/design/units/:unit_name",
        element: <ProtectedRoute component={TopoUnitDetail} />
    },
    {
        path: "physical_network/design/dc_templates",
        element: <ProtectedRoute component={DCTemplates} />
    },
    {
        path: "physical_network/design/dc_templates/:template_name",
        element: <ProtectedRoute component={DCTemplatesDetail} />
    },
    {
        path: "physical_network/fabrics",
        element: <ProtectedRoute component={FabricManagement} />
    },
    {
        path: "physical_network/fabrics/:fabric_name",
        element: <ProtectedRoute component={FabricDetail} />
    },
    {
        path: "physical_network/RoCE/RoCE_easydeploy/:type",
        element: <ProtectedRoute component={RoCEEasydeployManager} />
    },
    {
        path: "physical_network/RoCE/RoCE_policies/:type",
        element: <ProtectedRoute component={RoCEPoliciesManager} />
    },
    {
        path: "physical_network/RoCE/load_balancing/:type",
        element: <ProtectedRoute component={LoadBalancingManager} />
    },
    {
        path: "physical_network/RoCE/NIC_configurations/:type",
        element: <RoceConfigManager />
    },
    {
        path: "telemetry/switch/optical_modules",
        element: <ProtectedRoute component={OpticalModules} />
    }
    // {
    //     path: "telemetry/switch/optical_modules_pre",
    //     element: <ProtectedRoute component={OpticalModules} />
    // }
];

export const ampConCampusRoute = [
    // {
    //     path: "dashboard/wireless_view",
    //     element: <ProtectedRoute component={WirelessView} />
    // },
    {
        path: "dashboard/switch_view",
        element: <ProtectedRoute component={SwitchView} />
    },
    {
        path: "dashboard/telemetry_dashboard",
        element: <ProtectedRoute component={TelemetryView} />
    },
    {
        path: "maintain/automation/playbooks",
        element: <ProtectedRoute component={Playbook} />
    },
    {
        path: "maintain/automation/other_devices",
        element: <ProtectedRoute component={OtherDevices} />
    },
    {
        path: "maintain/automation/ansible_jobs/:type",
        element: <AnsibleJobsList />
    },
    {
        path: "maintain/automation/schedule",
        element: <ProtectedRoute component={Schedule} />
    },
    {
        path: "maintain/system_backup",
        element: <ProtectedRoute component={SystemBackup} />
    },
    {
        path: "maintain/web_access",
        element: <ProtectedRoute component={CLIConfiguration} />
    },
    {
        path: "system/user_management",
        element: <ProtectedRoute component={UserManagement} />
    },
    {
        path: "system/email_settings",
        element: <ProtectedRoute component={EmailSetting} />
    },
    {
        path: "system/software_licenses/license_view",
        element: <ProtectedRoute component={LicenseView} />
    },
    {
        path: "system/software_licenses/license_management",
        element: <ProtectedRoute component={LicenseManagement} />
    },
    {
        path: "system/software_licenses/license_log",
        element: <ProtectedRoute component={LicenseLog} />
    },
    {
        path: "resource/upgrade_management/:type",
        element: <UpgradeManagementSwitch />
    },
    {
        path: "resource/auth_management/device_license_management/:type",
        element: <SwitchManager />
    },
    {
        path: "resource/auth_management/group_management",
        element: <ProtectedRoute component={GroupManagement} />
    },
    {
        path: "resource/auth_management/fabric_management",
        element: <ProtectedRoute component={FabricManagement} />
    },
    {
        path: "resource/auth_management/site_management",
        element: <ProtectedRoute component={SiteManagement} />
    },
    {
        path: "service/config_templates/:type",
        element: <ConfigTemplateIndex />
    },
    {
        path: "service/switch",
        element: <ProtectedRoute component={Switch} />
    },
    {
        path: "service/global_configuration",
        element: <ProtectedRoute component={GlobalConfiguration} />
    },
    {
        path: "service/switch_configuration",
        element: <ProtectedRoute component={SwitchConfiguration} />
    },
    {
        path: "service/config_file_view",
        element: <ProtectedRoute component={ConfigFileView} />
    },
    {
        path: "service/switch_model",
        element: <ProtectedRoute component={SwitchModel} />
    },
    {
        path: "service/system_configuration",
        element: <ProtectedRoute component={AmpConSystemConfig} />
    },
    {
        path: "monitor/alerts/alert_list",
        element: <ProtectedRoute component={Alarms} />
    },
    {
        path: "monitor/alerts/notification_rules",
        element: <ProtectedRoute component={AlarmNotificationRules} />
    },
    {
        path: "monitor/alerts/notification_history",
        element: <ProtectedRoute component={HistoricalAlarmEmailLogs} />
    },
    {
        path: "monitor/wired_clients",
        element: <ProtectedRoute component={WiredClients} />
    },
    {
        path: "topo/topology",
        element: <ProtectedRoute component={TopoEntrance} />
    },
    {
        path: "topo/campus_fabric",
        element: <ProtectedRoute component={CampusFabric} />
    },
    {
        path: "service/switch/:sn",
        element: <ProtectedRoute component={SwitchTelemetry} />
    },
    {
        path: "service/nics/inventory",
        element: <ProtectedRoute component={NICsInventory} />
    },
    {
        path: "service/nics/monitoring",
        element: <ProtectedRoute component={NICsMonitoring} />
    },
    {
        path: "topo/campus_fabric/mlag_view",
        element: <ProtectedRoute component={CampusFabricMLAGRecordView} />
    },
    {
        path: "topo/campus_fabric/ipclos_view",
        element: <ProtectedRoute component={CampusFabricIPCLOSRecordView} />
    },
    {
        path: "topo/switch_templates",
        element: <ProtectedRoute component={SwitchTemplates} />
    }
];
