import React, {useEffect, useState} from "react";
import Icon, {MenuUnfoldOutlined, MenuFoldOutlined} from "@ant-design/icons";
import {Layout, Menu, Button, theme, Breadcrumb, Space, Dropdown, Modal, Divider} from "antd";
import {Outlet, useLocation, useNavigate} from "react-router-dom";
import {useDispatch, useSelector} from "react-redux";
import {fetchLogout} from "@/store/modules/common/user_slice";
import {getAlarmCount, updateAlarmSearch, updateAlarmSearchStatus} from "@/store/modules/common/alarm_slice";

import {useForm} from "antd/es/form/Form";
import {UserEditModalForm} from "@/modules-ampcon/pages/System/user_modal";
import {useGetVenue} from "@/modules-smb/hooks/Network/Venues";

import {
    collapsedLogoSvg,
    downSvg,
    pointSvg,
    upSvg,
    userSvg,
    alarm1Svg,
    alarm3Svg,
    alarm4Svg
} from "@/utils/common/iconSvg";
import {removeCssStyleByCssSelector, ALARM_COLOR} from "@/modules-ampcon/utils/util";
import {customTitle, sidebarItems, sideSvg} from "@/custom_modules";
import styles from "./layout.module.scss";

const {Header, Sider, Content} = Layout;

const FSAmpConLayout = () => {
    const currentUser = useSelector(state => state.user.userInfo);
    const userType = currentUser?.type;
    const userRole = currentUser?.role;
    const items = sidebarItems[userType] ? sidebarItems[userType] : sidebarItems.readonly;
    const [siderWidth, setSiderWidth] = useState(200);

    const {
        token: {colorPrimary}
    } = theme.useToken();

    const navigate = useNavigate();
    const location = useLocation();
    const dispatch = useDispatch();
    const [openKeys, setOpenKeys] = useState([]);

    const fourthLevelDefaultTAB = {
        ansible_jobs_list: "job_view",
        ansible_jobs: "job_view",
        // config_template: userType === "readonly" ? "template_verify" : "new_template",
        config_templates: userType === "readonly" ? "template_verify" : "new_template",
        hosts: "device_discovery",
        device_profiles: "global_configuration",
        RoCE_easydeploy: "deployment",
        RoCE_counters: "pfc_ecn",
        optical_modules: "switch",
        performance_statistics: "switch",
        // upgrade_management: "otn",
        NICs: "inventory",
        l0_config: "optical-power-management",
        device_license_management: "license_audit",
        device_licenses: "license_audit",
        // monitor_switch: "pfc_ecn",
        event_log: "ai_event",
        // RoCE_configuration: "script_deployment",
        NIC_configurations: "form_deployment",
        e2e_service_config: "create-business",
        performance: "current",
        performance_subscription: "sensor_group",
        time_management: "time_management",
        link_measure: "otdr",
        resource_interconnection: "PoD",
        load_balancing: "deployment",
        RoCE_policies: "deployment"
        // alarm: "current_alarm"
    };

    const onOpenChange = newOpenKeys => {
        // If only one menu item is allowed to expand, only the key of the last menu item is reserved
        const lastKey = newOpenKeys[newOpenKeys.length - 1];
        if (lastKey === undefined) {
            setOpenKeys(undefined);
        } else {
            const first_part_regex = `/${lastKey.match(/^\/(.+?)(?:\/|$)/)[1]}`;
            if (first_part_regex === newOpenKeys[0]) {
                setOpenKeys(newOpenKeys);
            } else if (first_part_regex !== lastKey) {
                setOpenKeys(undefined);
            } else {
                setOpenKeys([lastKey]);
            }
        }
    };

    const onMenuClick = item => {
        const path = fourthLevelDefaultTAB[item.key.split("/").pop()];
        if (path) {
            navigate(`${item.key}/${path}`);
        } else if (item.key === "/monitor/RoCE_counters/switch") {
            navigate(`${item.key}/pfc_ecn`); // 暂定
        } else {
            if (item.key === "/monitor/alerts/alert_list") {
                dispatch(updateAlarmSearch(""));
                dispatch(updateAlarmSearchStatus(false));
            }
            navigate(item.key);
        }
        if (item.keyPath.length === 1) {
            setOpenKeys([item.key]);
        }
    };
    const currentVersion = useSelector(state => state.version.currentVersionInfo);
    const [collapsed, setCollapsed] = useState(false);
    const [isVersionOpen, setIsVersionOpen] = useState(false);

    // findOpenKeys =>  get parent path all list
    // findSelectedKey => get 4th parent level path single list
    const findOpenKeys = currentPath => {
        const keys = [];
        const pathArray = currentPath.split("/").filter(Boolean);
        let currentKey = "";
        for (let i = 0; i < pathArray.length - 1; i++) {
            currentKey += `/${pathArray[i]}`;
            keys.push(currentKey);
        }
        return keys;
    };

    const findSelectedKey = () => {
        const pathName = location.pathname;
        if (pathName.startsWith("/monitor/RoCE_counters/switch/")) {
            return "/monitor/RoCE_counters/switch"; // 返回静态路由
        }
        if (pathName.startsWith("/device/switches/")) {
            return "/device/switches"; // 返回静态路由
        }
        if (pathName.startsWith("/service/switch/")) {
            return "/service/switch"; // 返回静态路由
        }
        if (pathName.startsWith("/resource/resource_interconnection/")) {
            return "/resource/resource_interconnection";
        }
        if (pathName.startsWith("/topo/campus_fabric/")) {
            return "/topo/campus_fabric"; // 返回静态路由
        }
        if (pathName.startsWith("/service_provision/logical_networks/")) {
            return "/service_provision/logical_networks"; // 返回静态路由
        }
        if (pathName.startsWith("/service_provision/logical_routers/")) {
            return "/service_provision/logical_routers"; // 返回静态路由
        }
        if (pathName.startsWith("/service_provision/logical_switches/")) {
            return "/service_provision/logical_switches"; // 返回静态路由
        }
        if (pathName.startsWith("/physical_network/design/dc_templates/")) {
            return "/physical_network/design/dc_templates"; // 静态菜单 key
        }
        if (pathName.startsWith("/physical_network/design/units/")) {
            return "/physical_network/design/units"; // 静态菜单 key
        }
        if (pathName.startsWith("/physical_network/fabrics/")) {
            return "/physical_network/fabrics"; // 静态菜单 key
        }
        if (Object.keys(fourthLevelDefaultTAB).some(key => pathName.split("/").includes(key))) {
            return findOpenKeys(pathName).slice(-1);
        }
        return pathName;
    };

    useEffect(() => {
        // if the sidebar is collapsed, open the parent path, otherwise will do nothing
        if (!collapsed) {
            setOpenKeys(findOpenKeys(location.pathname));
        }
    }, [location.pathname, colorPrimary]);

    const getBreadcrumb = () => {
        const uppercaseWords = [
            "ai",
            "otn",
            "e2e",
            "wss",
            "tff",
            "pmp",
            "otdr",
            "ocm",
            "ntp",
            "cli",
            "dlb",
            "dc",
            "az",
            "asn",
            "ip",
            "vni",
            "mlag",
            "nic",
            "os",
            "vlan"
        ];

        // 特殊处理：检查是否匹配 /wireless/venue/:id
        const venueMatch = location.pathname.match(/^\/wireless\/venue\/(\d+)$/);
        const venueId = venueMatch ? venueMatch[1] : undefined;
        const {data: venueData} = useGetVenue({id: venueId});
        if (venueId) {
            return [{title: "Wireless"}, {title: "Entities"}, {title: venueData?.name}];
        }

        const pathItems = location.pathname
            .split("/")
            .filter(item => item !== "")
            .map(item => {
                let title = item.replace(/[_-]/g, " ");
                title = title.replace(/\b\w/g, c => c.toUpperCase());

                if (title.toLowerCase() === "ipclos view") {
                    title = "IP Clos View";
                }
                if (title.toLowerCase() === "pfc ecn") {
                    title = "PFC & ECN";
                }
                uppercaseWords.forEach(word => {
                    // if (title.toLowerCase().includes(word)) {
                    //     title = title.replace(new RegExp(`\\b${word}\\b`, "gi"), word.toUpperCase());
                    // }
                    if (title.toLowerCase().includes(`${word} `)) {
                        title = title.replace(new RegExp(word, "gi"), word.toUpperCase());
                    }
                });
                if (title === "PoD" && location.pathname.includes("/resource/resource_interconnection/PoD/")) {
                    return {
                        title: (
                            <span
                                style={{cursor: "pointer", color: "#14C9BB"}}
                                onClick={() => navigate("/resource/resource_interconnection/PoD")}
                            >
                                PoD
                            </span>
                        )
                    };
                }
                if (
                    title === "VLAN Domain" &&
                    location.pathname.includes("/resource/resource_interconnection/vlan_domain/")
                ) {
                    return {
                        title: (
                            <span
                                style={{cursor: "pointer", color: "#14C9BB"}}
                                onClick={() => navigate("/resource/resource_interconnection/vlan_domain")}
                            >
                                VLAN Domain
                            </span>
                        )
                    };
                }
                if (
                    title === "Node Addition" &&
                    location.pathname.includes("/resource/resource_interconnection/node_addition/")
                ) {
                    return {
                        title: (
                            <span
                                style={{cursor: "pointer", color: "#14C9BB"}}
                                onClick={() => navigate("/resource/resource_interconnection/node_addition")}
                            >
                                Node Addition
                            </span>
                        )
                    };
                }
                if (title === "Switches" && location.pathname.includes("/device/switches/")) {
                    return {
                        title: (
                            <span
                                style={{cursor: "pointer", color: "#14C9BB"}}
                                onClick={() => navigate("/device/switches")}
                            >
                                Switches
                            </span>
                        )
                    };
                }
                if (title === "Switch" && location.pathname.includes("/service/switch/")) {
                    return {
                        title: (
                            <span
                                style={{cursor: "pointer", color: "#14C9BB"}}
                                onClick={() => navigate("/service/switch")}
                            >
                                Switch
                            </span>
                        )
                    };
                }
                if (title === "Fabrics" && location.pathname.includes("/physical_network/fabrics/")) {
                    return {
                        title: (
                            <span
                                style={{cursor: "pointer", color: "#14C9BB"}}
                                onClick={() => navigate("/physical_network/fabrics")}
                            >
                                Fabrics
                            </span>
                        )
                    };
                }
                if (title === "Units" && location.pathname.includes("/physical_network/design/units/")) {
                    return {
                        title: (
                            <span
                                style={{cursor: "pointer", color: "#14C9BB"}}
                                onClick={() => navigate("/physical_network/design/units")}
                            >
                                Units
                            </span>
                        )
                    };
                }
                if (
                    title.toLowerCase() === "dc templates" &&
                    location.pathname.includes("/physical_network/design/dc_templates/")
                ) {
                    return {
                        title: (
                            <span
                                style={{cursor: "pointer", color: "#14C9BB"}}
                                onClick={() => navigate("/physical_network/design/dc_templates")}
                            >
                                DC Templates
                            </span>
                        )
                    };
                }
                if (
                    title === "Logical Networks" &&
                    location.pathname.includes("/service_provision/logical_networks/")
                ) {
                    return {
                        title: (
                            <span
                                style={{cursor: "pointer", color: "#14C9BB"}}
                                onClick={() => navigate("/service_provision/logical_networks")}
                            >
                                Logical Networks
                            </span>
                        )
                    };
                }
                if (title === "Logical Routers" && location.pathname.includes("/service_provision/logical_routers/")) {
                    return {
                        title: (
                            <span
                                style={{cursor: "pointer", color: "#14C9BB"}}
                                onClick={() => navigate("/service_provision/logical_routers")}
                            >
                                Logical Routers
                            </span>
                        )
                    };
                }
                if (
                    title === "Logical Switches" &&
                    location.pathname.includes("/service_provision/logical_switches/")
                ) {
                    return {
                        title: (
                            <span
                                style={{cursor: "pointer", color: "#14C9BB"}}
                                onClick={() => navigate("/service_provision/logical_switches")}
                            >
                                Logical Switches
                            </span>
                        )
                    };
                }

                return {title};
            });

        if (pathItems.length === 0) {
            pathItems.push({title: "Dashboard"});
        }
        return pathItems;
    };
    const logoutConfirm = () => {
        dispatch(fetchLogout());
    };

    const toHome = () => {
        navigate("/");
    };

    const userItems = [
        userRole === "local" && {
            label: <a>User Management</a>,
            key: "item-2",
            onClick: () => {
                setIsModalOpen(true);
                form.setFieldValue("username", currentUser.username);
            }
        },
        {
            label: <a>Version</a>,
            key: "item-1",
            onClick: () => {
                setIsVersionOpen(true);
            }
        },
        {
            label: <a>Logout</a>,
            key: "item-3",
            onClick: logoutConfirm
        }
    ];

    const [form] = useForm();
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [hoverStatus, setHoverStatus] = useState(false);
    const currentAlarm = useSelector(state => state.alarm.alarmInfo);
    const [alarmCount, setAlarmCount] = useState({});

    useEffect(() => {
        dispatch(getAlarmCount());
        setAlarmCount({
            error: currentAlarm[0],
            info: currentAlarm[1],
            warn: currentAlarm[2]
        });
    }, currentAlarm);

    useEffect(() => {
        const siderLogoElement = document.querySelector(".ant-layout-sider-children svg");
        if (siderLogoElement) {
            const siderLogoWidth = siderLogoElement.getBoundingClientRect().width;
            if (siderLogoWidth > 200) {
                setSiderWidth(siderLogoWidth + 14);
            } else {
                setSiderWidth(200);
            }
        }
    }, []);

    const alarmIconMap = {
        error: alarm1Svg,
        info: alarm4Svg,
        warn: alarm3Svg
    };

    const isDC = import.meta.env.VITE_APP_EXPORT_MODULE === "AmpCon-DC";
    const documentUrl = isDC
        ? "https://pica8-fs.atlassian.net/wiki/spaces/ampcondc22/overview?homepageId=335774050"
        : "https://pica8-fs.atlassian.net/wiki/spaces/ampconcampus22/overview?homepageId=332529676";

    const menuItems = [
        {
            key: "queryTool",
            label: (
                <a
                    href="https://www.fs.com/tool/tool-home"
                    target="_blank"
                    rel="noopener noreferrer"
                    style={{display: "inline-block", width: "70px", textAlign: "center"}}
                >
                    Query Tool
                </a>
            )
        },
        {
            key: "document",
            label: (
                <a
                    href={documentUrl}
                    target="_blank"
                    rel="noopener noreferrer"
                    style={{display: "inline-block", width: "70px", textAlign: "center"}}
                >
                    Document
                </a>
            )
        }
    ];

    return (
        <Layout className={styles.layoutStyle}>
            <Modal title={currentVersion} open={isVersionOpen} onCancel={() => setIsVersionOpen(false)} footer={null}>
                <>
                    <Divider />
                    <ul>
                        <li className="actionLink">
                            <a>Release notes for {customTitle}</a>
                        </li>
                    </ul>
                </>
            </Modal>

            <UserEditModalForm
                title="User Account Settings"
                isModalOpen={isModalOpen}
                setIsModalOpen={setIsModalOpen}
                formInstance={form}
                layoutProps={{
                    labelCol: {
                        span: 8
                    },
                    wrapperCol: {
                        span: 19
                    }
                }}
            />
            <Sider collapsed={collapsed} className={styles.themeBackground} width={siderWidth}>
                <div className={styles.logoContainer}>
                    {collapsed ? (
                        <Icon component={collapsedLogoSvg} />
                    ) : (
                        <Icon component={sideSvg} onClick={() => toHome()} />
                    )}
                    {/* <div>nihao</div> */}
                </div>
                <Menu
                    mode="inline"
                    items={items}
                    className={styles.themeBackground}
                    onClick={onMenuClick}
                    openKeys={openKeys}
                    // onOpenChange={keys => setOpenKeys(keys)}
                    onOpenChange={onOpenChange}
                    selectedKeys={findSelectedKey()}
                />
            </Sider>
            <Layout>
                <Header className={styles.layoutHeader}>
                    <div className={styles.breadCrumb}>
                        <Button
                            type="text"
                            icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
                            onClick={() => {
                                if (collapsed) {
                                    // set sidebar tooltip to visible
                                    removeCssStyleByCssSelector(".ant-tooltip:has(.fixed-tooltip)");
                                    // for adapt firefox 96
                                    document.querySelectorAll(".ant-tooltip").forEach(tooltip => {
                                        if (tooltip.querySelector(".fixed-tooltip")) {
                                            tooltip.style.position = "fixed";
                                            tooltip.style.left = "210px";
                                            tooltip.style.setProperty("left", "210px", "important"); // 兼容 !important
                                        }
                                    });
                                } else {
                                    removeCssStyleByCssSelector(".ant-tooltip:has(.fixed-tooltip)");
                                    // for adapt firefox 96
                                    document.querySelectorAll(".ant-tooltip").forEach(tooltip => {
                                        if (tooltip.querySelector(".fixed-tooltip")) {
                                            tooltip.style.display = "none";
                                        }
                                    });
                                }
                                setCollapsed(!collapsed);
                            }}
                            className={styles.collapsedButton}
                        />
                        <Breadcrumb separator=">" className={styles.breadCrumbInternal} items={getBreadcrumb()} />
                        <div className={styles.iconList}>
                            <Space size="middle">
                                <>
                                    {Object.entries(ALARM_COLOR).map(([k]) => (
                                        <div
                                            className={styles.iconList_iconDiv}
                                            key={`alarm_${k}`}
                                            onClick={() => {
                                                if (k === "error") {
                                                    dispatch(updateAlarmSearch("error"));
                                                }
                                                if (k === "warn") {
                                                    dispatch(updateAlarmSearch("warn"));
                                                }
                                                if (k === "info") {
                                                    dispatch(updateAlarmSearch("info"));
                                                }
                                                dispatch(updateAlarmSearchStatus(true));
                                                navigate("/monitor/alerts/alert_list");
                                            }}
                                        >
                                            <div className={styles.iconList_icon}>
                                                <Icon component={alarmIconMap[k]} />
                                            </div>
                                            <div className={styles.iconList_label}>{alarmCount[k]}</div>
                                        </div>
                                    ))}
                                </>
                                <Dropdown
                                    menu={{items: userItems}}
                                    trigger={["hover"]}
                                    onOpenChange={val => setHoverStatus(val)}
                                >
                                    <div className={styles.iconList_iconUserDiv} style={{marginLeft: "8px"}}>
                                        <div className={styles.iconList_userLabel}>
                                            <Icon component={userSvg} />
                                            <span style={{marginLeft: "4px", marginRight: "2px"}}>
                                                {currentUser.username}
                                            </span>
                                        </div>
                                        <div
                                            className={styles.iconList_icon}
                                            style={{marginLeft: "2px", marginRight: "8px"}}
                                        >
                                            <Icon component={hoverStatus ? upSvg : downSvg} />
                                        </div>
                                    </div>
                                </Dropdown>
                                <Dropdown
                                    menu={{items: menuItems}}
                                    trigger={["hover"]}
                                    placement="bottomLeft"
                                    // arrow // 添加箭头
                                    forceRender
                                    getPopupContainer={triggerNode => triggerNode} // 指定挂载到当前元素的父级
                                >
                                    <div className={styles.pointSvg} style={{cursor: "pointer"}}>
                                        <Icon component={pointSvg} />
                                    </div>
                                </Dropdown>
                            </Space>
                        </div>
                    </div>
                </Header>
                <Content className={styles.contentContainer}>
                    <Outlet />
                </Content>
            </Layout>
        </Layout>
    );
};
export default FSAmpConLayout;
