import React, { useEffect, useState ,useCallback,useMemo} from 'react';
import { useToast, SimpleGrid } from '@chakra-ui/react';
import { Formik, Form } from 'formik';
import PropTypes from 'prop-types';
import { useTranslation } from 'react-i18next';
import { v4 as uuid } from 'uuid';
import SpecialConfigurationManager from '../../../CustomFields/SpecialConfigurationManager';
import DeviceRulesField from '@/modules-smb/components/CustomFields/DeviceRulesField';
import SelectField from '@/modules-smb/components/FormFields/SelectField';
import SelectWithSearchField from '@/modules-smb/components/FormFields/SelectWithSearchField';
import StringField from '@/modules-smb/components/FormFields/StringField';
import ToggleField from '@/modules-smb/components/FormFields/ToggleField';
import { CreateTagSchema } from '@/modules-smb/constants/formSchemas';
import { useGetEntities } from '@/modules-smb/hooks/Network/Entity';
import { useGetVenues } from '@/modules-smb/hooks/Network/Venues';
import { updateInterfaces } from '@/modules-smb/utils/configHelpers';
import GroupSelect from '@/modules-smb/Wireless/components/FormFields/LabelSelect';

const propTypes = {
  isOpen: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  create: PropTypes.instanceOf(Object).isRequired,
  refresh: PropTypes.func.isRequired,
  formRef: PropTypes.instanceOf(Object).isRequired,
  onConfigurationChange: PropTypes.func.isRequired,
  configuration: PropTypes.instanceOf(Object),
  deviceTypesList: PropTypes.arrayOf(PropTypes.string).isRequired,
  entityId: PropTypes.string.isRequired,
  deviceClass: PropTypes.string.isRequired,
  subId: PropTypes.string.isRequired,
  venueId: PropTypes.string, // 接收venueId属性
};

const defaultProps = {
  configuration: null,
  venueId: '', // 接收venueId属性
};

const CreateTagForm = ({
  isOpen,
  onClose,
  create,
  refresh,
  formRef,
  deviceTypesList,
  entityId,
  onConfigurationChange,
  configuration,
  deviceClass,
  subId,
  venueId
}) => {
  const { t } = useTranslation();
  const toast = useToast();
  const [formKey, setFormKey] = useState(uuid());
  const { data: entities } = useGetEntities({ t, toast });
  const { data: venues } = useGetVenues({ t, toast });
  // 新增：跟踪当前选中的venueId，用于控制界面行为
  const [currentVenueId, setCurrentVenueId] = useState(venueId);
  // 新增：控制venue下拉框是否禁用
  const [isVenueDisabled, setIsVenueDisabled] = useState(venueId !== '');
  
  const getEntityId = () => {
    if (entityId === '') return '';
    const splitEntity = entityId.split(':');
    if (splitEntity[0] === 'entity') return `ent:${splitEntity[1]}`;
    return `ven:${splitEntity[1]}`;
  };

  const createParameters = ({
    serialNumber,
    name,
    description,
    note,
    deviceType,
    devClass,
    deviceRules,
    entity,
    doNotAllowOverrides,
    group, // 新增 group 字段
  }) => ({
    serialNumber: serialNumber.toLowerCase(),
    name,
    deviceRules,
    deviceType,
    devClass: deviceClass !== '' ? deviceClass : devClass,
    description: description.length > 0 ? description : undefined,
    notes: note.length > 0 ? [{ note }] : undefined,
    entity: entity === '' || entity.split(':')[0] !== 'ent' ? '' : entity.split(':')[1],
    venue: entity === '' || entity.split(':')[0] !== 'ven' ? '' : entity.split(':')[1],
    doNotAllowOverrides,
    subscriber: subId !== '' ? subId : '',
    group // 新增 group 字段
  });

  useEffect(() => {
    setFormKey(uuid());
  }, [isOpen]);

  // 当父组件传递的venueId变化时更新currentVenueId和禁用状态
  useEffect(() => {
    setCurrentVenueId(venueId);
    setIsVenueDisabled(venueId !== '');
  }, [venueId]);
  
  const parent = {
    entity: entityId === '' || entityId.split(':')[0] !== 'entity' ? '' : entityId.split(':')[1],
    venue: entityId === '' || entityId.split(':')[0] !== 'venue' ? '' : entityId.split(':')[1],
    subscriber: subId !== '' ? subId : '',
  }

  return (
    <Formik
      innerRef={formRef}
      key={formKey}
      initialValues={{
        serialNumber: '',
        name: '',
        description: '',
        deviceType: deviceTypesList[0],
        deviceRules: {
          rrm: 'inherit',
          rcOnly: 'inherit',
          firmwareUpgrade: 'inherit',
        },
        devClass: deviceClass !== '' ? deviceClass : 'any',
        note: '',
        entity: venueId ? `ven:${venueId}` : getEntityId(),
        doNotAllowOverrides: false,
        group: '' // 初始化 group 字段
      }}
      validationSchema={CreateTagSchema(t)}
      onSubmit={(formData, { setSubmitting, resetForm }) => {
        console.log('formData', formData);
        const params = createParameters(formData);
        console.log('params', params);
        if (configuration !== null) {
          // console.log('createConfiguration', configuration);
          // 增加保存SSID排序逻辑
          if (configuration.data?.configuration?.length && configuration.data.configuration[0].configuration && configuration.data.configuration[0].configuration.interfaces) {  
            configuration.data.configuration[0].configuration.interfaces = updateInterfaces(configuration.data.configuration[0].configuration.interfaces);
          }
          params.__newConfig = {
            ...configuration.data,
            name: `device:${formData.serialNumber}`,
            description: 'Created from the Edit Tag menu',
            deviceTypes: [formData.deviceType],
          };
        }
        create.mutateAsync(params, {
          onSuccess: () => {
            setSubmitting(false);
            resetForm();
            toast({
              id: 'tag-creation-success',
              title: t('common.success'),
              description: t('crud.success_create_obj', {
                obj: t('certificates.device'),
              }),
              status: 'success',
              duration: 5000,
              isClosable: true,
              position: 'top-right',
            });
            refresh();
            onClose();
          },
          onError: (e) => {
            toast({
              id: uuid(),
              title: t('common.error'),
              description: t('crud.error_create_obj', {
                obj: t('certificates.device'),
                e: e?.response?.data?.ErrorDescription,
              }),
              status: 'error',
              duration: 5000,
              isClosable: true,
              position: 'top-right',
            });
            setSubmitting(false);
          },
        });
      }}
    >
      {({ setFieldValue, values }) => {
        // 监听 entity 字段变化，动态设置 deviceRules.rrm
        React.useEffect(() => {
          // 如果没有站点，设置默认值为 'no' 
          if (values.entity === '' && values.deviceRules.rrm === 'inherit') {
            setFieldValue('deviceRules.rrm', 'no');
          // 如果有站点，设置默认值为 'inherit'
          } else if (values.entity !== '' && values.deviceRules.rrm === 'no') {
            setFieldValue('deviceRules.rrm', 'inherit');
          }
        }, [values.entity]);
        // 新增：监听 entity 字段变化，提取 venueId 并更新 currentVenueId
        useEffect(() => {
          if (values.entity && values.entity.startsWith('ven:')) {
            const id = values.entity.split(':')[1];
            setCurrentVenueId(id);
            // 重置 group 选择
            setFieldValue('group', '');
          } else if (!values.entity) {
            // 当 entity 为空时，清空 currentVenueId
            setCurrentVenueId('');
            setFieldValue('group', '');
          }
        }, [values.entity, setFieldValue]);
        return (
          <Form>
            <SimpleGrid minChildWidth="300px" spacing="20px" mb={6}>
              <StringField name="serialNumber" label={t('inventory.serial_number')} isRequired />
              <StringField name="name" label={t('common.name')} isRequired />
              <SelectField
                name="deviceType"
                label={t('inventory.device_type')}
                options={deviceTypesList.map((deviceType) => ({
                  value: deviceType,
                  label: deviceType,
                }))}
                isRequired
              />
              <SelectWithSearchField
                name="entity"
                label={t('inventory.venue')}
                // options={[
                //   { label: t('common.none'), value: '' },
                //   {
                //     label: t('entities.title'),
                //     options:
                //       entities?.map((ent) => ({
                //         value: `ent:${ent.id}`,
                //         label: `${ent.name}${ent.description ? `: ${ent.description}` : ''}`,
                //       })) ?? [],
                //   },
                //   {
                //     label: t('venues.title'),
                //     options:
                //       venues?.map((ven) => ({
                //         value: `ven:${ven.id}`,
                //         label: `${ven.name}${ven.description ? `: ${ven.description}` : ''}`,
                //       })) ?? [],
                //   },
                // ]}
                options={[
                { label: t('common.none'), value: '' },
                ...(venues?.map((ven) => ({
                  value: `ven:${ven.id}`,
                  label: `${ven.name}${ven.description ? `: ${ven.description}` : ''}`,
                })) ?? []),
                ]}
                isHidden={entityId !== '' || subId !== '' || deviceClass === 'subscriber'}
                isDisabled={isVenueDisabled} // 禁用下拉框
              />
              <GroupSelect
                name="group"
                value={values.group}
                label={t('inventory.group')}
                onChange={(newValue) => setFieldValue('group', newValue)}
                siteId={useMemo(() => {
                  return currentVenueId ? parseInt(currentVenueId, 10) : null;
                }, [currentVenueId])} // 传递siteId或null
              />
              {/* <DeviceRulesField /> */}
              {/* <SelectField
                name="devClass"
                label={t('inventory.dev_class')}
                options={[
                  { value: 'any', label: 'any' },
                  { value: 'entity', label: 'entity' },
                  { value: 'venue', label: 'venue' },
                  { value: 'subscriber', label: 'subscriber' },
                ]}
                isRequired
                isHidden={deviceClass !== ''}
              /> */}
              <ToggleField name="doNotAllowOverrides" label={t('overrides.ignore_overrides')} isRequired />
              <StringField name="description" label={t('common.description')} />
              <StringField name="note" label={t('common.note')} />
            </SimpleGrid>
            {/* <SpecialConfigurationManager editing onChange={onConfigurationChange} parent={parent} /> */}
          </Form>
        );
      }}
    </Formik>
  );
};

CreateTagForm.propTypes = propTypes;
CreateTagForm.defaultProps = defaultProps;

export default CreateTagForm;
