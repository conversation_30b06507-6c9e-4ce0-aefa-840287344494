import React from 'react';
const DashboardPage = React.lazy(() => import('@/modules-smb/Wireless/pages/Dashboard'));
const VenuePage = React.lazy(() => import('@/modules-smb/Wireless/pages/Entities'));
const AllDevicesPage = React.lazy(() => import('@/modules-smb/Wireless/pages/Devices'));
const InventoryPage = React.lazy(() => import('@/modules-smb/Wireless/pages/Inventory'));
const Profile = React.lazy(() => import('@/modules-smb/Wireless/pages/Profile'));
const Clients = React.lazy(() => import('@/modules-smb/Wireless/pages/Monitor/Clients'));
const BlacklistPage = React.lazy(() => import('@/modules-smb/pages/Devices/Blacklist'));
const DevicePage= React.lazy(() => import('@/modules-smb/pages/Device'));
const getModules = () => {
    let smbRoute = [
        {
            path: "wireless/devices",
            element: <AllDevicesPage />
        },
        {
            path: "dashboard/wireless_view",
            element: <DashboardPage />
        },
        {
            path: "wireless/entities",
            element: <VenuePage />
        },
        {
            path: "wireless/entities/Monitor",
            element: <VenuePage />
        },
        {
            path: "wireless/entities/Configure",
            element: <VenuePage />
        },
        {
            path: "wireless/entities/RRM-Optimize",
            element: <VenuePage />
        },
        {
            path: "wireless/inventory",
            element: <InventoryPage />
        },
        {
            path: "monitor/wireless_clients",
            element: <Clients />
        },
        {
            path: "wireless/profile",
            element: <Profile />
        },
        {
            path: "wireless/profile/Network",
            element: <Profile />
        },
        {
            path: "wireless/profile/SSIDRadius",
            element: <Profile />
        },
        {
            path: "wireless/profile/MPSKUser",
            element: <Profile />
        },
        {
            path: "wireless/profile/CaptivePortal",
            element: <Profile />
        },
        {
            path: "wireless/profile/TimeRange",
            element: <Profile />
        },
        {
            path: "wireless/devices/blacklist",
            element: <BlacklistPage />
        },
        {
            path: "wireless/devices/:id",
            element: <DevicePage/>
        },
    ];

    return {
        smbRoute
    };
};

export const { smbRoute } = getModules();
