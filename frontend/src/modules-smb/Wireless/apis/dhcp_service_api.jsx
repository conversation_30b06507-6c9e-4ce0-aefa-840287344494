import { request } from "@/utils/common/request";

const baseUrl = "/ampcon/wireless/configure";


export function filterDhcpServiceNames({ siteId, key }) {
  return request({
    url: `${baseUrl}/dhcp_service_filter`,
    method: "GET",
    params: {
      siteId,
      key
    }
  });
}


export function getDhcpServiceList({
  siteId,
  pageNum,
  pageSize,
  sortBy,
  sortType
}) {
  return request({
    url: `${baseUrl}/dhcp_service_list`,
    method: "GET",
    params: {
      siteId,
      pageNum,
      pageSize,
      sortBy,
      sortType
    }
  });
}


export function getDhcpServiceDetail({ id }) {
  return request({
    url: `${baseUrl}/dhcp_service`,
    method: "GET",
    params: { id }
  });
}


export function createDhcpService({
  site_id,
  name,
  subnet,
  vlan,
  dhcp_configure,
  description
}) {
  return request({
    url: `${baseUrl}/dhcp_service`,
    method: "POST",
    data: {
      site_id,
      name,
      subnet,
      vlan,
      dhcp_configure,
      description
    }
  });
}


export function updateDhcpService({
  id,
  name,
  subnet,
  vlan,
  dhcp_configure,
  description
}) {
  return request({
    url: `${baseUrl}/dhcp_service`,
    method: "PUT",
    data: {
      id,
      name,
      subnet,
      vlan,
      dhcp_configure,
      description
    }
  });
}


export function deleteDhcpService({ id }) {
  return request({
    url: `${baseUrl}/dhcp_service`,
    method: "DELETE",
    data: { id }
  });
}