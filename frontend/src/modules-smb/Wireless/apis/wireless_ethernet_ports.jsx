import { request } from "@/utils/common/request";

const baseUrl = "/ampcon/wireless/configure";

export function createEthernetPort({
  site_id,
  port,
  mac,
  network_type,  
  vlan_or_dhcp_name = null,
  multicast = 1,
  learning = 1,
  reverse_path = 2,
  vlan_tag = 1
}) {
  return request({
    url: `${baseUrl}/ethernet_ports`,
    method: "POST",
    data: {
      site_id,
      port,
      mac,
      network_type,  
      vlan_or_dhcp_name,
      multicast,
      learning,
      reverse_path,
      vlan_tag
    }
  });
}


export function getEthernetPortList({
  siteId,
  pageNum,
  pageSize,
  sortBy,
  sortType
}) {
  return request({
    url: `${baseUrl}/ethernet_ports`,
    method: "GET",
    params: {
      siteId,
      pageNum,
      pageSize,
      sortBy,
      sortType
    }
  }).then(response => {
    return response;
  });;
}


export function deleteEthernetPort({
  id
}) {
  return request({
    url: `${baseUrl}/ethernet_ports`,
    method: "DELETE",
    data: {
      id
    }
  });
}

// 创建接口调用示例
// createEthernetPort({
//   site_id: 1,
//   port: "LAN1",
//   mac: "11:22:33:44:55:66",
//   network_type: 2, // 必填，1/2/3
//   vlan_or_dhcp_name: "1121", // 可选
//   multicast: 1,
//   learning: 1,
//   reverse_path: 1,
//   vlan_tag: 1
// });

// 查询接口调用示例（不变）
// getEthernetPortList({
//   siteId: 1,
//   pageNum: 1,
//   pageSize: 10
// });
