import { request } from "@/utils/common/request";

const baseUrl = "/ampcon/wireless/site/label";

/**
 * 获取组列表
 * @param params 查询参数
 */
export function fetchLables(params: {
  siteId: number;
  key?: string;
}) {
  return request({
    url: `${baseUrl}`,
    method: "GET",
    params
  });
}

/**
 * 创建组
 * @param data 创建参数
 */
export function createLable(data: {
  site_id: number;
  name: string;
}) {
  return request({
    url: `${baseUrl}`,
    method: "POST",
    data
  });
}

/**
 * 删除组
 * @param id 组ID
 */
export function deleteLable(id: number) {
  return request({
    url: `${baseUrl}`,
    method: "DELETE",
    data: { id }
  });
}