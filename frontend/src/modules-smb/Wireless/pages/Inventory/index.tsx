import React from "react";
import { useNavigate } from "react-router-dom";
import { useState, useEffect } from "react";
import SiteSelect from "@/modules-smb/Wireless/components/SiteSelect";
import InventoryList from "@/modules-smb/Wireless/pages/Inventory/table/InventoryList";
import { Form, Switch, Space } from "antd"; 
import { useTranslation } from "react-i18next"; 

const InventoryPage = () => {
  const { t } = useTranslation(); 
  const [venueId, setVenueId] = useState<number | null>(null);
  const [onlyUnassigned, setOnlyUnassigned] = useState(false); 
  const navigate = useNavigate();

  useEffect(() => {
    const hash = window.location.hash.replace('#', '');
    if (/^\d+$/.test(hash)) {
      setVenueId(parseInt(hash, 10));
    }
  }, []);

  const handleChange = (value: string | string[]) => {
    const pathWithoutTab = window.location.pathname;
    navigate(`${pathWithoutTab}#${value}`);
    if (typeof value === 'string' && /^\d+$/.test(value)) {
      setVenueId(parseInt(value, 10));
    } else {
      setVenueId(null);
    }
  };
  const handleUnassignedToggle = (checked: boolean) => {
    setOnlyUnassigned(checked);
  };

  return (
    <>
      <span className="text-title">Inventory</span>
      <div style={{
        display: 'flex',
        alignItems: 'center',
        gap: 16, 
      }}>
        <SiteSelect onChange={handleChange} />
        <Form.Item 
          label={t('devices.unassigned_only')} 
          style={{ marginBottom: 16, marginLeft: '80px' }} 
        >
          <Switch
            checked={onlyUnassigned}
            onChange={handleUnassignedToggle}
            size="small"
          />
        </Form.Item>
      </div>
      
      {/* 将onlyUnassigned状态传递给InventoryList（子组件） */}
      <InventoryList venueId={venueId} onlyUnassigned={onlyUnassigned} />
    </>
  );
};

export default InventoryPage;