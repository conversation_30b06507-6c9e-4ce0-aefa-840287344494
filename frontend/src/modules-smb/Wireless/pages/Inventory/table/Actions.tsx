import { useDisclosure } from '@chakra-ui/react';
import React from 'react';
import { Button, Popover, Tooltip, Modal, Space, message } from 'antd';
import { useTranslation } from 'react-i18next';
import DeviceActionDropdown from '@/modules-smb/Wireless/components/TableCells/DeviceActionDropdown';
import ConfigurationPushModal from '@/modules-smb/components/Tables/InventoryTable/ConfigurationPushModal';
import { useGetGatewayUi } from '@/modules-smb/hooks/Network/Endpoints';
import { useDeleteTag, usePushConfig } from '@/modules-smb/hooks/Network/Inventory';
import { Device } from '@/modules-smb/models/Device';
import { confirmModalAction } from "@/modules-ampcon/components/custom_modal";

interface Props {
  cell: { original: Device };
  refreshTable: () => void;
  openEditModal: (dev: Device) => void;
  onOpenScan: (serialNumber: string) => void;
  onOpenFactoryReset: (serialNumber: string) => void;
  onOpenUpgradeModal: (serialNumber: string) => void;
}

const Actions: React.FC<Props> = ({
  cell: { original: tag },
  refreshTable,
  openEditModal,
  onOpenScan,
  onOpenFactoryReset,
  onOpenUpgradeModal,
}) => {
  const { t } = useTranslation();
  const { isOpen, onOpen, onClose } = useDisclosure();
  const { isOpen: isPushOpen, onOpen: openPush, onClose: closePush } = useDisclosure();
  const { isOpen: isConfirmPushOpen, onOpen: openConfirmPush, onClose: closeConfirmPush } = useDisclosure();

  const { data: gwUi } = useGetGatewayUi();
  const { mutateAsync: deleteConfig, isLoading: isDeleting } = useDeleteTag({
    name: tag.name,
    refreshTable,
    onClose,
  });

  const pushConfiguration = usePushConfig({
    onSuccess: () => {
      closeConfirmPush();
      openPush();
    },
  });

  const handleDeleteClick = () => deleteConfig(tag.serialNumber);
  const handleOpenEdit = () => openEditModal(tag);
  const target = window.location.origin;
  const handleOpenInGateway = () => window.open(`${target}/wireless/devices/${tag.serialNumber}#/devices/${tag.serialNumber}`, '_blank');
  const handleSyncConfig = () => openConfirmPush();
  const handlePushConfig = () => pushConfiguration.mutateAsync(tag.serialNumber);
  const handleDelete = () => {
    confirmModalAction(
      `Are you sure you want to delete this Device?`,
      handleDeleteClick
    );
  };
  return (
    <Space >
      {/* View 按钮 */}
      <Button type="link" onClick={handleOpenEdit}>
        {t('common.view')}
      </Button>

      {/* View In Controller 按钮 */}
      <Button type="link" onClick={handleOpenInGateway}>
        {t('common.view_in_gateway')}
      </Button>

      {/* Push Configuration 按钮 */}
      <Button type="link" onClick={handleSyncConfig}>
        {t('configurations.push_configuration')}
      </Button>

      {/* Action */}
      <DeviceActionDropdown
        device={tag}
        refresh={refreshTable}
        onOpenScan={onOpenScan}
        onOpenFactoryReset={onOpenFactoryReset}
        onOpenUpgradeModal={onOpenUpgradeModal}
      />
      {/* Delete 弹窗 */}
      <Button type="link" onClick={handleDelete}>
        {t('crud.delete')}
      </Button>

      {/* 配置推送确认模态框 */}
      <Modal
        title={t('configurations.push_configuration')}
        open={isConfirmPushOpen}
        onCancel={closeConfirmPush}
        footer={[
          <Button key="cancel" type="link" onClick={closeConfirmPush}>
            {t('common.cancel')}
          </Button>,
          <Button
            key="confirm"
            type="link"
            danger
            onClick={handlePushConfig}
            loading={pushConfiguration.isLoading}
          >
            {t('common.yes')}
          </Button>,
        ]}
      >
        <p>
          Are you sure you want to push the configuration to device <b>#{tag.serialNumber}</b>? <br />
          <br />
          You cannot undo this action afterwards.
        </p>
      </Modal>

      <ConfigurationPushModal
        isOpen={isPushOpen}
        onClose={closePush}
        pushResult={pushConfiguration.data}
      />
    </Space>
  );
};

export default Actions;