import React, { useEffect, useState } from "react";
import { Ta<PERSON>, Breadcrumb, Select } from "antd";
import { Buildings } from "@phosphor-icons/react";
import { useSelector } from "react-redux";
import { useLocation, useNavigate } from "react-router-dom";
import ProtectedRoute from "@/modules-ampcon/utils/util";
import VenueSelect from "@/modules-smb/Wireless/components/VenueSelect";
// import Network from "./Network";
import SSIDRadius from "./SSIDRadius";
import MPSKUser from "./MPSKUser";
import CaptivePortal from "./CaptivePortal";
import TimeRange from "./TimeRange";

const ProfilePage = () => {
    const currentUser = useSelector((state: any) => state.user.userInfo);
    const location = useLocation();
    const navigate = useNavigate();
    const [currentActiveKey, setCurrentActiveKey] = useState("");
    const handleChange = (value: string | string[]) => {
        // console.log(`selected ${value}`);
        // 获取当前地址栏url
        const pathWithoutTab = location.pathname;
        navigate(`${pathWithoutTab}#${value}`);
    };

    const allItems = [
        // {
        //     key: "Network",
        //     label: "Network",
        //     children: <ProtectedRoute component={Network} />
        // },
        {
            key: "SSIDRadius",
            label: "SSIDRadius",
            children: <ProtectedRoute component={SSIDRadius} />
        },
        {
            key: "MPSKUser",
            label: "MPSKUser",
            children: <ProtectedRoute component={MPSKUser} />
        },
        {
            key: "CaptivePortal",
            label: "CaptivePortal",
            children: <ProtectedRoute component={CaptivePortal} />
        },
        {
            key: "TimeRange",
            label: "TimeRange",
            children: <ProtectedRoute component={TimeRange} />
        }
    ];

    const items: any[] =
        currentUser.type === "readonly"
            ? [] // 只读用户不显示任何 tab
            : allItems;

    useEffect(() => {
        const match = location.pathname.match(/(SSIDRadius|MPSKUser|CaptivePortal|TimeRange)$/);
        if (match) {
            setCurrentActiveKey(match[0]);
        } else if (items.length > 0) {
            setCurrentActiveKey(items[0].key);
            let pathWithoutTab = `${location.pathname.replace(/\/$/, "")}/${items[0].key}`;
            if (location.hash) {
                pathWithoutTab += location.hash;
            }
            navigate(pathWithoutTab);
        }
    }, [location.pathname, items]);

    // 点击 tab 更新 URL
    const onChange = (key: any) => {
        let pathWithoutTab = location.pathname.replace(/(SSIDRadius|MPSKUser|CaptivePortal|TimeRange)$/, "");
        pathWithoutTab = `${pathWithoutTab.replace(/\/$/, "")}/${key}`;
        if (location.hash) {
            pathWithoutTab += location.hash;
        }
        navigate(pathWithoutTab);
    };

    return (
        <div>
            <VenueSelect onChange={handleChange} />
            <div>
                <Tabs
                    style={{ flex: 1 }}
                    activeKey={currentActiveKey}
                    onChange={onChange}
                    destroyInactiveTabPane
                    items={items}
                />
            </div>
        </div>
    );
};
export default ProfilePage;
