import React, { useState, useEffect, useCallback, useRef } from 'react';
import { Form, Input, Select, Button, message, Row, Col, Tooltip } from 'antd';
import debounce from 'lodash.debounce';
import clickTemplate from '@/modules-smb/assets/CaptiveTemplate/click.htm';
import credentialsTemplate from '@/modules-smb/assets/CaptiveTemplate/credentials.htm';
import radiusTemplate from '@/modules-smb/assets/CaptiveTemplate/radius.htm';
import { createWirelessProfile, updateWirelessProfile } from '@/modules-smb/Wireless/apis/wireless_profile_api';
import { convertHtmlToBase64 } from '@/modules-smb/utils/configHelpers';
import { useTranslation } from 'react-i18next';
import '@/modules-smb/Wireless/assets/form.scss';
import './WebrootForm.scss';

// 默认值常量
const defaultValues = {
  name: '',
  description: '',
  mode: 'Click',
  backgroundColor: '#F0F8F9',
  logoBase64: '',
  welcomeMessage: 'Welcome to use Wi-Fi',
  termsOfService: '',
  corporateInfo: '© 2025 FS.COM INC. All rights reserved',
};

// 字段最大长度常量
const defaultMaxLength = {
  welcomeMessage: 31,
  termsOfService: 200,
  corporateInfo: 50,
  logo: 10
};

export interface WebRootFormProps {
  isDisabled?: boolean;
  resource?: any;
  onClose?: () => void;
  refresh?: () => void;
  siteId?: number;
}

const WebRootForm: React.FC<WebRootFormProps> = ({
  isDisabled = false,
  resource,
  onClose,
  refresh,
  siteId,
}) => {
  const { t } = useTranslation();
  const [form] = Form.useForm();
  const [htmlContent, setHtmlContent] = useState<string>('');
  const [templateHtml, setTemplateHtml] = useState<string>('');
  const [mode, setMode] = useState<string>(defaultValues.mode);
  const [delayedWelcome, setDelayedWelcome] = useState<string>(defaultValues.welcomeMessage);
  const [delayedTerms, setDelayedTerms] = useState<string>(defaultValues.termsOfService);
  const [delayedCopyright, setDelayedCopyright] = useState<string>(defaultValues.corporateInfo);
  const [backgroundColor, setBackgroundColor] = useState<string>(defaultValues.backgroundColor);
  const [logoBase64, setLogoBase64] = useState<string>(defaultValues.logoBase64);
  const colorInputRef = useRef<HTMLInputElement>(null);
  const logoInputRef = useRef<HTMLInputElement>(null);
  const iframeRef = useRef<HTMLIFrameElement>(null);
  const [submitting, setSubmitting] = useState(false);

  const getTemplatePath = (mode: string) => {
    switch (mode) {
      case 'Click': return clickTemplate;
      case 'Radius': return radiusTemplate;
      case 'Credentials': return credentialsTemplate;
      default: return clickTemplate;
    }
  };

  const mergeHtmlContent = (tpl: string, params: any) => {
    let updatedHtml = tpl;
    if (params.backgroundColor) {
      updatedHtml = updatedHtml.replace(
        /background-color:\s*(?:unset|#[a-fA-F0-9]{3}(?:[a-fA-F0-9]{3})?)\s*;?/i,
        `background-color: ${params.backgroundColor};`
      );
    }
    if (params.logoBase64) {
      updatedHtml = updatedHtml.replace(/<img id="logo"[^>]*src="[^"]*"[^>]*>/, `<img id="logo" src="${params.logoBase64}" alt="logo" />`);
    }
    updatedHtml = updatedHtml.replace(/<p class="lead" id="title">.*?<\/p>/, `<p class="lead" id="title">${params.welcomeMessage}</p>`);
    updatedHtml = updatedHtml.replace(/<div[^>]*id="readmeTxt"[^>]*>([\s\S]*?)<\/div>/, `<div id="readmeTxt">${params.termsOfService}</div>`);
    updatedHtml = updatedHtml.replace(/<div[^>]*id="corporate-info"[^>]*>([\s\S]*?)<\/div>/, `<div id="corporate-info">${params.corporateInfo}</div>`);
    return updatedHtml;
  };

  // 延迟更新 welcome/terms/copyright
  const debounceWelcome = useCallback(debounce((val: string) => setDelayedWelcome(val), 400), []);
  const debounceTerms = useCallback(debounce((val: string) => setDelayedTerms(val), 400), []);
  const debounceCopyright = useCallback(debounce((val: string) => setDelayedCopyright(val), 400), []);

  // 监听mode变化，切换模板
  useEffect(() => {
    const templatePath = getTemplatePath(mode);
    fetch(templatePath)
      .then((response) => response.text())
      .then((html) => {
        setTemplateHtml(html);
        const values = form.getFieldsValue();
        setHtmlContent(mergeHtmlContent(html, {
          ...values,
          backgroundColor,
          logoBase64,
          welcomeMessage: delayedWelcome,
          termsOfService: delayedTerms,
          corporateInfo: delayedCopyright,
        }));
      })
      .catch((error) => console.error('Failed to load defaultHtml:', error));
  }, [mode]);

  const handleValuesChange = (changed: any, allValues: any) => {
    if (!templateHtml) return;
    if (Object.prototype.hasOwnProperty.call(changed, 'welcomeMessage')) {
      debounceWelcome(changed.welcomeMessage);
    }
    if (Object.prototype.hasOwnProperty.call(changed, 'termsOfService')) {
      debounceTerms(changed.termsOfService);
    }
    if (Object.prototype.hasOwnProperty.call(changed, 'corporateInfo')) {
      debounceCopyright(changed.corporateInfo);
    }
    if (Object.prototype.hasOwnProperty.call(changed, 'backgroundColor')) {
      setBackgroundColor(changed.backgroundColor);
    }
    if (Object.prototype.hasOwnProperty.call(changed, 'logoBase64')) {
      setLogoBase64(changed.logoBase64);
    }
  };

  // 字段变化时，更新预览html内容
  useEffect(() => {
    if (!templateHtml) return;
    const values = form.getFieldsValue();
    setHtmlContent(mergeHtmlContent(templateHtml, {
      ...values,
      backgroundColor,
      logoBase64,
      welcomeMessage: delayedWelcome,
      termsOfService: delayedTerms,
      corporateInfo: delayedCopyright,
    }));
  }, [delayedWelcome, delayedTerms, delayedCopyright, templateHtml, backgroundColor, logoBase64]);

  // 编辑回显
  useEffect(() => {
    if (resource) {
      const param = resource.parameter || {};
      setMode(param.mode || defaultValues.mode);
      setBackgroundColor(param.background || defaultValues.backgroundColor);
      setLogoBase64(param.logo || defaultValues.logoBase64);
      form.setFieldsValue({
        name: resource.name || defaultValues.name,
        description: resource.description || defaultValues.description,
        mode: param.mode || defaultValues.mode,
        backgroundColor: param.background || defaultValues.backgroundColor,
        logoBase64: param.logo || defaultValues.logoBase64,
        welcomeMessage: param.welcome || defaultValues.welcomeMessage,
        termsOfService: param.terms_of_service || defaultValues.termsOfService,
        corporateInfo: param.copyright || defaultValues.corporateInfo,
      });
      setDelayedWelcome(param.welcome || defaultValues.welcomeMessage);
      setDelayedTerms(param.terms_of_service || defaultValues.termsOfService);
      setDelayedCopyright(param.copyright || defaultValues.corporateInfo);
    }
  }, [resource]);

  // 监听 htmlContent，刷新 iframe 预览内容
  useEffect(() => {
    if (iframeRef.current && htmlContent) {
      iframeRef.current.srcdoc = htmlContent;
      setTimeout(() => {
        try {
          const doc = iframeRef.current?.contentDocument;
          const form = doc?.querySelector('form');
          if (form) {
            form.onsubmit = (e) => {
              e.preventDefault();
              return false;
            };
          }
        } catch {}
      }, 50);
    }
  }, [htmlContent]);

  // 图片上传校验
  const validateImage = (file: File, maxSize: number): boolean => {
    const validTypes = ['image/png', 'image/jpeg', 'image/jpg', 'image/gif'];
    if (!validTypes.includes(file.type)) {
      message.error('Only PNG, JPG, JPEG, or GIF file types are supported.');
      return false;
    }
    if (file.size > maxSize) {
      message.error(`Choose a picture that is no more than ${maxSize / 1024}KB.`);
      return false;
    }
    return true;
  };

  // 提交表单
  const handleFinish = async (values: any) => {
    setSubmitting(true);
    try {
      // 先请求 convertHtmlToBase64
      let base64Html = '';
      try {
        base64Html = await convertHtmlToBase64(htmlContent, values.mode);
      } catch (e) {
        message.error('Failed to convert HTML');
        setSubmitting(false);
        return;
      }
      const parameter = {
        mode: values.mode,
        background: values.backgroundColor,
        logo: values.logoBase64 || '',
        welcome: values.welcomeMessage,
        terms_of_service: values.termsOfService,
        copyright: values.corporateInfo,
      };
      const config_variables = [
        {
          prefix: 'captive-webroot',
          type: 'string',
          value: base64Html,
          weight: 0,
        },
      ];
      let res;
      if (resource) {
        res = await updateWirelessProfile({
          id: resource.id,
          site_id: siteId,
          type: 3,
          name: values.name,
          description: values.description,
          parameter: parameter,
          config_variables: JSON.stringify(config_variables),
        });
        if (res?.status !== 200) {
          message.error(res?.info || 'Failed to update resource');
          setSubmitting(false);
          return;
        }
        message.success('Resource updated successfully');
      } else {
        res = await createWirelessProfile({
          site_id: siteId,
          type: 3,
          name: values.name,
          description: values.description,
          parameter: parameter,
          config_variables: JSON.stringify(config_variables),
        });
        if (res?.status !== 200) {
          message.error(res?.info || 'Failed to create resource');
          setSubmitting(false);
          return;
        }
        message.success('Resource created successfully');
      }
      refresh && refresh();
      onClose && onClose();
    } catch (e) {
      message.error('Failed to create resource');
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <Form
      form={form}
      initialValues={defaultValues}
      onValuesChange={handleValuesChange}
      onFinish={handleFinish}
      className="wirelessForm"
    >
      <h3 className='header1'></h3>
      <Row gutter={24}>
        <Col span={12}>
          <Form.Item name="name" label={t('common.name')} rules={[{ required: true, message: t('form.required') }]}>
            <Input disabled={isDisabled} />
          </Form.Item>
        </Col>
      </Row>
      <Row gutter={24}>
        <Col span={12}>
          <Form.Item name="mode" label="Mode" rules={[{ required: true, message: t('form.required') }]}>
            <Select
              disabled={isDisabled}
              value={mode}
              onChange={val => { setMode(val); form.setFieldsValue({ mode: val }); }}
            >
              <Select.Option value="Click">Click</Select.Option>
              <Select.Option value="Radius">Radius</Select.Option>
              <Select.Option value="Credentials">Credentials</Select.Option>
            </Select>
          </Form.Item>
        </Col>
      </Row>
      <Row gutter={24}>
        <Col span={12}>
          <Form.Item
            name="description"
            label={t('common.description')}
          >
            <Input.TextArea disabled={isDisabled} rows={2} />
          </Form.Item>
        </Col>
      </Row>
      <Row gutter={24} style={{ borderRadius: '16px', border: '1px solid #E7E7E7', padding: '16px', margin: 0, marginBottom: '32px' }}>
        {/* Configuration */}
        <Col span={11} className="webroot-form">
          <h4 style={{ marginTop: 0, fontSize: '16px' }}>Configuration</h4>
          <Row gutter={24}>
            <Col span={18}>
              <Form.Item name="backgroundColor" label="Background" rules={[{ required: true, message: 'Background is required.' }]}>
                <Input type="text" disabled value={backgroundColor} readOnly style={{ marginRight: 12 }} />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Button className="upload-button" onClick={() => colorInputRef.current?.click()} disabled={isDisabled}>
                Color
              </Button>
              <input ref={colorInputRef} type="color" value={backgroundColor} disabled={isDisabled} className="input-upload-absolute"
                onChange={e => {
                  setBackgroundColor(e.target.value);
                  form.setFieldsValue({ backgroundColor: e.target.value });
                }}
              />
            </Col>
          </Row>
          <Row gutter={24}>
            <Col span={18}>
              <Form.Item name="logoBase64" label="Upload Logo">
                <Tooltip title={`PNG, JPG, JPEG and GIF, Must be less than ${defaultMaxLength.logo}kB`}>
                  <Input type="text" disabled readOnly value={logoBase64}
                    placeholder={`PNG, JPG, JPEG and GIF, Must be less than ${defaultMaxLength.logo}kB`}
                  />
                </Tooltip>
              </Form.Item>
            </Col>
            <Col span={6}>
              <Button className="upload-button" disabled={isDisabled}>Upload Logo</Button>
              <input ref={logoInputRef} type="file" accept="image/*" disabled={isDisabled} className="input-upload-absolute"
                onChange={e => {
                  const file = e.target.files?.[0];
                  if (!file) return;
                  if (!validateImage(file, defaultMaxLength.logo * 1024)) return;
                  const reader = new FileReader();
                  reader.onload = () => {
                    setLogoBase64(reader.result as string);
                    form.setFieldsValue({ logoBase64: reader.result as string });
                  };
                  reader.readAsDataURL(file);
                }}
              />
            </Col>
          </Row>
          <Row gutter={24}>
            <Col span={18}>
              <Form.Item name="welcomeMessage" label="Welcome Message"
                rules={[{ max: defaultMaxLength.welcomeMessage, message: `This length should be no more than ${defaultMaxLength.welcomeMessage}.` }]}
              >
                <Input disabled={isDisabled} onChange={e => debounceWelcome(e.target.value)} />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={24}>
            <Col span={18}>
              <Form.Item name="termsOfService" label="Terms of Service"
                rules={[{ max: defaultMaxLength.termsOfService, message: `This length should be no more than ${defaultMaxLength.termsOfService}.` }]}
              >
                <Input.TextArea disabled={isDisabled} rows={5} onChange={e => debounceTerms(e.target.value)} />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={24}>
            <Col span={18}>
              <Form.Item
                name="corporateInfo"
                label="Copyright"
                rules={[{ max: defaultMaxLength.corporateInfo, message: `This length should be no more than ${defaultMaxLength.corporateInfo}.` }]}
                className="form-item-uniform"
              >
                <Input.TextArea
                  disabled={isDisabled}
                  rows={5}
                  onChange={e => debounceCopyright(e.target.value)}
                />
              </Form.Item>
            </Col>
          </Row>
        </Col>
        {/* Preview */}
        <Col span={13} style={{ display: 'flex', flexDirection: 'column' }}>
          <h4 style={{ marginTop: 0, fontSize: '16px' }}>Preview</h4>
          <div className="previewBox">
            <iframe ref={iframeRef} className="previewContent"/>
          </div>
        </Col>
      </Row>
      <h3 className='header1'></h3>
      <Row className='foot-btns' style={{ marginRight: 0}}>
        <Button onClick={onClose} disabled={submitting}>
          Cancel
        </Button>
        <Button type="primary" htmlType="submit" loading={submitting}>
          Apply
        </Button>
      </Row>
    </Form>
  );
};

export default WebRootForm;