import React, { useState, useEffect } from "react";
import { Table, Button, Space, message, Modal } from "antd";
import { PlusOutlined } from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import { getWirelessProfileList, deleteWirelessProfile, getWirelessProfileDetail } from '@/modules-smb/Wireless/apis/wireless_profile_api';
import {confirmModalAction} from "@/modules-ampcon/components/custom_modal";
import WebrootForm from './WebrootForm/Form';


interface Props {
  siteId?: number;
}

const CaptivePortal: React.FC<Props> = ({ siteId = 0 }) => {
  // url中获取siteId
  if (window.location.hash) {
    const hash = window.location.hash.replace('#', '');
    if (/^\d+$/.test(hash)) {
      siteId = parseInt(hash, 10);
    }
  }
  const [data, setData] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [modalOpen, setModalOpen] = useState(false);
  const [editingResource, setEditingResource] = useState<any>(null);
  const [pagination, setPagination] = useState({ current: 1, pageSize: 10, total: 0 });
  const [sorter, setSorter] = useState<{ field?: string, order?: string }>({});

  // List
  const fetchList = (page = 1, pageSize = 10, sorterParam = sorter) => {
    setIsLoading(true);
    const sortFields = sorterParam.field
      ? [{ field: sorterParam.field, order: sorterParam.order }]
      : [];
    getWirelessProfileList(3, siteId, page, pageSize, [], sortFields)
      .then(res => {
        if (res?.status !== 200) {
          message.error(res?.info);
          return;
        }
        setData(res?.info || []);
        setPagination({
          current: page,
          pageSize,
          total: res?.total || 0,
        });
      })
      .catch(() => message.error('Failed to fetch list'))
      .finally(() => setIsLoading(false));
  };

  useEffect(() => {
    fetchList();
  }, [siteId]);

  // Delete
  const handleDelete = (record: any) => {
    confirmModalAction(
      `Are you sure you want to delete?`,
      () => {
        deleteWirelessProfile({ id: record.key })
          .then(res => {
            if (res?.status !== 200) {
              message.error(res?.info);
              return;
            }
            message.success('Deleted successfully');
            fetchList(pagination.current, pagination.pageSize);
          })
          .catch(() => message.error('Delete failed'));
      }
    );
  };

  // Edit
  const handleEdit = (record: any) => {
    getWirelessProfileDetail(record.key)
      .then(res => {
        if (res?.status !== 200) {
          message.error(res?.info);
          return;
        }
        // Parse parameter in detail
        let detail = res?.info || null;
        if (detail && typeof detail.parameter === 'string') {
          try {
            detail.parameter = JSON.parse(detail.parameter);
          } catch (e) {
            detail.parameter = {};
          }
        }
        setEditingResource(detail);
        setModalOpen(true);
      })
      .catch(() => message.error('Failed to fetch detail'));
  };

  const handleCreate = () => {
    setEditingResource(null);
    setModalOpen(true);
  };

  const handleCloseModal = () => {
    setModalOpen(false);
    setEditingResource(null);
  };

  const tableData = (data || []).map((item: any) => {
    // Parse parameter in detail
    let parameterObj = {};
    if (typeof item.parameter === 'string') {
      try {
        parameterObj = JSON.parse(item.parameter);
      } catch (e) {
        parameterObj = {};
      }
    } else if (typeof item.parameter === 'object' && item.parameter !== null) {
      parameterObj = item.parameter;
    }
    return {
      key: item.id,
      name: item.name,
      mode: parameterObj.mode,
      modified_time: item.modified_time || '',
      description: item.description || '',
      originResource: item,
    };
  });

  const columns: ColumnsType<any> = [
    { title: 'Name', dataIndex: 'name', key: 'name', sorter: true },
    { title: 'Mode', dataIndex: 'mode', key: 'mode', sorter: true },
    { title: 'Modified', dataIndex: 'modified_time', key: 'modified_time', sorter: true },
    { title: 'Description', dataIndex: 'description', key: 'description', sorter: true },
    {
      title: 'Operate',
      key: 'operate',
      render: (_: any, record: any) => (
        <Space>
          <Button type="text" size="small" onClick={() => handleEdit(record)}>
            Edit
          </Button>
          <Button type="text" size="small" onClick={() => handleDelete(record)}>
            Delete
          </Button>
        </Space>
      ),
    },
  ];

  return (
    <div>
      <div style={{ marginBottom: 16 }}>
        <Button type="primary" icon={<PlusOutlined />} onClick={handleCreate}>
          Create New Portal Webroot Profile
        </Button>
      </div>
      <Table
        columns={columns}
        dataSource={tableData}
        loading={isLoading}
        pagination={{
          current: pagination.current,
          pageSize: pagination.pageSize,
          total: pagination.total,
          showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`,
          showSizeChanger: true,
          pageSizeOptions: ["10", "20", "50", "100"],
          onChange: (page, pageSize) => fetchList(page, pageSize),
        }}
        scroll={{ x: 1000 }}
        rowKey="key"
        bordered
        onChange={(pagination, filters, sorterObj) => {
          let order = '';
          let field = '';
          if (!Array.isArray(sorterObj)) {
            if (sorterObj.order === 'ascend') order = 'asc';
            else if (sorterObj.order === 'descend') order = 'desc';
            if (typeof sorterObj.field === 'string') {
              // 排序字段名映射处理
              switch (sorterObj.field) {
                case 'mode':
                  field = 'parameter.mode';
                  break;
                default:
                  field = sorterObj.field;
              }
            } else field = '';
          }
          setSorter({ field, order });
          fetchList(pagination.current, pagination.pageSize, { field, order });
        }}
      />
      <Modal
        open={modalOpen}
        title={editingResource ? 'Edit Portal Webroot Profile' : 'Create Portal Webroot Profile'}
        onCancel={handleCloseModal}
        footer={null}
        width={1300}
        destroyOnClose
        styles={{ body: { maxHeight: '80vh', overflowY: 'auto', overflowX: 'hidden' } }}
      >
        <WebrootForm
          onClose={handleCloseModal}
          refresh={() => fetchList(pagination.current, pagination.pageSize)}
          resource={editingResource}
          isDisabled={false}
          siteId={siteId}
        />
      </Modal>
    </div>
  );
};

export default CaptivePortal;