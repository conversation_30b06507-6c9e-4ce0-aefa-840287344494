import React, { useState, useEffect } from "react";
import { Table, Button, Space, message, Modal } from "antd";
import { PlusOutlined } from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import { getWirelessProfileList, deleteWirelessProfile, getWirelessProfileDetail } from '@/modules-smb/Wireless/apis/wireless_profile_api';
import NetworkForm from './NetworkForm/Form';
import { confirmModalAction } from "@/modules-ampcon/components/custom_modal";


interface Props {
    siteId?: number;
}

const Network: React.FC<Props> = ({ siteId }) => {
    // url中获取siteId
    if (window.location.hash) {
        const hash = window.location.hash.replace('#', '');
        if (/^\d+$/.test(hash)) {
            siteId = parseInt(hash, 10);
        }
    }
    const [data, setData] = useState([]);
    const [isLoading, setIsLoading] = useState(false);
    const [modalOpen, setModalOpen] = useState(false);
    const [editingResource, setEditingResource] = useState<any>(null);
    const [pagination, setPagination] = useState({ current: 1, pageSize: 10, total: 0 });
    const [sortInfo, setSortInfo] = useState<{ id: any; sort: string; }[]>([]);


    // 列表查询：接收分页和排序信息
    const fetchList = (currentPagination: typeof pagination, currentSortInfo: typeof sortInfo) => {
        setIsLoading(true);
        // 调用API时传入排序信息（sortFields）
        getWirelessProfileList(
            1,
            siteId,
            currentPagination.current,
            currentPagination.pageSize,
            [],
            currentSortInfo,
            {}
        )
            .then(res => {
                if (res?.status !== 200) {
                    message.error(res?.info);
                    return;
                }
                setData(res?.info || []);
                setPagination({
                    ...currentPagination,
                    total: res?.total || 0, // 更新总条数
                });
            })
            .catch(() => message.error('Failed to fetch list'))
            .finally(() => setIsLoading(false));
    };


    useEffect(() => {
        fetchList(pagination, sortInfo);
    }, [siteId]);


    const handleTableChange = (pagination, filters, sorter) => {
        // 更新分页状态
        const updatedPagination = {
            current: pagination.current || 1,
            pageSize: pagination.pageSize || 10,
            total: pagination.total, // 总条数不变
        };
        let sortField = sorter.field;
        if (sorter.field === 'role') {
            sortField = 'parameter.role';
        } else if (sorter.field === 'vlan') {
            sortField = 'parameter.vlan';
        }

        const updatedSortInfo = sorter.field
            ? [{
                field: sortField,
                order: sorter.order === 'ascend' ? 'asc' : 'desc'
            }]
            : [];
        setSortInfo(updatedSortInfo);
        fetchList(updatedPagination, updatedSortInfo);
    };


    const handleDelete = (record: any) => {
        confirmModalAction(
            `This profile is currently in use by one or more configurations and cannot be deleted,
             Please update the relevant configurations to use a different profile before proceeding??`,
            () => {
                deleteWirelessProfile({ id: record.key })
                    .then(res => {
                        if (res?.status !== 200) {
                            message.error(res?.info);
                            return;
                        }
                        message.success('Deleted successfully');
                        fetchList(pagination, sortInfo); // 用当前分页和排序刷新
                    })
                    .catch(() => message.error('Delete failed'));
            }
        );
    };


    const handleEdit = (record: any) => {
        getWirelessProfileDetail(record.key)
            .then(res => {
                if (res?.status !== 200) {
                    message.error(res?.info);
                    return;
                }
                let detail = res?.info || null;
                if (detail && typeof detail.parameter === 'string') {
                    try {
                        detail.parameter = JSON.parse(detail.parameter);
                    } catch (e) {
                        detail.parameter = {};
                    }
                }
                setEditingResource(detail);
                setModalOpen(true);
            })
            .catch(() => message.error('Failed to fetch detail'));
    };


    const handleCopy = async (record: any) => {
        try {
            // 复制Network Name并添加_copy后缀
            const copyText = `${record.name}_copy`;
            await navigator.clipboard.writeText(copyText);
            message.success('Copied successfully');
        } catch (error) {
            message.error('Copy failed, please try again');
            console.error('Copy failed:', error);
        }
    };

    const handleCreate = () => {
        setEditingResource(null);
        setModalOpen(true);
    };

    // 关闭模态框
    const handleCloseModal = () => {
        setModalOpen(false);
        setEditingResource(null);
    };

    const tableData = (data || []).map((item: any) => {
        let parameterObj = {};
        if (typeof item.parameter === 'string') {
            try {
                parameterObj = JSON.parse(item.parameter);
            } catch (e) {
                parameterObj = {};
            }
        } else if (typeof item.parameter === 'object' && item.parameter !== null) {
            parameterObj = item.parameter;
        }
        return {
            key: item.id,
            name: item.name,
            role: parameterObj.role || '',
            vlan: parameterObj.vlan || '',
            modified_time: item.modified_time || '',
            description: item.description || '',
        };
    });

    const columns: ColumnsType<any> = [
        { title: 'Network Name', dataIndex: 'name', key: 'name', sorter: true },
        { title: 'Role', dataIndex: 'role', key: 'role', sorter: true },
        { title: 'VLAN', dataIndex: 'vlan', key: 'vlan', sorter: true },
        { title: 'Modified', dataIndex: 'modified_time', key: 'modified_time', sorter: true },
        { title: 'Description', dataIndex: 'description', key: 'description', sorter: true },
        {
            title: 'Operate',
            key: 'operate',
            render: (_: any, record: any) => (
                <Space>
                    <Button type="text" onClick={() => handleEdit(record)}>Edit</Button>
                    <Button type="text" onClick={() => handleCopy(record)}>Copy</Button>
                    <Button type="text" disabled={record.name.toLowerCase() === 'default'} onClick={() => handleDelete(record)}>Delete</Button>
                </Space>
            ),
        },
    ];
    return (
        <div>
            <div style={{ marginBottom: 16 }}>
                <Button type="primary" icon={<PlusOutlined />} onClick={handleCreate}>
                    Create Network Profile
                </Button>
            </div>
            <Table
                columns={columns}
                dataSource={tableData}
                loading={isLoading}
                onChange={handleTableChange} // 绑定表格变化事件
                pagination={{
                    current: pagination.current,
                    pageSize: pagination.pageSize,
                    total: pagination.total,
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: (total) => `Total ${total} items`,
                    pageSizeOptions: ['10', '20', '50', '100'],
                }}
                scroll={{ x: 1000 }}
                rowKey="key"
                bordered
            />
            <Modal
                open={modalOpen}
                title={editingResource ? 'Edit Network Profile' : 'Create Network Profile'}
                onCancel={handleCloseModal}
                footer={null}
                width={1000}
                destroyOnClose
                styles={{ body: { maxHeight: '70vh', overflowY: 'auto', overflowX: 'hidden' } }}
            >
                <NetworkForm
                    onClose={handleCloseModal}
                    refresh={() => fetchList(pagination, sortInfo)} // 刷新时用当前分页和排序
                    resource={editingResource}
                    siteId={siteId}
                />
            </Modal>
        </div>
    );
};

export default Network;