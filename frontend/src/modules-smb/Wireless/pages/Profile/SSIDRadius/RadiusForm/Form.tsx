import React, { useState } from 'react';
import { PlusOutlined } from '@ant-design/icons';
import { Form, Input, Select, Button, Switch, Row, Col, message, InputNumber, Table } from 'antd';
import UsersForm from './UsersForm';
import { useTranslation } from 'react-i18next';
import { createWirelessProfile, updateWirelessProfile } from '@/modules-smb/Wireless/apis/wireless_profile_api';
import '@/modules-smb/Wireless/assets/form.scss';

interface Props {
  isDisabled?: boolean;
  resource?: any;
  onClose?: () => void;
  refresh?: () => void;
  siteId?: number;
}

const defaultValues = {
  name: 'NAME',
  description: '',
  mode: 'External',
  authentication: {
    host: '***************',
    port: 1812,
    secret: 'YOUR_SECRET',
    'mac-filter': false,
  },
  accounting: undefined,
  'dynamic-authorization': undefined,
  'nas-identifier': '',
  'chargeable-user-id': false,
  local: {
    'server-identity': 'uCentral',
    users: [],
  },
};

const getInitialValues = (resource: any) => {
  if (!resource) return { ...defaultValues };

  let parsedVars = {};
  let modeVal = 'External';

  try {
    if (resource.config_variables) {
      const arr = JSON.parse(resource.config_variables);
      if (Array.isArray(arr) && arr[0]?.value) {
        parsedVars = typeof arr[0].value === 'string'
          ? JSON.parse(arr[0].value)
          : arr[0].value;
      }
    }
    if (resource.parameter) {
      let paramObj = resource.parameter;
      if (typeof paramObj === 'string') {
        try { paramObj = JSON.parse(paramObj); } catch {}
      }
      if (paramObj && paramObj.type) {
        modeVal = paramObj.type;
      }
    }
  } catch {}

  return {
    ...parsedVars,
    name: resource.name,
    description: resource.description,
    mode: modeVal,
  };
};

const RadiusForm: React.FC<Props> = ({ isDisabled = false, resource, onClose, refresh, siteId }) => {
  const { t } = useTranslation();
  const [form] = Form.useForm();
  const initialVals = getInitialValues(resource);
  const [mode, setMode] = useState(initialVals.mode);
  const [submitting, setSubmitting] = useState(false);
  const [accountingEnabled, setAccountingEnabled] = useState(!!(initialVals && (initialVals as any).accounting));
  const [dynamicEnabled, setDynamicEnabled] = useState(!!(initialVals && (initialVals as any)['dynamic-authorization']));

  const handleFinish = async (values: any) => {
    setSubmitting(true);
    try {
      // 生成 parameter
      const parameter = {
        type: values.mode || 'External',
        auth_server_host: values.authentication?.host || '-',
        port: values.authentication?.port || '-',
      };
      // 生成 config_variables
      const { name, mode, description, ...pureValue } = values;
      const config_variables = JSON.stringify([
        {
          type: 'json',
          weight: 0,
          prefix: 'interface.ssid.radius',
          value: JSON.stringify(pureValue),
        },
      ]);
      let res;
      if (resource && resource.id) {
        res = await updateWirelessProfile({
          id: resource.id,
          site_id: siteId,
          type: 1,
          name: values.name,
          parameter,
          description: values.description,
          config_variables,
        });
        if (res?.status !== 200) {
          message.error(res?.info || t('crud.error_update_obj', { obj: t('resources.configuration_resource') }));
          setSubmitting(false);
          return;
        }
        message.success(t('crud.success_update_obj', { obj: t('resources.configuration_resource') }));
      } else {
        res = await createWirelessProfile({
          site_id: siteId,
          type: 1,
          name: values.name,
          parameter,
          description: values.description,
          config_variables,
        });
        if (res?.status !== 200) {
          message.error(res?.info || t('crud.error_create_obj', { obj: t('resources.configuration_resource') }));
          setSubmitting(false);
          return;
        }
        message.success(t('crud.success_create_obj', { obj: t('resources.configuration_resource') }));
      }
      refresh && refresh();
      onClose && onClose();
    } catch (e) {
      message.error(t('crud.error_create_obj', { obj: t('resources.configuration_resource') }));
    } finally {
      setSubmitting(false);
    }
  };

  // 动态表单项条件
  const isLocal = mode === 'Local';
  const isFieldDisabled = isLocal || isDisabled;

  // 动态表单项: Local
  const [userPage, setUserPage] = useState(1);
  const [userPageSize, setUserPageSize] = useState(10);
  const users = Form.useWatch(['local', 'users'], form) || [];
  const [userModalOpen, setUserModalOpen] = useState(false);
  const handleAddUser = () => setUserModalOpen(true);
  const handleSaveUser = (values: any) => {
    form.setFieldValue(['local', 'users'], [values, ...users]);
    setUserModalOpen(false);
    setUserPage(1);
  };
  const handleDeleteUser = (idx: number) => {
    const newUsers = [...users];
    newUsers.splice(idx, 1);
    form.setFieldValue(['local', 'users'], newUsers);
    if ((userPage - 1) * userPageSize >= newUsers.length && userPage > 1) {
      setUserPage(userPage - 1);
    }
  };
  const pagedUsers = users.slice((userPage - 1) * userPageSize, userPage * userPageSize);
  const userColumns = [
    { title: 'MAC', dataIndex: 'mac', key: 'mac' },
    { title: 'User Name', dataIndex: 'user-name', key: 'user-name' },
    { title: 'Password', dataIndex: 'password', key: 'password' },
    { title: 'VLAN ID', dataIndex: 'vlan-id', key: 'vlan-id' },
    {
      title: 'Operate',
      key: 'action',
      render: (_: any, _record: any, idx: number) => (
        <Button type="text" onClick={() => handleDeleteUser((userPage - 1) * userPageSize + idx)}>
          Delete
        </Button>
      ),
    },
  ];
  const renderLocalUsers = () => (
    <>
      <Row gutter={24}>
        <Col span={8} style={{ display: 'none' }}>
          <Form.Item
            name={['local', 'server-identity']}
            label="Server Identity"
            rules={[{ required: true, message: t('form.required') }]}
            initialValue="uCentral"
          >
            <Input disabled={isDisabled} />
          </Form.Item>
        </Col>
        <Col span={24} style={{ display: 'none' }}>
          <Form.List name={['local', 'users']}>
            {(fields) => (
              <>
                {fields.map(() => null)}
              </>
            )}
          </Form.List>
        </Col>
      </Row>
      <Row gutter={24}>
        <Col span={24}>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 8 }}>
              <Button type="primary" icon={<PlusOutlined />} onClick={handleAddUser} disabled={isDisabled}>
                Add User
              </Button>
            </div>
            <Table
              columns={userColumns}
              dataSource={pagedUsers}
              pagination={{
                current: userPage,
                pageSize: userPageSize,
                total: users.length,
                showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`,
                showSizeChanger: true,
                pageSizeOptions: ['5', '10', '20', '50'],
                onChange: (page, pageSize) => {
                  setUserPage(page);
                  setUserPageSize(pageSize);
                },
              }}
              rowKey={(_record, idx) => (typeof idx === 'number' ? idx.toString() : '')}
              size="middle"
              bordered
            />
            <UsersForm
              open={userModalOpen}
              onCancel={() => setUserModalOpen(false)}
              onOk={handleSaveUser}
              isDisabled={isDisabled}
            />
          </Col>
      </Row>
    </>
  );

  // 动态表单项：External
  const renderExternalFields = () => (
    <>
      <Row gutter={24}>
        <Col span={12}>
          <Form.Item
            name={['authentication', 'host']}
            label="Authentication Host"
            rules={[{ required: true, message: t('form.required') }]}
          >
            <Input disabled={isFieldDisabled} />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item
            name={['authentication', 'port']}
            label="Authentication Port"
            rules={[
              { required: true, message: t('form.required') },
              { type: 'number', min: 1, message: 'port must be a positive number' },
              { type: 'number', max: 4049, message: 'port must be less than 4050' },
            ]}
          >
            <InputNumber disabled={isFieldDisabled} />
          </Form.Item>
        </Col>
      </Row>
      <Row gutter={24}>
        <Col span={12}>
            <Form.Item
              name={['authentication', 'secret']}
              label="Authentication Secret"
              rules={[{ required: true, message: t('form.required') }]}
            >
              <Input.Password disabled={isFieldDisabled} />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name={['authentication', 'mac-filter']}
              label="MAC Filter"
              valuePropName="checked"
            >
              <Switch disabled={isFieldDisabled} />
            </Form.Item>
          </Col>
        </Row>
      <Row gutter={24}>
        <Col span={12}>
          <Form.Item
            label="Enable Accounting"
          >
            <Switch
              checked={accountingEnabled}
              disabled={isFieldDisabled}
              onChange={checked => {
                setAccountingEnabled(checked);
                if (checked) {
                  form.setFieldsValue({
                    accounting: { host: '***************', port: 1813, secret: 'YOUR_SECRET' },
                  });
                } else {
                  form.setFieldsValue({ accounting: undefined });
                }
              }}
            />
          </Form.Item>
        </Col>
        {accountingEnabled && (
          <Col span={12}>
            <Form.Item
              name={['accounting', 'host']}
              label="Accounting Host"
              rules={[{ required: true, message: t('form.required') }]}
            >
              <Input disabled={isFieldDisabled} />
            </Form.Item>
          </Col>
        )}
      </Row>
      {accountingEnabled && (
        <Row gutter={24}>
          <Col span={12}>
            <Form.Item
              name={['accounting', 'port']}
              label="Accounting Port"
              rules={[
                { required: true, message: t('form.required') },
                { type: 'number', min: 1, message: 'port must be a positive number' },
                { type: 'number', max: 4049, message: 'port must be less than 4050' },
              ]}
            >
              <InputNumber disabled={isFieldDisabled} />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name={['accounting', 'secret']}
              label="Accounting Secret"
              rules={[{ required: true, message: t('form.required') }]}
            >
              <Input.Password disabled={isFieldDisabled} />
            </Form.Item>
          </Col>
        </Row>
        )}
      {/* Enable Dynamic Authorization */}
      <Row gutter={24}>
        <Col span={12}>
          <Form.Item label="Enable Dynamic Auth">
            <Switch
              checked={dynamicEnabled}
              disabled={isFieldDisabled}
              onChange={checked => {
                setDynamicEnabled(checked);
                if (checked) {
                  form.setFieldsValue({
                    'dynamic-authorization': { host: '***************', port: 1814, secret: 'YOUR_SECRET' },
                  });
                } else {
                  form.setFieldsValue({ 'dynamic-authorization': undefined });
                }
              }}
            />
          </Form.Item>
        </Col>
        {dynamicEnabled && (
          <Col span={12}>
            <Form.Item
              name={['dynamic-authorization', 'host']}
              label="Dynamic Auth Host"
              rules={[{ required: true, message: t('form.required') }]}
            >
              <Input disabled={isFieldDisabled} />
            </Form.Item>
          </Col>
        )}
      </Row>
      {dynamicEnabled && (
        <Row gutter={24}>
          <Col span={12}>
            <Form.Item
              name={['dynamic-authorization', 'port']}
              label="Dynamic Auth Port"
              rules={[
                { required: true, message: t('form.required') },
                { type: 'number', min: 1, message: 'port must be a positive number' },
                { type: 'number', max: 4049, message: 'port must be less than 4050' },
              ]}
            >
              <InputNumber disabled={isFieldDisabled} />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name={['dynamic-authorization', 'secret']}
              label="Dynamic Auth Secret"
              rules={[{ required: true, message: t('form.required') }]}
            >
              <Input.Password disabled={isFieldDisabled} />
            </Form.Item>
          </Col>
        </Row>
      )}
      <Row gutter={24}>
        <Col span={12}>
          <Form.Item
            name="nas-identifier"
            label="NAS Identifier"
          >
            <Input disabled={isFieldDisabled} />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item
            name="chargeable-user-id"
            label="Chargeable User ID"
            valuePropName="checked"
          >
            <Switch disabled={isFieldDisabled} />
          </Form.Item>
        </Col>
      </Row>
    </>
  );

  return (
    <Form
      form={form}
      initialValues={initialVals}
      onFinish={handleFinish}
      disabled={isDisabled}
      className="wirelessForm"
      onValuesChange={(_, allValues) => { if (allValues.mode !== mode) setMode(allValues.mode);}}
    >
      <h3 className='header1'></h3>
      <Row gutter={24}>
        <Col span={12}>
          <Form.Item
            name="name"
            label={t('common.name')}
            rules={[{ required: true, message: t('form.required') }]}
          >
            <Input disabled={isDisabled} />
          </Form.Item>
        </Col>
      </Row>
      <Row gutter={24}>
        <Col span={12}>
          <Form.Item
            name="mode"
            label="Mode"
            rules={[{ required: true, message: t('form.required') }]}
          >
            <Select
              disabled={isDisabled}
              options={[
                { label: 'External', value: 'External' },
                { label: 'Local', value: 'Local' },
              ]}
            />
          </Form.Item>
        </Col>
      </Row>
      <Row gutter={24}>
        <Col span={12}>
          <Form.Item
            name="description"
            label={t('common.description')}
          >
            <Input.TextArea disabled={isDisabled} rows={2} />
          </Form.Item>
        </Col>
      </Row>
      {isLocal ? renderLocalUsers() : renderExternalFields()}
      <div className='foot-btns'>
        <Button onClick={onClose} disabled={submitting}>
          Cancel
        </Button>
        <Button type="primary" htmlType="submit" loading={submitting}>
          Apply
        </Button>
      </div>
    </Form>
  );
};

export default RadiusForm;
