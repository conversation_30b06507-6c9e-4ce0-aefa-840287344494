import React, { useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Modal, Form, Input, InputNumber } from 'antd';

interface UsersFormProps {
  open: boolean;
  onCancel: () => void;
  onOk: (values: any) => void;
  isDisabled?: boolean;
}

const initialUser = { mac: '', 'user-name': '', password: '', 'vlan-id': 1 };

const UsersForm: React.FC<UsersFormProps> = ({ open, onCancel, onOk, isDisabled }) => {
  const { t } = useTranslation();
  const [form] = Form.useForm();

  useEffect(() => {
    if (open) {
      form.setFieldsValue(initialUser);
    }
  }, [open]);
  return (
    <Modal
      open={open}
      title='Add User'
      onCancel={onCancel}
      onOk={() => {
        form.validateFields().then(values => {
          onOk(values);
          form.resetFields();
        });
      }}
      okText='Apply'
      cancelText='Cancel'
      destroyOnClose
    >
      <Form form={form} className="wirelessForm">
        <Form.Item
          label="mac"
          name="mac"
          rules={[
            { required: true, message: t('form.required') },
            { pattern: /^([0-9A-Fa-f]{2}:){5}[0-9A-Fa-f]{2}$/, message: t('form.invalid_mac_uc') },
          ]}
        >
          <Input disabled={isDisabled} />
        </Form.Item>
        <Form.Item
          label="user-name"
          name="user-name"
          rules={[{ required: true, message: t('form.required') }]}
        >
          <Input disabled={isDisabled} />
        </Form.Item>
        <Form.Item
          label="password"
          name="password"
          rules={[
            { required: true, message: t('form.required') },
            { min: 8, max: 63, message: t('form.min_max_string', { min: 8, max: 63 }) },
          ]}
        >
          <Input.Password disabled={isDisabled} />
        </Form.Item>
        <Form.Item
          label="vlan-id"
          name="vlan-id"
          rules={[
            { required: true, message: t('form.required') },
            { type: 'number', min: 1, max: 4096, message: 'vlan-id must be less than 4097' },
          ]}
        >
          <InputNumber disabled={isDisabled} />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default UsersForm; 