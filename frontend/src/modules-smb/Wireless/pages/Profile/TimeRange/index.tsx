import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, Card, Space, Popconfirm, message, Table, Divider, Modal } from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import CreateTimeRange from '@/modules-smb/Wireless/pages/Profile/TimeRange/CreateTimeRanger';
import {
  createColumnConfigMultipleParams,
  TableFilterDropdown,
  // GlobalSearchInput,
} from '@/modules-ampcon/components/custom_table';
import {confirmModalAction} from "@/modules-ampcon/components/custom_modal";
import {
  createWirelessProfile,
  updateWirelessProfile,
  getWirelessProfileList,
  deleteWirelessProfile,
} from '@/modules-smb/Wireless/apis/wireless_profile_api';

interface TimeRangeProps {
  siteId?: number;
}

interface TimeRangeItem {
  id: string;
  name: string;
  timeRange: string;
  modify: string;
  description: string;
  parameter: string;
}

const TimeRange: React.FC<TimeRangeProps> = ({ siteId }) => {
  const [data, setData] = useState<TimeRangeItem[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isFormVisible, setIsFormVisible] = useState(false);
  const [editingRecord, setEditingRecord] = useState<TimeRangeItem | null>(null);
  const [pagination, setPagination] = useState({ current: 1, pageSize: 10, total: 0 });
  const [searchValue, setSearchValue] = useState('');
  const [sorter, setSorter] = useState<{ field?: string; order?: string }>({});
  const type = 4;
  // url中获取siteId
  if (window.location.hash) {
    const hash = window.location.hash.replace('#', '');
    if (/^\d+$/.test(hash)) {
      siteId = parseInt(hash, 10);
    }
  }

  const parseTimeRangeStr = (parameterStr: string): string => {
    try {
      const paramObj = JSON.parse(parameterStr);
      const timeRangeArr = paramObj.time_range;
      return Array.isArray(timeRangeArr) ? timeRangeArr.join('\n') : 'No time range';
    } catch {
      return 'Invalid time range';
    }
  };

  const fetchList = async (page = 1, pageSize = 10) => {
    setIsLoading(true);
    try {
      const res = await getWirelessProfileList(type, siteId, page, pageSize);
      if (res.status === 200 && Array.isArray(res.info)) {
        const processed = res.info
          .map((item: any) => ({
            ...item,
            key: item.id,
            timeRange: parseTimeRangeStr(item.parameter),
            modify: item.modified_time || item.modify || '',
          }))
          .filter(item => {
            if (!searchValue) return true;
            const val = searchValue.toLowerCase();
            return (
              item.name?.toLowerCase().includes(val) ||
              item.description?.toLowerCase().includes(val) ||
              item.timeRange?.toLowerCase().includes(val)
            );
          })
          .sort((a, b) => {
            if (!sorter.field || !sorter.order) return 0;
            const aVal = a[sorter.field]?.toString() ?? '';
            const bVal = b[sorter.field]?.toString() ?? '';
            const direction = sorter.order === 'ascend' ? 1 : -1;
            return aVal.localeCompare(bVal, 'zh-Hans-CN', { numeric: true }) * direction;
          });

        setData(processed);
        setPagination({
          current: page,
          pageSize,
          total: res.total || 0,
        });
      } else {
        message.error('Failed to fetch profile list');
      }
    } catch {
      message.error('Failed to fetch profile list');
    }
    setIsLoading(false);
  };

  useEffect(() => {
    fetchList(pagination.current, pagination.pageSize);
  }, [searchValue, sorter, siteId]);

  const handleDelete = (record: any) => {
    confirmModalAction(
      `Are you sure you want to delete?`,
      () => {
        deleteWirelessProfile({ id: record.key })
          .then(res => {
            if (res?.status !== 200) {
              message.error(res?.info);
              return;
            }
            message.success('Deleted successfully');
            fetchList(pagination.current, pagination.pageSize);
          })
          .catch(() => message.error('Delete failed'));
      }
    );
  };

  const handleCreate = async (formData: { name: string; description?: string; timeRange?: string; parameter?: string }) => {
    try {
      const commonParams = {
        site_id: siteId,
        type,
        name: formData.name,
        description: formData.description || '',
        parameter: formData.parameter,
        config_variables: JSON.stringify({}),
      };

      const res = editingRecord
        ? await updateWirelessProfile({ ...commonParams, id: editingRecord.id })
        : await createWirelessProfile(commonParams);

      if (res.status === 200) {
        message.success(editingRecord ? 'Updated successfully' : 'Created successfully');
        fetchList(pagination.current, pagination.pageSize);
        setIsFormVisible(false);
        setEditingRecord(null);
      } else {
        message.error(
          editingRecord
            ? `Update failed: ${res.info || 'Unknown error'}`
            : `Create failed: ${res.info || 'Unknown error'}`
        );
      }
    } catch {
      message.error(
        editingRecord
          ? `Update failed: Unknown error`
          : `Create failed: Unknown error`
      );
    }
  };

  // const handleSearchChange = (val: string) => {
  //   setSearchValue(val.trim());
  // };

  const handleTableChange = (paginationChange: any, _: any, sorterChange: any) => {
    setPagination({
      ...pagination,
      current: paginationChange.current,
      pageSize: paginationChange.pageSize,
    });

    if (sorterChange?.order) {
      setSorter({ field: sorterChange.field, order: sorterChange.order });
    } else {
      setSorter({});
    }
  };

  const columns = [
    createColumnConfigMultipleParams({
      title: 'Name',
      dataIndex: 'name',
      enableSorter: true,
      enableFilter: false,
      filterDropdownComponent: TableFilterDropdown,
      defaultValue: '',
      width: '15%',
    }),
    createColumnConfigMultipleParams({
      title: 'Time Range',
      dataIndex: 'timeRange',
      enableSorter: false,
      enableFilter: false,
      filterDropdownComponent: TableFilterDropdown,
      render: (val: string) => <div style={{ whiteSpace: 'pre-wrap' }}>{val}</div>,
      width: '20%',
    }),
    createColumnConfigMultipleParams({
      title: 'Modify',
      dataIndex: 'modify',
      enableSorter: true,
      enableFilter: false,
      width: '15%',
    }),
    createColumnConfigMultipleParams({
      title: 'Description',
      dataIndex: 'description',
      enableSorter: false,
      enableFilter: false,
      filterDropdownComponent: TableFilterDropdown,
      width: '20%',
    }),
    {
      title: 'Operate',
      width: '15%',
      render: (_: any, record: TimeRangeItem) => (
        <Space>
          <Button
            type="link"
            onClick={() => {
              setEditingRecord(record);
              setIsFormVisible(true);
            }}
          >
            Edit
          </Button>
          <Button type="text" onClick={() => handleDelete(record)}>
            Delete
          </Button>
        </Space>
      ),
    },
  ];

  return (
    <div style={{ height: '100vh', width: '100%', overflow: 'auto', padding: 0 }}>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 16 }}>
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={() => {
            setEditingRecord(null);
            setIsFormVisible(true);
          }}
        >
          Create New Time Range Profile
        </Button>
      </div>
      <Table
        columns={columns}
        dataSource={data}
        loading={isLoading}
        onChange={handleTableChange}
        pagination={{
          current: pagination.current,
          pageSize: pagination.pageSize,
          total: pagination.total,
          showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`,
          showSizeChanger: true,
          pageSizeOptions: ['10', '20', '50', '100'],
        }}
        scroll={{ x: 1000 }}
        rowKey="key"
        bordered
      />
      <Modal
        title={editingRecord ? 'Edit Time Range Profile' : 'Create Time Range Profile'}
        open={isFormVisible}
        onCancel={() => {
          setIsFormVisible(false);
          setEditingRecord(null);
        }}
        footer={null}
        width={900}
        destroyOnClose
        styles={{ body: { maxHeight: '70vh', overflowY: 'auto', overflowX: 'hidden' } }}
      >
        <Divider style={{ marginTop: 8, marginBottom: 16 }} />

        <CreateTimeRange
          initialValues={editingRecord ?? undefined}
          onSave={handleCreate}
          onClose={() => {
            setIsFormVisible(false);
            setEditingRecord(null);
          }}
        />
      </Modal>
    </div>
  );
};

export default TimeRange;
