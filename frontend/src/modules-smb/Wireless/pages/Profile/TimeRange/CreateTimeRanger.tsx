import React, { useEffect, useState } from 'react';
import { Form, Input, Button, Checkbox, TimePicker, Row, Col, message ,Divider} from 'antd';
import dayjs from 'dayjs';

interface CreateTimeRangeProps {
  initialValues?: {
    name?: string;
    description?: string;
    parameter?: string;
  };
  onSave: (formData: {
    name: string;
    description?: string;
    parameter: string;
  }) => void;
  onClose: () => void;
}

interface TimeRangeItem {
  day: string;
  checked: boolean;
  startTime: dayjs.Dayjs | null;
  endTime: dayjs.Dayjs | null;
}

interface TimeRangeFormFieldsProps {
  form: any;
  timeRanges: TimeRangeItem[];
  onCheckChange: (index: number, checked: boolean) => void;
  onStartTimeChange: (index: number, time: dayjs.Dayjs | null) => void;
  onEndTimeChange: (index: number, time: dayjs.Dayjs | null) => void;
}

const dayList = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];

const CreateTimeRange: React.FC<CreateTimeRangeProps> = ({ initialValues, onSave, onClose }) => {
  const [form] = Form.useForm();

  const [timeRanges, setTimeRanges] = useState<TimeRangeItem[]>(
    dayList.map(day => ({
      day,
      checked: false,
      startTime: null,
      endTime: null,
    }))
  );

  useEffect(() => {
    if (initialValues) {
      form.setFieldsValue({
        name: initialValues.name || '',
        description: initialValues.description || '',
      });

      if (initialValues.parameter) {
        try {
          const paramObj = JSON.parse(initialValues.parameter);
          if (Array.isArray(paramObj.time_range)) {
            const newRanges = dayList.map(day => {
              const matched = paramObj.time_range.find((entry: string) => entry.startsWith(day));
              if (matched) {
                const m = matched.match(/^(\w+)\s+(\d{2}:\d{2})-(\d{2}:\d{2})$/);
                if (m) {
                  const [, , start, end] = m;
                  return {
                    day,
                    checked: true,
                    startTime: dayjs(start, 'HH:mm'),
                    endTime: dayjs(end, 'HH:mm'),
                  };
                }
              }
              return {
                day,
                checked: false,
                startTime: null,
                endTime: null,
              };
            });
            setTimeRanges(newRanges);
            return;
          }
        } catch (error) {
          console.warn('parameter parse error:', error);
        }
      }
    } else {
      form.resetFields();
      setTimeRanges(
        dayList.map(day => ({
          day,
          checked: false,
          startTime: null,
          endTime: null,
        }))
      );
    }
  }, [initialValues, form]);

  // 勾选时自动填默认时间，取消时清空时间
  const onCheckChange = (index: number, checked: boolean) => {
    const newRanges = [...timeRanges];
    newRanges[index].checked = checked;
    if (checked) {
      newRanges[index].startTime = newRanges[index].startTime || dayjs('08:00', 'HH:mm');
      newRanges[index].endTime = newRanges[index].endTime || dayjs('18:00', 'HH:mm');
    } else {
      newRanges[index].startTime = null;
      newRanges[index].endTime = null;
    }
    setTimeRanges(newRanges);
  };

  // 开始时间改变
  const onStartTimeChange = (index: number, time: dayjs.Dayjs | null) => {
    const newRanges = [...timeRanges];
    newRanges[index].startTime = time;
    if (time && newRanges[index].endTime && time.isAfter(newRanges[index].endTime)) {
      newRanges[index].endTime = time;
    }
    setTimeRanges(newRanges);
  };

  // 结束时间改变
  const onEndTimeChange = (index: number, time: dayjs.Dayjs | null) => {
    const newRanges = [...timeRanges];
    newRanges[index].endTime = time;
    if (time && newRanges[index].startTime && time.isBefore(newRanges[index].startTime)) {
      newRanges[index].startTime = time;
    }
    setTimeRanges(newRanges);
  };

  const handleApply = async () => {
    const hasChecked = timeRanges.some(item => item.checked);
    if (!hasChecked) {
      message.error('Please select at least one day.');
      return;
    }

    try {
      const values = await form.validateFields();
      onSave({
        name: values.name,
        description: values.description,
        parameter: JSON.stringify({
          time_range: timeRanges
            .filter(item => item.checked)
            .map(
              item =>
                `${item.day} ${item.startTime!.format('HH:mm')}-${item.endTime!.format('HH:mm')}`
            ),
        }),
      });
      form.resetFields();
      setTimeRanges(
        dayList.map(day => ({
          day,
          checked: false,
          startTime: null,
          endTime: null,
        }))
      );
    } catch {
      message.error('Validation failed!');
    }
  };

  // 取消
  const handleCancel = () => {
    form.resetFields();
    onClose();
  };

  return (
    <div>
      <Form form={form} layout="horizontal" labelAlign="left" labelCol={{ span: 4 }} wrapperCol={{ span: 19 }}>
        <TimeRangeFormFields
          form={form}
          timeRanges={timeRanges}
          onCheckChange={onCheckChange}
          onStartTimeChange={onStartTimeChange}
          onEndTimeChange={onEndTimeChange}
        />
        <Divider style={{marginTop: 16, marginBottom: 16}} />
        <Form.Item wrapperCol={{ offset: 6 }}>
          <div style={{ display: 'flex', justifyContent: 'flex-end', gap: 12 }}>
            <Button htmlType="button" onClick={handleCancel} style={{ width: 100 }}>
              Cancel
            </Button>
            <Button type="primary" onClick={handleApply} style={{ width: 100 }}>
              Apply
            </Button>
          </div>
        </Form.Item>
      </Form>
    </div>
  );
};

export default CreateTimeRange;


export const TimeRangeFormFields: React.FC<TimeRangeFormFieldsProps> = ({
  form,
  timeRanges,
  onCheckChange,
  onStartTimeChange,
  onEndTimeChange,
}) => {
  const calcProgress = (start: dayjs.Dayjs, end: dayjs.Dayjs) => {
    const startDecimal = start.hour() + start.minute() / 60;
    const endDecimal = end.hour() + end.minute() / 60;
    return ((endDecimal - startDecimal) / 24) * 100;
  };

  const calcProgressLeft = (start: dayjs.Dayjs) => {
    const startDecimal = start.hour() + start.minute() / 60;
    return (startDecimal / 24) * 100;
  };

  return (
    <>
      <Form.Item
        name="name"
        label="Name"
        rules={[{ required: true, message: 'Please input the name' }]}
      >
        <Input style={{ width: 248 }} placeholder="Enter name" />
      </Form.Item>
      <Form.Item name="description" label="Description">
        <Input.TextArea style={{ width: 248 }} rows={3} placeholder="Enter description" />
      </Form.Item>
      {!timeRanges.some(item => item.checked) && (
        <div style={{ color: '#ff4d4f', marginBottom: 12 }}>
          Please select at least one day.
        </div>
      )}
      {timeRanges.map((item, index) => (
        <Row key={item.day} align="middle" style={{ marginBottom: 10 }}>
          <Col span={4}>
            <Checkbox
              checked={item.checked}
              onChange={e => onCheckChange(index, e.target.checked)}
            >
              {item.day}
            </Checkbox>
          </Col>
          <Col span={3}>
            <TimePicker
              value={item.startTime}
              onChange={val => onStartTimeChange(index, val)}
              disabled={!item.checked}
              format="HH:mm"
              style={{ width: '100%' }}
              allowClear
              placeholder=""
            />
          </Col>
          <Col span={1} style={{ textAlign: 'center' }}>
            —
          </Col>
          <Col span={3}>
            <TimePicker
              value={item.endTime}
              onChange={val => onEndTimeChange(index, val)}
              disabled={!item.checked}
              format="HH:mm"
              style={{ width: '100%' }}
              allowClear
              placeholder=""
            />
          </Col>
          <Col span={10} style={{ paddingLeft: 16 }}>
            <div
              style={{
                position: 'relative',
                height: 16,
                backgroundColor: '#eee',
                borderRadius: 0,
                top: 4,
              }}
            >
              <div
                style={{
                  position: 'absolute',
                  left:
                    item.startTime && item.endTime
                      ? `${calcProgressLeft(item.startTime)}%`
                      : '0%',
                  width:
                    item.startTime && item.endTime
                      ? `${calcProgress(item.startTime, item.endTime)}%`
                      : '0%',
                  height: '100%',
                  backgroundColor: '#52c41a',
                  borderRadius: 0,
                  transition: 'width 0.3s ease',
                }}
              />
              {Array.from({ length: 25 }).map((_, i) => (
                <div
                  key={i}
                  style={{
                    position: 'absolute',
                    left: `${(i / 24) * 100}%`,
                    top: 0,
                    bottom: 0,
                    width: 1,
                    backgroundColor: '#999',
                    opacity: 0.3,
                  }}
                />
              ))}
            </div>

            <div style={{ position: 'relative', height: 16, marginTop: 1 }}>
              {Array.from({ length: 13 }).map((_, i) => {
                const hour = i * 2;
                return (
                  <div
                    key={hour}
                    style={{
                      position: 'absolute',
                      left: `${(hour / 24) * 100}%`,
                      transform: 'translateX(-50%)',
                      fontSize: 11,
                      color: '#333',
                      top: 2,
                    }}
                  >
                    {hour}
                  </div>
                );
              })}
            </div>
          </Col>
        </Row>
      ))}
    </>
  );
};