import React, { useState, useEffect } from 'react';
import { Form, Input, InputNumber, Button, Table, Space, message, Modal } from 'antd';
import { Formik } from 'formik';
import { useTranslation } from 'react-i18next';
import { Resource } from '@/modules-smb/models/Resource';
import {
    createWirelessProfile,
    updateWirelessProfile
} from "@/modules-smb/Wireless/apis/wireless_profile_api";
import { ExclamationCircleFilled } from '@ant-design/icons';


interface MPSKEntry {
    mac: string;
    key: string;
    vlan_id?: number;
}

interface FullPageMPSKFormProps {
    editingProfile?: Resource;
    onClose: (success?: boolean) => void;
    siteId: number;
}

interface ConfigVariable {
    type: string;
    weight: number;
    prefix: string;
    value: string;
}

const FullPageMPSKForm: React.FC<FullPageMPSKFormProps> = ({
    editingProfile,
    onClose,
    siteId
}) => {
    const { t } = useTranslation();
    const [initialValues, setInitialValues] = useState({
        name: '',
        description: ''
    });
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [modalVisible, setModalVisible] = useState(false);
    const [currentEntry, setCurrentEntry] = useState<MPSKEntry>({
        mac: '',
        key: ''
    });
    const [deleteConfirmVisible, setDeleteConfirmVisible] = useState(false);
    const [deletingIndex, setDeletingIndex] = useState<number | null>(null);

    const [entries, setEntries] = useState<MPSKEntry[]>(() => {
        if (!editingProfile?.parameter) return [];

        try {
            let paramStr = editingProfile.parameter;
            if (typeof paramStr === 'string') {
                if (paramStr.startsWith('(') && paramStr.endsWith('}')) {
                    const cleaned = paramStr
                        .slice(1, -1)
                        .replace(/::/g, '":"')
                        .replace(/,/g, '","')
                        .replace(/:/g, '":"');
                    const jsonStr = `{"${cleaned}"}`;
                    const parsed = JSON.parse(jsonStr);

                    return Object.keys(parsed)
                        .filter(key => key.startsWith('entry_'))
                        .map(key => ({
                            mac: parsed[key].mac || '',
                            key: parsed[key].key || '',
                            vlan_id: parsed[key].vlan_id || undefined
                        }));
                }
                paramStr = paramStr.replace(/\\"/g, '"');
                if (paramStr.startsWith('"') && paramStr.endsWith('"')) {
                    paramStr = paramStr.slice(1, -1);
                }
                const parsed = JSON.parse(paramStr);
                return Object.keys(parsed)
                    .filter(key => key.startsWith('entry_'))
                    .map(key => ({
                        mac: parsed[key].mac || '',
                        key: parsed[key].key || '',
                        vlan_id: parsed[key].vlan_id || undefined
                    }));
            }
            return [];
        } catch (e) {
            console.error('Parameter parse error:', e);
            return [];
        }
    });

    const handleAddEntry = () => {
        setCurrentEntry({ mac: '', key: '' });
        setModalVisible(true);
    };

    const handleModalCancel = () => {
        setModalVisible(false);
    };

    const handleModalOk = () => {
        if (!currentEntry.mac) {
            message.error(t('Please enter MAC address'));
            return;
        }
        if (currentEntry.vlan_id === undefined || currentEntry.vlan_id === null || currentEntry.vlan_id < 0) {
            message.error(t('VLAN ID must be a non-negative number'));
            return;
        }

        const standardizedMac = currentEntry.mac
            .toLowerCase()
            .replace(/[^a-f0-9]/g, '')
            .match(/.{1,2}/g)
            ?.join(':') || currentEntry.mac;

        const newEntry = {
            ...currentEntry,
            mac: standardizedMac
        };

        setEntries(prev => {
            const existingIndex = prev.findIndex(e => e.mac === standardizedMac);
            if (existingIndex >= 0) {
                const updated = [...prev];
                updated[existingIndex] = newEntry;
                return updated;
            }
            return [...prev, newEntry];
        });

        setModalVisible(false);
    };
    const validateMAC = (value: string): Promise<void> => {
        const macRegex = /^([0-9A-Fa-f]{2}[:-]?){5}([0-9A-Fa-f]{2})$/;
        if (!value) {
            return Promise.reject('MAC address is required');
        }
        if (!macRegex.test(value)) {
            return Promise.reject('');
        }
        return Promise.resolve();
    };

    const handleDelete = (index: number) => {
        setDeletingIndex(index);
        setDeleteConfirmVisible(true);
    };
    const handleConfirmDelete = () => {
        if (deletingIndex !== null) {
            try {
                const newEntries = [...entries];
                newEntries.splice(deletingIndex, 1);
                setEntries(newEntries);
                message.success(t('Successfully Deleted'));
            } catch (error) {
                console.error('Delete error:', error);
                message.error(t('Delete failed'));
            }
        }
        setDeleteConfirmVisible(false);
    };

    const [pagination, setPagination] = useState({
        current: 1,
        pageSize: 10,
        total: entries.length
    });
    const handleTableChange = (pagination: any) => {
        setPagination({
            current: pagination.current,
            pageSize: pagination.pageSize,
            total: pagination.total
        });
    };
    useEffect(() => {
        if (editingProfile) {
            setInitialValues({
                name: editingProfile.name || '',
                description: editingProfile.description || ''
            });

            try {
                const rawParam = editingProfile.parameter
                    .replace(/\\"/g, '"')
                    .replace(/^"{|}"$/g, '');

                const parsed = typeof rawParam === 'string'
                    ? JSON.parse(rawParam)
                    : rawParam;

                const newEntries = Object.keys(parsed)
                    .filter(key => key.startsWith('entry_'))
                    .map(key => ({
                        mac: parsed[key].mac || '',
                        key: parsed[key].key || '',
                        vlan_id: parsed[key].vlan_id || undefined
                    }));

                setEntries(newEntries);
            } catch (e) {
                console.error('Parameter parse error:', e);
                setEntries([]);
            }
        }
    }, [editingProfile]);

    const columns = [
        {
            title: t('MAC'),
            dataIndex: 'mac',
            key: 'mac',
            align: 'center' as const,
        },
        {
            title: t('KEY'),
            dataIndex: 'key',
            key: 'key',
            align: 'center' as const,
            render: (text: string) => text,
        },
        {
            title: t('VLAN-ID'),
            dataIndex: 'vlan_id',
            key: 'vlan_id',
            align: 'center' as const,
        },
        {
            title: t('Operation'),
            key: 'actions',
            align: 'center' as const,
            render: (_: any, record: MPSKEntry, index: number) => (
                <Space>
                    <Button type="link" onClick={() => handleDelete(index)}>
                        {t('Delete')}
                    </Button>
                </Space>

            ),
        },
    ];

    return (
        <>
            <h2 style={{
                margin: '0 0 24px 0',
                color: 'rgba(0, 0, 0, 0.85)',
                fontWeight: 500,
                fontSize: 20
            }}>
            </h2>

            <Formik
                initialValues={initialValues}
                enableReinitialize={true}
                validate={(values) => {
                    const errors: any = {};
                    if (!values.name) {
                        errors.name = t('Please enter profile name');
                    }
                    return errors;
                }}
                onSubmit={async (values) => {
                    setIsSubmitting(true);
                    try {
                        const parameterObj = entries.reduce((acc, entry, index) => {
                            acc[`entry_${index}`] = {
                                mac: entry.mac,
                                key: entry.key,
                                vlan_id: entry.vlan_id
                            };
                            return acc;
                        }, {} as Record<string, any>);

                        const configVariables: ConfigVariable[] = [
                            {
                                type: "json",
                                weight: 0,
                                prefix: "interface.ssid.mpsk", 
                                value: JSON.stringify({
                                    entries: entries.map(entry => ({
                                        mac: entry.mac,
                                        key: entry.key,
                                        vlan_id: entry.vlan_id
                                    }))
                                })
                            }
                        ];

                        const payload = {
                            site_id: siteId,
                            type: 2,
                            name: values.name,
                            parameter: JSON.stringify(parameterObj),
                            description: values.description,
                            config_variables: JSON.stringify(configVariables)
                        };


                        if (editingProfile?.id) {
                            console.log(editingProfile?.id, payload)
                            await updateWirelessProfile({
                                id: editingProfile.id,
                                ...payload
                            });
                            message.success(t('Profile updated successfully'));
                        } else {
                            await createWirelessProfile(payload);
                            message.success(t('Profile created successfully'));
                        }
                        onClose(true);
                    } catch (error) {
                        console.error('Submission error:', error);
                        message.error(t('Operation failed: ') + (error as Error).message);
                    } finally {
                        setIsSubmitting(false);
                    }
                }}
            >
                {({
                    values,
                    errors,
                    touched,
                    handleSubmit,
                    handleBlur,
                    handleChange
                }) => (
                    <Form
                        onFinish={handleSubmit}
                        style={{
                            flex: 1,
                            display: 'flex',
                            flexDirection: 'column'
                        }}
                    >
                        <div style={{
                            marginBottom: 24,
                            width: '100%',
                            paddingLeft: 0
                        }}>
                            <div style={{
                                display: 'flex',
                                alignItems: 'center',
                                marginBottom: 16,
                                width: '100%'
                            }}>
                                <span style={{
                                    fontWeight: 500,
                                    minWidth: '100px',
                                    marginRight: 13
                                }}>
                                    {t('Name')} <span style={{ color: 'red' }}>*</span>
                                </span>
                                <div style={{ width: 380 }}>
                                    <Input
                                        name="name"
                                        value={values.name}
                                        onChange={handleChange}
                                        onBlur={handleBlur}
                                        placeholder={t('Enter profile name')}
                                        style={{ width: '100%' }}
                                        status={touched.name && errors.name ? 'error' : ''}
                                    />
                                    {touched.name && errors.name && (
                                        <div style={{ color: '#ff4d4f', marginTop: 4 }}>{errors.name}</div>
                                    )}
                                </div>

                            </div>
                            <div style={{
                                display: 'flex',
                                alignItems: 'flex-start',
                                width: '100%',
                                marginBottom: 16
                            }}>
                                <span style={{
                                    fontWeight: 500,
                                    minWidth: '100px',
                                    marginRight: 13,
                                    paddingTop: 8
                                }}>
                                    {t('Description')}
                                </span>
                                <Input.TextArea
                                    name="description"
                                    value={values.description}
                                    onChange={handleChange}
                                    placeholder={t('Enter description')}
                                    style={{
                                        width: 380
                                    }}
                                    rows={2}
                                />
                            </div>
                        </div>

                        <div style={{ marginBottom: 24 }}>
                            <Button
                                type="primary"
                                onClick={handleAddEntry}
                                style={{ marginBottom: 16 }}
                            >
                                {t('+ Create')}
                            </Button>
                        </div>

                        <div style={{ flex: 1, marginBottom: 24 }}>
                            <Table
                                columns={columns}
                                dataSource={entries}
                                rowKey={(_, index) => index.toString()}
                                pagination={{
                                    ...pagination,
                                    showSizeChanger: true,
                                    showQuickJumper: true,
                                    pageSizeOptions: ['10', '20', '50'],
                                    showTotal: (total) => `Total ${total} items`,
                                    position: ['bottomRight']
                                }}
                                onChange={handleTableChange}
                                scroll={{ y: 300 }}
                                bordered
                                locale={{
                                    emptyText: (
                                        <div style={{ padding: 40, color: 'rgba(0, 0, 0, 0.25)', textAlign: 'center' }}>
                                            {t('No Data')}
                                        </div>
                                    )
                                }}
                            />
                        </div>
                        <div style={{
                            display: 'flex',
                            justifyContent: 'flex-end',
                            gap: 16,
                            paddingTop: 16,
                            borderTop: '1px solid #f0f0f0'
                        }}>
                            <Button onClick={() => onClose(false)} disabled={isSubmitting}>
                                {t('Cancel')}
                            </Button>
                            <Button
                                type="primary"
                                htmlType="submit"
                                loading={isSubmitting}
                            >
                                {t('Apply')}
                            </Button>
                        </div>
                    </Form>
                )}
            </Formik>

            <Modal
                title={t('Create')}
                visible={modalVisible}
                onCancel={handleModalCancel}
                footer={[
                    <Button key="back" onClick={handleModalCancel}>
                        {t('Cancel')}
                    </Button>,
                    <Button key="submit" type="primary" onClick={handleModalOk}>
                        {t('Apply')}
                    </Button>,
                ]}
                destroyOnClose
                width={700}
                bodyStyle={{
                    height: '200px',
                    padding: '24px'
                }}
            >

                <Form layout="horizontal" labelCol={{ span: 6 }} wrapperCol={{ span: 18 }} initialValues={currentEntry} onValuesChange={(_, values) => setCurrentEntry(values)} validateTrigger={['onChange', 'onBlur']}>
                    <Form.Item
                        name="mac"
                        label={<span>{t('MAC')}</span>}
                        rules={[
                            {
                                required: true,
                            },
                            { validator: (_, value) => validateMAC(value) }
                        ]}
                        validateTrigger={['onChange', 'onBlur']}
                        labelAlign="left"
                    >
                        <Input
                            autoComplete="off"
                            style={{
                                width: 300
                            }}
                        />
                    </Form.Item>

                    <Form.Item
                        name="key"
                        label={
                            <span>
                                {t('Key')}
                            </span>
                        }
                        rules={[
                            {
                                required: true,
                                message: t('Required')
                            },
                            {
                                message: t('Required')
                            }
                        ]}
                        labelAlign="left"
                        validateTrigger="onBlur"
                    >
                        <Input.Password
                            //placeholder={t('Enter secure key')}
                            autoComplete="new-password"
                            style={{ width: 300 }}
                        />
                    </Form.Item>

                    <Form.Item
                        name="vlan_id"
                        label={<span>{t('VLAN-ID')}</span>}
                        rules={[
                            {
                                required: true,
                                message: t('VLAN-ID must be a positive number')
                            },
                            {
                                validator: (_, value) =>
                                    value === undefined || value === null || value >= 0
                                        ? Promise.resolve()
                                        : Promise.reject(t('VLAN ID must be ≥ 0'))
                            }
                        ]}
                        labelAlign="left"
                        validateTrigger={['onChange', 'onBlur']}
                    >
                        <InputNumber
                            min={1}
                            //placeholder="Enter poditive vlan_id"
                            style={{
                                width: 200
                            }}
                        />
                    </Form.Item>
                </Form>
            </Modal>
            <Modal
                title={
                    <span style={{ fontWeight: 500 }}>
                        <ExclamationCircleFilled style={{ color: '#faad14', marginRight: 8 }} />
                        {t('Note')}
                    </span>
                }
                visible={deleteConfirmVisible}
                onCancel={() => {
                    setDeleteConfirmVisible(false);
                }}
                footer={[
                    <Button
                        key="cancel"
                        onClick={() => {
                            setDeleteConfirmVisible(false);
                        }}
                    >
                        {t('No')}
                    </Button>,
                    <Button
                        key="confirm"
                        type="primary"
                        onClick={handleConfirmDelete}
                    >
                        {t('Yes')}
                    </Button>,
                ]}
                width={416}
            >
                <p style={{ margin: '16px 0' }}>{t('Are you sure you want to delete?')}</p>
            </Modal>
        </>
    );
};

export default FullPageMPSKForm;