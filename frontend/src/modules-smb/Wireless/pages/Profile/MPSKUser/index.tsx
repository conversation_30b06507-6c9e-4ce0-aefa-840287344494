import { Card, Button, Form, Modal, message, Space, Table, Select } from "antd";
import React, { useState, useEffect } from "react";
import { PlusOutlined } from '@ant-design/icons';
import {
    deleteWirelessProfile,
    getWirelessProfileList,
    getWirelessProfileDetail
} from "@/modules-smb/Wireless/apis/wireless_profile_api";
import { confirmModalAction } from "@/modules-ampcon/components/custom_modal";
import FullPageMPSKForm from '@/modules-smb/Wireless/pages/Profile/MPSKUser/MPSKUserForm';


interface MPSKProfile {
    id: number;
    name: string;
    description?: string;
    modified_time?: string;
    parameter: {
        mpsk?: boolean;
        mac?: string;
        key?: string;
        vlan_id?: number;
    };
}

const MPSKUser: React.FC = () => {
    const [form] = Form.useForm();
    const [data, setData] = useState<MPSKProfile[]>([]);
    const [isLoading, setIsLoading] = useState(false);
    const [pagination, setPagination] = useState({
        current: 1,
        pageSize: 10,
        total: 0
    });
    const [sorter] = useState<{ field?: string; order?: string }>({});
    const [editingProfile, setEditingProfile] = useState<MPSKProfile | null>(null);
    const [isModalVisible, setIsModalVisible] = useState(false);


    useEffect(() => {
        fetchList();
    }, [sorter]);
    const fetchList = async (page = 1, pageSize = 10) => {
        setIsLoading(true);
        try {
            const res = await getWirelessProfileList(2, 0, page, pageSize);
            if (res?.status !== 200) {
                message.error(res?.info);
                return;
            }
            setData((res.info || []).map(item => ({
                ...item,
                parameter: typeof item.parameter === 'string' ?
                    JSON.parse(item.parameter) :
                    item.parameter || {}
            })));
            setPagination({
                current: page,
                pageSize,
                total: res?.total || 0,
            });
        } catch (error) {
            message.error('Failed to fetch list');
        } finally {
            setIsLoading(false);
        }
    };


    const handleCreate = () => {
        setEditingProfile(null);
        setIsModalVisible(true);
    };
    const handleModalClose = (success?: boolean) => {
        setIsModalVisible(false);
        if (success) {
            setPagination({
                current: 1,
                pageSize: pagination.pageSize,
                total: pagination.total
            });
            fetchList(1, pagination.pageSize);
        }
    };

    const handleEdit = async (record: any) => {
        try {
            const res = await getWirelessProfileDetail(record.key);
            if (res?.status !== 200) {
                message.error(res?.info);
                return;
            }

            let parameter = {};
            if (typeof res.info.parameter === 'string') {
                parameter = JSON.parse(res.info.parameter.replace(/\\"/g, '"'));
            } else {
                parameter = res.info.parameter;
            }

            setEditingProfile({
                ...res.info,
                parameter
            });
            setEditingProfile(res.info);
            setIsModalVisible(true);
        } catch (error) {
            message.error('Failed to fetch detail');
        }
    };

    const handleDelete = (record: any) => {
        confirmModalAction(
            'This profile is currently in use by one or more configurations and cannot be deleted. Please update the relevant configurations to use a different profile before proceeding?',
            async () => {
                try {
                    const res = await deleteWirelessProfile({ id: record.key });
                    if (res?.status !== 200) {
                        message.error(res?.info);
                        return;
                    }
                    message.success('Successfully Deleted');
                    fetchList();
                } catch (error) {
                    message.error('Delete failed');
                }
            }
        );
    };

    const tableData = data.map((item) => ({
        key: item.id,
        name: item.name,
        modified: item.modified_time || '',
        description: item.description || '',
        originResource: item,
    }));

    const columns = [
        {
            title: 'Name',
            dataIndex: 'name',
            key: 'name',
            width: '20%',
            align: 'center' as const,
            sorter: (a, b) => a.name.localeCompare(b.name),
            render: (text: string) => <div style={{ textAlign: 'center' }}>{text}</div>
        },
        {
            title: 'Modify',
            dataIndex: 'modified',
            key: 'modified',
            width: '20%',
            align: 'center' as const,
            sorter: (a, b) => new Date(a.modified).getTime() - new Date(b.modified).getTime(),
            render: (text: string) => <div style={{ textAlign: 'center' }}>{text}</div>
        },
        {
            title: 'Description',
            dataIndex: 'description',
            key: 'description',
            width: '25%',
            align: 'center' as const,
            render: (text: string) => <div style={{ textAlign: 'center' }}>{text.trim()}</div>
        },
        {
            title: 'Operate',
            key: 'operate',
            width: '25%',
            fixed: 'right' as const,
            align: 'center' as const,
            render: (_: any, record: any) => (
                <Space size="middle">
                    <Button type="link" size="small" onClick={() => handleEdit(record)}>
                        Edit
                    </Button>
                    <Button type="link" size="small" onClick={() => handleDelete(record)}>
                        Delete
                    </Button>
                </Space>
            ),
        },
    ];

    return (
        <div style={{ flex: 1 }} className="tab-main">

            <div style={{ marginBottom: 24 }}>
                <Button
                    type="primary"
                    onClick={handleCreate}
                    icon={<PlusOutlined />}
                >
                    Create New MPSK Profile
                </Button>
            </div>
            <Card
                style={{
                    width: '100%',
                    overflow: 'hidden',
                    boxShadow: 'none',
                    border: 'none'
                }}
                bodyStyle={{ padding: 0 }}
            >
                <Table
                    columns={columns}
                    dataSource={tableData}
                    loading={isLoading}
                    sortDirections={['ascend', 'descend']}
                    bordered
                    onChange={(pagination, _, sorter) => {
                        setPagination({
                            current: pagination.current || 1,
                            pageSize: pagination.pageSize || 10,
                            total: pagination.total || 0,
                        });
                        fetchList(pagination.current, pagination.pageSize);
                    }}
                    pagination={{
                        current: pagination.current,
                        pageSize: pagination.pageSize,
                        total: pagination.total,
                        showTotal: (total) => `Total ${total} items`,
                        showSizeChanger: true,
                        showQuickJumper: true,
                        pageSizeOptions: ["10", "20", "50", "100"],
                    }}
                    scroll={{ x: 800 }}
                />
            </Card>

            <Modal
                title={editingProfile ? "Edit MPSK Profile" : "Create MPSK Profile"}
                visible={isModalVisible}
                onCancel={() => handleModalClose(false)}
                footer={null}
                width={800}
                destroyOnClose
            >
                <FullPageMPSKForm
                    editingProfile={editingProfile}
                    onClose={handleModalClose}
                    siteId={0}
                />
            </Modal>
        </div>
    );
};
export default MPSKUser;