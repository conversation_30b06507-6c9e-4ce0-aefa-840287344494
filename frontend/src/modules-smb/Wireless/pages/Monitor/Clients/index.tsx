import { But<PERSON>, Card, Tag, Input } from "antd";
import React, { useState, useMemo, useRef } from 'react';
import { SearchOutlined } from '@ant-design/icons';
import {
  AmpConCustomTable,
  createColumnConfigMultipleParams,
  TableFilterDropdown
} from "@/modules-ampcon/components/custom_table";
import EditClientModal from "@/modules-ampcon/pages/Monitor/WiredClients/edit_client_modal";
import styles from "@/modules-ampcon/pages/Topo/Topology/topo.module.scss";
import { useNavigate } from "react-router-dom";


const WirelessClients = () => {

  const columns = [
    createColumnConfigMultipleParams({
      title: "Status",
      dataIndex: "status",
      filterDropdownComponent: TableFilterDropdown,
      width: "15%",
      align: 'center',
      sorter: false,
      onFilter: (value, record) => record.status === value,
      render: status => (
        <Tag
          color={status === "online" ? "green" : "default"}
          style={{
            margin: 0,
            borderRadius: 4,
            fontWeight: 500,
            minWidth: 60,
            textAlign: "center",
            lineHeight: "22px",
            padding: "0 8px",
            ...(status !== "online" && {
              backgroundColor: "#f5f5f5",
              color: "rgba(0, 0, 0, 0.65)"
            })
          }}
        >
          {status === "online" ? "Online" : "Offline"}
        </Tag>
      )
    }),
    createColumnConfigMultipleParams({
      title: "Hostname",
      dataIndex: "host_name",
      filterDropdown: null,
      filterIcon: false,
      width: "15%",
      align: 'center',
      sorter: (a, b) => {
        const valA = a.host_name || a.mac;
        const valB = b.host_name || b.mac;
        return valA.localeCompare(valB);
      },
      render: (text, record) => (
        <span style={{ display: "inline-block", width: "100%", textAlign: "center" }}>
          {text || record.mac}
        </span>
      )
    }),
    createColumnConfigMultipleParams({
      title: "MAC Address",
      dataIndex: "mac",
      filterDropdown: null,
      filterIcon: false,
      width: "15%",
      align: 'center',
      sorter: (a, b) => a.mac.localeCompare(b.mac),
      render: mac => (
        <div style={{ textAlign: "center" }}>
          <a
            onClick={() => {
              //navigate(`/wireless/entities/Monitor`);
              navigate(`/wireless/entities/Monitor`, {
                state: { scrollToClientLifecycle: true,  targetMac: mac }
              });

            }}
            style={{
              fontFamily: "monospace",
              display: "inline-block",
              cursor: "pointer"
            }}
          >
            {mac}
          </a>
        </div>
      )
    }),
    createColumnConfigMultipleParams({
      title: "Vendor",
      dataIndex: "vendor",
      width: "15%",
      align: 'center',
      filterDropdown: null,
      filterIcon: false,
      sorter: (a, b) => {
        const vendorA = a.vendor || "";
        const vendorB = b.vendor || "";
        return vendorA.localeCompare(vendorB);
      },
      render: vendor => (
        <div
          style={{
            textAlign: "center",
            width: "100%"
          }}
        >
          {vendor || "-"}
        </div>
      )
    }),
    createColumnConfigMultipleParams({
      title: "Connect AP",
      dataIndex: "connect_ap",
      filterDropdown: null,
      filterIcon: false,
      width: "15%",
      align: 'center',
      sorter: (a, b) => a.connect_ap.localeCompare(b.connect_ap),
      render: ap => (
        <div style={{ textAlign: "center", width: "100%" }}>
          <a
            onClick={() => navigate(`/resource/upgrade_management/ap`)}
            className={styles["ap-link"]}
            style={{
              display: "inline-block",
              margin: "0 auto"
            }}
          >
            {ap}
          </a>
        </div>
      )
    }),
    createColumnConfigMultipleParams({
      title: "Site",
      dataIndex: "venue",
      filterDropdown: null,
      filterIcon: false,
      width: "15%",
      align: 'center',
      sorter: (a, b) => (a.venue || "").localeCompare(b.venue || ""),
      render: venue => (
        <div
          style={{
            textAlign: "center",
            width: "100%"
          }}
        >
          {venue ? (
            <a
              onClick={() => navigate(`/wireless/entities/Monitor`)}
              className={styles["venue-link"]}
              style={{
                display: "inline-block"
              }}
            >
              {venue}
            </a>
          ) : (
            <span style={{ display: "inline-block" }}>-</span>
          )}
        </div>
      )
    }),
    createColumnConfigMultipleParams({
      title: "SSID",
      dataIndex: "ssid",
      filterDropdown: null,
      filterIcon: false,
      width: "15%",
      align: 'center',
      sorter: (a, b) => (a.ssid || "").localeCompare(b.ssid || ""),
      render: band => <div style={{ textAlign: "center" }}>{band || "-"}</div>
    }),
    createColumnConfigMultipleParams({
      title: "RSSI",
      dataIndex: "rssi",
      filterDropdown: null,
      filterIcon: false,
      width: "15%",
      align: 'center',
      sorter: (a, b) => (a.rssi || 0) - (b.rssi || 0),
      render: rssi => {
        if (!rssi && rssi !== 0) {
          return <div style={{ textAlign: "center", width: "100%" }}>-</div>;
        }
        return (
          <div style={{ textAlign: "center", width: "100%" }}>
            <span className={styles["rssi-value"]}>{rssi} dBm</span>
          </div>
        );
      }
    }),
    createColumnConfigMultipleParams({
      title: "Band",
      dataIndex: "band",
      filterDropdown: null,
      filterIcon: false,
      width: "15%",
      align: 'center',
      sorter: (a, b) => {
        const bandOrder = { "2G": 0, "5G": 1, "6G": 2 };
        return (bandOrder[a.band] || 0) - (bandOrder[b.band] || 0);
      },
      render: band => <div style={{ textAlign: "center" }}>{band || "-"}</div>
    }),
    createColumnConfigMultipleParams({
      title: "Channel",
      dataIndex: "channel",
      filterDropdown: null,
      filterIcon: false,
      width: "10%",
      align: 'center',
      sorter: (a, b) => (a.channel || 0) - (b.channel || 0),
      render: band => <div style={{ textAlign: "center" }}>{band || "-"}</div>
    }),
    createColumnConfigMultipleParams({
      title: "Channel Width",
      dataIndex: "channel_width",
      filterDropdown: null,
      filterIcon: false,
      width: "15%",
      align: 'center',
      sorter: (a, b) => {
        const getNumericValue = record => {
          const value = record.status === "online" ? record.currentWidth : record.lastWidth;
          return typeof value === "string" ? parseInt(value.replace("MHz", "")) : Number(value) || 0;
        };
        return getNumericValue(a) - getNumericValue(b);
      }

    }),
    createColumnConfigMultipleParams({
      title: "IP",
      dataIndex: "ip",
      filterDropdown: null,
      filterIcon: false,
      width: "15%",
      align: 'center',
      sorter: (a, b) => a.ip.localeCompare(b.ip)
    }),
    createColumnConfigMultipleParams({
      title: "Authentication",
      dataIndex: "authentication",
      filterDropdown: null,
      filterIcon: false,
      width: "15%",
      align: 'center',
      sorter: (a, b) => a.authentication.localeCompare(b.authentication)
    }),
    createColumnConfigMultipleParams({
      title: "VLAN",
      dataIndex: "vlan",
      filterDropdown: null,
      filterIcon: false,
      width: "15%",
      align: 'center',
      sorter: (a, b) => a.vlan.localeCompare(b.vlan),
      render: (vlan: string) => vlan || '--'
    }),
    createColumnConfigMultipleParams({
      title: "Rx",
      dataIndex: "rx",
      filterDropdown: null,
      filterIcon: false,
      width: "15%",
      align: 'center',
      sorter: (a, b) => a.rx.localeCompare(b.rx)
    }),
    createColumnConfigMultipleParams({
      title: "Tx",
      dataIndex: "tx",
      filterDropdown: null,
      filterIcon: false,
      width: "15%",
      align: 'center',
      sorter: (a, b) => a.tx.localeCompare(b.tx)
    }),
    createColumnConfigMultipleParams({
      title: "Tx Packets",
      dataIndex: "tx_packets",
      filterDropdown: null,
      filterIcon: false,
      width: "15%",
      align: 'center',
      sorter: (a, b) => a.tx_packets - b.tx_packets
    }),
    createColumnConfigMultipleParams({
      title: "Rx Packets",
      dataIndex: "rx_packets",
      filterDropdown: null,
      filterIcon: false,
      width: "15%",
      align: 'center',
      sorter: (a, b) => a.rx_packets - b.rx_packets
    })
  ];

  const mockWirelessClients = [
    {
      id: 1,
      status: "online",
      host_name: "Client-01",
      mac: "00:1A:2B:3C:4D:5E",
      vendor: "Cisco",
      connect_ap: "AP-01",
      venue: "Conference Room",
      ssid: "Office-WiFi",
      rssi: -65,
      band: "5G",
      channel: 36,
      channel_width: "40MHz",
      ip: "*************",
      authentication: "WPA2-Enterprise",
      vlan: "WLAN-1",
      rx: "120 Mbps",
      tx: "80 Mbps",
      tx_packets: 1250,
      rx_packets: 980,
      state: "online"
    },
    {
      id: 2,
      status: "offline",
      host_name: "Client-02",
      mac: "00:2B:3C:4D:5E:6F",
      vendor: "Aruba",
      connect_ap: "AP-02",
      venue: "Lobby",
      ssid: "Guest-WiFi",
      rssi: -72,
      band: "2G",
      channel: 6,
      channel_width: "20MHz",
      ip: "*************",
      authentication: "Open",
      vlan: "WLAN-2",
      rx: "54 Mbps",
      tx: "24 Mbps",
      tx_packets: 800,
      rx_packets: 650,
      state: "offline"
    },
    {
      id: 3,
      status: "offline",
      host_name: "",
      mac: "00:2B:3C:4D:5E:6F",
      vendor: "",
      connect_ap: "AP-02",
      venue: "Lobby",
      ssid: "Guest-WiFi",
      rssi: -72,
      band: "2G",
      channel: 6,
      channel_width: "20MHz",
      ip: "*************",
      authentication: "Open",
      vlan: "WLAN-2",
      rx: "54 Mbps",
      tx: "24 Mbps",
      tx_packets: 800,
      rx_packets: 650,
      state: "offline"
    },
    {
      id: 4,
      status: "online",
      host_name: "Client-03",
      mac: "00:3C:4D:5E:6F:7A",
      vendor: "Huawei",
      connect_ap: "AP-03",
      venue: "Engineering",
      ssid: "Staff-WiFi",
      rssi: -58,
      band: "5G",
      channel: 149,
      channel_width: "80MHz",
      ip: "*************",
      authentication: "WPA3",
      vlan: "WLAN-3",
      rx: "240 Mbps",
      tx: "180 Mbps",
      tx_packets: 2100,
      rx_packets: 1950,
      state: "online"
    }
  ];

  const searchFieldsList = ["host_name", "connect_ap", "ssid", "vendor", "ip"];
  const matchFieldsList = [
    { name: "host_name", matchMode: "fuzzy" },
    { name: "connect_ap", matchMode: "fuzzy" },
    { name: "ssid", matchMode: "fuzzy" },
    { name: "vendor", matchMode: "fuzzy" },
    { name: "ip", matchMode: "fuzzy" },
  ];

  const editClientModalRef = useRef();
  const navigate = useNavigate();
  const [searchText, setSearchText] = useState('');
  const [activeTab, setActiveTab] = useState('all');
  const tableRef = useRef();


  const handleSearch = (value) => {
    setSearchText(value);
  };
  const clientCounts = useMemo(() => {
    return {
      all: mockWirelessClients.length,
      online: mockWirelessClients.filter(c => c.status === 'online').length,
      offline: mockWirelessClients.filter(c => c.status === 'offline').length
    };
  }, [mockWirelessClients]);
  const filteredClients = useMemo(() => {
    let results = mockWirelessClients;

    if (searchText) {
      const lowerCaseValue = searchText.toLowerCase();
      results = results.filter(item => {
        return searchFieldsList.some(field => {
          const fieldValue = item[field] || '';
          return fieldValue.toString().toLowerCase().includes(lowerCaseValue);
        });
      });
    }

    if (activeTab !== 'all') {
      results = results.filter(client =>
        activeTab === 'online' ? client.status === 'online' : client.status === 'offline'
      );
    }

    return results;
  }, [searchText, activeTab, mockWirelessClients]);


  return (
    <Card style={{ display: "flex", flex: 1 }}>
      <EditClientModal
        ref={editClientModalRef}
        saveCallback={() => {
          tableRef.current.refreshTable();
        }}
      />
      <h2 style={{ margin: "8px 0 20px" }}>Wireless Clients</h2>
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: 16
      }}>

        <div style={{ display: 'flex', borderBottom: '1px solid #d9d9d9' }}>
          {['all', 'online', 'offline'].map((key, index, array) => (
            <Button
              key={key}
              type="default"
              style={{
                width: '110px',
                height: '40px',
                fontSize: '16px',
                padding: '0 20px',
                borderRadius: 0,
                margin: 0,
                backgroundColor: activeTab === key ? '#e6f7ff' : '#fff',
                borderBottom: '1px solid',
                borderRight: index === array.length - 1 ? undefined : 'none'
              }}
              onClick={() => setActiveTab(key)}
            >
              {key === 'all' && `All (${clientCounts.all})`}
              {key === 'online' && `Online (${clientCounts.online})`}
              {key === 'offline' && `Offline (${clientCounts.offline})`}
            </Button>
          ))}
        </div>
        <Input
          placeholder="Search by STA, IP, AP, SSID or Vendor"
          allowClear
          enterButton={false}
          size="large"
          prefix={<SearchOutlined style={{ color: 'rgba(0, 0, 0, 0.25)' }} />}
          style={{
            width: 350,
            border: '1px solid #d9d9d9',
          }}
          value={searchText}
          onChange={(e) => handleSearch(e.target.value)}
        />
      </div>

      <AmpConCustomTable
        ref={tableRef}
        columns={columns}
        dataSource={filteredClients}
        matchFieldsList={matchFieldsList}
        onSearch={handleSearch}
        pagination={{
          showTotal: total => `Total ${total} items`,
          showSizeChanger: true,
          showQuickJumper: true,
          pageSizeOptions: ["10", "20", "50", "100"],
          defaultPageSize: 10,
          showLessItems: false
        }}
      />

    </Card>
  );
};
export default WirelessClients;