import React, { useMemo, useEffect } from "react";
import { Form, Row, Col, Switch, Select, InputNumber, Tooltip } from "antd";
import { useTranslation } from "react-i18next";
import _ from "lodash";
import { labelWithTooltip } from "./SetRadioForm"

const { Option } = Select;

type Props = {
  namePrefix: string;
  isDisabled?: boolean;
  values: any;
  setFieldValue: (path: string[], val: any) => void;
};

const itemLayout = { labelCol: { span: 8 }, wrapperCol: { span: 14 } };

const AdvancedSettings = ({ namePrefix, isDisabled, values, setFieldValue }: Props) => {
  const { t } = useTranslation();

  const band = _.get(values, [namePrefix, "band"]);
  const legacyRates = _.get(values, [namePrefix, "legacy-rates"]);

  const beaconRateAndMulticastOptions = useMemo(() => {
    const noneValue = "__none__";
    const commonOptions = [
      { value: noneValue, label: t("common.none") },
      { value: 1000, label: "1000" },
      { value: 2000, label: "2000" },
      { value: 5500, label: "5500" },
      { value: 6000, label: "6000" },
      { value: 9000, label: "9000" },
      { value: 11000, label: "11000" },
      { value: 12000, label: "12000" },
      { value: 18000, label: "18000" },
      { value: 24000, label: "24000" },
      { value: 36000, label: "36000" },
      { value: 48000, label: "48000" },
      { value: 54000, label: "54000" }
    ];
    const highBandOptions = [
      { value: noneValue, label: t("common.none") },
      { value: 6000, label: "6000" },
      { value: 9000, label: "9000" },
      { value: 12000, label: "12000" },
      { value: 18000, label: "18000" },
      { value: 24000, label: "24000" },
      { value: 36000, label: "36000" },
      { value: 48000, label: "48000" },
      { value: 54000, label: "54000" }
    ];

    return band === "2G" && legacyRates ? commonOptions : highBandOptions;
  }, [band, legacyRates, t]);

  // he和rates，空对象，设置为undifined
  useEffect(() => {
    const he = _.get(values, [namePrefix, "he"]);
    if (he && typeof he === "object" && Object.keys(he).length === 0) {
      setFieldValue([namePrefix, "he"], undefined);
    }

    const rates = _.get(values, [namePrefix, "rates"]);
    if (rates && typeof rates === "object" && Object.keys(rates).length === 0) {
      setFieldValue([namePrefix, "rates"], undefined);
    }
  }, [values, namePrefix, setFieldValue]);
  // 2g不显示 Allow-DFS选项，并设置undifined
  const allowDfs = _.get(values, [namePrefix, "allow-dfs"]);
  if (band === "2G" && allowDfs !== undefined) {
    setFieldValue([namePrefix, "allow-dfs"], undefined);
  }

  return (
    <>
      <h4 style={{ fontSize: 16, margin: "16px 0" }}>{t("configurations.advanced_settings")}</h4>
      <Row gutter={32}>
        {/* 2g不显示 Allow-DFS选项*/}
        {(band !== "2G") && (
          <Col span={8}>
            <Form.Item label={labelWithTooltip("Allow-DFS", "This property defines whether a radio may use DFS channels.")} {...itemLayout}>
              <Switch
                checked={_.get(values, [namePrefix, "allow-dfs"])}
                onChange={(val) => setFieldValue([namePrefix, "allow-dfs"], val)}
                disabled={isDisabled}
              />
            </Form.Item>
          </Col>
        )}
        <Col span={8}>
          <Form.Item label={labelWithTooltip("Beacon-Rate", "The beacon rate that shall be used by the BSS. Values are in Kbps.")} {...itemLayout}>
            <Select
              style={{ width: "80%" }}
              value={_.get(values, [namePrefix, "rates", "beacon"]) === undefined ? "__none__" : _.get(values, [namePrefix, "rates", "beacon"])}
              onChange={(val) => {
                setFieldValue([namePrefix, "rates", "beacon"], val === "__none__" ? undefined : val);
              }}
              allowClear
              placeholder={t("common.none")}
            >
              {beaconRateAndMulticastOptions.map((opt) => (
                <Select.Option key={opt.value} value={opt.value}>
                  {opt.label}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>
        </Col>

        <Col span={8}>
          <Form.Item label="Beacon-Interval" required {...itemLayout}>
            <InputNumber
              min={15}
              value={_.get(values, [namePrefix, "beacon-interval"])}
              onChange={(val) => setFieldValue([namePrefix, "beacon-interval"], val)}
              disabled={isDisabled}
              style={{ width: "40%" }}
            />
          </Form.Item>
        </Col>
      </Row>

      {/* 这一行放 Multicast、EMA 和 BSS-Color */}
      <Row gutter={32}>
        <Col span={8}>
          <Form.Item
            label={labelWithTooltip("Multicast", "The multicast rate that shall be used by the BSS. Values are in Kbps.")}
            {...itemLayout}
          >
            <Select
              style={{ width: "80%" }}
              allowClear
              value={_.get(values, [namePrefix, "rates", "multicast"]) === undefined ? "__none__" : _.get(values, [namePrefix, "rates", "multicast"])}
              onChange={(val) => {
                const cleanVal = val === "__none__" || val === null ? undefined : val;
                setFieldValue([namePrefix, "rates", "multicast"], cleanVal);
              }}
              placeholder="Select multicast rate"
            >
              {beaconRateAndMulticastOptions.map((opt) => (
                <Option key={opt.value} value={opt.value}>
                  {opt.label}
                </Option>
              ))}
            </Select>
          </Form.Item>
        </Col>

        <Col span={8}>
          <Form.Item
            label={labelWithTooltip(
              "EMA",
              "Enableing this option will make the PHY broadcast its multiple BSSID beacons using EMA."
            )}
            {...itemLayout}
          >
            <Switch
              disabled={isDisabled}
              checked={_.get(values, `${namePrefix}.he.ema`)}
              onChange={(val) =>
                setFieldValue([namePrefix, "he", "ema"], val ? true : undefined)
              }
            />
          </Form.Item>
        </Col>

        <Col span={8}>
          <Form.Item
            label={labelWithTooltip(
              "BSS-Color",
              "This enables BSS Coloring on the PHY. setting it to 0 disables the feature 1-63 sets the color and 64 will make hostapd pick a random color."
            )}
            {...itemLayout}
          >
            <InputNumber
              disabled={isDisabled}
              min={0}
              max={63}
              style={{ width: "40%" }}
              value={_.get(values, `${namePrefix}.he.bss-color`)}
              onChange={(val) => {
                const parsed = typeof val === 'number' && !isNaN(val) ? val : undefined;
                setFieldValue([namePrefix, "he", "bss-color"], parsed);
              }}
            />

          </Form.Item>
        </Col>
      </Row>
    </>
  );
};

export default AdvancedSettings;





