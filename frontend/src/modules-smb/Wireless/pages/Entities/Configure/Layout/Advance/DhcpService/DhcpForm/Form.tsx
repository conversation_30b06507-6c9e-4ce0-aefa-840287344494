import React, { useState, useMemo, useEffect } from 'react';
import { Form, Input, Select, Switch, Button, Row, Col, message } from 'antd';
import { useTranslation } from 'react-i18next';
import '@/modules-smb/Wireless/components/Tables/InventoryTable/CreateTagModal/CreateDeviceForm.scss';
import TextArea from 'antd/es/input/TextArea';
import { createWirelessProfile, updateWirelessProfile } from '@/modules-smb/Wireless/apis/wireless_profile_api';

interface Props {
  resource?: any; // 编辑模式的已有数据
  onClose?: () => void;
  refresh?: () => void;
  siteId?: number;
}

const NetworkForm: React.FC<Props> = ({ resource, onClose, refresh, siteId }) => {
  const { t } = useTranslation();
  const [form] = Form.useForm();
  const [vlanEnabled, setVlanEnabled] = useState(false);
  const [selectedRole, setSelectedRole] = useState('upstream');
  const [submitting, setSubmitting] = useState(false);
  const [isRoleDisabled, setIsRoleDisabled] = useState(false);
  // 角色映射（value <-> label）
  const roleLabelMap = {
    upstream: 'Bridged Mode (Layer 2 bridging)',
    downstream: 'Routing Mode (NAT)'
  };
  // 反向映射（label -> value，用于编辑时匹配角色值）
  const roleValueMap = Object.fromEntries(
    Object.entries(roleLabelMap).map(([k, v]) => [v, k])
  );

  // 缓存IPv4默认值
  const defaultIpv4Values = useMemo(() => ({
    subnet: '***********/24',
    gateway: '***********',
    'send-hostname': true,
    'lease-first': 1,
    'lease-count': 128,
    'lease-time': '6h',
    'relay-server': '',
    'circuit-id-format': '',
    'remote-id-format': '',
  }), []);

  // 编辑模式：解析resource数据并设置表单初始值
  useEffect(() => {
    if (resource) {
      const baseValues = {
        name: resource.name || '',
        description: resource.description || '',
      };
      let parameterObj;
      if (typeof resource.parameter === 'string') {
        try {
          parameterObj = JSON.parse(resource.parameter);
        } catch (e) {
          message.error('Failed to parse parameter string:', e);
          parameterObj = {};
        }
      } else {
        parameterObj = resource.parameter || {};
      }

      const roleLabel = parameterObj.role || '';
      const roleValue = roleValueMap[roleLabel] || 'upstream';
      setSelectedRole(roleValue);
      const isDefault = resource.name?.toLowerCase() === 'default';
      setIsRoleDisabled(isDefault);
      const vlanId = parameterObj.vlan;
      const vlanEnabled = typeof vlanId !== 'undefined' && vlanId !== '-';
      setVlanEnabled(vlanEnabled);

      let configVariablesObj = { interfaces: [] };
      if (resource.config_variables && typeof resource.config_variables === 'string') {
        try {
          configVariablesObj = JSON.parse(resource.config_variables);
        } catch (e) {
          message.error('Parsing failed:', e);
        }
      }
      const interfaceConfig = configVariablesObj.interfaces?.[0] || {};
      const ipv4Config = interfaceConfig.ipv4 || {};
      const dhcpConfig = ipv4Config.dhcp || {};
      
    
      const initialValues = {
        ...baseValues,
        role: roleLabelMap[roleValue], 
        vlan: vlanEnabled, 
        vlan_id: vlanId || 1080, 
        ipv6: !!interfaceConfig.ipv6, 
        'isolate-hosts': interfaceConfig['isolate-hosts'] || false, 
        services: interfaceConfig.services || ['SSH'], 

        // IPv4相关字段（仅downstream角色需要）
        subnet: ipv4Config.subnet || '',
        gateway: ipv4Config.gateway || '',
        'send-hostname': ipv4Config['send-hostname'] || false,
        'lease-first': dhcpConfig['lease-first'] || 1,
        'lease-count': dhcpConfig['lease-count'] || 128,
        'lease-time': dhcpConfig['lease-time'] || '6h',
        'relay-server': dhcpConfig['relay-server'] || '',
        'circuit-id-format': dhcpConfig['circuit-id-format'] || '',
        'remote-id-format': dhcpConfig['remote-id-format'] || '',
      };

      // 设置表单初始值
      form.setFieldsValue(initialValues);
    }
  }, [resource, form]); 

  const onFinish = async (values) => {
    setSubmitting(true);
    try {
      const { name, description, vlan, vlan_id, ipv6, services, ...restValues } = values;
      const parameterObj: any = {
        role: roleLabelMap[selectedRole],
        vlan: vlan_id || '-'
      };
      const parameter = JSON.stringify(parameterObj);
      const interfaceObj: any = {
        name: name,
        role: selectedRole,
        services: services,
        'isolate-hosts': restValues['isolate-hosts'],
      };

      if (selectedRole === 'downstream') {
        interfaceObj.ipv4 = {
          addressing: 'static', 
          dhcp: {
            'lease-count': restValues['lease-count'],
            'lease-first': restValues['lease-first'],
            'lease-time': restValues['lease-time'],
            'relay-server': restValues['relay-server'],
            'circuit-id-format': restValues['circuit-id-format'],
            'remote-id-format': restValues['remote-id-format']
          },
          gateway: restValues.gateway,
          'send-hostname': restValues['send-hostname'],
          subnet: restValues.subnet
        };
      }
      if (ipv6) {
        interfaceObj.ipv6 = {
          addressing: 'dynamic'
        };
      }

      if (vlanEnabled) {
        interfaceObj.vlan = {
          id: vlan_id
        };
      }

      const configVariablesObj = {
        interfaces: [interfaceObj]
      };
      const config_variables = JSON.stringify(configVariablesObj);

      const requestParams = {
        site_id: siteId,
        type: 1,
        name: name,
        parameter: parameter,
        description: description || '',
        config_variables: config_variables
      };

      let res;
      if (resource?.id) {
        res = await updateWirelessProfile({
          ...requestParams,
          id: resource.id
        });
      } else {
        res = await createWirelessProfile(requestParams);
      }

      if (res?.status !== 200) {
        message.error(res?.info || t('crud.error_create_obj', { obj: t('resources.configuration_resource') }));
        return;
      }
      message.success(
        resource?.id
          ? t('crud.success_update_obj', { obj: t('resources.configuration_resource') })
          : t('crud.success_create_obj', { obj: t('resources.configuration_resource') })
      );
      refresh && refresh(); 
      onClose && onClose(); 
    } catch (error) {
      message.error(t('crud.error_create_obj', { obj: t('resources.configuration_resource') }));
    } finally {
      setSubmitting(false);
    }
  };
  const handleVlanChange = (checked: boolean) => {
    setVlanEnabled(checked);
    if (!checked) {
      form.setFieldsValue({ vlan_id: undefined });
    } else {
      form.setFieldsValue({ vlan_id: 1080 });
    }
  };

  const handleRoleChange = (value) => {
    setSelectedRole(value);
    if (value !== 'downstream') {
      form.setFieldsValue({
        subnet: undefined,
        gateway: undefined,
        'send-hostname': undefined,
        'lease-first': undefined,
        'lease-count': undefined,
        'lease-time': undefined,
        'relay-server': undefined,
        'circuit-id-format': undefined,
        'remote-id-format': undefined,
      });
    } else {
      form.setFieldsValue(defaultIpv4Values);
    }
  };

  return (
    <Form
      form={form}
      name="networkForm"
      onFinish={onFinish}
      initialValues={{
        name: '',
        role: 'Bridged Mode (Layer 2 bridging)',
        services: ['SSH'],
        'isolate-hosts': false,
        description: '',
        ipv6: false,
        vlan: false,
        vlan_id: 1080,
        ...defaultIpv4Values,
      }}
      className="wirelessFormCreateDevice"
    >
      <h3 className='header1'></h3>
      <Row gutter={24}>
        <Col span={12}>
          <Form.Item
            name="name"
            label={t('common.name')}
            rules={[{ required: true, message: t('form.required') }]}
          >
            <Input />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item
            name="role"
            label="Role"
            rules={[{ required: true, message: t('form.required') }]}
          >
            <Select
              options={[
                { value: 'upstream', label: 'Bridged Mode (Layer 2 bridging)' },
                { value: 'downstream', label: 'Routing Mode (NAT)' },
              ]}
              disabled={isRoleDisabled}
              onChange={handleRoleChange}
            />
          </Form.Item>
        </Col>
      </Row>

      <Row gutter={24}>
        <Col span={12}>
          <Form.Item
            name="services"
            label="Service"
          >
            <Select
              mode="multiple"
              options={[
                { label: 'SSH', value: 'SSH' },
                { label: 'dhcp-snooping', value: 'dhcp-snooping' },
                { label: 'http', value: 'http' },
                { label: 'igmp', value: 'igmp' },
                { label: 'lldp', value: 'lldp' },
                { label: 'mdns', value: 'mdns' },
                { label: 'ntp', value: 'ntp' },
              ]}
            />
          </Form.Item>
        </Col>
      </Row>
      <Row gutter={24}>
        <Col span={12}>
          <Form.Item
            name="description"
            label="Description"
          >
            <TextArea
              rows={2}
            />
          </Form.Item>
        </Col>
      </Row>
      <Row gutter={24}>
        <Col span={12}>
          <Form.Item
            name="isolate-hosts"
            label="Isolate-hosts"
            valuePropName="checked"
          >
            <Switch />
          </Form.Item>
        </Col>

        {selectedRole === 'downstream' && (
          <>
            <Col span={24}>
              <h3 className='header2'>IPv4</h3>
            </Col>
            <Col span={12}>
              <Form.Item
                name="subnet"
                label="subnet"
                rules={[{ required: true, message: t('form.required') }]}
              >
                <Input />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="gateway"
                label="gateway"
                rules={[{ required: true, message: t('form.required') }]}
              >
                <Input />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="send-hostname"
                label="send-hostname"
                valuePropName="checked"
                rules={[{ required: true, message: t('form.required') }]}
              >
                <Switch />
              </Form.Item>
            </Col>

            <Col span={24}>
              <h3 className='header2'>DHCP V4</h3>
            </Col>
            <Col span={12}>
              <Form.Item
                name="lease-first"
                label="lease-first"
                rules={[{ required: true, message: t('form.required') }]}
              >
                <Input type="number" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="lease-count"
                label="lease-count"
                rules={[{ required: true, message: t('form.required') }]}
              >
                <Input type="number" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="lease-time"
                label="lease-time"
                rules={[{ required: true, message: t('form.required') }]}
              >
                <Input />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="relay-server"
                label="relay-server"
              >
                <Input />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="circuit-id-format"
                label="circuit-id-format"
                rules={[{ required: true, message: t('form.required') }]}
              >
                <Input />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="remote-id-format"
                label="remote-id-format"
                rules={[{ required: true, message: t('form.required') }]}
              >
                <Input />
              </Form.Item>
            </Col>
          </>
        )}

        <Col span={24}>
          <Form.Item
            name="ipv6"
            label="IPv6"
            valuePropName="checked"
          >
            <Switch />
          </Form.Item>
        </Col>
        <Col span={24}>
          <Form.Item
            name="vlan"
            label="VLAN"
            valuePropName="checked"
          >
            <Switch
              onChange={handleVlanChange}
            />
          </Form.Item>
        </Col>

        {vlanEnabled && (
          <Col span={24}>
            <Form.Item
              name="vlan_id"
              label="id"
            >
              <Input
                type="number"
              />
            </Form.Item>
          </Col>
        )}
      </Row>

      <div style={{
        display: 'flex',
        justifyContent: 'flex-end',
        gap: '12px',
        marginRight: '30px',
        paddingBottom: '8px',
      }}>
        <Button
          onClick={() => {
            form.resetFields();
            onClose && onClose();
          }}
          disabled={submitting}
        >
          Cancel
        </Button>
        <Button type="primary" htmlType="submit" disabled={submitting} loading={submitting}>
          Apply
        </Button>
      </div>
    </Form>
  );
};

export default NetworkForm;