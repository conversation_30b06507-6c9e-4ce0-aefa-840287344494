import React, { useState }  from 'react';
import { Form, Input, Select, Row, Col, Switch } from 'antd';
import { useTranslation } from 'react-i18next';
import { LabelTip } from '@/modules-smb/Wireless/components/LabelTip';
import {
  ENCRYPTION_OPTIONS,
  ENCRYPTION_PROTOS_REQUIRE_RADIUS,
  ENCRYPTION_PROTOS_CAN_RADIUS,
  ENCRYPTION_PROTOS_REQUIRE_KEY,
  ENCRYPTION_PROTOS_REQUIRE_IEEE,
  NO_MULTI_PROTOS,
} from '@/modules-smb/pages/ConfigurationPage/ConfigurationCard/ConfigurationSectionsCard/InterfaceSection/interfacesConstants';
import ProfileSelect from '@/modules-smb/Wireless/components/ProfileSelect';

interface AuthenticationFormProps {
  resource?: any;
  siteId?: number;
  networkRole?: string;
}

const AuthenticationForm: React.FC<AuthenticationFormProps> = ({ resource, siteId, networkRole }) => {
  const { t } = useTranslation();
  const [radiusEnabled, setRadiusEnabled] = useState(!!resource?.radius);
  const [multiPskEnabled, setMultiPskEnabled] = useState(!!resource?.['multi-psk']);
  const form = Form.useFormInstance();
  const proto = Form.useWatch(['encryption', 'proto'], form) || 'psk2';

  const isKeyNeeded = ENCRYPTION_PROTOS_REQUIRE_KEY.includes(proto);
  const needIeee = ENCRYPTION_PROTOS_REQUIRE_IEEE.includes(proto);
  const isUsingRadius = ENCRYPTION_PROTOS_REQUIRE_RADIUS.includes(proto);
  const canUseRadius = ENCRYPTION_PROTOS_CAN_RADIUS.includes(proto);
  const keyCachingDefault = proto === 'none' ? false : true;

  return (
    <>
      <Row gutter={24}>
        <Col span={12}>
          <Form.Item
            name={['encryption', 'proto']}
            label="proto"
            tooltip={LabelTip('interface.ssid.encryption.proto')}
            rules={[
              { required: true, message: t('form.required') }
            ]}
          >
            <Select placeholder="proto">
              {ENCRYPTION_OPTIONS.map((opt: any) => (
                <Select.Option key={opt.value} value={opt.value}>{opt.label}</Select.Option>
              ))}
            </Select>
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item
            name={['encryption', 'key-caching']}
            label="key-caching"
            tooltip={LabelTip('interface.ssid.encryption.key-caching')}
            valuePropName="checked"
          >
            <Switch defaultChecked={keyCachingDefault}/>
          </Form.Item>
        </Col>
      </Row>
      <Row gutter={24}>
        <Col span={12}>
          {/* 根据 needIeee 判断是否显示 ieee80211w 字段 */}
          {needIeee && (
            <Form.Item
              name={['encryption', 'ieee80211w']}
              label="ieee80211w"
              tooltip={LabelTip('interface.ssid.encryption.ieee80211w')}
            >
              <Select placeholder="ieee80211w">
                <Select.Option value="disabled">Disabled</Select.Option>
                <Select.Option value="optional">Optional</Select.Option>
                <Select.Option value="required">Required</Select.Option>
              </Select>
            </Form.Item>
          )}
        </Col>
        <Col span={12}>
          {/* 根据 isKeyNeeded 判断是否显示 key 字段 */}
          {isKeyNeeded && (
            <Form.Item
              name={['encryption', 'key']}
              label="key"
              tooltip={LabelTip('interface.ssid.encryption.key')}
              rules={[
                { required: true, message: t('form.required') },
                { min: 8, max: 63, message: t('form.min_max_string', { min: 8, max: 63 }) },
              ]}
            >
              <Input.Password placeholder="key" />
            </Form.Item>
          )}
        </Col>
      </Row>
      {/* Radius 配置部分 */}
      {(isUsingRadius || canUseRadius) && (
        <Row gutter={24}>
          <Col span={24}>
            <ProfileSelect
              label="Radius"
              formName={['radius']}
              switchEnabled={radiusEnabled}
              onSwitchChange={setRadiusEnabled}
              type={1}
              siteId={siteId}
              edit={resource?.radius}
            />
          </Col>
        </Row>
      )}
      {/* multi-psk 模块 */}
      {(!NO_MULTI_PROTOS.includes(proto) && proto !== 'none' && networkRole !== 'Routing Mode (NAT)') && (
        <Row gutter={24}>
          <Col span={24}>
            <ProfileSelect
              label="Multi Psk"
              formName={['multi-psk']}
              switchEnabled={multiPskEnabled}
              onSwitchChange={setMultiPskEnabled}
              type={2}
              siteId={siteId}
              edit={resource?.['multi-psk']}
            />
          </Col>
        </Row>
      )}
    </>
  );
};

export default AuthenticationForm; 