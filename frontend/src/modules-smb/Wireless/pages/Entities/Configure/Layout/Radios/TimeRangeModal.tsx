import React, { useState, useEffect } from "react";
import { Mo<PERSON>, Form, Divider, Button, message } from "antd";
import dayjs from "dayjs";
import { TimeRangeFormFields } from "@/modules-smb/Wireless/pages/Profile/TimeRange/CreateTimeRanger";
import {
  createWirelessProfile,
} from '@/modules-smb/Wireless/apis/wireless_profile_api';

const dayList = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];

interface TimeRangeModalProps {
  visible: boolean;
  onClose: () => void;
  siteId?: number | null;
}

const TimeRangeModal: React.FC<TimeRangeModalProps> = ({ visible, onClose, siteId }) => {
  const [modalForm] = Form.useForm();
  const [timeRanges, setTimeRanges] = useState(
    dayList.map(day => ({
      day,
      checked: false,
      startTime: null as dayjs.Dayjs | null,
      endTime: null as dayjs.Dayjs | null,
    }))
  );


  const onCheckChange = (index: number, checked: boolean) => {
    const newRanges = [...timeRanges];
    newRanges[index].checked = checked;
    if (checked) {
      newRanges[index].startTime = newRanges[index].startTime || dayjs('08:00', 'HH:mm');
      newRanges[index].endTime = newRanges[index].endTime || dayjs('18:00', 'HH:mm');
    } else {
      newRanges[index].startTime = null;
      newRanges[index].endTime = null;
    }
    setTimeRanges(newRanges);
  };

  const onStartTimeChange = (index: number, time: dayjs.Dayjs | null) => {
    const newRanges = [...timeRanges];
    newRanges[index].startTime = time;
    if (time && newRanges[index].endTime && time.isAfter(newRanges[index].endTime)) {
      newRanges[index].endTime = time;
    }
    setTimeRanges(newRanges);
  };

  const onEndTimeChange = (index: number, time: dayjs.Dayjs | null) => {
    const newRanges = [...timeRanges];
    newRanges[index].endTime = time;
    if (time && newRanges[index].startTime && time.isBefore(newRanges[index].startTime)) {
      newRanges[index].startTime = time;
    }
    setTimeRanges(newRanges);
  };

  const handleOk = () => {
    modalForm.validateFields()
      .then(async (formData: { name: string; description?: string }) => {
        try {
          const parameter = JSON.stringify({
            time_range: timeRanges
              .filter(item => item.checked)
              .map(
                item =>
                  `${item.day} ${item.startTime!.format('HH:mm')}-${item.endTime!.format('HH:mm')}`
              ),
          });
          const commonParams = {
            site_id: siteId,
            type: 4,
            name: formData.name,
            description: formData.description || '',
            parameter,
            config_variables: JSON.stringify({}),
          };

          const res = await createWirelessProfile(commonParams);

          if (res.status === 200) {
            message.success('Created successfully');
            handleClose();
          } else {
            message.error(`Create failed: ${res.info || 'Unknown error'}`);
            handleClose();
          }
        } catch {
          message.error(`Create failed: Unknown error`);
          handleClose();
        }
      })
      .catch(() => {
        // 校验失败不处理
      });
  };

  const handleClose = () => {
    modalForm.resetFields();
    setTimeRanges(
      dayList.map(day => ({
        day,
        checked: false,
        startTime: null,
        endTime: null,
      }))
    );
    onClose();
  };


  return (
    <Modal
      title="Create time range profile"
      open={visible}
      onOk={handleOk}
      onCancel={handleClose}
      width={900}
      footer={
        <>
          <Divider style={{ marginTop: 16, marginBottom: 16 }} />
          <div style={{ display: 'flex', justifyContent: 'flex-end', gap: 8 }}>
            <Button onClick={handleClose}>Cancel</Button>
            <Button type="primary" onClick={handleOk}>Apply</Button>
          </div>
        </>
      }
    >
      <Divider style={{ marginTop: 8, marginBottom: 16 }} />
      <Form form={modalForm} layout="horizontal" labelAlign="left" labelCol={{ span: 4 }} wrapperCol={{ span: 19 }}>
        <TimeRangeFormFields
          form={modalForm}
          timeRanges={timeRanges}
          onCheckChange={onCheckChange}
          onStartTimeChange={onStartTimeChange}
          onEndTimeChange={onEndTimeChange}         
        />
      </Form>
    </Modal>
  );
};

export default TimeRangeModal;
