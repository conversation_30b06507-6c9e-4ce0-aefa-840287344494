import React from 'react';
import { Row, Col } from 'antd';
import ToggleField from '@/modules-smb/Wireless/components/FormFields/ToggleField';

const Igmp = () => (
  <Row gutter={[20, 20]} style={{ marginBottom: 0, marginTop: 8, width: '100%' }}>
      <Col>
        <ToggleField
          name="configuration.services.igmp.enable"
          label="IGMP"
          // isDisabled={true}
          // isRequired
        />
      </Col>
    </Row>
);

export default React.memo(Igmp);
