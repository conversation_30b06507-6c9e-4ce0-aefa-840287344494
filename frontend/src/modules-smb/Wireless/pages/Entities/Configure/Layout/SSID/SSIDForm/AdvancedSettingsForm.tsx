import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Form, Input, InputNumber, Select, Switch, Row, Col, Checkbox } from 'antd';
import CaptivePortalForm from './CaptivePortalForm/Form';
import { LabelTip } from '@/modules-smb/Wireless/components/LabelTip';

interface Props {
  resource?: any;
  siteId?: number;
}

const AdvancedSettingsForm: React.FC<Props> = ({ resource, siteId }) => {
  const { t } = useTranslation();
  const initial = resource || {};
  const [rateLimitEnabled, setRateLimitEnabled] = useState(!!initial['rate-limit']);
  const [rrmEnabled, setRrmEnabled] = useState(!!initial.rrm);
  const [aclEnabled, setAclEnabled] = useState(!!initial['access-control-list']);
  const [roamingState, setRoamingState] = useState(
    initial.roaming && initial.roaming['message-exchange'] ? 'custom' : (initial.roaming ? 'on' : 'off')
  );

  // wifi-steering联动
  const handleWifiSteeringChange = (checked: boolean, form: any) => {
    const services = form.getFieldValue('services') || [];
    if (checked) {
      if (!services.includes('wifi-steering')) {
        form.setFieldValue('services', [...services, 'wifi-steering']);
      }
    } else {
      form.setFieldValue('services', services.filter((s: string) => s !== 'wifi-steering'));
    }
  };
  const renderWifiSteeringSwitch = (form: any) => {
    const services = form.getFieldValue('services') || [];
    return (
      <Switch
        checked={services.includes('wifi-steering')}
        onChange={checked => handleWifiSteeringChange(checked, form)}
      />
    );
  };

  return (
    <Form.Item noStyle shouldUpdate>
      {form => {
        // authMode变化时联动services
        const handleAuthModeChange = (authMode: string) => {
          const services = form.getFieldValue('services') || [];
          if (authMode !== 'none') {
            if (!services.includes('captive')) {
              form.setFieldValue('services', [...services, 'captive']);
            }
          } else {
            if (services.includes('captive')) {
              form.setFieldValue('services', services.filter((s: string) => s !== 'captive'));
            }
          }
        };
        return (
          <>
            <Row gutter={24}>
              <Col span={12}>
                <Form.Item
                  name={['hidden-ssid']}
                  label="hidden-ssid"
                  tooltip={LabelTip('interface.ssid.hidden-ssid')}
                  valuePropName="checked"
                >
                  <Switch />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="wifi-steering" valuePropName="checked">
                  {renderWifiSteeringSwitch(form)}
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={24}>
              <Col span={12}>
                <Form.Item
                  name={['services']}
                  label="services"
                  style={{ display: 'none' }}
                >
                  <Select mode="multiple" options={[]} />
                </Form.Item>
              </Col>
              <Col span={12}></Col>
            </Row>
            <Row gutter={24}>
              <Col span={12}>
                <Form.Item
                  name={['maximum-clients']}
                  label="maximum-clients"
                  tooltip={LabelTip('interface.ssid.maximum-clients')}
                  rules={[
                    { required: true, message: t('form.required') },
                    { type: 'number', min: 1, max: 65535, message: 'maximum-clients must be 1 ~ 65535' }
                  ]}
                >
                  <InputNumber />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name={['fils-discovery-interval']}
                  label="fils-discovery-interval"
                  tooltip={LabelTip('interface.ssid.fils-discovery-interval')}
                  rules={[
                    { type: 'number', min: 0, max: 21, message: 'fils-discovery-interval must be 0 ~ 21' }
                  ]}
                >
                  <InputNumber />
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={24}>
              <Col span={12}>
                <Form.Item
                  name={['dtim-period']}
                  label="dtim-period"
                  rules={[
                    { type: 'number', min: 0, max: 256, message: 'dtim-period must be 0 ~ 256' }
                  ]}
                >
                  <InputNumber/>
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name={['isolate-clients']}
                  label="isolate-clients"
                  tooltip={LabelTip('interface.ssid.isolate-clients')}
                  valuePropName="checked"
                >
                  <Switch />
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={24}>
              <Col span={12}>
                <Form.Item
                  name={['power-save']}
                  label="power-save"
                  tooltip={LabelTip('interface.ssid.power-save')}
                  valuePropName="checked"
                >
                  <Switch />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name={['broadcast-time']}
                  label="broadcast-time"
                  tooltip={LabelTip('interface.ssid.broadcast-time')}
                  valuePropName="checked"
                >
                  <Switch />
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={24}>
              <Col span={12}>
                <Form.Item
                  name={['unicast-conversion']}
                  label="unicast-conversion"
                  tooltip={LabelTip('interface.ssid.unicast-conversion')}
                  valuePropName="checked"
                >
                  <Switch />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name={['proxy-arp']}
                  label="proxy-arp"
                  tooltip={LabelTip('interface.ssid.proxy-arp')}
                  valuePropName="checked"
                >
                  <Switch />
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={24}>
              <Col span={12}>
                <Form.Item
                  name={['disassoc-low-ack']}
                  label="disassoc-low-ack"
                  valuePropName="checked"
                >
                  <Switch />
                </Form.Item>
              </Col>
            </Row>

            <h3 className='header2'></h3>
            <Row gutter={24}>
              <Col span={12}>
                <Form.Item label="rate-limit">
                  <Switch
                    checked={rateLimitEnabled}
                    onChange={setRateLimitEnabled}
                  />
                </Form.Item>
              </Col>
              <Col span={12}></Col>
            </Row>
            {rateLimitEnabled && (
              <Row gutter={24}>
                <Col span={12}>
                  <Form.Item
                    name={['rate-limit', 'ingress-rate']}
                    label="ingress-rate"
                    tooltip={LabelTip('interface.ssid.rate-limit.ingress-rate')}
                    rules={[
                      { required: true, message: t('form.required') },
                      { type: 'number', min: 0, max: 65535, message: 'ingress-rate must be less than 65535' },
                    ]}
                  >
                    <InputNumber min={0} />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name={['rate-limit', 'egress-rate']}
                    label="egress-rate"
                    tooltip={LabelTip('interface.ssid.rate-limit.egress-rate')}
                    rules={[
                      { required: true, message: t('form.required') },
                      { type: 'number', min: 0, max: 65535, message: 'egress-rate must be less than 65535' },
                    ]}
                  >
                    <InputNumber min={0} />
                  </Form.Item>
                </Col>
              </Row>
            )}
            <Row gutter={24}>
              <Col span={12}>
                <Form.Item label="rrm">
                  <Switch
                    checked={rrmEnabled}
                    onChange={setRrmEnabled}
                  />
                </Form.Item>
              </Col>
              <Col span={12}></Col>
            </Row>
            {rrmEnabled && (
              <>
                <Row gutter={24}>
                  <Col span={12}>
                    <Form.Item name={['rrm', 'neighbor-reporting']} label="neighbor-reporting" tooltip={LabelTip('interface.ssid.rrm.neighbor-reporting')} valuePropName="checked">
                      <Switch />
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item name={['rrm', 'lci']} label="lci" tooltip={LabelTip('interface.ssid.rrm.lci')}>
                      <Input placeholder="lci" />
                    </Form.Item>
                  </Col>
                </Row>
                <Row gutter={24}>
                  <Col span={12}>
                    <Form.Item name={['rrm', 'civic-location']} label="civic-location" tooltip={LabelTip('interface.ssid.rrm.civic-location')}>
                      <Input placeholder="civic-location" />
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item name={['rrm', 'ftm-responder']} label="ftm-responder" tooltip={LabelTip('interface.ssid.rrm.ftm-responder')} valuePropName="checked">
                      <Switch />
                    </Form.Item>
                  </Col>
                </Row>
                <Row gutter={24}>
                  <Col span={12}>
                    <Form.Item name={['rrm', 'stationary-ap']} label="stationary-ap" tooltip={LabelTip('interface.ssid.rrm.stationary-ap')} valuePropName="checked">
                      <Switch />
                    </Form.Item>
                  </Col>
                  <Col span={12}></Col>
                </Row>
              </>
            )}
            <Row gutter={24}>
              <Col span={12}>
                <Form.Item label="access-control-list">
                  <Switch
                    checked={aclEnabled}
                    onChange={setAclEnabled}
                  />
                </Form.Item>
              </Col>
              <Col span={12}></Col>
            </Row>
            {aclEnabled && (
              <Row gutter={24}>
                <Col span={12}>
                  <Form.Item
                    name={['access-control-list', 'mode']}
                    label="mode"
                    rules={[
                      { required: true, message: t('form.required') }
                    ]}
                  >
                    <Select placeholder="mode">
                      <Select.Option value="allow">allow</Select.Option>
                      <Select.Option value="deny">deny</Select.Option>
                    </Select>
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name={['access-control-list', 'mac-address']}
                    label="mac-address"
                    required
                    validateTrigger={['onChange', 'onBlur']}
                    rules={[
                      {
                        validator: (_, value) => {
                          if (!value || value.length === 0) {
                            return Promise.reject('required');
                          }
                          const macPattern = /^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$/;
                          const invalid = value.some((mac: string) => !macPattern.test(mac));
                          return invalid ? Promise.reject(t('form.invalid_mac_uc')) : Promise.resolve();
                        }
                      }
                    ]}
                  >
                    <Select
                      mode="tags"
                      tokenSeparators={[',']}
                    />
                  </Form.Item>
                </Col>
              </Row>
            )}
            <Row gutter={24}>
              <Col span={12}>
                <Form.Item label="roaming">
                  <Select
                    value={roamingState}
                    onChange={setRoamingState}
                  >
                    <Select.Option value="on">Auto</Select.Option>
                    <Select.Option value="custom">Custom</Select.Option>
                    <Select.Option value="off">Off</Select.Option>
                  </Select>
                </Form.Item>
              </Col>
              <Col span={12}></Col>
            </Row>
            {roamingState === 'custom' && (
              <>
                <Row gutter={24}>
                  <Col span={12}>
                    <Form.Item
                      name={['roaming', 'message-exchange']}
                      label="message-exchange"
                      tooltip={LabelTip('interface.ssid.roaming.message-exchange')}
                      rules={[
                        { required: true, message: t('form.required') }
                      ]}
                    >
                      <Select placeholder="message-exchange">
                        <Select.Option value="air">air</Select.Option>
                        <Select.Option value="ds">ds</Select.Option>
                      </Select>
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item
                      name={['roaming', 'generate-psk']}
                      label="generate-psk"
                      tooltip={LabelTip('interface.ssid.roaming.generate-psk')}
                      valuePropName="checked"
                    >
                      <Switch />
                    </Form.Item>
                  </Col>
                </Row>
                <Row gutter={24}>
                  <Col span={12}>
                    <Form.Item name={['roaming', 'domain-identifier']} label="domain-identifier" tooltip={LabelTip('interface.ssid.roaming.domain-identifier')}>
                      <Input placeholder="domain-identifier" />
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item name={['roaming', 'pmk-r0-key-holder']} label="pmk-r0-key-holder" tooltip={LabelTip('interface.ssid.roaming.pmk-r0-key-holder')}>
                      <Input placeholder="pmk-r0-key-holder" />
                    </Form.Item>
                  </Col>
                </Row>
                <Row gutter={24}>
                  <Col span={12}>
                    <Form.Item name={['roaming', 'pmk-r1-key-holder']} label="pmk-r1-key-holder" tooltip={LabelTip('interface.ssid.roaming.pmk-r1-key-holder')}>
                      <Input placeholder="pmk-r1-key-holder" />
                    </Form.Item>
                  </Col>
                  <Col span={12}></Col>
                </Row>
              </>
            )}

            <CaptivePortalForm resource={resource} siteId={siteId} onAuthModeChange={handleAuthModeChange} />
          </>
        );
      }}
    </Form.Item>
  );
};

export default AdvancedSettingsForm;
