import React,{useEffect} from 'react';
import NumberField from '@/modules-smb/Wireless/components/FormFields/NumberField';
import SelectField from '@/modules-smb/Wireless/components/FormFields/SelectField';
import ToggleField from '@/modules-smb/Wireless/components/FormFields/ToggleField';
import {Row,Col} from 'antd';
import useFastField from '@/modules-smb/hooks/useFastField';
import { useTranslation } from 'react-i18next';
import { useFormikContext } from 'formik';
import {getSubSectionDefaults} from './servicesConstants';

const WifiSteering = () => {
  const { value: wifiSteeringEnabled } = useFastField({ name: 'configuration.services.wifi-steering.enabled' });

  const {t}=useTranslation();
          
  const { values, setFieldValue, errors } = useFormikContext<any>();
  useEffect(() => {
     if (wifiSteeringEnabled) {
      const defaultWifiSteeringConfig = getSubSectionDefaults(t,'wifi-steering');
       setFieldValue('configuration.services.wifi-steering', {
     ...defaultWifiSteeringConfig,
     enabled: true,
     });
     }else {
   // 可选：关闭时清除 wifi-steering 字段
   setFieldValue('configuration.services.wifi-steering', undefined);
     }
   }, [wifiSteeringEnabled]);
  return(
    <Row gutter={[20, 20]} style={{ marginBottom: 0, marginTop: 8, width: '100%' }}>
    <Col>
      <ToggleField
        name="configuration.services.wifi-steering.enabled"
        label="Wifi Steering"
        // definitionKey="service.mdns.enable"
        // isDisabled={!editing}
        // isRequired
      />
      {wifiSteeringEnabled && (
        <>
        <SelectField
        name="configuration.services.wifi-steering.mode"
        label="Mode"
        definitionKey="service.wifi-steering.mode"
        // isDisabled={!editing}
        isRequired
        w={280}
        options={[
          { value: 'local', label: 'local' },
          {
            value: 'cloud',
            label: 'cloud',
          },
        ]}
      />
      <ToggleField
        name="configuration.services.wifi-steering.assoc-steering"
        label="Assoc-Steering"
        definitionKey="service.wifi-steering.assoc-steering"
        // isDisabled={!editing}
        isRequired
      />
      <ToggleField
        name="configuration.services.wifi-steering.auto-channel"
        label="Auto-Channel"
        definitionKey="service.wifi-steering.auto-channel"
        // isDisabled={!editing}
        isRequired
      />
      <NumberField
        name="configuration.services.wifi-steering.required-probe-snr"
        label="Required-Probe-Snr"
        definitionKey="service.wifi-steering.required-probe-snr"
        // isDisabled={!editing}
        isRequired
        w={140}
      />
      <NumberField
        name="configuration.services.wifi-steering.required-roam-snr"
        label="Required-Roam-Snr"
        definitionKey="service.wifi-steering.required-roam-snr"
        // isDisabled={!editing}
        isRequired
        w={140}
      />
        </>
      )}
      
    </Col>
  </Row>
  );
  
};

export default React.memo(WifiSteering);
