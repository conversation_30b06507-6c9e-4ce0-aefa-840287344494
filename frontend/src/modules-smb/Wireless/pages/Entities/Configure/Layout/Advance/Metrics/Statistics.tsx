import React,{useEffect} from 'react';
import { useTranslation } from 'react-i18next';
import {Col,Row} from 'antd';
import MultiSelectField from '@/modules-smb/Wireless/components/FormFields/MultiSelectField';
import NumberField from '@/modules-smb/Wireless/components/FormFields/NumberField';
import ToggleField from '@/modules-smb/Wireless/components/FormFields/ToggleField';
import useFastField from '@/modules-smb/hooks/useFastField';
import {useFormikContext} from 'formik';
import { getSubSectionDefaults } from './metricsConstants';

const Statistics = () => {
  const { t } = useTranslation();
  const { value: statisticsEnabled } = useFastField({ name: 'configuration.metrics.statistics.enabled' });
  const { values, setFieldValue, errors } = useFormikContext<any>();
  useEffect(() => {
     if (statisticsEnabled) {
      const defaultWifiSteeringConfig = getSubSectionDefaults(t,'statistics');
       setFieldValue('configuration.metrics.statistics', {
     ...defaultWifiSteeringConfig,
     enabled: true,
     });
     }else {
   // 可选：关闭时清除 statistics 字段
   setFieldValue('configuration.metrics.statistics', undefined);
     }
   }, [statisticsEnabled]);
  return (
    <Row gutter={[20, 20]} style={{ marginBottom: 0, marginTop: 8, width: '100%' }}>
        <Col>
          <ToggleField
            name="configuration.metrics.statistics.enabled"
            label="Statistics"
          />
          {statisticsEnabled&&(
            <>
            <NumberField
            name="configuration.metrics.statistics.interval"
            label="Interval"
            definitionKey="metrics.statistics.interval"
            // isDisabled={!editing}
            isRequired
            w={140}
          />
          <MultiSelectField
            name="configuration.metrics.statistics.types"
            label="Types"
            definitionKey="metrics.statistics.types"
            w={280}
            options={[
              { value: 'ssids', label: 'ssids' },
              { value: 'lldp', label: 'lldp' },
              { value: 'clients', label: 'clients' },
            ]}
            isRequired
            // isDisabled={!editing}
          />
            </>
          )}
        </Col>
      </Row>
  );
};

export default React.memo(Statistics);
