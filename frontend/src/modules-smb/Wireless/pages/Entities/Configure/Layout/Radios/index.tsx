import React, { useState, useRef, useEffect } from "react";
import { Button, Form, message, Row, Col, Select, Switch, Radio, Modal, Card, Divider, Input } from "antd";
import SetRadioForm, { SetRadioFormRef } from "./RadioForm/SetRadioForm";
import COUNTRY_LIST from "@/modules-smb/constants/countryList";
import { useNavigate } from "react-router-dom";
import { SINGLE_RADIO_SCHEMA } from "./radiosConstants";
import * as yup from "yup";
import { useTranslation } from "react-i18next";
import TimeRangeModal from "./TimeRangeModal";
import { getWirelessProfileList } from "@/modules-smb/Wireless/apis/wireless_profile_api";
import { setProfileVariable, getProfileVariable } from "@/modules-smb/Wireless/utils/util";
import { PlusOutlined } from '@ant-design/icons';

const { Option } = Select;

type BandType = "2G" | "5G" | "6G";

interface RadiosProps {
  // siteId?: number;
  defaultConfiguration?: any;
}

const Radios: React.FC<RadiosProps> = ({ defaultConfiguration }) => {
  const [form] = Form.useForm();
  const radioScheduled = Form.useWatch("radio-schedule-enable", form) === 1;
  const [isModalVisible, setIsModalVisible] = useState(false);
  const formikRef = useRef<SetRadioFormRef>(null);
  const [profiles, setProfiles] = useState<any[]>([]);
  const navigate = useNavigate();
  const { t } = useTranslation();
  const type = 4; // time range profile 类型
  const countrycode = Form.useWatch("country", form) || "US";

  // get siteid
  const [siteId, setSiteId] = useState<number | null>(null);
  useEffect(() => {
    const hash = location.hash.replace("#", "");
    const parsed = Number(hash);
    if (!isNaN(parsed)) {
      setSiteId(parsed);
    }
  }, [location.hash]);
  // console.log(siteId)

  // 获取 time range profile 列表
  const fetchList = async () => {
    if (typeof siteId !== "number") return;
    try {
      const res = await getWirelessProfileList(type, siteId, 1, 10000);
      if (res?.status === 200) {
        setProfiles(res.info || []);
      } else {
        message.error(res?.info || "Fetch profile list failed");
      }
    } catch (err) {
      message.error("An error occurred while fetching profiles.");
    }
  };

  //siteid 变化刷新列表
  useEffect(() => {
    if (siteId !== null) {
      // console.log("Fetching profile for siteId", siteId);
      fetchList();
    }
  }, [siteId]);

  //初始化default配置，从接口获取
  const radioConfigObj = defaultConfiguration.find(item => item.id === "0-radio");
  const initialRadioValues = radioConfigObj?.configuration?.[0]?.configuration
    ? JSON.parse(radioConfigObj.configuration[0].configuration)
    : null;
  // console.log("radio默认配置:", initialRadioValues);
  
  //校验
  // const validationSchema = yup.object({
  //   "2G": SINGLE_RADIO_SCHEMA(t, false, "2G").required(),
  //   "5G": SINGLE_RADIO_SCHEMA(t, false, "5G").required(),
  //   "6G": SINGLE_RADIO_SCHEMA(t, false, "6G").required(),
  // });
  const bands: BandType[] = ["2G", "5G", "6G"];

  const validationSchema = yup.object().shape(
    bands.reduce((acc, band) => {
      acc[band] = SINGLE_RADIO_SCHEMA(t, false, band).nullable().notRequired();
      return acc;
    }, {} as Record<BandType, any>)
  );


  const handleApply = () => {
    form
      .validateFields()
      .then(() => {
        formikRef.current?.submit();
      })
      .catch(() => {
        message.error("Please check your input.");
      });
  };

  const transformToRadios = (values: Record<string, any>, parentValues: Record<string, any>) => {
    //处理参数
    const processedParentValues = { ...parentValues };
    if (processedParentValues["time-range-index"]) {
      processedParentValues["time-range-index"] = setProfileVariable(processedParentValues["time-range-index"]);
    }

    return {
      radios: ['2G', '5G', '6G']
        .map(band => values[band] ? { ...values[band], band, ...processedParentValues } : null)
        .filter(Boolean),
    };
  };

  const handleFormikApply = (formikValues: Partial<Record<BandType, any>>) => {
    const parentValues = form.getFieldsValue();
    const { configuration, ...parentWithoutConfig } = parentValues;
    const radiosObj = transformToRadios(formikValues, parentWithoutConfig);
    const jsonString = JSON.stringify(radiosObj);
    const finalPayload = [{ name: "Radio", configuration: jsonString }];

    console.log("最终输出：", finalPayload);
    //调接口上传
    message.success("Settings applied!");
  };

  const handleCancel = () => {
    form.resetFields();
    message.info("Changes have been reset.");
  };

  return (
    <Card bordered={false} style={{ boxShadow: "none", padding: 0, minWidth: 1300, width: "100%", overflowX: "auto" }}>
      <Form form={form} layout="horizontal" initialValues={{ country: "US", "radio-schedule-radio-mode": 0, 'radio-schedule-enable': 0 }}>
        <Row gutter={32}>
          <Col span={16}>
            <Form.Item
              label="Country"
              name="country"
              labelAlign="left"
              labelCol={{ span: 3 }}
              wrapperCol={{ span: 14 }}
            >
              <Select options={COUNTRY_LIST} style={{ width: "35%" }} />
            </Form.Item>
          </Col>
        </Row>
        <Row gutter={32} style={{ marginTop: 8 }}>
          <Col span={16}>
            <Form.Item
              name="radio-schedule-enable"
              label="RadioScheduled"
              labelAlign="left"
              valuePropName="checked"
              labelCol={{ span: 3 }}
              wrapperCol={{ span: 14 }}
            >
              {/* <Switch onChange={(checked) => { if (checked) fetchList(); }} /> */}
              <Switch
                checked={form.getFieldValue('radio-schedule-enable') === 1}
                onChange={(checked) => {
                  const value = checked ? 1 : 0;
                  form.setFieldsValue({ 'radio-schedule-enable': value });
                  if (value === 1) {
                    fetchList();
                  }
                }}
              />
            </Form.Item>
          </Col>
        </Row>
        {radioScheduled && (
          <>
            <Row gutter={32} style={{ marginTop: 8 }}>
              <Col span={16}>
                <Form.Item
                  name="radio-schedule-radio-mode"
                  label="Radio Mode"
                  labelAlign="left"
                  labelCol={{ span: 3 }}
                  wrapperCol={{ span: 14 }}
                >
                  <Radio.Group>
                    <Radio value={1}>radio on</Radio>
                    <Radio value={0}>radio off</Radio>
                  </Radio.Group>
                </Form.Item>
              </Col>
            </Row>

            <Form.Item name="time-range-name" noStyle>
              {/* 隐藏字段 */}
              <Input type="hidden" />
            </Form.Item>
            <Row gutter={32} style={{ marginTop: 8 }}>
              <Col span={16}>
                <Form.Item
                  name="time-range-index"
                  label="Time Range"
                  labelAlign="left"
                  labelCol={{ span: 3 }}
                  wrapperCol={{ span: 14 }}
                  required
                  rules={[{ required: true, message: 'Please select time range!' }]}
                >
                  <Select
                    style={{ width: "35%" }}
                    placeholder={`Select a time range Profile`}
                    onChange={(selectedId) => {
                      if (selectedId === "custom") {
                        form.setFieldsValue({ "time-range-index": null });
                        setIsModalVisible(true);
                        return;
                      }
                      const selectedProfile = profiles.find(p => p.id === selectedId);
                      if (selectedProfile) {
                        form.setFieldsValue({
                          "time-range-index": selectedProfile.variable_id,
                          "time-range-name": selectedProfile.name
                        });
                      }
                    }}
                    dropdownRender={(menu) => (
                      <div>
                        {menu}
                        <Divider style={{ margin: "0" }} />
                        <Button type="link" icon={<PlusOutlined />}
                          onClick={() => {
                            form.setFieldsValue({ "time-range-index": null });
                            setIsModalVisible(true);
                          }} style={{ width: '100%', borderTop: '1px solid #E7E7E7' }}>
                          Create time range profile
                        </Button>
                      </div>
                    )}
                  >
                    {profiles.map((item) => (
                      <Option key={item.id} value={item.id}>
                        {item.name}
                      </Option>
                    ))}
                  </Select>

                  <Button
                    type="text"
                    style={{ backgroundColor: "#fff", marginLeft: 8 }}
                    onClick={() => navigate("/wireless/profile/TimeRange")}
                  >
                    Manage Time Range Profile
                  </Button>
                </Form.Item>
              </Col>
            </Row>
          </>
        )}

        <SetRadioForm
          ref={formikRef}
          initialValues={initialRadioValues}
          validationSchema={validationSchema}
          onApply={handleFormikApply}
          countryCode={countrycode}
        />

        <div style={{ textAlign: "right", marginTop: 24 }}>
          <Button onClick={handleCancel} style={{ marginRight: 16, width: 100 }}>
            Cancel
          </Button>
          <Button type="primary" onClick={handleApply} style={{ width: 100 }}>
            Apply
          </Button>
        </div>
      </Form>

      <TimeRangeModal
        siteId={siteId}
        visible={isModalVisible}
        onClose={() => {
          setIsModalVisible(false);
          fetchList(); // 关闭后刷新列表
        }}
      />
    </Card>
  );
};

export default Radios;
