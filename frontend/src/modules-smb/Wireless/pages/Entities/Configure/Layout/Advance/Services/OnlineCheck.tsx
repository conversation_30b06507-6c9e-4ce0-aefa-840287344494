import React,{useEffect} from 'react';
import {Col,Row} from 'antd';
import CreatableSelectField from '@/modules-smb/Wireless/components/FormFields/CreatableSelectField';
import MultiSelectField from '@/modules-smb/Wireless/components/FormFields/MultiSelectField';
import NumberField from '@/modules-smb/Wireless/components/FormFields/NumberField';
import ToggleField from '@/modules-smb/Wireless/components/FormFields/ToggleField';
import useFastField from '@/modules-smb/hooks/useFastField';
import { useTranslation } from 'react-i18next';
import { useFormikContext } from 'formik';
import {getSubSectionDefaults} from './servicesConstants';
const OnlineCheck = () => {
  const { value: onlineCheckEnabled } = useFastField({ name: 'configuration.services.online-check.enabled' });
    const {t}=useTranslation();
        
    const { values, setFieldValue, errors } = useFormikContext<any>();
    useEffect(() => {
       if (onlineCheckEnabled) {
        const defaultOnlineCheckConfig = getSubSectionDefaults(t,'online-check');
         setFieldValue('configuration.services.online-check', {
       ...defaultOnlineCheckConfig,
       enabled: true,
       });
       }else {
     // 可选：关闭时清除 online-check 字段
     setFieldValue('configuration.services.online-check', undefined);
       }
     }, [onlineCheckEnabled]);
  return(
<Row gutter={[20, 20]} style={{ marginBottom: 0, marginTop: 8, width: '100%' }}>
    <Col>
      <ToggleField
        name="configuration.services.online-check.enabled"
        label="Online Check"
      />
      {onlineCheckEnabled && (
        <> 
        <MultiSelectField
        name="configuration.services.online-check.action"
        label="Action"
        definitionKey="service.online-check.action"
        // isDisabled={!editing}
        w={280}
        isRequired
        options={[
          { value: 'wifi', label: 'wifi' },
          {
            value: 'leds',
            label: 'leds',
          },
        ]}
      />
      <CreatableSelectField
        name="configuration.services.online-check.ping-hosts"
        label="Ping-Hosts"
        definitionKey="service.online-check.ping-hosts"
        // isDisabled={!editing}
        isRequired
        w={280}
      />
      <CreatableSelectField
        name="configuration.services.online-check.download-hosts"
        label="Download-Hosts"
        definitionKey="service.online-check.download-hosts"
        // isDisabled={!editing}
        isRequired
        w={280}
      />
      <NumberField
        name="configuration.services.online-check.check-interval"
        label="Check-Interval"
        definitionKey="service.online-check.check-interval"
        // isDisabled={!editing}
        isRequired
        w={140}
      />
      <NumberField
        name="configuration.services.online-check.check-threshold"
        label="Check-Threshold"
        definitionKey="service.online-check.check-threshold"
        // isDisabled={!editing}
        isRequired
        w={140}
      />
        </>
      )}
      
    </Col>
  </Row>
  );
  
};

export default React.memo(OnlineCheck);
