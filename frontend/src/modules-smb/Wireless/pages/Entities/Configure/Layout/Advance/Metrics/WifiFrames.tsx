import React,{useEffect} from 'react';
import { useTranslation } from 'react-i18next';
import {Col,Row} from 'antd';
import MultiSelectField from '@/modules-smb/Wireless/components/FormFields/MultiSelectField';
import ToggleField from '@/modules-smb/Wireless/components/FormFields/ToggleField';
import useFastField from '@/modules-smb/hooks/useFastField';
import {useFormikContext} from 'formik';
import { getSubSectionDefaults } from './metricsConstants';

const WifiFrames = () => {
  const { t } = useTranslation();
  const { value: wifiFramesEnabled } = useFastField({ name: 'configuration.metrics.wifi-frames.enabled' });
  const { values, setFieldValue, errors } = useFormikContext<any>();
  useEffect(() => {
     if (wifiFramesEnabled) {
      const defaultWifiSteeringConfig = getSubSectionDefaults(t,'wifi-frames');
       setFieldValue('configuration.metrics.wifi-frames', {
     ...defaultWifiSteeringConfig,
     enabled: true,
     });
     }else {
   // 可选：关闭时清除 wifi-frames 字段
   setFieldValue('configuration.metrics.wifi-frames', undefined);
     }
   }, [wifiFramesEnabled]);
  return (
    <Row gutter={[20, 20]} style={{ marginBottom: 0, marginTop: 8, width: '100%' }}>
            <Col>
              <ToggleField
                name="configuration.metrics.wifi-frames.enabled"
                label="Wifi Frames"
              />
              {wifiFramesEnabled && (
              <MultiSelectField
                name="configuration.metrics.wifi-frames.filters"
                label="Filters"
                definitionKey="metrics.wifi-frames.filters"
                hasVirtualAll
                canSelectAll
                w={280}
                options={[
                  { value: 'probe', label: 'probe' },
                  { value: 'auth', label: 'auth' },
                  { value: 'assoc', label: 'assoc' },
                  { value: 'disassoc', label: 'disassoc' },
                  { value: 'deauth', label: 'deauth' },
                  { value: 'local-deauth', label: 'local-deauth' },
                  { value: 'inactive-deauth', label: 'inactive-deauth' },
                  { value: 'key-mismatch', label: 'key-mismatch' },
                  { value: 'beacon-report', label: 'beacon-report' },
                  { value: 'radar-detected', label: 'radar-detected' },
                ]}
                isRequired
                // isDisabled={!editing}
              />
              )}
              
            </Col>
          </Row>
  );
};

export default React.memo(WifiFrames);
