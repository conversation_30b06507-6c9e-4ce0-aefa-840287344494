import React,{useEffect} from 'react';
import {Col,Row} from 'antd';
import NumberField from '@/modules-smb/Wireless/components/FormFields/NumberField';
import SelectField from '@/modules-smb/Wireless/components/FormFields/SelectField';
import StringField from '@/modules-smb/Wireless/components/FormFields/StringField';
import ToggleField from '@/modules-smb/Wireless/components/FormFields/ToggleField';
import useFastField from '@/modules-smb/hooks/useFastField';
import { useFormikContext } from 'formik';
import {getSubSectionDefaults} from './servicesConstants';
import { useTranslation } from 'react-i18next';
const Log = () => {
   const { value: logEnabled } = useFastField({ name: 'configuration.services.log.enabled' });
   const {t}=useTranslation();

   const { values, setFieldValue, errors } = useFormikContext<any>();
   console.log('log values',values);
   useEffect(() => {
      if (logEnabled) {
       const defaultLogConfig = getSubSectionDefaults(t,'log');
        setFieldValue('configuration.services.log', {
      ...defaultLogConfig,
      enabled: true,
      });
      }else {
    // 可选：关闭时清除 log 字段
    setFieldValue('configuration.services.log', undefined);
      }
    }, [logEnabled]);
  return(
  <Row gutter={[20, 20]} style={{ marginBottom: 0, marginTop: 8, width: '100%' }}>
      <Col>
        <ToggleField
          name="configuration.services.log.enabled"
          label="Log"
        />
        {logEnabled && (
          <>
          <StringField
          name="configuration.services.log.host"
          label="Host"
          definitionKey="service.log.host"
          // isDisabled={!editing}
          isRequired
          w={280}
        />
        <NumberField
          name="configuration.services.log.port"
          label="Port"
          definitionKey="service.log.port"
          // isDisabled={!editing}
          isRequired
          w={140}
        />
        <SelectField
          name="configuration.services.log.proto"
          label="Proto"
          definitionKey="service.log.proto"
          // isDisabled={!editing}
          isRequired
          w={280}
          options={[
            { value: 'udp', label: 'udp' },
            {
              value: 'tcp',
              label: 'tcp',
            },
          ]}
        />
        <NumberField
          name="configuration.services.log.size"
          label="Size"
          definitionKey="service.log.size"
          // isDisabled={!editing}
          isRequired
          w={140}
        />
          </>
        )}
      </Col>
    </Row>
  );
};

export default React.memo(Log);
