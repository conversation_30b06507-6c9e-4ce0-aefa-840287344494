import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Button, Table, Modal, Form, Input, Row, Col } from 'antd';
import { PlusOutlined } from '@ant-design/icons';

interface User {
  username: string;
  password: string;
}

interface CredentialsUsersFormProps {
  value?: User[];
  onChange?: (users: User[]) => void;
}

const initialUser: User = { username: '', password: '' };

const CredentialsUsersForm: React.FC<CredentialsUsersFormProps> = ({ value = [], onChange }) => {
  const { t } = useTranslation();
  const [users, setUsers] = useState<User[]>(value);
  const [modalOpen, setModalOpen] = useState(false);
  const [form] = Form.useForm();

  useEffect(() => {
    setUsers(value);
  }, [value]);

  const handleAddUser = () => {
    setModalOpen(true);
    form.setFieldsValue(initialUser);
  };

  const handleSaveUser = () => {
    form.validateFields().then(values => {
      const newUsers = [values, ...users];
      setUsers(newUsers);
      onChange && onChange(newUsers);
      setModalOpen(false);
      form.resetFields();
    });
  };

  const handleDeleteUser = (idx: number) => {
    const newUsers = [...users];
    newUsers.splice(idx, 1);
    setUsers(newUsers);
    onChange && onChange(newUsers);
  };

  const columns = [
    { title: 'Username', dataIndex: 'username', key: 'username' },
    { title: 'Password', dataIndex: 'password', key: 'password' },
    {
      title: 'Operate',
      key: 'action',
      render: (_: any, _record: any, idx: number) => (
        <Button type="text" onClick={() => handleDeleteUser(idx)}>
          Delete
        </Button>
      ),
    },
  ];

  return (
    <div>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 8 }}>
        <Button type="primary" icon={<PlusOutlined />} onClick={handleAddUser}>
          Add Credentials
        </Button>
      </div>
      <Table
        columns={columns}
        dataSource={users}
        pagination={false}
        rowKey={(_record, idx) => (typeof idx === 'number' ? idx.toString() : '')}
        size="middle"
        bordered
      />
      <Modal
        open={modalOpen}
        title='Add Credentials'
        onCancel={() => setModalOpen(false)}
        onOk={handleSaveUser}
        okText='Apply'
        cancelText='Cancel'
        destroyOnClose
      >
        <Form form={form}>
          <Row gutter={24}>
            <Col span={24}>
              <Form.Item
                label='username'
                name="username"
                rules={[{ required: true, message: t('form.required') }]}
              >
                <Input />
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item
                label='password'
                name="password"
                rules={[{ required: true, message: t('form.required') }]}
              >
                <Input.Password />
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Modal>
    </div>
  );
};

export default CredentialsUsersForm; 