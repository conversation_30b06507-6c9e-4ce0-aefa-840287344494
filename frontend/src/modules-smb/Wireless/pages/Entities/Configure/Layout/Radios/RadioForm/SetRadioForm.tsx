import React, { forwardRef, useImperativeHandle, useRef, useEffect, useState } from "react";
import { InputNumber, Select, Radio, Col, Row, Collapse, Switch, Tooltip, Form } from "antd";
import { QuestionCircleFilled } from "@ant-design/icons";
import { Formik, Form as FormikForm, FormikProps, FormikValues } from "formik";
import _ from "lodash";
import AdvancedSettings from "./AdvancedSettings";
// import He from "./He";
// import ChannelPicker from "./ChannelPicker";
// import { SINGLE_RADIO_SCHEMA } from '../radiosConstants';
import * as yup from "yup";
import "./SetRadioForm.css";
import { getWirelessChannels } from '@/modules-smb/Wireless/apis/configure_api';

const { Option } = Select;
type BandType = "2G" | "5G" | "6G";

const itemLayout = { labelCol: { span: 8 }, wrapperCol: { span: 14 } };

//标签提示语
export function labelWithTooltip(label: string, tooltipText: string): React.ReactNode {
  return (
    <span>
      {label}&nbsp;
      <Tooltip title={tooltipText}>
        <QuestionCircleFilled
          style={{ color: '#B3BBC8', cursor: 'pointer', paddingLeft: 0, marginLeft: 2 }}
        />
      </Tooltip>
    </span>
  );
}

//radio通用选项布局
const renderRadioPanel = (
  band: BandType,
  values: FormikValues,
  setFieldValue: (path: string[], val: any) => void,
  channelData: any,
  isDisabled: boolean
): React.ReactNode => {
  const bandKey = `${band.toLowerCase()}_channel`;
  //channel-width
  const widths = Object.keys(channelData?.[bandKey] || {}).filter((key) => key !== "dfs");
  const selectedWidth = String(_.get(values, [band, "channel-width"]));
  const options = widths.map((w) => ({ label: `${w} MHz`, value: String(w) })); //  value 是 string
  const channelOptions = selectedWidth ? channelData?.[bandKey]?.[selectedWidth] || [] : [];

  return (
    <Form layout="horizontal" labelAlign="left" disabled={isDisabled}>
      <div style={{ padding: "0 24px" }}>
        <Row gutter={32}>
          <Col span={8}>
            <Form.Item name="tx-power" label="TX-Power" {...itemLayout}>
              <Radio.Group
                value={typeof _.get(values, [band, 'tx-power']) === 'number' ? 'Set Power' : 'Default'}
                onChange={(e) => {
                  const val = e.target.value;
                  if (val === 'Default') {
                    setFieldValue([band, 'tx-power'], undefined);
                  } else {
                    // 如果之前不是数字，默认给1
                    const currentVal = _.get(values, [band, 'tx-power']);
                    if (typeof currentVal !== 'number') {
                      setFieldValue([band, 'tx-power'], 1);
                    }
                  }
                }}
              >
                <Radio value="Default">Default</Radio>
                <Radio value="Set Power">
                  <span style={{ display: 'inline-flex', alignItems: 'center' }}>
                    Set Power
                    {typeof _.get(values, [band, 'tx-power']) === 'number' && (
                      <InputNumber
                        min={1}
                        max={30}
                        value={_.get(values, [band, 'tx-power'])}
                        onChange={(val) => setFieldValue([band, 'tx-power'], val)}
                        style={{ width: 110, marginLeft: 4 }}
                        addonAfter="dBm"
                      />
                    )}
                  </span>
                </Radio>
              </Radio.Group>
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item
              required
              label={labelWithTooltip("Channel-Width", "The channel width that the radio shall use. This is just a hint for the AP. If the requested value is not supported then the AP will use the highest common denominator.")}
              {...itemLayout}
            >
              <Select
                style={{ width: "80%" }}
                value={options.some(opt => opt.value === selectedWidth) ? selectedWidth : 'None'}
                onChange={(val) => {
                  setFieldValue([band, "channel-width"], val);
                  setFieldValue([band, "channel"], "auto");
                }}
              >
                {options.map((opt) => (
                  <Option key={opt.value} value={opt.value}>
                    {opt.label}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item label="Channel" {...itemLayout}>
              <Select
                style={{ width: "80%" }}
                value={channelOptions.length === 0 ? 'None' : _.get(values, [band, "channel"])}
                onChange={(val) => setFieldValue([band, "channel"], val)}
                disabled={isDisabled || !selectedWidth}
                placeholder="Select Channel"
              >
                <Option value="auto">auto</Option>
                {(channelOptions || []).map((ch) => (
                  <Option key={ch} value={ch}>
                    {ch}
                  </Option>
                ))}
              </Select>

            </Form.Item>
          </Col>
        </Row>

        <Row gutter={32}>
          {band === "2G" && (
            <Col span={8}>
              <Form.Item
                label={labelWithTooltip("Legacy-Rates", "Allow legacy 802.11b data rates.")}
                {...itemLayout}
              >
                <Switch
                  checked={_.get(values, [band, "legacy-rates"])}
                  onChange={(checked) => setFieldValue([band, "legacy-rates"], checked ? true : undefined)}
                />
              </Form.Item>
            </Col>
          )}
          <Col span={8}>
            <Form.Item
              label={labelWithTooltip("Multiple-Bssid", "Enabling this option will make the PHY broadcast its BSSs using the multiple BSSID beacon IE.")}
              {...itemLayout}
            >
              <Switch
                checked={_.get(values, [band, "multiple-bssid"])}
                onChange={(checked) => setFieldValue([band, "multiple-bssid"], checked)}
              />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item label="Maximum-Clients" {...itemLayout}>
              <InputNumber
                min={1}
                style={{ width: "40%" }}
                value={_.get(values, [band, "maximum-clients"])}
                onChange={(val) => setFieldValue([band, "maximum-clients"], val)}
              />
            </Form.Item>
          </Col>
        </Row>

        <AdvancedSettings
          namePrefix={band}
          isDisabled={isDisabled}
          values={values}
          setFieldValue={setFieldValue}
        />
      </div>
    </Form>
  );
};

export interface SetRadioFormRef {
  submit: () => void;
}

interface SetRadioFormProps {
  initialValues: Record<BandType, any>;
  validationSchema: yup.ObjectSchema<any>;
  onApply: (values: Partial<Record<BandType, any>>) => void
  countryCode: string; //for get channels
}

const SetRadioForm = forwardRef<SetRadioFormRef, SetRadioFormProps>(
  ({ initialValues, validationSchema, onApply, countryCode }, ref) => {
    const formikRef = useRef<FormikProps<Record<BandType, any>>>(null);
    const [channelData, setChannelData] = useState<any>({});
    const [formValues, setFormValues] = useState(initialValues);
    const [disabledBands, setDisabledBands] = useState<BandType[]>([]);//存储，没有channel-width的band
    console.log(formValues)

    //向父组件暴露，提交的formik
    useImperativeHandle(ref, () => ({
      submit: () => {
        const formik = formikRef.current;
        if (!formik) return;
        const filtered = _.omit(formik.values, disabledBands); // 剔除无效的radio选项
        // console.log("提交的数据:", filtered);
        onApply(filtered); // 直接提交当前数据
      },
    }));

    useEffect(() => {
      //get channels by countrycode
      getWirelessChannels(countryCode).then((res) => {
        const result = res || {};
        setChannelData(result);
        const newValues = _.cloneDeep(initialValues);
        const disabled: BandType[] = [];
        //处理获取到channel-width,shannel为空的情况
        (["2G", "5G", "6G"] as BandType[]).forEach((band) => {
          const bandKey = `${band.toLowerCase()}_channel`;
          const width = _.get(newValues, [band, "channel-width"]);
          const channel = _.get(newValues, [band, "channel"]);
          const hasWidths = result?.[bandKey] && Object.keys(result[bandKey]).length > 0;
          if (!width || !hasWidths || !result[bandKey][width] || result[bandKey][width].length === 0) {
            _.set(newValues, [band, "channel-width"], "none");
            _.set(newValues, [band, "channel"], "none");
            disabled.push(band);//// 加入禁用radio
          }
        });

        setDisabledBands(disabled);
        setFormValues(newValues);
      });
    }, [countryCode]);


    return (
      <div >
        <Formik
          innerRef={formikRef}
          enableReinitialize  //处理获取到channel-width,shannel为空的情况
          initialValues={formValues}   //后续从接口获取
          validationSchema={validationSchema}
          onSubmit={(values) => {
            onApply(values);
          }}
        >
          {({ values, setFieldValue }) => (
            <FormikForm >
              <Collapse
                className="no-collapse-border"
                style={{ marginBottom: 24, border: "none" }}
                expandIconPosition="right"
                // defaultActiveKey={["2g"]}
                items={[
                  {
                    key: "2g",
                    label: <h3 style={{ fontSize: "16px", margin: 4, border: "none" }}>2G Radio</h3>,
                    children: renderRadioPanel("2G", values, (path, val) => {
                      setFieldValue(path.join("."), val);
                    }, channelData, disabledBands.includes("2G")),
                  },
                  {
                    key: "5g",
                    label: <h3 style={{ fontSize: "16px", margin: 4, border: "none" }}>5G Radio</h3>,
                    children: renderRadioPanel("5G", values, (path, val) => {
                      setFieldValue(path.join("."), val);
                    }, channelData, disabledBands.includes("5G")),
                  },
                  {
                    key: "6g",
                    label: <h3 style={{ fontSize: "16px", margin: 4, border: "none" }}>6G Radio</h3>,
                    children: renderRadioPanel("6G", values, (path, val) => {
                      setFieldValue(path.join("."), val);
                    }, channelData, disabledBands.includes("6G")),
                  },
                ]}
              />
            </FormikForm>
          )}
        </Formik>
      </div>
    );
  }
);

export default SetRadioForm;