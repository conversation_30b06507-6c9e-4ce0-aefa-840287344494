import React ,{useState}from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  message,
} from "antd";
import { useTranslation } from "react-i18next";
import { Formik, Form } from "formik";
import { v4 as uuid } from 'uuid';
import SelectField from "@/modules-smb/Wireless/components/FormFields/SelectField";
import MultiSelectFieldWithCheckBox from "@/modules-smb/Wireless/components/FormFields/MultiSelectFieldWithCheckBox";
import StringField from "@/modules-smb/Wireless/components/FormFields/StringField";
import ToggleField from "@/modules-smb/Wireless/components/FormFields/ToggleField";

import { INTERFACE_ETHERNET_SCHEMA } from "../ethernetConstants";

const EthernetCreateModal = ({
  title,
  okText,
  isModalOpen,
  onCancel,
  role = "",
  modalClass = "",
}) => {
  const { t } = useTranslation();

  const [form<PERSON>ey, setForm<PERSON>ey] = useState(uuid());
  const validationSchema = React.useMemo(
    () => INTERFACE_ETHERNET_SCHEMA(t, false, role),
    [t, role]
  );
  const initialValues = validationSchema.getDefault();

  const allSelectPortsOptions = [
    { value: "LAN*", label: "LAN*" },
    { value: "LAN1", label: "LAN1" },
    { value: "LAN2", label: "LAN2" },
    { value: "LAN3", label: "LAN3" },
    { value: "LAN4", label: "LAN4" },
  ];

  return (
    <Formik
      key={formKey}
      initialValues={initialValues}
      validationSchema={validationSchema}
     onSubmit={async (values, { setSubmitting }) => {
    try {
      const success = true;
      if (success) {
        message.success("Added successfully");
        onCancel();             // 关闭弹窗
        setFormKey(prev => prev + 1); // 重建 form
      } else {
        message.error("Failed to add.");
      }
    } catch {
      message.error("Network error.");
    } finally {
      setSubmitting(false);
    }
  }}
    >
      {({ handleSubmit, resetForm,values}) => {
        console.log("ethernet============",values);
        return(<>
        <Modal
          className={modalClass || ""}
          title={
            <div>
              {title}
              <Divider style={{ marginTop: 8, marginBottom: 0 }} />
            </div>
          }
          open={isModalOpen}
          footer={[
            <Divider style={{ marginTop: 0, marginBottom: 20 }} key="divider" />,
            <Button key="cancel" onClick={() => { resetForm(); onCancel(); }}>
              Cancel
            </Button>,
            <Button key="ok" type="primary" onClick={() => {handleSubmit();}}>
              Apply
            </Button>,
          ]}
          onCancel={() => {
            resetForm();
            onCancel();
          }}
          destroyOnClose
        >
          <Form>
            <MultiSelectFieldWithCheckBox
              name="select-ports"
              label="Ports"
              options={allSelectPortsOptions}
              isRequired
            />
            <SelectField
              name="network"
              label="Network"
              options={[
                { label: "Auto", value: "auto" },
                { label: "Tagged", value: "tagged" },
                { label: "Un-tagged", value: "un-tagged" },
              ]}
              w={150}
              isRequired
            />
            <StringField
              name="macaddr"
              label="Mac Address"
              w={150}
              emptyIsUndefined
            />
            <SelectField
              name="vlan-tag"
              label="Vlan Tag"
              options={[
                { label: "Auto", value: "auto" },
                { label: "Tagged", value: "tagged" },
                { label: "Un-tagged", value: "un-tagged" },
              ]}
              w={150}
            />
            <ToggleField name="multicast" label="Multicast" />
            <ToggleField name="learning" label="Learning" />
            <ToggleField name="reverse-path" label="Reverse Path" />
          </Form>
        </Modal>
        </>);
        
}}
    </Formik>
  );
};

export default EthernetCreateModal;
