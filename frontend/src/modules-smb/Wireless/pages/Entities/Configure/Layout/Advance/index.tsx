import React, { useEffect, useMemo, useState } from 'react';
import { Collapse, Button, message } from 'antd';
import { useTranslation } from 'react-i18next';
import { Formik, Form } from 'formik';
import * as Yup from 'yup';

import SystemSettingsFields from './System/SystemSettingsFields';
import DhcpService from './DhcpService';
import Ethernet from './Ethernet';
import Services from './Services';
import Metrics from './Metrics';

import { SERVICES_SCHEMA } from './Services/servicesConstants';
import { METRICS_SCHEMA } from './Metrics/metricsConstants';
// import { ADVANCE_SCHEMA } from './advanceConstants';
import { UNIT_SCHEMA} from './System/unitConstants';
import {INTERFACE_ETHERNET_SCHEMA} from './ethernetConstants';

import './collapse.css';

const Advance: React.FC = () => {
  const { t } = useTranslation();

  // 控制哪些子模块被启用（仿 ConfigurationSectionsCard）
  const defaultModules = ['system', 'ethernet', 'services', 'metrics','DHCP Service'];
  const [activeModules, setActiveModules] = useState<string[]>([]);

  // 根据启用模块生成 initialValues
  const initialValues = useMemo(() => {
    const base = {
      configuration: {
      ...SERVICES_SCHEMA(t).cast({}).services,
      ...METRICS_SCHEMA(t).cast({}).metrics,
      ...UNIT_SCHEMA(t).cast({}).unit,
      // ...INTERFACE_ETHERNET_SCHEMA(t).cast({}).configuration,


      },
    };
    if(defaultModules.includes('system')){
      Object.assign(base.configuration, UNIT_SCHEMA(t).cast({}));
    }

    if (defaultModules.includes('services')) {
      Object.assign(base.configuration, SERVICES_SCHEMA(t).cast({})); // 服务模块默认值
    }

    if (defaultModules.includes('metrics')) {
      Object.assign(base.configuration, METRICS_SCHEMA(t).cast({}));
    }

    // if (defaultModules.includes('ethernet')) {
    //   base['select-ports'] = [];
    //   base.multicast = true;
    //   base.learning = true;
    //   base.isolate = false;
    //   base.macaddr = '';
    //   base['reverse-path'] = false;
    //   base['vlan-tag'] = 'auto';
    // }

    return base;
  }, [t]);

  // 拼接校验 schema
  const combinedSchema = useMemo(() => {
    let schema = INTERFACE_ETHERNET_SCHEMA(t);
    schema=schema.concat(UNIT_SCHEMA(t)).concat(SERVICES_SCHEMA(t)).concat(METRICS_SCHEMA(t));
    // if (defaultModules.includes('services')) schema = schema.concat(SERVICES_SCHEMA(t));
    // if (defaultModules.includes('metrics')) schema = schema.concat(METRICS_SCHEMA(t));
    return schema;
  }, [t]);

  // 初始化激活模块
  useEffect(() => {
    setActiveModules(defaultModules);
  }, []);

  const onSubmit = (values: any) => {
    console.log('Form submitted:', values);
    const result: any[] = [];

  // if (activeModules.includes('services') && values.configuration.services) {
  //   result.push({
  //     configuration: JSON.stringify({ services: values.configuration.services }),
  //     name: 'Services',
  //   });
  // }

  // if (activeModules.includes('metrics') && values.configuration.metrics) {
  //   result.push({
  //     configuration: JSON.stringify({ metrics: values.configuration.metrics }),
  //     name: 'Metrics',
  //   });
  // }

  // if (activeModules.includes('system') && values.configuration.unit) {
  //   result.push({
  //     configuration: JSON.stringify({ unit: values.configuration.unit }),
  //     name: 'System',
  //   });
  // }
  // console.log('result',result);
  };

  const systemItems = [
    {
      key: '1',
      label: <span style={{ fontWeight: 600, fontSize: 16 }}>System</span>,
      children: <SystemSettingsFields editing={true} />,
    },
     {
      key: '2',
      label: <span style={{ fontWeight: 600, fontSize: 16 }}>DHCP Service</span>,
      children: <DhcpService />,
    },
    {
      key: '3',
      label: <span style={{ fontWeight: 600, fontSize: 16 }}>Manage Ethernet Ports</span>,
      children: <Ethernet />,
    },
    {
      key: '4',
      label: <span style={{ fontWeight: 600, fontSize: 16 }}>Services</span>,
      children: <Services />,
    },
    {
      key: '5',
      label: <span style={{ fontWeight: 600, fontSize: 16 }}>Metrics</span>,
      children: <Metrics />,
    },
  ]
  .filter((item) => {
    if (item.key === '1') return activeModules.includes('system');
    if (item.key === '2') return activeModules.includes('DHCP Service');
    if (item.key === '3') return activeModules.includes('ethernet');
    if (item.key === '4') return activeModules.includes('services');
    if (item.key === '5') return activeModules.includes('metrics');
    return true;
  });

  return (
    <div style={{ width: '100%', overflowX: 'auto' }}>
      <Formik initialValues={initialValues} onSubmit={onSubmit} validationSchema={combinedSchema}>
        {({ resetForm, handleSubmit,values }) => {
          console.log('values.......',values);
          const result: any[] = [];
          // 工具函数：递归去除 enabled 字段
          const removeEnabledField = (obj: any): any => {
            if (Array.isArray(obj)) {
              return obj.map(removeEnabledField);
            } else if (typeof obj === 'object' && obj !== null) {
              const newObj: any = {};
              for (const key in obj) {
                if (key === 'enabled') continue; // 过滤掉 enabled
                newObj[key] = removeEnabledField(obj[key]); // 递归处理
              }
              return newObj;
            }
            return obj; // 原始值
          };

  const cleanedServices = removeEnabledField(values.configuration.services);
  console.log('cleanedServices',cleanedServices);
  const cleanedMetrics = removeEnabledField(values.configuration.metrics);
  console.log('cleanedMetrics',cleanedMetrics);
  if (activeModules.includes('services') && values.configuration.services) {
    result.push({
      name: 'Services',
      configuration: JSON.stringify({ services: cleanedServices }),
    });
  }

  if (activeModules.includes('metrics') && values.configuration.metrics) {
    result.push({
      name: 'Metrics',
      configuration: JSON.stringify({ metrics: cleanedMetrics }),
    });
  }

  if (activeModules.includes('system') && values.configuration.unit) {
    result.push({
      name: 'System',
      configuration: JSON.stringify({ unit: values.configuration.unit }),
    });
  }
  console.log('result',result);
          return(
          <>
            <Collapse
              size="large"
              items={systemItems}
              defaultActiveKey={[]}
              expandIconPosition="right"
              className="no-collapse-border"
              style={{ marginTop: 12, marginBottom: 24, border: 'none' }}
              onChange={(key) => console.log('Collapse changed:', key)}
            />
            <div style={{ display: 'flex', justifyContent: 'flex-end', gap: 10 }}>
              <Button
                onClick={() => {
                  resetForm();
                  message.info('Changes have been reset.');
                }}
              >
                Cancel
              </Button>
              <Button type="primary" onClick={() => handleSubmit()}>
                Apply
              </Button>
            </div>
          </>
          );
        }}
      </Formik>
    </div>
  );
};

export default Advance;
