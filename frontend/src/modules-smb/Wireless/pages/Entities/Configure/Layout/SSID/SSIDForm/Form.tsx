import React, { useState, useEffect } from 'react';
import { Form, Input, Select, Button, Row, Col, message } from 'antd';
import { useTranslation } from 'react-i18next';
import { LabelTip } from '@/modules-smb/Wireless/components/LabelTip';
import { DEFAULT_SSID_SCHEMA } from '@/modules-smb/pages/ConfigurationPage/ConfigurationCard/ConfigurationSectionsCard/InterfaceSection/interfacesConstants';
import '@/modules-smb/Wireless/assets/form.scss';
import AuthenticationForm from './AuthenticationForm';
import ProfileSelect from '@/modules-smb/Wireless/components/ProfileSelect';
import AdvancedSettingsForm from './AdvancedSettingsForm';
import { createWirelessConfigure, updateWirelessConfigure } from "@/modules-smb/Wireless/apis/configure_api";
import { setProfileVariable, getProfileVariable } from "@/modules-smb/Wireless/utils/util";

interface Props {
  isDisabled?: boolean;
  resource?: any;
  onClose?: () => void;
  refresh?: () => void;
  siteId?: number;
}

const SSIDForm: React.FC<Props> = ({ isDisabled = false, resource, onClose, refresh, siteId }) => {
  const { t } = useTranslation();
  const [form] = Form.useForm();

  const defaultValues = DEFAULT_SSID_SCHEMA(t, true).cast();
  let initialValues
  // 处理编辑参数
  let ssidConfigure: any = {}
  if (resource && resource.ssid_configure) {
    ssidConfigure = { ...resource.ssid_configure }
    ssidConfigure.network = resource.network_name
    if(ssidConfigure.radius) {
      ssidConfigure.radius = getProfileVariable(ssidConfigure.radius)
    }
    if(ssidConfigure.schedule) {
      ssidConfigure.schedule = getProfileVariable(ssidConfigure.schedule)
    }
    if(ssidConfigure['multi-psk']) {
      ssidConfigure['multi-psk'] = getProfileVariable(ssidConfigure['multi-psk'])
    }
    if(ssidConfigure.captive?.['web-root']) {
      ssidConfigure.captive['web-root'] = getProfileVariable(ssidConfigure.captive['web-root'])
    }
    initialValues = ssidConfigure
  } else {
    initialValues = defaultValues
  }
  const [networkEnabled, setNetworkEnabled] = useState(!!initialValues?.network);
  const [scheduleEnabled, setScheduleEnabled] = useState(!!initialValues?.schedule);
  const [networkRole, setNetworkRole] = useState<string | undefined>(undefined);
  
  // 特殊处理network的role判断
  const handleNetworkProfileChange = (profile: any) => {
    let param = profile?.parameter;
    if (typeof param === 'string') {
      try { param = JSON.parse(param); } catch { param = {}; }
    }
    setNetworkRole(param?.role);
  };

  const handleFinish = (values: any) => {

    const submitValues = { ...values };
    // 处理传参
    const name = submitValues.name;
    const security = submitValues.encryption.proto;
    const network_name = submitValues.network;
    delete submitValues.network;
    if(submitValues.radius) {
      submitValues.radius = setProfileVariable(submitValues.radius)
    }
    if(submitValues.schedule) {
      submitValues.schedule = setProfileVariable(submitValues.schedule)
    }
    if(submitValues['multi-psk']) {
      submitValues['multi-psk'] = setProfileVariable(submitValues['multi-psk'])
    }
    if(submitValues.captive?.['web-root']) {
      submitValues.captive['web-root'] = setProfileVariable(submitValues.captive['web-root'])
    }
    const radio = (submitValues['wifi-bands'] || []).join(',');
    const ssid_configure = JSON.stringify(submitValues)

    if (resource && resource.id) {
      updateWirelessConfigure({
        id: resource.id,
        name: name,
        security: security,
        radio: radio,
        network_name: network_name,
        ssid_configure: ssid_configure,
      })
      .then(res => {
        if (res?.status !== 200) {
          message.error(res?.info);
          return;
        }
        message.info(res.info)
        refresh && refresh();
        onClose && onClose();
      })
      .catch(() => message.error('Failed to create Configure'))
    } else {
      createWirelessConfigure({
        site_id: siteId,
        name: name,
        security: security,
        radio: radio,
        network_name: network_name,
        ssid_configure: ssid_configure,
      })
      .then(res => {
        if (res?.status !== 200) {
          message.error(res?.info);
          return;
        }
        message.info(res.info)
        refresh && refresh();
        onClose && onClose();
      })
      .catch(() => message.error('Failed to create Configure'))
    }
  };

  return (
    <Form
      form={form}
      initialValues={initialValues}
      onFinish={handleFinish}
      disabled={isDisabled}
      className="wirelessForm"
    >
      <h3 className='header1'></h3>
      <Row gutter={24}>
        <Col span={12}>
          <Form.Item
            name="name"
            label="SSID"
            tooltip={LabelTip('interface.ssid.name')}
            rules={[
              { required: true, message: t('form.required') },
              { min: 1, max: 32, message: t('form.min_max_string', { min: 1, max: 32 }) },
            ]}
          >
            <Input placeholder="name" maxLength={32} />
          </Form.Item>
        </Col>
        <Col span={12} style={{ display: 'none' }}>
          <Form.Item
            name="bss-mode"
            label="bss-mode"
            tooltip={LabelTip('interface.ssid.bss-mode')}
            rules={[{ required: true, message: t('form.required') }]}
          >
            <Select placeholder="bss-mode">
              <Select.Option value="ap">ap</Select.Option>
              <Select.Option value="sta">sta</Select.Option>
              <Select.Option value="mesh">mesh</Select.Option>
              <Select.Option value="wds-ap">wds-ap</Select.Option>
              <Select.Option value="wds-sta">wds-sta</Select.Option>
            </Select>
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item
            name="wifi-bands"
            label="wifi-bands"
            tooltip={LabelTip('interface.ssid.wifi-bands')}
            rules={[
              { required: true, message: t('form.required') }
            ]}
          >
            <Select mode="multiple" placeholder="wifi-bands">
              <Select.Option value="2G">2G</Select.Option>
              <Select.Option value="5G">5G</Select.Option>
              <Select.Option value="6G">6G</Select.Option>
            </Select>
          </Form.Item>
        </Col>
      </Row>
      {/* Network功能 */}
      {/* <Row gutter={24}>
        <Col span={24}>
          <ProfileSelect
            label="Network"
            formName={["network"]}
            switchEnabled={networkEnabled}
            onSwitchChange={setNetworkEnabled}
            type={1}
            siteId={siteId}
            edit={ssidConfigure?.network}
            onProfileChange={handleNetworkProfileChange}
          />
        </Col>
      </Row> */}
      {/* Schedule 功能 */}
      <Row gutter={24}>
        <Col span={24}>
          <ProfileSelect
            label="Schedule"
            formName={["schedule"]}
            switchEnabled={scheduleEnabled}
            onSwitchChange={setScheduleEnabled}
            type={4}
            siteId={siteId}
            edit={ssidConfigure?.schedule}
          />
        </Col>
      </Row>
      {/* Authentication 模块 */}
      <h3 className='header2'>Authentication</h3>
      <AuthenticationForm
        resource={ssidConfigure}
        siteId={siteId}
        networkRole={networkRole}
      />
      {/* Advanced Settings 模块 */}
      <h3 className='header2'>Advanced Settings</h3>
      <AdvancedSettingsForm resource={ssidConfigure} siteId={siteId} />
      <div className='foot-btns'>
        <Button onClick={onClose}>Cancel</Button>
        <Button type="primary" htmlType="submit">Apply</Button>
      </div>
    </Form>
  );
};

export default SSIDForm;
