import * as React from 'react';
import { Tab, <PERSON>b<PERSON><PERSON>, TabPanel, TabPanels, Tabs, useColorMode } from '@chakra-ui/react';
import Radios from "./Radios";
import SSID from "./SSID";
import Advance from "./Advance";
import { useGetVenue } from "@/modules-smb/hooks/Network/Venues"
import { useGetSelectConfigurations } from "@/modules-smb/hooks/Network/Configurations"


const SETTING = 'configurepage.tabIndex';

const getDefaultTabIndex = () => {
  const tabIndex = localStorage.getItem(SETTING);

  try {
    if (tabIndex) {
      const parsedTabIndex = parseInt(tabIndex, 10);
      if (parsedTabIndex >= 0 && parsedTabIndex <= 1) {
        return parsedTabIndex;
      }
    }

    return 0;
  } catch (e) {
    return 0;
  }
};

type Props = {
  id: string;
};

const ConfigureLayout = ({ id }: Props) => {
  const [tabIndex, setTabIndex] = React.useState(getDefaultTabIndex());
  const { colorMode } = useColorMode();
  const isLight = colorMode === 'light';
  //获取默认配置,只在页面刷新时加载一次
  const getVenue = useGetVenue({ id: 0 });
  const selectIds = React.useMemo(() => getVenue.data?.configurations ?? [], [getVenue.data?.configurations]);
  const { data: degaultConfigurations = [], isFetching } = useGetSelectConfigurations({
    select: selectIds,
  });
  // console.log("批量default radios&advance结果:", degaultConfigurations);


  const tabStyle = {
    textColor: isLight ? 'var(--chakra-colors-blue-600)' : 'var(--chakra-colors-blue-300)',
    fontWeight: 'semibold',
    borderWidth: '0px',
    marginBottom: '-1px',
    borderBottom: '2px solid',
  };

  const onTabChange = (index: number) => {
    setTabIndex(index);
    localStorage.setItem(SETTING, index.toString());
  };

  React.useEffect(() => {
    setTabIndex(getDefaultTabIndex());
  }, [id]);

  return (
    <Tabs index={tabIndex} onChange={onTabChange}>
      <TabList>
        <Tab _selected={tabStyle}>SSID</Tab>
        <Tab _selected={tabStyle}>Radio</Tab>
        <Tab _selected={tabStyle}>Advance</Tab>
      </TabList>
      <TabPanels>
        <TabPanel px={0}>
          <SSID />
        </TabPanel>
        <TabPanel px={0}>
          <Radios defaultConfiguration={degaultConfigurations}/>
        </TabPanel>
        <TabPanel px={0}>
          <Advance />
        </TabPanel>
      </TabPanels>
    </Tabs>
  );
};

export default ConfigureLayout;
