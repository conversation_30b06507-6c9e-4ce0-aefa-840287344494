import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Form, Input, Select, InputNumber, Row, Col, Button } from 'antd';
import ProfileSelect from '@/modules-smb/Wireless/components/ProfileSelect';
import CredentialsUsersForm from './CredentialsUsersForm';

interface Props {
  resource?: any;
  siteId?: number;
  onAuthModeChange?: (authMode: string) => void;
}

const UAM_MAC_FORMAT_OPTIONS = [
  { value: 'aabbccddeeff', label: 'aabbccddeeff' },
  { value: 'aa-bb-cc-dd-ee-ff', label: 'aa-bb-cc-dd-ee-ff' },
  { value: 'aa:bb:cc:dd:ee:ff', label: 'aa:bb:cc:dd:ee:ff' },
  { value: 'AABBCCDDEEFF', label: 'AABBCCDDEEFF' },
  { value: 'AA:BB:CC:DD:EE:FF', label: 'AA:BB:CC:DD:EE:FF' },
  { value: 'AA-BB-CC-DD-EE-FF', label: 'AA-BB-CC-DD-EE-FF' },
];

const initialCredential = { username: '', password: '' };

const CaptivePortalForm: React.FC<Props> = ({ resource, siteId, onAuthModeChange }) => {
  const { t } = useTranslation();
  const [authMode, setAuthMode] = useState(resource?.captive?.['auth-mode'] || 'none');
  const [webRootEnabled, setWebRootEnabled] = useState(!!resource?.captive?.['web-root']);
  const [credentials, setCredentials] = useState([{ ...initialCredential }]);
  const [form] = Form.useFormInstance ? [Form.useFormInstance()] : [null];
  
  useEffect(() => {
    if (onAuthModeChange) onAuthModeChange(authMode);
  }, [authMode, form]);

  // credentials 动态表单
  const handleCredentialChange = (idx: number, key: string, value: string) => {
    const newCreds = credentials.map((item, i) =>
      i === idx ? { ...item, [key]: value } : item
    );
    setCredentials(newCreds);
    if (form) form.setFieldValue(['captive', 'credentials'], newCreds);
  };
  const addCredential = () => {
    const newCreds = [...credentials, { ...initialCredential }];
    setCredentials(newCreds);
    if (form) form.setFieldValue(['captive', 'credentials'], newCreds);
  };
  const removeCredential = (idx: number) => {
    const newCreds = credentials.filter((_, i) => i !== idx);
    setCredentials(newCreds);
    if (form) form.setFieldValue(['captive', 'credentials'], newCreds);
  };

  return (
    <>
      <h3 className='header2'>Captive Portal</h3>
      <Row gutter={24}>
        <Col span={12}>
          <Form.Item
            name={['captive', 'auth-mode']}
            label="auth-mode"
            rules={[{ required: true, message: t('form.required') }]}
          >
            <Select
              value={authMode}
              onChange={val => setAuthMode(val)}
              placeholder="auth-mode"
            >
              <Select.Option value="none">none</Select.Option>
              <Select.Option value="click-to-continue">click-to-continue</Select.Option>
              <Select.Option value="radius">radius</Select.Option>
              <Select.Option value="credentials">credentials</Select.Option>
              <Select.Option value="uam">uam</Select.Option>
            </Select>
          </Form.Item>
        </Col>
      </Row>

      {authMode !== 'none' && <>
        {/* 通用字段 */}
        <Row gutter={24}>
          <Col span={12}>
            <Form.Item
              name={['captive', 'walled-garden-fqdn']}
              label="walled-garden-fqdn"
            >
              <Select
                mode="tags"
                tokenSeparators={[',']}
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name={['captive', 'walled-garden-ipaddr']}
              label="walled-garden-ipaddr"
            >
              <Select
                mode="tags"
                tokenSeparators={[',']}
              />
            </Form.Item>
          </Col>
        </Row>
        <Row gutter={24}>
          <Col span={12}>
            <Form.Item
              name={['captive', 'idle-timeout']}
              label="idle-timeout"
              rules={[
                { required: true, message: t('form.required') },
                { type: 'number', min: 0, max: 65535, message: 'idle-timeout must be less than 65535' }
              ]}
            >
              <InputNumber min={0}/>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name={['captive', 'session-timeout']}
              label="session-timeout"
              rules={[{ type: 'number', min: 0, max: 65535, message: 'session-timeout must be less than 65535' }]}
            >
              <InputNumber min={0} />
            </Form.Item>
          </Col>
        </Row>
        {/* credentials 模式 */}
        {authMode === 'credentials' && (
          <Row gutter={24}>
            <Col span={24}>
              <Form.Item
                label="credentials"
                name={['captive', 'credentials']}
                rules={[{ required: true, message: t('form.required') }]}
              >
                <CredentialsUsersForm />
              </Form.Item>
            </Col>
          </Row>
        )}
        {/* uam 模式 */}
        {authMode === 'uam' && (
          <>
            <Row gutter={24}>
              <Col span={12}>
                <Form.Item 
                  name={['captive', 'uam-server']} 
                  label="uam-server" 
                  rules={[{ required: true, message: t('form.required') }]}
                > 
                  <Input /> 
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item 
                  name={['captive', 'uam-secret']} 
                  label="uam-secret" 
                  rules={[{ required: true, message: t('form.required') }]}
                > 
                  <Input /> 
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={24}>
              <Col span={12}>
                <Form.Item 
                  name={['captive', 'uam-port']} 
                  label="uam-port" 
                  rules={[{ required: true, type: 'number', min: 1023, max: 65535, message: t('form.min_max_string', { min: 1023, max: 65535 }) }]}
                > 
                  <InputNumber /> 
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item 
                  name={['captive', 'nasid']} 
                  label="nasid" 
                  rules={[{ required: true, message: t('form.required') }]}
                > 
                  <Input /> 
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={24}>
              <Col span={12}>
                <Form.Item 
                  name={['captive', 'nasmac']} 
                  label="nasmac" 
                > 
                  <Input /> 
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item 
                  name={['captive', 'mac-format']} 
                  label="mac-format" 
                  rules={[{ required: true, message: t('form.required') }]}
                > 
                  <Select options={UAM_MAC_FORMAT_OPTIONS} />
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={24}>
              <Col span={12}>
                <Form.Item 
                  name={['captive', 'ssid']} 
                  label="ssid" 
                > 
                  <Input /> 
                </Form.Item>
              </Col>
            </Row>
          </>
        )}
        {/* radius/uam 模式 */}
        {(authMode === 'radius' || authMode === 'uam') && (
          <>
            <Row gutter={24}>
              <Col span={12}>
                <Form.Item 
                  name={['captive', 'auth-server']} 
                  label="auth-server" 
                  rules={[{ required: true, message: t('form.required') }]}
                > 
                  <Input /> 
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item 
                  name={['captive', 'auth-secret']} 
                  label="auth-secret" 
                  rules={[{ required: true, message: t('form.required') }]}
                > 
                  <Input.Password /> 
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={24}>
              <Col span={12}>
                <Form.Item 
                  name={['captive', 'auth-port']} 
                  label="auth-port" 
                  rules={[
                    { required: true, message: t('form.required') },
                    { type: 'number', min: 1023, max: 65535, message: t('form.min_max_string', { min: 1023, max: 65535 }) }
                  ]}
                > 
                  <InputNumber /> 
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item 
                  name={['captive', 'acct-server']} 
                  label="acct-server" 
                > 
                  <Input /> 
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={24}>
              <Col span={12}>
                <Form.Item 
                  name={['captive', 'acct-secret']} 
                  label="acct-secret" 
                > 
                  <Input.Password /> 
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item 
                  name={['captive', 'acct-port']} 
                  label="acct-port" 
                  rules={[
                    { required: true, message: t('form.required') },
                    { type: 'number', min: 1023, max: 65535, message: t('form.min_max_string', { min: 1023, max: 65535 }) }
                  ]}
                > 
                  <InputNumber /> 
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={24}>
              <Col span={12}>
                <Form.Item 
                  name={['captive', 'acct-interval']} 
                  label="acct-interval" 
                  rules={[
                    { required: true, message: t('form.required') },
                    { type: 'number', min: 1, max: 65535, message: t('form.min_max_string', { min: 1, max: 65535 }) }
                  ]}
                > 
                  <InputNumber /> 
                </Form.Item>
              </Col>
            </Row>
          </>
        )}
        {/* webroot 配置部分 */}
        { authMode !== 'uam' && (
          <Row gutter={24}>
            <Col span={24}>
              <ProfileSelect
                label="web-root"
                formName={['captive', 'web-root']}
                switchEnabled={webRootEnabled}
                onSwitchChange={setWebRootEnabled}
                type={3}
                siteId={siteId}
                modalWidth={1300}
                edit={resource?.captive?.['web-root']}
              />
            </Col>
          </Row>
        )}
      </>}
    </>
  );
};

export default CaptivePortalForm; 