import React,{useEffect} from 'react';
import {Col,Row} from 'antd';
import { EVENT_TYPES_OPTIONS } from './metricsConstants';
import MultiSelectField from '@/modules-smb/Wireless/components/FormFields/MultiSelectField';
import ToggleField from '@/modules-smb/Wireless/components/FormFields/ToggleField';
import useFastField from '@/modules-smb/hooks/useFastField';
import {useFormikContext} from 'formik';
import { getSubSectionDefaults } from './metricsConstants';
import { useTranslation } from 'react-i18next';
const Realtime = () => {
  const { t } = useTranslation();
  const { value: realtimeEnabled } = useFastField({ name: 'configuration.metrics.realtime.enabled' });
  const { values, setFieldValue, errors } = useFormikContext<any>();
        useEffect(() => {
           if (realtimeEnabled) {
            const defaultRealtimeConfig = getSubSectionDefaults(t,'realtime');
             setFieldValue('configuration.metrics.realtime', {
           ...defaultRealtimeConfig,
           enabled: true,
           });
           }else {
         // 可选：关闭时清除 realtime 字段
         setFieldValue('configuration.metrics.realtime', undefined);
           }
         }, [realtimeEnabled]);
  return(
  <Row gutter={[20, 20]} style={{ marginBottom: 0, marginTop: 8, width: '100%' }}>
      <Col>
        <ToggleField
          name="configuration.metrics.realtime.enabled"
          label="Realtime"
        />
        {realtimeEnabled && (
          <>
          <MultiSelectField
          name="configuration.metrics.realtime.types"
          label="Types"
          definitionKey="metrics.realtime.types"
          options={EVENT_TYPES_OPTIONS}
          // isDisabled={!editing}
          canSelectAll
          hasVirtualAll
          w={280}
        />
          </>
        )}
        
      </Col>
    </Row>
  );

};

export default React.memo(Realtime);
