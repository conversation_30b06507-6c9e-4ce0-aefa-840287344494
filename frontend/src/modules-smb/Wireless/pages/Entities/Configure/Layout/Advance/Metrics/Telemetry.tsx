import React,{useEffect} from 'react';
import {Col,Row} from 'antd';
import { EVENT_TYPES_OPTIONS } from './metricsConstants';
import MultiSelectField from '@/modules-smb/Wireless/components/FormFields/MultiSelectField';
import NumberField from '@/modules-smb/Wireless/components/FormFields/NumberField';
import ToggleField from '@/modules-smb/Wireless/components/FormFields/ToggleField';
import useFastField from '@/modules-smb/hooks/useFastField';
import {useFormikContext} from 'formik';
import { getSubSectionDefaults } from './metricsConstants';
import { useTranslation } from 'react-i18next';
const Telemetry = () => {
  const { t } = useTranslation();
  const { value: telemetryEnabled } = useFastField({ name: 'configuration.metrics.telemetry.enabled' });
  const { values, setFieldValue, errors } = useFormikContext<any>();
    console.log('telemetry values',values);
    useEffect(() => {
       if (telemetryEnabled) {
        const defaultTelemetryConfig = getSubSectionDefaults(t,'telemetry');
         setFieldValue('configuration.metrics.telemetry', {
       ...defaultTelemetryConfig,
       enabled: true,
       });
       }else {
     // 可选：关闭时清除 telemetry 字段
     setFieldValue('configuration.metrics.telemetry', undefined);
       }
     }, [telemetryEnabled]);
  return(
<Row gutter={[20, 20]} style={{ marginBottom: 0, marginTop: 8, width: '100%' }}>
      <Col>
        <ToggleField
          name="configuration.metrics.telemetry.enabled"
          label="Telemetry"
        />
        {telemetryEnabled && (
          <>
          <NumberField
          name="configuration.metrics.telemetry.interval"
          label="Interval"
          definitionKey="metrics.telemetry.interval"
          unit="s"
          // isDisabled={!editing}
          isRequired
          w={140}
        />
        <MultiSelectField
          name="configuration.metrics.telemetry.types"
          label="Types"
          definitionKey="metrics.telemetry.types"
          options={EVENT_TYPES_OPTIONS}
          // isDisabled={!editing}
          canSelectAll
          hasVirtualAll
          w={280}
        />
          </>
        )}
      </Col>
    </Row>
  );
  
  };

export default React.memo(Telemetry);
