import React, { useState, useEffect } from "react";
import { <PERSON><PERSON>, message, Alert } from "antd";
import ConfigModal from "@/modules-smb/Wireless/pages/Entities/RRMOptimize/components/ConfigModal";
import ConfirmModal from "@/modules-smb/Wireless/pages/Entities/RRMOptimize/components/ConfirmModal";
import SchedulerModal from "@/modules-smb/Wireless/pages/Entities/RRMOptimize/components/SchedulerModal";
import FailedDeviceModal from "@/modules-smb/Wireless/pages/Entities/RRMOptimize/components/FailedDeviceModal";
import HistoryTable from "@/modules-smb/Wireless/pages/Entities/RRMOptimize/components/HistoryTable";
import { OptimizeHistoryItem, RRMConfig, RRMApiRequest } from "@/modules-smb/Wireless/pages/Entities/RRMOptimize/types";
import { useGetVenue, useUpdateVenue } from '@/modules-smb/hooks/Network/Venues';
import type { SchedulerConfig } from './types';
import '@/modules-smb/Wireless/pages/Entities/RRMOptimize/styles/index.scss';
import { fetchRRMTaskRecords, runOptimizeNow } from '@/modules-smb/Wireless/apis/wireless_rrm_api';

const PAGE_SIZE = 10;


// 根据url获取当前页面的 venueId
const getVenueIdFromHash = () => {
  if (window.location.hash) {
    return window.location.hash.replace('#', '');
  }
  return '';
};


/**
 * 解析 rrm 字符串为对象
 */
function parseRrm(rrmStr: string): { algorithms: any[]; schedule: string; vendor: string } {
  try {
    return JSON.parse(rrmStr);
  } catch {
    return { algorithms: [], schedule: '', vendor: '' };
  }
}


/**
 * 解析 algorithms 数组为 RRMConfig
 */
function parseAlgorithms(algorithms: any[]): RRMConfig {
  const config: Partial<RRMConfig> = {};
  algorithms.forEach(item => {
    if (item.name === 'OptimizeTxPower') {
      const params = Object.fromEntries(item.parameters.split(',').map((kv: string) => kv.split('=')));
      config.txPowerMode = params.mode?.replace(/"/g, '') || 'measure_ap_ap';
      config.setDifferentTxPowerPerAp = params.setDifferentTxPowerPerAp === 'true';
      config.targetMcs = params.targetMcs ? Number(params.targetMcs) : undefined;
      config.coverageThreshold = params.coverageThreshold ? Number(params.coverageThreshold) : undefined;
      config.nthSmallestRssi = params.nthSmallestRssi ? Number(params.nthSmallestRssi) : undefined;
    }
    if (item.name === 'OptimizeChannel') {
      const params = Object.fromEntries(item.parameters.split(',').map((kv: string) => kv.split('=')));
      config.channelMode = params.mode?.replace(/"/g, '') || 'least_used';
      config.setDifferentChannelPerAp = params.setDifferentChannelPerAp === 'true';
    }
  });
  return {
    channelMode: config.channelMode || 'least_used',
    setDifferentChannelPerAp: config.setDifferentChannelPerAp ?? false,

    txPowerMode: config.txPowerMode || 'measure_ap_ap',
    setDifferentTxPowerPerAp: config.setDifferentTxPowerPerAp ?? false,
    coverageThreshold: config.coverageThreshold,
    nthSmallestRssi: config.nthSmallestRssi,
    targetMcs: config.targetMcs,
  };
}

/**
 * 组装 RRMConfig 为 algorithms 数组
 */
function buildAlgorithms(rrmConfig: RRMConfig) {
  // 组装 OptimizeChannel 参数
  let channelParams = `mode=${rrmConfig.channelMode}`;
  if (rrmConfig.channelMode === 'random') {
    channelParams += `,setDifferentChannelPerAp=${rrmConfig.setDifferentChannelPerAp}`;
  }

  // 组装 OptimizeTxPower 参数
  let txPowerParams = `mode=${rrmConfig.txPowerMode}`;
  if (rrmConfig.txPowerMode === 'random') {
    txPowerParams += `,setDifferentTxPowerPerAp=${rrmConfig.setDifferentTxPowerPerAp}`;
  } else if (rrmConfig.txPowerMode === 'measure_ap_client') {
    txPowerParams += `,targetMcs=${rrmConfig.targetMcs}`;
  } else if (rrmConfig.txPowerMode === 'measure_ap_ap') {
    txPowerParams += `,coverageThreshold=${rrmConfig.coverageThreshold},nthSmallestRssi=${rrmConfig.nthSmallestRssi}`;
  } else if (rrmConfig.txPowerMode === 'location_optimal') {
    // location_optimal模式可能需要特定参数，目前只传mode
    // 如果需要其他参数，可以在这里添加
  }

  return [
    {
      name: 'OptimizeChannel',
      parameters: channelParams
    },
    {
      name: 'OptimizeTxPower',
      parameters: txPowerParams
    }
  ];
}

/**
 *  解析 cron 表达式为 SchedulerConfig（支持星期几字段）
 */
function parseSchedule(cron: string): SchedulerConfig {
  if (!cron || cron.trim() === '' || cron === '0 0 0 * * *') {
    // cron为空，开关应为关闭
    return {
      enabled: false,
      executeTime: '00:00',
      days: ['1', '2', '3', '4', '5', '6', '7'],
    };
  }
  const parts = cron.split(' ');
  const minute = parts[1] || '00';
  const hour = parts[2] || '00';
  const days = parts[5] && parts[5] !== '*' ? parts[5].split(',') : ['1', '2', '3', '4', '5', '6', '7'];
  return {
    enabled: true,
    executeTime: `${hour.padStart(2, '0')}:${minute.padStart(2, '0')}`,
    days,
  };
}

/**
 * 组装 SchedulerConfig 为 cron 表达式（支持星期几字段）
 */
function buildSchedule(scheduler: SchedulerConfig): string {
  // 当 Scheduled Optimization 为 false 时，返回空字符串
  if (!scheduler.enabled) return '';
  const [hour, minute] = scheduler.executeTime.split(':');
  // cron格式: 秒 分 时 日 月 星期
  // 星期：1=Monday, 7=Sunday
  const days = scheduler.days && scheduler.days.length === 7 ? '*' : scheduler.days.join(',');
  return `0 ${minute || '0'} ${hour || '0'} * * ${days}`;
}


const RRMOptimize = () => {

  // 弹窗控制
  const [configVisible, setConfigVisible] = useState(false);
  const [confirmVisible, setConfirmVisible] = useState(false);
  const [schedulerVisible, setSchedulerVisible] = useState(false);
  const [failedModal, setFailedModal] = useState<{ visible: boolean; historyId: string | null }>({ visible: false, historyId: null });

  // 历史数据
  const [history, setHistory] = useState<OptimizeHistoryItem[]>([]);
  const [total, setTotal] = useState(0);
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(PAGE_SIZE);
  const [loading, setLoading] = useState(false);
  // 排序状态
  const [sortField, setSortField] = useState<string>('create_time');
  const [sortOrder, setSortOrder] = useState<'ascend' | 'descend' | null>('descend');

  // 使用state管理venueId，支持动态更新
  const [venueId, setVenueId] = useState(() => getVenueIdFromHash());


  // 获取 venue 配置
  // let venueData = useGetVenue({ id: venueId });
  const { data: venue, refetch: refetchVenue, isLoading: venueLoading } = useGetVenue({ id: venueId });
  const updateVenue = useUpdateVenue({ id: venueId });

  // 当venueId变化时，重新获取所有相关数据
  useEffect(() => {
    setVenueId(getVenueIdFromHash())

    if (venueId) {

      console.log(`Loading all data for venueId: ${venueId}`);

      // 1. 重新获取venue配置（包含RRM配置）
      refetchVenue();

      // 2. 重新加载历史数据
      loadHistory(1, pageSize);

    }
  }, [getVenueIdFromHash(), refetchVenue, pageSize]);



  // 解析 rrm 字符串为对象
  const rrmObj = React.useMemo(() => {
    if (!venue?.deviceRules?.rrm || venue.deviceRules.rrm === 'inherit' || venue.deviceRules.rrm === 'off') return { algorithms: [], schedule: '', vendor: '' };
    return parseRrm(venue.deviceRules.rrm);
  }, [venue]);

  // 解析为 RRMConfig/SchedulerConfig
  const rrmConfig = React.useMemo(() => parseAlgorithms(rrmObj.algorithms || []), [rrmObj]);
  const schedulerConfig = React.useMemo(() => parseSchedule(rrmObj.schedule || ''), [rrmObj]);

  // 受控state
  const [rrmForm, setRrmForm] = useState<RRMConfig>(rrmConfig);
  const [schedulerForm, setSchedulerForm] = useState<SchedulerConfig>(schedulerConfig);

  // 拆分算法配置和定时配置
  const [algorithms, setAlgorithms] = useState(rrmObj.algorithms || []);
  const [schedule, setSchedule] = useState(rrmObj.schedule || '');

  // 额外可扩展 vendor
  const [vendor, setVendor] = useState(rrmObj.vendor || 'Meta');

  // 监听 venue 变化，重置 state
  useEffect(() => {
    setAlgorithms(rrmObj.algorithms || []);
    setSchedule(rrmObj.schedule || '');
    setVendor(rrmObj.vendor || 'Meta');
    setRrmForm(rrmConfig);
    setSchedulerForm(schedulerConfig);
  }, [rrmObj, rrmConfig, schedulerConfig]);

  // // 算法配置变更
  // const handleConfigChange = (form: RRMConfig) => setRrmForm(form);
  // // 定时配置变更
  // const handleSchedulerChange = (form: SchedulerConfig) => setSchedulerForm(form);

  // Config/Scheduler弹窗apply时直接传递最新表单值
  const handleConfigApply = async (newConfig: RRMConfig) => {
    const algorithms = buildAlgorithms(newConfig);
    const schedule = buildSchedule(schedulerForm); // 用当前定时配置
    const rrm = schedulerForm.enabled
      ? { algorithms, schedule, vendor }
      : { algorithms, vendor }; // 空对象
    try {
      await updateVenue.mutateAsync({ params: { deviceRules: { ...venue.deviceRules, rrm } } });
      refetchVenue();
      message.success('save successfully');
      setConfigVisible(false);
      return true;
    } catch (e) {
      message.error('Failed to save the configuration');
      return false;
      // 不关闭弹窗
    }
  };
  const handleSchedulerApply = async (newScheduler: SchedulerConfig) => {
    const algorithms = buildAlgorithms(rrmForm); // 用当前算法配置
    const schedule = buildSchedule(newScheduler);
    // 当 Scheduled Optimization 为 false 时，保存没有 schedule 对象
    const rrm = newScheduler.enabled
      ? { algorithms, schedule, vendor }
      : { algorithms, vendor }; // 空对象
    // const rrm = { algorithms, schedule, vendor }
    try {
      // 新增校验：在开启定时任务时,必须选择周几
      if (newScheduler.enabled && (!newScheduler.days || newScheduler.days.length === 0)) {
        message.error('Please select at least one week of execution!');
        return false; // 阻止弹窗关闭
      }
      await updateVenue.mutateAsync({ params: { deviceRules: { ...venue.deviceRules, rrm } } });
      refetchVenue();
      message.success('save successfully');
      setSchedulerVisible(false); // 只在成功时关闭弹窗
      return true;
    } catch (e) {
      message.error('Failed to save the configuration');
      return false;
      // 不关闭弹窗
    }
  };

  // 处理排序变化
  const handleSortChange = (sorter: { field: string; order: 'ascend' | 'descend' | null }) => {
    setSortField(sorter.field);
    setSortOrder(sorter.order);
    // 重置到第一页
    loadHistory(1, pageSize, sorter.field, sorter.order);
  };

  // 刷新历史
  const loadHistory = async (
    p = page,
    ps = pageSize,
    sortFieldParam = sortField,
    sortOrderParam = sortOrder
  ) => {
    setLoading(true);
    try {
      const res = await fetchRRMTaskRecords({
        siteId: venueId,
        sortBy: sortFieldParam || 'create_time',
        sortType: sortOrderParam === 'ascend' ? 'asc' : 'desc',
        pageNum: p,
        pageSize: ps,
      });
      // 直接使用后端字段
      setHistory(res.info || []);
      setTotal(res.total || 0);
      // 更新分页状态
      setPage(p);
      setPageSize(ps);
    } catch {
      message.error("Failed to fetch optimization history");
    } finally {
      setLoading(false);
    }
  };


  // 立即优化
  const handleOptimizeNow = async () => {
    setConfirmVisible(false);

    // 显示加载状态
    const loadingMessage = message.loading('The RRM optimization task is being initiated...', 0);

    try {
      // 构造API请求参数
      const algorithmsParam = buildAlgorithms(rrmConfig)

      const apiParams = {
        parameter: algorithmsParam,
        siteId: venueId
      };
      // console.log('API params:', JSON.stringify(apiParams, null, 2));

      // // 验证参数格式是否符合API要求
      // console.log('Expected format example:');
      // console.log(JSON.stringify({
      //   "parameter": [
      //     {
      //       "algorithm": "OptimizeChannel",
      //       "args": "mode=random,setDifferentChannelPerAp=true"
      //     },
      //     {
      //       "algorithm": "OptimizeTxPower",
      //       "args": "mode=measure_ap_ap,coverageThreshold=100,nthSmallestRssi=50"
      //     }
      //   ],
      //   "siteId": "1101"
      // }, null, 2));

      // 创建带超时的Promise
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => {
          reject(new Error('Request timeout'));
        }, 300000); // 5分钟超时
      });

      // 执行API调用，带超时控制
      const result = await Promise.race([
        runOptimizeNow(apiParams),
        timeoutPromise
      ]);

      console.log('Optimization result:', result);
      loadingMessage(); // 关闭加载提示

      message.success("Successful launch!");
      loadHistory(1, pageSize);

    } catch (error: any) {
      loadingMessage(); // 关闭加载提示
      console.error("Failed to start optimization:", error);
      message.error('Failed to start optimization');

      // 即使失败也刷新历史记录
      setTimeout(() => {
        loadHistory(1, pageSize);
      }, 2000);
    }
  };

  return (
    <div className="rrm-optimize-root">
      <div className="rrm-optimize-header">
        <div>
          <h2>WLAN Optimization</h2>
        </div>
        <a
          href="#"
          onClick={e => { e.preventDefault(); setConfigVisible(true); }}
        >
          <span>&#9776;</span> Optimization Config
        </a>
      </div>
      <div className="rrm-optimize-desc">
        With the WLAN optimization service, the organization will determine the optimum operation channels and power concluded from the scanning, considering the traffic, deployment size, and client factors.
      </div>
      <div className="rrm-optimize-alert">
        <Alert
          type="info"
          showIcon
          closable
          message={
            <span>
              <b>Note:</b> The connection to internet will be lost for several minutes during the scanning and optimization. Please select a spare time of network to start scanning.
            </span>
          }
        />
      </div>
      <div className="rrm-optimize-actions">
        <Button
          type="primary"
          onClick={() => setConfirmVisible(true)}
        >
          Optimization Now
        </Button>
        <Button
          type="default"
          onClick={() => setSchedulerVisible(true)}
        >
          Optimization Scheduler
        </Button>
      </div>
      <hr />
      <div className="rrm-optimize-history">
        <h3>Optimization History</h3>
        <HistoryTable
          data={history}
          total={total}
          page={page}
          pageSize={pageSize}
          onPageChange={(p: number, ps: number) => loadHistory(p, ps)}
          onSortChange={handleSortChange}
          onShowFailed={(historyId: string) => setFailedModal({ visible: true, historyId })}
        />
      </div>
      <ConfigModal
        visible={configVisible}
        onClose={() => setConfigVisible(false)}
        config={rrmForm}
        // onChange={handleConfigChange}
        onApply={handleConfigApply}
      />
      <SchedulerModal
        visible={schedulerVisible}
        onClose={() => setSchedulerVisible(false)}
        config={schedulerForm}
        // onChange={handleSchedulerChange}
        onApply={handleSchedulerApply}
      />
      <ConfirmModal
        visible={confirmVisible}
        onOk={handleOptimizeNow}
        onCancel={() => setConfirmVisible(false)}
      />

      <FailedDeviceModal
        visible={failedModal.visible}
        historyId={failedModal.historyId || ''}
        onClose={() => setFailedModal({ visible: false, historyId: null })}
      />
    </div>
  );
};

export default RRMOptimize;