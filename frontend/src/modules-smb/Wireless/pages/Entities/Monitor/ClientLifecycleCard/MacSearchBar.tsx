import React, { useCallback, useState } from 'react';
import { Select, Typography, Space } from 'antd';
import { useTranslation } from 'react-i18next';

const { Option } = Select;
const { Text } = Typography;

const MacSearchBar: React.FC<{ macs?: string[]; setMac: React.Dispatch<React.SetStateAction<string | undefined>> }> = ({
  macs,
  setMac,
}) => {
  const { t } = useTranslation();
  const [inputValue, setInputValue] = useState('');

  // 过滤选项逻辑
  const filterOption = useCallback(
    (input: string, option: Option) => {
      if (!input) return true;
      return option.value.includes(input.replace('*', ''));
    },
    [macs]
  );

  // 选择MAC地址的回调
  const onSelect = (value: string) => {
    setMac(value);
    setInputValue(value);
  };

  // 输入值变化的回调
  const onSearch = (value: string) => {
    setInputValue(value);
  };

  // 聚焦时清空输入框
  const onFocus = useCallback(() => {
    setInputValue('');
  }, []);

  // 自定义无结果提示
  const renderEmpty = () => (
    <Space direction="vertical" align="center" style={{ padding: 16, textAlign: 'center' }}>
      <Text>{t('common.no_clients_found')}</Text>
    </Space>
  );

  // 处理所有值变化，包括清除按钮
  const onChange = (value: string | undefined) => {
    if (value === undefined) { // 清除按钮被点击
      setInputValue('');
    }
  };

  return (
    <Select
      showSearch
      style={{ width: 280, borderRadius: 36 }}
      placeholder={t("common.search")}
      value={inputValue || undefined} // 确保空字符串显示为placeholder
      onSelect={onSelect}
      onSearch={onSearch}
      onFocus={onFocus}
      onChange={onChange} // 添加这个事件处理
      filterOption={filterOption}
      notFoundContent={renderEmpty()}
      allowClear
      bordered
      dropdownMatchSelectWidth
      // 自定义样式
      getPopupContainer={(trigger) => trigger.parentElement}
      dropdownStyle={{ borderRadius: 24 }}
      optionFilterProp="value"
    >
      {macs?.map((mac) => (
        <Option key={mac} value={mac}>
          {mac}
        </Option>
      ))}
    </Select>
  );
};

export default MacSearchBar;