import React from 'react';
import { Card, Typography, Tooltip, Button, Divider } from 'antd';
import { useTranslation } from 'react-i18next';
import { useVenueMonitoring } from '../VenueMonitoringContext';
import threeLineIcon from '@/modules-smb/Wireless/assets/Monitor/Logo_Three_Line.png';
import iconDetails from '@/modules-smb/Wireless/assets/Monitor/Logo_Detail.png';
const { Title, Text } = Typography;

const DeviceStatusCard = () => {
  const { t } = useTranslation();
  const { dashboard, handleDashboardModalOpen } = useVenueMonitoring();
 
  return (
    <Card
      style={{
        width: '100%',
        height: '100%',
        background: 'linear-gradient(180deg, #F6F7FF 0%, #ECECFF 100%)',
        borderRadius: '8px',
        padding: 0,
      }}
      bodyStyle={{ padding: 0 }}
    >
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', padding: '12px 16px' }}>
        <Title level={4} style={{ margin: 0, paddingLeft: 0 }}>
          {t('common.status')}
          <Tooltip title={t('analytics.total_devices_explanation', {
            connectedCount: dashboard?.connectedDevices ?? 0,
            disconnectedCount: dashboard?.disconnectedDevices ?? 0,
          })}>
             <img src={iconDetails} style={{marginLeft: 2,width: 12,height: 12}}/>
          </Tooltip>
        </Title>
        <Tooltip title={t('common.view_details')}>
          <Button
            aria-label={t('common.view_details')}
            icon={<img src={threeLineIcon} style={{ height: 20, width: 20 }} />}
            onClick={() =>
              handleDashboardModalOpen({
                prioritizedColumns: ['connected'],
                sortBy: [
                  {
                    id: 'connected',
                    desc: true,
                  },
                ],
              })
            }
            style={{
              border: 'none',
              outline: 'none',
              background: 'transparent'
            }}
          />
        </Tooltip>
      </div>
      <Divider style={{ margin: '0', borderColor: '#ccc' }} />
      <div style={{
        display: 'flex',
        justifyContent: 'space-around',
        alignItems: 'center',
        padding: '24px 16px',
        marginTop: 14,
        height: 'calc(100% - 64px)'
      }}>
        <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', marginRight: 32 }}>
          <Text style={{ fontSize: '24px', fontWeight: 'bold' }}>{dashboard?.connectedDevices ?? 0}</Text>
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <div style={{ width: 10, height: 10, borderRadius: '50%', background: '#52c41a', marginRight: 4 }} />
            <Text style={{ fontSize: '16px' }}>{t('analytics.connected')}</Text>
          </div>
        </div>
        <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
          <Text style={{ fontSize: '24px', fontWeight: 'bold' }}>{dashboard?.disconnectedDevices ?? 0}</Text>
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <div style={{ width: 10, height: 10, borderRadius: '50%', background: '#ccc', marginRight: 4 }} />
            <Text style={{ fontSize: '16px', color: '#999' }}>{t('analytics.disconnected')}</Text>
          </div>
        </div>
      </div>
    </Card>
  );
};

export default DeviceStatusCard;