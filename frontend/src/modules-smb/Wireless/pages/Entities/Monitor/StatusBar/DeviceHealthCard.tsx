import React from 'react';
import { QuestionCircleOutlined } from '@ant-design/icons';
import { Card, Typography, Tooltip, Button, Divider } from 'antd';
import healthCurveIcon from '@/modules-smb/Wireless/assets/Monitor/Logo_Health.png';
import threeLineIcon from '@/modules-smb/Wireless/assets/Monitor/Logo_Three_Line.png';
import iconDetails from '@/modules-smb/Wireless/assets/Monitor/Logo_Detail.png';
import { useTranslation } from 'react-i18next';
import { useVenueMonitoring } from '../VenueMonitoringContext';

const { Title, Text } = Typography;

const DeviceHealthCard = () => {
  const { t } = useTranslation();
  const { dashboard, handleDashboardModalOpen } = useVenueMonitoring();
  return (
    <Card
      style={{
        width: '100%',
        height: '100%',
        background: 'linear-gradient(180deg, #F2F9FE 0%, #E6F4FE 100%)',
        borderRadius: '8px',
        padding: 0,
      }}
      bodyStyle={{ padding: 0 }}
    >
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', padding: '12px 16px' }}>
        <Title level={4} style={{ margin: 0, paddingLeft: 0 }}>
          {t('analytics.average_health')}
          <Tooltip title={t('analytics.average_health_explanation')}>
             <img src={iconDetails} style={{marginLeft: 2,width: 12,height: 12}}/>
          </Tooltip>
        </Title>
        <Tooltip title={t('common.view_details')}>
          <Button
            aria-label={t('common.view_details')}
            icon={<img src={threeLineIcon} style={{ height: 20, width: 20 }} />}
            onClick={() =>
              handleDashboardModalOpen({
                prioritizedColumns: ['lastHealth', 'health'],
                sortBy: [
                  {
                    id: 'health',
                    desc: false,
                  },
                ],
              })
            }
            style={{
              border: 'none',
              outline: 'none',
              background: 'transparent'
            }}
          />
        </Tooltip>
      </div>
      <Divider style={{ margin: '0', borderColor: '#ccc' }} />
      <div style={{
        display: 'flex',
        justifyContent: 'space-around',
        marginTop: 14,
        padding: '0 16px',
        alignItems: 'center',
        height: 'calc(100% - 64px)'
      }}>
        <Title level={3} style={{ display: 'flex', alignItems: 'center', width: '100%', margin: 0,marginLeft:'16px' }}>
          <Text style={{ fontSize: '24px', fontWeight: 'bold' }}>{`${dashboard?.avgHealth ?? 0}%`}</Text>
          <img
            src={healthCurveIcon}
            alt="Health Curve Icon"
           style={{ marginLeft: 'auto', marginBottom: 0, width: '112px', height: 'auto' }}
          />
        </Title>
      </div>
    </Card>
  );
};

export default DeviceHealthCard;