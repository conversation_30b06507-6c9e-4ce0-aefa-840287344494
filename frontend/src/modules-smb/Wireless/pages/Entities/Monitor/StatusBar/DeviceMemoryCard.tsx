import React from 'react';
import { Card, Typography, Toolt<PERSON>, Button, Divider } from 'antd';
import { useTranslation } from 'react-i18next';
import { useVenueMonitoring } from '../VenueMonitoringContext';
import threeLineIcon from '@/modules-smb/Wireless/assets/Monitor/Logo_Three_Line.png';
import { CircularProgressEcharts } from '@/modules-smb/Wireless/components/Echart/echarts_common';
import iconDetails from '@/modules-smb/Wireless/assets/Monitor/Logo_Detail.png';
const { Title, Text } = Typography;

const DeviceMemoryCard = () => {
  const { t } = useTranslation();
  const { dashboard, handleDashboardModalOpen } = useVenueMonitoring();
 
  return (
    <Card
      style={{
       width: '100%',
       height: '100%',
        background: 'linear-gradient(180deg, #F5FEF2 0%, #E6FEEE 100%)',
        borderRadius: '8px',
        padding: 0,
      }}
      bodyStyle={{ padding: 0 }}
    >
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', padding: '12px 16px' }}>
        <Title level={4} style={{ margin: 0, paddingLeft: 0 }}>
          {t('analytics.average_memory')}
          <Tooltip title={t('analytics.average_memory_explanation')}>
              <img src={iconDetails} style={{marginLeft: 2,width: 12,height: 12}}/>
          </Tooltip>
        </Title>
        <Tooltip title={t('common.view_details')}>
          <Button
            aria-label={t('common.view_details')}
            icon={<img src={threeLineIcon} style={{ height: 20, width: 20 }} />}
            onClick={() =>
              handleDashboardModalOpen({
                prioritizedColumns: ['lastPing','memory'],
                sortBy: [
                  {
                    id:'memory',
                    desc: true,
                  },
                ],
              })
            }
            style={{
              border: 'none',
              outline: 'none',
              background: 'transparent'
            }}
          />
        </Tooltip>
      </div>
      <Divider style={{ margin: '0', borderColor: '#ccc' }} />
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        marginTop: 14,
        padding: '0 16px',
        alignItems: 'center',
        height: 'calc(100% - 64px)'
      }}>
          <Title level={3} style={{ display: 'flex', alignItems: 'center', width: '100%', margin: 0,marginLeft:'16px' }}>
          <Text style={{ fontSize: '24px', fontWeight: 'bold' }}>{`${dashboard?.avgMemoryUsed ?? 0}%`}</Text>
          <CircularProgressEcharts 
            value={dashboard?.avgMemoryUsed?? 0} 
          />
        </Title>
      </div>
    </Card>
  );
};

export default DeviceMemoryCard;