; import React from 'react';
import { Card, Typography, Toolt<PERSON>, Button, Divider } from 'antd';
import { useTranslation } from 'react-i18next';
import { useVenueMonitoring } from '../VenueMonitoringContext';
import threeLineIcon from '@/modules-smb/Wireless/assets/Monitor/Logo_Three_Line.png';
import associationsIcon from '@/modules-smb/Wireless/assets/Monitor/Logo_Associations.png';
import iconDetails from '@/modules-smb/Wireless/assets/Monitor/Logo_Detail.png';
const { Title, Text } = Typography;

const TotalAssociationsCard = () => {
  const { t } = useTranslation();
  const { dashboard, handleDashboardModalOpen } = useVenueMonitoring();

  return (
    <Card
      style={{
        width: '100%',
        height: '100%',
        background: 'linear-gradient(180deg, #F2F9FE 0%, #E6F4FE 100%)',
        borderRadius: '8px',
        padding: 0,
      }}
      bodyStyle={{ padding: 0 }}
    >
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', padding: '12px 16px' }}>
        <Title level={4} style={{ margin: 0, paddingLeft: 0 }}>
          {t('analytics.associations')}
          <Tooltip title={t('analytics.associations_explanation')}>
              <img src={iconDetails} style={{marginLeft: 2,width: 12,height: 12}}/>
          </Tooltip>
        </Title>
        <Tooltip title={t('common.view_details')}>
          <Button
            aria-label={t('common.view_details')}
            icon={<img src={threeLineIcon} style={{ height: 20, width: 20 }} />}
            onClick={() =>
              handleDashboardModalOpen({
                prioritizedColumns: ['2g', '5g', '6g'],
                sortBy: [
                  {
                    id: '2g',
                    desc: true,
                  },
                  {
                    id: '5g',
                    desc: true,
                  },
                  {
                    id: '6g',
                    desc: true,
                  },
                ],
              })
            }
            style={{
              border: 'none',
              outline: 'none',
              background: 'transparent'
            }}
          />
        </Tooltip>
      </div>
      <Divider style={{ margin: '0', borderColor: '#ccc' }} />
      <div style={{
        display: 'flex',
        justifyContent: 'space-around',
        padding: '16px',
        height: 'calc(100% - 64px)',
        marginTop: 32
      }}>
        {[
          { label: '2G', value: dashboard?.twoGAssociations ?? 0 },

          { label: '5G', value: dashboard?.fiveGAssociations ?? 0 },
          { label: '6G', value: dashboard?.sixGAssociations ?? 0 }
        ].map(({ label, value }) => (
          <div key={label} style={{
            display: 'flex',
            width: '33.33%',
            justifyContent: 'center'
          }}>
            <img
              src={associationsIcon}
              alt={`${label} Associations`}
              style={{ height: 24, width: 24, marginRight: 8, marginTop: 24 }}
            />
            <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'flex-start' }}>
              <Text style={{ fontSize: '24px', fontWeight: '900' }}>{value}</Text>
              <Text style={{ fontSize: '14px', color: '#777', marginTop: 4 }}>{label}</Text>
            </div>
          </div>
        ))}
      </div>
    </Card>
  );
};

export default TotalAssociationsCard;