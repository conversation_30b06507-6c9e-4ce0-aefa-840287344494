import React, { useEffect } from 'react';
import { VenueMonitoringProvider } from './VenueMonitoringContext';
import BusiestVenueDevicesCard from './BusiestDevicesCard';
import ClientLifecycleCard from './ClientLifecycleCard';
import VenueMonitoringTree from './MonitoringTree';
import VenueStatusBar from './StatusBar';
import { useLocation } from "react-router-dom";
import useGetEntityFavorite from "@/modules-smb/Wireless/hooks/useGetEntityFavorite";
type Props = {
  venueId: string;
};

const VenueMonitoringTab = ({ venueId }: Props) => {
  const { defaultValue } = useGetEntityFavorite();
  const location = useLocation();
  // 从URL的hash中提取venueId
  let hashVenueId: string | null = null;
  const hash = window.location.hash.replace('#', '');
  if (hash && /^\d+$/.test(hash)) {
    hashVenueId = hash;
  }
  const finalVenueId = venueId || hashVenueId || defaultValue;

  useEffect(() => {
    if (location.state?.scrollToClientLifecycle) {
      const el = document.getElementById('client-lifecycle-card');
      if (el) {
        el.scrollIntoView({ behavior: 'smooth' });
      }
    }
  }, [location]);
  return (
    <VenueMonitoringProvider venueId={finalVenueId}>
      <div style={containerStyle}>
        <VenueStatusBar />
        <div style={{
          display: 'grid',
          gridTemplateColumns: `
            calc((100% - 24px) * 3.05 / 4.27)
            calc((100% - 24px) * 1.22 / 4.27)
          `,
          gap: '32px',
          width: '100%',
          minWidth: '800px'
        }}>
          <div style={{
            ...baseCardStyle,

            width: 'calc(100% + 16px)',
            marginLeft: '-8px'
          }}>
            <BusiestVenueDevicesCard />
          </div>
          <div style={rightCardStyle}>
            <VenueMonitoringTree />
          </div>
        </div>


        <div id="client-lifecycle-card" style={fullWidthCardStyle}>
          <ClientLifecycleCard venueId={finalVenueId} />
        </div>
      </div>
    </VenueMonitoringProvider>
  );
};

// 样式定义
const containerStyle: React.CSSProperties = {
  display: 'flex',
  flexDirection: 'column',
  gap: '24px',
  width: '100%',
  padding: '16px',
  boxSizing: 'border-box',
};

const baseCardStyle: React.CSSProperties = {
  height: '500px',
  background: '#FFFFFF',
  border: '1px solid #E7E7E7',
  borderRadius: '8px',
  overflow: 'hidden',
};


const rightCardStyle: React.CSSProperties = {
  ...baseCardStyle,
  width: '100%',
};


const fullWidthCardStyle: React.CSSProperties = {
  ...baseCardStyle,
  minHeight: '481px',
  height: 'auto',
  width: '100%',
  overflow: 'hidden', // 关键：限制卡片内容不超出边界
  boxSizing: 'border-box',
};
export default VenueMonitoringTab;    