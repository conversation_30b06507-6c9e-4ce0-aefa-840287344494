import React, { use<PERSON><PERSON>back, useMemo, useState, useEffect,forwardRef,useImperativeHandle} from 'react';
import { Space, Typography, Tag,Button,message} from 'antd';
import { useTranslation } from 'react-i18next';
import { WirelessCustomTable } from '@/modules-ampcon/components/custom_table.jsx';
import useControlledTable from '@/modules-smb/hooks/useControlledTable';
import { Column, SortInfo } from '@/modules-smb/models/Table';
import { UseQueryResult } from '@tanstack/react-query';
import { DevicePlatform, DeviceWithStatus, useGetDeviceCount, useGetDevices,useGetDeviceRtty } from '@/modules-smb/hooks/Network/Devices';
import { FirmwareAgeResponse, useGetFirmwareAges } from '@/modules-smb/hooks/Network/Firmware';
import { getRevision } from '@/modules-smb/helpers/stringHelper';
import { DataGridColumn, useDataGrid } from '@/modules-smb/components/DataTables/DataGrid/useDataGrid';
import { useNavigate } from 'react-router-dom';
import Actions from './Actions';
import { WifiScanModal } from '@/modules-smb/components/Modals/WifiScanModal';
import { ConfigureModal } from '@/modules-smb/components/Modals/ConfigureModal';
import { RebootModal } from '@/modules-smb/components/Modals/RebootModal';
import { EventQueueModal } from '@/modules-smb/components/Modals/EventQueueModal';
import { TraceModal } from '@/modules-smb/components/Modals/TraceModal';
import FactoryResetModal from '@/modules-smb/components/Modals/FactoryResetModal';
import { TelemetryModal } from '@/modules-smb/components/Modals/TelemetryModal';
import ProvisioningStatusCell from './ProvisioningStatusCell';

import Icon from "@ant-design/icons";
import denyListSvg from "../../assets/Devices/deny_list.svg?react";
import "../../assets/wireless.scss";
import SearchInput from "./SearchInput";
import ExportDevicesTableButton from "./ExportButton";
import { refreshSvg } from "@/utils/common/iconSvg";
type DevicesListProps = {
  venueId?: string;
};
const DevicesList= forwardRef(({ venueId }: DevicesListProps, ref) => {
  const { t } = useTranslation();
  const [sortInfo, setSortInfo] = useState<SortInfo>([{ id: 'timestamp', sort: 'dsc' }]);
  const [pageInfo, setPageInfo] = useState<PageInfo>({ index: 0, limit: 10 });
  const [platform, setPlatform] = React.useState<DevicePlatform>('ALL');
  const [serialNumber, setSerialNumber] = React.useState<string>('');
  const [isScanModalOpen, setIsScanModalOpen] = React.useState(false);
  const [isConfigureModalOpen, setIsConfigureModalOpen] = React.useState(false);
  const [isRebootModalOpen, setIsRebootModalOpen] = React.useState(false);
  const [isEventQueueModalOpen, setIsEventQueueModalOpen] = React.useState(false);
  const [isFactoryResetModalOpen, setIsFactoryResetModalOpen] = React.useState(false);
  const [isTelemetryModalOpen, setIsTelemetryModalOpen] = React.useState(false);
  const [isTraceModalOpen, setIsTraceModalOpen] = React.useState(false);
  const getCount = useGetDeviceCount({ enabled: true, platform,venueId  });

  const navigate = useNavigate();
    const tableController = useDataGrid({
    tableSettingsId: 'gateway.devices.table',
    defaultOrder: [
      'serialNumber',
      'sanity',
      'memory',
      'load',
      'temperature',
      'firmware',
      'compatible',
      'connected',
      'actions',
    ],
  });

  const fourDigitNumber = (v?: number) => {
  if (v === undefined || typeof v !== 'number') return '-';
  if (v === 0) {
    return '0.00';
  }
  const str = v.toString();
  const fourthChar = str.charAt(3);
  if (fourthChar === '.') return `${str.slice(0, 3)}`;
  return `${str.slice(0, 4)}`;
};

const getDevices = useGetDevices({
    pageInfo: {
      limit: tableController.pageInfo.pageSize,
      index: tableController.pageInfo.pageIndex,
    },
    enabled: true,
    platform,
    venueId
  });

  const hexToRgba = (hex: string, alpha: number) => {
  const r = parseInt(hex.slice(1, 3), 16);
  const g = parseInt(hex.slice(3, 5), 16);
  const b = parseInt(hex.slice(5, 7), 16);
  return `rgba(${r}, ${g}, ${b}, ${alpha})`;
};

  const serialCell = React.useCallback(
      (device: DeviceWithStatus) => {
        return(<a href={`/wireless/devices/${device.serialNumber}#/devices/${device.serialNumber}`} style={{color:'black'}}>
          <pre>{device.serialNumber}</pre>
        </a>
        );
      },
      [],);
  const sanityCell = React.useCallback((device: DeviceWithStatus) => {
      if (!device.connected) return <span>-</span>;
      let colorScheme = '#F53F3F';
      if (device.sanity >= 80) colorScheme = 'rgb(243 237 31)';
      if (device.sanity === 100) colorScheme = '#2BC174';
  
      return (
        <Tag style={{
        border: `1px solid ${colorScheme}`,
        color: colorScheme,
        background: hexToRgba(colorScheme,0.1),
        }}
        >
        {device.sanity}%
        </Tag>
      );
    }, []);

    const statusCell = React.useCallback((device: DeviceWithStatus) => {
      console.log('device',device);
      // if (!device.connected) return <p>-</p>;
      console.log('device--connected',device.connected);
      let colorScheme = 'red';
      let connected = device.connected ? "Connected": "Disconnected";
      
      if (device.connected) {colorScheme = '#2BC174'}
      else colorScheme = '#B3BBC8';
  
      return (
      <Tag style={{
        border: `1px solid ${colorScheme}`,
        color: colorScheme,
        background: hexToRgba(colorScheme,0.1),
      }}
      >
      {connected}
      </Tag> );
    }, []);

    const getAges = useGetFirmwareAges({
        serialNumbers: getDevices.data?.devicesWithStatus.map((device) => device.serialNumber),
      });
    
    const firmwareCell = React.useCallback(
        (device: DeviceWithStatus & { age?: FirmwareAgeResponse }) => {
           if (!device.firmware) return <span>-</span>;
          return(<span>{getRevision(device.firmware)}</span>);
        },
        [getAges],
      );

    const temperatureCell = React.useCallback((device: DeviceWithStatus) => {
        if (!device.connected || device.temperature === 0) return <p>-</p>;
    
        const temperature = device.temperature > 1000 ? device.temperature / 1000 : device.temperature;
    
        return (
          <>
            <span>{fourDigitNumber(temperature)}°C</span>
          </>
        );
      }, []);
    const provCell = React.useCallback(
        (device: DeviceWithStatus) =>
          device.subscriber || device.entity || device.venue ? <ProvisioningStatusCell device={device} /> : '-',
        [],
      );

  const onOpenScan = (serial: string) => {
    setSerialNumber(serial);
     setIsScanModalOpen(true);
  };

  const onCloseScan = () => {
    setIsScanModalOpen(false);
  };

  const onOpenConfigureModal = (serial: string) => {
    setSerialNumber(serial);
    setIsConfigureModalOpen(true);
  };

  const onCloseConfigure = () => {
    setIsConfigureModalOpen(false);
  };
    
  const onOpenRebootModal = (serial: string) => {
    setSerialNumber(serial);
    setIsRebootModalOpen(true);
  };

  const onCloseReboot = () => {
    setIsRebootModalOpen(false);
  };

  const onOpenEventQueue = (serial: string) => {
    setSerialNumber(serial);
    setIsEventQueueModalOpen(true);
  };

  const onCloseEventQueue = () => {
    setIsEventQueueModalOpen(false);
  };

  const onOpenFactoryReset = (serial: string) => {
    setSerialNumber(serial);
    setIsFactoryResetModalOpen(true);
  };

  const onCloseFactoryReset = () => {
    setIsFactoryResetModalOpen(false);
  };

  const onOpenTelemetry = (serial: string) => {
    setSerialNumber(serial);
    setIsTelemetryModalOpen(true);
  };

  const onCloseTelemetry = () => {
    setIsTelemetryModalOpen(false);
  };

  const onOpenTrace = (serial: string) => {
    setSerialNumber(serial);
    setIsTraceModalOpen(true);
  };

  const onCloseTrace = () => {
    setIsTraceModalOpen(false);
  };



    const actionsCell=React.useCallback((device: DeviceWithStatus) => {
      return(
      <Actions device={device}  
      refreshTable={getDevices.refetch} 
      onOpenScan={onOpenScan}
      onOpenConfigureModal={onOpenConfigureModal}
      onOpenRebootModal={onOpenRebootModal}
      onOpenEventQueue={onOpenEventQueue}
      onOpenFactoryReset={onOpenFactoryReset}
      onOpenTelemetryModal={onOpenTelemetry}
      onOpenTrace={onOpenTrace}
      />
      );
    }, []);

  const { count, data: devices, isFetching } = useControlledTable({
    useCount: useGetDeviceCount as (props: unknown) => UseQueryResult,
    useGet: useGetDevices as (props: unknown) => UseQueryResult,
    countParams: { enabled: true, platform },
    getParams: {  pageInfo: {
      limit: tableController.pageInfo.pageSize,
      index: tableController.pageInfo.pageIndex,
    },
    enabled: true,
    platform, },
  });

  // 处理表格变化（分页、排序）
  const handleTableChange = (pagination, sorter) => {
    // 更新分页信息
    setPageInfo({
      index: pagination.current - 1,
      limit: pagination.pageSize,
    });
    // 更新排序信息
    if (sorter.field) {
      setSortInfo([{
        id: sorter.field,
        sort: sorter.order === 'ascend' ? 'asc' : 'desc'
      }]);
    }
  };
  const handleDenyListClick = () => {
    // console.log(`handleDenyListClick`);
    navigate("/wireless/devices/blacklist");
  };

  const handleRefresh = () => {
        getDevices.refetch();
        getCount.refetch();
    
  };
  
  useImperativeHandle(ref, () => ({
      refreshTable: () => {
         getDevices.refetch();
        getCount.refetch();
      },
    }));
  const data = React.useMemo(() => {
        if (!getDevices.data) return [];
        return getDevices.data.devicesWithStatus.map((device) => ({
          ...device,
          age: getAges?.data?.ages.find(({ serialNumber: devSerial }) => devSerial === device.serialNumber),
        }));
      }, [getAges, getDevices.data, getDevices.dataUpdatedAt]);
  const columns = useMemo((): Column[] => [
    {
        key: 'connected',
        title: 'Status',
        footer: '',
        dataIndex: 'connected',
        render: (_, record: DeviceWithStatus) => statusCell(record),
        fixed: 'left',
        sorter: true,
        isMonospace: true,
      },
    {
        key: 'serialNumber',
        title: t('inventory.serial_number'),
        footer: '',
        dataIndex: 'serialNumber',
        render: (_, record: DeviceWithStatus) => serialCell(record),
        fixed: 'left',
        sorter: true,
        isMonospace: true,
      },
      {
        key: 'compatible',
        title: t('common.type'),
        footer: '',
        dataIndex: 'compatible',
        // enableSorting: false,
        fixed: 'left',
        sorter: true,
        isMonospace: true,
      },
      {
        key: 'sanity',
        title: t('devices.sanity'),
        dataIndex: 'sanity',
        footer: '',
        render: (_, record: DeviceWithStatus) => sanityCell(record),
        meta: {
          headerStyleProps: {
            textAlign: 'center',
          },
        },
        fixed: 'left',
        sorter: true,
        isMonospace: true,
      },
      {
        key: 'temperature',
        title: 'Temp(°C)',
        dataIndex: 'temperature',
        footer: '',
        render: (_, record: DeviceWithStatus) => temperatureCell(record),
        // enableSorting: false,
        meta: {
          headerOptions: {
            tooltip: t('analytics.temperature'),
          },
          columnSelectorOptions: {
            label: t('analytics.temperature'),
          },
          headerStyleProps: {
            textAlign: 'center',
          },
        },
        fixed: 'left',
        sorter: true,
        isMonospace: true,
      },
      {
        // id: 'provisioning',
        // header: 'Provisioning',
        key: 'Venue',
        title: 'Venue',
        footer: '',
        dataIndex: 'Venue',
        render: (_, record: DeviceWithStatus) => provCell(record),
        enableSorting: false,
        meta: {
          stopPropagation: true,
        },
      },
      {
        key: 'firmware',
        title: t('commands.revision'),
        footer: '',
        dataIndex: 'firmware',
        render: (_, record: DeviceWithStatus) => firmwareCell(record),
        // enableSorting: false,
        meta: {
          stopPropagation: true,
          customWidth: '50px',
        },
        fixed: 'left',
        sorter: true,
        isMonospace: true,
      },
      {
        key: 'actions',
        title: 'Operation',
        footer: '',
        dataIndex: 'actions',
        render: (_, record: DeviceWithStatus) => (
        <div data-col-key="actions">
          {actionsCell(record)}
        </div>),
        enableSorting: false,
        meta: {
          customWidth: '50px',
          alwaysShow: true,
        },
      },
  ], [t]);

  return (
    <>
     <div style={{ display: 'flex',gap: 10}}>
        <ExportDevicesTableButton currentPageSerialNumbers={data.map((device) => device.serialNumber)} />
        <Button
          htmlType="button"
          onClick={() => {
            handleRefresh();
            message.success("Device refresh success.");
          }}
        >
          <Icon component={refreshSvg} />
          Refresh
        </Button>
        <Button  icon={<Icon component={denyListSvg} />} onClick={handleDenyListClick}>
          Denylist
        </Button>
            <div style={{ display: "flex", justifyContent: "flex-end", width: "100%" }}>
          <SearchInput />
        </div>
      </div>
          <WirelessCustomTable
            ref={ref}
            columns={columns}
            dataSource={devices?.devicesWithStatus || []}
            loading={isFetching}
            onChange={handleTableChange}
            isShowPagination={true}
            showColumnSelector={true}
            fetchAPIInfo={useGetDevices}
            pagination={{
              current: pageInfo.index + 1,
              pageSize: pageInfo.limit,
              total: count,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total) => `Total ${total} items`,
              pageSizeOptions: ['10', '20', '30', '40', '50'],
            }}
          //   onRow={(record: DeviceWithStatus) => ({
          //   onClick: () => {
          //     navigate(`/devices/${record.serialNumber}`);
          //   },
          //   style: { cursor: 'pointer' }, // 鼠标变手势
          // })}
          // onRow={(record: DeviceWithStatus) => ({
          //   onClick: (e) => {
          //     // 防止点击 actions 列中的元素触发跳转
          //     const isAction = (e.target as HTMLElement).closest('[data-col-key="actions"]');
          //     if (!isAction) {
          //       navigate(`/wireless/devices/${record.serialNumber}#/devices/${record.serialNumber}`);
          //     }
          //   },
          //   style: { cursor: 'pointer' }, // 鼠标变手势
          // })}
          />
          <WifiScanModal modalProps={{ isOpen: isScanModalOpen, onClose: onCloseScan }} serialNumber={serialNumber} />
          <ConfigureModal modalProps={{ isOpen: isConfigureModalOpen, onClose: onCloseConfigure }} serialNumber={serialNumber} />
          <RebootModal modalProps={{ isOpen: isRebootModalOpen, onClose: onCloseReboot }} serialNumber={serialNumber} />
          <FactoryResetModal modalProps={{ isOpen: isFactoryResetModalOpen, onClose: onCloseFactoryReset }} serialNumber={serialNumber} />
          <TelemetryModal modalProps={{ isOpen: isTelemetryModalOpen, onClose: onCloseTelemetry }} serialNumber={serialNumber} />
          <TraceModal modalProps={{ isOpen: isTraceModalOpen, onClose: onCloseTrace }} serialNumber={serialNumber} />
          <EventQueueModal modalProps={{ isOpen: isEventQueueModalOpen, onClose: onCloseEventQueue }} serialNumber={serialNumber} />
    </>
  );
});

export default DevicesList;
