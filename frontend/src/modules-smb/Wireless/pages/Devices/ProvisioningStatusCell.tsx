import * as React from 'react';
import { Button } from 'antd';
import { DeviceWithStatus } from '@/modules-smb/hooks/Network/Devices';
import { useGetTag } from '@/modules-smb/hooks/Network/Inventory';
import { useNavigate } from 'react-router-dom';

type Props = {
  device: DeviceWithStatus;
};

const ProvisioningStatusCell = ({ device }: Props) => {
  const getTag = useGetTag({ serialNumber: device.serialNumber });
  const navigate = useNavigate();

  const handleClick = (path: string) => () => {
    navigate(`/wireless/${path}`);
  };

  if (getTag.data?.extendedInfo?.entity?.name) {
    return (
      <a style={{color:'#14C9BB ',textDecoration:'underline'}} onClick={handleClick(`entity/${getTag.data?.entity}`)}>
        {getTag.data?.extendedInfo?.entity?.name}
      </a>
    );
  }
  if (getTag.data?.extendedInfo?.venue?.name) {
    return (
      <a style={{color:'#14C9BB ',textDecoration:'underline'}} onClick={handleClick(`venue/${getTag.data?.venue}`)}>
        {getTag.data?.extendedInfo?.venue?.name}
      </a>
    );
  }
  if (getTag.data?.extendedInfo?.subscriber?.name) {
    return (
      <a style={{color:'#14C9BB ',textDecoration:'underline'}}  onClick={handleClick(`venue/${getTag.data?.subscriber}`)}>
        {getTag.data?.extendedInfo?.subscriber?.name}
      </a>
    );
  }
  return <span>-</span>;
};

export default ProvisioningStatusCell;
