import React,{useState} from "react";
import { useLocation, useNavigate } from "react-router-dom";
import SiteSelect from "@/modules-smb/Wireless/components/SiteSelect";
import "../../assets/wireless.scss";
import DevicesList from "./DevicesList";
const DevicesPage = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const [venueId, setVenueId] = useState<number | null>(null);
  const handleChange = (value: string | string[]) => {
    const pathWithoutTab = location.pathname;
    navigate(`${pathWithoutTab}#${value}`);
    if (typeof value === 'string' && /^\d+$/.test(value)) {
      setVenueId(parseInt(value, 10));
    } else {
      setVenueId(null);
    }
  };

  return (
    <>
      <div>
        <span className="text-title">Devices</span>
        <SiteSelect onChange={handleChange} />
      </div>
      <DevicesList venueId={venueId?.toString()} />
    </>
  );
};

export default DevicesPage;
