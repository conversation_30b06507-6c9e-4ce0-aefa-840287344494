import React, { useState, useEffect } from 'react';
import { useMutation } from '@tanstack/react-query';
import PropTypes from 'prop-types';
import { useTranslation } from 'react-i18next';
import { Modal, Button, Form, Input, Select, Switch, Row, Col, message } from 'antd';
import Icon from "@ant-design/icons";
import { addSvg } from "@/utils/common/iconSvg";
import { axiosProv } from '@/modules-smb/utils/axiosInstances';
import { useGetDeviceTypes } from "@/modules-ampcon/apis/upgrade_api";
import { useGetVenues } from '@/modules-smb/hooks/Network/Venues';
import LabelSelect from '@/modules-smb/Wireless/components/FormFields/LabelSelect';
import './CreateDeviceForm.scss';
const { Option } = Select;

const propTypes = {
  refresh: PropTypes.func.isRequired,
  entityId: PropTypes.string,
  subId: PropTypes.string,
  deviceClass: PropTypes.string,
  venueId: PropTypes.string,
};

const defaultProps = {
  entityId: '',
  subId: '',
  deviceClass: '',
  venueId: '',
};

const CreateTagModal = ({ refresh, entityId, venueId, subId, deviceClass }) => {
  const { t } = useTranslation();
  const [form] = Form.useForm();
  const [visible, setVisible] = useState(false);
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [deviceTypes, setDeviceTypes] = useState([]);
  const [venues, setVenues] = useState([]);
  // const [isVenueDisabled, setIsVenueDisabled] = useState(!!venueId);
  const [isVenueDisabled, setIsVenueDisabled] = useState(venueId !== undefined && venueId !== null && venueId !== '');
  const { data: deviceTypesData } = useGetDeviceTypes();
  const { data: venuesData } = useGetVenues({ t, toast: message });
  useEffect(() => {
    if (deviceTypesData) {
      setDeviceTypes(deviceTypesData);
    }
  }, [deviceTypesData]);

  useEffect(() => {
    if (venuesData) {
      setVenues(venuesData);
    }
  }, [venuesData]);

  useEffect(() => {
    setIsVenueDisabled(venueId !== undefined && venueId !== null && venueId !== '');
    form.setFieldsValue({
      entity: (venueId !== '' && venueId !== undefined && venueId !== null) ? `ven:${venueId}` : ''
    });
  }, [venueId, form]);
  const create = useMutation((newObj) =>
    axiosProv.post(
      `inventory/${newObj.serialNumber}`,
      newObj
    )
  );

  const createParameters = (formData) => ({
    serialNumber: formData.serialNumber.toLowerCase(),
    name: formData.name,
    deviceRules: formData.deviceRules,
    deviceType: formData.deviceType,
    devClass: deviceClass !== '' ? deviceClass : formData.devClass,
    description: formData.description || undefined,
    notes: formData.note ? [{ note: formData.note }] : undefined,
    entity: formData.entity === '' || formData.entity.split(':')[0] !== 'ent' ? '' : formData.entity.split(':')[1],
    venue: formData.entity === '' || formData.entity.split(':')[0] !== 'ven' ? '' : formData.entity.split(':')[1],
    doNotAllowOverrides: formData.doNotAllowOverrides,
    subscriber: subId !== '' ? subId : '',
    label: formData.label
  });

  const handleSubmit = async () => {
    try {
      setConfirmLoading(true);
      const formValues = await form.validateFields();
      const extraValues = {
        deviceRules: {
          rrm: 'inherit',
          rcOnly: 'inherit',
          firmwareUpgrade: 'inherit',
        },
        devClass: deviceClass !== '' ? deviceClass : 'any',
      };
      const values = { ...formValues, ...extraValues };
      const params = createParameters(values);
      console.log("params,params", params)
      await create.mutateAsync(params, {
        onSuccess: () => {
          message.success(t('crud.success_create_obj', {
            obj: t('certificates.device'),
          }));
          refresh();
          setVisible(false);
          form.resetFields();
        },
        onError: (e) => {
          message.error(t('crud.error_create_obj', {
            obj: t('certificates.device'),
            e: e?.response?.data?.ErrorDescription,
          }));
        },
      });
    } catch (error) {
      console.error('Validation failed:', error);
    } finally {
      setConfirmLoading(false);
    }
  };

  const handleCancel = () => {
    form.resetFields();
    setVisible(false);
  };

  return (
    <>
      <Button
        type="primary"
        onClick={() => setVisible(true)}
        style={{ display: "flex", alignItems: "center" }}
        icon={<Icon component={addSvg} />}
      >
        {t('common.create')}
      </Button>

      <Modal
        title={t('common.create')}
        visible={visible}
        onCancel={handleCancel}
        width={1400}
        footer={[
          <Button key="back" onClick={handleCancel}>
            Cancel
          </Button>,
          <Button
            key="submit"
            type="primary"
            loading={confirmLoading}
            onClick={handleSubmit}
          >
            Apply
          </Button>,
        ]}
      >
        <Form
          form={form}
          initialValues={{
            serialNumber: '',
            name: '',
            description: '',
            deviceType: deviceTypes[0],
            deviceRules: {
              rrm: 'inherit',
              rcOnly: 'inherit',
              firmwareUpgrade: 'inherit',
            },
            devClass: deviceClass !== '' ? deviceClass : 'any',
            note: '',
            entity: (venueId !== '' && venueId !== undefined && venueId !== null) ? `ven:${venueId}` : '',
            doNotAllowOverrides: false,
            label: ''
          }}
          className="wirelessFormCreateDevice"
        >
          <h3 className='header1'></h3>
          <Row gutter={24}>
            <Col span={12}>
              <Form.Item
                name="serialNumber"
                label={t('inventory.serial_number')}
                rules={[{ required: true, message: t('form.required') }]}
              >
                <Input />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="name"
                label={t('common.name')}
                rules={[{ required: true, message: t('form.required') }]}
              >
                <Input />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={24}>
            <Col span={12}>
              <Form.Item
                name="deviceType"
                label={t('inventory.device_type')}
                rules={[{ required: true, message: t('form.required') }]}
              >
                <Select>
                  {deviceTypes.map(deviceType => (
                    <Option key={deviceType} value={deviceType}>{deviceType}</Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="entity"
                label={t('inventory.site')}
              >
                <Select disabled={isVenueDisabled}>
                  <Option value="">{t('common.none')}</Option>
                  {venues.map(ven => (
                    <Option key={`ven:${ven.id}`} value={`ven:${ven.id}`}>
                      {`${ven.name}${ven.description ? `: ${ven.description}` : ''}`}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={24}>
            <Col span={12}>
              <Form.Item
                noStyle
                shouldUpdate={(prevValues, currentValues) => prevValues.entity !== currentValues.entity}
              >
                {({ getFieldValue }) => {
                  const entityValue = getFieldValue('entity');
                  const siteId = entityValue && entityValue.startsWith('ven:')
                    ? parseInt(entityValue.split(':')[1], 10)
                    : null;
                  return (
                    <Form.Item
                      name="label"
                      label={t('inventory.label')}
                    >
                      <LabelSelect
                        siteId={siteId}
                        value={form.getFieldValue("label")}
                        onChange={(value) => form.setFieldsValue({ label: value })}
                      />
                    </Form.Item>
                  );
                }}
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="doNotAllowOverrides"
                label={t('overrides.ignore_overrides')}
                valuePropName="checked"
                rules={[{ required: true, message: t('form.required') }]}
              >
                <Switch />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={24}>
            <Col span={12}>
              <Form.Item
                name="description"
                label={t('common.description')}
              >
                <Input />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="note"
                label={t('common.note')}
              >
                <Input />
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Modal>
    </>
  );
};

CreateTagModal.propTypes = propTypes;
CreateTagModal.defaultProps = defaultProps;

export default CreateTagModal;