.wirelessFormCreateDevice {
  .ant-form-item-label > label {
    width: 220px;
    text-align: left;
    word-wrap: break-word;
    overflow: hidden;
  }
  .ant-form-item-control {
    .ant-input-affix-wrapper,
    .ant-input,
    .ant-select,
    .ant-input-number,
    .ant-picker,
    .ant-select-selector {
      width: 300px;
    }
  }
  .header1 {
    margin-top: 10px;
    margin-bottom: 20px;
    border-bottom: 1px solid #eee;
  }
  .header2 {
    margin-top: 10px;
    padding-top: 20px;
    margin-bottom: 20px;
    border-top: 1px solid #eee;
  }
  .foot-btns {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    margin-top: 24px;
    margin-right: 30px;
    padding-bottom: 8px;
  }
}
