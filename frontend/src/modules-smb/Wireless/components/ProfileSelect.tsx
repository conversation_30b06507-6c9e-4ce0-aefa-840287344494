import React, { useState, useEffect } from 'react';
import { Form, Select, Button, message, Modal, Switch, Flex } from 'antd';
import { useTranslation } from 'react-i18next';
import { PlusOutlined } from '@ant-design/icons';
import { useNavigate } from "react-router-dom";
import { getWirelessProfileList } from '@/modules-smb/Wireless/apis/wireless_profile_api';
import RadiusForm from '@/modules-smb/Wireless/pages/Profile/SSIDRadius/RadiusForm/Form';
import WebrootForm from '@/modules-smb/Wireless/pages/Profile/CaptivePortal/WebrootForm/Form';
import FullPageMPSKForm from '@/modules-smb/Wireless/pages/Profile/MPSKUser/MPSKUserForm';
import TimeRangeModal from '@/modules-smb/Wireless/pages/Entities/Configure/Layout/Radios/TimeRangeModal';

interface ProfileSelectProps {
  label: string;
  formName: string; // 表单项名称，如 ['radius', 'profileId']
  switchEnabled: boolean;
  onSwitchChange: (checked: boolean) => void;
  type: number;
  siteId: number;
  modalWidth?: number;
  edit?: string;
  onProfileChange?: (profile: any) => void;
}

// profile初始化配置
const PROFILE_CONFIG = {
  1: { // Radius
    modalComponent: RadiusForm,
    navigateUrl: "/wireless/profile/SSIDRadius",
  },
  2: { // Multi Psk
    modalComponent: FullPageMPSKForm,
    navigateUrl: "/wireless/profile/MPSKUser",
  },
  3: { // Captive Portal
    modalComponent: WebrootForm,
    navigateUrl: "/wireless/profile/CaptivePortal",
  },
  4: { // Schedule
    modalComponent: null, // 已有模态框特殊处理
    navigateUrl: "/wireless/profile/TimeRange",
  },
};

const ProfileSelect: React.FC<ProfileSelectProps> = ({
  label,
  formName,
  switchEnabled,
  onSwitchChange,
  type,
  siteId,
  modalWidth = 1000,
  edit,
  onProfileChange,
}) => {
  const { t } = useTranslation();
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [profiles, setProfiles] = useState<any[]>([]);
  const form = Form.useFormInstance();
  const navigate = useNavigate();

  // 从配置字典获取配置
  const config = PROFILE_CONFIG[type as keyof typeof PROFILE_CONFIG];
  const ModalComponent = config.modalComponent;

  const loadProfiles = async () => {
    try {
      const res = await getWirelessProfileList(type, siteId, 1, 1000);
      if (res?.status === 200) {
        setProfiles(res.info || []);
      } else {
        message.error(res?.info || 'fetch profile list fail');
      }
    } catch (error) {
      if (error instanceof Error) {
        message.error(error.message);
      } else {
        message.error('An unknown error occurred');
      }
    }
  };

  useEffect(() => {
    if (switchEnabled) {
      loadProfiles();
    }
  }, [switchEnabled]);

  // type为1时初始化自动加载
  useEffect(() => {
    if (type === 1) {
      loadProfiles();
    }
  }, [type, siteId]);

  // edit有值时自动选中
  useEffect(() => {
    if (!edit) return;
    if (profiles.length === 0) return;
    if (!switchEnabled) {
      onSwitchChange(true);
    }
    form.setFieldValue(formName, edit);
    if (onProfileChange) {
      const selected = profiles.find(p => (type === 1 ? p.name : p.variable_id) === edit);
      onProfileChange(selected);
    }
    return;
  }, [edit, profiles, type]);

  const showModal = () => {
    setIsModalVisible(true);
  };

  const handleModalCancel = () => {
    setIsModalVisible(false);
  };

  const handleModalOk = () => {
    setIsModalVisible(false);
    loadProfiles();
  };

  const handleSwitchChange = (checked: boolean) => {
    onSwitchChange(checked);
    if (!checked && form) {
      form.setFieldValue(formName, undefined);
    }
  };

  const handleSelectChange = (value: any) => {
    form.setFieldValue(formName, value);
    if (onProfileChange) {
      const selected = profiles.find(p => (type === 1 ? p.name : p.variable_id) === value);
      onProfileChange(selected);
    }
  };

  return (
    <>
      <Form.Item label={label} style={{ marginBottom: 10 }}>
        <Switch
          checked={switchEnabled}
          onChange={handleSwitchChange}
        />
      </Form.Item>
      {switchEnabled && (
        <Flex>
          <Form.Item
            name={formName}
            label= " " 
          >
            <Select
              dropdownRender={menu => (
                <>
                  {menu}
                  <Button type="link" icon={<PlusOutlined />} onClick={showModal} style={{ width: '100%', borderTop: '1px solid #E7E7E7'}}>
                    Create {label} Profile
                  </Button>
                </>
              )}
              onChange={handleSelectChange}
            >
              {profiles.map(profile => (
                <Select.Option
                  key={profile.variable_id}
                  value={profile.variable_id}
                >
                  {profile.name}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>
          <Button
            type="text"
            style={{ backgroundColor: '#fff' }}
            onClick={() => navigate(config.navigateUrl + '#' + siteId)}
          >
            Manage {label} Profile
          </Button>
        </Flex>
      )}
      {/* 新增模态框 */}
      {type === 4 ? (
        <TimeRangeModal
          visible={isModalVisible}
          onClose={handleModalOk}
          siteId={siteId}
        />
      ) : (
        <Modal
          title={`Create ${label} Profile`}
          open={isModalVisible}
          onCancel={handleModalCancel}
          footer={null}
          width={modalWidth}
          destroyOnClose
          styles={{ body: { maxHeight: '80vh', overflowY: 'auto', overflowX: 'hidden' } }}
        >
          {ModalComponent && <ModalComponent onClose={handleModalOk} siteId={siteId} />}
        </Modal>
      )}
    </>
  );
};

export default ProfileSelect; 