import React, { useEffect, useState } from 'react';
import { Dropdown, Checkbox, Input, Button } from 'antd';
import { DownOutlined, UpOutlined, SearchOutlined } from '@ant-design/icons';

interface GroupSelectProps {
  value?: string;
  handleOk?: (group_name: string) => void;
  groupOptions: { label: string; value: string }[];
}

const GroupSelect: React.FC<GroupSelectProps> = ({ value, handleOk, groupOptions }) => {
  const [open, setOpen] = useState(false);
  const [selected, setSelected] = useState<string[]>([]);
  const [search, setSearch] = useState('');

  // 外部value变化时同步selected
  useEffect(() => {
    if (value === 'all') {
      setSelected(groupOptions.map(opt => opt.value));
    } else if (value) {
      setSelected(value.split(',').map(s => s.trim()).filter(Boolean));
    } else {
      setSelected([]);
    }
  }, [value, groupOptions]);

  const allValues = groupOptions.map(opt => opt.value);
  const filteredOptions = groupOptions.filter(opt => opt.label.toLowerCase().includes(search.toLowerCase()));

  const handleCheckboxChange = (list: any[]) => {
    if (list.length === allValues.length) {
      setSelected(allValues);
    } else {
      setSelected(list);
    }
  };

  const handleOkClick = () => {
    const isAll = selected.length === allValues.length;
    if (handleOk && handleOk(isAll ? 'all' : selected.join(','))) {
      setOpen(false);
    }
  };

  const handleReset = () => {
    setSelected([]);
  };

  return (
    <Dropdown
      trigger={['click']}
      open={open}
      onOpenChange={flag => {
        setOpen(flag);
        if (flag) {
          // 打开时重置selected为value对应的内容
          if (value === 'all') {
            setSelected(groupOptions.map(opt => opt.value));
          } else if (value) {
            setSelected(value.split(',').map(s => s.trim()).filter(Boolean));
          } else {
            setSelected([]);
          }
        }
      }}
      dropdownRender={() => (
        <div style={{ width: 140, padding: 10, background: '#fff', boxShadow: '0px 4px 4px 0px #00000026', marginTop: -5 }}>
          <Input
            prefix={<SearchOutlined style={{ color: '#B8BFBF' }}/>} 
            value={search}
            onChange={e => setSearch(e.target.value)}
            style={{ marginBottom: 8 }}
          />
          <Checkbox.Group
            options={filteredOptions}
            value={selected}
            onChange={handleCheckboxChange}
            style={{ width: '100%', display: 'flex', flexDirection: 'column', padding: 10 }}
          />
          <div style={{ display: 'flex', justifyContent: 'space-between', borderTop: '1px solid #E7E7E7' }}>
            <Button type="text" onClick={handleReset} style={{ background: '#fff' }}>Reset</Button>
            <Button type="text" onClick={handleOkClick} style={{ background: '#fff' }}>OK</Button>
          </div>
        </div>
      )}
      overlayStyle={{ zIndex: 2000 }}
    >
      <Button type="link" style={{ width: 160, background: '#fff', textAlign: 'left', boxShadow: open ? '0px -2px 4px 0px #00000026' : undefined }}>
        {value || 'Select Group'} {open ? <UpOutlined /> : <DownOutlined />}
      </Button>
    </Dropdown>
  );
};

export default GroupSelect; 