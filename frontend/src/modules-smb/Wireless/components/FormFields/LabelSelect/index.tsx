import React, { useState, useEffect } from 'react';
import { Dropdown, Menu, Input, Modal, Form, message, Button } from 'antd';
import { DeleteOutlined, PlusOutlined, DownOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { fetchLables, createLable, deleteLable } from '@/modules-smb/Wireless/apis/lable';
import '@/modules-smb/Wireless/components/FormFields/LabelSelect/createLabel.scss';
import { confirmModalAction } from "@/modules-ampcon/components/custom_modal";
import Logo_Delete from "@/modules-smb/Wireless/assets/Inventory/Logo_Delete.png"
const LabelSelect = ({ siteId, value, onChange }) => {
  const { t } = useTranslation();
  const [labels, setLabels] = useState([]);
  const [loading, setLoading] = useState(false);
  const [searchValue, setSearchValue] = useState('');
  const [createModalVisible, setCreateModalVisible] = useState(false);
  const [createForm] = Form.useForm();
  const [submitting, setSubmitting] = useState(false);

  useEffect(() => {
    if (!siteId) return;
    loadLabels(searchValue);
  }, [siteId, searchValue]);

  const loadLabels = async (searchKey = '') => {
    try {
      setLoading(true);
      const params = { siteId };

      if (searchKey.trim() !== '') {
        params.key = searchKey;
      }

      const response = await fetchLables(params);
      setLabels(response.data || []);
    } catch (error) {

    } finally {
      setLoading(false);
    }
  };

  const handleCreateLabel = async () => {
    try {
      setSubmitting(true);
      const values = await createForm.validateFields();
      await createLable({ site_id: siteId, name: values.name });
      message.success('Create label success');
      loadLabels();
      onClose();
    } catch (error) {
      message.error('Failed to create label');

    } finally {
      setSubmitting(false);
    }
  };

  const onClose = () => {
    setCreateModalVisible(false);
    createForm.resetFields();
  };

  const handleDeleteLabel = (labelId, labelName, e) => {
    e.stopPropagation();
    confirmModalAction(
      `Are you sure to delete the Label "${labelName}"?`,
      async () => {
        try {
          await deleteLable(labelId);
          message.success('Delete label success');
          const currentValue = form.getFieldValue('label');
          if (currentValue === labelId) {
            form.setFieldsValue({ label: undefined });
          }
          loadLabels();
        } catch (error) {
          message.error('Failed to delete label');
        }
      }
    );
  };

  const handleSelectLabel = (labelId) => {
    onChange(labelId);
    const selected = labels.find(label => label.id === labelId);
    if (selected) setSearchValue(selected.name);
  };

  const NoOptionsMessage = () => (
    <div style={{ padding: '8px 16px', textAlign: 'center' }}>
      No Labels Found
    </div>
  );

  const menu = (
    <Menu>
      {labels.length > 0 ? (
        labels.map(label => (
          <Menu.Item
            key={label.id}
            onClick={() => handleSelectLabel(label.id)}
          >
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <span>{label.name}</span>
              <span
                onClick={(e) => handleDeleteLabel(label.id, label.name, e)}
                style={{ cursor: 'pointer', display: 'inline-block' }}
              >
                <img
                  src={Logo_Delete}
                  alt="Delete"
                  style={{ width: 16, height: 16, verticalAlign: 'middle' }}
                />
              </span>
            </div>
          </Menu.Item>
        ))
      ) : (
        <Menu.Item disabled style={{ border: 'none', padding: 0 }}>
          <NoOptionsMessage />
        </Menu.Item>
      )}

      <Menu.Item
        key="create"
        onClick={() => setCreateModalVisible(true)}
        style={{
          color: '#14C9BB',
          textAlign: 'center'
        }}
      >
        <span style={{ display: 'inline-flex', alignItems: 'center' }}>
          <PlusOutlined style={{ color: '#14C9BB', marginRight: 4 }} /> {t('common.create')}
        </span>
      </Menu.Item>
    </Menu>
  );
  return (
    <>
      <Dropdown
        trigger={['click']}
        overlay={menu}
        placement="bottomLeft"
        disabled={siteId === undefined || siteId === null} 
      >
        <Input
          value={searchValue}
          onChange={(e) => setSearchValue(e.target.value)}
          suffix={<DownOutlined />}
        />
      </Dropdown>

      <Modal
        title={'Create Label'}
        visible={createModalVisible}
        onCancel={onClose}
        footer={null}
        width={1200}
        className="label-modal"
      >
        <Form className="wirelessFormCreateLabel" form={createForm} onFinish={handleCreateLabel}>
          <Form.Item
            name="name"
            label={t('common.name')}
            rules={[{ required: true, message: t('form.required') }]}
          >
            <Input />
          </Form.Item>
          <div className="foot-btns">
            <Button onClick={onClose} disabled={submitting}>
              Cancel
            </Button>
            <Button type="primary" htmlType="submit" loading={submitting}>
              Apply
            </Button>
          </div>
        </Form>
      </Modal>
    </>
  );
};

export default LabelSelect;