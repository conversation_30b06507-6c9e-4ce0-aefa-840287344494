import React, { useCallback } from 'react';
import { useField } from 'formik';
import PropTypes from 'prop-types';
import Field from './FastCreatableSelectInput';

const propTypes = {
  name: PropTypes.string.isRequired,
  label: PropTypes.string.isRequired,
  isDisabled: PropTypes.bool,
  isRequired: PropTypes.bool,
  isHidden: PropTypes.bool,
  emptyIsUndefined: PropTypes.bool,
  placeholder: PropTypes.string,
  definitionKey: PropTypes.string,
};

const defaultProps = {
  isRequired: false,
  isDisabled: false,
  isHidden: false,
  emptyIsUndefined: false,
  placeholder: '',
  definitionKey: null,
};

const CreatableSelectField = ({
  name,
  isDisabled,
  label,
  isRequired,
  isHidden,
  emptyIsUndefined,
  placeholder,
  definitionKey,
  w,
}) => {
  const [{ value }, { touched, error }, { setValue, setTouched }] = useField(name);

 const onChange = useCallback((opts) => {
  if (emptyIsUndefined && opts.length === 0) {
    setValue(undefined);
  } else {
    setValue(opts);
  }
  setTouched(true);
}, [emptyIsUndefined]);


  const onFieldBlur = useCallback(() => {
    setTouched(true);
  }, []);

  return (
    <Field
      label={label}
      value={value}
      onChange={onChange}
      onBlur={onFieldBlur}
      error={error}
      touched={touched}
      placeholder={placeholder}
      isDisabled={isDisabled}
      isRequired={isRequired}
      isHidden={isHidden}
      definitionKey={definitionKey}
      w={w}
    />
  );
};

CreatableSelectField.propTypes = propTypes;
CreatableSelectField.defaultProps = defaultProps;

export default React.memo(CreatableSelectField);
