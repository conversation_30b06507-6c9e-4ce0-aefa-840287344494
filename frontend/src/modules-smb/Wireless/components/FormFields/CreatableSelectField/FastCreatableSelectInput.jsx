import React from 'react';
import PropTypes from 'prop-types';
import { Select, Form } from 'antd';
import { useTranslation } from 'react-i18next';
import ConfigurationFieldExplanation from '../ConfigurationFieldExplanation';

const { Option } = Select;

const propTypes = {
  value: PropTypes.arrayOf(PropTypes.string),
  label: PropTypes.string.isRequired,
  onChange: PropTypes.func.isRequired,
  onBlur: PropTypes.func.isRequired,
  error: PropTypes.oneOfType([PropTypes.string, PropTypes.bool]),
  touched: PropTypes.bool,
  isDisabled: PropTypes.bool,
  isRequired: PropTypes.bool,
  isHidden: PropTypes.bool,
  definitionKey: PropTypes.string,
  placeholder: PropTypes.string,
  width: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
};

const defaultProps = {
  value: [],
  error: false,
  touched: false,
  isRequired: false,
  isDisabled: false,
  isHidden: false,
  definitionKey: null,
  placeholder: '',
};

const FastCreatableSelectInput = ({
  label,
  value,
  onChange,
  onBlur,
  error,
  touched,
  isRequired,
  isDisabled,
  isHidden,
  definitionKey,
  placeholder,
  w,
}) => {
  const { t } = useTranslation();

  const handleChange = (newValue) => {
    onChange(newValue);
  };

  return (
    <Form.Item
      label={
        <div style={{ display: 'flex', alignItems: 'center', gap: 2,width:'180px' }}>
         <span>{label}</span>
         {isRequired && <span style={{ color: 'red' ,marginBottom:-3}}>*</span>}
         <ConfigurationFieldExplanation definitionKey={definitionKey} />
        </div>
      }
      // required={isRequired}
      hidden={isHidden}
      validateStatus={error && touched ? 'error' : ''}
      help={touched && error ? error : null}
      // style={{width:"20px"}}
    >
      <Select
        mode="tags"
        style={{ width: typeof w === 'number' ? `${w}px` : w || '100%' }}
        placeholder={placeholder || t('common.type_for_options')}
        value={value}
        onChange={handleChange}
        onBlur={onBlur}
        disabled={isDisabled}
         options={(value || []).map((v) => ({ label: v, value: v }))}
      />
    </Form.Item>
  );
};

FastCreatableSelectInput.propTypes = propTypes;
FastCreatableSelectInput.defaultProps = defaultProps;

export default React.memo(FastCreatableSelectInput);
