{"account": {"account": "C<PERSON><PERSON>", "activating_google_authenticator": "Activando el Autenticador de Google", "activating_sms_mfa": "Validación del número de teléfono", "avatar": "Avatar", "error_fetching_qr": "Error al obtener el código QR: {{e}}", "error_phone_verif": "Error con su código de validación, inténtelo de nuevo.", "google_authenticator": "Autenticador de Google", "google_authenticator_intro": "Para usar Google Authenticator como método de autenticación doble de su cuenta, primero debe instalar la aplicación en su dispositivo iOS o Android", "google_authenticator_ready": "Una vez que tenga la aplicación lista para usar, puede continuar", "google_authenticator_scan_qr_code_explanation": "Escanee el siguiente código QR usando \"Escanear un código QR\" en la aplicación Google Authenticator", "google_authenticator_scanned_qr_code": "Una vez que el código QR se haya escaneado con éxito en su teléfono, puede continuar con el siguiente paso", "google_authenticator_success_explanation": "Ahora ha configurado con éxito Google Authenticator con su cuenta. ¡No olvides guardar tus cambios para confirmar!", "google_authenticator_type_code": "Ingrese el código de 6 dígitos de su aplicación Google Authenticator a continuación", "google_authenticator_wait_for_code": "<PERSON><PERSON>e el siguiente código (no {{old}})", "google_authenticator_wrong_code": "¡Codigo invalido! Vuelva a intentarlo o espere a que se genere el siguiente código en la aplicación Google Authenticator.", "mfa": "Autenticación multifactorial", "phone": "Teléfono", "phone_number": "Número de teléfono", "phone_number_add_introduction": "Ingrese el número de teléfono que le gustaría usar para proteger su cuenta al iniciar sesión", "phone_required": "Para activar la verificación por SMS, debe ingresar un número de teléfono", "phone_validation_success_explanation": "¡Número de teléfono verificado con éxito! Haga clic en 'Guardar' para agregar este número de teléfono a su cuenta", "proceed_to_activation": "Iniciar proceso de activación", "resend": "Reenviar", "sms": "SMS", "success_phone_verif": "¡Número de teléfono verificado con éxito! Ahora puedes guardar tu perfil", "title": "Mi cuenta", "verify_phone_instructions": "Debería recibir un código en su número de teléfono en los próximos segundos. Ingréselo a continuación para verificar su número de teléfono", "verify_phone_number": "Verifica tu numero de teléfono"}, "analytics": {"ack_signal": "Señal de reconocimiento", "active": "Activo", "airtime": "Tiempo en antena", "analyze_sub_venues": "Supervisar sublugares", "associations": "Asociaciones", "associations_explanation": "Asociaciones totales", "average_health": "Salud en general", "average_health_explanation": "Sanidad promedio de todos los dispositivos conectados que brindan la información de verificación de estado", "average_memory": "Memoria usada", "average_memory_explanation": "Porcentaje medio de memoria utilizada", "average_uptime": "Tiempo de actividad promedio", "average_uptime_explanation": "Tiempo de actividad promedio del dispositivo (DD:HH:MM:SS)", "band": "Banda", "bandwidth": "<PERSON>a ancha", "board": "Colección de análisis", "busy": "Ocupado", "channel": "Canal", "client_lifecycle": "Ciclo de vida del cliente", "client_lifecycle_one": "{{count}} ciclo de vida del cliente", "client_lifecycle_other": "{{count}} ciclos de vida del cliente", "connected": "Conectado", "connection_explanation": "{{connectedCount}} conectado, {{disconnectedCount}} no conectado", "connection_percentage": "{{count}}% conectado", "connection_percentage_explanation": "Porcentaje de todos los dispositivos en este lugar que están conectados ({{connectedCount}} conectados, {{disconnectedCount}} no conectados)", "create_board": "<PERSON><PERSON><PERSON> a monitorear", "dashboard": "<PERSON><PERSON>", "delta": "delta", "device_types": "Los tipos", "device_types_explanation": "Tipos de dispositivos de todos los dispositivos disponibles", "disconnected": "Desconectado", "firmware": "Firmware", "health": "salud", "inactive": "inactivo", "interval": "intervalo", "last_connected": "Última conexion", "last_connected_tooltip": "La última vez que se conectó este dispositivo al controlador. Esto se puede usar para estimar cuándo se desconectó un dispositivo", "last_connection": "Última conexion", "last_contact": "<PERSON>ltimo <PERSON>o", "last_disconnection": "Última desconexión", "last_firmware_explanation": "Firmware más común que se ejecuta en los dispositivos analizados", "last_health": "<PERSON>ltima salud", "last_ping": "<PERSON><PERSON><PERSON> ping", "live_view": "Vista en vivo", "live_view_explanation_five": "También puede hacer clic en cualquiera de los círculos para acercar", "live_view_explanation_four": "<PERSON>uede pasar el mouse sobre cualquiera de los objetos para ver información detallada", "live_view_explanation_one": "El gráfico 'Vista en vivo' es una representación visual de su lugar.", "live_view_explanation_three": "Lugar -> AP -> Radio -> SSID -> UE", "live_view_explanation_two": "De afuera hacia adentro:", "live_view_help": "Ayuda de visualización en vivo", "memory": "Memoria", "memory_used": "Memoria usada", "missing_board": "El monitoreo analítico en este lugar ya no está activo. Haga clic aquí para reiniciar el monitoreo", "mode": "Modo", "monitoring": "Vigilancia", "no_board": "Sin <PERSON>eo", "no_board_description": "No está monitoreando este lugar en este momento, haga clic aquí para comenzar", "noise": "<PERSON><PERSON><PERSON>", "packets": "<PERSON><PERSON><PERSON>", "radio": "RADIO", "raw_analytics_data": "Datos analíticos sin procesar", "raw_data": "Datos sin procesar", "receive": "Recibir", "retention": "Retencion", "retries": "Reintentos", "search_serials": "Buscar seriales", "stop_monitoring": "<PERSON><PERSON>", "stop_monitoring_success": "¡Se detuvo el lugar de monitoreo!", "stop_monitoring_warning": "¿Está seguro? Esto borrará todos los datos de monitoreo grabados para este lugar.", "temperature": "temperatura", "title": "ANALÍTICA", "total_data": "Datos totales", "total_devices_explanation": "Todos los dispositivos en este lugar ({{connectedCount}} conectado, {{disconnectedCount}} no conectado)", "total_devices_one": "{{count}} dispositivo", "total_devices_other": "{{count}} dispositivos", "uptime": "Tiempo de actividad"}, "batch": {"batches": "lotes", "cannot_edit_macs": "Debido a que los trabajos ya se ejecutaron en este lote, no puede editar sus direcciones MAC", "change_warning": "ATENCIÓN: has actualizado el modelo o el fabricante. Recomendamos enfáticamente actualizar sus certificados para que se mantengan consistentes con este lote eligiendo la opción \"Guardar y actualizar certificados\".", "create": "<PERSON><PERSON>r certificados", "create_certificates": "<PERSON><PERSON>r certificados", "create_certificates_explanation": "¿Está seguro de que desea crear los certificados {{nbCerts}} de este lote?", "create_certificates_title": "<PERSON><PERSON>r certificados de {{name}}", "delete_explanation": "¿Está seguro de que desea eliminar este lote? Esto revocará todos sus certificados {{nbCerts}} y los eliminará. Esta operación no es reversible.", "delete_title": "Eliminar lote {{name}}", "duplicate_in_file": "MAC duplicado encontrado en la fila {{firstRow}} y {{secondRow}}: {{mac}}", "emails_to_notify": "Correos electrónicos para notificar cuando se complete esta tarea", "error_push": "Error al iniciar el trabajo de cambios de inserción: {{e}}", "general_error_treating_file": "Error general al tratar el archivo: asegúrese de que esté en formato .CSV, contiene solo una columna que no tiene encabezado.", "invalid_mac": "MAC no válido en la fila {{row}}: {{mac}}", "mac_count_title": "{{nb}}  MAC forman parte actualmente de este lote", "nb_macs": "{{nb}} MAC", "need_devices": "¡Necesitas tener al menos un certificado para crear!", "parsing_error": "Error de análisis en la fila {{row}}: {{e}}", "phones_to_notify": "Números de teléfono para notificar cuando se complete la tarea", "push_changes": "Empujar cambios", "push_changes_explanation": "¿Está seguro de que desea enviar la información del lote a todos los certificados de este lote?", "revoke_explanation": "¿Está seguro de que desea revocar este lote? Esto revocará todos sus {{nbCerts}} certificados. Esta operación no es reversible.", "revoke_title": "Revocar lote {{name}}", "save_and_change": "Guardar y actualizar certificados", "success_push": "¡Comenzó con éxito el trabajo de cambios de inserción! Número de trabajo: {{job}}", "title": "Lote"}, "certificates": {"certificate": "Certificado", "common_names_explanation": "Necesita un archivo .CSV de una sola columna sin nombre que contenga 12 MAC de dispositivo de dígitos HEX.", "device": "Dispositivo", "device_macs": "MAC del dispositivo", "domain_name": "Nombre de dominio", "error_fetching": "Error al obtener certificados: {{e}}", "error_revoke": "Error al intentar revocar el certificado: {{e}}", "expires_on": "Expira el", "filename": "Nombre del archivo", "invalid_domain": "Los formatos aceptados son: dominio.dominio_nivel_superior o sub_dominio.dominio._dominio_nivel_superior", "invalid_mac": "Debe tener 12 caracteres hexadecimales", "invalid_redirector": "Los formatos aceptados son: ejemplo.com, ejemplo.com:16000", "mac_address": "Dirección MAC", "macs": "MAC", "manufacturer": "Fabricante", "model": "<PERSON><PERSON>", "redirector": "redirector", "revoke": "REVOCAR", "revoke_count": "Recuento de revocaciones", "revoke_warning": "¿Está seguro de que desea revocar este certificado?", "server": "<PERSON><PERSON><PERSON>", "successful_revoke": "¡Certificado revocado con éxito!", "title": "Certificados"}, "commands": {"abort_command_explanation": "¿Está seguro de que desea dejar de esperar el resultado de este comando?", "abort_command_title": "Comando de cancelación", "active_scan": "Escaneo activo", "blink": "<PERSON><PERSON><PERSON><PERSON>", "blink_error": "Error al enviar el comando de parpadeo: {{e}}", "blink_success": "¡Comando de parpadeo enviado con éxito!", "channel": "Canal", "confirm_reset": "Iniciar reinicio del dispositivo #{{serialNumber}}", "connect": "Conectar", "rtty": "Rtty", "execution_time": "Tiempo de ejecución", "factory_reset": "Dispositivo de restablecimiento de fábrica", "factory_reset_error": "Error al intentar restablecer el dispositivo de fábrica: {{e}}", "factory_reset_success": "¡Restablecimiento de fábrica del dispositivo iniciado con éxito!", "factory_reset_warning": "¿Está seguro de que desea restablecer de fábrica este dispositivo? Esta acción no es reversible", "firmware_upgrade": "Actualización de firmware", "firmware_upgrade_error": "Error al intentar actualizar el firmware del dispositivo: {{e}}", "firmware_upgrade_success": "¡Actualización del dispositivo iniciada con éxito!", "image_date": "<PERSON><PERSON>", "keep_redirector": "¿Mantener el redirector?", "other": "comandos", "override_dfs": "Anular DFS", "reboot": "Reiniciar", "reboot_description": "¿Quieres reiniciar este dispositivo?", "reboot_error": "Error al enviar el comando de reinicio: {{e}}", "reboot_success": "¡Comando de reinicio enviado con éxito!", "revision": "revisión", "scan": "Escanear", "signal": "Se<PERSON>l", "upgrade": "<PERSON><PERSON><PERSON>", "wifiscan": "Escaneo Wifi", "wifiscan_error": "Error al intentar escanear el dispositivo: {{e}}", "wifiscan_error_1": "Su radio 5G está en un canal de radar, debe habilitar \"Anular DFS\" para permitir el escaneo de todos los canales 5G"}, "common": {"actions": "Comportamiento", "address_search_autofill": "Busque ubicaciones para completar automáticamente los campos a continuación", "alert": "<PERSON><PERSON><PERSON>", "all": "TODOS", "assign": "<PERSON><PERSON><PERSON>", "avg": "promedio", "back": "atrás", "base_information": "Información base", "by": "Por", "cancel": "<PERSON><PERSON><PERSON>", "claim": "Reclamación", "close": "<PERSON><PERSON><PERSON>", "columns": "Columnas", "command": "Mando", "completed": "terminado", "confirm": "Confirmar", "connected": "Conectado", "copied": "Copiado", "copy": "<PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON>", "create_new": "<PERSON><PERSON><PERSON> nuevo", "created": "creado", "creator": "<PERSON><PERSON><PERSON>", "custom": "Personalizado", "daily": "diario", "date": "<PERSON><PERSON>", "day": "Día", "days": "días", "default": "Defecto", "defaults": "Valores predeterminados", "description": "Descripción", "details": "Detalles", "device_details": "Detalles del dispositivo", "discard_changes": "¿Descartar los cambios?", "disconnected": "Desconectado", "display_name": "Nombre para mostrar", "download": "<PERSON><PERSON><PERSON>", "download_instructions": "¡Descarga Exitosa! Si no puede encontrar el archivo, confirme que está permitiendo las descargas desde este sitio web", "duplicate": "Duplicar", "edit": "<PERSON><PERSON>", "email": "Email", "empty_list": "Lista vacía", "end": "Fin", "entries_one": "entrada", "entries_other": "entradas", "error": "Error", "error_claiming_obj": "Error al reclamar {{obj}}", "error_download": "Error al <PERSON>ar descargar: {{e}}", "errors": "<PERSON> errores", "exit_fullscreen": "salida", "export": "Exportar", "finished": "terminado", "fullscreen": "<PERSON><PERSON><PERSON>", "general_error": "Error al conectar con el servidor. Consulte a su administrador.", "general_info": "Información general", "go_back": "Regresa", "go_to_map": "<PERSON><PERSON> al mapa", "hide": "Esconder", "hourly": "<PERSON><PERSON> hora", "identification": "identificación", "inherit": "<PERSON><PERSON>", "language": "idioma", "last_use": "Utilizado por última vez", "lifetime": "Toda la vida", "locale": "lugar", "logout": "<PERSON><PERSON><PERSON>", "main": "Principal", "make_higher_priority": "Dar mayor prioridad", "make_lower_priority": "Hacer una prioridad más baja", "manage": "Gestionar", "manual": "Manual", "manufacturer": "Fabricante", "map": "Mapa", "max": "Max", "min": "Min", "miscellaneous": "<PERSON><PERSON><PERSON>", "mode": "Modo", "model": "<PERSON><PERSON>", "modified": "Modificado", "monthly": "<PERSON><PERSON><PERSON>", "months": "Meses", "my_account": "Mi cuenta", "name": "Nombre", "name_error": "El nombre debe tener menos de 50 caracteres", "next": "Siguient<PERSON>", "no": "No", "no_addresses_found": "No se encontraron direcciones", "no_clients_found": "No se encontraron clientes", "no_devices_found": "No se encontraron dispositivos", "no_items_yet": "Aún no hay elementos", "no_obj_found": "No se encontró {{obj}} ", "no_records_found": "No se encontrarón archivos", "no_statistics_found": "No se encontraron estadísticas", "no_last_statistics_found": "No se encontraron estadísticas recientes", "none": "Ninguna", "not_found": "404 No encontrado", "note": "<PERSON>a", "notes": "Notas", "of": "de", "password": "Contraseña", "preview": "Avance", "quarterly": "Trimestral", "redirector": "redirector", "refresh": "<PERSON><PERSON><PERSON><PERSON>", "remove": "retirar", "remove_claim": "<PERSON><PERSON><PERSON> reclamo", "reset": "Reiniciar", "revoked": "Revocado", "save": "<PERSON><PERSON>", "search": "Buscar", "seconds": "<PERSON><PERSON><PERSON>", "select_all": "<PERSON><PERSON> todo", "select_value": "Selecciona valor", "sending": "Enviando", "sent_code": "¡Código enviado!", "show": "Show", "size": "<PERSON><PERSON><PERSON>", "start": "comienzo", "start_time": "hora de inicio", "end_time": "hora de finalización", "started": "<PERSON><PERSON><PERSON><PERSON>", "state": "Estado", "status": "Estado", "stop_editing": "<PERSON><PERSON> <PERSON>", "submitted": "<PERSON>ado", "success": "Éxito", "successfully_claimed_obj": "Re<PERSON>lamado con éxito {{count}} {{obj}}", "sync": "Sincronizar", "test": "Prueba", "theme": "<PERSON><PERSON>", "time": "hora", "timestamp": "marca de tiempo", "type": "Tipo", "type_for_options": "Escriba el valor que necesita crear ...", "unknown": "Desconocido", "use_file": "Usar archivo", "value": "Valor", "variable": "Variable", "view": "<PERSON>er", "view_details": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "view_in_gateway": "Ver en controlador", "view_json": "Ver JSON", "warning": "Advertencia", "warnings": "Advertencias", "yearly": "<PERSON><PERSON>", "yes": "Sí", "your_new_note": "tu nueva nota"}, "configurations": {"add_interface": "Añadir interfaz", "add_radio": "Agregar radio", "add_ssid": "Agregar SSID", "add_subsection": "Agregar subsección", "advanced_settings": "<PERSON><PERSON><PERSON><PERSON>", "affected_explanation_one": "Hay {{count}} dispositivos afectados por esta configuración", "affected_explanation_other": "Hay {{count}} dispositivos afectados por esta configuración", "applied_configuration": "Configuración aplicada", "cant_delete_explanation": "No se puede eliminar esta configuración porque la está utilizando al menos un dispositivo, lugar o entidad. Puede ver cuáles son haciendo clic en el botón junto a \"En uso\" en el formulario de esta configuración", "cant_delete_explanation_simple": "No se puede eliminar esta configuración porque la está utilizando al menos un dispositivo, lugar o entidad. Puede ver cuáles son yendo a la página de configuración", "configuration_json": "JSON de configuración", "configuration_push_result": "Resultado de envío de configuración", "configuration_sections": "Secciones de configuración", "delete_interface": "Eliminar interfaz", "delete_radio": "Eliminar radio", "delete_ssid": "Eliminar SSID", "device_configurations": "Configuraciones de dispositivos", "device_types": "Tipos de dispositivos", "dhcp_snooping": "Espionaje de DHCP", "error_pushes_one": "Error (podría deberse a una mala configuración): {{count}}", "error_pushes_other": "Errores (pueden deberse a una mala configuración): {{count}}", "expert_name": "Modo experto", "expert_name_explanation": "Puede usar el modo experto para modificar directamente su configuración, incluida la adición de valores que actualmente no son compatibles con la interfaz de usuario. Utilice este formato: { \"interfaces\": [ ... ], \"radios\": { ... }, ...etc }", "explanation": "Explicación", "firewall": "cortafuegos", "firmware_upgrade": "Actualización de firmware", "globals": "globales", "health": "salud", "hostapd_warning": "Parámetro de URL, ej.: prueba=valor", "import_file": "Importar configuración desde archivo", "import_file_explanation": "Puede utilizar la siguiente opción para importar un archivo JSON de configuración, con un contenido de este formato:\n{\n     \"interfaces\": [ ... ],\n     \"radios\": { ... },\n     ...etc\n}", "import_warning": "ADVERTENCIA: Esta operación sobrescribirá todas las secciones de configuración que ya haya creado.", "imported_configuration": "Configuración importada", "in_use_title": "{{name}} en uso", "interfaces": "Interfaces", "network": "red", "interfaces_instruction": "Utilice una cadena JSON válida en el siguiente formato: { \"interfaces\": [] }.", "invalid_resource": "Recurso no válido o eliminado", "metrics": "M<PERSON><PERSON><PERSON>", "no_resource_selected": "Ningún recurso seleccionado", "notification_details": "Actualizado: {{success}}, <PERSON><PERSON>ando <PERSON>: {{warning}}, Errores: {{error}}", "one": "Configuración", "push_configuration": "Configuración de inserción", "push_configuration_error": "Error al intentar enviar la configuración al dispositivo: {{e}}", "push_configuration_explanation": "Configuración no enviada, código de error {{code}}", "push_success": "¡Se verificó la configuración y ahora el controlador inició un comando \"Configurar\"!", "radio_limit": "Has alcanzado la cantidad máxima de radios (5). Necesita eliminar una de las bandas activadas para agregar una nueva", "radios": "Radios", "rc_only": "Solo candidatos de lanzamiento", "save_warnings": "¿Está seguro de que desea guardar su configuración?", "services": "<PERSON><PERSON><PERSON>", "special_configuration": "Configuración específica del dispositivo", "start_special_creation": "Crear configuración para este dispositivo", "statistics": "estadística", "successful_pushes_one": "<PERSON><PERSON><PERSON> exitoso: {{count}}", "successful_pushes_other": "<PERSON><PERSON><PERSON><PERSON> exitosos: {{count}}", "third_party": "<PERSON><PERSON><PERSON>", "third_party_instructions": "Utilice una cadena JSON válida en el siguiente formato: { \"value_name\": \"value\" }.", "title": "Configuraciones", "unit": "Unidad", "system": "Sistema", "view_affected_devices": "Ver dispositivos afectados", "view_in_use": "Ver en uso", "warning_pushes_one": "Esperando a que los dispositivos se conecten: {{count}}", "warning_pushes_other": "Esperando a que los dispositivos se conecten: {{count}}", "weight": "Peso", "wifi_bands_max": "No puede haber más de 16 SSID usando esta banda wifi", "wifi_frames": "<PERSON>", "multi_psk_key_exsist": "La clave ya existe"}, "contacts": {"access_pin": "PIN de acceso", "claim_explanation": "Para reclamar contactos puede usar la tabla a continuación", "first_name": "Nombre de pila", "identifier": "Identificador", "initials": "Iniciales", "last_name": "Apellido", "mobiles": "MOBILES", "one": "Contacto", "other": "Contactos", "phones": "Los telefonos", "primary_email": "Correo electrónico principal", "salutation": "saludo", "secondary_email": "Email secundario", "title": "<PERSON><PERSON><PERSON><PERSON>", "to_claim": "Contactos para reclamar", "visual": "Visual"}, "controller": {"configurations": {"create_success": "¡Configuración creada!", "delete_success": "¡La configuración ahora está eliminada!", "title": "Configuraciones predeterminadas", "update_success": "¡Configuración actualizada!"}, "configure": {"invalid": "Su nueva configuración debe ser JSON válido", "success": "La nueva configuración ahora se está implementando en el dispositivo", "title": "Configurar a través de CLI", "warning": "Tenga cuidado: solo se realizarán pruebas mínimas en esta configuración"}, "crud": {"choose_time": "<PERSON> de tiempo personalizado", "clear_time": "Tiempo claro", "delete_success_obj": "Eliminado {{obj}}"}, "dashboard": {"associations": "Asociaciones", "associations_explanation": "Todas las asociaciones conectadas actuales (o UE)", "certificates": "Certificados", "certificates_explanation": "Estado de los certificados de los dispositivos actualmente conectados", "commands": "comandos", "commands_explanation": "Todos los comandos ejecutados", "device_dashboard_refresh": "Nuevas estadísticas de conexión", "device_types": "Tipos de dispositivos", "device_types_explanation": "Tipos de dispositivos de todos los dispositivos que apuntan a este controlador", "devices_explanation": "Todos los dispositivos apuntan hacia este punto final del controlador", "error_fetching": "Error al obtener el panel", "expand": "Expandir", "last_ping_explanation": "Cuando se recopilaron estos datos", "memory": "Uso de memoria", "memory_explanation": "Dispositivos conectados actualmente con la cantidad correspondiente de memoria utilizada", "no_certificate": "Sin certificado", "not_connected": "No conectado", "others": "<PERSON><PERSON><PERSON>", "overall_health": "Salud en general", "overall_health_explanation": "Salud promedio de todos los dispositivos actualmente conectados de los cuales recibimos datos de salud. El cálculo exacto es: (Dispositivos=100% * 100 + Dispositivos>90 * 95 + Dispositivos>60 * 75 + Dispositivos<60 * 30) / Dispositivos conectados", "overall_health_explanation_pie": "La cantidad de dispositivos con un porcentaje de salud dentro de estas categorías", "serial_mismatch": "Discrepancia de serie", "status": "Estado", "status_explanation": "Estado de todos los dispositivos que apuntan a este punto final del controlador", "unknown_status": "Estado no reconocido", "unrecognized": "Poco reconocido", "uptimes": "", "uptimes_explanation": "Dispositivos conectados actualmente con los tiempos de actividad correspondientes", "vendors": "Vendedores", "vendors_explanation": "Proveedores de los dispositivos que apuntan a este controlador", "verified": "Verificado"}, "devices": {"add_blacklist": "Agregar número de serie", "added": "Adicional", "added_blacklist": "¡Número de serie agregado a la lista negra!", "average_uptime": "Tiempo de actividad promedio", "blacklist": "Lista negra", "blacklist_update": "Actualizar registro {{serialNumber}} ", "by": "Por", "capabilities": "capacidades", "command_one": "Mando", "commands": "comandos", "complete_data": "<PERSON>tos completos", "config_id": "ID de configuración", "connecting": "<PERSON><PERSON><PERSON><PERSON>", "connection_changes": "Estados de conexión", "delete_blacklist": "¡Se eliminó el número de serie de la lista negra!", "delete_health_explanation": "Esto eliminará de forma permanente todas las comprobaciones de estado antes de la fecha que elija", "delete_logs_explanation": "Esto eliminará permanentemente todos los registros anteriores a la fecha que elija", "error_code": "c<PERSON><PERSON> de error", "executed": "ejecutado", "finished_reboot": " ¡{{serialNumber}} acaba de terminar de reiniciarse!", "finished_upgrade": " ¡{{serialNumber}} ha terminado de actualizarse!", "from_to": "De {{from}} a {{to}}", "healthchecks": "Chequeos de salud", "last_modified": "Última modificación:", "last_upgrade": "Última actualización", "localtime": "Hora local", "logs": "registros", "new_statistics": "Nuevas estadísticas", "no_more_available": "<PERSON><PERSON> recuperado", "reason": "Razón", "results": "Resul<PERSON><PERSON>", "sent_upgrade_to_latest": "Se envió el comando 'Actualizar a la última' al dispositivo", "severity": "Gravedad", "show_more": "Mostrar más", "started_reboot": " ¡{{serialNumber}} apague para reiniciar!", "started_upgrade": " ¡{{serialNumber}} simplemente apague para iniciar la actualización!", "trace": "Rastro", "trace_description": "Lanzar un rastreo remoto de este dispositivo por una duración específica o por una cantidad de paquetes", "update_success": "¡Dispositivo actualizado!", "updated_blacklist": "¡Lista negra actualizada!"}, "firmware": {"devices_explanation": "Dispositivos que han apuntado hacia este servidor de firmware. Esto podría explicar las discrepancias entre este número y el tablero del dispositivo.", "endpoints": "Puntos finales", "endpoints_explanation": "Todos los puntos finales que apuntarán hacia este servidor de firmware", "firmware_age": "Edad del firmware", "firmware_age_explanation": "Antigüedad media del firmware de los dispositivos de los que tenemos esos datos", "latest": "Último firmware instalado", "old_firmware": "Firmware antiguo", "ouis_explanation": "OUI de dispositivos que se han conectado a este servidor de firmware", "outdated_one": "Firmware {{count}} día de antigüedad", "outdated_other": "Firmware de {{count}} días de antigüedad", "outdated_unknown": "Firmware de antigüedad desconocida", "release": "Lanzamiento", "show_dev_releases": "Lanzamientos de desarrollo", "status_explanation": "Estado de conexión de los dispositivos que se han conectado a este servidor de firmware", "unrecognized": "Firmware no reconocido", "unrecognized_firmware": "Firmware no reconocido", "unrecognized_firmware_explanation": "Firmware que utilizan actualmente los dispositivos y no es reconocido por este servidor de firmware", "up_to_date": "Dispositivos actualizados", "up_to_date_explanation": "Dispositivos que utilizan el último software disponible disponible para ellos"}, "provisioning": {"title": "Aprovisionamiento"}, "queue": {"title": "Cola de eventos"}, "radius": {"calling_station_id": "Estación", "disconnect": "desconectar", "disconnect_success": "¡Sesión de radio desconectada!", "input_octets": "entrada", "output_octets": "salida", "radius_clients": "Clientes de radio", "session_time": "Tiempo de sesión", "username": "Nombre de usuario"}, "stats": {"load": "Carga (1 | 5 | 15 m.)", "seconds_ago": " Hace {{s}} segundos", "used": "Usado"}, "telemetry": {"duration": "Duración", "interval": "intervalo", "kafka": "Kafka", "kafka_success": "¡La telemetría de Kafka ya ha comenzado!", "last_update": "Última actualización", "minutes": "<PERSON><PERSON><PERSON>", "need_types": "Debe seleccionar al menos un tipo", "output": "<PERSON><PERSON>", "seconds_ago": " Hace{{seconds}} segundos", "title": "Telemetria", "types": "Los tipos", "websocket": "WebSocket"}, "trace": {"down": "ABAJO", "download": "<PERSON><PERSON><PERSON>", "duration": "Duración", "network": "Red", "packets": "<PERSON><PERSON><PERSON>", "success": "Rast<PERSON>o completado en el dispositivo #{{serialNumber}}. Ya puedes descargar el resultado.", "up": "Arriba", "wait": "¿Esperar los resultados?"}, "wifi": {"active_ms": "Activo", "busy_ms": "Ocupado", "channel_width": "ancho de canal", "mode": "Modo", "noise": "<PERSON><PERSON><PERSON>", "receive_ms": "Recibir", "rx_rate": "Tasa de prescripción", "station": "Estación", "tx_rate": "Tasa de transmisión", "vendor": "<PERSON><PERSON><PERSON>", "wifi_analysis": "<PERSON><PERSON><PERSON><PERSON> de Wi-Fi"}}, "crud": {"add": "<PERSON><PERSON><PERSON>", "confirm_cancel": "¿Está seguro de que desea descartar los cambios que ha realizado?", "confirm_delete_obj": "¿Está seguro de que desea eliminar esto {{obj}}?", "create": "<PERSON><PERSON><PERSON>", "create_object": "<PERSON><PERSON>r {{obj}}", "delete": "Bo<PERSON>r", "delete_confirm": "¿Está seguro de que desea eliminar esto {{obj}}?", "delete_obj": "Eliminar {{obj}}", "edit": "<PERSON><PERSON>", "edit_obj": "Editar {{obj}}", "error_create_obj": "Error al crear {{obj}}: {{e}}", "error_delete_obj": "Error al eliminar {{obj}}: {{e}}", "error_fetching_obj": "Error al obtener {{obj}}: {{e}}", "error_revoke_obj": "Error al revocar {{obj}}: {{e}}", "error_update_obj": "Error al actualizar {{obj}}: {{e}}", "success_create_obj": "¡Creado con éxito {{obj}}!", "success_delete_obj": "¡Eliminó con éxito {{obj}}!", "success_revoke_obj": "¡Revocado con éxito {{obj}}!", "success_update_obj": "¡Actualizado con éxito {{obj}}!"}, "devices": {"all": "TODOS", "associations": "Asociaciones", "certificate_expires_in": "El certificado caduca en", "certificate_expiry": "Cert. Expira en", "connected": "Conectado", "crash_logs": "Registros de fallas", "create_errors": "errores al intentar crear dispositivos", "create_success": " dispositivos creados con éxito", "current_firmware": "Firmware actual", "device_type_not_found": "Tipo de dispositivo no reconocido", "duplicate_serial": "Número de serie duplicado dentro del archivo", "error_rtty": "Error al intentar conectarse al dispositivo: {{e}}", "file_errors": "dispositivos problemáticos", "found_assigned": "dispositivos ya asignados", "found_not_assigned": "dispositivos ya existentes pero ahora propiedad", "import_batch_tags": "Importar dispositivos", "import_device_warning": "Asegúrese de que no haya espacios adicionales al principio o al final de ningún valor a menos que sea parte del valor deseado", "import_explanation": "Para importar dispositivos de forma masiva, debe usar un archivo CSV con las siguientes columnas: Número de serie, Tipo de dispositivo, Nombre, Descripción, Nota", "invalid_serial_number": "Número de serie no válido (debe tener 12 caracteres HEX)", "logs_one": "In<PERSON><PERSON>", "new_devices": "Nuevos dispositivos", "no_model_image": "No se encontró ninguna imagen de modelo", "not_connected": "No conectado", "not_found_gateway": "Error: el dispositivo aún no se ha conectado a la puerta de enlace", "notifications": "notificaciones de dispositivos", "one": "Dispositivo", "reassign_already_owned": "¿Reasignar dispositivos que ya existen y son propiedad de otra entidad/lugar/suscriptor?", "reboot_logs": "Reiniciar registros", "restricted": "Restringido", "restricted_overriden": "Este es un dispositivo restringido, pero está en modo de desarrollo. Actualmente se ignoran todas las restricciones.", "restrictions_overriden_title": "MODO DE DESARROLLO", "sanity": "Cordura", "start_import": "Iniciar la importación de dispositivos", "test_batch": "Datos de importación de prueba", "test_results": "Resultados de la prueba", "title": "Dispositivos", "treating": "Pruebas:", "unassigned_only": "Solo sin asignar", "update_error": "errores al intentar actualizar dispositivos", "update_success": "dispositivos actualizados con éxito"}, "entities": {"active": "Activo", "add_configurations": "Agregar configuraciones", "add_ips": "Agregar direcciones IP", "add_ips_explanation": "Puede agregar direcciones IPv4 o IPv6 en los siguientes formatos", "api_key": "Clave API", "cant_delete_explanation": "No se puede eliminar esta entidad porque tiene entidades y/o lugares secundarios. Debe eliminar todos los elementos secundarios de esta entidad antes de eliminarla.", "claim_device_explanation": "Para reclamar dispositivos, puede usar la tabla a continuación. Si un dispositivo ya fue reclamado por otra entidad o lugar, también lo desasignaremos antes de asignarlo a esta entidad.", "client_enrollment_profile": "Perfil de inscripción del cliente", "create_child_entity": "Crear entidad secundaria", "create_root": "Crear entidad raíz", "create_root_explanation": "Ingrese la información necesaria para crear la entidad raíz de su servicio de aprovisionamiento. Esta información se puede modificar después de la creación.", "current_state": "Estado Actual", "default_redirector": "Redirector predeterminado", "devices_to_claim": "Nuevos dispositivos para reclamar", "devices_under_root": "Los dispositivos no se pueden crear directamente bajo la entidad raíz. Cree nuevas entidades o lugares y cree dispositivos debajo de ellos.", "enter_ips": "Ingrese las IP que desea agregar aquí", "entity": "Entidad", "error_sync": "Error al intentar iniciar la sincronización de {{name}}: {{e}}", "failed_test": "Pruebas fallidas con credenciales de DigiCert, verifique la información de su entidad", "initial_state": "Estado inicial", "ip_cidr": "IP/número (ejemplo: 10.0.0.0/8)", "ip_detection": "Detección de IP", "ip_list": "Lista: IP, IP IP", "ip_range": "Rango: IP-IP", "ip_single_address": "Dirección única: IP", "one": "Entidad", "organization": "Organización", "server_enrollment_profile": "Perfil de inscripción del servidor", "status": "Estado", "sub_one": "subentidad", "sub_other": "subentidades", "success_sync": "¡Inició con éxito la sincronización de {{name}}!", "success_test": "¡La prueba de las credenciales DigiCert de esta entidad fue exitosa!", "suspended": "Suspendido", "sync_explanation": "¿Le gustaría sincronizar esta entidad? Esto puede tomar un tiempo dependiendo de la cantidad de certificados que pertenezcan a esta entidad.", "sync_title": "sincronizar {{name}}", "test_digicert_creds": "Credenciales de prueba", "title": "entidades", "tree": "Árbol de entidades", "update_success": "¡Entidad actualizada!", "venues_under_root": "Los lugares no se pueden crear directamente bajo la entidad raíz"}, "firmware": {"confirm_default_data": "Confirme la información a continuación y haga clic en 'Confirmar' una vez que esté listo para comenzar el proceso", "create_success": "¡Se crearon nuevas configuraciones de firmware predeterminadas!", "db_update_warning": "Esta operación se realiza automáticamente todos los días de forma automática sin necesidad de utilizar esta actualización manual. La actualización de esta base de datos puede tardar hasta 25 minutos", "default_created_error_one": "{{count}} error al intentar crear una nueva configuración", "default_created_error_other": "{{count}} errores al intentar crear una nueva configuración", "default_created_one": "{{count}} configuración de firmware predeterminada creada", "default_created_other": "{{count}} ajustes de firmware predeterminados creados", "default_found_one": "Se encontró una revisión válida para el tipo de dispositivo {{count}} ", "default_found_other": "Se encontraron revisiones válidas para {{count}} tipos de dispositivos", "default_mass_delete_success_one": "¡Se eliminó {{count}} configuración de firmware predeterminada!", "default_mass_delete_success_other": "¡Se eliminaron {{count}} configuraciones de firmware predeterminadas!", "default_not_found_one": "No hay versiones de firmware válidas para el tipo de dispositivo {{count}} ", "default_not_found_other": "No hay versiones de firmware válidas para {{count}} tipos de dispositivos", "default_title": "", "default_update_success": "¡Firmware predeterminado actualizado para {{deviceType}}!", "delete_success": "¡Configuración de firmware predeterminada eliminada!", "edit_default_title": "Este es el firmware actual que se utiliza como versión mínima para los nuevos AP de tipo {{deviceType}}. Si un nuevo AP {{deviceType}} se conecta a la puerta de enlace, se actualizará automáticamente a esta versión.", "fetching_defaults": "Obteniendo todo el firmware disponible para los tipos de dispositivos seleccionados...", "last_db_update_modal": "Base de datos de firmware", "last_db_update_title": "Base de datos", "one": "Firmware", "select_default_device_types": "Seleccione todos los tipos de dispositivos a los que desea apuntar con esta nueva regla de firmware predeterminada. Si no puede encontrar el tipo de dispositivo deseado, significa que ya tienen una regla aplicada.", "select_default_revision": "Ahora puede seleccionar la revisión mínima a la que desea que se dirijan sus tipos de dispositivos", "start_db_update": "Iniciar actualización de la base de datos", "started_db_update": "Actualización de la base de datos iniciada, esta operación debería tardar hasta 25 minutos en completarse", "update_success": "¡Información de firmware predeterminada guardada!"}, "footer": {"powered_by": "energizado por", "version": "Versión"}, "form": {"captive_web_root_explanation": "Utilice únicamente archivos .tar (no archivos comprimidos como .targz, por ejemplo)", "certificate_file_explanation": "Utilice un archivo .pem que comience con \"-----BEGIN CERTIFICATE-----\" y termine con \"-----END CERTIFICATE-----\"", "invalid_alphanumeric_with_dash": "Caracteres aceptados. son solo alfan<PERSON><PERSON><PERSON><PERSON> (letras y números)", "invalid_cidr": "Dirección IPv4 CIDR no válida. Ejemplo: ***********/12", "invalid_email": "<PERSON><PERSON>", "invalid_file_content": "Contenido de archivo no válido, confirme que tiene un formato válido", "invalid_fqdn_host": "Nombre de host FQDN no válido", "invalid_hostname": "Nombre de host no válido: debe estar compuesto solo de caracteres alfanuméricos y guiones", "invalid_icon_lang": "Idioma no válido, debe estar en un formato de 3 letras (eng, fre, ger, ita, etc.)", "invalid_ieee": "Para este protocolo de encriptación, ieee80211w debe ser 'opcional' u 'requerido'", "invalid_ieee_required": "ieee80211w debe ser 'requerido' para este protocolo de encriptación", "invalid_interfaces": "Cadena JSON de interfaces no válida. Confirme que su valor es: JSON válido y tiene interfaces como su única clave y que el valor de las interfaces es una matriz. Ejemplo: {\"interfaces\": []}", "invalid_ipv4": "Dirección IPv4 no válida (ej.: ***********/16)", "invalid_ipv6": "Dirección IPv6 no válida (ej.: fd00::/64)", "invalid_json": "Cadena JSON no válida", "invalid_lease_time": "¡Valor de tiempo de arrendamiento no válido! Deben estar en el formato digitUnit. Por ejemplo: 6d2h5m, lo que significa 6 días, 2 horas y 5 minutos. Estas son las unidades aceptadas: m, h, d. Si no desea utilizar una unidad, omí<PERSON>a por completo. Entonces, en lugar de decir 0d2h0m, usa 2h", "invalid_mac_uc": "Valor de MAC no válido, por ejemplo: 00:00:5e:00:53:af", "duplicate_mac": "Esta dirección MAC ya existe en la lista", "duplicate_ip": "Esta dirección IP ya existe en la lista", "invalid_password": "Contraseña no válida, consulte la política de contraseñas", "invalid_phone_number": "Numero de telefono invalido", "invalid_phone_numbers": "Uno o más de los números de teléfono no son válidos. Proporciónelos sin símbolos ni espacios, o en este formato: +1(123)123-1234", "invalid_port_range": "Valor de puerto no válido. <PERSON><PERSON> ser mayor que 0 y menor que 65 535. Si usa un rango de puertos, asegúrese de que el segundo puerto sea un número más alto que el primero.", "invalid_port_ranges": "¡Combinación de intervalo de puertos no válida! Asegúrese de que ambos valores de puerto sean del mismo tipo (único o rango). Si son rangos, asegúrese de que ambos cubran la misma cantidad de puertos", "invalid_proto_6g": "Este protocolo de encriptación no se puede usar en un SSID que usa 6G", "invalid_proto_passpoint": "Este protocolo de cifrado no se puede utilizar con un SSID de punto de acceso. Seleccione un protocolo que pueda usar Radius", "invalid_select_ports": "¡Valores incompatibles entre interfaces! Asegúrese de que no haya una combinación de ID de VLAN/PUERTO duplicada entre sus interfaces", "invalid_static_ipv4_d": "Dirección no válida, este rango está reservado para multidifusión (clase D). El primer octeto debe ser 223 o inferior", "invalid_static_ipv4_e": "Dirección no válida, este rango reservado para experimentos (clase E). El primer octeto debe ser 223 o inferior", "invalid_third_party": "Cadena JSON de terceros no válida. Confirme que su valor es un JSON válido", "key_file_explanation": "Utilice un archivo .pem que comience con \"-----BEGIN PRIVATE KEY-----\" y termine con \"-----END PRIVATE KEY-----\"", "max_length": "<PERSON><PERSON><PERSON> máxima de {{max}} caracteres.", "max_value": "Valor máximo de {{max}}", "min_length": "<PERSON><PERSON><PERSON> mínima de {{min}} caracteres.", "min_max_string": "El valor debe tener una longitud entre {{min}} (inclusive) y {{max}} (inclusive)", "min_value": "<PERSON><PERSON> mínimo de {{min}}", "missing_interface_upstream": "Debe tener al menos una interfaz de modo puente (puente de capa 2). En este momento, todas sus interfaces están en modo de enrutamiento (NAT)", "new_email_to_notify": "Nuevo correo electrónico para notificar", "new_phone_to_notify": "Nuevo teléfono para avisar", "not_selected": "No seleccionado", "not_uploaded_yet": "no subido todavía", "pem_file_explanation": "Utilice un archivo .pem", "required": "Necesario", "using_file": "(usando archivo: {{filename}})", "value_recorded_no_filename": "Valor registrado, sin nombre de archivo", "invalid_mac_format": "El formato legal de Mac solo puede contener letras y números minúsculas, con un delimitador de ':'. Por favor, utilice el formato: 00:00:5e:00:53:af"}, "inventory": {"computed_configuration": "Configuración calculada", "dev_class": "Clase de dispositivo", "device_type": "Tipo de dispositivo", "error_reboots": "Error al enviar el comando: {{count}}", "error_remove_claim": "Error al eliminar el reclamo: {{e}}", "error_upgrades": "Error al enviar el comando de actualización: {{count}}", "invalid_serial_number": "Numero de serie invalido. Un número de serie solo debe tener 12 caracteres HEX (A-F, 0-9)", "no_computed": "Sin configuración calculada: deberá asignar una configuración válida para verla", "no_firmware": "No hay firmware disponible para el tipo de dispositivo: {{count}}", "not_connected": "Dispositivo no conectado: {{count}}", "parent": "<PERSON><PERSON>", "serial_number": "Número de serie", "skipped_upgrades": "Actualizaciones omitidas: {{count}}", "success_remove_claim": "Reclamación eliminada con éxito en: {{serial}}", "successful_reboots": "Comenzó a reiniciar: {{count}}", "successful_upgrades": "Actualizaciones exitosas: {{count}}", "tag_one": "Etiqueta", "tags": "Etiquetas de inventario", "title": "Inventario", "warning_reboots": "No conectado: {{count}}", "warning_upgrades": "Dispositivos no conectados: {{count}}", "label": "Etiqueta", "site": "Sitio"}, "jobs": {"error_macs": "MAC con errores", "job": "Trabajo", "job_details": "Detalles del trabajo", "notify_emails": "Notificar correos electrónicos", "notify_sms": "Notificar SMS", "successful_macs": "MAC exitosos", "title": "Trabajos"}, "keys": {"description_error": "La descripción debe tener menos de 64 caracteres", "expire_error": "El vencimiento no puede ser más de un año en el futuro", "expires": "Vence", "max_keys": "Número máximo de claves alcanzado (10)", "name_error": "El nombre debe ser único y tener entre 6 y 20 caracteres alfanuméricos", "one": "Clave API", "other": "<PERSON><PERSON><PERSON> de <PERSON>"}, "locations": {"address_line_one": "Dirección Línea Uno", "address_line_two": "Dirección línea dos", "building_name": "Nombre del edificio", "city": "ciudad", "claim_explanation": "Para reclamar ubicaciones, puede usar la tabla a continuación", "country": "<PERSON><PERSON>", "elevation": "Elevación", "geocode": "Código geográ<PERSON>o", "lat": "Latitud", "longitude": "<PERSON><PERSON><PERSON>", "one": "Ubicación", "other": "Ubicaciones", "postal": "código postal", "state": "Provincia del estado", "title": "Ubicaciones", "to_claim": "Ubicaciones para reclamar", "view_gps": ""}, "login": {"access_policy": "Política de acceso", "change_password_error": "Contraseña rechazada, esta puede ser una contraseña antigua", "change_password_explanation": "Ingrese y confirme su nueva contraseña", "change_your_password": "Cambia la contraseña", "confirm_new_password": "confirmar nueva contraseña", "email_instructions": "Pronto debería recibir un código de 6 dígitos en su dirección de correo electrónico. Si no puede encontrarlo, verifique su carpeta de correo no deseado.", "error_sending_code": "Error al intentar enviar el código: {{e}}", "forgot_password": "¿Se te olvidó tu contraseña?", "forgot_password_instructions": "Ingrese su dirección de correo electrónico para recibir un correo electrónico con las instrucciones para restablecer su contraseña", "forgot_password_successful": "Pronto debería recibir un correo electrónico con las instrucciones para restablecer su contraseña. Asegúrese de verificar su correo no deseado si no puede encontrar el correo electrónico", "forgot_password_title": "Se te olvidó tu contraseña", "google_instructions": "Ingrese el código de 6 dígitos de su aplicación Google Authenticator. Si está a punto de caducar, puede esperar a uno nuevo", "invalid_credentials": "Credenciales no válidas, confirme que está utilizando el correo electrónico y la contraseña correctos.", "invalid_mfa": "¡Codigo invalido! Inténtalo de nuevo", "login_explanation": "Ingrese su correo electrónico y contraseña para iniciar sesión", "new_password": "Nueva contraseña", "password_policy": "Política de contraseñas", "remember_me": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "resend": "Reenviar", "resent_code": "¡Código de reenvío exitoso!", "reset_password": "Restablecer la contraseña", "sign_in": "Registrarse", "sms_instructions": "Debería recibir un código de 6 dígitos en su teléfono pronto. Por favor, introdúzcalo a continuación para iniciar sesión", "suspended_error": "Cuenta suspendida, comuníquese con su administrador", "verification": "Verifica tu inicio de sesión", "waiting_for_email_verification": "Cuenta aún no validada por correo electrónico. Mire su bandeja de entrada o solicite a su administrador que vuelva a enviar una validación.", "welcome_back": "¡Dar una buena acogida!", "your_email": "Tu correo electrónico", "your_new_password": "Tu nueva contraseña", "your_password": "Tu contraseña"}, "logs": {"configuration_upgrade": "Actualización de configuración", "device_firmware_upgrade": "Actualización de firmware", "device_statistics": "Estadísticas del dispositivo", "export": "Exportar", "filter": "Filtrar", "firmware": "Firmware", "global_connections": "Conexiones globales", "level": "<PERSON><PERSON>", "message": "Men<PERSON><PERSON>", "one": "In<PERSON><PERSON>", "receiving_types": "Filtro de notificaciones", "security": "SEGURIDAD", "source": "Fuente", "thread": "<PERSON><PERSON>", "venue_config": "Configuración", "venue_reboot": "Reiniciar", "venue_upgrade": "<PERSON><PERSON><PERSON>"}, "map": {"auto_align": "Alineación automática", "auto_map": "Mapa automático", "by_others": "Mapas de otros", "cumulative_devices": "Dispositivos acumulativos", "default_map": "Mapa predeterminado", "delete_warning": "¿Está seguro de que desea eliminar este mapa? Esta operación no es reversible.", "duplicating": "Mapa duplicado", "my_maps": "<PERSON><PERSON> mapas", "other": "Mapas", "root": "<PERSON><PERSON><PERSON>", "root_node": "Nodo raíz", "set_as_default": "<PERSON><PERSON><PERSON> por defecto", "title": "Mapa", "visibility": "Visibilidad"}, "notification": {"one": "Notificación", "other": "Notificaciones"}, "openroaming": {"pool_strategy": "Estrategia de piscina", "radius_endpoint_one": "Punto final del radio", "radius_endpoint_other": "Puntos finales de radio"}, "operator": {"delete_explanation": "¿Está seguro de que desea eliminar este operador? Esta operación no es reversible.", "delete_operator": "Eliminar operador", "import_location_from_device": "Importar desde otro dispositivo", "one": "OPERADOR", "operator_one": "OPERADOR", "operator_other": "Operadores", "other": "Operadores", "registration_id": "Identificación de Registro"}, "organization": {"my_organization": "MI ORGANIZACION", "title": "Organización"}, "overrides": {"delete_source": "Eliminar todas las anulaciones de {{source}}", "ignore_overrides": "Ignorar anulaciones de configuración", "name_error": "El parámetro ya está definido por su fuente", "one": "Anulación de configuración", "other": "Anulaciones de configuración", "param_name": "parámetro", "param_value": "Valor", "parameter": "parámetro", "reason": "Razón", "reason_error": "Su motivo debe tener menos de 64 caracteres. largo", "source": "Fuente", "tx_power_error": "La potencia Tx debe estar entre 1 y 32", "update_success": "¡Anulaciones de configuración actualizadas!", "value": "Valor"}, "profile": {"about_me": "Sobre mí", "activate": "", "add_new_note": "<PERSON><PERSON><PERSON> nota", "deactivate": "Desactivar", "delete_account": "Eliminar mi perfil", "delete_account_confirm": "Eliminar toda mi información", "delete_warning": "Esta acción no es reversible. Toda la información de su perfil y sus claves API serán eliminadas", "deleted_success": "Su perfil ahora está eliminado, ahora cerraremos su sesión...", "disabled": "Discapacitado", "enabled": "Habilitado", "manage_avatar": "Administrar avatar", "new_password": "Nueva contraseña", "new_password_confirmation": "confirmar nueva contraseña", "your_profile": "<PERSON>"}, "resources": {"configuration_resource": "Recurso", "title": "Recursos", "variable": "Variable"}, "restrictions": {"algo": "Algoritmo de firma", "allowed": "Permitido", "countries": "países permitidos", "developer": "<PERSON><PERSON> desarrollador", "dfs": "Anulación de DFS", "gw_commands": "Comandos de puerta de enlace", "identifier": "Identificador", "key_verification": "Información clave de firma", "restricted": "Restringido", "signed_upgrade": "Solo actualización firmada", "title": "Las restricciones", "tty": "Acceso TTY"}, "roaming": {"account_created": "¡Nueva cuenta creada!", "account_deleted": "¡Cuenta eliminada!", "account_one": "C<PERSON><PERSON>", "account_other": "Cuentas", "certificate_deleted": "Certificado eliminado!", "certificate_one": "Certificado", "certificate_other": "Certificados", "city": "ciudad", "common_name": "Nombre común", "country": "<PERSON><PERSON>", "global_reach": "Alcance global", "global_reach_account_id": "ID de cuenta ", "invalid_certificate": "Certificado inválido", "invalid_key": "Clave privada no válida", "location_details_title": "Ubicación", "organization": "Organización", "private_key": "Llave privada", "province": "Provincia", "state": "Estado"}, "rrm": {"algorithm": "Algoritmo", "algorithm_other": "Algoritmos", "cant_save_custom": "No se pueden crear ni editar configuraciones de RRM personalizadas hasta que se pueda acceder al servidor de RRM. Por favor consulte con su administrador", "cron_error": "Error al analizar la expresión CRON: confirme que es válida", "cron_scheduler": "Programador CRON", "cron_templates": "Plantillas", "no_algos": "No podemos obtener algoritmos RRM en este momento", "no_providers": "No podemos obtener proveedores de RRM en este momento", "param_error": "Sus parámetros no respetan las reglas de este algoritmo. Por favor, mire los ejemplos y detalles del algoritmo.", "parameters": "Parámetros", "vendor": "<PERSON><PERSON><PERSON>", "version": "Versión"}, "script": {"author": "<PERSON><PERSON><PERSON>", "automatic": "Automático", "create_success": "¡El script ahora está creado y listo para usar!", "custom_domain": "<PERSON>inio personaliza<PERSON>", "deferred": "Diferido", "device_title": "<PERSON><PERSON><PERSON><PERSON> g<PERSON>", "diagnostics": "Diagnós<PERSON><PERSON>", "explanation": "Ejecute un script personalizado en este dispositivo y descargue sus resultados", "file_not_ready": "El resultado aún no se ha subido, vuelva más tarde", "file_too_large": "Seleccione un archivo que tenga menos de 500 KB", "helper": "Documentación", "no_script_available": "No hay script disponible para su rol de usuario", "now": "ahora", "one": "<PERSON><PERSON><PERSON>", "other": "<PERSON><PERSON><PERSON>", "restricted": "Usuarios autorizados a ejecutar este script", "schedule_success": "¡Ejecución de script programada!", "signature": "Firma", "started_execution": "Comenzó la ejecución del script, ¡venga más tarde para conocer los resultados!", "timeout": "Se acabó el tiempo", "update_success": "G<PERSON>ón actualizado!", "upload_destination": "Destino de carga de resultados", "upload_file": "Subir archivo", "visit_external_website": "VER DOCUMENTACIÓN", "when": "Programar Ejecucion"}, "service": {"billing_code": "Código de facturación", "billing_frequency": "Frecuencia de facturación", "class_one": "Clase de servicio", "class_other": "Clases de servicio", "cost": "Costo", "one": "Clase de servicio", "other": "Clases de servicio"}, "simulation": {"cancel": "Cancelar simulación", "cancel_explanation": "Detener la simulación y borrar su registro", "cancel_success": "¡Detuvo la simulación y borró su registro!", "client_interval": "Intervalo de cliente", "concurrent_devices": "Dispositivos concurrentes", "controller": "Controlador", "current_live_devices": "Dispositivos activos actuales", "currently_running_one": "Actualmente hay {{count}} simulación en ejecución", "currently_running_other": "Actualmente hay {{count}} simulaciones ejecutándose", "delete_devices_confirm": "¿Está seguro de que desea eliminar todos los dispositivos y sus estadísticas de la puerta de enlace? Esta acción no es reversible", "delete_devices_loading": "Este proceso puede tardar hasta 5 minutos.", "delete_simulation_devices": "BORRAR DISPOSITIVOS", "delete_success": "¡Simulación eliminada!", "duration": "Duración", "error_devices": "Dispositivos de error", "healthcheck_interval": "Intervalo de comprobación de estado", "infinite": "infinito", "keep_alive": "Mantener viva", "mac_prefix": "Prefijo MAC", "mac_prefix_length": "Su prefijo MAC debe tener 6 dígitos hexadecimales válidos (p. ej.: 00112233)", "max_associations": "<PERSON><PERSON>", "max_clients": "<PERSON><PERSON>", "min_associations": "Min. <PERSON>", "min_clients": "<PERSON><PERSON>", "no_sim_running": "No hay ninguna simulación actualmente en ejecución", "one": "Simulación", "other": "Simulaciones", "owner": "Propietario", "realtime_data": "Datos en Tiempo real", "realtime_messages": "Mensajes en tiempo real", "reconnect_interval": "Intervalo de reconexión", "result_delete_success": "¡Resultado eliminado!", "rx": "recibido", "rx_messages": "Mensajes prescritos", "sim_currently_running": "Simulación \"{{sim}}\" en curso", "sim_history": "{{sim}} ejecuciones anteriores", "simulated": "<PERSON><PERSON><PERSON><PERSON>", "start": "Iniciar simulación", "start_success": "¡Ejecución de simulación iniciada!", "state_interval": "Intervalo de estado", "stop": "Detener simulación", "stop_success": "Simulación detenida!", "threads": "Trapos", "time_to_full": "Tiempo para dispositivos completos", "tx": "Transmitido", "tx_messages": "Mensajes TX", "view_previous_runs": "Ver carreras anteriores"}, "statistics": {"last_stats": "Últimas estadísticas", "latest": "Últimas estadísticas", "memory": "Memoria"}, "subscribers": {"billing_contact_info": "Detalles de facturación y contacto", "claim_device_explanation": "Para reclamar dispositivos, puede usar la tabla a continuación. Si un dispositivo ya fue reclamado por un usuario, deberá ir a sus detalles y anular la asignación antes de reclamarlo.", "devices_claimed_one": "{{count}} dispositivo reclamado", "devices_claimed_other": "{{count}} dispositivos reclamados", "devices_to_claim_one": "{{count}} dispositivo para reclamar", "devices_to_claim_other": "{{count}} Dispositivos para reclamar", "error_claiming": "Error al reclamar: {{serials}}", "error_removing_claim": "Error al eliminar reclamo(s) en: {{serials}}", "no_subscribers_found": "No se encontraron suscriptores", "one": "Abonado", "other": "Suscriptores", "reactivate_explanation": "¿Está seguro de que desea reactivar este suscriptor?", "reactivate_title": "Reactivar Suscriptor", "title": "Suscriptores"}, "system": {"advanced": "<PERSON><PERSON><PERSON>", "backend_logs": "Registros de back-end", "configuration": "Configuración", "could_not_retrieve": "Error: no se pudo recuperar la información del sistema {{name}} ", "endpoint": "punto final", "hostname": "Nombre de host", "info": "Información del sistema", "level": "nivel de registro", "logging": "<PERSON><PERSON><PERSON>", "no_log_levels": "Niveles de registro no informados", "os": "sistema operativo", "processors": "Procesadores", "reload_chosen_subsystems": "Recargar subsistemas elegidos", "secrets": "<PERSON><PERSON>", "secrets_create": "Crear secreto", "secrets_one": "secreto", "services": "<PERSON><PERSON><PERSON>", "start": "comienzo", "subsystems": "Subsistemas", "success_reload": "¡Comando de recarga enviado con éxito!", "systems_to_reload": "Elige sistemas para recargar", "title": "Sistema", "update_level_success": "¡Niveles de registro actualizados!", "update_levels": "Actualizar", "uptime": "Tiempo de actividad", "version": "Versión"}, "table": {"columns": "Columnas", "columns_hidden_one": "{{count}} columna oculta", "columns_hidden_other": "{{count}} columnas ocultas", "display_column": "Monitor", "drag_always_show": "No puede ocultar esta columna pero puede cambiar su posición", "drag_explanation": "Arrastre y suelte para reordenar y cambiar la visibilidad de las columnas", "drag_locked": "Esta columna está bloqueada en su posición.", "export_current_page": "Solo página actual", "first_page": "Primera pagina", "go_to_page": "Ir a la página", "hide_column": "Esconder", "last_page": "Ultima pagina", "next_page": "Siguiente página", "page": "<PERSON><PERSON><PERSON><PERSON>", "preferences": "Preferencias de mesa", "previous_page": "Página anterior", "reset": "Reiniciar preferencias", "settings": "<PERSON><PERSON><PERSON><PERSON>"}, "user": {"email_not_validated": "correo electrónico no validado", "error_fetching": "Error al obtener la información del usuario: {{e}}", "password": "Contraseña", "role": "papel", "suspended": "Suspendido", "title": "Usuario"}, "users": {"change_password": "Forzar cambio de contraseña", "email_validation": "VALIDACIÓN DE CORREO ELECTRÓNICO", "error_fetching": "Error al obtener usuarios: {{e}}", "error_sending_validation": "Error al enviar la validación de correo electrónico: {{e}}", "last_login": "Último acceso", "login_id": "Ingresar identificación", "one": "Usuario", "re_validate_email": "Revalidar correo electrónico", "reactivate_user": "Reactivar usuario", "reset_mfa": "Restablecer MFA", "reset_mfa_success": "¡Restablecer correctamente el MFA del usuario!", "reset_password": "Restablecer la contraseña", "reset_password_error": "Error al intentar restablecer la contraseña de usuario: {{e}}", "reset_password_success": "Correo electrónico de restablecimiento de contraseña enviado con éxito a la dirección de correo electrónico del usuario", "role": "papel", "send_validation": "Enviar validación de correo electrónico", "send_validation_explanation": "¿Desea reenviar el enlace de verificación de correo electrónico?", "stop_suspension": "reactivar", "success_sending_validation": "¡Correo electrónico de validación enviado!", "suspend": "Suspender", "suspend_success": "El usuario ahora está suspendido", "suspended": "Suspendido", "title": "Usuarios", "waitiing_for_email_verification": "Correo electrónico no verificado"}, "venues": {"confirm_remove_contact": "¿Quieres eliminar este contacto de este lugar?", "create_child": "Crear lugar secundario", "error_remove_contact": "Error al intentar eliminar el contacto: {{e}}", "error_update_devices": "Error al iniciar la actualización del dispositivo: {{e}}", "go_to_page": "Ir a la página", "one": "<PERSON><PERSON> de encuentro", "reboot_all_devices": "Reiniciar todos los dispositivos", "sub_one": "<PERSON><PERSON><PERSON>", "sub_other": "Subsedes", "subvenues": "Subsedes", "successfully_reboot_devices": "¡Reiniciando {{num}} dispositivos!", "successfully_removed_contact": "¡Contacto eliminado con éxito!", "successfully_update_devices": "¡Actualizando {{num}} dispositivos!", "title": "<PERSON><PERSON>", "update_all_devices": "Actualizar todas las configuraciones de dispositivos", "update_success": "Lugar actualizado!", "upgrade_all_devices": "Actualizar el firmware de todos los dispositivos", "upgrade_all_devices_error": "Error al actualizar dispositivos: {{e}}", "upgrade_all_devices_success": "¡Comenzó con éxito la actualización de dispositivos!", "upgrade_options_available": "Aquí están todas las revisiones disponibles, seleccione la que desea que TODOS los dispositivos de este lugar se actualicen", "use_existing": "Utilizar existente", "use_existing_contacts": "Usar contactos existentes", "use_this_contact": "Usa este contacto"}}