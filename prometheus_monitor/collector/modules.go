package collector

import (
	"bytes"
	"crypto/tls"
	"encoding/json"
	"fmt"
	"gnmi_exporter/config"
	"io/ioutil"
	"log"
	"net/http"
	"path/filepath"
	"reflect"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/openconfig/gnmi/proto/gnmi"
	"github.com/prometheus/client_golang/prometheus"
	"gopkg.in/yaml.v2"
)

// openconfig_platform:components_component_openconfig_platform_transceiver
type OpenconfigPlatform struct {
	powerMap        map[string]transceiverState
	ddmMap          map[string]DDMTransceiverState
	ddmMapMutex     sync.RWMutex
	ampconAddress   string
	targetName      string
	thresholdConfig *ThresholdConfig
	debugMode       bool
}

type transceiverState struct {
	inputPower       float64
	outputPower      float64
	transportType    string
	currentPowerDiff float64
	prevPowerDiff    float64
	rpsuPowerOnState bool // 记录rpsu power on 状态
	rpsuDiff         bool // 记录是否需要告警
	laserTemperature float64
}

type PowerThreshold struct {
	inputPowerMin  float64
	inputPowerMax  float64
	outputPowerMin float64
	outputPowerMax float64
}

type DDMTransceiverState struct {
	SerialNo         string                  `json:"serialNo"`
	SupplyVoltage    float64                 `json:"supplyVoltage"`
	LaserTemperature float64                 `json:"laserTemperature"`
	ChannelsState    map[string]ChannelState `json:"channelsState"`
	WarnThreshold    DDMThreshold            `json:"warnThreshold"`
	AlarmThreshold   DDMThreshold            `json:"alarmThreshold"`
	HasThreshold     bool                    `json:"hasThreshold"`
	IsAbnormal       bool                    `json:"isAbnormal"`
	TransportType    string                  `json:"transportType"`
	TargetName       string                  `json:"targetName"`
	ModuleType       string                  `json:"moduleName"`
	Timestamp        time.Time               `json:"timeStamp"`
}

type ChannelState struct {
	OutputPower      float64 `json:"outputPower"`
	InputPower       float64 `json:"inputPower"`
	LaserBiasCurrent float64 `json:"laserBiasCurrent"`
}

type DDMThreshold struct {
	LaserTemperatureUpper float64 `json:"laserTemperatureUpper"`
	LaserTemperatureLower float64 `json:"laserTemperatureLower"`
	OutputPowerUpper      float64 `json:"outputPowerUpper"`
	OutputPowerLower      float64 `json:"outputPowerLower"`
	InputPowerUpper       float64 `json:"inputPowerUpper"`
	InputPowerLower       float64 `json:"inputPowerLower"`
	LaserBiasCurrentUpper float64 `json:"laserBiasCurrentUpper"`
	LaserBiasCurrentLower float64 `json:"laserBiasCurrentLower"`
	SupplyVoltageUpper    float64 `json:"supplyVoltageUpper"`
	SupplyVoltageLower    float64 `json:"supplyVoltageLower"`
}

type AlertLevel int

const (
	Normal AlertLevel = iota // 0: 正常
	Warn                     // 1: 警告
	Error                    // 2: 错误
)

// 定义阈值映射表
var powerThresholds = map[string]PowerThreshold{
	// CFP系列
	"openconfig-transport-types:CFP": {
		outputPowerMin: -8, outputPowerMax: 2,
		inputPowerMin: -10, inputPowerMax: 0,
	},
	"openconfig-transport-types:CFP2": {
		outputPowerMin: -8, outputPowerMax: 2,
		inputPowerMin: -10, inputPowerMax: 0,
	},
	"openconfig-transport-types:CFP2_ACO": {
		outputPowerMin: -5, outputPowerMax: 5,
		inputPowerMin: -10, inputPowerMax: 0,
	},
	"openconfig-transport-types:CFP4": {
		outputPowerMin: -8, outputPowerMax: 2,
		inputPowerMin: -10, inputPowerMax: 0,
	},

	// QSFP系列
	"openconfig-transport-types:QSFP": {
		outputPowerMin: -8, outputPowerMax: 2,
		inputPowerMin: -10, inputPowerMax: 0,
	},
	"openconfig-transport-types:QSFP28": {
		outputPowerMin: -8, outputPowerMax: 2,
		inputPowerMin: -10, inputPowerMax: 0,
	},
	"openconfig-transport-types:QSFP28_DD": {
		outputPowerMin: -8, outputPowerMax: 2,
		inputPowerMin: -10, inputPowerMax: 0,
	},
	"openconfig-transport-types:QSFP56": {
		outputPowerMin: -8, outputPowerMax: 2,
		inputPowerMin: -10, inputPowerMax: 0,
	},
	"openconfig-transport-types:QSFP56_DD": {
		outputPowerMin: -8, outputPowerMax: 2,
		inputPowerMin: -10, inputPowerMax: 0,
	},
	"openconfig-transport-types:QSFP56_DD_TYPE1": {
		outputPowerMin: -8, outputPowerMax: 2,
		inputPowerMin: -10, inputPowerMax: 0,
	},
	"openconfig-transport-types:QSFP56_DD_TYPE2": {
		outputPowerMin: -8, outputPowerMax: 2,
		inputPowerMin: -10, inputPowerMax: 0,
	},
	"openconfig-transport-types:QSFP_PLUS": {
		outputPowerMin: -8, outputPowerMax: 2,
		inputPowerMin: -10, inputPowerMax: 0,
	},

	// SFP系列
	"openconfig-transport-types:SFP": {
		outputPowerMin: -9, outputPowerMax: -1,
		inputPowerMin: -17, inputPowerMax: -1,
	},
	"openconfig-transport-types:SFP_PLUS": {
		outputPowerMin: -9, outputPowerMax: -1,
		inputPowerMin: -17, inputPowerMax: -1,
	},
	"openconfig-transport-types:SFP28": {
		outputPowerMin: -8, outputPowerMax: 4,
		inputPowerMin: -12, inputPowerMax: -1,
	},
	"openconfig-transport-types:SFP56": {
		outputPowerMin: -8, outputPowerMax: 4,
		inputPowerMin: -12, inputPowerMax: -1,
	},
	"openconfig-transport-types:SFP_DD": {
		outputPowerMin: -8, outputPowerMax: 4,
		inputPowerMin: -12, inputPowerMax: -1,
	},

	// 其他类型
	"openconfig-transport-types:CPAK": {
		outputPowerMin: -8, outputPowerMax: 2,
		inputPowerMin: -10, inputPowerMax: 0,
	},
	"openconfig-transport-types:CSFP": {
		outputPowerMin: -9, outputPowerMax: -1,
		inputPowerMin: -17, inputPowerMax: -1,
	},
	"openconfig-transport-types:DSFP": {
		outputPowerMin: -8, outputPowerMax: 4,
		inputPowerMin: -12, inputPowerMax: -1,
	},
	"openconfig-transport-types:XFP": {
		outputPowerMin: -8, outputPowerMax: 2,
		inputPowerMin: -10, inputPowerMax: 0,
	},
	"openconfig-transport-types:X2": {
		outputPowerMin: -8, outputPowerMax: 2,
		inputPowerMin: -10, inputPowerMax: 0,
	},
	"openconfig-transport-types:OSFP": {
		outputPowerMin: -8, outputPowerMax: 2,
		inputPowerMin: -10, inputPowerMax: 0,
	},
}

type MetricType string

const (
	SupplyVoltage    MetricType = "supplyVoltage"
	LaserTemperature MetricType = "laserTemperature"
	OutputPower      MetricType = "outputPower"
	InputPower       MetricType = "inputPower"
	LaserBiasCurrent MetricType = "laserBiasCurrent"
)

type ThresholdMapping struct {
	MetricField string
	WarnUpper   string
	WarnLower   string
	AlarmUpper  string
	AlarmLower  string
	AlertName   string
	Description string
}

var thresholdMappings = map[MetricType]ThresholdMapping{
	SupplyVoltage: {
		MetricField: "supplyVoltage",
		WarnUpper:   "SupplyVoltageUpper",
		WarnLower:   "SupplyVoltageLower",
		AlarmUpper:  "SupplyVoltageUpper",
		AlarmLower:  "SupplyVoltageLower",
		AlertName:   "SupplyVoltage",
		Description: "Interface %s Voltage %.2f %s",
	},
	LaserTemperature: {
		MetricField: "laserTemperature",
		WarnUpper:   "LaserTemperatureUpper",
		WarnLower:   "LaserTemperatureLower",
		AlarmUpper:  "LaserTemperatureUpper",
		AlarmLower:  "LaserTemperatureLower",
		AlertName:   "LaserTemperature",
		Description: "Interface %s Temperature %.2f %s",
	},
	OutputPower: {
		MetricField: "outputPower",
		WarnUpper:   "OutputPowerUpper",
		WarnLower:   "OutputPowerLower",
		AlarmUpper:  "OutputPowerUpper",
		AlarmLower:  "OutputPowerLower",
		AlertName:   "OutputPower",
		Description: "Interface %s Channel %s Rx Power %.2f %s",
	},
	InputPower: {
		MetricField: "inputPower",
		WarnUpper:   "InputPowerUpper",
		WarnLower:   "InputPowerLower",
		AlarmUpper:  "InputPowerUpper",
		AlarmLower:  "InputPowerLower",
		AlertName:   "InputPower",
		Description: "Interface %s Channel %s Tx Power %.2f %s",
	},
	LaserBiasCurrent: {
		MetricField: "laserBiasCurrent",
		WarnUpper:   "LaserBiasCurrentUpper",
		WarnLower:   "LaserBiasCurrentLower",
		AlarmUpper:  "LaserBiasCurrentUpper",
		AlarmLower:  "LaserBiasCurrentLower",
		AlertName:   "LaserBiasCurrent",
		Description: "Interface %s Channel %s Laser Bias Current %.2f %s",
	},
}

// 首先定义基础指标和通道指标的分类
var (
	baseMetrics = []MetricType{
		SupplyVoltage,
		LaserTemperature,
	}

	channelMetrics = []MetricType{
		OutputPower,
		InputPower,
		LaserBiasCurrent,
	}
)

// 1. 修改自定义阈值配置结构
type CustomThreshold struct {
	MetricType MetricType `yaml:"metric_type"`
	WarnUpper  float64    `yaml:"warn_upper"`
	WarnLower  float64    `yaml:"warn_lower"`
	AlarmUpper float64    `yaml:"alarm_upper"`
	AlarmLower float64    `yaml:"alarm_lower"`
}

type ThresholdConfig struct {
	CustomThresholds map[MetricType]CustomThreshold `yaml:"custom_thresholds"`
	Debug            bool                           `yaml:"debug"`
}

// 定义警告(Warn)阈值常量
const (
	// 激光温度警告阈值
	WarnLaserTemperatureUpper = 70.0
	WarnLaserTemperatureLower = -5.0

	// 输出功率警告阈值
	WarnOutputPowerUpper = 0.0
	WarnOutputPowerLower = 0.0

	// 输入功率警告阈值
	WarnInputPowerUpper = 0.0
	WarnInputPowerLower = 0.0

	// 激光偏置电流警告阈值
	WarnLaserBiasCurrentUpper = 1000.0
	WarnLaserBiasCurrentLower = -1000.0

	// 供电电压警告阈值
	WarnSupplyVoltageUpper = 3.46
	WarnSupplyVoltageLower = 3.13
)

// 定义告警(Alarm)阈值常量
const (
	// 激光温度告警阈值
	AlarmLaserTemperatureUpper = 75.0
	AlarmLaserTemperatureLower = -10.0

	// 输出功率告警阈值
	AlarmOutputPowerUpper = 1000.0
	AlarmOutputPowerLower = -1000.0

	// 输入功率告警阈值
	AlarmInputPowerUpper = 1000.0
	AlarmInputPowerLower = -1000.0

	// 激光偏置电流告警阈值
	AlarmLaserBiasCurrentUpper = 1000.0
	AlarmLaserBiasCurrentLower = -1000.0

	// 供电电压告警阈值
	AlarmSupplyVoltageUpper = 3.63
	AlarmSupplyVoltageLower = 2.97
)

// 初始化警告阈值
func NewWarnThreshold() DDMThreshold {
	return DDMThreshold{
		LaserTemperatureUpper: WarnLaserTemperatureUpper,
		LaserTemperatureLower: WarnLaserTemperatureLower,
		OutputPowerUpper:      WarnOutputPowerUpper,
		OutputPowerLower:      WarnOutputPowerLower,
		InputPowerUpper:       WarnInputPowerUpper,
		InputPowerLower:       WarnInputPowerLower,
		LaserBiasCurrentUpper: WarnLaserBiasCurrentUpper,
		LaserBiasCurrentLower: WarnLaserBiasCurrentLower,
		SupplyVoltageUpper:    WarnSupplyVoltageUpper,
		SupplyVoltageLower:    WarnSupplyVoltageLower,
	}
}

// 初始化告警阈值
func NewAlarmThreshold() DDMThreshold {
	return DDMThreshold{
		LaserTemperatureUpper: AlarmLaserTemperatureUpper,
		LaserTemperatureLower: AlarmLaserTemperatureLower,
		OutputPowerUpper:      AlarmOutputPowerUpper,
		OutputPowerLower:      AlarmOutputPowerLower,
		InputPowerUpper:       AlarmInputPowerUpper,
		InputPowerLower:       AlarmInputPowerLower,
		LaserBiasCurrentUpper: AlarmLaserBiasCurrentUpper,
		LaserBiasCurrentLower: AlarmLaserBiasCurrentLower,
		SupplyVoltageUpper:    AlarmSupplyVoltageUpper,
		SupplyVoltageLower:    AlarmSupplyVoltageLower,
	}
}

// 定义静默告警类型 (只用于DDM消息)
type SilentAlertType string

const (
	SupplyVoltageSilent SilentAlertType = "supplyVoltage"
)

func NewOpenconfigPlatform(targetName string) Hook {
	var ampconAddress string
	config, err := config.LoadFile([]string{"gnmi.yaml"})
	if err != nil {
		log.Fatalln("load config failed")
		ampconAddress = "nginx-service:443"
	} else {
		ampconAddress = config.GlobalConfig.AmpconAddress
	}

	// 加载阈值配置
	thresholdConfig := &ThresholdConfig{}
	// 尝试多个可能的配置文件位置
	configPaths := []string{
		"threshold_debug.yaml",                    // 当前目录
		"../threshold_debug.yaml",                 // 上级目录
		"prometheus_monitor/threshold_debug.yaml", // 项目根目录下的prometheus_monitor目录
	}

	var configLoaded bool
	for _, path := range configPaths {
		if data, err := ioutil.ReadFile(path); err == nil {
			if err := yaml.Unmarshal(data, thresholdConfig); err == nil {
				log.Printf("成功加载阈值配置文件: %s", path)
				configLoaded = true
				break
			}
		}
	}

	if !configLoaded {
		log.Println("未找到阈值配置文件或配置文件无效，将使用默认阈值")
	}

	return &OpenconfigPlatform{
		powerMap:        map[string]transceiverState{},
		ddmMap:          map[string]DDMTransceiverState{},
		ampconAddress:   ampconAddress,
		targetName:      targetName,
		thresholdConfig: thresholdConfig,
		debugMode:       thresholdConfig.Debug,
	}
}

// DDMHandler 定义处理 DDM 消息的接口
type DDMHandler interface {
	HandleDDMMessage(interfaceName string, msg map[string]interface{}) error
}

// RedisDDMHandler 实现将 DDM 消息推送到 Redis
type RedisDDMHandler struct {
	redisClient *RedisClient
}

func NewRedisDDMHandler(redisClient *RedisClient) *RedisDDMHandler {
	return &RedisDDMHandler{
		redisClient: redisClient,
	}
}

func (h *RedisDDMHandler) HandleDDMMessage(interfaceName string, msg map[string]interface{}) error {
	jsonData, err := json.Marshal(msg)
	if err != nil {
		return fmt.Errorf("failed to marshal DDM state for interface %s: %v", interfaceName, err)
	}
	if err := h.redisClient.client.RPush(h.redisClient.ctx, RedisQueueKey, jsonData).Err(); err != nil {
		return fmt.Errorf("failed to push DDM state for interface %s to Redis: %v", interfaceName, err)
	}
	return nil
}

// HTTPDDMHandler 实现将 DDM 消息通过 HTTP POST 发送
type HTTPDDMHandler struct {
	ampconAddress string
	httpClient    *http.Client
}

func NewHTTPDDMHandler(ampconAddress string) *HTTPDDMHandler {
	// 创建一个自定义的传输配置
	tr := &http.Transport{
		TLSClientConfig: &tls.Config{
			InsecureSkipVerify: true, // 忽略证书验证
		},
	}

	// 使用自定义传输配置创建客户端
	client := &http.Client{
		Transport: tr,
	}

	return &HTTPDDMHandler{
		ampconAddress: ampconAddress,
		httpClient:    client,
	}
}

func (h *HTTPDDMHandler) HandleDDMMessage(interfaceName string, msg map[string]interface{}) error {
	jsonData, err := json.Marshal(msg)
	if err != nil {
		return fmt.Errorf("failed to marshal DDM state for interface %s: %v", interfaceName, err)
	}

	url := "https://" + h.ampconAddress + "/ampcon/monitor/alert_log"
	resp, err := h.httpClient.Post(url, "application/json", bytes.NewBuffer(jsonData))
	if err != nil {
		return fmt.Errorf("failed to send DDM state for interface %s: %v", interfaceName, err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("failed to send DDM state for interface %s: status code %d", interfaceName, resp.StatusCode)
	}

	return nil
}

// handleDDMMessage 处理 DDM 消息
func handleDDMMessage(interfaceName string, msg map[string]interface{}) error {
	// Redis 处理方式
	// if redisClient := GetRedisClient(); redisClient != nil {
	// 	handler := NewRedisDDMHandler(redisClient)
	// 	return handler.HandleDDMMessage(interfaceName, msg)
	// }

	// HTTP 处理方式

	ampconAddress := "nginx-service:443" // 默认地址
	handler := NewHTTPDDMHandler(ampconAddress)
	return handler.HandleDDMMessage(interfaceName, msg)

}

func (i *OpenconfigPlatform) AfterSubscribe(result interface{}) {
	var alerts []Alert
	for _, update := range result.(*gnmi.Notification).Update {
		baseMetric := updateToBaseMetric(time.Now(), update)

		name := filepath.Base(baseMetric.Name)
		parentDir := filepath.Dir(baseMetric.Name)
		parent := filepath.Base(parentDir)
		switch name {
		case "instant":
			if parent == "supply-voltage" || parent == "fsconfig-platform-transceiver-extensions:laser-temperature" {
				i.updateDDMTransceiverState(i.ddmMap, baseMetric, parent)
			}
		case "power-on":
			psu_index := baseMetric.Label["psu_index"]
			power_on := baseMetric.Value
			i.updateTransceiverState(i.powerMap, "rpsu_"+psu_index, power_on, "power_on")
		case "laser-temperature-upper", "laser-temperature-lower", "output-power-upper", "output-power-lower", "input-power-upper", "input-power-lower",
			"laser-bias-current-upper", "laser-bias-current-lower", "supply-voltage-upper", "supply-voltage-lower":
			i.updateDDMThreshold(i.ddmMap, baseMetric, name)

		case "serial-no", "form-factor", "fsconfig-platform-transceiver-extensions:transmission-rate":
			i.updateDDMTransceiverState(i.ddmMap, baseMetric, name)

		case "avg":
			if parent == "output-power" || parent == "input-power" || parent == "laser-bias-current" {
				i.updateDDMTransceiverState(i.ddmMap, baseMetric, parent)
			}

		default:
		}
	}

	// 在格式化 DDM 消息之前添加以下代码
	i.ddmMapMutex.RLock()
	for interfaceName, state := range i.ddmMap {
		// 检查是否有 TransportType
		if state.TransportType == "" {
			continue
		}

		if state.WarnThreshold.InputPowerUpper == 0 && state.WarnThreshold.InputPowerLower == 0 {
			// 从 powerThresholds 获取对应类型的阈值
			if threshold, exists := powerThresholds[state.TransportType]; exists {
				// 更新 WarnThreshold 中的功率阈值
				state.WarnThreshold.InputPowerUpper = threshold.inputPowerMax
				state.WarnThreshold.InputPowerLower = threshold.inputPowerMin
				state.WarnThreshold.OutputPowerUpper = threshold.outputPowerMax
				state.WarnThreshold.OutputPowerLower = threshold.outputPowerMin

				// 更新状态
				i.ddmMap[interfaceName] = state
			}
		} else {
			continue
		}

	}
	i.ddmMapMutex.RUnlock()

	// 然后继续原有的 DDM 消息格式化逻辑
	for interfaceName, state := range i.ddmMap {
		// 复制原始阈值对象
		warnThreshold := state.WarnThreshold
		alarmThreshold := state.AlarmThreshold

		// 如果有自定义阈值配置且 debug 模式开启，则覆盖相应的阈值
		if i.thresholdConfig != nil && i.thresholdConfig.CustomThresholds != nil && i.debugMode {
			log.Printf("Debug mode enabled, applying custom thresholds for interface %s", interfaceName)
			for metricType, customThreshold := range i.thresholdConfig.CustomThresholds {
				switch metricType {
				case SupplyVoltage:
					warnThreshold.SupplyVoltageUpper = customThreshold.WarnUpper
					warnThreshold.SupplyVoltageLower = customThreshold.WarnLower
					alarmThreshold.SupplyVoltageUpper = customThreshold.AlarmUpper
					alarmThreshold.SupplyVoltageLower = customThreshold.AlarmLower
					log.Printf("Applied custom thresholds for SupplyVoltage: Warn[%.2f, %.2f], Alarm[%.2f, %.2f]",
						customThreshold.WarnLower, customThreshold.WarnUpper,
						customThreshold.AlarmLower, customThreshold.AlarmUpper)
				case LaserTemperature:
					warnThreshold.LaserTemperatureUpper = customThreshold.WarnUpper
					warnThreshold.LaserTemperatureLower = customThreshold.WarnLower
					alarmThreshold.LaserTemperatureUpper = customThreshold.AlarmUpper
					alarmThreshold.LaserTemperatureLower = customThreshold.AlarmLower
					log.Printf("Applied custom thresholds for LaserTemperature: Warn[%.2f, %.2f], Alarm[%.2f, %.2f]",
						customThreshold.WarnLower, customThreshold.WarnUpper,
						customThreshold.AlarmLower, customThreshold.AlarmUpper)
				case OutputPower:
					warnThreshold.OutputPowerUpper = customThreshold.WarnUpper
					warnThreshold.OutputPowerLower = customThreshold.WarnLower
					alarmThreshold.OutputPowerUpper = customThreshold.AlarmUpper
					alarmThreshold.OutputPowerLower = customThreshold.AlarmLower
					log.Printf("Applied custom thresholds for OutputPower: Warn[%.2f, %.2f], Alarm[%.2f, %.2f]",
						customThreshold.WarnLower, customThreshold.WarnUpper,
						customThreshold.AlarmLower, customThreshold.AlarmUpper)
				case InputPower:
					warnThreshold.InputPowerUpper = customThreshold.WarnUpper
					warnThreshold.InputPowerLower = customThreshold.WarnLower
					alarmThreshold.InputPowerUpper = customThreshold.AlarmUpper
					alarmThreshold.InputPowerLower = customThreshold.AlarmLower
					log.Printf("Applied custom thresholds for InputPower: Warn[%.2f, %.2f], Alarm[%.2f, %.2f]",
						customThreshold.WarnLower, customThreshold.WarnUpper,
						customThreshold.AlarmLower, customThreshold.AlarmUpper)
				case LaserBiasCurrent:
					warnThreshold.LaserBiasCurrentUpper = customThreshold.WarnUpper
					warnThreshold.LaserBiasCurrentLower = customThreshold.WarnLower
					alarmThreshold.LaserBiasCurrentUpper = customThreshold.AlarmUpper
					alarmThreshold.LaserBiasCurrentLower = customThreshold.AlarmLower
					log.Printf("Applied custom thresholds for LaserBiasCurrent: Warn[%.2f, %.2f], Alarm[%.2f, %.2f]",
						customThreshold.WarnLower, customThreshold.WarnUpper,
						customThreshold.AlarmLower, customThreshold.AlarmUpper)
				}
			}
		} else {
			log.Printf("Using default thresholds for interface %s (Debug mode: %v)", interfaceName, i.debugMode)
		}

		var silentAlertTypes []SilentAlertType
		if state.SupplyVoltage == 0 {
			silentAlertTypes = append(silentAlertTypes, SupplyVoltageSilent)
		}

		msg := map[string]interface{}{
			"interface":        interfaceName,
			"serialNo":         state.SerialNo,
			"supplyVoltage":    state.SupplyVoltage,
			"laserTemperature": state.LaserTemperature,
			"channelsState":    state.ChannelsState,
			"warnThreshold":    warnThreshold,
			"alarmThreshold":   alarmThreshold,
			"hasThreshold":     state.HasThreshold,
			"isAbnormal":       state.IsAbnormal,
			"transportType":    state.TransportType,
			"switch_sn":        state.TargetName,
			"moduleType":       state.ModuleType,
			"silentAlertTypes": silentAlertTypes,
		}

		if err := handleDDMMessage(interfaceName, msg); err != nil {
			log.Printf("Error handling DDM message for interface %s: %v", interfaceName, err)
		}
	}

	// 遍历powerMap检查功率差值变化
	for interfaceName, transceiverState := range i.powerMap {
		if transceiverState.rpsuDiff {
			var severity string
			if transceiverState.rpsuPowerOnState {
				severity = "info"
			} else {
				severity = "warn"
			}
			alert := i.generateRpsuPowerOnAlert(interfaceName, severity, transceiverState.rpsuPowerOnState)
			log.Println(alert)
			alerts = append(alerts, alert)
		}
	}

	for interfaceName, transceiverState := range i.ddmMap {
		// if !transceiverState.HasThreshold {
		// 	continue
		// }

		newAlerts, isAbnormal := i.checkTransceiverMetrics(interfaceName, &transceiverState)
		alerts = append(alerts, newAlerts...)

		// 更新异常状态
		i.ddmMapMutex.Lock()
		updatedState := i.ddmMap[interfaceName]
		updatedState.IsAbnormal = isAbnormal
		i.ddmMap[interfaceName] = updatedState
		i.ddmMapMutex.Unlock()
	}

	if len(alerts) > 0 {
		err := sendAlertLog("https://"+i.ampconAddress+"/ampcon/monitor/alert_log", alerts)
		if err != nil {
			log.Println("Error sending alert:", err)
		}
	}
}

func (i *OpenconfigPlatform) generateAlert(interfaceName, severity, description string, value float64) Alert {
	alert := Alert{
		Labels: map[string]string{
			"target":    i.targetName,
			"severity":  severity,
			"alertname": "laser-temperature",
		},
		Annotations: map[string]string{
			"description": fmt.Sprintf(description, interfaceName, i.targetName, value),
			"summary":     "laser-temperature",
		},
	}
	// log.Printf("generate alert: %v", alert)
	log.Println(alert.Annotations["description"])
	return alert
}

func (i *OpenconfigPlatform) generateOutputInputPowerDiffAlert(interfaceName, severity, description string, prevPowerDiff, currentPowerDiff float64) Alert {
	alert := Alert{
		Labels: map[string]string{
			"target":    i.targetName,
			"severity":  severity,
			"alertname": "output-power -input-power",
		},
		Annotations: map[string]string{
			// 使用 %.2f 格式化浮点数
			"description": fmt.Sprintf(description,
				interfaceName, i.targetName,
				Decimal(prevPowerDiff),
				Decimal(currentPowerDiff)),

			"summary": "output-power -input-power",
		},
	}
	log.Printf("Found interface: %s on switch %s output_power-input_power changed from %.2f to %.2f",
		interfaceName, i.targetName, prevPowerDiff, currentPowerDiff)
	return alert
}

func (i *OpenconfigPlatform) generatePowerRangeAlert(interfaceName, severity, description, transportType string, value, min, max float64) Alert {
	alert := Alert{
		Labels: map[string]string{
			"target":    i.targetName,
			"severity":  severity,
			"alertname": getPowerName(description),
		},
		Annotations: map[string]string{
			// 使用 %.2f 格式化浮点数

			"description": fmt.Sprintf(description, interfaceName, transportType, i.targetName,
				Decimal(value),
				Decimal(min),
				Decimal(max)),
			"summary": getPowerName(description),
		},
	}
	log.Printf(alert.Annotations["description"])
	return alert
}

func (i *OpenconfigPlatform) generateRpsuPowerOnAlert(interfaceName, severity string, value bool) Alert {
	alert := Alert{
		Labels: map[string]string{
			"target":    i.targetName,
			"severity":  severity,
			"alertname": "rpsu",
		},
		Annotations: map[string]string{
			"description": fmt.Sprintf("Switch %s rpsu: %s power-on status change to %t", i.targetName, interfaceName, value),
			"summary":     "rpsu",
		},
	}
	log.Printf(alert.Annotations["description"])
	return alert
}

func (i *OpenconfigPlatform) generateDDMAlert(severity, alertname, description string) Alert {
	alert := Alert{
		Labels: map[string]string{
			"target":    i.targetName,
			"severity":  severity,
			"alertname": alertname,
		},
		Annotations: map[string]string{
			"description": description,
			"summary":     "DDM",
		},
	}
	log.Println(severity, alert.Annotations["description"])
	return alert
}

func (i *OpenconfigPlatform) updateTransceiverState(powerMap map[string]transceiverState, interfaceName string, value string, updateType string) { // 1. 先获取结构体
	transceiver, exists := powerMap[interfaceName]
	if !exists {
		// 如果不存在，创建新的结构体
		transceiver = transceiverState{}
		boolvalue, err := strconv.ParseBool(value)
		if err == nil {
			transceiver.rpsuPowerOnState = boolvalue
		}
	}

	// 2. 更新结构体的属性
	switch updateType {
	case "power_on":
		boolvalue, err := strconv.ParseBool(value)
		if err == nil {
			if transceiver.rpsuPowerOnState != boolvalue {
				transceiver.rpsuPowerOnState = boolvalue
				transceiver.rpsuDiff = true
			} else {
				transceiver.rpsuDiff = false
			}
		} else {
			transceiver.rpsuDiff = false
		}
	}

	// 3. 将更新后的结构体放回map
	powerMap[interfaceName] = transceiver

}

func (i *OpenconfigPlatform) updateDDMTransceiverState(ddmMap map[string]DDMTransceiverState, baseMetric BaseMetric, updateType string) {
	i.ddmMapMutex.Lock()
	defer i.ddmMapMutex.Unlock()
	interfaceName := baseMetric.Label["interface_name"]
	value := baseMetric.Value
	transceiver, exists := ddmMap[interfaceName]
	if !exists {
		// 如果不存在，创建新的结构体
		transceiver = DDMTransceiverState{
			ChannelsState:  make(map[string]ChannelState),
			HasThreshold:   false,
			IsAbnormal:     false,
			TargetName:     i.targetName,
			WarnThreshold:  NewWarnThreshold(),  // 使用警告阈值初始化
			AlarmThreshold: NewAlarmThreshold(), // 使用告警阈值初始化
			Timestamp:      baseMetric.Timestamp,
		}
	}

	// 2. 更新结构体的属性
	switch updateType {
	case "serial-no":
		transceiver.SerialNo = value
	case "supply-voltage":
		val, err := strconv.ParseFloat(value, 64)
		if err != nil {
			log.Printf("Error parsing value %s to float: %v", value, err)
			return
		}
		transceiver.SupplyVoltage = Decimal(val)
	case "fsconfig-platform-transceiver-extensions:laser-temperature":
		val, err := strconv.ParseFloat(value, 64)
		if err != nil {
			log.Printf("Error parsing value %s to float: %v", value, err)
			return
		}
		transceiver.LaserTemperature = val
	case "output-power", "input-power", "laser-bias-current":
		channel_index := baseMetric.Label["channel_index"]
		channel_state, exists := transceiver.ChannelsState[channel_index]
		if !exists {
			channel_state = ChannelState{}
		}
		val, err := strconv.ParseFloat(value, 64)
		if err != nil {
			log.Printf("Error parsing value %s to float: %v", value, err)
			return
		}

		switch updateType {
		case "output-power":
			channel_state.OutputPower = val
		case "input-power":
			channel_state.InputPower = val
		case "laser-bias-current":
			channel_state.LaserBiasCurrent = val
		}
		transceiver.ChannelsState[channel_index] = channel_state
	case "form-factor":
		transceiver.TransportType = value
	case "fsconfig-platform-transceiver-extensions:transmission-rate":
		transceiver.ModuleType = value

	}
	transceiver.Timestamp = baseMetric.Timestamp

	// 3. 将更新后的结构体放回map
	ddmMap[interfaceName] = transceiver

}

func (i *OpenconfigPlatform) updateDDMThreshold(ddmMap map[string]DDMTransceiverState, baseMetric BaseMetric, thresholdName string) {
	// fmt.Println(baseMetric.Label, baseMetric.Value)
	i.ddmMapMutex.Lock()
	defer i.ddmMapMutex.Unlock()
	interfaceName := baseMetric.Label["interface_name"]
	threshold_type := baseMetric.Label["threshold_severity"]

	val, err := strconv.ParseFloat(baseMetric.Value, 64)
	if err != nil {
		log.Printf("Error parsing value %s to float: %v", baseMetric.Value, err)
		return
	}

	transceiver, exists := ddmMap[interfaceName]
	if !exists {
		// 如果不存在，创建新的结构体
		transceiver = DDMTransceiverState{ChannelsState: map[string]ChannelState{}, HasThreshold: false, IsAbnormal: false}
	}

	threshold := &transceiver.WarnThreshold
	if threshold_type == "openconfig-alarm-types:MAJOR" {
		threshold = &transceiver.AlarmThreshold
	} else if threshold_type != "openconfig-alarm-types:WARNING" {
		log.Printf("invalid threshold type: %s", threshold_type)
		return
	}

	transceiver.HasThreshold = true
	switch thresholdName {
	case "laser-temperature-upper":
		threshold.LaserTemperatureUpper = val
	case "laser-temperature-lower":
		threshold.LaserTemperatureLower = val
	case "output-power-upper":
		threshold.OutputPowerUpper = val
	case "output-power-lower":
		threshold.OutputPowerLower = val
	case "input-power-upper":
		threshold.InputPowerUpper = val
	case "input-power-lower":
		threshold.InputPowerLower = val
	case "laser-bias-current-upper":
		threshold.LaserBiasCurrentUpper = val
	case "laser-bias-current-lower":
		threshold.LaserBiasCurrentLower = val
	case "supply-voltage-upper":
		threshold.SupplyVoltageUpper = val
	case "supply-voltage-lower":
		threshold.SupplyVoltageLower = val
	default:
		log.Printf("unknown DDM threshold key: %s", thresholdName)
	}

	ddmMap[interfaceName] = transceiver
}

func (i *OpenconfigPlatform) getAbnormalSamples(ts time.Time, Interval int) []prometheus.Metric {
	i.ddmMapMutex.RLock()
	defer i.ddmMapMutex.RUnlock()

	samples := []prometheus.Metric{}
	for interfaceName, state := range i.ddmMap {
		// 如果stats中是时间戳过老 当做模块不存在
		if state.IsAbnormal && (ts.Sub(state.Timestamp) <= time.Duration(Interval)*time.Second) {
			ddmAbnormalMetricName := strings.ReplaceAll("openconfig_platform:components_interface_abnormal_num", "-", "_")
			labelNames := []string{"target", "interface_name"}
			labelValues := []string{i.targetName, interfaceName}
			ddmAbnormalMetric := prometheus.NewMetricWithTimestamp(
				ts,
				prometheus.MustNewConstMetric(
					prometheus.NewDesc(
						ddmAbnormalMetricName,
						ddmAbnormalMetricName,
						labelNames,
						nil),
					prometheus.GaugeValue,
					1, labelValues...))
			samples = append(samples, ddmAbnormalMetric)
		}
	}
	return samples
}

func CheckThreshold(value, warnLower, warnUpper, alarmLower, alarmUpper float64) AlertLevel {
	// 检查阈值是否有效
	if warnLower == 0 && warnUpper == 0 && alarmLower == 0 && alarmUpper == 0 {
		return Normal // 如果所有阈值都为0，认为阈值未配置，返回正常状态
	}

	// 检查是否在警告范围内
	if value >= warnLower && value <= warnUpper {
		return Normal
	}

	// 检查是否在告警范围内
	if value >= alarmLower && value <= alarmUpper {
		return Warn
	}

	return Error
}

func getSeverityAndRange(alertLevel AlertLevel, value float64, warnLower, warnUpper, alarmLower, alarmUpper float64) (string, string) {
	switch alertLevel {
	case Warn:
		if value > warnUpper {
			return "warn", fmt.Sprintf("Exceeds Warning Upper Limit %.2f", warnUpper)
		}
		return "warn", fmt.Sprintf("Below Warning Lower Limit %.2f", warnLower)
	case Error:
		if value > alarmUpper {
			return "error", fmt.Sprintf("Exceeds Alarm Upper Limit %.2f", alarmUpper)
		}
		return "error", fmt.Sprintf("Below Alarm Lower Limit %.2f", alarmLower)
	default:
		return "info", ""
	}
}

func Decimal(value float64) float64 {
	f, _ := strconv.ParseFloat(fmt.Sprintf("%.2f", value), 64)
	return f
}

func getPowerName(description string) string {
	if strings.Contains(description, "input-power") {
		return "input-power"
	}
	return "output-power"
}

type PlatformCollectorWorker struct {
	*CollectorWorker
}

func NewPlatformCollectorWorker(base *CollectorWorker) BaseCollectorWorker {
	return &PlatformCollectorWorker{
		CollectorWorker: base,
	}
}

func (w *PlatformCollectorWorker) ResultToSamples(result map[string]BaseMetric, targetName string, ts time.Time) []prometheus.Metric {
	samples := []prometheus.Metric{}
	newBaseMetric := make(map[string]BaseMetric)
	for _, base := range result {

		if strings.Contains(base.Name, "memory") || strings.Contains(base.Name, "cpus") || strings.Contains(base.Name, "rates") || strings.Contains(base.Name, "thresholds") ||
			((strings.Contains(base.Name, "speed") || strings.Contains(base.Name, "pwm")) && (strings.Contains(base.Name, "fan") || strings.Contains(base.Name, "rear-fan"))) {
			// 过滤不需要的cpu参数
			if strings.Contains(base.Name, "cpus") &&
				(strings.Contains(base.Name, "hardware-interrupt") || strings.Contains(base.Name, "nice") ||
					strings.Contains(base.Name, "idle") || strings.Contains(base.Name, "wait") ||
					strings.Contains(base.Name, "user") || strings.Contains(base.Name, "kernel") || strings.Contains(base.Name, "software-interrupt")) {
				continue
			}

			if strings.Contains(base.Name, "thresholds") && strings.Contains(base.Name, "severity") {
				continue
			}

			// 计算pwm的结果
			if strings.Contains(base.Name, "pwm") {
				parts := strings.Split(base.Value, "/")
				if len(parts) == 2 {
					numerator, err1 := strconv.Atoi(parts[0])
					denominator, err2 := strconv.Atoi(parts[1])
					if err1 != nil || err2 != nil {
						log.Println("Error converting to integer:", err1, err2)
						continue
					}
					result := float64(numerator) / float64(denominator)
					base.Value = fmt.Sprintf("%.4f", result)
				}

			}
			metric := buildMetric(base.Name, base.Value, targetName, base.Label, base.Timestamp)
			samples = append(samples, metric)
		} else if strings.Contains(base.Name, "output-power") || strings.Contains(base.Name, "input-power") ||
			strings.Contains(base.Name, "laser-temperature") || strings.Contains(base.Name, "laser-bias-current") || strings.Contains(base.Name, "supply-voltage") {
			dir := filepath.Dir(base.Name)
			dir = strings.ReplaceAll(dir, "\\", "/")
			if strings.Contains(base.Name, "output-power") || strings.Contains(base.Name, "input-power") {
				dir = strings.Replace(dir, "/physical-channels/channel", "", 1)
			}
			metric := buildMetric(dir, base.Value, targetName, base.Label, base.Timestamp)
			samples = append(samples, metric)
		} else {
			// 对于非counters类型的指标，根据base路径和label进行合并，dir作为指标名称 name作为label
			w.ProcessGroupedMetric(&newBaseMetric, base, ts)
		}
	}

	if hook, ok := w.Hooks.(*OpenconfigPlatform); ok {
		abnormalSamples := hook.getAbnormalSamples(ts, w.Interval)
		samples = append(samples, abnormalSamples...)

	}

	samples = append(samples, w.BuildGroupedMetrics(newBaseMetric, targetName)...)
	return samples
}

// 3. 修改阈值获取方法
func (i *OpenconfigPlatform) getCustomThreshold(
	metricType MetricType,
) *CustomThreshold {
	if !i.debugMode {
		return nil
	}

	if threshold, exists := i.thresholdConfig.CustomThresholds[metricType]; exists {
		return &threshold
	}
	return nil
}

// 4. 修改检查阈值的方法
func (i *OpenconfigPlatform) checkMetricThreshold(
	metricType MetricType,
	value float64,
	state *DDMTransceiverState,
	interfaceName string,
	channelIndex string,
) (Alert, bool) {
	mapping := thresholdMappings[metricType]

	// 检查是否有自定义阈值
	var warnLower, warnUpper, alarmLower, alarmUpper float64
	var thresholdSource string

	if customThreshold := i.getCustomThreshold(metricType); customThreshold != nil {
		warnLower = customThreshold.WarnLower
		warnUpper = customThreshold.WarnUpper
		alarmLower = customThreshold.AlarmLower
		alarmUpper = customThreshold.AlarmUpper
		thresholdSource = "自定义阈值"
	} else {
		// 检查阈值是否已初始化
		if state == nil || (metricType == SupplyVoltage && state.SupplyVoltage == 0) {
			log.Printf("警告：接口 %s 的阈值未初始化", interfaceName)
			return Alert{}, false
		}

		// 使用默认阈值
		warnLower = getThresholdValue(state.WarnThreshold, mapping.WarnLower)
		warnUpper = getThresholdValue(state.WarnThreshold, mapping.WarnUpper)
		alarmLower = getThresholdValue(state.AlarmThreshold, mapping.AlarmLower)
		alarmUpper = getThresholdValue(state.AlarmThreshold, mapping.AlarmUpper)
		thresholdSource = "默认阈值"
	}

	// 打印详细的阈值检查信息
	channelInfo := ""
	if channelIndex != "" {
		channelInfo = fmt.Sprintf(" (通道: %s)", channelIndex)
	}

	log.Printf(
		"【阈值检查详情】\n"+
			"SN: %s\n"+
			"接口: %s%s\n"+
			"指标类型: %s\n"+
			"当前值: %.2f\n"+
			"阈值来源: %s\n"+
			"告警阈值范围: [%.2f, %.2f]\n"+
			"错误阈值范围: [%.2f, %.2f]",
		i.targetName,
		interfaceName,
		channelInfo,
		metricType,
		value,
		thresholdSource,
		warnLower, warnUpper,
		alarmLower, alarmUpper,
	)

	alertLevel := CheckThreshold(value, warnLower, warnUpper, alarmLower, alarmUpper)

	if alertLevel != Normal {
		severity, rangeStr := getSeverityAndRange(
			alertLevel,
			value,
			warnLower,
			warnUpper,
			alarmLower,
			alarmUpper,
		)

		// 打印告警生成信息
		log.Printf(
			"【生成告警】\n"+
				"接口: %s%s\n"+
				"指标类型: %s\n"+
				"当前值: %.2f\n"+
				"告警级别: %s\n"+
				"超限说明: %s",
			interfaceName,
			channelInfo,
			metricType,
			value,
			severity,
			rangeStr,
		)

		var desc string
		if channelIndex != "" {
			desc = fmt.Sprintf(mapping.Description, interfaceName, channelIndex, value, rangeStr)
		} else {
			desc = fmt.Sprintf(mapping.Description, interfaceName, value, rangeStr)
		}

		alert := i.generateDDMAlert(severity, mapping.AlertName, desc)
		return alert, true
	} else {
		// 打印正常状态信息
		log.Printf(
			"【正常状态】接口: %s%s, 指标: %s, 当前值: %.2f (在正常范围内)",
			interfaceName,
			channelInfo,
			metricType,
			value,
		)
	}

	return Alert{}, false
}

func getThresholdValue(threshold interface{}, fieldName string) float64 {
	r := reflect.ValueOf(threshold)
	if r.Kind() == reflect.Ptr {
		r = r.Elem()
	}

	f := r.FieldByName(fieldName)
	if !f.IsValid() {
		log.Printf("警告：阈值字段 %s 不存在", fieldName)
		return 0
	}

	if !f.CanFloat() {
		log.Printf("警告：阈值字段 %s 不是浮点类型", fieldName)
		return 0
	}

	return f.Float()
}

// 修改检查逻辑
func (i *OpenconfigPlatform) checkTransceiverMetrics(
	interfaceName string,
	transceiverState *DDMTransceiverState,
) ([]Alert, bool) {
	var alerts []Alert
	isAbnormal := false

	// 检查基础指标
	for _, metricType := range baseMetrics {
		var value float64
		switch metricType {
		case SupplyVoltage:
			value = transceiverState.SupplyVoltage
		case LaserTemperature:
			value = transceiverState.LaserTemperature
		}

		if alert, hasAlert := i.checkMetricThreshold(
			metricType,
			value,
			transceiverState,
			interfaceName,
			"",
		); hasAlert {
			isAbnormal = true
			alerts = append(alerts, alert)
		}
	}

	// 检查通道指标
	for channelIndex, channelState := range transceiverState.ChannelsState {
		for _, metricType := range channelMetrics {
			var value float64
			switch metricType {
			case OutputPower:
				value = channelState.OutputPower
			case InputPower:
				value = channelState.InputPower
			case LaserBiasCurrent:
				value = channelState.LaserBiasCurrent
			}

			if alert, hasAlert := i.checkMetricThreshold(
				metricType,
				value,
				transceiverState,
				interfaceName,
				channelIndex,
			); hasAlert {
				isAbnormal = true
				alerts = append(alerts, alert)
			}
		}
	}

	return alerts, isAbnormal
}
